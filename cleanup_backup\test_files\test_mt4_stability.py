"""
MT4连接稳定性测试脚本
用于测试与MT4的连接稳定性，并找出一个可靠的连接方案
"""
import time
import traceback
from datetime import datetime
import sys
import os
import json
import argparse

# 导入MT4客户端
from app.utils.mt4_client import mt4_client

# 测试配置默认值
DEFAULT_DURATION = 300  # 测试持续时间（秒）
DEFAULT_INTERVAL = 5    # 测试间隔（秒）
DEFAULT_RECONNECT_ATTEMPTS = 3  # 重连尝试次数
DEFAULT_RECONNECT_DELAY = 2     # 重连延迟（秒）
DEFAULT_LOG_FILE = "mt4_stability_test.log"  # 日志文件

# 测试结果统计
results = {
    'total_tests': 0,
    'successful_tests': 0,
    'failed_tests': 0,
    'reconnect_attempts': 0,
    'successful_reconnects': 0,
    'failed_reconnects': 0,
    'errors': {},
    'response_times': {
        'connect': [],
        'market_info': [],
        'active_orders': [],
        'pending_orders': [],
        'account_info': []
    }
}

def log(message, log_file=None):
    """记录日志"""
    now = datetime.now()
    log_message = f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] {message}'
    print(log_message)
    
    # 如果指定了日志文件，也写入文件
    if log_file:
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')

def test_connection(log_file=None):
    """测试MT4连接"""
    log("开始测试MT4连接...", log_file)
    
    # 检查连接状态
    if not mt4_client.is_connected:
        log("MT4客户端未连接，尝试连接...", log_file)
        start_time = time.time()
        connect_result = mt4_client.connect()
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 毫秒
        results['response_times']['connect'].append(response_time)
        
        if connect_result:
            log(f"MT4客户端连接成功，响应时间: {response_time:.2f}ms", log_file)
        else:
            log(f"MT4客户端连接失败，响应时间: {response_time:.2f}ms", log_file)
            return False
    else:
        log("MT4客户端已连接", log_file)
    
    # 测试获取市场信息
    try:
        log("测试获取市场信息...", log_file)
        start_time = time.time()
        market_info = mt4_client.get_market_info('EURUSD')
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 毫秒
        results['response_times']['market_info'].append(response_time)
        
        if market_info and market_info.get('status') == 'success':
            log(f"获取市场信息成功，响应时间: {response_time:.2f}ms", log_file)
            log(f"当前价格: Bid={market_info['data']['bid']}, Ask={market_info['data']['ask']}", log_file)
            return True
        else:
            log(f"获取市场信息失败: {market_info}，响应时间: {response_time:.2f}ms", log_file)
            return False
    except Exception as e:
        log(f"获取市场信息出错: {e}", log_file)
        traceback.print_exc()
        return False

def test_get_active_orders(log_file=None):
    """测试获取活跃订单"""
    try:
        log("测试获取活跃订单...", log_file)
        start_time = time.time()
        orders = mt4_client.get_active_orders()
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 毫秒
        results['response_times']['active_orders'].append(response_time)
        
        if orders and orders.get('status') == 'success':
            log(f"获取活跃订单成功: {len(orders.get('orders', []))}个订单，响应时间: {response_time:.2f}ms", log_file)
            return True
        else:
            log(f"获取活跃订单失败: {orders}，响应时间: {response_time:.2f}ms", log_file)
            return False
    except Exception as e:
        log(f"获取活跃订单出错: {e}", log_file)
        traceback.print_exc()
        return False

def test_get_pending_orders(log_file=None):
    """测试获取挂单"""
    try:
        log("测试获取挂单...", log_file)
        start_time = time.time()
        orders = mt4_client.get_pending_orders()
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 毫秒
        results['response_times']['pending_orders'].append(response_time)
        
        if orders and orders.get('status') == 'success':
            log(f"获取挂单成功: {len(orders.get('orders', []))}个挂单，响应时间: {response_time:.2f}ms", log_file)
            return True
        else:
            log(f"获取挂单失败: {orders}，响应时间: {response_time:.2f}ms", log_file)
            return False
    except Exception as e:
        log(f"获取挂单出错: {e}", log_file)
        traceback.print_exc()
        return False

def test_get_account_info(log_file=None):
    """测试获取账户信息"""
    try:
        log("测试获取账户信息...", log_file)
        start_time = time.time()
        account_info = mt4_client.get_account_info()
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 毫秒
        results['response_times']['account_info'].append(response_time)
        
        if account_info and account_info.get('status') == 'success':
            log(f"获取账户信息成功，响应时间: {response_time:.2f}ms", log_file)
            log(f"账户余额: {account_info['data'].get('balance')}", log_file)
            return True
        else:
            log(f"获取账户信息失败: {account_info}，响应时间: {response_time:.2f}ms", log_file)
            return False
    except Exception as e:
        log(f"获取账户信息出错: {e}", log_file)
        traceback.print_exc()
        return False

def calculate_statistics():
    """计算统计信息"""
    stats = {
        'success_rate': (results['successful_tests'] / results['total_tests'] * 100) if results['total_tests'] > 0 else 0,
        'reconnect_success_rate': (results['successful_reconnects'] / results['reconnect_attempts'] * 100) if results['reconnect_attempts'] > 0 else 0,
        'avg_response_times': {}
    }
    
    # 计算平均响应时间
    for key, times in results['response_times'].items():
        if times:
            stats['avg_response_times'][key] = sum(times) / len(times)
        else:
            stats['avg_response_times'][key] = 0
    
    return stats

def run_test_suite(duration=DEFAULT_DURATION, interval=DEFAULT_INTERVAL, 
                  reconnect_attempts=DEFAULT_RECONNECT_ATTEMPTS, 
                  reconnect_delay=DEFAULT_RECONNECT_DELAY,
                  log_file=DEFAULT_LOG_FILE):
    """运行完整测试套件"""
    log("=" * 50, log_file)
    log("开始MT4连接稳定性测试", log_file)
    log(f"测试持续时间: {duration}秒, 测试间隔: {interval}秒", log_file)
    log(f"重连尝试次数: {reconnect_attempts}, 重连延迟: {reconnect_delay}秒", log_file)
    log("=" * 50, log_file)
    
    # 记录开始时间
    start_time = time.time()
    
    # 运行测试循环
    while time.time() - start_time < duration:
        results['total_tests'] += 1
        log(f"\n测试 #{results['total_tests']} 开始", log_file)
        
        # 测试连接
        connection_success = test_connection(log_file)
        
        if connection_success:
            # 如果连接成功，测试其他功能
            active_orders_success = test_get_active_orders(log_file)
            pending_orders_success = test_get_pending_orders(log_file)
            account_info_success = test_get_account_info(log_file)
            
            # 判断整体测试是否成功
            test_success = connection_success and active_orders_success and pending_orders_success and account_info_success
        else:
            test_success = False
            
            # 尝试重连
            for attempt in range(reconnect_attempts):
                results['reconnect_attempts'] += 1
                log(f"尝试重连 #{attempt+1}/{reconnect_attempts}...", log_file)
                
                # 先断开连接
                mt4_client.disconnect()
                time.sleep(reconnect_delay)
                
                # 重新连接
                start_time_reconnect = time.time()
                reconnect_success = mt4_client.connect()
                end_time_reconnect = time.time()
                reconnect_time = (end_time_reconnect - start_time_reconnect) * 1000  # 毫秒
                
                if reconnect_success:
                    log(f"重连成功，响应时间: {reconnect_time:.2f}ms", log_file)
                    results['successful_reconnects'] += 1
                    
                    # 重新测试连接
                    connection_success = test_connection(log_file)
                    if connection_success:
                        test_success = True
                        break
                else:
                    log(f"重连失败，响应时间: {reconnect_time:.2f}ms", log_file)
                    results['failed_reconnects'] += 1
        
        # 更新测试结果
        if test_success:
            results['successful_tests'] += 1
            log("测试成功 ✅", log_file)
        else:
            results['failed_tests'] += 1
            log("测试失败 ❌", log_file)
            
            # 记录错误类型
            error_type = "连接失败" if not connection_success else "功能测试失败"
            results['errors'][error_type] = results['errors'].get(error_type, 0) + 1
        
        # 计算剩余时间
        elapsed_time = time.time() - start_time
        remaining_time = duration - elapsed_time
        
        # 等待下一次测试
        wait_time = min(interval, max(0, remaining_time))
        if wait_time > 0:
            log(f"等待 {wait_time:.1f} 秒后进行下一次测试...", log_file)
            time.sleep(wait_time)
    
    # 测试完成，计算统计信息
    stats = calculate_statistics()
    
    # 输出结果
    log("\n" + "=" * 50, log_file)
    log("MT4连接稳定性测试完成", log_file)
    log("=" * 50, log_file)
    log(f"总测试次数: {results['total_tests']}", log_file)
    log(f"成功次数: {results['successful_tests']} ({stats['success_rate']:.2f}%)", log_file)
    log(f"失败次数: {results['failed_tests']} ({100-stats['success_rate']:.2f}%)", log_file)
    log(f"重连尝试次数: {results['reconnect_attempts']}", log_file)
    log(f"重连成功次数: {results['successful_reconnects']} ({stats['reconnect_success_rate']:.2f}%)", log_file)
    log(f"重连失败次数: {results['failed_reconnects']}", log_file)
    log("错误类型统计:", log_file)
    for error_type, count in results['errors'].items():
        log(f"  - {error_type}: {count}次", log_file)
    
    log("\n平均响应时间:", log_file)
    for key, avg_time in stats['avg_response_times'].items():
        log(f"  - {key}: {avg_time:.2f}ms", log_file)
    
    log("=" * 50, log_file)
    
    # 保存结果到JSON文件
    result_file = "mt4_stability_test_results.json"
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump({
            'results': results,
            'stats': stats,
            'config': {
                'duration': duration,
                'interval': interval,
                'reconnect_attempts': reconnect_attempts,
                'reconnect_delay': reconnect_delay
            },
            'timestamp': datetime.now().isoformat()
        }, f, indent=2)
    
    log(f"测试结果已保存到 {result_file}", log_file)
    
    return results, stats

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='MT4连接稳定性测试')
    parser.add_argument('--duration', type=int, default=DEFAULT_DURATION, help=f'测试持续时间（秒），默认{DEFAULT_DURATION}秒')
    parser.add_argument('--interval', type=int, default=DEFAULT_INTERVAL, help=f'测试间隔（秒），默认{DEFAULT_INTERVAL}秒')
    parser.add_argument('--reconnect-attempts', type=int, default=DEFAULT_RECONNECT_ATTEMPTS, help=f'重连尝试次数，默认{DEFAULT_RECONNECT_ATTEMPTS}次')
    parser.add_argument('--reconnect-delay', type=int, default=DEFAULT_RECONNECT_DELAY, help=f'重连延迟（秒），默认{DEFAULT_RECONNECT_DELAY}秒')
    parser.add_argument('--log-file', type=str, default=DEFAULT_LOG_FILE, help=f'日志文件，默认{DEFAULT_LOG_FILE}')
    
    args = parser.parse_args()
    
    # 运行测试
    run_test_suite(
        duration=args.duration,
        interval=args.interval,
        reconnect_attempts=args.reconnect_attempts,
        reconnect_delay=args.reconnect_delay,
        log_file=args.log_file
    )
