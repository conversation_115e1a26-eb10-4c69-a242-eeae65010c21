"""
测试平仓功能
"""
import os
import sys
import time
from datetime import datetime

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
try:
    from app.utils.mt4_client import mt4_client
    from app.utils.performance_evaluator import get_virtual_account
    from app.utils.order_result_analyzer import get_order_result_statistics
    from app.services.forex_trading_service import execute_trade

    print("模块导入成功")
except Exception as e:
    print(f"模块导入失败: {e}")
    sys.exit(1)

def test_mt4_connection():
    """测试MT4连接"""
    print("\n=== 测试MT4连接 ===")
    try:
        connected = mt4_client.connect()
        print(f"MT4连接状态: {connected}")
        return connected
    except Exception as e:
        print(f"MT4连接失败: {e}")
        return False

def get_active_orders():
    """获取活跃订单"""
    print("\n=== 获取活跃订单 ===")
    try:
        response = mt4_client.get_active_orders()
        orders = response.get('orders', [])
        print(f"活跃订单数量: {len(orders)}")

        # 打印订单详情
        for order in orders:
            order_id = order.get('order_id')
            symbol = order.get('symbol')
            type_str = order.get('type')
            lots = order.get('lots')
            open_price = order.get('open_price')
            sl = order.get('sl')
            tp = order.get('tp')

            print(f"订单ID: {order_id}, 货币对: {symbol}, 类型: {type_str}, 手数: {lots}, 开仓价: {open_price}, 止损: {sl}, 止盈: {tp}")

        return orders
    except Exception as e:
        print(f"获取活跃订单失败: {e}")
        return []

def get_pending_orders():
    """获取挂单"""
    print("\n=== 获取挂单 ===")
    try:
        response = mt4_client.get_pending_orders()
        orders = response.get('orders', [])
        print(f"挂单数量: {len(orders)}")

        # 打印订单详情
        for order in orders:
            order_id = order.get('order_id')
            symbol = order.get('symbol')
            type_str = order.get('type')
            lots = order.get('lots')
            open_price = order.get('open_price')
            sl = order.get('sl')
            tp = order.get('tp')

            print(f"订单ID: {order_id}, 货币对: {symbol}, 类型: {type_str}, 手数: {lots}, 开仓价: {open_price}, 止损: {sl}, 止盈: {tp}")

        return orders
    except Exception as e:
        print(f"获取挂单失败: {e}")
        return []

def test_virtual_account():
    """测试虚拟账户"""
    print("\n=== 测试虚拟账户 ===")
    try:
        account = get_virtual_account()
        print(f"虚拟账户余额: {account['current_balance']}")
        print(f"开仓交易数量: {len(account['open_trades'])}")
        print(f"平仓交易数量: {len(account['closed_trades'])}")

        # 打印开仓交易详情
        print("\n开仓交易详情:")
        for trade in account['open_trades']:
            print(f"交易ID: {trade['id']}, 货币对: {trade['symbol']}, 方向: {trade['direction']}, 手数: {trade['lot_size']}, 入场价: {trade['entry_price']}, 止损: {trade['stop_loss']}, 止盈: {trade['take_profit']}")

        # 打印平仓交易详情
        print("\n平仓交易详情:")
        for trade in account['closed_trades']:
            print(f"交易ID: {trade['id']}, 货币对: {trade['symbol']}, 方向: {trade['direction']}, 手数: {trade['lot_size']}, 入场价: {trade['entry_price']}, 出场价: {trade['exit_price']}, 盈亏: {trade['profit_loss']}")

        return account
    except Exception as e:
        print(f"获取虚拟账户失败: {e}")
        return None

def test_order_result_statistics():
    """测试订单结果统计"""
    print("\n=== 测试订单结果统计 ===")
    try:
        stats = get_order_result_statistics()
        print(f"总订单数: {stats['total_count']}")
        print(f"止盈触发率: {stats['take_profit_rate']:.1f}%")
        print(f"止损触发率: {stats['stop_loss_rate']:.1f}%")
        print(f"手动平仓率: {stats['manual_close_rate']:.1f}%")
        print(f"平均持仓时间: {stats['avg_duration_hours']:.1f}小时")
        print(f"平均风险回报比: {stats['avg_risk_reward_ratio']:.2f}")
        return True
    except Exception as e:
        print(f"获取订单结果统计失败: {e}")
        return False

def test_close_pending_order(order_id):
    """测试删除挂单"""
    print(f"\n=== 测试删除挂单 (订单ID: {order_id}) ===")
    try:
        # 删除挂单
        delete_result = mt4_client.send_request({
            'action': 'DELETEPENDING',
            'order_id': str(order_id)
        })
        print(f"删除挂单结果: {delete_result}")

        return delete_result.get('status') == 'success'
    except Exception as e:
        print(f"删除挂单失败: {e}")
        return False

def test_close_all_positions():
    """测试平仓所有持仓"""
    print("\n=== 测试平仓所有持仓 ===")
    try:
        # 创建一个平仓的交易指令
        trade_instructions = {
            "action": "CLOSE",
            "reasoning": "测试平仓所有持仓"
        }

        # 执行交易
        result = execute_trade(trade_instructions)
        print(f"平仓所有持仓结果: {result}")

        return True
    except Exception as e:
        print(f"平仓所有持仓失败: {e}")
        return False

def run_tests():
    """运行所有测试"""
    print("=== 开始测试 ===")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 测试MT4连接
    mt4_connected = test_mt4_connection()
    if not mt4_connected:
        print("MT4未连接，无法继续测试")
        return

    # 获取活跃订单
    active_orders = get_active_orders()

    # 获取挂单
    pending_orders = get_pending_orders()

    # 测试虚拟账户
    test_virtual_account()

    # 测试订单结果统计
    test_order_result_statistics()

    # 如果有挂单，测试删除挂单
    if pending_orders:
        order_to_delete = pending_orders[0]
        order_id = order_to_delete.get('order_id')
        print(f"\n选择订单ID: {order_id} 进行删除挂单测试")

        # 测试删除挂单
        delete_success = test_close_pending_order(order_id)

        # 再次测试虚拟账户
        if delete_success:
            print("\n删除挂单后再次测试虚拟账户")
            test_virtual_account()

            # 再次测试订单结果统计
            print("\n删除挂单后再次测试订单结果统计")
            test_order_result_statistics()
    else:
        print("\n没有挂单，跳过删除挂单测试")

    # 如果有活跃订单，测试平仓所有持仓
    if active_orders:
        print("\n测试平仓所有持仓")
        test_close_all_positions()

        # 再次测试虚拟账户
        print("\n平仓所有持仓后再次测试虚拟账户")
        test_virtual_account()

        # 再次测试订单结果统计
        print("\n平仓所有持仓后再次测试订单结果统计")
        test_order_result_statistics()
    else:
        print("\n没有活跃订单，跳过平仓所有持仓测试")

    print("\n=== 测试完成 ===")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    run_tests()
