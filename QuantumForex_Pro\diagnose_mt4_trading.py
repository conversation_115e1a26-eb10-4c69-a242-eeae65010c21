#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断MT4交易执行问题
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mt4_connection():
    """测试MT4连接"""
    print("🔍 测试MT4连接...")
    print("=" * 50)
    
    try:
        from utils.mt4_client import MT4Client, should_skip_mt4_connection
        
        # 检查跳过状态
        skip_status = should_skip_mt4_connection()
        print(f"📊 MT4跳过状态: {skip_status}")
        
        # 创建MT4客户端
        mt4_client = MT4Client()
        print("✅ MT4客户端创建成功")
        
        # 尝试连接
        print("🔗 尝试连接MT4服务器...")
        connected = mt4_client.connect()
        
        if connected:
            print("✅ MT4连接成功")
            print(f"   连接状态: {mt4_client.is_connected}")
            print(f"   授权状态: {mt4_client.is_authorized}")
            if mt4_client.user_info:
                print(f"   用户信息: {mt4_client.user_info}")
        else:
            print("❌ MT4连接失败")
            
        return connected, mt4_client
        
    except Exception as e:
        print(f"❌ MT4连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_mt4_market_info(mt4_client):
    """测试MT4市场信息获取"""
    print("\n🔍 测试MT4市场信息获取...")
    print("=" * 50)
    
    try:
        # 测试获取市场信息
        symbols = ['EURUSD', 'GBPUSD', 'AUDUSD']
        
        for symbol in symbols:
            print(f"📊 获取{symbol}市场信息...")
            market_info = mt4_client.get_market_info(symbol)
            
            if market_info and market_info.get('status') == 'success':
                data = market_info.get('data', {})
                print(f"✅ {symbol}: Bid={data.get('bid')}, Ask={data.get('ask')}, Spread={data.get('spread')}")
            else:
                print(f"❌ {symbol}: 获取失败 - {market_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 市场信息测试失败: {e}")
        return False

def test_mt4_positions(mt4_client):
    """测试MT4持仓获取"""
    print("\n🔍 测试MT4持仓获取...")
    print("=" * 50)
    
    try:
        # 获取活跃订单
        print("📊 获取活跃订单...")
        active_orders = mt4_client.get_active_orders()
        
        if active_orders and active_orders.get('status') == 'success':
            orders = active_orders.get('orders', [])
            print(f"✅ 活跃订单: {len(orders)}个")
            for order in orders[:3]:  # 只显示前3个
                print(f"   订单: {order}")
        else:
            print(f"❌ 获取活跃订单失败: {active_orders}")
        
        # 获取挂单
        print("📊 获取挂单...")
        pending_orders = mt4_client.get_pending_orders()
        
        if pending_orders and pending_orders.get('status') == 'success':
            orders = pending_orders.get('orders', [])
            print(f"✅ 挂单: {len(orders)}个")
        else:
            print(f"❌ 获取挂单失败: {pending_orders}")
        
        return True
        
    except Exception as e:
        print(f"❌ 持仓测试失败: {e}")
        return False

def test_trade_execution(mt4_client):
    """测试交易执行"""
    print("\n🔍 测试交易执行...")
    print("=" * 50)
    
    try:
        # 测试小额交易
        symbol = 'EURUSD'
        lot = 0.01  # 最小手数
        
        print(f"📤 测试买入: {symbol} {lot}手")
        
        # 执行买入
        buy_result = mt4_client.buy(
            symbol=symbol,
            lot=lot,
            sl=0,  # 不设置止损
            tp=0,  # 不设置止盈
            comment='测试订单'
        )
        
        print(f"📋 买入结果: {buy_result}")
        
        if buy_result and buy_result.get('status') == 'success':
            print("✅ 买入测试成功")
            order_id = buy_result.get('order_id')
            
            if order_id:
                print(f"📊 订单ID: {order_id}")
                
                # 等待一下然后平仓
                import time
                time.sleep(2)
                
                print(f"📤 测试平仓: 订单{order_id}")
                close_result = mt4_client.close_order(order_id)
                print(f"📋 平仓结果: {close_result}")
                
                if close_result and close_result.get('status') == 'success':
                    print("✅ 平仓测试成功")
                else:
                    print("❌ 平仓测试失败")
            else:
                print("⚠️ 买入成功但未获取到订单ID")
        else:
            print("❌ 买入测试失败")
            
        return buy_result and buy_result.get('status') == 'success'
        
    except Exception as e:
        print(f"❌ 交易执行测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trade_executor():
    """测试交易执行引擎"""
    print("\n🔍 测试交易执行引擎...")
    print("=" * 50)
    
    try:
        from core.execution_engine.trade_executor import TradeExecutor
        
        # 创建交易执行器
        executor = TradeExecutor()
        print("✅ 交易执行器创建成功")
        
        # 检查MT4客户端
        if executor.mt4_client:
            print("✅ MT4客户端已初始化")
            
            # 测试连接
            connected = executor.mt4_client.connect()
            print(f"📊 MT4连接状态: {connected}")
            
        else:
            print("❌ MT4客户端未初始化")
            
        # 创建测试交易决策
        test_decision = {
            'symbol': 'EURUSD',
            'action': 'enter_long',
            'volume': 0.01,
            'confidence': 0.75,
            'reasoning': '测试交易决策'
        }
        
        print(f"📋 测试交易决策: {test_decision}")
        
        # 测试订单创建
        order = executor._create_trade_order(test_decision)
        
        if order:
            print("✅ 订单创建成功")
            print(f"   订单详情: {order.symbol} {order.order_type.value} {order.volume}手")
            print(f"   入场价: {order.entry_price}")
            print(f"   止损: {order.stop_loss}")
            print(f"   止盈: {order.take_profit}")
            
            # 测试订单执行
            print("📤 测试订单执行...")
            executed_order = executor._execute_order(order)
            
            if executed_order:
                print("✅ 订单执行成功")
                print(f"   订单ID: {executed_order.order_id}")
                print(f"   成交价: {executed_order.fill_price}")
                print(f"   状态: {executed_order.status.value}")
            else:
                print("❌ 订单执行失败")
                
        else:
            print("❌ 订单创建失败")
            
        return True
        
    except Exception as e:
        print(f"❌ 交易执行引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主诊断函数"""
    print("🚀 QuantumForex Pro MT4交易诊断")
    print("=" * 60)
    print(f"⏰ 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 诊断结果
    results = []
    
    # 1. 测试MT4连接
    connected, mt4_client = test_mt4_connection()
    results.append(connected)
    
    if connected and mt4_client:
        # 2. 测试市场信息
        results.append(test_mt4_market_info(mt4_client))
        
        # 3. 测试持仓获取
        results.append(test_mt4_positions(mt4_client))
        
        # 4. 测试交易执行
        results.append(test_trade_execution(mt4_client))
    
    # 5. 测试交易执行引擎
    results.append(test_trade_executor())
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 诊断总结:")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通过测试: {passed}/{total}")
    print(f"❌ 失败测试: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 所有测试通过！MT4交易功能正常")
    elif passed > 0:
        print("\n⚠️ 部分测试通过，存在一些问题")
    else:
        print("\n❌ 所有测试失败，MT4交易功能异常")
    
    print("\n📋 可能的问题:")
    print("   1. MT4服务器未启动或连接失败")
    print("   2. MT4 EA未正确安装或配置")
    print("   3. 网络连接问题")
    print("   4. 授权码错误或过期")
    print("   5. 交易时间限制（周末不交易）")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
