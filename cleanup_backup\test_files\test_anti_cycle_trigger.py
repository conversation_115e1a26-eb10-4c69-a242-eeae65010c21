"""
测试预分析模板防循环触发机制

这个脚本测试预分析模板的防循环触发机制，验证系统是否能够避免因同一指标持续在触发范围内而循环触发完整分析
"""
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append('.')

# 导入需要测试的模块
from app.utils import prompt_template_manager

def print_header(message):
    """打印带有分隔线的标题"""
    print("\n" + "=" * 80)
    print(f" {message} ".center(80, "="))
    print("=" * 80)

def test_anti_cycle_trigger_mechanism():
    """测试预分析模板防循环触发机制"""
    print_header("测试预分析模板防循环触发机制")
    
    # 准备模板数据
    template_data = {
        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'current_price': '1.1320',
        'time_since_last_analysis': '5',  # 很短的时间，测试短时间内重复触发
        'trading_session': 'London',
        'last_analysis_time': (datetime.now().replace(minute=datetime.now().minute-5)).strftime("%Y-%m-%d %H:%M:%S"),
        'last_analysis_conclusion': '上升趋势，价格回踩13日均线',
        'last_trade_instruction': 'BUY LIMIT 1.1305',
        'last_entry_price': '1.1305',
        'last_stop_loss': '1.1290',
        'last_take_profit': '1.1335',
        'price_change_short_term': '0.02%',  # 很小的变化，测试指标没有显著变化
        'price_change_medium_term': '0.05%',
        'price_volatility': '0.08%',
        'rsi_value': '55',
        'ma13_15min_direction': 'UP',
        'ma13_1hour_direction': 'UP',
        'price_to_ma13_15min': '+12 pips',  # 在触发范围内，但没有显著变化
        'price_to_ma13_1hour': '+15 pips',
        'ma13_15min_slope': '0.3 pips/15min',
        'ma13_1hour_slope': '0.5 pips/hour',
        'active_positions': '无持仓',
        'pending_orders': 'BUYLIMIT 1.1305',
        'focus_points': '价格接近13日均线，RSI中性',
        'news_events': '无重要新闻',
        'last_trigger_indicator': 'PRICE_RETRACEMENT',  # 上次触发的指标
        'last_trigger_count': '3',  # 连续触发次数
        'last_indicator_value': '13 pips'  # 上次指标值
    }
    
    # 渲染模板
    try:
        rendered_template = prompt_template_manager.render_template('market_change_analyzer_template', template_data)
        
        # 检查防循环触发机制是否包含在渲染结果中
        highest_priority_present = "防止循环触发机制**（最高优先级）" in rendered_template
        same_indicator_rule_present = "如果上次完整分析是由相同的指标或条件触发的，且该指标或条件没有显著变化" in rendered_template
        consecutive_trigger_rule_present = "如果连续3次分析都是由同一指标触发的，第4次应提高触发阈值" in rendered_template
        persistent_indicator_rule_present = "如果某个指标持续在触发范围内超过2小时，应暂时忽略该指标" in rendered_template
        
        # 检查触发信息记录是否包含在渲染结果中
        trigger_info_present = "triggerInfo" in rendered_template
        trigger_type_present = "triggerType" in rendered_template
        is_repeat_trigger_present = "isRepeatTrigger" in rendered_template
        previous_trigger_count_present = "previousTriggerCount" in rendered_template
        indicator_change_percent_present = "indicatorChangePercent" in rendered_template
        threshold_adjusted_present = "thresholdAdjusted" in rendered_template
        
        # 检查最终提示是否包含防循环触发相关内容
        final_tip_present = "防止循环触发是最高优先级" in rendered_template
        cautious_tip_present = "如果上次分析是由相同指标触发的，请特别谨慎" in rendered_template
        threshold_adjustment_tip_present = "如果连续多次由同一指标触发，应考虑调整阈值或暂时忽略该指标" in rendered_template
        
        # 打印结果
        print(f"最高优先级存在: {highest_priority_present}")
        print(f"相同指标规则存在: {same_indicator_rule_present}")
        print(f"连续触发规则存在: {consecutive_trigger_rule_present}")
        print(f"持续指标规则存在: {persistent_indicator_rule_present}")
        print(f"触发信息存在: {trigger_info_present}")
        print(f"触发类型存在: {trigger_type_present}")
        print(f"重复触发标志存在: {is_repeat_trigger_present}")
        print(f"连续触发次数存在: {previous_trigger_count_present}")
        print(f"指标变化百分比存在: {indicator_change_percent_present}")
        print(f"阈值调整标志存在: {threshold_adjusted_present}")
        print(f"最终提示存在: {final_tip_present}")
        print(f"谨慎提示存在: {cautious_tip_present}")
        print(f"阈值调整提示存在: {threshold_adjustment_tip_present}")
        
        # 验证结果
        assert highest_priority_present, "最高优先级应该存在于渲染结果中"
        assert same_indicator_rule_present, "相同指标规则应该存在于渲染结果中"
        assert consecutive_trigger_rule_present, "连续触发规则应该存在于渲染结果中"
        assert persistent_indicator_rule_present, "持续指标规则应该存在于渲染结果中"
        assert trigger_info_present, "触发信息应该存在于渲染结果中"
        assert trigger_type_present, "触发类型应该存在于渲染结果中"
        assert is_repeat_trigger_present, "重复触发标志应该存在于渲染结果中"
        assert previous_trigger_count_present, "连续触发次数应该存在于渲染结果中"
        assert indicator_change_percent_present, "指标变化百分比应该存在于渲染结果中"
        assert threshold_adjusted_present, "阈值调整标志应该存在于渲染结果中"
        assert final_tip_present, "最终提示应该存在于渲染结果中"
        assert cautious_tip_present, "谨慎提示应该存在于渲染结果中"
        assert threshold_adjustment_tip_present, "阈值调整提示应该存在于渲染结果中"
        
        # 打印渲染结果的前500个字符
        print("\n渲染结果预览:")
        print(rendered_template[:500] + "...")
        
        print("\n测试通过: 预分析模板包含正确的防循环触发机制")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试开始')
    test_anti_cycle_trigger_mechanism()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试结束')
