#!/usr/bin/env python3
"""
测试模型路径修复
"""

import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.join(os.getcwd(), 'QuantumForex_Pro'))

def test_model_path():
    """测试模型路径"""
    print("🔍 测试模型路径修复...")
    print("=" * 60)
    
    # 当前工作目录
    cwd = os.getcwd()
    print(f"当前工作目录: {cwd}")
    
    # 测试路径
    model_dir = os.path.join("QuantumForex_Pro", "data", "models")
    print(f"模型目录路径: {model_dir}")
    print(f"绝对路径: {os.path.abspath(model_dir)}")
    print(f"目录存在: {os.path.exists(model_dir)}")
    
    if os.path.exists(model_dir):
        # 列出模型文件
        model_files = list(Path(model_dir).glob("*.pkl"))
        print(f"发现模型文件: {len(model_files)}个")
        
        # 查找Trainer模型
        trainer_files = [f for f in model_files if any(keyword in f.name for keyword in ['lightgbm', 'xgboost', 'random_forest'])]
        print(f"Trainer模型文件: {len(trainer_files)}个")
        
        for f in trainer_files[:3]:  # 只显示前3个
            print(f"  - {f.name}")
    
    print("\n🔍 测试ML引擎初始化...")
    try:
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine
        
        ml_engine = LightweightMLEngine()
        
        print("✅ ML引擎初始化成功")
        
        # 检查加载的模型
        trainer_models = 0
        for model_type, model in ml_engine.models.items():
            if model is not None:
                model_name = type(model).__name__
                if 'LGBM' in model_name or 'LGB' in model_name or 'XGB' in model_name:
                    trainer_models += 1
                    print(f"✅ {model_type.value}: {model_name} (Trainer模型)")
                else:
                    print(f"📊 {model_type.value}: {model_name} (标准模型)")
        
        print(f"\n🎯 结果: {trainer_models}个Trainer模型已加载")
        return trainer_models > 0
        
    except Exception as e:
        print(f"❌ ML引擎初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_model_path()
    if success:
        print("\n🎉 路径修复成功！")
    else:
        print("\n❌ 路径修复失败！")
