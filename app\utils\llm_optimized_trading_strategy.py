"""
LLM优化交易策略
针对LLM分析时间约束的交易策略优化
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from app.utils.logger_manager import log_analysis, LogLevel

@dataclass
class OptimizedTradingParameters:
    """优化的交易参数"""
    timeframe: str
    min_analysis_interval: int  # 最小分析间隔（分钟）
    target_risk_reward_ratio: float
    position_size_multiplier: float
    stop_loss_atr_multiplier: float
    take_profit_atr_multiplier: float
    trend_confirmation_required: bool
    volatility_adjustment: bool

@dataclass
class MarketConditionAdaptation:
    """市场条件适应"""
    volatility_level: str
    trend_strength: str
    recommended_timeframe: str
    recommended_risk_reward: float
    recommended_position_size: float

class LLMOptimizedTradingStrategy:
    """LLM优化交易策略"""

    def __init__(self):
        # 基于LLM分析时间的策略参数
        self.strategy_configs = {
            # 快速策略（适合强趋势）
            'fast': OptimizedTradingParameters(
                timeframe='30min',
                min_analysis_interval=15,  # 15分钟最小间隔
                target_risk_reward_ratio=2.0,
                position_size_multiplier=0.8,  # 降低仓位
                stop_loss_atr_multiplier=1.5,
                take_profit_atr_multiplier=3.0,
                trend_confirmation_required=True,
                volatility_adjustment=True
            ),

            # 标准策略（平衡型）
            'standard': OptimizedTradingParameters(
                timeframe='1h',
                min_analysis_interval=30,  # 30分钟最小间隔
                target_risk_reward_ratio=2.5,
                position_size_multiplier=1.0,
                stop_loss_atr_multiplier=2.0,
                take_profit_atr_multiplier=5.0,
                trend_confirmation_required=True,
                volatility_adjustment=True
            ),

            # 稳健策略（适合震荡市）
            'conservative': OptimizedTradingParameters(
                timeframe='4h',
                min_analysis_interval=60,  # 1小时最小间隔
                target_risk_reward_ratio=3.0,
                position_size_multiplier=1.2,
                stop_loss_atr_multiplier=2.5,
                take_profit_atr_multiplier=7.5,
                trend_confirmation_required=True,
                volatility_adjustment=True
            )
        }

        # LLM分析时间约束
        self.llm_constraints = {
            'min_analysis_time': 3,  # 最小分析时间（分钟）
            'max_analysis_time': 8,  # 最大分析时间（分钟）
            'avg_analysis_time': 5,  # 平均分析时间（分钟）
            'network_buffer': 2      # 网络缓冲时间（分钟）
        }

        # 市场条件阈值
        self.market_thresholds = {
            'high_volatility': 1.5,    # ATR百分比
            'low_volatility': 0.5,
            'strong_trend': 25,        # ADX值
            'weak_trend': 15
        }

        self.last_analysis_time = None
        self.current_strategy = 'standard'

    def optimize_for_llm_constraints(self, market_data: Dict, technical_indicators: Dict) -> Dict:
        """
        基于LLM约束优化交易策略

        Args:
            market_data: 市场数据
            technical_indicators: 技术指标

        Returns:
            Dict: 优化后的交易参数
        """
        try:
            # 1. 分析市场条件
            market_condition = self._analyze_market_condition(technical_indicators)

            # 2. 选择最适合的策略
            optimal_strategy = self._select_optimal_strategy(market_condition)

            # 3. 检查分析间隔约束
            analysis_allowed = self._check_analysis_interval_constraint()

            # 4. 计算优化的交易参数
            optimized_params = self._calculate_optimized_parameters(
                optimal_strategy, market_condition, market_data
            )

            # 5. 生成交易建议
            trading_recommendation = self._generate_trading_recommendation(
                optimized_params, market_condition, analysis_allowed
            )

            return {
                'strategy_name': optimal_strategy,
                'market_condition': market_condition,
                'analysis_allowed': analysis_allowed,
                'optimized_parameters': optimized_params,
                'trading_recommendation': trading_recommendation,
                'llm_efficiency_score': self._calculate_llm_efficiency_score(optimized_params)
            }

        except Exception as e:
            log_analysis(f"LLM策略优化失败: {e}", LogLevel.ERROR)
            return self._create_fallback_strategy()

    def _analyze_market_condition(self, indicators: Dict) -> MarketConditionAdaptation:
        """分析市场条件"""
        try:
            # 获取波动率信息
            volatility_level = 'normal'
            if 'volatility' in indicators and 'atr' in indicators['volatility']:
                atr_pct = indicators['volatility']['atr'].get('percentage', 1.0)
                if atr_pct > self.market_thresholds['high_volatility']:
                    volatility_level = 'high'
                elif atr_pct < self.market_thresholds['low_volatility']:
                    volatility_level = 'low'

            # 获取趋势强度
            trend_strength = 'moderate'
            if 'trend' in indicators and 'trend_strength' in indicators['trend']:
                adx = indicators['trend']['trend_strength'].get('adx', 20)
                if adx > self.market_thresholds['strong_trend']:
                    trend_strength = 'strong'
                elif adx < self.market_thresholds['weak_trend']:
                    trend_strength = 'weak'

            # 基于市场条件推荐参数
            if volatility_level == 'high' and trend_strength == 'strong':
                recommended_timeframe = '30min'
                recommended_risk_reward = 2.0
                recommended_position_size = 0.8
            elif volatility_level == 'low' and trend_strength == 'weak':
                recommended_timeframe = '4h'
                recommended_risk_reward = 3.0
                recommended_position_size = 1.2
            else:
                recommended_timeframe = '1h'
                recommended_risk_reward = 2.5
                recommended_position_size = 1.0

            return MarketConditionAdaptation(
                volatility_level=volatility_level,
                trend_strength=trend_strength,
                recommended_timeframe=recommended_timeframe,
                recommended_risk_reward=recommended_risk_reward,
                recommended_position_size=recommended_position_size
            )

        except Exception:
            return MarketConditionAdaptation(
                volatility_level='normal',
                trend_strength='moderate',
                recommended_timeframe='1h',
                recommended_risk_reward=2.5,
                recommended_position_size=1.0
            )

    def _select_optimal_strategy(self, market_condition: MarketConditionAdaptation) -> str:
        """选择最优策略"""
        try:
            # 基于市场条件选择策略
            if (market_condition.volatility_level == 'high' and
                market_condition.trend_strength == 'strong'):
                return 'fast'
            elif (market_condition.volatility_level == 'low' or
                  market_condition.trend_strength == 'weak'):
                return 'conservative'
            else:
                return 'standard'

        except Exception:
            return 'standard'

    def _check_analysis_interval_constraint(self) -> bool:
        """检查分析间隔约束"""
        try:
            if self.last_analysis_time is None:
                return True

            now = datetime.now()
            time_since_last = (now - self.last_analysis_time).total_seconds() / 60

            # 考虑LLM分析时间，最小间隔应该是分析时间的2倍
            min_interval = self.llm_constraints['avg_analysis_time'] * 2

            return time_since_last >= min_interval

        except Exception:
            return True

    def _calculate_optimized_parameters(self, strategy_name: str,
                                      market_condition: MarketConditionAdaptation,
                                      market_data: Dict) -> Dict:
        """计算优化的交易参数"""
        try:
            strategy = self.strategy_configs[strategy_name]
            current_price = market_data.get('currentPrice', 1.0)

            # 基础参数
            base_params = {
                'timeframe': strategy.timeframe,
                'min_analysis_interval': strategy.min_analysis_interval,
                'risk_reward_ratio': strategy.target_risk_reward_ratio,
                'position_size_multiplier': strategy.position_size_multiplier
            }

            # 根据市场条件调整
            adjusted_params = base_params.copy()

            # 调整风险回报比
            if market_condition.volatility_level == 'high':
                adjusted_params['risk_reward_ratio'] *= 0.8  # 降低目标
            elif market_condition.volatility_level == 'low':
                adjusted_params['risk_reward_ratio'] *= 1.2  # 提高目标

            # 调整仓位大小
            if market_condition.trend_strength == 'strong':
                adjusted_params['position_size_multiplier'] *= 1.1  # 增加仓位
            elif market_condition.trend_strength == 'weak':
                adjusted_params['position_size_multiplier'] *= 0.9  # 减少仓位

            # 计算具体的止损止盈点位
            atr_value = self._get_atr_value(market_data)

            adjusted_params.update({
                'stop_loss_distance': atr_value * strategy.stop_loss_atr_multiplier,
                'take_profit_distance': atr_value * strategy.take_profit_atr_multiplier,
                'atr_value': atr_value,
                'current_price': current_price
            })

            return adjusted_params

        except Exception:
            return self._get_default_parameters()

    def _get_atr_value(self, market_data: Dict) -> float:
        """获取ATR值"""
        try:
            # 从技术指标中获取ATR
            if 'indicators' in market_data:
                indicators = market_data['indicators']
                if isinstance(indicators, dict) and 'atr' in indicators:
                    return float(indicators['atr'])

            # 如果没有ATR，使用默认值（当前价格的1%）
            current_price = market_data.get('currentPrice', 1.0)
            return current_price * 0.01

        except Exception:
            return 0.01  # 默认ATR值

    def _generate_trading_recommendation(self, params: Dict,
                                       market_condition: MarketConditionAdaptation,
                                       analysis_allowed: bool) -> Dict:
        """生成交易建议"""
        try:
            recommendation = {
                'action': 'WAIT' if not analysis_allowed else 'ANALYZE',
                'reasoning': '',
                'risk_management': {},
                'timing_advice': {},
                'efficiency_notes': []
            }

            if not analysis_allowed:
                # 注意：这里不应该阻止分析，因为定期分析已经检查过时间间隔
                # 这个检查是多余的，会导致时间间隔判断冲突
                recommendation['action'] = 'ANALYZE'  # 允许分析继续
                recommendation['reasoning'] = f"策略优化建议：继续执行分析（时间间隔检查已在上层完成）"
                return recommendation

            # 生成具体建议
            recommendation['action'] = 'ANALYZE'
            recommendation['reasoning'] = f"市场条件：{market_condition.volatility_level}波动率，{market_condition.trend_strength}趋势强度"

            # 风险管理建议
            recommendation['risk_management'] = {
                'recommended_risk_reward': params['risk_reward_ratio'],
                'position_size_multiplier': params['position_size_multiplier'],
                'stop_loss_distance': params['stop_loss_distance'],
                'take_profit_distance': params['take_profit_distance'],
                'max_risk_per_trade': f"{params['position_size_multiplier'] * 3:.1f}%"
            }

            # 时机建议
            recommendation['timing_advice'] = {
                'optimal_timeframe': params['timeframe'],
                'next_analysis_window': f"{params['min_analysis_interval']}分钟后",
                'market_condition_suitability': self._assess_market_suitability(market_condition)
            }

            # 效率提升建议
            recommendation['efficiency_notes'] = self._generate_efficiency_notes(params, market_condition)

            return recommendation

        except Exception:
            return {
                'action': 'WAIT',
                'reasoning': '策略计算失败，建议等待',
                'risk_management': {},
                'timing_advice': {},
                'efficiency_notes': []
            }

    def _assess_market_suitability(self, market_condition: MarketConditionAdaptation) -> str:
        """评估市场适合度"""
        if (market_condition.volatility_level == 'high' and
            market_condition.trend_strength == 'strong'):
            return '优秀 - 高波动率强趋势，适合LLM深度分析'
        elif (market_condition.volatility_level == 'low' and
              market_condition.trend_strength == 'weak'):
            return '一般 - 低波动率弱趋势，建议延长分析周期'
        else:
            return '良好 - 市场条件适中，标准策略执行'

    def _generate_efficiency_notes(self, params: Dict,
                                 market_condition: MarketConditionAdaptation) -> List[str]:
        """生成效率提升建议"""
        notes = []

        # 基于LLM分析时间的建议
        notes.append(f"建议使用{params['timeframe']}时间框架，匹配LLM分析时间")

        # 基于市场条件的建议
        if market_condition.volatility_level == 'high':
            notes.append("高波动率环境，建议提高风险回报比目标")

        if market_condition.trend_strength == 'strong':
            notes.append("强趋势环境，可适当增加仓位规模")

        # 基于风险回报比的建议
        if params['risk_reward_ratio'] >= 3.0:
            notes.append("高风险回报比设置，适合LLM深度分析的优势")

        return notes

    def _calculate_llm_efficiency_score(self, params: Dict) -> float:
        """计算LLM效率评分"""
        try:
            score = 0.0

            # 时间框架效率（30%权重）
            timeframe_score = {
                '15min': 0.3,  # 不适合LLM
                '30min': 0.6,  # 勉强适合
                '1h': 0.8,     # 较适合
                '4h': 1.0      # 最适合
            }.get(params['timeframe'], 0.5)
            score += timeframe_score * 0.3

            # 风险回报比效率（40%权重）
            rr_ratio = params['risk_reward_ratio']
            if rr_ratio >= 3.0:
                rr_score = 1.0
            elif rr_ratio >= 2.5:
                rr_score = 0.8
            elif rr_ratio >= 2.0:
                rr_score = 0.6
            else:
                rr_score = 0.4
            score += rr_score * 0.4

            # 分析间隔效率（30%权重）
            interval = params['min_analysis_interval']
            if interval >= 60:
                interval_score = 1.0
            elif interval >= 30:
                interval_score = 0.8
            elif interval >= 15:
                interval_score = 0.6
            else:
                interval_score = 0.4
            score += interval_score * 0.3

            return min(1.0, max(0.0, score))

        except Exception:
            return 0.5

    def _create_fallback_strategy(self) -> Dict:
        """创建回退策略"""
        return {
            'strategy_name': 'fallback',
            'market_condition': MarketConditionAdaptation(
                volatility_level='normal',
                trend_strength='moderate',
                recommended_timeframe='1h',
                recommended_risk_reward=2.5,
                recommended_position_size=1.0
            ),
            'analysis_allowed': True,
            'optimized_parameters': self._get_default_parameters(),
            'trading_recommendation': {
                'action': 'ANALYZE',
                'reasoning': '使用默认策略参数',
                'risk_management': {'max_risk_per_trade': '3.0%'},
                'timing_advice': {'optimal_timeframe': '1h'},
                'efficiency_notes': ['使用保守的默认参数']
            },
            'llm_efficiency_score': 0.7
        }

    def _get_default_parameters(self) -> Dict:
        """获取默认参数"""
        return {
            'timeframe': '1h',
            'min_analysis_interval': 30,
            'risk_reward_ratio': 2.5,
            'position_size_multiplier': 1.0,
            'stop_loss_distance': 0.01,
            'take_profit_distance': 0.025,
            'atr_value': 0.01,
            'current_price': 1.0
        }

    def update_last_analysis_time(self):
        """更新最后分析时间"""
        self.last_analysis_time = datetime.now()

    def get_strategy_summary(self) -> Dict:
        """获取策略摘要"""
        return {
            'available_strategies': list(self.strategy_configs.keys()),
            'current_strategy': self.current_strategy,
            'llm_constraints': self.llm_constraints,
            'market_thresholds': self.market_thresholds,
            'last_analysis_time': self.last_analysis_time.isoformat() if self.last_analysis_time else None
        }
