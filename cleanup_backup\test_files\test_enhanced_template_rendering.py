"""
测试强化后的提示词模板渲染

这个脚本测试强化后的最终分析提示词模板的渲染，验证格式警告和JSON结构示例是否正确包含
"""
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append('.')

# 导入需要测试的模块
from app.utils import prompt_template_manager

def print_header(message):
    """打印带有分隔线的标题"""
    print("\n" + "=" * 80)
    print(f" {message} ".center(80, "="))
    print("=" * 80)

def test_enhanced_final_analysis_template():
    """测试强化后的最终分析提示词模板"""
    print_header("测试强化后的最终分析提示词模板")
    
    # 准备模板数据
    template_data = {
        'symbol': 'EURUSD',
        'analysis_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'current_price': '1.1320',
        'initial_insights': '初始分析要点...',
        'detail_insights': '详细分析要点...',
        'news_data': '最新相关新闻...',
        'calendar_data': '经济日历事件...',
        'positions_data': '当前持仓...',
        'pending_orders_data': '当前挂单...',
        'history_analysis': '历史分析记录...',
        'trade_results': '历史交易结果...'
    }
    
    # 渲染模板
    try:
        rendered_template = prompt_template_manager.render_template('final_analysis_template', template_data)
        
        # 检查强化的格式警告是否包含在渲染结果中
        structure_warning_present = "必须严格遵循以下JSON结构，不要添加额外的字段或嵌套结构" in rendered_template
        create_own_warning_present = "不要创建自己的JSON结构" in rendered_template
        analysis_field_warning_present = "不要添加\"analysis\"、\"instructions\"或\"summary\"等字段" in rendered_template
        
        # 检查JSON结构示例是否包含在渲染结果中
        new_order_example_present = "\"action\": \"BUY或SELL\"" in rendered_template
        modify_order_example_present = "\"action\": \"MODIFY\"" in rendered_template
        delete_order_example_present = "\"action\": \"DELETE\"" in rendered_template
        none_action_example_present = "\"action\": \"NONE\"" in rendered_template
        
        # 检查最终格式检查部分是否包含在渲染结果中
        final_check_present = "## 最终格式检查" in rendered_template
        
        # 打印结果
        print(f"结构警告存在: {structure_warning_present}")
        print(f"自定义结构警告存在: {create_own_warning_present}")
        print(f"字段警告存在: {analysis_field_warning_present}")
        print(f"新建订单示例存在: {new_order_example_present}")
        print(f"修改订单示例存在: {modify_order_example_present}")
        print(f"删除订单示例存在: {delete_order_example_present}")
        print(f"观望示例存在: {none_action_example_present}")
        print(f"最终格式检查存在: {final_check_present}")
        
        # 验证结果
        assert structure_warning_present, "结构警告应该存在于渲染结果中"
        assert create_own_warning_present, "自定义结构警告应该存在于渲染结果中"
        assert analysis_field_warning_present, "字段警告应该存在于渲染结果中"
        assert new_order_example_present, "新建订单示例应该存在于渲染结果中"
        assert modify_order_example_present, "修改订单示例应该存在于渲染结果中"
        assert delete_order_example_present, "删除订单示例应该存在于渲染结果中"
        assert none_action_example_present, "观望示例应该存在于渲染结果中"
        assert final_check_present, "最终格式检查应该存在于渲染结果中"
        
        # 打印渲染结果的前500个字符
        print("\n渲染结果预览:")
        print(rendered_template[:500] + "...")
        
        print("\n测试通过: 强化后的最终分析提示词模板包含正确的格式警告和JSON结构示例")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试开始')
    test_enhanced_final_analysis_template()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试结束')
