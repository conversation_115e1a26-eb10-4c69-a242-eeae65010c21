"""
MT4客户端服务器
用于处理来自MT4客户端的请求，包括注册、登录和获取信号
"""
import os
import json
import uuid
import time
import zmq
import threading
import hashlib
import sqlite3
from datetime import datetime
from app.utils.error_logger import log_error, log_operation, ErrorType, OperationType
from app.utils import forex_analysis_history
from app.utils import mt4_client

# 数据库文件路径
DB_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'appdata', 'users.db')

# 客户端服务器地址
CLIENT_SERVER_ADDRESS = os.getenv('CLIENT_SERVER_ADDRESS', 'tcp://*:5558')

# 全局变量
server_running = False
server_thread = None
context = None
socket = None

# 用户数据库操作
def init_db():
    """初始化用户数据库"""
    try:
        # 确保appdata目录存在
        appdata_dir = os.path.dirname(DB_PATH)
        if not os.path.exists(appdata_dir):
            os.makedirs(appdata_dir)

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 创建用户表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            username TEXT UNIQUE,
            password TEXT,
            auth_code TEXT UNIQUE,
            account_type TEXT,
            created_at TEXT,
            last_login TEXT,
            expiry_date TEXT
        )
        ''')

        conn.commit()
        conn.close()

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 用户数据库初始化成功')
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 用户数据库初始化失败: {e}')
        log_error(
            error_type=ErrorType.DATABASE_ERROR,
            message=f'用户数据库初始化失败: {e}',
            details={'db_path': DB_PATH},
            operation=OperationType.DATABASE
        )

def register_user(username, password, account_id=None):
    """注册新用户"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 检查用户名是否已存在
        cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
        if cursor.fetchone():
            conn.close()
            return {
                'status': 'error',
                'message': '用户名已存在'
            }

        # 生成用户ID
        user_id = account_id or str(uuid.uuid4())

        # 生成授权码
        auth_code = f"AUTH-{user_id}-{int(time.time())}"

        # 密码加密
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        # 设置账户类型和过期时间
        account_type = '标准账户'
        created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        expiry_date = datetime.now().replace(year=datetime.now().year + 1).strftime('%Y-%m-%d %H:%M:%S')

        # 插入用户记录
        cursor.execute(
            'INSERT INTO users (id, username, password, auth_code, account_type, created_at, last_login, expiry_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
            (user_id, username, password_hash, auth_code, account_type, created_at, created_at, expiry_date)
        )

        conn.commit()
        conn.close()

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 用户注册成功: {username}')

        return {
            'status': 'success',
            'message': '注册成功',
            'user_id': user_id,
            'username': username,
            'auth_code': auth_code,
            'account_type': account_type,
            'expiry_date': expiry_date
        }
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 用户注册失败: {e}')
        log_error(
            error_type=ErrorType.DATABASE_ERROR,
            message=f'用户注册失败: {e}',
            details={'username': username},
            operation=OperationType.DATABASE
        )
        return {
            'status': 'error',
            'message': f'注册失败: {e}'
        }

def login_user(username, password):
    """用户登录"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 密码加密
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        # 查询用户
        cursor.execute('SELECT id, auth_code, account_type, expiry_date FROM users WHERE username = ? AND password = ?',
                      (username, password_hash))
        user = cursor.fetchone()

        if not user:
            conn.close()
            return {
                'status': 'error',
                'message': '用户名或密码错误'
            }

        # 更新最后登录时间
        last_login = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cursor.execute('UPDATE users SET last_login = ? WHERE id = ?', (last_login, user[0]))

        conn.commit()
        conn.close()

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 用户登录成功: {username}')

        return {
            'status': 'success',
            'message': '登录成功',
            'user_id': user[0],
            'username': username,
            'auth_code': user[1],
            'account_type': user[2],
            'expiry_date': user[3]
        }
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 用户登录失败: {e}')
        log_error(
            error_type=ErrorType.DATABASE_ERROR,
            message=f'用户登录失败: {e}',
            details={'username': username},
            operation=OperationType.DATABASE
        )
        return {
            'status': 'error',
            'message': f'登录失败: {e}'
        }

def verify_auth_code(auth_code):
    """验证授权码"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询用户
        cursor.execute('SELECT id, username, account_type, expiry_date FROM users WHERE auth_code = ?', (auth_code,))
        user = cursor.fetchone()

        if not user:
            conn.close()
            return {
                'status': 'error',
                'message': '无效的授权码'
            }

        # 检查过期时间
        expiry_date = datetime.strptime(user[3], '%Y-%m-%d %H:%M:%S')
        if expiry_date < datetime.now():
            conn.close()
            return {
                'status': 'error',
                'message': '授权码已过期'
            }

        # 更新最后登录时间
        last_login = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cursor.execute('UPDATE users SET last_login = ? WHERE id = ?', (last_login, user[0]))

        conn.commit()
        conn.close()

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 授权码验证成功: {auth_code}')

        return {
            'status': 'success',
            'message': '授权验证成功',
            'user_id': user[0],
            'username': user[1],
            'account_type': user[2],
            'expiry_date': user[3]
        }
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 授权码验证失败: {e}')
        log_error(
            error_type=ErrorType.DATABASE_ERROR,
            message=f'授权码验证失败: {e}',
            details={'auth_code': auth_code},
            operation=OperationType.DATABASE
        )
        return {
            'status': 'error',
            'message': f'授权验证失败: {e}'
        }

def get_trading_signals():
    """获取交易信号"""
    try:
        # 获取最新的分析结果
        analysis = forex_analysis_history.get_latest_analysis_record()

        if not analysis:
            return {
                'status': 'error',
                'message': '没有可用的分析结果'
            }

        # 提取交易指令
        trade_instructions = analysis.get('tradeInstructions', {})

        # 如果没有交易指令或指令为观望
        if not trade_instructions or trade_instructions.get('action') == 'NONE':
            return {
                'status': 'success',
                'message': '当前没有交易信号',
                'signals': []
            }

        # 构建信号
        signal = {
            'id': str(uuid.uuid4()),
            'symbol': trade_instructions.get('symbol', 'EURUSD'),
            'direction': trade_instructions.get('action', 'NONE'),
            'price': trade_instructions.get('entryPrice', 0),
            'sl': trade_instructions.get('stopLoss', 0),
            'tp': trade_instructions.get('takeProfit', 0),
            'lot': trade_instructions.get('lot', 0.1),
            'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        return {
            'status': 'success',
            'message': '获取交易信号成功',
            'signals': [signal]
        }
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 获取交易信号失败: {e}')
        log_error(
            error_type=ErrorType.SYSTEM_ERROR,
            message=f'获取交易信号失败: {e}',
            details={},
            operation=OperationType.OTHER
        )
        return {
            'status': 'error',
            'message': f'获取交易信号失败: {e}'
        }

def handle_client_request(request):
    """处理客户端请求"""
    action = request.get('action')

    if action == 'REGISTER':
        # 处理注册请求
        username = request.get('username')
        password = request.get('password')
        account_id = request.get('account_id')

        if not username or not password:
            return {
                'status': 'error',
                'message': '缺少用户名或密码'
            }

        return register_user(username, password, account_id)

    elif action == 'LOGIN_WITH_CREDENTIALS':
        # 处理凭证登录请求
        username = request.get('username')
        password = request.get('password')

        if not username or not password:
            return {
                'status': 'error',
                'message': '缺少用户名或密码'
            }

        return login_user(username, password)

    elif action == 'LOGIN':
        # 处理授权码登录请求
        auth_code = request.get('auth_code')

        if not auth_code:
            return {
                'status': 'error',
                'message': '缺少授权码'
            }

        return verify_auth_code(auth_code)

    elif action == 'GET_SIGNALS':
        # 处理获取信号请求
        auth_code = request.get('auth_code')

        if not auth_code:
            return {
                'status': 'error',
                'message': '缺少授权码'
            }

        # 验证授权码
        auth_result = verify_auth_code(auth_code)
        if auth_result.get('status') != 'success':
            return auth_result

        # 获取交易信号
        return get_trading_signals()

    else:
        # 其他请求转发到MT4服务器
        try:
            # 确保MT4客户端已连接
            if not mt4_client.mt4_client.is_connected:
                mt4_client.mt4_client.connect()

            # 转发请求
            response = mt4_client.mt4_client.send_request(request)
            return response
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 转发请求到MT4服务器失败: {e}')
            return {
                'status': 'error',
                'message': f'转发请求到MT4服务器失败: {e}'
            }

def start_server():
    """启动客户端服务器"""
    global server_running, context, socket

    try:
        # 检查服务器是否已经在运行
        if server_running:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端服务器已经在运行中，跳过启动')
            return

        # 初始化用户数据库
        init_db()

        # 创建ZeroMQ上下文和套接字
        context = zmq.Context()
        socket = context.socket(zmq.REP)

        # 尝试绑定地址，如果失败则尝试其他端口
        try:
            socket.bind(CLIENT_SERVER_ADDRESS)
        except zmq.error.ZMQError as e:
            if "Address in use" in str(e):
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 端口被占用，尝试使用其他端口')
                # 尝试其他端口
                for port in range(5559, 5565):
                    try:
                        alt_address = f'tcp://*:{port}'
                        socket.bind(alt_address)
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 使用替代端口: {alt_address}')
                        break
                    except zmq.error.ZMQError:
                        continue
                else:
                    raise e  # 如果所有端口都被占用，抛出原始错误
            else:
                raise e

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端服务器已启动，监听地址: {CLIENT_SERVER_ADDRESS}')
        server_running = True

        while server_running:
            try:
                # 接收请求
                message = socket.recv_string()
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 收到客户端请求: {message}')

                # 解析请求
                request = json.loads(message)

                # 处理请求
                response = handle_client_request(request)

                # 发送响应
                socket.send_string(json.dumps(response))
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 已发送响应: {response}')

            except zmq.error.Again:
                # 超时，继续循环
                continue
            except Exception as e:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 处理客户端请求时出错: {e}')

                # 发送错误响应
                try:
                    socket.send_string(json.dumps({
                        'status': 'error',
                        'message': f'服务器内部错误: {str(e)}'
                    }))
                except:
                    pass

                # 记录错误
                log_error(
                    error_type=ErrorType.SYSTEM_ERROR,
                    message=f'处理客户端请求时出错: {e}',
                    details={},
                    operation=OperationType.OTHER
                )
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 启动MT4客户端服务器失败: {e}')
        server_running = False

        # 记录错误
        log_error(
            error_type=ErrorType.SYSTEM_ERROR,
            message=f'启动MT4客户端服务器失败: {e}',
            details={'address': CLIENT_SERVER_ADDRESS},
            operation=OperationType.OTHER
        )
    finally:
        # 清理资源
        if socket:
            socket.close()
        if context:
            context.term()

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端服务器已停止')
        server_running = False

def start_server_thread():
    """在单独的线程中启动服务器"""
    global server_thread

    if server_thread and server_thread.is_alive():
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端服务器已经在运行中')
        return

    server_thread = threading.Thread(target=start_server)
    server_thread.daemon = True
    server_thread.start()

    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端服务器线程已启动')

def stop_server():
    """停止服务器"""
    global server_running

    server_running = False
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 正在停止MT4客户端服务器...')

    # 等待服务器线程结束
    if server_thread and server_thread.is_alive():
        server_thread.join(timeout=5)
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端服务器线程已停止')
