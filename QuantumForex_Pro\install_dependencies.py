#!/usr/bin/env python3
"""
QuantumForex Pro 依赖安装脚本
自动检测并安装必要的Python包
适用于Windows Server 2012环境
"""

import subprocess
import sys
import os
from datetime import datetime

def print_status(message, status="INFO"):
    """打印状态信息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    status_symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARNING": "⚠️"
    }
    symbol = status_symbols.get(status, "ℹ️")
    print(f"[{timestamp}] {symbol} {message}")

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print_status(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print_status("Python版本过低，需要3.8+", "ERROR")
        return False
    
    print_status("Python版本检查通过", "SUCCESS")
    return True

def install_package(package_name, pip_name=None):
    """安装单个包"""
    if pip_name is None:
        pip_name = package_name
    
    try:
        # 先尝试导入
        __import__(package_name)
        print_status(f"{package_name} 已安装", "SUCCESS")
        return True
    except ImportError:
        print_status(f"正在安装 {pip_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", pip_name])
            print_status(f"{pip_name} 安装成功", "SUCCESS")
            return True
        except subprocess.CalledProcessError as e:
            print_status(f"{pip_name} 安装失败: {e}", "ERROR")
            return False

def install_core_dependencies():
    """安装核心依赖"""
    print_status("开始安装核心依赖包...")
    
    # 核心依赖列表 (按重要性排序)
    core_packages = [
        ("pandas", "pandas>=1.3.0"),
        ("numpy", "numpy>=1.21.0"),
        ("sklearn", "scikit-learn>=1.6.0"),
        ("pymysql", "PyMySQL>=1.0.2"),
        ("dotenv", "python-dotenv>=0.19.0"),
        ("requests", "requests>=2.25.0"),
        ("psutil", "psutil>=7.0.0"),
        ("scipy", "scipy>=1.7.0"),
        ("zmq", "pyzmq>=24.0.0"),
        ("dateutil", "python-dateutil>=2.8.0")
    ]
    
    success_count = 0
    total_count = len(core_packages)
    
    for package_name, pip_name in core_packages:
        if install_package(package_name, pip_name):
            success_count += 1
    
    print_status(f"核心依赖安装完成: {success_count}/{total_count}", 
                "SUCCESS" if success_count == total_count else "WARNING")
    
    return success_count == total_count

def install_optional_dependencies():
    """安装可选依赖"""
    print_status("开始安装可选依赖包...")
    
    optional_packages = [
        ("redis", "redis>=4.0.0"),
        ("ujson", "ujson>=4.0.0"),
        ("colorlog", "colorlog>=6.0.0"),
        ("yaml", "PyYAML>=6.0"),
        ("websocket", "websocket-client>=1.2.0"),
        ("matplotlib", "matplotlib>=3.5.0"),
        ("cryptography", "cryptography>=3.4.0")
    ]
    
    success_count = 0
    for package_name, pip_name in optional_packages:
        if install_package(package_name, pip_name):
            success_count += 1
    
    print_status(f"可选依赖安装完成: {success_count}/{len(optional_packages)}")

def test_imports():
    """测试关键模块导入"""
    print_status("测试关键模块导入...")
    
    test_modules = [
        "pandas",
        "numpy", 
        "sklearn",
        "pymysql",
        "dotenv",
        "requests",
        "psutil",
        "scipy",
        "zmq"
    ]
    
    failed_modules = []
    
    for module in test_modules:
        try:
            __import__(module)
            print_status(f"{module} 导入成功", "SUCCESS")
        except ImportError as e:
            print_status(f"{module} 导入失败: {e}", "ERROR")
            failed_modules.append(module)
    
    if failed_modules:
        print_status(f"以下模块导入失败: {', '.join(failed_modules)}", "ERROR")
        return False
    else:
        print_status("所有关键模块导入成功", "SUCCESS")
        return True

def test_quantumforex_imports():
    """测试QuantumForex Pro模块导入"""
    print_status("测试QuantumForex Pro模块导入...")
    
    try:
        from utils.db_client import test_connection
        print_status("utils.db_client 导入成功", "SUCCESS")
    except ImportError as e:
        print_status(f"utils.db_client 导入失败: {e}", "ERROR")
        return False
    
    try:
        from utils.mt4_client import MT4Client
        print_status("utils.mt4_client 导入成功", "SUCCESS")
    except ImportError as e:
        print_status(f"utils.mt4_client 导入失败: {e}", "ERROR")
        return False
    
    try:
        from utils.intelligent_pair_selector import IntelligentPairSelector
        print_status("utils.intelligent_pair_selector 导入成功", "SUCCESS")
    except ImportError as e:
        print_status(f"utils.intelligent_pair_selector 导入失败: {e}", "ERROR")
        return False
    
    print_status("QuantumForex Pro模块导入测试通过", "SUCCESS")
    return True

def main():
    """主函数"""
    print_status("=" * 60)
    print_status("QuantumForex Pro 依赖安装脚本")
    print_status("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 升级pip
    print_status("升级pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print_status("pip升级成功", "SUCCESS")
    except subprocess.CalledProcessError:
        print_status("pip升级失败，继续安装", "WARNING")
    
    # 安装核心依赖
    if not install_core_dependencies():
        print_status("核心依赖安装不完整，可能影响系统运行", "WARNING")
    
    # 安装可选依赖
    install_optional_dependencies()
    
    # 测试导入
    if test_imports():
        print_status("Python依赖测试通过", "SUCCESS")
    else:
        print_status("Python依赖测试失败", "ERROR")
        sys.exit(1)
    
    # 测试QuantumForex模块
    if test_quantumforex_imports():
        print_status("QuantumForex Pro准备就绪", "SUCCESS")
    else:
        print_status("QuantumForex Pro模块测试失败", "ERROR")
        sys.exit(1)
    
    print_status("=" * 60)
    print_status("安装完成！可以运行 python start.py 启动系统", "SUCCESS")
    print_status("=" * 60)

if __name__ == "__main__":
    main()
