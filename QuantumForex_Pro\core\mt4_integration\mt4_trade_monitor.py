#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MT4交易监控器
综合监控MT4交易状态，提供完整的交易生命周期管理
"""

import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
import logging

from .mt4_order_tracker import MT4OrderTracker
from .trade_result_sync import TradeResultSync
from ..learning_system import LearningCoordinator

class MT4TradeMonitor:
    """MT4交易监控器"""
    
    def __init__(self, learning_coordinator: LearningCoordinator):
        self.learning_coordinator = learning_coordinator
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.trade_sync = TradeResultSync(learning_coordinator)
        
        # 监控状态
        self.monitoring = False
        self.monitor_thread = None
        
        # 统计信息
        self.monitor_stats = {
            'start_time': None,
            'total_orders_detected': 0,
            'total_orders_closed': 0,
            'total_sync_success': 0,
            'total_sync_failed': 0,
            'last_activity': None
        }
        
        # 注册学习系统的交易记录回调
        self._setup_learning_integration()
        
    def _setup_learning_integration(self):
        """设置学习系统集成"""
        try:
            # 重写学习协调器的交易记录方法，添加MT4同步
            original_record_entry = self.learning_coordinator.record_trade_entry
            
            def enhanced_record_entry(trade_data: Dict) -> str:
                # 调用原始方法
                trade_id = original_record_entry(trade_data)
                
                # 注册到MT4同步系统
                if trade_id and self.trade_sync.is_sync_enabled():
                    self.trade_sync.register_system_trade(trade_id, trade_data)
                    self.logger.info(f"🔗 交易已注册到MT4同步: {trade_id}")
                
                return trade_id
            
            # 替换方法
            self.learning_coordinator.record_trade_entry = enhanced_record_entry
            
            self.logger.info("✅ 学习系统集成设置完成")
            
        except Exception as e:
            self.logger.error(f"设置学习系统集成失败: {e}")
    
    def start_monitoring(self):
        """开始监控"""
        try:
            if self.monitoring:
                self.logger.warning("MT4交易监控已经在运行")
                return
            
            # 启动交易结果同步
            self.trade_sync.start()
            
            # 启动监控线程
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            
            # 更新统计
            self.monitor_stats['start_time'] = datetime.now()
            
            self.logger.info("🚀 MT4交易监控启动成功")
            
        except Exception as e:
            self.logger.error(f"启动MT4交易监控失败: {e}")
            self.monitoring = False
    
    def stop_monitoring(self):
        """停止监控"""
        try:
            self.monitoring = False
            
            # 停止交易结果同步
            self.trade_sync.stop()
            
            # 停止监控线程
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=10)
            
            self.logger.info("⏹️ MT4交易监控已停止")
            
        except Exception as e:
            self.logger.error(f"停止MT4交易监控失败: {e}")
    
    def _monitor_loop(self):
        """监控循环"""
        self.logger.info("MT4交易监控循环启动")
        
        while self.monitoring:
            try:
                # 更新统计信息
                self._update_statistics()
                
                # 检查系统健康状态
                self._check_system_health()
                
                # 定期报告状态
                self._periodic_status_report()
                
                # 等待下次检查
                time.sleep(30)  # 30秒检查一次
                
            except Exception as e:
                self.logger.error(f"MT4交易监控循环异常: {e}")
                time.sleep(30)
        
        self.logger.info("MT4交易监控循环结束")
    
    def _update_statistics(self):
        """更新统计信息"""
        try:
            # 获取同步统计
            sync_stats = self.trade_sync.get_sync_statistics()
            
            # 更新监控统计
            self.monitor_stats.update({
                'last_activity': datetime.now(),
                'sync_stats': sync_stats
            })
            
        except Exception as e:
            self.logger.error(f"更新统计信息失败: {e}")
    
    def _check_system_health(self):
        """检查系统健康状态"""
        try:
            # 检查同步服务状态
            if not self.trade_sync.is_sync_enabled():
                self.logger.warning("⚠️ 交易结果同步已禁用")
            
            # 检查待匹配交易数量
            sync_stats = self.trade_sync.get_sync_statistics()
            pending_count = sync_stats.get('pending_mappings', 0)
            
            if pending_count > 10:
                self.logger.warning(f"⚠️ 待匹配交易过多: {pending_count}")
            
            # 检查匹配成功率
            success_rate = sync_stats.get('success_rate', 0)
            if success_rate < 0.8 and sync_stats.get('total_mappings', 0) > 5:
                self.logger.warning(f"⚠️ 交易匹配成功率较低: {success_rate:.1%}")
            
        except Exception as e:
            self.logger.error(f"检查系统健康状态失败: {e}")
    
    def _periodic_status_report(self):
        """定期状态报告"""
        try:
            # 每5分钟报告一次状态
            if not hasattr(self, '_last_report_time'):
                self._last_report_time = datetime.now()
            
            if (datetime.now() - self._last_report_time).total_seconds() >= 300:
                self._print_status_report()
                self._last_report_time = datetime.now()
                
        except Exception as e:
            self.logger.error(f"定期状态报告失败: {e}")
    
    def _print_status_report(self):
        """打印状态报告"""
        try:
            sync_stats = self.trade_sync.get_sync_statistics()
            
            print("\n" + "="*60)
            print("📊 MT4交易监控状态报告")
            print("="*60)
            print(f"🕐 运行时间: {self._get_uptime()}")
            print(f"🔗 同步状态: {'启用' if sync_stats.get('sync_enabled', False) else '禁用'}")
            print(f"📈 总映射数: {sync_stats.get('total_mappings', 0)}")
            print(f"⏳ 待匹配: {sync_stats.get('pending_mappings', 0)}")
            print(f"✅ 已匹配: {sync_stats.get('matched_mappings', 0)}")
            print(f"🏁 已关闭: {sync_stats.get('closed_mappings', 0)}")
            print(f"📊 成功率: {sync_stats.get('success_rate', 0):.1%}")
            print("="*60)
            
        except Exception as e:
            self.logger.error(f"打印状态报告失败: {e}")
    
    def _get_uptime(self) -> str:
        """获取运行时间"""
        try:
            if self.monitor_stats['start_time']:
                uptime = datetime.now() - self.monitor_stats['start_time']
                hours = int(uptime.total_seconds() // 3600)
                minutes = int((uptime.total_seconds() % 3600) // 60)
                return f"{hours}小时{minutes}分钟"
            return "未知"
        except:
            return "未知"
    
    def force_sync_all_trades(self):
        """强制同步所有交易"""
        try:
            self.logger.info("🔄 开始强制同步所有交易...")
            
            # 强制同步最近24小时的交易
            self.trade_sync.force_sync_recent_trades(hours=24)
            
            self.logger.info("✅ 强制同步完成")
            
        except Exception as e:
            self.logger.error(f"强制同步所有交易失败: {e}")
    
    def get_monitoring_status(self) -> Dict:
        """获取监控状态"""
        try:
            sync_stats = self.trade_sync.get_sync_statistics()
            
            return {
                'monitoring': self.monitoring,
                'uptime': self._get_uptime(),
                'monitor_stats': self.monitor_stats,
                'sync_stats': sync_stats,
                'system_health': self._get_system_health_score()
            }
            
        except Exception as e:
            self.logger.error(f"获取监控状态失败: {e}")
            return {'error': str(e)}
    
    def _get_system_health_score(self) -> float:
        """获取系统健康评分"""
        try:
            sync_stats = self.trade_sync.get_sync_statistics()
            
            # 基础分数
            score = 1.0
            
            # 同步启用状态
            if not sync_stats.get('sync_enabled', False):
                score -= 0.3
            
            # 匹配成功率
            success_rate = sync_stats.get('success_rate', 0)
            if success_rate < 0.8:
                score -= (0.8 - success_rate) * 0.5
            
            # 待匹配交易数量
            pending_count = sync_stats.get('pending_mappings', 0)
            if pending_count > 5:
                score -= min(0.2, pending_count * 0.02)
            
            return max(0.0, score)
            
        except Exception as e:
            self.logger.error(f"计算系统健康评分失败: {e}")
            return 0.0
    
    def enable_sync(self, enabled: bool = True):
        """启用/禁用同步"""
        self.trade_sync.enable_sync(enabled)
        self.logger.info(f"MT4交易同步 {'启用' if enabled else '禁用'}")
    
    def is_monitoring(self) -> bool:
        """检查是否正在监控"""
        return self.monitoring
    
    def get_recent_mt4_orders(self, hours: int = 24) -> List:
        """获取最近的MT4订单"""
        try:
            return self.trade_sync.mt4_tracker.get_recent_orders(hours)
        except Exception as e:
            self.logger.error(f"获取最近MT4订单失败: {e}")
            return []
    
    def manual_match_trade(self, system_trade_id: str, mt4_ticket: int) -> bool:
        """手动匹配交易"""
        try:
            # 获取MT4订单
            mt4_order = self.trade_sync.mt4_tracker.get_order_by_ticket(mt4_ticket)
            if not mt4_order:
                self.logger.error(f"未找到MT4订单: {mt4_ticket}")
                return False
            
            # 创建手动映射
            from .trade_result_sync import TradeMapping
            
            mapping = TradeMapping(
                system_trade_id=system_trade_id,
                mt4_ticket=mt4_ticket,
                symbol=mt4_order.symbol,
                action=mt4_order.order_type,
                volume=mt4_order.volume,
                system_entry_price=mt4_order.open_price,
                mt4_entry_price=mt4_order.open_price,
                created_at=mt4_order.open_time,
                status="MATCHED"
            )
            
            # 保存映射
            self.trade_sync.trade_mappings[system_trade_id] = mapping
            
            # 如果订单已关闭，同步结果
            if mt4_order.status == "CLOSED":
                self.trade_sync._sync_trade_result(mapping, mt4_order)
            
            self.logger.info(f"✅ 手动匹配成功: {system_trade_id} ↔ MT4#{mt4_ticket}")
            return True
            
        except Exception as e:
            self.logger.error(f"手动匹配交易失败: {e}")
            return False
