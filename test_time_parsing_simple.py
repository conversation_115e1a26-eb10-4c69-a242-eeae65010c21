#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的时间解析测试
直接测试时间格式解析逻辑
"""

from datetime import datetime

def test_time_parsing():
    """测试时间解析逻辑"""
    print("🧪 测试时间格式解析逻辑...")
    
    # 测试不同格式的时间字符串
    test_time_formats = [
        "2025.05.29 21:23:28",  # MT4格式
        "2025-05-29 21:23:28",  # 标准格式
        "2025-05-29T21:23:28",  # ISO格式
        "invalid_time_format"   # 无效格式
    ]
    
    print("📊 测试不同时间格式的解析:")
    
    for time_str in test_time_formats:
        print(f"🔍 测试时间格式: '{time_str}'")
        
        # 模拟修复后的解析逻辑
        open_time = time_str
        if isinstance(open_time, str):
            # 支持多种时间格式
            try:
                # 尝试MT4格式: "2025.05.29 21:23:28"
                parsed_time = datetime.strptime(open_time, "%Y.%m.%d %H:%M:%S")
                print(f"   ✅ MT4格式解析成功: {parsed_time}")
            except ValueError:
                try:
                    # 尝试ISO格式
                    parsed_time = datetime.fromisoformat(open_time)
                    print(f"   ✅ ISO格式解析成功: {parsed_time}")
                except ValueError:
                    try:
                        # 尝试标准格式: "2025-05-29 21:23:28"
                        parsed_time = datetime.strptime(open_time, "%Y-%m-%d %H:%M:%S")
                        print(f"   ✅ 标准格式解析成功: {parsed_time}")
                    except ValueError:
                        # 如果都失败，使用当前时间
                        print(f"   ⚠️ 无法解析时间格式: {open_time}，使用当前时间")
                        parsed_time = datetime.now()
        
        # 计算持仓时间
        holding_time = (datetime.now() - parsed_time).total_seconds() / 3600
        print(f"   持仓时间: {holding_time:.2f} 小时")
        print("")

def test_original_error():
    """测试原始错误"""
    print("🚨 测试原始错误场景...")
    
    # 模拟原始的错误代码
    time_str = "2025.05.29 21:23:28"
    print(f"原始时间字符串: '{time_str}'")
    
    try:
        # 这是原来失败的代码
        open_time = datetime.fromisoformat(time_str)
        print("✅ 原始代码成功")
    except ValueError as e:
        print(f"❌ 原始代码失败: {e}")
    
    print("")
    
    # 测试修复后的代码
    print("🛠️ 修复后的代码:")
    try:
        # 尝试MT4格式: "2025.05.29 21:23:28"
        open_time = datetime.strptime(time_str, "%Y.%m.%d %H:%M:%S")
        print(f"✅ 修复后成功解析: {open_time}")
        
        # 计算持仓时间
        holding_time = (datetime.now() - open_time).total_seconds() / 3600
        print(f"持仓时间: {holding_time:.2f} 小时")
        
    except ValueError as e:
        print(f"❌ 修复后仍然失败: {e}")

def main():
    """主函数"""
    print("🚀 时间格式解析测试")
    print("=" * 60)
    
    # 测试原始错误
    test_original_error()
    
    print("=" * 60)
    
    # 测试时间解析
    test_time_parsing()
    
    print("=" * 60)
    print("📊 总结:")
    print("✅ MT4时间格式 '2025.05.29 21:23:28' 现在可以正确解析")
    print("✅ 支持多种时间格式的兼容性解析")
    print("✅ 无效格式时使用当前时间作为备选")
    print("🎯 修复完成！持仓风险评估的时间解析问题已解决")

if __name__ == "__main__":
    main()
    input("按任意键退出...")
