"""
顶级量化交易系统 - 高级技术分析引擎
结合最先进的技术分析理论和量化方法
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import talib
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

class SignalStrength(Enum):
    VERY_STRONG = 5
    STRONG = 4
    MODERATE = 3
    WEAK = 2
    VERY_WEAK = 1

@dataclass
class TechnicalSignal:
    """技术信号数据结构"""
    signal_type: str
    direction: str  # BUY/SELL/NEUTRAL
    strength: SignalStrength
    confidence: float
    entry_price: float
    stop_loss: float
    take_profit: float
    timeframe: str
    indicators_used: List[str]
    market_regime: str

class AdvancedTechnicalEngine:
    """
    顶级技术分析引擎
    整合最先进的技术分析方法
    """
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.ml_model = RandomForestRegressor(n_estimators=50, max_depth=10, random_state=42)
        self.model_trained = False
        
        # 技术分析参数
        self.ta_params = {
            # 趋势分析
            'trend_periods': [5, 10, 20, 50, 100, 200],
            'ema_periods': [8, 13, 21, 34, 55, 89],  # 斐波那契数列
            
            # 动量分析
            'rsi_periods': [14, 21],
            'macd_params': [(12, 26, 9), (5, 35, 5)],
            'stoch_params': [(14, 3, 3), (21, 5, 5)],
            
            # 波动率分析
            'bb_periods': [20, 50],
            'atr_periods': [14, 21],
            'keltner_periods': [20, 10],
            
            # 成交量分析
            'volume_periods': [10, 20, 50],
            'vwap_periods': [20, 50],
            
            # 市场微观结构
            'orderflow_periods': [10, 20],
            'liquidity_periods': [14, 28]
        }
    
    def analyze_comprehensive(self, data: pd.DataFrame, symbol: str) -> Dict:
        """
        综合技术分析
        
        Args:
            data: OHLCV数据
            symbol: 交易品种
            
        Returns:
            Dict: 综合分析结果
        """
        try:
            # 数据预处理
            df = self._prepare_data(data)
            
            # 1. 市场状态识别
            market_regime = self._identify_market_regime(df)
            
            # 2. 多维度技术分析
            trend_analysis = self._analyze_trend(df)
            momentum_analysis = self._analyze_momentum(df)
            volatility_analysis = self._analyze_volatility(df)
            volume_analysis = self._analyze_volume(df)
            pattern_analysis = self._analyze_patterns(df)
            
            # 3. 高级量化指标
            quantitative_signals = self._calculate_quantitative_signals(df)
            
            # 4. 机器学习预测
            ml_prediction = self._generate_ml_prediction(df)
            
            # 5. 综合信号生成
            composite_signal = self._generate_composite_signal(
                trend_analysis, momentum_analysis, volatility_analysis,
                volume_analysis, pattern_analysis, quantitative_signals,
                ml_prediction, market_regime
            )
            
            return {
                'symbol': symbol,
                'timestamp': df.index[-1],
                'market_regime': market_regime,
                'trend_analysis': trend_analysis,
                'momentum_analysis': momentum_analysis,
                'volatility_analysis': volatility_analysis,
                'volume_analysis': volume_analysis,
                'pattern_analysis': pattern_analysis,
                'quantitative_signals': quantitative_signals,
                'ml_prediction': ml_prediction,
                'composite_signal': composite_signal,
                'confidence_score': self._calculate_confidence_score(composite_signal)
            }
            
        except Exception as e:
            return {'error': f'技术分析失败: {str(e)}'}
    
    def _prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        df = data.copy()
        
        # 确保列名标准化
        df.columns = [col.lower() for col in df.columns]
        
        # 计算基础衍生指标
        df['hl2'] = (df['high'] + df['low']) / 2
        df['hlc3'] = (df['high'] + df['low'] + df['close']) / 3
        df['ohlc4'] = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        
        # 计算收益率
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        return df
    
    def _identify_market_regime(self, df: pd.DataFrame) -> str:
        """
        识别市场状态
        使用多重技术指标判断市场处于趋势、震荡还是突破状态
        """
        try:
            # 1. 趋势强度分析
            adx = talib.ADX(df['high'], df['low'], df['close'], timeperiod=14)
            current_adx = adx.iloc[-1]
            
            # 2. 波动率分析
            atr = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14)
            atr_ratio = atr.iloc[-1] / atr.rolling(50).mean().iloc[-1]
            
            # 3. 价格位置分析
            bb_upper, bb_middle, bb_lower = talib.BBANDS(df['close'], timeperiod=20)
            bb_position = (df['close'].iloc[-1] - bb_lower.iloc[-1]) / (bb_upper.iloc[-1] - bb_lower.iloc[-1])
            
            # 4. 成交量确认
            volume_sma = df['volume'].rolling(20).mean()
            volume_ratio = df['volume'].iloc[-1] / volume_sma.iloc[-1]
            
            # 综合判断
            if current_adx > 25 and atr_ratio < 1.2:
                return "TRENDING"
            elif current_adx < 20 and 0.2 < bb_position < 0.8:
                return "RANGING"
            elif atr_ratio > 1.5 and volume_ratio > 1.5:
                return "BREAKOUT"
            else:
                return "TRANSITIONAL"
                
        except Exception:
            return "UNKNOWN"
    
    def _analyze_trend(self, df: pd.DataFrame) -> Dict:
        """
        趋势分析 - 使用多重时间框架和高级趋势指标
        """
        try:
            trend_signals = []
            
            # 1. 多重移动平均线分析
            for period in self.ta_params['trend_periods']:
                if len(df) >= period:
                    sma = talib.SMA(df['close'], timeperiod=period)
                    ema = talib.EMA(df['close'], timeperiod=period)
                    
                    # 价格与均线关系
                    price_vs_sma = "ABOVE" if df['close'].iloc[-1] > sma.iloc[-1] else "BELOW"
                    price_vs_ema = "ABOVE" if df['close'].iloc[-1] > ema.iloc[-1] else "BELOW"
                    
                    # 均线斜率
                    sma_slope = (sma.iloc[-1] - sma.iloc[-5]) / 5 if len(sma) >= 5 else 0
                    
                    trend_signals.append({
                        'period': period,
                        'price_vs_sma': price_vs_sma,
                        'price_vs_ema': price_vs_ema,
                        'sma_slope': sma_slope,
                        'trend_strength': abs(sma_slope) * 10000  # 标准化
                    })
            
            # 2. MACD分析
            macd_signals = []
            for fast, slow, signal in self.ta_params['macd_params']:
                if len(df) >= slow:
                    macd, macd_signal, macd_hist = talib.MACD(df['close'], fastperiod=fast, slowperiod=slow, signalperiod=signal)
                    
                    macd_signals.append({
                        'params': f"{fast}-{slow}-{signal}",
                        'macd_value': macd.iloc[-1],
                        'signal_value': macd_signal.iloc[-1],
                        'histogram': macd_hist.iloc[-1],
                        'crossover': "BULLISH" if macd.iloc[-1] > macd_signal.iloc[-1] else "BEARISH"
                    })
            
            # 3. ADX趋势强度
            adx = talib.ADX(df['high'], df['low'], df['close'], timeperiod=14)
            plus_di = talib.PLUS_DI(df['high'], df['low'], df['close'], timeperiod=14)
            minus_di = talib.MINUS_DI(df['high'], df['low'], df['close'], timeperiod=14)
            
            # 4. 综合趋势评分
            bullish_signals = sum(1 for signal in trend_signals if signal['price_vs_sma'] == 'ABOVE' and signal['sma_slope'] > 0)
            bearish_signals = sum(1 for signal in trend_signals if signal['price_vs_sma'] == 'BELOW' and signal['sma_slope'] < 0)
            
            trend_score = (bullish_signals - bearish_signals) / len(trend_signals) if trend_signals else 0
            
            return {
                'trend_signals': trend_signals,
                'macd_signals': macd_signals,
                'adx': {
                    'adx_value': adx.iloc[-1],
                    'plus_di': plus_di.iloc[-1],
                    'minus_di': minus_di.iloc[-1],
                    'trend_strength': "STRONG" if adx.iloc[-1] > 25 else "WEAK"
                },
                'trend_score': trend_score,
                'overall_trend': "BULLISH" if trend_score > 0.3 else "BEARISH" if trend_score < -0.3 else "NEUTRAL"
            }
            
        except Exception as e:
            return {'error': f'趋势分析失败: {str(e)}'}
    
    def _analyze_momentum(self, df: pd.DataFrame) -> Dict:
        """
        动量分析 - 多重动量指标综合分析
        """
        try:
            momentum_signals = []
            
            # 1. RSI分析
            for period in self.ta_params['rsi_periods']:
                if len(df) >= period:
                    rsi = talib.RSI(df['close'], timeperiod=period)
                    current_rsi = rsi.iloc[-1]
                    
                    # RSI状态判断
                    if current_rsi > 70:
                        rsi_state = "OVERBOUGHT"
                    elif current_rsi < 30:
                        rsi_state = "OVERSOLD"
                    elif current_rsi > 50:
                        rsi_state = "BULLISH"
                    else:
                        rsi_state = "BEARISH"
                    
                    momentum_signals.append({
                        'indicator': f'RSI_{period}',
                        'value': current_rsi,
                        'state': rsi_state
                    })
            
            # 2. 随机指标分析
            for k_period, k_slowing, d_period in self.ta_params['stoch_params']:
                if len(df) >= k_period:
                    slowk, slowd = talib.STOCH(df['high'], df['low'], df['close'], 
                                             fastk_period=k_period, slowk_period=k_slowing, slowd_period=d_period)
                    
                    momentum_signals.append({
                        'indicator': f'STOCH_{k_period}_{k_slowing}_{d_period}',
                        'k_value': slowk.iloc[-1],
                        'd_value': slowd.iloc[-1],
                        'state': "OVERBOUGHT" if slowk.iloc[-1] > 80 else "OVERSOLD" if slowk.iloc[-1] < 20 else "NEUTRAL"
                    })
            
            # 3. 威廉指标
            willr = talib.WILLR(df['high'], df['low'], df['close'], timeperiod=14)
            
            # 4. CCI指标
            cci = talib.CCI(df['high'], df['low'], df['close'], timeperiod=14)
            
            # 5. 动量综合评分
            momentum_score = self._calculate_momentum_score(momentum_signals)
            
            return {
                'momentum_signals': momentum_signals,
                'williams_r': willr.iloc[-1],
                'cci': cci.iloc[-1],
                'momentum_score': momentum_score,
                'momentum_direction': "BULLISH" if momentum_score > 0.2 else "BEARISH" if momentum_score < -0.2 else "NEUTRAL"
            }
            
        except Exception as e:
            return {'error': f'动量分析失败: {str(e)}'}
    
    def _calculate_momentum_score(self, signals: List[Dict]) -> float:
        """计算动量综合评分"""
        if not signals:
            return 0.0
        
        score = 0
        total_weight = 0
        
        for signal in signals:
            weight = 1.0
            if 'RSI' in signal['indicator']:
                if signal['state'] == 'BULLISH':
                    score += 0.5 * weight
                elif signal['state'] == 'BEARISH':
                    score -= 0.5 * weight
                elif signal['state'] == 'OVERBOUGHT':
                    score -= 1.0 * weight
                elif signal['state'] == 'OVERSOLD':
                    score += 1.0 * weight
            
            total_weight += weight
        
        return score / total_weight if total_weight > 0 else 0.0
