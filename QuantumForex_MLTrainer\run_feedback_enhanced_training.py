#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反馈增强训练脚本
整合Pro端实时数据进行深度训练
"""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def main():
    """主训练流程"""
    logger = setup_logging()

    logger.info("🚀 QuantumForex 反馈增强训练开始")
    logger.info("=" * 60)

    try:
        # 步骤1: 收集Pro端反馈数据
        logger.info("步骤1: 收集Pro端反馈数据...")
        from data_collector.pro_data_collector import pro_data_collector

        pro_feedback = pro_data_collector.collect_all_data(days=7)

        # 检查是否有真实的反馈数据
        has_trade_data = len(pro_feedback.get('trade_records', [])) > 0
        has_param_data = len(pro_feedback.get('parameter_optimizations', [])) > 0
        has_llm_data = len(pro_feedback.get('llm_analyses', [])) > 0

        use_feedback = has_trade_data or has_param_data or has_llm_data

        if not use_feedback:
            logger.warning("⚠️ 没有可用的Pro端反馈数据，使用基础训练模式")
        else:
            logger.info(f"✅ 收集到Pro端反馈数据:")
            logger.info(f"  - 交易记录: {len(pro_feedback.get('trade_records', []))}条")
            logger.info(f"  - 参数优化: {len(pro_feedback.get('parameter_optimizations', []))}条")
            logger.info(f"  - LLM分析: {len(pro_feedback.get('llm_analyses', []))}条")

        # 步骤2: 生成基础特征
        logger.info("步骤2: 生成基础特征...")
        from feature_engineering.technical_features import technical_engine
        from feature_engineering.market_features import market_engine

        # 创建测试数据
        import pandas as pd
        import numpy as np

        dates = pd.date_range('2024-01-01', periods=2000, freq='1min')
        np.random.seed(42)

        price_changes = np.random.randn(2000) * 0.0001
        prices = 1.1000 + np.cumsum(price_changes)

        test_data = pd.DataFrame({
            'close': prices,
            'open': prices + np.random.randn(2000) * 0.00005,
            'high': prices + np.abs(np.random.randn(2000)) * 0.0001,
            'low': prices - np.abs(np.random.randn(2000)) * 0.0001,
            'volume': np.random.randint(1000, 10000, 2000)
        }, index=dates)

        # 确保OHLC逻辑正确
        test_data['high'] = np.maximum(test_data[['open', 'close']].max(axis=1), test_data['high'])
        test_data['low'] = np.minimum(test_data[['open', 'close']].min(axis=1), test_data['low'])

        logger.info(f"测试数据创建完成: {len(test_data)}条记录")

        # 技术指标特征
        df_with_tech = technical_engine.generate_all_features(test_data.copy())
        tech_features = technical_engine.get_feature_names()
        logger.info(f"技术指标特征: {len(tech_features)}个")

        # 市场特征
        df_with_market = market_engine.generate_all_features(df_with_tech)
        market_features = market_engine.get_feature_names()
        logger.info(f"市场特征: {len(market_features)}个")

        # 步骤3: 生成反馈增强特征
        feedback_features = []
        if use_feedback:
            logger.info("步骤3: 生成反馈增强特征...")
            from feature_engineering.feedback_features import feedback_engine

            df_with_feedback = feedback_engine.generate_feedback_features(
                df_with_market,
                pro_feedback.get('trade_records', []),
                pro_feedback.get('parameter_optimizations', []),
                pro_feedback.get('llm_analyses', [])
            )

            feedback_features = feedback_engine.get_feature_names()
            logger.info(f"反馈特征: {len(feedback_features)}个")

            final_df = df_with_feedback
        else:
            logger.info("步骤3: 跳过反馈特征生成")
            final_df = df_with_market

        # 合并所有特征
        all_features = tech_features + market_features + feedback_features
        logger.info(f"总特征数: {len(all_features)}个")

        # 步骤4: 增强模型训练
        logger.info("步骤4: 增强模型训练...")
        from model_training.price_prediction_trainer import price_trainer

        if len(final_df) > 100:
            # 训练价格方向预测模型
            results = price_trainer.train_price_direction_model(
                final_df, all_features, horizon=5, threshold=0.0001
            )

            if results:
                logger.info("✅ 价格方向预测模型训练完成")

                # 获取最佳模型
                best_model_name, best_model = price_trainer.get_best_model(results, 'f1_score')
                if best_model_name:
                    logger.info(f"🏆 最佳模型: {best_model_name}")

                    # 如果使用了反馈数据，记录特征重要性
                    if use_feedback and feedback_features:
                        logger.info("📊 反馈特征重要性分析:")
                        feature_importance = price_trainer.feature_importance.get(best_model_name, {})

                        # 分析反馈特征的重要性
                        feedback_importance = {k: v for k, v in feature_importance.items() if k in feedback_features}

                        if feedback_importance:
                            sorted_feedback = sorted(feedback_importance.items(), key=lambda x: x[1], reverse=True)
                            for feature, importance in sorted_feedback[:5]:  # 显示前5个
                                logger.info(f"  {feature}: {importance:.4f}")
                        else:
                            logger.info("  反馈特征重要性数据不可用")
            else:
                logger.warning("❌ 模型训练失败")
        else:
            logger.warning("⚠️ 数据量不足，跳过模型训练")

        # 步骤5: 模型上传
        logger.info("步骤5: 模型上传...")
        from utils.cloud_transfer import CloudTransferManager

        try:
            cloud_transfer = CloudTransferManager()

            if cloud_transfer.test_connection():
                logger.info("✅ 云服务器连接正常")

                upload_result = cloud_transfer.upload_all_models()

                if upload_result['success']:
                    logger.info(f"✅ 模型上传成功: {upload_result['uploaded_count']}个模型")
                else:
                    logger.warning(f"⚠️ 模型上传部分失败: {upload_result}")
            else:
                logger.warning("⚠️ 云服务器连接失败，模型将保存在本地")

        except Exception as e:
            logger.error(f"❌ 模型上传失败: {e}")

        # 步骤6: 生成增强训练报告
        logger.info("步骤6: 生成增强训练报告...")

        training_summary = {
            'timestamp': datetime.now().isoformat(),
            'feedback_enhanced': use_feedback,
            'data_records': len(test_data),
            'base_features': len(tech_features + market_features),
            'feedback_features': len(feedback_features),
            'total_features': len(all_features),
            'models_trained': len(price_trainer.models),
            'training_history': price_trainer.training_history,
            'pro_feedback_summary': {
                'trade_records': len(pro_feedback.get('trade_records', [])),
                'parameter_optimizations': len(pro_feedback.get('parameter_optimizations', [])),
                'llm_analyses': len(pro_feedback.get('llm_analyses', []))
            } if use_feedback else None
        }

        # 保存训练报告
        report_file = f"enhanced_training_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path = Path("logs") / report_file
        report_path.parent.mkdir(exist_ok=True)

        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(training_summary, f, indent=2, ensure_ascii=False)

        logger.info(f"✅ 增强训练报告已保存: {report_path}")

        # 最终总结
        logger.info("🎉 反馈增强训练完成！")
        logger.info("=" * 60)
        logger.info("训练摘要:")
        logger.info(f"  - 反馈增强: {'是' if use_feedback else '否'}")
        logger.info(f"  - 数据记录: {len(test_data)}")
        logger.info(f"  - 基础特征: {len(tech_features + market_features)}")
        logger.info(f"  - 反馈特征: {len(feedback_features)}")
        logger.info(f"  - 总特征数: {len(all_features)}")
        logger.info(f"  - 训练模型: {len(price_trainer.models)}")
        logger.info(f"  - 报告文件: {report_path}")

        if use_feedback:
            logger.info("💡 反馈数据价值:")
            logger.info(f"  - 真实交易标签: {len(pro_feedback.get('trade_records', []))}个")
            logger.info(f"  - 参数优化历史: {len(pro_feedback.get('parameter_optimizations', []))}个")
            logger.info(f"  - LLM分析洞察: {len(pro_feedback.get('llm_analyses', []))}个")

        return True

    except Exception as e:
        logger.error(f"❌ 反馈增强训练失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    input("\n按任意键退出...")
    sys.exit(0 if success else 1)
