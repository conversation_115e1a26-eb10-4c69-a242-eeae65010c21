#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时数据清理脚本
清理历史交易记录、token记录等临时数据，保留系统配置和重要文件
"""

import os
import json
import shutil
from datetime import datetime
from pathlib import Path

def backup_important_data():
    """备份重要数据到backup文件夹"""
    backup_dir = Path("backup")
    backup_dir.mkdir(exist_ok=True)
    
    # 创建时间戳文件夹
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_timestamp_dir = backup_dir / f"backup_{timestamp}"
    backup_timestamp_dir.mkdir(exist_ok=True)
    
    print(f"📁 创建备份目录: {backup_timestamp_dir}")
    
    # 备份重要配置文件
    important_files = [
        "config.py",
        "requirements.txt",
        ".env.local",
        "诊断系统开发文档.md",
        "交易系统运行流程说明.md"
    ]
    
    for file_path in important_files:
        if os.path.exists(file_path):
            shutil.copy2(file_path, backup_timestamp_dir)
            print(f"✅ 备份文件: {file_path}")
    
    # 备份重要数据文件（如果存在且不为空）
    data_files_to_backup = [
        "app/data/forex_analysis_history.json",
        "app/data/token_statistics.json",
        "app/data/trade_results.json"
    ]
    
    for file_path in data_files_to_backup:
        if os.path.exists(file_path):
            try:
                # 检查文件是否为空或只包含空数据
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content and content != '[]' and content != '{}':
                        shutil.copy2(file_path, backup_timestamp_dir)
                        print(f"✅ 备份数据文件: {file_path}")
                    else:
                        print(f"⚠️ 跳过空数据文件: {file_path}")
            except Exception as e:
                print(f"❌ 备份文件失败 {file_path}: {e}")
    
    return backup_timestamp_dir

def clean_temporary_data():
    """清理临时数据"""
    print("🧹 开始清理临时数据...")
    
    # 要清理的文件列表
    files_to_clean = [
        # 分析历史记录
        "app/data/forex_analysis_history.json",
        "app/data/forex_latest_analysis.json",
        "app/data/forex_statistics.json",
        "app/data/forex_statistics_report.txt",
        
        # Token统计记录
        "app/data/token_statistics.json",
        "app/data/token_report.txt",
        
        # 交易结果记录
        "app/data/trade_results.json",
        
        # 监控数据
        "app/data/monitoring/alerts.json",
        "app/data/monitoring/analysis_metrics.json",
        "app/data/monitoring/counters.json",
        "app/data/monitoring/system_metrics.json",
        "app/data/monitoring/trading_metrics.json"
    ]
    
    # 要清理的目录列表
    dirs_to_clean = [
        "app/data/charts",
        "app/data/errors",
        "logs",
        "logs/error_logs"
    ]
    
    cleaned_files = 0
    cleaned_dirs = 0
    
    # 清理文件
    for file_path in files_to_clean:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"🗑️ 删除文件: {file_path}")
                cleaned_files += 1
            except Exception as e:
                print(f"❌ 删除文件失败 {file_path}: {e}")
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    # 清理目录
    for dir_path in dirs_to_clean:
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                print(f"🗑️ 删除目录: {dir_path}")
                cleaned_dirs += 1
            except Exception as e:
                print(f"❌ 删除目录失败 {dir_path}: {e}")
        else:
            print(f"⚠️ 目录不存在: {dir_path}")
    
    return cleaned_files, cleaned_dirs

def recreate_necessary_structure():
    """重新创建必要的目录结构"""
    print("📁 重新创建必要的目录结构...")
    
    # 需要重新创建的目录
    dirs_to_create = [
        "app/data",
        "app/data/monitoring",
        "app/data/charts",
        "app/data/errors",
        "logs",
        "logs/error_logs"
    ]
    
    for dir_path in dirs_to_create:
        os.makedirs(dir_path, exist_ok=True)
        print(f"📁 创建目录: {dir_path}")
    
    # 创建空的初始数据文件
    initial_files = {
        "app/data/forex_analysis_history.json": [],
        "app/data/token_statistics.json": {
            "total_tokens": 0,
            "total_cost": 0.0,
            "analysis_count": 0,
            "records": []
        },
        "app/data/trade_results.json": [],
        "app/data/monitoring/counters.json": {
            "analysis_count": 0,
            "trade_count": 0,
            "error_count": 0,
            "last_reset": datetime.now().isoformat()
        }
    }
    
    for file_path, initial_content in initial_files.items():
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(initial_content, f, ensure_ascii=False, indent=2)
            print(f"📄 创建初始文件: {file_path}")
        except Exception as e:
            print(f"❌ 创建文件失败 {file_path}: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🧹 外汇交易系统 - 临时数据清理工具")
    print("=" * 60)
    print()
    
    # 确认操作
    print("⚠️ 此操作将清理以下数据：")
    print("   - 历史分析记录")
    print("   - Token使用统计")
    print("   - 交易结果记录")
    print("   - 监控数据和图表")
    print("   - 系统日志文件")
    print()
    print("✅ 重要配置文件和代码将被保留")
    print("✅ 重要数据将自动备份到backup文件夹")
    print()
    
    confirm = input("确认执行清理操作？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 操作已取消")
        return
    
    print()
    print("🚀 开始执行清理操作...")
    print()
    
    try:
        # 1. 备份重要数据
        backup_dir = backup_important_data()
        print()
        
        # 2. 清理临时数据
        cleaned_files, cleaned_dirs = clean_temporary_data()
        print()
        
        # 3. 重新创建必要结构
        recreate_necessary_structure()
        print()
        
        # 4. 显示清理结果
        print("=" * 60)
        print("✅ 清理操作完成！")
        print("=" * 60)
        print(f"📊 清理统计:")
        print(f"   - 删除文件: {cleaned_files} 个")
        print(f"   - 删除目录: {cleaned_dirs} 个")
        print(f"   - 备份位置: {backup_dir}")
        print()
        print("🎯 系统已重置为初始状态，可以开始新的交易分析！")
        print()
        print("📋 下一步操作建议:")
        print("   1. 检查配置文件是否正确")
        print("   2. 启动交易系统: python run.py")
        print("   3. 监控系统运行状态")
        
    except Exception as e:
        print(f"❌ 清理操作失败: {e}")
        print("请检查文件权限或手动清理")

if __name__ == "__main__":
    main()
