"""
LLM分析结果格式化器
将技术分析结果转换为LLM友好的格式
"""

from typing import Dict, List, Optional
from datetime import datetime
from app.utils.logger_manager import log_analysis, LogLevel

class LLMAnalysisFormatter:
    """LLM分析结果格式化器"""
    
    def __init__(self):
        self.signal_strength_descriptions = {
            'very_strong': '非常强烈',
            'strong': '强烈',
            'moderate': '中等',
            'weak': '微弱',
            'very_weak': '非常微弱'
        }
        
        self.trend_descriptions = {
            'uptrend': '上升趋势',
            'downtrend': '下降趋势',
            'sideways': '横盘整理',
            'unknown': '趋势不明'
        }
        
        self.volatility_descriptions = {
            'extremely_high': '极高波动',
            'high': '高波动',
            'normal': '正常波动',
            'low': '低波动',
            'extremely_low': '极低波动'
        }
    
    def format_for_llm(self, signals: List, market_context, indicators: Dict, timeframe: str) -> Dict:
        """
        为LLM格式化分析结果
        
        Args:
            signals: 技术信号列表
            market_context: 市场环境上下文
            indicators: 技术指标数据
            timeframe: 时间框架
            
        Returns:
            Dict: LLM友好的分析结果
        """
        try:
            # 构建LLM友好的分析结果
            llm_analysis = {
                'analysis_timestamp': datetime.now().isoformat(),
                'timeframe': timeframe,
                'market_overview': self._create_market_overview(market_context),
                'technical_summary': self._create_technical_summary(signals, indicators),
                'key_signals': self._create_key_signals(signals),
                'risk_assessment': self._create_risk_assessment(indicators, market_context),
                'trading_recommendation': self._create_trading_recommendation(signals, market_context),
                'detailed_analysis': self._create_detailed_analysis(indicators),
                'llm_interpretation_guide': self._create_interpretation_guide(signals, market_context)
            }
            
            return llm_analysis
            
        except Exception as e:
            log_analysis(f"LLM分析格式化失败: {e}", LogLevel.ERROR)
            return self._create_error_response(str(e))
    
    def _create_market_overview(self, market_context) -> Dict:
        """创建市场概览"""
        try:
            return {
                'trend_direction': {
                    'value': market_context.trend_direction,
                    'description': self.trend_descriptions.get(market_context.trend_direction, '未知'),
                    'strength': market_context.trend_strength
                },
                'volatility_environment': {
                    'level': market_context.volatility_level,
                    'description': self.volatility_descriptions.get(market_context.volatility_level, '未知')
                },
                'market_phase': {
                    'current_phase': market_context.market_phase,
                    'description': self._get_market_phase_description(market_context.market_phase)
                },
                'support_resistance_quality': {
                    'quality': market_context.support_resistance_quality,
                    'description': self._get_sr_quality_description(market_context.support_resistance_quality)
                }
            }
        except Exception:
            return {'error': '市场概览生成失败'}
    
    def _create_technical_summary(self, signals: List, indicators: Dict) -> Dict:
        """创建技术分析摘要"""
        try:
            bullish_signals = [s for s in signals if hasattr(s, 'signal') and s.signal == 'bullish']
            bearish_signals = [s for s in signals if hasattr(s, 'signal') and s.signal == 'bearish']
            
            # 计算总体信号强度
            total_bullish_strength = sum(s.strength for s in bullish_signals)
            total_bearish_strength = sum(s.strength for s in bearish_signals)
            
            # 确定主导信号
            if total_bullish_strength > total_bearish_strength:
                dominant_signal = 'bullish'
                signal_strength = total_bullish_strength / (total_bullish_strength + total_bearish_strength)
            elif total_bearish_strength > total_bullish_strength:
                dominant_signal = 'bearish'
                signal_strength = total_bearish_strength / (total_bullish_strength + total_bearish_strength)
            else:
                dominant_signal = 'neutral'
                signal_strength = 0.5
            
            return {
                'overall_signal': {
                    'direction': dominant_signal,
                    'strength': signal_strength,
                    'description': self._get_signal_description(dominant_signal, signal_strength)
                },
                'signal_count': {
                    'bullish': len(bullish_signals),
                    'bearish': len(bearish_signals),
                    'total': len(signals)
                },
                'key_indicators_status': self._get_key_indicators_status(indicators)
            }
            
        except Exception:
            return {'error': '技术摘要生成失败'}
    
    def _create_key_signals(self, signals: List) -> List[Dict]:
        """创建关键信号列表"""
        try:
            key_signals = []
            
            # 按强度排序，取前5个最强信号
            sorted_signals = sorted(signals, key=lambda x: x.strength if hasattr(x, 'strength') else 0, reverse=True)
            
            for signal in sorted_signals[:5]:
                if hasattr(signal, 'indicator'):
                    key_signals.append({
                        'indicator': signal.indicator,
                        'signal_type': signal.signal,
                        'strength': signal.strength,
                        'confidence': signal.confidence,
                        'description': signal.description,
                        'interpretation': self._interpret_signal_for_llm(signal)
                    })
            
            return key_signals
            
        except Exception:
            return [{'error': '关键信号生成失败'}]
    
    def _create_risk_assessment(self, indicators: Dict, market_context) -> Dict:
        """创建风险评估"""
        try:
            risk_factors = []
            risk_score = 0.5  # 基础风险评分
            
            # 波动率风险
            if market_context.volatility_level in ['extremely_high', 'high']:
                risk_factors.append('高波动率环境，价格波动剧烈')
                risk_score += 0.2
            elif market_context.volatility_level in ['extremely_low', 'low']:
                risk_factors.append('低波动率环境，可能出现突然的波动率扩张')
                risk_score += 0.1
            
            # 趋势风险
            if market_context.trend_direction == 'sideways':
                risk_factors.append('横盘整理阶段，方向性不明确')
                risk_score += 0.15
            
            # 支撑阻力风险
            if market_context.support_resistance_quality == 'weak':
                risk_factors.append('支撑阻力位质量较弱，突破概率较高')
                risk_score += 0.1
            
            # 技术指标分歧风险
            if self._check_indicator_divergence(indicators):
                risk_factors.append('技术指标出现分歧，信号可靠性降低')
                risk_score += 0.15
            
            # 限制风险评分在0-1之间
            risk_score = min(1.0, max(0.0, risk_score))
            
            return {
                'overall_risk_score': risk_score,
                'risk_level': self._get_risk_level(risk_score),
                'risk_factors': risk_factors,
                'risk_mitigation_suggestions': self._get_risk_mitigation_suggestions(risk_factors)
            }
            
        except Exception:
            return {'error': '风险评估生成失败'}
    
    def _create_trading_recommendation(self, signals: List, market_context) -> Dict:
        """创建交易建议"""
        try:
            # 计算信号强度
            bullish_strength = sum(s.strength for s in signals if hasattr(s, 'signal') and s.signal == 'bullish')
            bearish_strength = sum(s.strength for s in signals if hasattr(s, 'signal') and s.signal == 'bearish')
            
            # 生成建议
            if bullish_strength > bearish_strength * 1.5:
                recommendation = 'BUY'
                confidence = min(0.9, bullish_strength / (bullish_strength + bearish_strength))
                reasoning = f'多头信号强度{bullish_strength:.2f}明显超过空头信号{bearish_strength:.2f}'
            elif bearish_strength > bullish_strength * 1.5:
                recommendation = 'SELL'
                confidence = min(0.9, bearish_strength / (bullish_strength + bearish_strength))
                reasoning = f'空头信号强度{bearish_strength:.2f}明显超过多头信号{bullish_strength:.2f}'
            else:
                recommendation = 'HOLD'
                confidence = 0.5
                reasoning = f'多空信号相对均衡，多头{bullish_strength:.2f} vs 空头{bearish_strength:.2f}'
            
            return {
                'action': recommendation,
                'confidence': confidence,
                'reasoning': reasoning,
                'entry_conditions': self._get_entry_conditions(recommendation, signals),
                'risk_management': self._get_risk_management_advice(recommendation, market_context)
            }
            
        except Exception:
            return {'error': '交易建议生成失败'}
    
    def _create_detailed_analysis(self, indicators: Dict) -> Dict:
        """创建详细分析"""
        try:
            detailed = {}
            
            # 趋势分析详情
            if 'trend' in indicators:
                detailed['trend_analysis'] = self._format_trend_analysis(indicators['trend'])
            
            # 动量分析详情
            if 'momentum' in indicators:
                detailed['momentum_analysis'] = self._format_momentum_analysis(indicators['momentum'])
            
            # 波动率分析详情
            if 'volatility' in indicators:
                detailed['volatility_analysis'] = self._format_volatility_analysis(indicators['volatility'])
            
            # 支撑阻力分析详情
            if 'support_resistance' in indicators:
                detailed['support_resistance_analysis'] = self._format_sr_analysis(indicators['support_resistance'])
            
            return detailed
            
        except Exception:
            return {'error': '详细分析生成失败'}
    
    def _create_interpretation_guide(self, signals: List, market_context) -> Dict:
        """创建LLM解释指南"""
        try:
            return {
                'analysis_focus': self._get_analysis_focus(signals, market_context),
                'key_considerations': self._get_key_considerations(market_context),
                'signal_reliability': self._assess_signal_reliability(signals),
                'market_context_impact': self._assess_market_context_impact(market_context),
                'recommended_analysis_approach': self._get_recommended_approach(signals, market_context)
            }
            
        except Exception:
            return {'error': '解释指南生成失败'}
    
    def _get_market_phase_description(self, phase: str) -> str:
        """获取市场阶段描述"""
        descriptions = {
            'accumulation': '吸筹阶段 - 价格在底部区域震荡，成交量逐渐放大',
            'markup': '拉升阶段 - 价格快速上涨，趋势明确',
            'distribution': '派发阶段 - 价格在高位震荡，成交量放大',
            'markdown': '下跌阶段 - 价格快速下跌，趋势明确',
            'unknown': '阶段不明 - 市场处于过渡期或信号不明确'
        }
        return descriptions.get(phase, '未知阶段')
    
    def _get_sr_quality_description(self, quality: str) -> str:
        """获取支撑阻力质量描述"""
        descriptions = {
            'strong': '支撑阻力位质量强 - 多次测试有效，可靠性高',
            'moderate': '支撑阻力位质量中等 - 有一定参考价值',
            'weak': '支撑阻力位质量弱 - 容易被突破，参考价值有限',
            'unknown': '支撑阻力位质量未知'
        }
        return descriptions.get(quality, '质量未知')
    
    def _get_signal_description(self, direction: str, strength: float) -> str:
        """获取信号描述"""
        strength_level = 'strong' if strength > 0.7 else 'moderate' if strength > 0.5 else 'weak'
        
        if direction == 'bullish':
            return f'看涨信号，强度{strength_level}({strength:.2f})'
        elif direction == 'bearish':
            return f'看跌信号，强度{strength_level}({strength:.2f})'
        else:
            return f'中性信号，强度{strength:.2f}'
    
    def _get_key_indicators_status(self, indicators: Dict) -> Dict:
        """获取关键指标状态"""
        status = {}
        
        try:
            # MA状态
            if 'trend' in indicators and 'ma_system' in indicators['trend']:
                ma_data = indicators['trend']['ma_system']
                status['moving_averages'] = {
                    'alignment': ma_data.get('alignment', 'unknown'),
                    'price_position': ma_data.get('price_vs_ma20', 'unknown')
                }
            
            # RSI状态
            if 'momentum' in indicators and 'rsi' in indicators['momentum']:
                rsi_data = indicators['momentum']['rsi']
                status['rsi'] = {
                    'value': rsi_data.get('value', 50),
                    'level': rsi_data.get('level', 'neutral')
                }
            
            # MACD状态
            if 'trend' in indicators and 'macd' in indicators['trend']:
                macd_data = indicators['trend']['macd']
                status['macd'] = {
                    'signal': macd_data.get('signal', 'neutral'),
                    'divergence': macd_data.get('divergence', 'none')
                }
            
        except Exception:
            status['error'] = '指标状态获取失败'
        
        return status
    
    def _interpret_signal_for_llm(self, signal) -> str:
        """为LLM解释信号"""
        try:
            if not hasattr(signal, 'indicator') or not hasattr(signal, 'signal'):
                return '信号解释失败'
            
            interpretations = {
                'MA_System': {
                    'bullish': '移动平均线多头排列，表明上升趋势强劲，适合做多',
                    'bearish': '移动平均线空头排列，表明下降趋势强劲，适合做空'
                },
                'MACD': {
                    'bullish': 'MACD金叉信号，动量转向积极，支持价格上涨',
                    'bearish': 'MACD死叉信号，动量转向消极，支持价格下跌'
                },
                'RSI': {
                    'bullish': 'RSI超卖反弹信号，价格可能出现技术性反弹',
                    'bearish': 'RSI超买回调信号，价格可能出现技术性回调'
                }
            }
            
            return interpretations.get(signal.indicator, {}).get(signal.signal, '标准技术信号')
            
        except Exception:
            return '信号解释失败'
    
    def _check_indicator_divergence(self, indicators: Dict) -> bool:
        """检查指标分歧"""
        try:
            signals = []
            
            # 收集各指标信号
            if 'trend' in indicators:
                if 'ma_system' in indicators['trend']:
                    alignment = indicators['trend']['ma_system'].get('alignment', 'unknown')
                    if alignment == 'bullish_alignment':
                        signals.append('bullish')
                    elif alignment == 'bearish_alignment':
                        signals.append('bearish')
                
                if 'macd' in indicators['trend']:
                    macd_signal = indicators['trend']['macd'].get('signal', 'neutral')
                    if macd_signal != 'neutral':
                        signals.append(macd_signal)
            
            if 'momentum' in indicators and 'rsi' in indicators['momentum']:
                rsi_signal = indicators['momentum']['rsi'].get('signal', 'neutral')
                if rsi_signal == 'buy_signal':
                    signals.append('bullish')
                elif rsi_signal == 'sell_signal':
                    signals.append('bearish')
            
            # 检查是否有分歧
            bullish_count = signals.count('bullish')
            bearish_count = signals.count('bearish')
            
            return bullish_count > 0 and bearish_count > 0
            
        except Exception:
            return False
    
    def _get_risk_level(self, risk_score: float) -> str:
        """获取风险等级"""
        if risk_score >= 0.8:
            return 'very_high'
        elif risk_score >= 0.6:
            return 'high'
        elif risk_score >= 0.4:
            return 'moderate'
        elif risk_score >= 0.2:
            return 'low'
        else:
            return 'very_low'
    
    def _create_error_response(self, error_msg: str) -> Dict:
        """创建错误响应"""
        return {
            'error': True,
            'message': error_msg,
            'timestamp': datetime.now().isoformat(),
            'fallback_analysis': '技术分析处理失败，请检查数据质量'
        }
