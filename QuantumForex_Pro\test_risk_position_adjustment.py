#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantumForex Pro - 风险管理仓位自动调整测试
测试系统是否能根据风险管理建议自动调整仓位大小
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.execution_engine.trade_executor import TradeExecutor

# 手动创建风险枚举类（避免导入问题）
from enum import Enum

class RiskAction(Enum):
    """风险行动枚举"""
    ALLOW_TRADING = "allow_trading"
    REDUCE_POSITION = "reduce_position"
    STOP_NEW_TRADES = "stop_new_trades"
    CLOSE_ALL_POSITIONS = "close_all_positions"
    EMERGENCY_STOP = "emergency_stop"

class RiskLevel(Enum):
    """风险等级枚举"""
    VERY_LOW = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    VERY_HIGH = 5

def test_risk_position_adjustment():
    """测试风险管理仓位自动调整"""
    print("🚀 QuantumForex Pro 风险管理仓位调整测试")
    print("="*60)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    try:
        # 1. 创建交易执行器
        print("🔧 初始化交易执行器...")
        executor = TradeExecutor()

        # 2. 测试不同风险管理建议下的仓位调整
        test_cases = [
            {
                'name': '正常交易 - 无风险调整',
                'risk_action': RiskAction.ALLOW_TRADING,
                'risk_level': RiskLevel.LOW,
                'expected_adjustment': 1.0
            },
            {
                'name': '风险管理 - 减少仓位',
                'risk_action': RiskAction.REDUCE_POSITION,
                'risk_level': RiskLevel.MEDIUM,
                'expected_adjustment': 0.5
            },
            {
                'name': '高风险 - 停止交易',
                'risk_action': RiskAction.STOP_NEW_TRADES,
                'risk_level': RiskLevel.HIGH,
                'expected_adjustment': 0.0
            }
        ]

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试案例 {i}: {test_case['name']}")
            print("-" * 50)

            # 创建模拟风险评估
            risk_metrics = type('RiskMetrics', (), {
                'recommended_action': test_case['risk_action'],
                'risk_level': test_case['risk_level'],
                'risk_score': 0.5
            })()

            risk_assessment = {
                'risk_metrics': risk_metrics,
                'overall_risk': test_case['risk_level'].name
            }

            # 设置风险评估
            executor.set_risk_assessment(risk_assessment)

            # 创建测试交易决策
            test_decision = {
                'symbol': 'EURUSD',
                'action': 'enter_long',
                'volume': 0.02,  # 原始仓位0.02手
                'confidence': 0.7,
                'reasoning': f'测试{test_case["name"]}'
            }

            print(f"📊 原始仓位: {test_decision['volume']}手")
            print(f"🛡️ 风险建议: {test_case['risk_action'].value}")
            print(f"📈 风险等级: {test_case['risk_level'].name}")

            # 测试仓位计算
            calculated_volume = executor._calculate_position_size(test_decision)
            expected_volume = test_decision['volume'] * test_case['expected_adjustment']

            print(f"🎯 计算仓位: {calculated_volume}手")
            print(f"🎯 期望仓位: {expected_volume}手")

            # 验证结果
            if abs(calculated_volume - expected_volume) < 0.001:
                print("✅ 仓位调整正确")
            else:
                print("❌ 仓位调整错误")
                print(f"   期望: {expected_volume}手")
                print(f"   实际: {calculated_volume}手")

        # 3. 测试置信度调整
        print(f"\n📋 测试案例 4: 置信度调整")
        print("-" * 50)

        # 正常风险，但低置信度
        risk_metrics = type('RiskMetrics', (), {
            'recommended_action': RiskAction.ALLOW_TRADING,
            'risk_level': RiskLevel.LOW,
            'risk_score': 0.3
        })()

        risk_assessment = {
            'risk_metrics': risk_metrics,
            'overall_risk': 'LOW'
        }

        executor.set_risk_assessment(risk_assessment)

        low_confidence_decision = {
            'symbol': 'EURUSD',
            'action': 'enter_long',
            'volume': 0.02,
            'confidence': 0.4,  # 低置信度40%
            'reasoning': '测试低置信度调整'
        }

        print(f"📊 原始仓位: {low_confidence_decision['volume']}手")
        print(f"💪 信号置信度: {low_confidence_decision['confidence']:.1%}")

        calculated_volume = executor._calculate_position_size(low_confidence_decision)

        # 期望调整：置信度40% < 60%，所以调整系数 = 0.4/0.6 = 0.667
        expected_adjustment = low_confidence_decision['confidence'] / 0.6
        expected_volume = low_confidence_decision['volume'] * expected_adjustment

        print(f"🎯 计算仓位: {calculated_volume}手")
        print(f"🎯 期望仓位: {expected_volume:.3f}手")
        print(f"📊 调整系数: {expected_adjustment:.3f}")

        if abs(calculated_volume - expected_volume) < 0.001:
            print("✅ 置信度调整正确")
        else:
            print("❌ 置信度调整错误")

        # 4. 测试组合调整（风险+置信度）
        print(f"\n📋 测试案例 5: 组合调整（风险+置信度）")
        print("-" * 50)

        # 中等风险 + 低置信度
        risk_metrics = type('RiskMetrics', (), {
            'recommended_action': RiskAction.REDUCE_POSITION,
            'risk_level': RiskLevel.MEDIUM,
            'risk_score': 0.6
        })()

        risk_assessment = {
            'risk_metrics': risk_metrics,
            'overall_risk': 'MEDIUM'
        }

        executor.set_risk_assessment(risk_assessment)

        combo_decision = {
            'symbol': 'EURUSD',
            'action': 'enter_long',
            'volume': 0.02,
            'confidence': 0.5,  # 低置信度50%
            'reasoning': '测试组合调整'
        }

        print(f"📊 原始仓位: {combo_decision['volume']}手")
        print(f"🛡️ 风险建议: reduce_position (50%调整)")
        print(f"💪 信号置信度: {combo_decision['confidence']:.1%}")

        calculated_volume = executor._calculate_position_size(combo_decision)

        # 期望调整：风险调整50% * 置信度调整(0.5/0.6) = 0.5 * 0.833 = 0.417
        risk_adj = 0.5  # reduce_position
        confidence_adj = combo_decision['confidence'] / 0.6
        total_adj = risk_adj * confidence_adj
        expected_volume = combo_decision['volume'] * total_adj

        print(f"🎯 计算仓位: {calculated_volume}手")
        print(f"🎯 期望仓位: {expected_volume:.3f}手")
        print(f"📊 风险调整: {risk_adj:.3f}")
        print(f"📊 置信度调整: {confidence_adj:.3f}")
        print(f"📊 总调整系数: {total_adj:.3f}")

        if abs(calculated_volume - expected_volume) < 0.001:
            print("✅ 组合调整正确")
        else:
            print("❌ 组合调整错误")

        print("\n" + "="*60)
        print("📊 测试总结")
        print("="*60)
        print("✅ 风险管理仓位自动调整功能测试完成")
        print("💡 系统现在能够根据风险管理建议自动调整仓位大小")
        print("🎯 支持的调整类型:")
        print("   • 风险管理建议调整 (reduce_position: 50%)")
        print("   • 信号置信度调整 (低于60%时按比例调整)")
        print("   • 市场波动率调整 (高波动率时减少30%)")
        print("   • 组合调整 (多种因素同时作用)")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_risk_position_adjustment()
