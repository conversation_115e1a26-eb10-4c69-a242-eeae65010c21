"""
测试MT4客户端服务器（仅服务器部分）
"""
import os
import sys
import time
import traceback
from datetime import datetime

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'

# 确保输出不会被缓存
sys.stdout.reconfigure(line_buffering=True)
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 脚本开始执行')

# 导入客户端服务器模块
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 导入客户端服务器模块')
try:
    from app.utils import client_server
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 客户端服务器模块导入成功')
except Exception as e:
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 导入客户端服务器模块失败: {e}')
    traceback.print_exc()
    sys.exit(1)

def main():
    """主函数"""
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 开始测试MT4客户端服务器（仅服务器部分）')

    try:
        # 初始化用户数据库
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 初始化用户数据库')
        try:
            client_server.init_db()
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 用户数据库初始化成功')
        except Exception as db_error:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 用户数据库初始化失败: {db_error}')
            traceback.print_exc()
            return

        # 启动服务器
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 启动MT4客户端服务器')
        try:
            client_server.start_server_thread()
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端服务器线程已启动')
        except Exception as server_error:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 启动MT4客户端服务器失败: {server_error}')
            traceback.print_exc()
            return

        # 等待服务器启动
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 等待服务器启动...')
        time.sleep(2)

        # 检查服务器是否运行
        if client_server.server_thread and client_server.server_thread.is_alive():
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端服务器已成功启动')
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器地址: {client_server.CLIENT_SERVER_ADDRESS}')

            # 等待用户输入以保持服务器运行
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器正在运行，按Enter键停止...')
            input()

            # 停止服务器
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 停止MT4客户端服务器')
            try:
                client_server.stop_server()
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端服务器已停止')
            except Exception as stop_error:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 停止MT4客户端服务器失败: {stop_error}')
                traceback.print_exc()

            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试完成')
        else:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端服务器启动失败')
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] server_thread: {client_server.server_thread}')
            if client_server.server_thread:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] server_thread.is_alive(): {client_server.server_thread.is_alive()}')
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试过程中出错: {e}')
        traceback.print_exc()

if __name__ == '__main__':
    main()
