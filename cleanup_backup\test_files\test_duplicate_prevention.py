"""
测试重复订单防止功能
"""
import os
import sys
import time
from datetime import datetime

from app.services.forex_trading_service import execute_trade
from app.utils.mt4_client import mt4_client
# 不再需要定时任务
# from app.utils.forex_scheduled_tasks import start_hourly_forex_analysis, stop_all_tasks

def test_duplicate_prevention():
    """测试重复订单防止功能"""
    try:
        print('=' * 50)
        print(f'开始测试重复订单防止功能，时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        print('=' * 50)

        # 步骤1: 确保MT4连接
        print('\n步骤1: 确保MT4连接')
        if not mt4_client.is_connected:
            print('MT4客户端未连接，尝试连接')
            connected = mt4_client.connect()
            if not connected:
                print('无法连接到MT4客户端，测试失败')
                return
        print('MT4连接正常')

        # 步骤2: 获取当前挂单
        print('\n步骤2: 获取当前挂单')
        pending_orders_response = mt4_client.get_pending_orders()
        pending_orders = pending_orders_response.get('orders', [])
        print(f'当前挂单数量: {len(pending_orders)}')
        for order in pending_orders:
            print(f'  - 挂单: 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 货币对: {order.get("symbol")}, 手数: {order.get("lots")}, 价格: {order.get("open_price")}, 止损: {order.get("sl")}, 止盈: {order.get("tp")}')

        # 步骤3: 创建预定义的交易指令
        print('\n步骤3: 创建预定义的交易指令')

        # 获取市场信息
        market_info = mt4_client.get_market_info('EURUSD')
        if not market_info or market_info.get('status') != 'success':
            print('获取市场信息失败，测试终止')
            return

        current_price = float(market_info['data']['ask'])
        print(f'当前EURUSD价格: {current_price}')

        # 创建一个卖出限价单
        trade_instructions = {
            'action': 'SELL',
            'orderType': 'LIMIT',
            'entryPrice': round(current_price + 0.0050, 5),  # 限价设置在当前价格上方50点
            'stopLoss': round(current_price + 0.0080, 5),  # 止损设置在限价上方30点
            'takeProfit': round(current_price - 0.0050, 5),  # 止盈设置在当前价格下方50点
            'riskLevel': 'LOW',  # 低风险
            'reasoning': '测试卖出限价单'
        }
        print(f'创建预定义的交易指令: {trade_instructions}')

        # 步骤4: 执行交易
        print('\n步骤4: 执行交易')
        if trade_instructions and trade_instructions.get('action') != 'NONE':
            print('执行第一次交易...')
            trade_result = execute_trade(trade_instructions, check_duplicate=False)
            print(f'第一次交易执行结果: {trade_result}')

            # 等待一秒
            time.sleep(1)

            print('执行第二次交易（应该检测到重复）...')
            trade_result2 = execute_trade(trade_instructions, check_duplicate=True)
            print(f'第二次交易执行结果: {trade_result2}')

            # 检查是否检测到重复
            if trade_result2.get('duplicate'):
                print('成功检测到重复订单！')
            else:
                print('未检测到重复订单，测试失败')
        else:
            print('交易指令为观望，无法测试重复订单防止功能')
            return

        # 步骤5: 再次获取挂单
        print('\n步骤5: 再次获取挂单')
        time.sleep(1)  # 等待一秒，确保订单已经被处理

        new_pending_orders_response = mt4_client.get_pending_orders()
        new_pending_orders = new_pending_orders_response.get('orders', [])
        print(f'新的挂单数量: {len(new_pending_orders)}')
        for order in new_pending_orders:
            print(f'  - 挂单: 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 货币对: {order.get("symbol")}, 手数: {order.get("lots")}, 价格: {order.get("open_price")}, 止损: {order.get("sl")}, 止盈: {order.get("tp")}')

        # 步骤6: 测试手动执行相同交易
        print('\n步骤6: 测试手动执行相同交易')
        print('再次执行相同的交易指令（应该检测到重复）...')
        trade_result3 = execute_trade(trade_instructions, check_duplicate=True)
        print(f'再次执行相同交易指令的结果: {trade_result3}')

        # 检查是否检测到重复
        if trade_result3.get('duplicate'):
            print('成功检测到重复订单！')
        else:
            print('未检测到重复订单，测试失败')

        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_duplicate_prevention()
