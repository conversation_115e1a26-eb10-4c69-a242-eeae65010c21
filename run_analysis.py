"""
手动执行外汇分析
"""
import os
import sys
import time
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.forex_trading_service import analyze_forex
try:
    from app.services.forex_trading_service import execute_trade_with_risk_management as execute_trade
    print("使用风险管理交易执行")
except ImportError:
    from app.services.forex_trading_service import execute_trade
    print("使用标准交易执行")


def main():
    """主函数"""
    print("=" * 50)
    print("外汇交易系统分析工具")
    print("=" * 50)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 询问是否自动交易
    auto_trade = input("是否自动执行交易？(y/n): ").lower() == 'y'

    # 执行分析
    print("\n开始执行外汇分析...")
    start_time = time.time()
    analysis_result = analyze_forex(force=True)
    end_time = time.time()

    if analysis_result:
        print("\n分析完成！")
        print(f"分析耗时: {end_time - start_time:.2f}秒")

        # 显示分析结果
        trade_instructions = analysis_result.get('tradeInstructions', {})
        action = trade_instructions.get('action', 'UNKNOWN')
        reasoning = trade_instructions.get('reasoning', '')

        print("\n分析结果:")
        print("-" * 50)
        print(f"交易行为: {action}")
        print(f"分析理由: {reasoning}")

        # 显示订单管理指令
        order_management = trade_instructions.get('orderManagement', [])
        if order_management:
            print("\n订单管理指令:")
            for i, order_action in enumerate(order_management):
                print(f"  {i+1}. 操作: {order_action.get('action')}, 订单ID: {order_action.get('orderId')}")
                print(f"     原因: {order_action.get('reason')}")

        # 如果启用自动交易，执行交易
        if auto_trade:
            print("\n开始执行交易...")
            trade_start_time = time.time()
            trade_result = execute_trade(trade_instructions, check_duplicate=True)
            trade_end_time = time.time()

            print("\n交易执行完成！")
            print(f"交易耗时: {trade_end_time - trade_start_time:.2f}秒")
            print(f"交易结果: {trade_result}")
        else:
            print("\n自动交易已禁用，跳过交易执行")
    else:
        print("\n分析失败！")

    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)


if __name__ == "__main__":
    main()
