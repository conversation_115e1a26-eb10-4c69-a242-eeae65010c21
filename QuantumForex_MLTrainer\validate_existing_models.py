#!/usr/bin/env python3
"""
验证现有模型的真实效果
对已训练的模型进行回测验证
"""

import sys
import os
import logging
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model_evaluation.real_backtest_engine import RealBacktestEngine

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def find_all_models() -> dict:
    """查找所有已训练的模型"""
    models_dir = Path("data/models")
    if not models_dir.exists():
        return {}
    
    model_categories = {
        'price_prediction': [],
        'trend_classification': [],
        'volatility_prediction': [],
        'risk_assessment': []
    }
    
    # 查找各类模型
    for category in model_categories.keys():
        model_files = list(models_dir.glob(f"*{category}*.pkl"))
        # 排除scaler文件
        model_files = [f for f in model_files if 'scaler' not in f.name]
        
        # 按修改时间排序，取最新的3个
        model_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        model_categories[category] = model_files[:3]
    
    return model_categories

def evaluate_model_effectiveness(metrics) -> tuple:
    """评估模型效果"""
    score = 0.0
    
    # 盈利能力 (30%)
    if metrics.total_return > 0:
        profit_score = min(0.3, metrics.total_return * 3)
        score += profit_score
    
    # 胜率 (25%)
    win_rate_score = metrics.win_rate * 0.25
    score += win_rate_score
    
    # 预测准确性 (25%)
    accuracy_score = metrics.prediction_accuracy * 0.25
    score += accuracy_score
    
    # 风险控制 (20%)
    if metrics.max_drawdown < 0.1:
        risk_score = 0.2 - (metrics.max_drawdown * 2)
        score += max(0, risk_score)
    
    # 评级
    if score >= 0.7:
        grade = "🌟 优秀"
    elif score >= 0.5:
        grade = "✅ 良好"
    elif score >= 0.3:
        grade = "⚠️ 一般"
    else:
        grade = "❌ 较差"
    
    return score, grade

def validate_all_models():
    """验证所有模型"""
    logger = setup_logging()
    
    print("🚀 QuantumForex 现有模型回测验证")
    print("="*60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 查找所有模型
        print("\n📦 查找已训练的模型...")
        model_categories = find_all_models()
        
        total_models = sum(len(models) for models in model_categories.values())
        if total_models == 0:
            print("❌ 没有找到任何模型文件")
            return False
        
        print(f"✅ 找到{total_models}个模型文件:")
        for category, models in model_categories.items():
            if models:
                print(f"   📊 {category}: {len(models)}个模型")
                for model in models:
                    file_size = model.stat().st_size
                    mod_time = datetime.fromtimestamp(model.stat().st_mtime)
                    print(f"      📄 {model.name} ({file_size:,}字节, {mod_time.strftime('%m-%d %H:%M')})")
        
        # 2. 创建回测引擎
        print("\n🔧 初始化回测引擎...")
        backtest_engine = RealBacktestEngine()
        
        # 3. 验证每个模型
        all_results = []
        model_count = 0
        
        for category, models in model_categories.items():
            if not models:
                continue
            
            print(f"\n{'='*60}")
            print(f"🧪 验证 {category} 模型")
            print(f"{'='*60}")
            
            category_results = []
            
            for i, model_file in enumerate(models, 1):
                model_count += 1
                print(f"\n🔍 回测模型 {i}/{len(models)}: {model_file.name}")
                print("-" * 50)
                
                try:
                    # 运行回测
                    metrics = backtest_engine.run_comprehensive_backtest(
                        model_path=str(model_file),
                        data_days=7  # 使用7天数据
                    )
                    
                    # 评估效果
                    effectiveness_score, grade = evaluate_model_effectiveness(metrics)
                    
                    # 显示关键指标
                    print(f"📊 关键指标:")
                    print(f"   总交易: {metrics.total_trades}")
                    print(f"   胜率: {metrics.win_rate:.1%}")
                    print(f"   收益率: {metrics.total_return:.1%}")
                    print(f"   最大回撤: {metrics.max_drawdown:.1%}")
                    print(f"   预测准确率: {metrics.prediction_accuracy:.1%}")
                    print(f"   夏普比率: {metrics.sharpe_ratio:.2f}")
                    print(f"   效果评分: {effectiveness_score:.1%}")
                    print(f"   效果等级: {grade}")
                    
                    # 保存结果
                    result = {
                        'category': category,
                        'model_name': model_file.name,
                        'model_path': str(model_file),
                        'effectiveness_score': effectiveness_score,
                        'grade': grade,
                        'metrics': metrics,
                        'validated': effectiveness_score >= 0.3
                    }
                    
                    category_results.append(result)
                    all_results.append(result)
                    
                    if effectiveness_score >= 0.5:
                        print(f"✅ 模型通过验证")
                    elif effectiveness_score >= 0.3:
                        print(f"⚠️ 模型勉强合格")
                    else:
                        print(f"❌ 模型未通过验证")
                    
                except Exception as e:
                    print(f"❌ 回测失败: {e}")
                    logger.error(f"模型 {model_file.name} 回测失败: {e}")
                    
                    result = {
                        'category': category,
                        'model_name': model_file.name,
                        'model_path': str(model_file),
                        'effectiveness_score': 0.0,
                        'grade': "❌ 错误",
                        'metrics': None,
                        'validated': False,
                        'error': str(e)
                    }
                    category_results.append(result)
                    all_results.append(result)
            
            # 显示类别总结
            if category_results:
                valid_models = [r for r in category_results if r['validated']]
                print(f"\n📋 {category} 总结:")
                print(f"   测试模型: {len(category_results)}")
                print(f"   通过验证: {len(valid_models)}")
                
                if valid_models:
                    best_model = max(valid_models, key=lambda x: x['effectiveness_score'])
                    print(f"   最佳模型: {best_model['model_name']}")
                    print(f"   最佳评分: {best_model['effectiveness_score']:.1%}")
        
        # 4. 生成总体报告
        print(f"\n{'='*60}")
        print("📊 总体验证报告")
        print(f"{'='*60}")
        
        if not all_results:
            print("❌ 没有完成任何验证")
            return False
        
        # 统计结果
        total_tested = len(all_results)
        excellent_models = [r for r in all_results if r['effectiveness_score'] >= 0.7]
        good_models = [r for r in all_results if 0.5 <= r['effectiveness_score'] < 0.7]
        fair_models = [r for r in all_results if 0.3 <= r['effectiveness_score'] < 0.5]
        poor_models = [r for r in all_results if r['effectiveness_score'] < 0.3]
        
        print(f"📈 验证统计:")
        print(f"   总测试模型: {total_tested}")
        print(f"   优秀模型: {len(excellent_models)} ({len(excellent_models)/total_tested:.1%})")
        print(f"   良好模型: {len(good_models)} ({len(good_models)/total_tested:.1%})")
        print(f"   一般模型: {len(fair_models)} ({len(fair_models)/total_tested:.1%})")
        print(f"   较差模型: {len(poor_models)} ({len(poor_models)/total_tested:.1%})")
        
        # 找出最佳模型
        valid_results = [r for r in all_results if r.get('metrics') is not None]
        if valid_results:
            best_overall = max(valid_results, key=lambda x: x['effectiveness_score'])
            
            print(f"\n🏆 最佳模型:")
            print(f"   模型: {best_overall['model_name']}")
            print(f"   类型: {best_overall['category']}")
            print(f"   评分: {best_overall['effectiveness_score']:.1%}")
            print(f"   等级: {best_overall['grade']}")
            
            if best_overall['metrics']:
                m = best_overall['metrics']
                print(f"   收益率: {m.total_return:.1%}")
                print(f"   胜率: {m.win_rate:.1%}")
                print(f"   预测准确率: {m.prediction_accuracy:.1%}")
        
        # 5. 部署建议
        print(f"\n💡 部署建议:")
        deployable_models = len(excellent_models) + len(good_models)
        
        if deployable_models >= 3:
            print("🌟 强烈建议立即部署到Pro系统")
            print("✅ 有足够数量的高质量模型")
            print("📝 建议优先部署优秀和良好等级的模型")
        elif deployable_models >= 1:
            print("⚠️ 可以谨慎部署部分模型")
            print("📝 建议先部署最佳模型，继续优化其他模型")
        else:
            print("❌ 不建议部署，所有模型效果不佳")
            print("📝 建议重新训练或改进特征工程")
        
        # 6. 保存详细报告
        save_validation_report(all_results)
        
        return deployable_models > 0
        
    except Exception as e:
        logger.error(f"验证系统失败: {e}")
        print(f"❌ 验证系统失败: {e}")
        return False

def save_validation_report(results: list):
    """保存验证报告"""
    try:
        import json
        
        # 创建报告目录
        reports_dir = Path("logs/validation_reports")
        reports_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成报告文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = reports_dir / f"model_validation_report_{timestamp}.json"
        
        # 准备报告数据
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'total_models': len(results),
            'excellent_models': len([r for r in results if r['effectiveness_score'] >= 0.7]),
            'good_models': len([r for r in results if 0.5 <= r['effectiveness_score'] < 0.7]),
            'fair_models': len([r for r in results if 0.3 <= r['effectiveness_score'] < 0.5]),
            'poor_models': len([r for r in results if r['effectiveness_score'] < 0.3]),
            'results': []
        }
        
        for result in results:
            result_data = {
                'category': result['category'],
                'model_name': result['model_name'],
                'effectiveness_score': result['effectiveness_score'],
                'grade': result['grade'],
                'validated': result['validated']
            }
            
            if result.get('metrics'):
                metrics = result['metrics']
                result_data['metrics'] = {
                    'total_trades': metrics.total_trades,
                    'win_rate': metrics.win_rate,
                    'total_return': metrics.total_return,
                    'max_drawdown': metrics.max_drawdown,
                    'prediction_accuracy': metrics.prediction_accuracy,
                    'sharpe_ratio': metrics.sharpe_ratio
                }
            
            if result.get('error'):
                result_data['error'] = result['error']
            
            report_data['results'].append(result_data)
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"📄 验证报告已保存: {report_file}")
        
    except Exception as e:
        print(f"⚠️ 保存报告失败: {e}")

if __name__ == "__main__":
    print("🎯 QuantumForex 模型真实效果验证系统")
    print("🔍 回测验证 → 效果评估 → 部署建议")
    print("="*60)
    
    success = validate_all_models()
    
    if success:
        print("\n🎉 模型验证完成！")
        print("✅ 发现了可部署的有效模型")
        print("💡 现在您知道哪些模型真的有效了！")
    else:
        print("\n⚠️ 模型验证完成，但效果不理想")
        print("💡 建议重新训练或改进模型")
