#!/usr/bin/env python3
"""
AgenticSeek 简化版CLI
只包含基本的聊天功能，使用云端API
"""

import os
import sys
import json
import configparser
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_path = Path("config.ini")

    if not config_path.exists():
        print("❌ 配置文件 config.ini 不存在")
        return None

    config.read(config_path, encoding='utf-8')
    return config

def get_api_client(config):
    """根据配置获取API客户端"""
    provider = config.get('MAIN', 'provider_name', fallback='siliconflow')
    model = config.get('MAIN', 'provider_model', fallback='deepseek-ai/DeepSeek-V3')

    if provider == 'siliconflow':
        try:
            import openai
            api_key = os.getenv('SILICONFLOW_API_KEY')
            if not api_key or api_key == 'sk-your-siliconflow-api-key-here':
                print("❌ 请在 .env 文件中设置有效的 SILICONFLOW_API_KEY")
                print("💡 获取API密钥: https://cloud.siliconflow.cn/")
                return None

            client = openai.OpenAI(
                api_key=api_key,
                base_url="https://api.siliconflow.cn/v1"
            )
            return client, model
        except ImportError:
            print("❌ 请安装 openai 包: pip install openai")
            return None

    elif provider == 'deepseek':
        try:
            import openai
            api_key = os.getenv('DEEPSEEK_API_KEY')
            if not api_key or api_key == 'sk-your-deepseek-api-key-here':
                print("❌ 请在 .env 文件中设置有效的 DEEPSEEK_API_KEY")
                return None

            client = openai.OpenAI(
                api_key=api_key,
                base_url="https://api.deepseek.com/v1"
            )
            return client, 'deepseek-chat'
        except ImportError:
            print("❌ 请安装 openai 包: pip install openai")
            return None

    elif provider == 'openai':
        try:
            import openai
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                print("❌ 请在 .env 文件中设置 OPENAI_API_KEY")
                return None

            client = openai.OpenAI(api_key=api_key)
            return client, 'gpt-4'
        except ImportError:
            print("❌ 请安装 openai 包: pip install openai")
            return None

    else:
        print(f"❌ 不支持的提供商: {provider}")
        return None

def chat_with_ai(client, model, message, conversation_history=None):
    """与AI进行对话"""
    if conversation_history is None:
        conversation_history = []

    # 添加系统提示
    if not conversation_history:
        conversation_history.append({
            "role": "system",
            "content": """你是AgenticSeek，一个基于硅基流动平台的智能AI助手。你可以：

🔧 **核心功能：**
1. 回答各种问题和提供专业建议
2. 帮助编程、调试和代码优化
3. 分析和总结复杂信息
4. 协助完成各种任务和项目规划
5. 提供技术文档和教程指导

💡 **特色能力：**
- 支持多种编程语言（Python、JavaScript、Java、C++等）
- 擅长数据分析和机器学习
- 能够进行系统架构设计
- 提供最佳实践建议

请用中文回复，保持友好、专业和高效的态度。如果遇到复杂问题，我会提供详细的分步解决方案。"""
        })

    # 添加用户消息
    conversation_history.append({
        "role": "user",
        "content": message
    })

    try:
        response = client.chat.completions.create(
            model=model,
            messages=conversation_history,
            max_tokens=2000,
            temperature=0.7
        )

        ai_response = response.choices[0].message.content

        # 添加AI回复到历史
        conversation_history.append({
            "role": "assistant",
            "content": ai_response
        })

        return ai_response, conversation_history

    except Exception as e:
        return f"❌ API调用失败: {str(e)}", conversation_history

def main():
    """主函数"""
    print("🚀 AgenticSeek 简化版")
    print("=" * 50)

    # 加载配置
    config = load_config()
    if not config:
        return

    # 获取API客户端
    api_result = get_api_client(config)
    if not api_result:
        return

    client, model = api_result
    print(f"✅ 已连接到 {config.get('MAIN', 'provider_name')} API")
    print(f"📝 使用模型: {model}")
    print("\n💡 输入 'quit' 或 'exit' 退出")
    print("💡 输入 'clear' 清空对话历史")
    print("💡 输入 'help' 查看帮助")
    print("-" * 50)

    conversation_history = []

    while True:
        try:
            # 获取用户输入
            user_input = input("\n🤔 你: ").strip()

            if not user_input:
                continue

            # 处理特殊命令
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break

            elif user_input.lower() in ['clear', '清空']:
                conversation_history = []
                print("🧹 对话历史已清空")
                continue

            elif user_input.lower() in ['help', '帮助']:
                print("""
📖 AgenticSeek 简化版帮助:
- 直接输入问题或指令与AI对话
- 'quit' 或 'exit': 退出程序
- 'clear': 清空对话历史
- 'help': 显示此帮助信息

🔧 支持的功能:
- 智能问答对话
- 编程帮助和调试
- 代码审查和优化
- 文本分析和总结
- 任务规划和项目管理
- 技术文档编写
- 数据分析建议

🌟 基于硅基流动平台，提供高质量AI服务
💡 获取API密钥: https://cloud.siliconflow.cn/
                """)
                continue

            # 与AI对话
            print("🤖 AgenticSeek: ", end="", flush=True)
            response, conversation_history = chat_with_ai(
                client, model, user_input, conversation_history
            )

            print(response)

        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {str(e)}")

if __name__ == "__main__":
    main()
