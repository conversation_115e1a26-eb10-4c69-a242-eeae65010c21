2025-05-29 21:47:05,442 - __main__ - INFO - 🚀 启动 QuantumForex Pro 云端模型接收服务
2025-05-29 21:47:05,442 - __main__ - INFO - ============================================================
2025-05-29 21:47:05,443 - __main__ - INFO - 🌐 启动API服务器...
2025-05-29 21:47:05,443 - __main__ - INFO - 📍 监听地址: 0.0.0.0:8081
2025-05-29 21:47:05,443 - __main__ - INFO - 🔗 API端点:
2025-05-29 21:47:05,443 - __main__ - INFO -    - 健康检查: http://**************:8081/api/health
2025-05-29 21:47:05,444 - __main__ - INFO -    - 模型状态: http://**************:8081/api/models/status
2025-05-29 21:47:05,444 - __main__ - INFO -    - 模型上传: http://**************:8081/api/models/upload
2025-05-29 21:47:05,445 - __main__ - INFO -    - 模型列表: http://**************:8081/api/models/list
2025-05-29 21:47:05,446 - utils.cloud_model_receiver - INFO - 🚀 启动云端模型接收服务器: 0.0.0.0:8081
2025-05-29 21:47:05,470 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://***************:8081
2025-05-29 21:47:05,470 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 21:48:32,407 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:48:32] "GET /api/health HTTP/1.1" 200 -
2025-05-29 21:48:32,413 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 21:48:32,415 - utils.cloud_model_receiver - ERROR - 缺少必要字段: original_name
2025-05-29 21:48:32,415 - utils.cloud_model_receiver - ERROR - ❌ 模型上传处理失败: 元数据验证失败
2025-05-29 21:48:32,416 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:48:32] "[31m[1mPOST /api/models/upload HTTP/1.1[0m" 400 -
2025-05-29 21:48:32,439 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:48:32] "GET /api/models/list HTTP/1.1" 200 -
2025-05-29 21:50:12,070 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:50:12] "GET /api/health HTTP/1.1" 200 -
2025-05-29 21:50:12,078 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 21:50:12,079 - utils.cloud_model_receiver - ERROR - 缺少必要字段: original_name
2025-05-29 21:50:12,079 - utils.cloud_model_receiver - ERROR - ❌ 模型上传处理失败: 元数据验证失败
2025-05-29 21:50:12,080 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:50:12] "[31m[1mPOST /api/models/upload HTTP/1.1[0m" 400 -
2025-05-29 21:50:12,087 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:50:12] "GET /api/models/list HTTP/1.1" 200 -
2025-05-29 21:53:20,269 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:53:20] "GET /api/health HTTP/1.1" 200 -
2025-05-29 21:53:49,537 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:53:49] "GET /api/health HTTP/1.1" 200 -
2025-05-29 21:53:49,543 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 21:53:49,545 - utils.cloud_model_receiver - ERROR - 缺少必要字段: original_name
2025-05-29 21:53:49,545 - utils.cloud_model_receiver - ERROR - ❌ 模型上传处理失败: 元数据验证失败
2025-05-29 21:53:49,546 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:53:49] "[31m[1mPOST /api/models/upload HTTP/1.1[0m" 400 -
2025-05-29 21:53:49,554 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:53:49] "GET /api/models/list HTTP/1.1" 200 -
2025-05-29 21:54:38,385 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:54:38] "GET /api/health HTTP/1.1" 200 -
2025-05-29 21:54:38,434 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 21:54:38,436 - utils.cloud_model_receiver - ERROR - 不支持的模型类型: price_prediction
2025-05-29 21:54:38,436 - utils.cloud_model_receiver - ERROR - ❌ 模型上传处理失败: 元数据验证失败
2025-05-29 21:54:38,436 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:54:38] "[31m[1mPOST /api/models/upload HTTP/1.1[0m" 400 -
2025-05-29 21:54:38,466 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 21:54:38,468 - utils.cloud_model_receiver - ERROR - 不支持的模型类型: price_prediction
2025-05-29 21:54:38,468 - utils.cloud_model_receiver - ERROR - ❌ 模型上传处理失败: 元数据验证失败
2025-05-29 21:54:38,469 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:54:38] "[31m[1mPOST /api/models/upload HTTP/1.1[0m" 400 -
2025-05-29 21:55:34,145 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:55:34] "GET /api/health HTTP/1.1" 200 -
2025-05-29 21:55:34,178 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 21:55:34,180 - utils.cloud_model_receiver - ERROR - 不支持的模型类型: price_prediction
2025-05-29 21:55:34,180 - utils.cloud_model_receiver - ERROR - ❌ 模型上传处理失败: 元数据验证失败
2025-05-29 21:55:34,180 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:55:34] "[31m[1mPOST /api/models/upload HTTP/1.1[0m" 400 -
2025-05-29 21:55:34,193 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 21:55:34,195 - utils.cloud_model_receiver - ERROR - 不支持的模型类型: price_prediction
2025-05-29 21:55:34,195 - utils.cloud_model_receiver - ERROR - ❌ 模型上传处理失败: 元数据验证失败
2025-05-29 21:55:34,195 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:55:34] "[31m[1mPOST /api/models/upload HTTP/1.1[0m" 400 -
