"""
定时任务测试脚本
用于测试定时任务功能
"""
import os
import sys
import time
from datetime import datetime

from app.utils.forex_scheduled_tasks import start_hourly_forex_analysis, stop_all_tasks

def test_scheduled_tasks():
    """测试定时任务"""
    try:
        print('=' * 50)
        print(f'开始测试定时任务，时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        print('=' * 50)
        
        # 启动定时任务，立即执行一次，并启用自动交易
        print('\n启动定时任务，立即执行一次，并启用自动交易')
        start_hourly_forex_analysis(run_immediately=True, auto_trade=True)
        
        # 等待一段时间，让任务执行
        print('\n等待60秒，让任务执行')
        for i in range(60):
            time.sleep(1)
            if i % 10 == 0:
                print(f'已等待 {i} 秒')
        
        # 停止所有任务
        print('\n停止所有任务')
        stop_all_tasks()
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_scheduled_tasks()
