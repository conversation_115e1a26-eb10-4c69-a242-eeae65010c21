"""
MT4客户端工具
用于与MT4服务器通信，执行交易操作
支持多用户和授权验证（授权验证由MT4 Server-V2处理）
"""
import os
import json
import uuid
import time
import zmq
from dotenv import load_dotenv
from datetime import datetime
from app.utils.error_logger import log_error, log_operation, ErrorType, OperationType

# 加载环境变量
load_dotenv()

# 检查是否跳过MT4连接
def should_skip_mt4_connection():
    """
    智能判断是否应该跳过MT4连接

    Returns:
        bool: True表示应该跳过MT4连接，False表示正常连接
    """
    # 首先检查手动设置的环境变量
    manual_skip = os.environ.get('SKIP_MT4_CONNECTION', '').lower()
    if manual_skip == 'true':
        return True
    elif manual_skip == 'false':
        return False

    # 如果没有手动设置，则根据市场时间自动判断
    try:
        from app.utils.market_time_checker import is_market_open
        market_open = is_market_open()

        if not market_open:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🕒 市场关闭时间，自动启用MT4跳过模式')
            return True
        else:
            return False
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ⚠️ 无法检查市场时间，默认不跳过MT4连接: {e}')
        return False

# 动态获取跳过状态
def get_skip_mt4_status():
    """获取当前MT4跳过状态"""
    return should_skip_mt4_connection()

# MT4服务器地址
MT4_SERVER_ADDRESS = os.getenv('MT4_SERVER_ADDRESS', 'tcp://127.0.0.1:5555')
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器地址: {MT4_SERVER_ADDRESS}')
# 授权码（可选）
AUTH_CODE = os.getenv('AUTH_CODE', None)


class MT4Client:
    """MT4客户端类，支持多用户和授权验证"""

    def __init__(self, server_address=None, auth_code=None):
        """
        初始化MT4客户端

        Args:
            server_address (str, optional): MT4服务器地址
            auth_code (str, optional): 授权码
        """
        self.server_address = server_address or MT4_SERVER_ADDRESS
        self.auth_code = auth_code or AUTH_CODE  # 使用传入的授权码或环境变量中的授权码
        self.user_info = None
        self.socket = None
        self.context = None
        self.is_connected = False
        self.is_authorized = False  # 授权状态由MT4 Server-V2验证
        self.request_timeout = 60000  # 请求超时时间（毫秒），增加到60秒，提高稳定性
        self.max_retries = 5  # 最大重试次数，增加到5次
        self.retry_delay = 3  # 重试延迟（秒），增加到3秒
        self.last_connect_attempt = 0  # 上次连接尝试时间
        self.connect_cooldown = 3  # 连接冷却时间（秒），减少到3秒，提高响应速度
        self.connecting = False  # 连接锁，防止多线程同时连接
        self.connect_lock_time = 0  # 连接锁设置时间
        self.connect_lock_timeout = 60  # 连接锁超时时间（秒），增加到60秒
        self.connection_id = 0  # 连接ID，用于跟踪连接操作
        self.heartbeat_interval = 60  # 心跳检测间隔（秒）
        self.last_heartbeat = time.time()  # 上次心跳时间
        self.heartbeat_enabled = True  # 是否启用心跳检测

    def set_auth_code(self, auth_code):
        """
        设置授权码

        Args:
            auth_code (str): 授权码
        """
        self.auth_code = auth_code
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 设置授权码: {auth_code}')
        # 授权验证将在连接MT4服务器时进行

    def connect(self):
        """
        连接到MT4服务器，包含重试机制和连接状态管理
        如果设置了授权码，会在连接时发送授权信息

        Returns:
            bool: 连接是否成功
        """
        # 检查是否跳过MT4连接
        if should_skip_mt4_connection():
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ⚠️  MT4服务器跳过模式已启用，模拟连接成功')
            self.is_connected = True
            self.is_authorized = True
            self.user_info = {
                'id': 'test-user',
                'username': '测试用户',
                'expiry_date': '2025-12-31',
                'account_type': '测试账户'
            }
            return True

        # 如果设置了授权码，记录日志
        if self.auth_code:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 使用授权码连接MT4服务器: {self.auth_code}')
        # 检查是否已经连接
        if self.is_connected and self.socket:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已经连接到MT4服务器，无需重新连接')
            return True

        # 检查连接锁是否超时
        current_time = time.time()
        if self.connecting:
            # 如果连接锁已经超时，强制释放
            if current_time - self.connect_lock_time > self.connect_lock_timeout:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接锁已超时 ({self.connect_lock_timeout}秒)，强制释放')
                self.connecting = False
            else:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已有连接操作正在进行中，跳过本次连接请求')
                return False

        # 检查连接冷却时间
        if current_time - self.last_connect_attempt < self.connect_cooldown:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接请求过于频繁，请等待 {self.connect_cooldown - (current_time - self.last_connect_attempt):.1f} 秒后再试')
            return False

        # 设置连接锁和连接ID
        self.connecting = True
        self.connect_lock_time = current_time
        self.last_connect_attempt = current_time
        self.connection_id += 1
        connection_id = self.connection_id

        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接操作 #{connection_id} 开始')

        try:
            # 无论如何，先断开现有连接并清理资源
            self.disconnect()

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 正在连接到MT4服务器: {self.server_address}')

            # 创建新的ZMQ上下文和套接字
            if self.context is None:
                self.context = zmq.Context()

            # 确保创建新的套接字
            self.socket = self.context.socket(zmq.REQ)

            # 设置超时时间
            self.socket.setsockopt(zmq.RCVTIMEO, self.request_timeout)

            # 连接到服务器
            self.socket.connect(self.server_address)

            # 记录连接操作
            log_operation(
                operation_type=OperationType.CONNECTION,
                success=True,
                message=f'尝试连接到MT4服务器: {self.server_address}',
                parameters={'server_address': self.server_address, 'timeout': self.request_timeout}
            )

            # 使用重试机制发送ping请求测试连接
            for retry in range(self.max_retries):
                try:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 发送ping请求测试连接... (尝试 {retry+1}/{self.max_retries})')

                    # 直接使用socket发送ping请求，避免递归调用send_request
                    request_id = str(uuid.uuid4())
                    request = {'action': 'ping', 'requestId': request_id}

                    # 如果设置了授权码，添加到请求中
                    if self.auth_code:
                        request['auth_code'] = self.auth_code
                        request['auth_type'] = 'client'  # 标识为客户端授权

                    request_str = json.dumps(request)

                    self.socket.send_string(request_str)
                    response_str = self.socket.recv_string()

                    # 解析响应
                    response = json.loads(response_str)

                    if response and response.get('status') == 'success':
                        self.is_connected = True
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 成功连接到MT4服务器')

                        # 检查授权状态
                        if 'auth_status' in response:
                            self.is_authorized = response.get('auth_status') == 'valid'
                            if self.is_authorized:
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 授权验证成功，用户: {response.get("username", "未知")}')
                                # 保存用户信息
                                self.user_info = {
                                    'id': response.get('user_id', ''),
                                    'username': response.get('username', '未知用户'),
                                    'expiry_date': response.get('expiry_date', ''),
                                    'account_type': response.get('account_type', '标准账户')
                                }
                            else:
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 授权验证失败: {response.get("auth_message", "未知错误")}')
                                # 记录错误日志
                                log_error(
                                    error_type=ErrorType.AUTH_ERROR,
                                    message=f'授权验证失败: {response.get("auth_message", "未知错误")}',
                                    details={'auth_code': self.auth_code, 'server_address': self.server_address},
                                    operation=OperationType.AUTH
                                )

                        # 记录成功连接
                        log_operation(
                            operation_type=OperationType.CONNECTION,
                            success=True,
                            message='成功连接到MT4服务器',
                            result=response
                        )

                        self.connecting = False
                        return True
                    else:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器连接测试失败: {response}')

                        # 如果不是最后一次重试，等待后再试
                        if retry < self.max_retries - 1:
                            wait_time = self.retry_delay * (retry + 1)  # 指数退避
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                            time.sleep(wait_time)
                        else:
                            self.is_connected = False

                            # 记录连接失败
                            log_operation(
                                operation_type=OperationType.CONNECTION,
                                success=False,
                                message='MT4服务器连接测试失败',
                                result=response,
                                error={'status': 'error', 'message': response.get('message', '未知错误')}
                            )

                            # 记录错误
                            log_error(
                                error_type=ErrorType.CONNECTION_ERROR,
                                message='MT4服务器连接测试失败',
                                details=response,
                                operation=OperationType.CONNECTION
                            )
                except zmq.error.Again:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器响应超时 (尝试 {retry+1}/{self.max_retries})')

                    # 如果不是最后一次重试，等待后再试
                    if retry < self.max_retries - 1:
                        wait_time = self.retry_delay * (retry + 1)  # 指数退避
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                        time.sleep(wait_time)
                    else:
                        self.is_connected = False

                        # 记录连接失败
                        log_operation(
                            operation_type=OperationType.CONNECTION,
                            success=False,
                            message='MT4服务器响应超时',
                            error={'status': 'error', 'message': 'MT4服务器响应超时'}
                        )

                        # 记录错误
                        log_error(
                            error_type=ErrorType.CONNECTION_ERROR,
                            message='MT4服务器响应超时',
                            details={'server_address': self.server_address},
                            operation=OperationType.CONNECTION
                        )
                except Exception as ping_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 发送ping请求失败: {ping_error} (尝试 {retry+1}/{self.max_retries})')

                    # 如果不是最后一次重试，等待后再试
                    if retry < self.max_retries - 1:
                        wait_time = self.retry_delay * (retry + 1)  # 指数退避
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                        time.sleep(wait_time)
                    else:
                        self.is_connected = False

                        # 记录连接失败
                        log_operation(
                            operation_type=OperationType.CONNECTION,
                            success=False,
                            message=f'发送ping请求失败: {ping_error}',
                            error={'status': 'error', 'message': str(ping_error)}
                        )

                        # 记录错误
                        log_error(
                            error_type=ErrorType.CONNECTION_ERROR,
                            message=f'发送ping请求失败: {ping_error}',
                            details={'exception': str(ping_error)},
                            operation=OperationType.CONNECTION
                        )

            # 所有重试都失败
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接操作 #{connection_id} 失败: 所有重试都失败')
            self.is_connected = False
            self.connecting = False
            return False

        except Exception as error:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接操作 #{connection_id} 失败: {error}')
            self.is_connected = False

            # 记录连接异常
            log_operation(
                operation_type=OperationType.CONNECTION,
                success=False,
                message=f'连接MT4服务器异常: {error}',
                error={'status': 'error', 'message': str(error)}
            )

            # 记录错误
            log_error(
                error_type=ErrorType.CONNECTION_ERROR,
                message=f'连接MT4服务器异常: {error}',
                details={'exception': str(error), 'connection_id': connection_id},
                operation=OperationType.CONNECTION
            )

            # 释放连接锁
            self.connecting = False
            return False
        finally:
            # 确保在任何情况下都释放连接锁
            if not self.is_connected:
                self.connecting = False
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接操作 #{connection_id} 结束，连接锁已释放')

    def _fix_mt4_error_messages(self, response_str):
        """
        修复MT4服务器返回的错误消息格式

        Args:
            response_str (str): 原始响应字符串

        Returns:
            str: 修复后的响应字符串
        """
        import re
        now = datetime.now()

        # 处理 "message":"交易执行失败X" 格式，其中X是任何非法字符
        message_error_pattern = r'"message":"([^"]*)"'
        message_match = re.search(message_error_pattern, response_str)
        if message_match and message_match.group(1):
            error_message = message_match.group(1)

            # 检查是否包含"交易执行失败"
            if "交易执行失败" in error_message:
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到交易执行失败消息，尝试修复JSON')

                # 提取错误描述（如果有）
                error_desc = ""
                if ", 错误:" in error_message:
                    error_desc = error_message.split(", 错误:")[1].strip()

                # 替换为安全格式
                safe_message = "交易执行失败"
                if error_desc:
                    safe_message += f"，错误代码：{error_desc}"

                # 替换原始消息
                response_str = re.sub(
                    r'"message":"' + re.escape(error_message) + r'"',
                    f'"message":"{safe_message}"',
                    response_str
                )

                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修复后的消息: {safe_message}')

        # 处理删除挂单响应中的特殊格式
        if "DELETEPENDING" in response_str and "交易执行" in response_str:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到删除挂单响应，尝试修复JSON')

            # 如果响应中包含"成功"，则认为是成功响应
            if "成功" in response_str:
                response_str = '{"status":"success","message":"挂单删除成功"}'
            else:
                # 尝试提取错误信息
                error_match = re.search(r'错误:\s*([^"]+)', response_str)
                error_message = error_match.group(1) if error_match else '未知错误'
                response_str = f'{{"status":"error","message":"挂单删除失败: {error_message}"}}'

            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修复后的响应: {response_str}')

        # 处理特殊格式 "交易执行失败},"
        if '交易执行失败},' in response_str:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到特殊错误格式 "交易执行失败}},"，尝试修复JSON')
            response_str = response_str.replace('交易执行失败},', '交易执行失败"}')

        # 处理特殊格式 "交易执行失败, },"
        if '交易执行失败, },' in response_str:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到特殊错误格式 "交易执行失败, }},"，尝试修复JSON')
            response_str = response_str.replace('交易执行失败, },', '交易执行失败"}')

        # 处理特殊格式 "交易执行失败},"
        if '交易执行失败},"' in response_str:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到特殊错误格式 "交易执行失败}},"，尝试修复JSON')
            response_str = response_str.replace('交易执行失败},"', '交易执行失败"}')

        # 处理日志中看到的特殊格式
        if '"message":"交易执行失败},"' in response_str:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到特殊错误格式 "message":"交易执行失败}},"，尝试修复JSON')
            response_str = response_str.replace('"message":"交易执行失败},"', '"message":"交易执行失败"}')

        return response_str

    def disconnect(self):
        """断开与MT4服务器的连接"""
        if self.socket:
            try:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 正在断开与MT4服务器的连接')

                try:
                    # 尝试断开连接
                    self.socket.disconnect(self.server_address)
                except Exception as disconnect_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 断开连接时出错: {disconnect_error}')

                try:
                    # 尝试关闭套接字
                    self.socket.close()
                except Exception as close_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 关闭套接字时出错: {close_error}')

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已断开与MT4服务器的连接')
            except Exception as error:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 断开MT4服务器连接时出错: {error}')
            finally:
                # 无论如何，都确保清理资源
                self.socket = None
                self.is_connected = False
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已清理连接资源')

    def check_heartbeat(self):
        """
        检查心跳，如果超过心跳间隔，发送ping请求测试连接

        Returns:
            bool: 连接是否正常
        """
        if not self.heartbeat_enabled:
            return True

        current_time = time.time()
        if current_time - self.last_heartbeat > self.heartbeat_interval:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 心跳检测：距离上次心跳已经过去 {current_time - self.last_heartbeat:.1f} 秒，发送ping请求')

            # 发送ping请求测试连接
            try:
                ping_request = {'action': 'ping', 'requestId': str(uuid.uuid4())}
                if self.auth_code:
                    ping_request['auth_code'] = self.auth_code
                    ping_request['auth_type'] = 'client'

                # 直接使用socket发送ping请求
                if self.socket:
                    self.socket.send_string(json.dumps(ping_request))
                    response_str = self.socket.recv_string()
                    response = json.loads(response_str)

                    if response and response.get('status') == 'success':
                        self.last_heartbeat = current_time
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 心跳检测：连接正常')
                        return True
                    else:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 心跳检测：连接异常，尝试重新连接')
                        self.is_connected = False
                        return self.connect()
                else:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 心跳检测：socket为空，尝试重新连接')
                    self.is_connected = False
                    return self.connect()
            except Exception as e:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 心跳检测异常：{e}，尝试重新连接')
                self.is_connected = False
                return self.connect()

        return True

    def send_request(self, request):
        """
        发送请求到MT4服务器
        如果设置了授权码，会在请求中添加授权信息

        Args:
            request (dict): 请求对象

        Returns:
            dict: 响应对象
        """
        # 检查是否跳过MT4连接
        if should_skip_mt4_connection():
            now = datetime.now()
            action = request.get('action', 'unknown')
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ⚠️  MT4服务器跳过模式：模拟执行 {action}')

            # 根据不同的操作返回模拟响应
            if action == 'ping':
                return {'status': 'success', 'message': '模拟ping成功'}
            elif action == 'get_market_info' or action == 'MARKET_INFO':
                return {
                    'status': 'success',
                    'data': {
                        'symbol': request.get('symbol', 'EURUSD'),
                        'bid': 1.13500,
                        'ask': 1.13520,
                        'spread': 2.0
                    }
                }
            elif action in ['get_active_orders', 'get_pending_orders', 'get_all_orders']:
                return {'status': 'success', 'orders': [], 'message': '模拟获取订单成功'}
            elif action in ['place_order', 'modify_order', 'close_order', 'delete_pending_order']:
                return {'status': 'success', 'message': f'模拟{action}成功'}
            else:
                return {'status': 'success', 'message': f'模拟{action}成功'}
        # 确定操作类型
        action = request.get('action', '').upper()
        operation_type = OperationType.OTHER

        if action == 'PING':
            operation_type = OperationType.CONNECTION
        elif action in ['BUY', 'SELL']:
            operation_type = OperationType.MARKET_ORDER
        elif action in ['BUYLIMIT', 'SELLLIMIT', 'BUYSTOP', 'SELLSTOP']:
            operation_type = OperationType.LIMIT_ORDER
        elif action == 'MODIFY':
            operation_type = OperationType.MODIFY_ORDER
        elif action == 'CLOSE':
            operation_type = OperationType.CLOSE_ORDER
        elif action == 'DELETEPENDING':
            operation_type = OperationType.DELETE_ORDER
        elif action in ['ACTIVE_ORDERS', 'PENDING_ORDERS']:
            operation_type = OperationType.GET_ORDERS

        # 生成请求ID
        request_id = str(uuid.uuid4())

        # 执行心跳检测（除非是ping请求）
        if action != 'PING' and self.heartbeat_enabled:
            self.check_heartbeat()

        # 检查连接状态
        if not self.is_connected or not self.socket:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 请求 {request_id}: 未连接到MT4服务器，尝试重新连接...')

            # 如果是ping请求，不尝试重新连接（避免递归）
            if request.get('action') == 'ping':
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 请求 {request_id}: ping请求跳过重连检查')
            else:
                # 尝试重新连接
                connect_result = self.connect()
                now = datetime.now()
                if connect_result:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 请求 {request_id}: 重新连接成功，继续发送请求')
                else:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 请求 {request_id}: 重新连接失败，无法发送请求')

                    # 记录操作失败
                    log_operation(
                        operation_type=operation_type,
                        success=False,
                        message='未连接到MT4服务器，重新连接失败',
                        parameters=request,
                        error={'status': 'error', 'message': '未连接到MT4服务器，重新连接失败', 'request_id': request_id}
                    )

                    # 记录错误
                    log_error(
                        error_type=ErrorType.CONNECTION_ERROR,
                        message='未连接到MT4服务器，重新连接失败',
                        details={'request': request, 'request_id': request_id},
                        operation=operation_type
                    )

                    return {
                        'status': 'error',
                        'message': '未连接到MT4服务器，重新连接失败',
                        'request_id': request_id
                    }

        try:
            # 使用已生成的请求ID
            if 'requestId' not in request:
                request['requestId'] = request_id

            # 如果设置了授权码，添加到请求中
            if self.auth_code:
                request['auth_code'] = self.auth_code
                request['auth_type'] = 'client'  # 标识为客户端授权
                if self.user_info:
                    request['user_id'] = self.user_info.get('id')

            request_with_id = request

            # 将请求转换为JSON字符串
            request_str = json.dumps(request_with_id)
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 请求 {request_id}: 发送请求到MT4服务器: {request_str}')

            # 记录操作开始
            log_operation(
                operation_type=operation_type,
                success=True,
                message=f'发送请求到MT4服务器: {action}',
                parameters=request_with_id
            )

            # 发送请求
            self.socket.send_string(request_str)

            # 接收响应
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 请求 {request_id}: 等待响应，超时时间: {self.request_timeout}毫秒')
            response_str = self.socket.recv_string()
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 请求 {request_id}: 收到MT4服务器响应，长度: {len(response_str)}字节')

            # 检查响应是否为空
            if not response_str:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 请求 {request_id}: 收到空响应')

                # 记录操作失败
                log_operation(
                    operation_type=operation_type,
                    success=False,
                    message='收到空响应',
                    parameters=request_with_id,
                    error={'status': 'error', 'message': '收到空响应', 'request_id': request_id}
                )

                # 记录错误
                log_error(
                    error_type=ErrorType.MT4_ERROR,
                    message='收到空响应',
                    details={'request': request_with_id, 'request_id': request_id},
                    operation=operation_type
                )

                return {
                    'status': 'error',
                    'message': '收到空响应',
                    'request_id': request_id
                }

            # 尝试解析响应
            try:
                # 移除控制字符
                response_str = ''.join(char for char in response_str if ord(char) >= 32 or char in '\n\r\t')

                # 如果响应被截断，尝试修复JSON
                if response_str.endswith('"order_id":') or response_str.endswith('"order_id":\n'):
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到响应被截断，尝试修复JSON')
                    response_str = response_str + '""}'

                # 检查是否是MT4操作响应且格式不标准
                import re  # 确保在这里导入re模块
                action = request.get('action', '').upper()
                if action and len(response_str) < 100:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到MT4操作响应，长度较短，尝试直接构造标准响应')

                    # 根据操作类型和响应内容构造标准响应
                    if "DELETEPENDING" in action:
                        # 删除挂单响应
                        if "成功" in response_str:
                            response_str = '{"status":"success","message":"挂单删除成功"}'
                        else:
                            # 尝试提取错误信息
                            error_match = re.search(r'错误:\s*([^"]+)', response_str)
                            error_message = error_match.group(1) if error_match else '未知错误'
                            response_str = f'{{"status":"error","message":"挂单删除失败: {error_message}"}}'
                    elif action in ["BUY", "SELL"]:
                        # 市价单响应
                        if "成功" in response_str:
                            # 尝试提取订单ID
                            order_id_match = re.search(r'订单号[：:]\s*(\d+)', response_str)
                            order_id = order_id_match.group(1) if order_id_match else '0'
                            response_str = f'{{"status":"success","message":"市价单执行成功","order_id":"{order_id}"}}'
                        else:
                            # 尝试提取错误信息
                            error_match = re.search(r'错误:\s*([^"]+)', response_str)
                            error_message = error_match.group(1) if error_match else '未知错误'
                            response_str = f'{{"status":"error","message":"市价单执行失败: {error_message}"}}'
                    elif action in ["BUYLIMIT", "SELLLIMIT", "BUYSTOP", "SELLSTOP"]:
                        # 挂单响应
                        if "成功" in response_str:
                            # 尝试提取订单ID
                            order_id_match = re.search(r'订单号[：:]\s*(\d+)', response_str)
                            order_id = order_id_match.group(1) if order_id_match else '0'
                            response_str = f'{{"status":"success","message":"挂单执行成功","order_id":"{order_id}"}}'
                        else:
                            # 尝试提取错误信息
                            error_match = re.search(r'错误:\s*([^"]+)', response_str)
                            error_message = error_match.group(1) if error_match else '未知错误'
                            response_str = f'{{"status":"error","message":"挂单执行失败: {error_message}"}}'
                    elif action == "MODIFY":
                        # 修改订单响应
                        if "成功" in response_str:
                            response_str = f'{{"status":"success","message":"修改订单成功","order_id":"{request.get("order_id", "0")}"}}'
                        else:
                            # 尝试提取错误信息
                            error_match = re.search(r'错误:\s*([^"]+)', response_str)
                            error_message = error_match.group(1) if error_match else '未知错误'
                            response_str = f'{{"status":"error","message":"修改订单失败: {error_message}"}}'
                    elif action == "CLOSE":
                        # 关闭订单响应
                        if "成功" in response_str:
                            response_str = f'{{"status":"success","message":"关闭订单成功","order_id":"{request.get("order_id", "0")}"}}'
                        else:
                            # 尝试提取错误信息
                            error_match = re.search(r'错误:\s*([^"]+)', response_str)
                            error_message = error_match.group(1) if error_match else '未知错误'
                            response_str = f'{{"status":"error","message":"关闭订单失败: {error_message}"}}'

                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 构造的标准响应: {response_str}')

                # 检查是否有未闭合的大括号
                if response_str.count('{') > response_str.count('}'):
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到未闭合的大括号，尝试修复JSON')
                    response_str = response_str + '}'

                # 检查是否有未闭合的引号
                if response_str.count('"') % 2 != 0:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到未闭合的引号，尝试修复JSON')
                    response_str = response_str + '"'

                # 检查是否有未闭合的方括号
                if response_str.count('[') > response_str.count(']'):
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到未闭合的方括号，尝试修复JSON')
                    response_str = response_str + ']}'

                # 检查是否有逗号后面没有内容
                if response_str.rstrip().endswith(','):
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到逗号后面没有内容，尝试修复JSON')
                    response_str = response_str.rstrip()[:-1] + '}'

                # 检查是否包含特殊错误格式 "交易执行失败},"
                if '交易执行失败},' in response_str:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到特殊错误格式 "交易执行失败}},"，尝试修复JSON')
                    response_str = response_str.replace('交易执行失败},', '交易执行失败"}')

                # 检查是否包含特殊错误格式 "交易执行失败, },"
                if '交易执行失败, },' in response_str:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到特殊错误格式 "交易执行失败, }},"，尝试修复JSON')
                    response_str = response_str.replace('交易执行失败, },', '交易执行失败"}')

                # 检查是否包含特殊错误格式 "交易执行失败},"
                if '交易执行失败},"' in response_str:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到特殊错误格式 "交易执行失败}},"，尝试修复JSON')
                    response_str = response_str.replace('交易执行失败},"', '交易执行失败"}')

                # 检查日志中看到的特殊格式
                if '"message":"交易执行失败},"' in response_str:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到特殊错误格式 "message":"交易执行失败}},"，尝试修复JSON')
                    response_str = response_str.replace('"message":"交易执行失败},"', '"message":"交易执行失败"}')

                # 检查是否有异常的逗号和大括号组合
                import re
                strange_patterns = [r',\s*}', r',\s*\]', r'}\s*,\s*"', r']\s*,\s*"']
                for pattern in strange_patterns:
                    if re.search(pattern, response_str):
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到异常格式 "{pattern}"，尝试修复JSON')
                        if pattern == r',\s*}':
                            response_str = re.sub(pattern, '}', response_str)
                        elif pattern == r',\s*\]':
                            response_str = re.sub(pattern, ']', response_str)
                        elif pattern == r'}\s*,\s*"':
                            response_str = re.sub(pattern, '},"', response_str)
                        elif pattern == r']\s*,\s*"':
                            response_str = re.sub(pattern, '],"', response_str)

                # 使用专门的函数处理MT4错误消息
                response_str = self._fix_mt4_error_messages(response_str)

                # 尝试修复常见的JSON格式问题
                response_str = response_str.replace('}{', '},{')
                response_str = response_str.replace('}"', '},"')

                # 确保响应是有效的JSON格式
                if not response_str.startswith('{') and not response_str.startswith('['):
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 响应不是有效的JSON格式，尝试修复')
                    response_str = '{' + response_str + '}'

                # 尝试解析响应
                try:
                    response = json.loads(response_str)
                except json.JSONDecodeError as json_error:
                    # 如果仍然无法解析，尝试最后的保底方案
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 所有修复尝试失败，使用保底方案提取关键信息')

                    # 导入re模块
                    import re

                    # 根据操作类型使用专用保底方案
                    action = request.get('action', '').upper()
                    if action:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到{action}操作响应，使用专用保底方案')

                        # 如果响应中包含"成功"，则认为是成功响应
                        if "成功" in response_str:
                            status = 'success'

                            # 根据操作类型设置消息
                            if "DELETEPENDING" in action:
                                message = '挂单删除成功'
                            elif action in ["BUY", "SELL"]:
                                message = '市价单执行成功'
                            elif action in ["BUYLIMIT", "SELLLIMIT", "BUYSTOP", "SELLSTOP"]:
                                message = '挂单执行成功'
                            elif action == "MODIFY":
                                message = '修改订单成功'
                            elif action == "CLOSE":
                                message = '关闭订单成功'
                            else:
                                message = f'{action}操作成功'
                        else:
                            status = 'error'
                            # 尝试提取错误信息
                            error_match = re.search(r'错误:\s*([^"]+)', response_str)
                            error_message = error_match.group(1) if error_match else '未知错误'

                            # 根据操作类型设置错误消息
                            if "DELETEPENDING" in action:
                                message = f"挂单删除失败: {error_message}"
                            elif action in ["BUY", "SELL"]:
                                message = f"市价单执行失败: {error_message}"
                            elif action in ["BUYLIMIT", "SELLLIMIT", "BUYSTOP", "SELLSTOP"]:
                                message = f"挂单执行失败: {error_message}"
                            elif action == "MODIFY":
                                message = f"修改订单失败: {error_message}"
                            elif action == "CLOSE":
                                message = f"关闭订单失败: {error_message}"
                            else:
                                message = f"{action}操作失败: {error_message}"
                    else:
                        # 尝试提取状态和消息
                        status_match = re.search(r'"status"\s*:\s*"([^"]+)"', response_str)
                        message_match = re.search(r'"message"\s*:\s*"([^"]+)"', response_str)

                        status = status_match.group(1) if status_match else 'error'
                        message = message_match.group(1) if message_match else '解析响应失败'

                    # 如果消息包含异常字符，清理它
                    if message and any(c in message for c in '{}[],"\\'):
                        # 尝试提取有用的错误信息
                        if '交易执行失败' in message:
                            # 尝试提取错误代码
                            error_code_match = re.search(r'错误:\s*([^,}]*)', message)
                            if error_code_match and error_code_match.group(1):
                                message = f'交易执行失败，错误代码：{error_code_match.group(1).strip()}'
                            else:
                                message = '交易执行失败'
                        else:
                            message = '交易执行失败'

                    # 构建一个有效的JSON
                    response = {
                        'status': status,
                        'message': message,
                        '_note': '此响应由保底方案生成，原始响应无法解析'
                    }

                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 保底方案生成的响应: {response}')

                # 记录操作结果
                success = response.get('status') == 'success'
                log_operation(
                    operation_type=operation_type,
                    success=success,
                    message=f'请求处理完成: {action}',
                    parameters=request,
                    result=response,
                    order_id=response.get('order_id')
                )

                # 如果操作失败，记录错误
                if not success:
                    log_error(
                        error_type=ErrorType.MT4_ERROR,
                        message=response.get('message', '未知错误'),
                        details={'request': request, 'response': response},
                        operation=operation_type,
                        order_id=response.get('order_id')
                    )

                return response
            except json.JSONDecodeError as json_error:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 解析响应失败: {json_error}')

                # 记录解析错误
                log_error(
                    error_type=ErrorType.PARSING_ERROR,
                    message=f'解析响应失败: {json_error}',
                    details={'request': request, 'response_str': response_str},
                    operation=operation_type
                )

                # 尝试提取状态和消息
                import re

                # 移除控制字符后再尝试提取
                clean_response = ''.join(char for char in response_str if ord(char) >= 32 or char in '\n\r\t')

                status_match = re.search(r'"status"\s*:\s*"([^"]+)"', clean_response)
                message_match = re.search(r'"message"\s*:\s*"([^"]+)"', clean_response)

                status = status_match.group(1) if status_match else 'error'
                message = message_match.group(1) if message_match else '解析响应失败'

                # 尝试提取更多信息
                try:
                    # 如果是删除挂单操作，尝试提取更多信息
                    if "挂单删除成功" in clean_response:
                        result = {
                            'status': 'success',
                            'message': '挂单删除成功'
                        }

                        # 记录操作成功
                        log_operation(
                            operation_type=operation_type,
                            success=True,
                            message='挂单删除成功',
                            parameters=request,
                            result=result
                        )

                        return result
                    elif "挂单删除失败" in clean_response:
                        error_match = re.search(r'错误:\s*([^"]+)', clean_response)
                        error_message = error_match.group(1) if error_match else '未知错误'
                        result = {
                            'status': 'error',
                            'message': f'挂单删除失败: {error_message}'
                        }

                        # 记录操作失败
                        log_operation(
                            operation_type=operation_type,
                            success=False,
                            message=f'挂单删除失败: {error_message}',
                            parameters=request,
                            error=result
                        )

                        # 记录错误
                        log_error(
                            error_type=ErrorType.MT4_ERROR,
                            message=f'挂单删除失败: {error_message}',
                            details={'request': request, 'response_str': clean_response},
                            operation=operation_type
                        )

                        return result
                except Exception as extract_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提取额外信息失败: {extract_error}')

                    # 记录提取错误
                    log_error(
                        error_type=ErrorType.PARSING_ERROR,
                        message=f'提取额外信息失败: {extract_error}',
                        details={'request': request, 'response_str': clean_response},
                        operation=operation_type
                    )

                result = {
                    'status': status,
                    'message': message
                }

                # 记录操作结果
                log_operation(
                    operation_type=operation_type,
                    success=status == 'success',
                    message=message,
                    parameters=request,
                    result=result
                )

                return result
        except zmq.error.Again:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器响应超时，尝试重新连接并重试')

            # 标记为未连接
            self.is_connected = False

            # 先断开现有连接并清理资源
            self.disconnect()

            # 等待一段时间，让ZMQ有时间释放资源
            time.sleep(1)

            # 尝试重新连接
            if self.connect():
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 重新连接成功，尝试重新发送请求')

                # 重新发送请求（最多重试一次）
                try:
                    # 添加请求ID
                    request_id = str(uuid.uuid4())
                    request_with_id = {
                        **request,
                        'requestId': request_id
                    }

                    # 将请求转换为JSON字符串
                    request_str = json.dumps(request_with_id)
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 重新发送请求到MT4服务器: {request_str}')

                    # 发送请求
                    self.socket.send_string(request_str)

                    # 接收响应
                    response_str = self.socket.recv_string()
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 收到MT4服务器响应: {response_str}')

                    # 解析响应
                    response = json.loads(response_str)

                    # 记录操作结果
                    success = response.get('status') == 'success'
                    log_operation(
                        operation_type=operation_type,
                        success=success,
                        message=f'重试请求处理完成: {action}',
                        parameters=request,
                        result=response,
                        order_id=response.get('order_id')
                    )

                    return response
                except Exception as retry_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 重试请求失败: {retry_error}')

                    # 再次断开连接并清理资源
                    self.disconnect()

            # 重新连接失败或重试失败，记录错误
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器响应超时，重试失败')

            # 记录超时错误
            log_error(
                error_type=ErrorType.CONNECTION_ERROR,
                message='MT4服务器响应超时，重试失败',
                details=request,
                operation=operation_type
            )

            # 记录操作失败
            log_operation(
                operation_type=operation_type,
                success=False,
                message='MT4服务器响应超时，重试失败',
                parameters=request,
                error={'status': 'error', 'message': 'MT4服务器响应超时，重试失败'}
            )

            return {
                'status': 'error',
                'message': 'MT4服务器响应超时，重试失败'
            }
        except Exception as error:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 发送请求到MT4服务器失败: {error}')

            # 标记为未连接
            self.is_connected = False

            # 断开连接并清理资源
            self.disconnect()

            # 记录异常错误
            log_error(
                error_type=ErrorType.UNKNOWN_ERROR,
                message=f'发送请求到MT4服务器失败: {error}',
                details={'request': request, 'exception': str(error)},
                operation=operation_type
            )

            # 记录操作失败
            log_operation(
                operation_type=operation_type,
                success=False,
                message=f'发送请求到MT4服务器失败: {error}',
                parameters=request,
                error={'status': 'error', 'message': f'发送请求到MT4服务器失败: {str(error)}'}
            )

            return {
                'status': 'error',
                'message': f'发送请求到MT4服务器失败: {str(error)}'
            }

    def get_account_info(self):
        """
        获取账户信息

        Returns:
            dict: 账户信息
        """
        return self.send_request({
            'action': 'ACCOUNT_INFO'
        })

    def get_active_orders(self):
        """
        获取活跃订单

        Returns:
            dict: 活跃订单列表
        """
        # 检查是否跳过MT4连接
        if should_skip_mt4_connection():
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ⚠️  MT4服务器跳过模式：模拟获取活跃订单')
            return {'status': 'success', 'orders': [], 'message': '模拟获取活跃订单成功'}

        try:
            # 设置更长的超时时间，专门用于获取活跃订单
            original_timeout = self.request_timeout
            if self.socket:
                self.socket.setsockopt(zmq.RCVTIMEO, 60000)  # 60秒超时
            else:
                # 如果socket为None，说明在跳过模式下，直接返回错误
                return {
                    'status': 'error',
                    'message': 'MT4跳过模式下无法获取活跃订单',
                    'orders': []
                }

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 开始获取活跃订单，超时时间设置为60秒...')

            # 从MT4服务器获取数据
            request = {
                'action': 'ACTIVE_ORDERS',
                'requestId': str(uuid.uuid4())
            }

            # 转换为JSON字符串
            request_str = json.dumps(request)

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 发送请求到MT4服务器: {request_str}')

            # 发送请求
            self.socket.send_string(request_str)

            # 接收响应
            response_str = self.socket.recv_string()

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 收到MT4服务器响应，长度: {len(response_str)}字节')

            # 恢复原来的超时时间
            self.socket.setsockopt(zmq.RCVTIMEO, original_timeout)

            # 尝试使用标准JSON解析
            try:
                # 移除控制字符
                response_str = ''.join(char for char in response_str if ord(char) >= 32 or char in '\n\r\t')

                # 修复常见的JSON格式问题
                response_str = self._fix_mt4_error_messages(response_str)

                # 尝试解析JSON
                response = json.loads(response_str)

                # 检查状态
                if response.get('status') != 'success':
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取活跃订单失败: {response.get("message", "未知错误")}')
                    return {
                        'status': 'error',
                        'message': response.get('message', '获取活跃订单失败'),
                        'orders': []
                    }

                # 获取订单列表
                orders = response.get('orders', [])

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 成功获取 {len(orders)} 个活跃订单')

                return {
                    'status': 'success',
                    'orders': orders
                }

            except json.JSONDecodeError as json_error:
                import re  # 确保re模块在作用域内
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] JSON解析失败，尝试修复JSON格式: {json_error}')

                # 尝试修复常见的JSON格式问题
                fixed_response = response_str

                # 修复末尾多余的逗号
                fixed_response = re.sub(r',\s*}', '}', fixed_response)
                fixed_response = re.sub(r',\s*]', ']', fixed_response)

                # 修复缺失的引号
                fixed_response = re.sub(r'(\w+):', r'"\1":', fixed_response)

                # 修复截断的JSON
                if not fixed_response.endswith('}') and not fixed_response.endswith(']'):
                    # 检查是否在对象内部截断
                    if '"orders":[' in fixed_response:
                        # 计算括号平衡
                        open_brackets = fixed_response.count('[')
                        close_brackets = fixed_response.count(']')
                        open_braces = fixed_response.count('{')
                        close_braces = fixed_response.count('}')

                        # 补全缺失的括号
                        if open_brackets > close_brackets:
                            fixed_response += ']' * (open_brackets - close_brackets)
                        if open_braces > close_braces:
                            fixed_response += '}' * (open_braces - close_braces)
                    elif not fixed_response.endswith('}'):
                        fixed_response += '}'

                # 额外的修复：处理不完整的字符串
                if fixed_response.count('"') % 2 != 0:
                    # 字符串引号不匹配，尝试补全
                    fixed_response += '"'

                # 修复常见的JSON格式问题
                fixed_response = re.sub(r'([^,\[\{])\s*}', r'\1}', fixed_response)  # 移除}前的多余空格
                fixed_response = re.sub(r'([^,\[\{])\s*]', r'\1]', fixed_response)  # 移除]前的多余空格

                # 尝试重新解析修复后的JSON
                try:
                    response = json.loads(fixed_response)
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] JSON修复成功，重新解析')

                    # 检查状态
                    if response.get('status') != 'success':
                        return {
                            'status': 'error',
                            'message': response.get('message', '获取活跃订单失败'),
                            'orders': []
                        }

                    # 获取订单列表
                    orders = response.get('orders', [])
                    return {
                        'status': 'success',
                        'orders': orders
                    }

                except json.JSONDecodeError:
                    # 如果修复后仍然无法解析，使用正则表达式提取
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] JSON修复失败，使用正则表达式提取')

                    # 使用正则表达式提取订单信息
                    # 检查状态
                    status_match = re.search(r'"status"\s*:\s*"([^"]+)"', response_str)
                    status = status_match.group(1) if status_match else 'error'

                    if status != 'success':
                        message_match = re.search(r'"message"\s*:\s*"([^"]+)"', response_str)
                        message = message_match.group(1) if message_match else '获取活跃订单失败'

                        return {
                            'status': 'error',
                            'message': message,
                            'orders': []
                        }

                # 直接使用正则表达式提取订单信息
                orders = []

                # 提取订单ID
                order_ids = re.findall(r'"order_id"\s*:\s*"([^"]+)"', response_str)
                symbols = re.findall(r'"symbol"\s*:\s*"([^"]+)"', response_str)
                types = re.findall(r'"type"\s*:\s*"([^"]+)"', response_str)
                lots = re.findall(r'"lots"\s*:\s*([^,\}]+)', response_str)
                open_prices = re.findall(r'"open_price"\s*:\s*([^,\}]+)', response_str)
                open_times = re.findall(r'"open_time"\s*:\s*"([^"]+)"', response_str)
                sls = re.findall(r'"sl"\s*:\s*([^,\}]+)', response_str)
                tps = re.findall(r'"tp"\s*:\s*([^,\}]+)', response_str)
                profits = re.findall(r'"profit"\s*:\s*([^,\}]+)', response_str)
                comments = re.findall(r'"comment"\s*:\s*"([^"]*)"', response_str)

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提取到 {len(order_ids)} 个订单ID')

                # 确保所有列表长度一致
                min_length = min(len(order_ids), len(symbols), len(types), len(lots), len(open_prices), len(open_times), len(sls), len(tps), len(profits), len(comments))

                for i in range(min_length):
                    try:
                        order = {
                            'order_id': order_ids[i],
                            'symbol': symbols[i],
                            'type': types[i],
                            'lots': float(lots[i]),
                            'open_price': float(open_prices[i]),
                            'open_time': open_times[i],
                            'sl': float(sls[i]),
                            'tp': float(tps[i]),
                            'profit': float(profits[i]),
                            'comment': comments[i]
                        }
                        orders.append(order)
                    except Exception as order_error:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 处理订单 {i+1} 时出错: {order_error}')

                return {
                    'status': 'success',
                    'orders': orders
                }
        except Exception as error:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取活跃订单出错: {error}')
            return {
                'status': 'error',
                'message': f'获取活跃订单出错: {str(error)}',
                'orders': []
            }

    def get_pending_orders(self):
        """
        获取挂单

        Returns:
            dict: 挂单列表
        """
        # 检查是否跳过MT4连接
        if should_skip_mt4_connection():
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ⚠️  MT4服务器跳过模式：模拟获取挂单')
            return {'status': 'success', 'orders': [], 'message': '模拟获取挂单成功'}

        try:
            # 设置更长的超时时间，专门用于获取挂单
            original_timeout = self.request_timeout
            if self.socket:
                self.socket.setsockopt(zmq.RCVTIMEO, 60000)  # 60秒超时
            else:
                # 如果socket为None，说明在跳过模式下，直接返回错误
                return {
                    'status': 'error',
                    'message': 'MT4跳过模式下无法获取挂单',
                    'orders': []
                }

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 开始获取挂单，超时时间设置为60秒...')

            # 从MT4服务器获取数据
            request = {
                'action': 'PENDING_ORDERS',
                'requestId': str(uuid.uuid4())
            }

            # 转换为JSON字符串
            request_str = json.dumps(request)

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 发送请求到MT4服务器: {request_str}')

            # 发送请求
            self.socket.send_string(request_str)

            # 接收响应
            response_str = self.socket.recv_string()

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 收到MT4服务器响应，长度: {len(response_str)}字节')

            # 恢复原来的超时时间
            self.socket.setsockopt(zmq.RCVTIMEO, original_timeout)

            # 尝试使用标准JSON解析
            try:
                # 移除控制字符
                response_str = ''.join(char for char in response_str if ord(char) >= 32 or char in '\n\r\t')

                # 修复常见的JSON格式问题
                response_str = self._fix_mt4_error_messages(response_str)

                # 尝试解析JSON
                response = json.loads(response_str)

                # 检查状态
                if response.get('status') != 'success':
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取挂单失败: {response.get("message", "未知错误")}')
                    return {
                        'status': 'error',
                        'message': response.get('message', '获取挂单失败'),
                        'orders': []
                    }

                # 获取订单列表
                orders = response.get('orders', [])

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 成功获取 {len(orders)} 个挂单')

                return {
                    'status': 'success',
                    'orders': orders
                }

            except json.JSONDecodeError as json_error:
                import re  # 确保re模块在作用域内
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] JSON解析失败，尝试修复JSON格式: {json_error}')

                # 尝试修复常见的JSON格式问题
                fixed_response = response_str

                # 修复末尾多余的逗号
                fixed_response = re.sub(r',\s*}', '}', fixed_response)
                fixed_response = re.sub(r',\s*]', ']', fixed_response)

                # 修复缺失的引号
                fixed_response = re.sub(r'(\w+):', r'"\1":', fixed_response)

                # 修复截断的JSON
                if not fixed_response.endswith('}') and not fixed_response.endswith(']'):
                    # 检查是否在对象内部截断
                    if '"orders":[' in fixed_response:
                        # 计算括号平衡
                        open_brackets = fixed_response.count('[')
                        close_brackets = fixed_response.count(']')
                        open_braces = fixed_response.count('{')
                        close_braces = fixed_response.count('}')

                        # 补全缺失的括号
                        if open_brackets > close_brackets:
                            fixed_response += ']' * (open_brackets - close_brackets)
                        if open_braces > close_braces:
                            fixed_response += '}' * (open_braces - close_braces)
                    elif not fixed_response.endswith('}'):
                        fixed_response += '}'

                # 额外的修复：处理不完整的字符串
                if fixed_response.count('"') % 2 != 0:
                    # 字符串引号不匹配，尝试补全
                    fixed_response += '"'

                # 修复常见的JSON格式问题
                fixed_response = re.sub(r'([^,\[\{])\s*}', r'\1}', fixed_response)  # 移除}前的多余空格
                fixed_response = re.sub(r'([^,\[\{])\s*]', r'\1]', fixed_response)  # 移除]前的多余空格

                # 尝试重新解析修复后的JSON
                try:
                    response = json.loads(fixed_response)
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] JSON修复成功，重新解析')

                    # 检查状态
                    if response.get('status') != 'success':
                        return {
                            'status': 'error',
                            'message': response.get('message', '获取挂单失败'),
                            'orders': []
                        }

                    # 获取订单列表
                    orders = response.get('orders', [])
                    return {
                        'status': 'success',
                        'orders': orders
                    }

                except json.JSONDecodeError:
                    # 如果修复后仍然无法解析，使用正则表达式提取
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] JSON修复失败，使用正则表达式提取')

                    # 使用正则表达式提取订单信息
                    # 检查状态
                    status_match = re.search(r'"status"\s*:\s*"([^"]+)"', response_str)
                    status = status_match.group(1) if status_match else 'error'

                    if status != 'success':
                        message_match = re.search(r'"message"\s*:\s*"([^"]+)"', response_str)
                        message = message_match.group(1) if message_match else '获取挂单失败'

                        return {
                            'status': 'error',
                            'message': message,
                            'orders': []
                        }

                # 直接使用正则表达式提取订单信息
                orders = []

                # 提取订单ID
                order_ids = re.findall(r'"order_id"\s*:\s*"([^"]+)"', response_str)
                symbols = re.findall(r'"symbol"\s*:\s*"([^"]+)"', response_str)
                types = re.findall(r'"type"\s*:\s*"([^"]+)"', response_str)
                lots = re.findall(r'"lots"\s*:\s*([^,\}]+)', response_str)
                open_prices = re.findall(r'"open_price"\s*:\s*([^,\}]+)', response_str)
                open_times = re.findall(r'"open_time"\s*:\s*"([^"]+)"', response_str)
                sls = re.findall(r'"sl"\s*:\s*([^,\}]+)', response_str)
                tps = re.findall(r'"tp"\s*:\s*([^,\}]+)', response_str)
                comments = re.findall(r'"comment"\s*:\s*"([^"]*)"', response_str)

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提取到 {len(order_ids)} 个挂单ID')

                # 确保所有列表长度一致
                min_length = min(
                    len(order_ids), len(symbols), len(types), len(lots),
                    len(open_prices), len(open_times), len(sls), len(tps), len(comments)
                )

                for i in range(min_length):
                    try:
                        order = {
                            'order_id': order_ids[i],
                            'symbol': symbols[i],
                            'type': types[i],
                            'lots': float(lots[i]),
                            'open_price': float(open_prices[i]),
                            'open_time': open_times[i],
                            'sl': float(sls[i]),
                            'tp': float(tps[i]),
                            'comment': comments[i]
                        }
                        orders.append(order)
                    except Exception as order_error:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 处理挂单 {i+1} 时出错: {order_error}')

                return {
                    'status': 'success',
                    'orders': orders
                }
        except Exception as error:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取挂单出错: {error}')
            return {
                'status': 'error',
                'message': f'获取挂单出错: {str(error)}',
                'orders': []
            }

    def get_all_orders(self):
        """
        获取所有订单，包括活跃订单和挂单

        Returns:
            dict: 所有订单列表
        """
        try:
            # 获取活跃订单
            active_orders_response = self.get_active_orders()
            active_orders = active_orders_response.get('orders', [])

            # 获取挂单
            pending_orders_response = self.get_pending_orders()
            pending_orders = pending_orders_response.get('orders', [])

            # 合并订单
            all_orders = active_orders + pending_orders

            return {
                'status': 'success',
                'active_orders': active_orders,
                'pending_orders': pending_orders,
                'all_orders': all_orders
            }
        except Exception as error:
            print(f'获取所有订单出错: {error}')
            return {
                'status': 'error',
                'message': f'获取所有订单出错: {str(error)}',
                'active_orders': [],
                'pending_orders': [],
                'all_orders': []
            }

    def get_market_info(self, symbol):
        """
        获取市场信息

        Args:
            symbol (str): 货币对符号

        Returns:
            dict: 市场信息
        """
        try:
            # 从MT4服务器获取数据
            response = self.send_request({
                'action': 'MARKET_INFO',
                'symbol': symbol
            })

            if not response:
                raise Exception('未收到MT4服务器响应')

            if response.get('status') != 'success':
                raise Exception(f'获取市场信息失败: {response.get("message")}')

            return response
        except Exception as error:
            print(f'获取市场信息出错: {error}')
            raise

    def buy(self, symbol, lot, sl=0, tp=0, comment='', max_retries=3):
        """
        执行市价买入，带重试机制和订单ID提取

        Args:
            symbol (str): 货币对符号
            lot (float): 交易手数
            sl (float, optional): 止损价格
            tp (float, optional): 止盈价格
            comment (str, optional): 注释
            max_retries (int, optional): 最大重试次数

        Returns:
            dict: 交易结果，包含订单ID
        """
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行买入操作: {symbol}, 手数: {lot}, 止损: {sl}, 止盈: {tp}')

        # 添加重试机制
        for attempt in range(max_retries):
            try:
                now = datetime.now()
                if attempt > 0:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 买入操作重试 #{attempt+1}/{max_retries}')

                # 从MT4服务器执行交易
                response = self.send_request({
                    'action': 'BUY',
                    'symbol': symbol,
                    'lot': str(lot),
                    'sl': str(sl),
                    'tp': str(tp),
                    'comment': comment
                })

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 买入操作响应: {response}')

                if not response:
                    raise Exception('未收到MT4服务器响应')

                if response.get('status') != 'success':
                    error_msg = response.get('message', '未知错误')
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行买入操作失败: {error_msg}')

                    # 检查是否需要重试
                    if attempt < max_retries - 1:
                        wait_time = self.retry_delay * (attempt + 1)  # 指数退避
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                        time.sleep(wait_time)
                        continue
                    else:
                        raise Exception(f'执行买入操作失败: {error_msg}')

                # 提取订单ID
                order_id = response.get('order_id', '')

                # 如果响应中没有订单ID，尝试从消息中提取
                if not order_id and 'message' in response:
                    import re
                    # 尝试从消息中提取订单ID
                    id_match = re.search(r'订单号[：:]\s*(\d+)', response['message'])
                    if id_match:
                        order_id = id_match.group(1)

                # 如果成功提取到订单ID，添加到响应中
                if order_id:
                    response['order_id'] = order_id
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 买入操作成功，订单ID: {order_id}')
                else:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 买入操作成功，但未能获取订单ID')

                return response

            except Exception as error:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行买入操作出错: {error}')

                # 检查是否需要重试
                if attempt < max_retries - 1:
                    wait_time = self.retry_delay * (attempt + 1)  # 指数退避
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                    time.sleep(wait_time)
                else:
                    raise

    def sell(self, symbol, lot, sl=0, tp=0, comment='', max_retries=3):
        """
        执行市价卖出，带重试机制和订单ID提取

        Args:
            symbol (str): 货币对符号
            lot (float): 交易手数
            sl (float, optional): 止损价格
            tp (float, optional): 止盈价格
            comment (str, optional): 注释
            max_retries (int, optional): 最大重试次数

        Returns:
            dict: 交易结果，包含订单ID
        """
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行卖出操作: {symbol}, 手数: {lot}, 止损: {sl}, 止盈: {tp}')

        # 添加重试机制
        for attempt in range(max_retries):
            try:
                now = datetime.now()
                if attempt > 0:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 卖出操作重试 #{attempt+1}/{max_retries}')

                # 从MT4服务器执行交易
                response = self.send_request({
                    'action': 'SELL',
                    'symbol': symbol,
                    'lot': str(lot),
                    'sl': str(sl),
                    'tp': str(tp),
                    'comment': comment
                })

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 卖出操作响应: {response}')

                if not response:
                    raise Exception('未收到MT4服务器响应')

                if response.get('status') != 'success':
                    error_msg = response.get('message', '未知错误')
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行卖出操作失败: {error_msg}')

                    # 检查是否需要重试
                    if attempt < max_retries - 1:
                        wait_time = self.retry_delay * (attempt + 1)  # 指数退避
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                        time.sleep(wait_time)
                        continue
                    else:
                        raise Exception(f'执行卖出操作失败: {error_msg}')

                # 提取订单ID
                order_id = response.get('order_id', '')

                # 如果响应中没有订单ID，尝试从消息中提取
                if not order_id and 'message' in response:
                    import re
                    # 尝试从消息中提取订单ID
                    id_match = re.search(r'订单号[：:]\s*(\d+)', response['message'])
                    if id_match:
                        order_id = id_match.group(1)

                # 如果成功提取到订单ID，添加到响应中
                if order_id:
                    response['order_id'] = order_id
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 卖出操作成功，订单ID: {order_id}')
                else:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 卖出操作成功，但未能获取订单ID')

                return response

            except Exception as error:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行卖出操作出错: {error}')

                # 检查是否需要重试
                if attempt < max_retries - 1:
                    wait_time = self.retry_delay * (attempt + 1)  # 指数退避
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                    time.sleep(wait_time)
                else:
                    raise

    def modify_order(self, order_id, sl, tp):
        """
        修改订单

        Args:
            order_id (int): 订单ID
            sl (float): 新的止损价格
            tp (float): 新的止盈价格

        Returns:
            dict: 修改结果
        """
        return self.send_request({
            'action': 'MODIFY',
            'order_id': str(order_id),
            'sl': str(sl),
            'tp': str(tp)
        })

    def close_order(self, order_id, lots=0):
        """
        关闭订单

        Args:
            order_id (int): 订单ID
            lots (float, optional): 平仓手数，如果为0则全部平仓

        Returns:
            dict: 平仓结果
        """
        request = {
            'action': 'CLOSE',
            'order_id': str(order_id)
        }

        if lots > 0:
            request['lots'] = str(lots)

        return self.send_request(request)

    def delete_order(self, order_id, max_retries=3):
        """
        删除挂单

        Args:
            order_id (int): 订单ID
            max_retries (int, optional): 最大重试次数

        Returns:
            dict: 删除结果
        """
        # 使用DELETEPENDING操作删除挂单
        # 这个操作使用MT4的OrderDelete函数删除挂单

        # 添加重试机制
        for attempt in range(max_retries):
            try:
                print(f'尝试删除挂单 {order_id}，第 {attempt + 1} 次尝试')

                response = self.send_request({
                    'action': 'DELETEPENDING',
                    'order_id': str(order_id)
                })

                # 检查响应
                if response.get('status') == 'success':
                    print(f'成功删除挂单 {order_id}')
                    return response

                # 如果失败，记录错误并重试
                print(f'删除挂单 {order_id} 失败: {response.get("message")}，将重试')
                time.sleep(1)  # 等待1秒后重试
            except Exception as error:
                print(f'删除挂单 {order_id} 时出错: {error}，将重试')
                time.sleep(1)  # 等待1秒后重试

        # 所有重试都失败
        print(f'删除挂单 {order_id} 失败，已达到最大重试次数 {max_retries}')
        return {
            'status': 'error',
            'message': f'删除挂单失败，已达到最大重试次数 {max_retries}'
        }

    def modify_position(self, order_id, stop_loss, take_profit):
        """
        修改持仓的止损止盈

        Args:
            order_id (int): 订单ID
            stop_loss (float): 新的止损价格，如果为None则保持原值
            take_profit (float): 新的止盈价格，如果为None则保持原值

        Returns:
            dict: 修改结果
        """
        # 如果止损或止盈为None，先获取当前订单信息
        if stop_loss is None or take_profit is None:
            try:
                # 先检查活跃订单
                positions_response = self.get_active_orders()
                positions = positions_response.get('orders', [])
                for position in positions:
                    if str(position.get('order_id')) == str(order_id):
                        # 如果止损为None，使用当前止损
                        if stop_loss is None:
                            stop_loss = float(position.get('sl', 0))
                            print(f'使用当前止损值: {stop_loss}')

                        # 如果止盈为None，使用当前止盈
                        if take_profit is None:
                            take_profit = float(position.get('tp', 0))
                            print(f'使用当前止盈值: {take_profit}')

                        break

                # 如果不是活跃订单，检查挂单
                if (stop_loss is None or take_profit is None):
                    pending_orders_response = self.get_pending_orders()
                    pending_orders = pending_orders_response.get('orders', [])
                    for order in pending_orders:
                        if str(order.get('order_id')) == str(order_id):
                            # 如果止损为None，使用当前止损
                            if stop_loss is None:
                                stop_loss = float(order.get('sl', 0))
                                print(f'使用当前止损值: {stop_loss}')

                            # 如果止盈为None，使用当前止盈
                            if take_profit is None:
                                take_profit = float(order.get('tp', 0))
                                print(f'使用当前止盈值: {take_profit}')

                            break
            except Exception as e:
                print(f'获取订单信息失败: {e}')
                # 如果获取失败，使用0作为默认值
                if stop_loss is None:
                    stop_loss = 0
                if take_profit is None:
                    take_profit = 0

        # 发送修改请求
        print(f'发送修改订单请求: 订单ID={order_id}, 止损={stop_loss}, 止盈={take_profit}')
        return self.send_request({
            'action': 'MODIFY',
            'order_id': str(order_id),
            'sl': str(stop_loss) if stop_loss is not None else '0',
            'tp': str(take_profit) if take_profit is not None else '0'
        })

    def buy_limit(self, symbol, lot, price, sl=0, tp=0, comment=''):
        """
        执行买入限价单

        Args:
            symbol (str): 货币对符号
            lot (float): 交易手数
            price (float): 限价
            sl (float, optional): 止损价格
            tp (float, optional): 止盈价格
            comment (str, optional): 注释

        Returns:
            dict: 交易结果
        """
        try:
            print(f'执行买入限价单: {symbol}, 手数: {lot}, 价格: {price}, 止损: {sl}, 止盈: {tp}')

            # 检查当前价格
            market_info = self.get_market_info(symbol)
            if not market_info or market_info.get('status') != 'success':
                raise Exception('获取市场信息失败，无法执行限价单')

            current_ask = float(market_info['data']['ask'])
            print(f'当前市场价格(Ask): {current_ask}, 限价单价格: {price}')

            # 如果限价低于当前价格，这是一个有效的限价单
            if price < current_ask:
                # 使用BUYLIMIT命令
                print('使用BUYLIMIT命令执行买入限价单')
                response = self.send_request({
                    'action': 'BUYLIMIT',
                    'symbol': symbol,
                    'lot': str(lot),
                    'price': str(price),
                    'sl': str(sl),
                    'tp': str(tp),
                    'comment': comment
                })

                print('买入限价单响应:', response)

                if not response:
                    raise Exception('未收到MT4服务器响应')

                if response.get('status') != 'success':
                    raise Exception(f'执行买入限价单失败: {response.get("message") or "未知错误"}')

                return response
            else:
                # 如果限价高于或等于当前价格，这不是一个有效的限价单
                print(f'限价({price})高于或等于当前价格({current_ask})，这不是一个有效的限价单')

                # 直接执行市价单
                print('价格已经达到入场价格，执行市价单')
                return self.buy(symbol, lot, sl, tp, comment + '(限价单转市价单)')
        except Exception as error:
            print(f'执行买入限价单出错: {error}')
            raise

    def sell_limit(self, symbol, lot, price, sl=0, tp=0, comment=''):
        """
        执行卖出限价单

        Args:
            symbol (str): 货币对符号
            lot (float): 交易手数
            price (float): 限价
            sl (float, optional): 止损价格
            tp (float, optional): 止盈价格
            comment (str, optional): 注释

        Returns:
            dict: 交易结果
        """
        try:
            print(f'执行卖出限价单: {symbol}, 手数: {lot}, 价格: {price}, 止损: {sl}, 止盈: {tp}')

            # 检查当前价格
            market_info = self.get_market_info(symbol)
            if not market_info or market_info.get('status') != 'success':
                raise Exception('获取市场信息失败，无法执行限价单')

            current_bid = float(market_info['data']['bid'])
            print(f'当前市场价格(Bid): {current_bid}, 限价单价格: {price}')

            # 如果限价高于当前价格，这是一个有效的限价单
            if price > current_bid:
                # 使用SELLLIMIT命令
                print('使用SELLLIMIT命令执行卖出限价单')
                response = self.send_request({
                    'action': 'SELLLIMIT',
                    'symbol': symbol,
                    'lot': str(lot),
                    'price': str(price),
                    'sl': str(sl),
                    'tp': str(tp),
                    'comment': comment
                })

                print('卖出限价单响应:', response)

                if not response:
                    raise Exception('未收到MT4服务器响应')

                if response.get('status') != 'success':
                    raise Exception(f'执行卖出限价单失败: {response.get("message") or "未知错误"}')

                return response
            else:
                # 如果限价低于或等于当前价格，这不是一个有效的限价单
                print(f'限价({price})低于或等于当前价格({current_bid})，这不是一个有效的限价单')

                # 直接执行市价单
                print('价格已经达到入场价格，执行市价单')
                return self.sell(symbol, lot, sl, tp, comment + '(限价单转市价单)')
        except Exception as error:
            print(f'执行卖出限价单出错: {error}')
            raise

    def buy_stop(self, symbol, lot, price, sl=0, tp=0, comment=''):
        """
        执行买入止损单

        Args:
            symbol (str): 货币对符号
            lot (float): 交易手数
            price (float): 止损价格
            sl (float, optional): 止损价格
            tp (float, optional): 止盈价格
            comment (str, optional): 注释

        Returns:
            dict: 交易结果
        """
        try:
            print(f'执行买入止损单: {symbol}, 手数: {lot}, 价格: {price}, 止损: {sl}, 止盈: {tp}')

            # 检查当前价格
            market_info = self.get_market_info(symbol)
            if not market_info or market_info.get('status') != 'success':
                raise Exception('获取市场信息失败，无法执行止损单')

            current_ask = float(market_info['data']['ask'])
            print(f'当前市场价格(Ask): {current_ask}, 止损单价格: {price}')

            # 如果止损价格高于当前价格，这是一个有效的止损单（价格突破向上）
            if price > current_ask:
                # 使用BUYSTOP命令
                print('使用BUYSTOP命令执行买入止损单')
                response = self.send_request({
                    'action': 'BUYSTOP',
                    'symbol': symbol,
                    'lot': str(lot),
                    'price': str(price),
                    'sl': str(sl),
                    'tp': str(tp),
                    'comment': comment
                })

                print('买入止损单响应:', response)

                if not response:
                    raise Exception('未收到MT4服务器响应')

                if response.get('status') != 'success':
                    raise Exception(f'执行买入止损单失败: {response.get("message") or "未知错误"}')

                return response
            else:
                # 如果止损价格低于或等于当前价格，这不是一个有效的止损单
                print(f'止损价格({price})低于或等于当前价格({current_ask})，这不是一个有效的止损单')

                # 直接执行市价单
                print('价格已经达到入场价格，执行市价单')
                return self.buy(symbol, lot, sl, tp, comment + '(止损单转市价单)')
        except Exception as error:
            print(f'执行买入止损单出错: {error}')
            raise

    def sell_stop(self, symbol, lot, price, sl=0, tp=0, comment=''):
        """
        执行卖出止损单

        Args:
            symbol (str): 货币对符号
            lot (float): 交易手数
            price (float): 止损价格
            sl (float, optional): 止损价格
            tp (float, optional): 止盈价格
            comment (str, optional): 注释

        Returns:
            dict: 交易结果
        """
        try:
            print(f'执行卖出止损单: {symbol}, 手数: {lot}, 价格: {price}, 止损: {sl}, 止盈: {tp}')

            # 检查当前价格
            market_info = self.get_market_info(symbol)
            if not market_info or market_info.get('status') != 'success':
                raise Exception('获取市场信息失败，无法执行止损单')

            current_bid = float(market_info['data']['bid'])
            print(f'当前市场价格(Bid): {current_bid}, 止损单价格: {price}')

            # 如果止损价格低于当前价格，这是一个有效的止损单（价格突破向下）
            if price < current_bid:
                # 使用SELLSTOP命令
                print('使用SELLSTOP命令执行卖出止损单')
                response = self.send_request({
                    'action': 'SELLSTOP',
                    'symbol': symbol,
                    'lot': str(lot),
                    'price': str(price),
                    'sl': str(sl),
                    'tp': str(tp),
                    'comment': comment
                })

                print('卖出止损单响应:', response)

                if not response:
                    raise Exception('未收到MT4服务器响应')

                if response.get('status') != 'success':
                    raise Exception(f'执行卖出止损单失败: {response.get("message") or "未知错误"}')

                return response
            else:
                # 如果止损价格高于或等于当前价格，这不是一个有效的止损单
                print(f'止损价格({price})高于或等于当前价格({current_bid})，这不是一个有效的止损单')

                # 直接执行市价单
                print('价格已经达到入场价格，执行市价单')
                return self.sell(symbol, lot, sl, tp, comment + '(止损单转市价单)')
        except Exception as error:
            print(f'执行卖出止损单出错: {error}')
            raise


    def delete_pending_order(self, order_id):
        """
        删除挂单

        Args:
            order_id (str): 订单ID

        Returns:
            dict: 删除结果
        """
        try:
            print(f'删除挂单: {order_id}')

            # 使用DELETEPENDING命令
            response = self.send_request({
                'action': 'DELETEPENDING',
                'order_id': str(order_id)
            })

            print('删除挂单响应:', response)

            if not response:
                raise Exception('未收到MT4服务器响应')

            if response.get('status') != 'success':
                raise Exception(f'删除挂单失败: {response.get("message") or "未知错误"}')

            return response
        except Exception as error:
            print(f'删除挂单出错: {error}')
            return {
                'status': 'error',
                'message': f'删除挂单出错: {str(error)}'
            }

    def get_order_history(self, order_id: str = None, days: int = 7):
        """
        获取订单历史记录

        Args:
            order_id (str, optional): 特定订单ID，如果为None则获取所有历史
            days (int): 查询天数，默认7天

        Returns:
            dict: 历史记录响应
        """
        try:
            print(f'获取订单历史记录: order_id={order_id}, days={days}')

            request = {
                'action': 'ORDER_HISTORY',
                'days': str(days)
            }

            if order_id:
                request['order_id'] = str(order_id)

            response = self.send_request(request)

            if not response:
                raise Exception('未收到MT4服务器响应')

            return response

        except Exception as error:
            print(f'获取订单历史记录出错: {error}')
            return {
                'status': 'error',
                'message': f'获取订单历史记录出错: {str(error)}',
                'orders': []
            }

    def check_order_exists(self, order_id: str):
        """
        检查订单是否存在（在活跃订单或历史记录中）

        Args:
            order_id (str): 订单ID

        Returns:
            bool: 订单是否存在
        """
        try:
            print(f'检查订单是否存在: {order_id}')

            # 首先检查活跃订单
            active_response = self.get_active_orders()
            if active_response and active_response.get('status') == 'success':
                active_orders = active_response.get('orders', [])
                for order in active_orders:
                    if str(order.get('ticket', order.get('order_id', ''))) == str(order_id):
                        print(f'订单 {order_id} 在活跃订单中找到')
                        return True

            # 然后检查挂单
            pending_response = self.get_pending_orders()
            if pending_response and pending_response.get('status') == 'success':
                pending_orders = pending_response.get('orders', [])
                for order in pending_orders:
                    if str(order.get('ticket', order.get('order_id', ''))) == str(order_id):
                        print(f'订单 {order_id} 在挂单中找到')
                        return True

            # 最后检查历史记录
            history_response = self.get_order_history(order_id, days=30)  # 查询30天历史
            if history_response and history_response.get('status') == 'success':
                history_orders = history_response.get('orders', [])
                for order in history_orders:
                    if str(order.get('ticket', order.get('order_id', ''))) == str(order_id):
                        # 检查订单状态，如果是已关闭则返回False
                        order_status = order.get('status', '').lower()
                        if order_status in ['closed', 'cancelled', 'deleted']:
                            print(f'订单 {order_id} 在历史记录中找到，但已关闭')
                            return False
                        else:
                            print(f'订单 {order_id} 在历史记录中找到，状态: {order_status}')
                            return True

            print(f'订单 {order_id} 未找到')
            return False

        except Exception as error:
            print(f'检查订单存在性出错: {error}')
            return None  # 返回None表示无法确定

    def get_closed_orders(self, days: int = 7):
        """
        获取已关闭的订单

        Args:
            days (int): 查询天数，默认7天

        Returns:
            dict: 已关闭订单列表
        """
        try:
            print(f'获取已关闭订单: days={days}')

            response = self.send_request({
                'action': 'CLOSED_ORDERS',
                'days': str(days)
            })

            if not response:
                raise Exception('未收到MT4服务器响应')

            return response

        except Exception as error:
            print(f'获取已关闭订单出错: {error}')
            return {
                'status': 'error',
                'message': f'获取已关闭订单出错: {str(error)}',
                'orders': []
            }


# 创建单例实例
mt4_client = MT4Client()


# 模拟响应函数，用于测试
def mock_response(action, **kwargs):
    """
    生成模拟响应，用于测试

    Args:
        action (str): 操作类型
        **kwargs: 其他参数

    Returns:
        dict: 模拟响应
    """
    if action == 'ping':
        return {
            'status': 'success',
            'message': '连接成功'
        }
    elif action == 'MARKET_INFO':
        return {
            'status': 'success',
            'message': '获取市场信息成功',
            'data': {
                'ask': 1.1350,
                'bid': 1.1348,
                'spread': 2,
                'time': time.strftime('%Y-%m-%d %H:%M:%S')
            }
        }
    elif action == 'ACTIVE_ORDERS':
        return {
            'status': 'success',
            'message': '获取活跃订单成功',
            'orders': [
                {
                    'ticket': 12345,
                    'type': 'BUY',
                    'symbol': 'EURUSD',
                    'lots': 0.1,
                    'open_price': 1.1320,
                    'current_price': 1.1348,
                    'sl': 1.1300,
                    'tp': 1.1400,
                    'profit': 28.0,
                    'comment': '测试订单'
                }
            ]
        }
    elif action in ['BUY', 'SELL', 'BUYLIMIT', 'SELLLIMIT', 'BUYSTOP', 'SELLSTOP']:
        return {
            'status': 'success',
            'message': f'执行{action}操作成功',
            'order_id': 12346,
            'details': {
                'symbol': kwargs.get('symbol', 'EURUSD'),
                'type': action,
                'lots': kwargs.get('lot', 0.1),
                'price': kwargs.get('price', 1.1350),
                'sl': kwargs.get('sl', 0),
                'tp': kwargs.get('tp', 0)
            }
        }
    elif action == 'MODIFY':
        return {
            'status': 'success',
            'message': '修改订单成功',
            'order_id': kwargs.get('order_id', 12345)
        }
    elif action == 'CLOSE':
        return {
            'status': 'success',
            'message': '关闭订单成功',
            'order_id': kwargs.get('order_id', 12345)
        }
    else:
        return {
            'status': 'error',
            'message': f'未知操作: {action}'
        }
