"""
错误日志系统
用于记录系统错误和交易操作的成功/失败状态
"""
import os
import json
import time
from datetime import datetime
import traceback

# 错误日志文件路径
ERROR_LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
ERROR_LOG_FILE = os.path.join(ERROR_LOG_DIR, 'error_log.json')
OPERATION_LOG_FILE = os.path.join(ERROR_LOG_DIR, 'operation_log.json')

# 确保日志目录存在
if not os.path.exists(ERROR_LOG_DIR):
    os.makedirs(ERROR_LOG_DIR)

# 错误类型
class ErrorType:
    """错误类型枚举"""
    CONNECTION_ERROR = "CONNECTION_ERROR"  # 连接错误
    MT4_ERROR = "MT4_ERROR"  # MT4服务器错误
    DATABASE_ERROR = "DATABASE_ERROR"  # 数据库错误
    LLM_ERROR = "LLM_ERROR"  # LLM错误
    PARSING_ERROR = "PARSING_ERROR"  # 解析错误
    EXECUTION_ERROR = "EXECUTION_ERROR"  # 执行错误
    VALIDATION_ERROR = "VALIDATION_ERROR"  # 验证错误
    UNKNOWN_ERROR = "UNKNOWN_ERROR"  # 未知错误
    DATA_ERROR = "DATA_ERROR"  # 数据错误
    SYSTEM_ERROR = "SYSTEM_ERROR"  # 系统错误
    AUTH_ERROR = "AUTH_ERROR"  # 授权错误

# 操作类型
class OperationType:
    """操作类型枚举"""
    MARKET_ORDER = "MARKET_ORDER"  # 市价单
    LIMIT_ORDER = "LIMIT_ORDER"  # 限价单
    STOP_ORDER = "STOP_ORDER"  # 止损单
    MODIFY_ORDER = "MODIFY_ORDER"  # 修改订单
    CLOSE_ORDER = "CLOSE_ORDER"  # 关闭订单
    DELETE_ORDER = "DELETE_ORDER"  # 删除订单
    GET_ORDERS = "GET_ORDERS"  # 获取订单
    ORDER_MANAGEMENT = "ORDER_MANAGEMENT"  # 订单管理
    ANALYSIS = "ANALYSIS"  # 分析
    CONNECTION = "CONNECTION"  # 连接
    DATA_FETCH = "DATA_FETCH"  # 数据获取
    DATA_PROCESSING = "DATA_PROCESSING"  # 数据处理
    AUTH = "AUTH"  # 授权
    OTHER = "OTHER"  # 其他

def log_error(error_type, message, details=None, operation=None, order_id=None, recovery_action=None):
    """
    记录错误

    Args:
        error_type (str): 错误类型
        message (str): 错误消息
        details (dict, optional): 错误详情
        operation (str, optional): 操作类型
        order_id (str, optional): 订单ID
        recovery_action (str, optional): 恢复操作
    """
    try:
        # 获取当前时间
        timestamp = datetime.now().isoformat()

        # 获取堆栈跟踪
        stack_trace = traceback.format_exc() if traceback.format_exc() != 'NoneType: None\n' else None

        # 构建错误记录
        error_record = {
            "timestamp": timestamp,
            "error_type": error_type,
            "message": message,
            "details": details,
            "operation": operation,
            "order_id": order_id,
            "recovery_action": recovery_action,
            "stack_trace": stack_trace
        }

        # 读取现有错误日志
        existing_errors = []
        if os.path.exists(ERROR_LOG_FILE):
            try:
                with open(ERROR_LOG_FILE, 'r', encoding='utf-8') as f:
                    existing_errors = json.load(f)
            except json.JSONDecodeError:
                # 如果文件损坏，创建新文件
                existing_errors = []

        # 添加新错误记录
        existing_errors.append(error_record)

        # 限制错误日志大小（保留最新的1000条记录）
        if len(existing_errors) > 1000:
            existing_errors = existing_errors[-1000:]

        # 写入错误日志
        with open(ERROR_LOG_FILE, 'w', encoding='utf-8') as f:
            json.dump(existing_errors, f, ensure_ascii=False, indent=2)

        # 打印错误信息
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 错误: {error_type} - {message}')
        if details:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 错误详情: {json.dumps(details, ensure_ascii=False)}')

        return True
    except Exception as e:
        # 如果记录错误时出错，打印错误信息
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 记录错误时出错: {e}')
        return False

def log_operation(operation_type, success, message, parameters=None, order_id=None, result=None, error=None):
    """
    记录操作

    Args:
        operation_type (str): 操作类型
        success (bool): 操作是否成功
        message (str): 操作消息
        parameters (dict, optional): 操作参数
        order_id (str, optional): 订单ID
        result (dict, optional): 操作结果
        error (dict, optional): 错误信息
    """
    try:
        # 获取当前时间
        timestamp = datetime.now().isoformat()

        # 构建操作记录
        operation_record = {
            "timestamp": timestamp,
            "operation_type": operation_type,
            "success": success,
            "message": message,
            "parameters": parameters,
            "order_id": order_id,
            "result": result,
            "error": error
        }

        # 读取现有操作日志
        existing_operations = []
        if os.path.exists(OPERATION_LOG_FILE):
            try:
                with open(OPERATION_LOG_FILE, 'r', encoding='utf-8') as f:
                    existing_operations = json.load(f)
            except json.JSONDecodeError:
                # 如果文件损坏，创建新文件
                existing_operations = []

        # 添加新操作记录
        existing_operations.append(operation_record)

        # 限制操作日志大小（保留最新的1000条记录）
        if len(existing_operations) > 1000:
            existing_operations = existing_operations[-1000:]

        # 写入操作日志
        with open(OPERATION_LOG_FILE, 'w', encoding='utf-8') as f:
            json.dump(existing_operations, f, ensure_ascii=False, indent=2)

        # 打印操作信息
        now = datetime.now()
        status = "成功" if success else "失败"
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 操作: {operation_type} - {status} - {message}')

        return True
    except Exception as e:
        # 如果记录操作时出错，打印错误信息
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 记录操作时出错: {e}')
        return False

def get_recent_errors(limit=10, error_type=None, operation=None):
    """
    获取最近的错误记录

    Args:
        limit (int): 限制条数
        error_type (str, optional): 错误类型
        operation (str, optional): 操作类型

    Returns:
        list: 错误记录列表
    """
    try:
        # 读取错误日志
        if not os.path.exists(ERROR_LOG_FILE):
            return []

        with open(ERROR_LOG_FILE, 'r', encoding='utf-8') as f:
            errors = json.load(f)

        # 过滤错误记录
        if error_type:
            errors = [e for e in errors if e.get('error_type') == error_type]
        if operation:
            errors = [e for e in errors if e.get('operation') == operation]

        # 返回最近的错误记录
        return errors[-limit:]
    except Exception as e:
        # 如果获取错误记录时出错，打印错误信息
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取错误记录时出错: {e}')
        return []

def get_recent_operations(limit=10, operation_type=None, success=None, order_id=None):
    """
    获取最近的操作记录

    Args:
        limit (int): 限制条数
        operation_type (str, optional): 操作类型
        success (bool, optional): 操作是否成功
        order_id (str, optional): 订单ID

    Returns:
        list: 操作记录列表
    """
    try:
        # 读取操作日志
        if not os.path.exists(OPERATION_LOG_FILE):
            return []

        with open(OPERATION_LOG_FILE, 'r', encoding='utf-8') as f:
            operations = json.load(f)

        # 过滤操作记录
        if operation_type:
            operations = [o for o in operations if o.get('operation_type') == operation_type]
        if success is not None:
            operations = [o for o in operations if o.get('success') == success]
        if order_id:
            operations = [o for o in operations if o.get('order_id') == order_id]

        # 返回最近的操作记录
        return operations[-limit:]
    except Exception as e:
        # 如果获取操作记录时出错，打印错误信息
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取操作记录时出错: {e}')
        return []

def get_order_operations(order_id, limit=100):
    """
    获取指定订单的操作记录

    Args:
        order_id (str): 订单ID
        limit (int): 限制条数

    Returns:
        list: 操作记录列表
    """
    return get_recent_operations(limit, order_id=order_id)

def get_recent_operations(limit=10, operation_type=None, success=None, order_id=None):
    """
    获取最近的操作记录

    Args:
        limit (int): 限制条数
        operation_type (str, optional): 操作类型
        success (bool, optional): 操作是否成功
        order_id (str, optional): 订单ID

    Returns:
        list: 操作记录列表
    """
    try:
        # 读取操作日志
        if not os.path.exists(OPERATION_LOG_FILE):
            return []

        with open(OPERATION_LOG_FILE, 'r', encoding='utf-8') as f:
            operations = json.load(f)

        # 过滤操作记录
        if operation_type:
            operations = [o for o in operations if o.get('operation_type') == operation_type]
        if success is not None:
            operations = [o for o in operations if o.get('success') == success]
        if order_id:
            operations = [o for o in operations if o.get('order_id') == order_id]

        # 返回最近的操作记录
        return operations[-limit:]
    except Exception as e:
        # 如果获取操作记录时出错，打印错误信息
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取操作记录时出错: {e}')
        return []

def get_all_operations():
    """
    获取所有操作记录

    Returns:
        list: 所有操作记录列表
    """
    try:
        # 读取操作日志
        if not os.path.exists(OPERATION_LOG_FILE):
            return []

        with open(OPERATION_LOG_FILE, 'r', encoding='utf-8') as f:
            operations = json.load(f)

        return operations
    except Exception as e:
        # 如果获取操作记录时出错，打印错误信息
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取所有操作记录时出错: {e}')
        return []

def get_error_statistics(days=7):
    """
    获取错误统计信息

    Args:
        days (int): 统计天数

    Returns:
        dict: 错误统计信息
    """
    try:
        # 读取错误日志
        if not os.path.exists(ERROR_LOG_FILE):
            return {
                "total_errors": 0,
                "error_types": {},
                "operations": {},
                "recovery_actions": {}
            }

        with open(ERROR_LOG_FILE, 'r', encoding='utf-8') as f:
            errors = json.load(f)

        # 计算统计开始时间
        now = datetime.now()
        start_time = now - datetime.timedelta(days=days)
        start_time_str = start_time.isoformat()

        # 过滤最近的错误记录
        recent_errors = [e for e in errors if e.get('timestamp', '') >= start_time_str]

        # 统计错误类型
        error_types = {}
        operations = {}
        recovery_actions = {}

        for error in recent_errors:
            # 统计错误类型
            error_type = error.get('error_type', 'UNKNOWN_ERROR')
            error_types[error_type] = error_types.get(error_type, 0) + 1

            # 统计操作类型
            operation = error.get('operation', 'OTHER')
            operations[operation] = operations.get(operation, 0) + 1

            # 统计恢复操作
            recovery_action = error.get('recovery_action', 'NONE')
            recovery_actions[recovery_action] = recovery_actions.get(recovery_action, 0) + 1

        return {
            "total_errors": len(recent_errors),
            "error_types": error_types,
            "operations": operations,
            "recovery_actions": recovery_actions
        }
    except Exception as e:
        # 如果获取错误统计信息时出错，打印错误信息
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取错误统计信息时出错: {e}')
        return {
            "total_errors": 0,
            "error_types": {},
            "operations": {},
            "recovery_actions": {}
        }
