"""
测试LLM API响应内容
"""
import os
import json
import requests
import time
from datetime import datetime

# DeepSeek API密钥
API_KEY = 'sk-dplvjslhezcjinavtmaporlyumqqwnowcbjwyvmetxychflk'

# API端点
API_ENDPOINT = 'https://api.siliconflow.cn/v1/chat/completions'

def test_llm_response():
    """测试LLM API响应内容"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 开始测试LLM API响应内容...")
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {API_KEY}'
    }
    
    # 使用简单的提示词
    data = {
        'model': 'Pro/deepseek-ai/DeepSeek-R1',
        'messages': [
            {'role': 'system', 'content': '你是一位专业的外汇分析师，擅长分析欧元/美元货币对的技术指标和基本面因素，给出客观、专业的交易建议。请用中文回答。'},
            {'role': 'user', 'content': '请分析当前欧元/美元的走势，并给出交易建议。请在回复中包含JSON格式的交易指令。'}
        ],
        'temperature': 0.1,
        'max_tokens': 1000
    }
    
    try:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 发送请求...")
        start_time = datetime.now()
        
        response = requests.post(
            API_ENDPOINT,
            json=data,
            headers=headers,
            timeout=(15, 120)  # 连接超时15秒，读取超时2分钟
        )
        
        end_time = datetime.now()
        elapsed_time = (end_time - start_time).total_seconds()
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 收到响应，状态码: {response.status_code}，耗时: {elapsed_time:.2f}秒")
        
        # 打印原始响应内容
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 原始响应内容:")
        print(response.text)
        
        if response.status_code == 200:
            response_json = response.json()
            
            # 打印响应的结构
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 响应JSON结构:")
            for key, value in response_json.items():
                if key == 'choices':
                    print(f"  - choices: 包含 {len(value)} 个选项")
                    for i, choice in enumerate(value):
                        print(f"    - choice {i}:")
                        for choice_key, choice_value in choice.items():
                            if choice_key == 'message':
                                print(f"      - message:")
                                for msg_key, msg_value in choice_value.items():
                                    if msg_key == 'content':
                                        print(f"        - content: {len(msg_value)} 字符")
                                        print(f"        - content 前100字符: {msg_value[:100]}...")
                                    else:
                                        print(f"        - {msg_key}: {msg_value}")
                            else:
                                print(f"      - {choice_key}: {choice_value}")
                elif key == 'usage':
                    print(f"  - usage:")
                    for usage_key, usage_value in value.items():
                        print(f"    - {usage_key}: {usage_value}")
                else:
                    print(f"  - {key}: {value}")
            
            # 提取并打印完整的内容
            content = response_json['choices'][0]['message']['content']
            print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 完整响应内容:")
            print(content)
            
            # 检查是否包含JSON格式的交易指令
            json_match = None
            if '```json' in content:
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 检测到JSON格式的交易指令")
                import re
                json_match = re.search(r'```json\s*(\{[\s\S]*?\})\s*```', content)
                if json_match:
                    json_str = json_match.group(1)
                    print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> JSON交易指令:")
                    print(json_str)
                    
                    # 尝试解析JSON
                    try:
                        trade_instructions = json.loads(json_str)
                        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 解析后的交易指令:")
                        print(json.dumps(trade_instructions, indent=2, ensure_ascii=False))
                    except json.JSONDecodeError as e:
                        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> JSON解析失败: {e}")
            
            if not json_match:
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 未检测到JSON格式的交易指令")
            
            # 打印token使用情况
            if 'usage' in response_json:
                prompt_tokens = response_json['usage'].get('prompt_tokens', 0)
                completion_tokens = response_json['usage'].get('completion_tokens', 0)
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> Token使用: 提示词={prompt_tokens}, 生成={completion_tokens}, 总计={prompt_tokens + completion_tokens}")
            
            print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试成功!")
            return True
        else:
            print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试失败! 状态码: {response.status_code}")
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试异常! 错误: {str(e)}")
        return False

if __name__ == "__main__":
    test_llm_response()
