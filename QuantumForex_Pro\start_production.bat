@echo off
chcp 65001
echo ========================================
echo 🚀 启动QuantumForex Pro生产模式
echo ========================================

echo 设置生产环境变量...
set SKIP_MT4_CONNECTION=false
set FLASK_ENV=production
set DEPLOYMENT_ENV=production

echo ✅ 生产模式环境变量已设置
echo   - SKIP_MT4_CONNECTION=false
echo   - FLASK_ENV=production
echo   - DEPLOYMENT_ENV=production
echo.

echo 🔥 启动QuantumForex Pro生产系统...
echo ⚠️  注意：这是真实交易模式！
echo.

python main.py

echo.
echo ========================================
echo 系统已停止
echo ========================================
pause
