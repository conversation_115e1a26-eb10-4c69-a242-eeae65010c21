"""
外汇数据处理工具
用于处理外汇数据，包括数据聚合、指标计算等
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from app.utils.advanced_technical_analysis import calculate_advanced_indicators
from app.utils.advanced_analysis_formatter import format_advanced_analysis


def aggregate_klines(min_data, period_minutes):
    """
    将1分钟K线数据聚合为更高时间周期的K线数据

    Args:
        min_data (list): 1分钟K线数据数组
        period_minutes (int): 目标周期（分钟数）

    Returns:
        list: 聚合后的K线数据
    """
    if not min_data or len(min_data) == 0:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 步骤2.1: 错误: K线数据为空")
        return []

    # 简化日志输出
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 步骤2.1: 聚合K线数据: {len(min_data)}条 -> {period_minutes}分钟")

    # 转换为DataFrame
    df = pd.DataFrame(min_data)

    # 确保时间列是datetime类型
    try:
        df['time'] = pd.to_datetime(df['time'])
    except Exception as e:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 步骤2.1: 错误: 时间列转换失败")
        raise

    # 检查时间列是否有重复值或缺失值（只在有问题时输出日志）
    duplicate_times = df['time'].duplicated().sum()
    if duplicate_times > 0:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 步骤2.1: 警告: 时间列有{duplicate_times}个重复值")

    # 按照指定的时间周期进行重采样
    # 设置时间为索引
    df.set_index('time', inplace=True)

    # 检查索引是否已排序
    if not df.index.is_monotonic_increasing:
        df = df.sort_index()

    # 按照指定的时间周期进行重采样
    try:
        resampled = df.resample(f'{period_minutes}min').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        })
    except Exception as e:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 步骤2.1: 错误: 重采样失败")
        raise

    # 检查是否有缺失值
    missing_values = resampled.isnull().sum().sum()
    if missing_values > 0:
        # 填充缺失值
        resampled = resampled.ffill()

    # 重置索引
    resampled.reset_index(inplace=True)

    # 转换为字典列表
    result = resampled.to_dict('records')

    # 确保时间格式正确
    for item in result:
        item['time'] = item['time'].strftime('%Y-%m-%d %H:%M:%S')  # 统一使用标准格式

    # 只输出简洁的结果信息
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 步骤2.1: 聚合完成: {len(result)}条数据")

    return result


def calculate_sma(data, period, field='close'):
    """
    计算简单移动平均线 (SMA)，确保与MT4计算方式一致

    Args:
        data (list): K线数据数组
        period (int): 周期
        field (str): 计算字段，默认为'close'

    Returns:
        list: SMA值数组
    """
    # 转换为DataFrame
    df = pd.DataFrame(data)

    # 确保数据类型正确
    if field in df.columns:
        df[field] = df[field].astype(float)
    else:
        print(f"警告: 字段 '{field}' 不存在于数据中，使用'close'代替")
        field = 'close'
        if field not in df.columns:
            print(f"错误: 'close'字段也不存在于数据中")
            return [None] * len(data)
        df[field] = df[field].astype(float)

    # 计算SMA - 确保与MT4计算方式一致
    # MT4的SMA是简单的算术平均，每个价格点的权重相同
    # 使用min_periods=period确保只有当有足够的数据时才计算SMA
    df[f'sma_{period}'] = df[field].rolling(window=period, min_periods=period).mean()

    # 打印一些调试信息，帮助诊断问题
    print(f"计算{period}周期SMA，数据长度: {len(data)}, 有效SMA值数量: {df[f'sma_{period}'].notna().sum()}")
    if not df[f'sma_{period}'].isna().all():
        print(f"最新SMA值: {df[f'sma_{period}'].iloc[-1]}")

    # 转换为列表
    result = df[f'sma_{period}'].tolist()

    return result


def calculate_ema(data, period, field='close'):
    """
    计算指数移动平均线 (EMA)

    Args:
        data (list): K线数据数组
        period (int): 周期
        field (str): 计算字段，默认为'close'

    Returns:
        list: EMA值数组
    """
    # 转换为DataFrame
    df = pd.DataFrame(data)

    # 计算EMA
    df[f'ema_{period}'] = df[field].ewm(span=period, adjust=False).mean()

    # 转换为列表
    result = df[f'ema_{period}'].tolist()

    return result


def calculate_rsi(data, period=14):
    """
    计算相对强弱指标 (RSI)

    Args:
        data (list): K线数据数组
        period (int): 周期，默认为14

    Returns:
        list: RSI值数组
    """
    # 转换为DataFrame
    df = pd.DataFrame(data)

    # 计算价格变化
    df['price_change'] = df['close'].diff()

    # 分离上涨和下跌
    df['gain'] = df['price_change'].apply(lambda x: x if x > 0 else 0)
    df['loss'] = df['price_change'].apply(lambda x: abs(x) if x < 0 else 0)

    # 计算平均上涨和下跌
    df['avg_gain'] = df['gain'].rolling(window=period).mean()
    df['avg_loss'] = df['loss'].rolling(window=period).mean()

    # 计算相对强度
    df['rs'] = df['avg_gain'] / df['avg_loss'].apply(lambda x: x if x != 0 else 0.0001)

    # 计算RSI
    df['rsi'] = 100 - (100 / (1 + df['rs']))

    # 转换为列表
    result = df['rsi'].tolist()

    return result


def calculate_macd(data, fast_period=12, slow_period=26, signal_period=9):
    """
    计算MACD指标

    Args:
        data (list): K线数据数组
        fast_period (int): 快线周期，默认为12
        slow_period (int): 慢线周期，默认为26
        signal_period (int): 信号线周期，默认为9

    Returns:
        dict: 包含MACD线、信号线和柱状图的字典
    """
    # 转换为DataFrame
    df = pd.DataFrame(data)

    # 计算快线EMA
    df['ema_fast'] = df['close'].ewm(span=fast_period, adjust=False).mean()

    # 计算慢线EMA
    df['ema_slow'] = df['close'].ewm(span=slow_period, adjust=False).mean()

    # 计算MACD线
    df['macd_line'] = df['ema_fast'] - df['ema_slow']

    # 计算信号线
    df['signal_line'] = df['macd_line'].ewm(span=signal_period, adjust=False).mean()

    # 计算柱状图
    df['histogram'] = df['macd_line'] - df['signal_line']

    # 转换为字典
    result = {
        'macdLine': df['macd_line'].tolist(),
        'signalLine': df['signal_line'].tolist(),
        'histogram': df['histogram'].tolist()
    }

    return result


def calculate_bollinger_bands(data, period=20, multiplier=2):
    """
    计算布林带指标

    Args:
        data (list): K线数据数组
        period (int): 周期，默认为20
        multiplier (int): 标准差乘数，默认为2

    Returns:
        dict: 包含中轨、上轨和下轨的字典
    """
    # 转换为DataFrame
    df = pd.DataFrame(data)

    # 计算中轨（SMA）
    df['middle'] = df['close'].rolling(window=period).mean()

    # 计算标准差
    df['std'] = df['close'].rolling(window=period).std()

    # 计算上轨和下轨
    df['upper'] = df['middle'] + (df['std'] * multiplier)
    df['lower'] = df['middle'] - (df['std'] * multiplier)

    # 转换为字典
    result = {
        'upper': df['upper'].tolist(),
        'middle': df['middle'].tolist(),
        'lower': df['lower'].tolist()
    }

    return result


def calculate_momentum(data, period=14):
    """
    计算动量指标

    Args:
        data (list): K线数据数组
        period (int): 周期，默认为14

    Returns:
        list: 动量值数组
    """
    # 转换为DataFrame
    df = pd.DataFrame(data)

    # 计算动量
    df['momentum'] = df['close'].diff(period)

    # 转换为列表
    result = df['momentum'].tolist()

    return result


def perform_advanced_analysis(data, timeframe='15min'):
    """
    执行高级技术分析

    Args:
        data (list): K线数据数组
        timeframe (str): 时间周期，如 '15min', '1h' 等

    Returns:
        dict: 高级技术分析结果
    """
    # 转换为DataFrame
    df = pd.DataFrame(data)

    # 确保列名正确
    if 'time' not in df.columns:
        df['time'] = pd.to_datetime(df.index)

    # 计算高级技术指标
    advanced_indicators = calculate_advanced_indicators(df, timeframe)

    # 生成分析文本
    analysis_text = format_advanced_analysis(advanced_indicators, timeframe)

    # 返回结果
    result = {
        'indicators': advanced_indicators,
        'analysis': analysis_text
    }

    return result


def calculate_technical_indicators(data, timeframe='15min'):
    """
    计算技术指标 - 兼容接口

    Args:
        data (list): K线数据数组
        timeframe (str): 时间周期

    Returns:
        dict: 技术指标结果
    """
    if not data or len(data) == 0:
        return {}

    try:
        # 计算各种技术指标
        indicators = {}

        # 移动平均线
        indicators['ma_5'] = calculate_sma(data, 5)[-1] if len(data) >= 5 else None
        indicators['ma_10'] = calculate_sma(data, 10)[-1] if len(data) >= 10 else None
        indicators['ma_20'] = calculate_sma(data, 20)[-1] if len(data) >= 20 else None
        indicators['ma_50'] = calculate_sma(data, 50)[-1] if len(data) >= 50 else None

        # RSI
        rsi_values = calculate_rsi(data)
        indicators['rsi'] = rsi_values[-1] if rsi_values and not pd.isna(rsi_values[-1]) else None

        # MACD
        macd_result = calculate_macd(data)
        if macd_result['macdLine']:
            indicators['macd'] = macd_result['macdLine'][-1]
            indicators['macd_signal'] = macd_result['signalLine'][-1]
            indicators['macd_histogram'] = macd_result['histogram'][-1]

        # 布林带
        bb_result = calculate_bollinger_bands(data)
        if bb_result['upper']:
            indicators['bb_upper'] = bb_result['upper'][-1]
            indicators['bb_middle'] = bb_result['middle'][-1]
            indicators['bb_lower'] = bb_result['lower'][-1]

        # 动量
        momentum_values = calculate_momentum(data)
        indicators['momentum'] = momentum_values[-1] if momentum_values and not pd.isna(momentum_values[-1]) else None

        # 计算ATR（简化版）
        if len(data) >= 14:
            df = pd.DataFrame(data)
            df['tr'] = np.maximum(
                df['high'] - df['low'],
                np.maximum(
                    abs(df['high'] - df['close'].shift(1)),
                    abs(df['low'] - df['close'].shift(1))
                )
            )
            indicators['atr'] = df['tr'].rolling(window=14).mean().iloc[-1]

        return indicators

    except Exception as e:
        print(f"计算技术指标失败: {e}")
        return {}