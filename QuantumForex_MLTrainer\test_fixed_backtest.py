#!/usr/bin/env python3
"""
测试修复后的回测引擎
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'QuantumForex_Pro'))

def test_fixed_backtest():
    """测试修复后的回测"""
    print("🧪 测试修复后的回测引擎")
    print("="*40)
    
    try:
        from model_evaluation.real_backtest_engine import RealBacktestEngine
        
        # 找到AUDUSD模型
        models_dir = Path("data/models")
        audusd_models = list(models_dir.glob("AUDUSD_price_prediction_*.pkl"))
        audusd_models = [f for f in audusd_models if 'scaler' not in f.name]
        
        if not audusd_models:
            print("❌ 没有找到AUDUSD模型")
            return
        
        latest_model = max(audusd_models, key=lambda x: x.stat().st_mtime)
        print(f"📦 测试模型: {latest_model.name}")
        
        # 创建回测引擎
        backtest_engine = RealBacktestEngine()
        
        # 运行回测
        print("🚀 开始回测...")
        metrics = backtest_engine.run_comprehensive_backtest(
            model_path=str(latest_model),
            data_days=3  # 使用3天数据快速测试
        )
        
        if metrics:
            print(f"\n📊 回测结果:")
            print(f"   总交易数: {metrics.total_trades}")
            print(f"   胜率: {metrics.win_rate:.1%}")
            print(f"   总收益率: {metrics.total_return:.1%}")
            print(f"   最大回撤: {metrics.max_drawdown:.1%}")
            print(f"   预测准确率: {metrics.prediction_accuracy:.1%}")
            print(f"   夏普比率: {metrics.sharpe_ratio:.3f}")
            
            if metrics.total_trades > 0:
                print(f"\n🎉 回测成功！生成了{metrics.total_trades}笔交易")
                return True
            else:
                print(f"\n⚠️ 回测完成但没有交易")
                return False
        else:
            print(f"\n❌ 回测失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_backtest()
    if success:
        print("\n✅ 回测引擎修复成功！")
    else:
        print("\n❌ 回测引擎仍有问题")
