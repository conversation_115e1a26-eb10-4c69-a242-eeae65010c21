#!/usr/bin/env python3
"""
测试生产模式设置
验证系统是否正确设置为生产模式
"""

import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_production_mode():
    """测试生产模式设置"""
    print("🔍 检查生产模式设置")
    print("="*50)

    # 1. 检查环境变量
    skip_mt4 = os.environ.get('SKIP_MT4_CONNECTION', '').lower()
    print(f"📊 SKIP_MT4_CONNECTION环境变量: '{skip_mt4}'")

    if skip_mt4 == 'false':
        print("✅ 环境变量正确设置为生产模式")
    elif skip_mt4 == 'true':
        print("❌ 环境变量设置为测试模式")
        return False
    else:
        print("⚠️ 环境变量未设置，将使用自动判断")

    # 2. 测试MT4客户端跳过逻辑
    try:
        from utils.mt4_client import should_skip_mt4_connection

        skip_status = should_skip_mt4_connection()
        print(f"📊 MT4跳过状态: {skip_status}")

        if skip_status:
            print("❌ 系统仍然处于模拟模式")
            print("💡 可能原因:")
            print("   - 市场时间判断认为当前是非交易时间")
            print("   - 环境变量设置未生效")
            return False
        else:
            print("✅ 系统正确设置为生产模式")
            return True

    except Exception as e:
        print(f"❌ 测试MT4跳过逻辑失败: {e}")
        return False

def test_mt4_connection():
    """测试MT4连接"""
    print("\n🔍 测试MT4连接")
    print("="*50)

    try:
        from utils.mt4_client import MT4Client

        # 创建MT4客户端
        mt4_client = MT4Client()
        print("✅ MT4客户端创建成功")

        # 尝试连接
        print("🔗 尝试连接MT4服务器...")
        connected = mt4_client.connect()

        if connected:
            if hasattr(mt4_client, 'user_info') and mt4_client.user_info and mt4_client.user_info.get('username') == '测试用户':
                print("❌ 连接成功但仍在模拟模式")
                print(f"   用户信息: {mt4_client.user_info}")
                return False
            else:
                print("✅ 真实MT4连接成功")
                print(f"   连接状态: {mt4_client.is_connected}")
                print(f"   授权状态: {mt4_client.is_authorized}")
                if hasattr(mt4_client, 'user_info') and mt4_client.user_info:
                    print(f"   用户信息: {mt4_client.user_info}")
                return True
        else:
            print("❌ MT4连接失败")
            return False

    except Exception as e:
        print(f"❌ MT4连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def force_production_mode():
    """强制设置生产模式"""
    print("\n🔧 强制设置生产模式")
    print("="*50)

    # 设置环境变量
    os.environ['SKIP_MT4_CONNECTION'] = 'false'
    os.environ['FLASK_ENV'] = 'production'
    os.environ['DEPLOYMENT_ENV'] = 'production'

    print("✅ 环境变量已强制设置:")
    print(f"   SKIP_MT4_CONNECTION = {os.environ.get('SKIP_MT4_CONNECTION')}")
    print(f"   FLASK_ENV = {os.environ.get('FLASK_ENV')}")
    print(f"   DEPLOYMENT_ENV = {os.environ.get('DEPLOYMENT_ENV')}")

    return True

def main():
    """主测试函数"""
    print("🚀 QuantumForex Pro 生产模式验证")
    print("="*60)

    # 强制设置生产模式
    force_production_mode()

    # 测试生产模式设置
    production_mode_ok = test_production_mode()

    # 测试MT4连接
    mt4_connection_ok = test_mt4_connection()

    print(f"\n📊 测试结果总结:")
    print(f"   生产模式设置: {'✅ 正确' if production_mode_ok else '❌ 错误'}")
    print(f"   MT4连接测试: {'✅ 正常' if mt4_connection_ok else '❌ 异常'}")

    if production_mode_ok and mt4_connection_ok:
        print(f"\n🎉 生产模式验证成功！")
        print(f"💡 系统现在应该使用真实MT4服务器")
        return True
    else:
        print(f"\n❌ 生产模式验证失败")
        print(f"💡 系统可能仍在模拟模式")

        if not production_mode_ok:
            print(f"\n🔧 修复建议:")
            print(f"   1. 检查市场时间判断逻辑")
            print(f"   2. 确认环境变量设置")
            print(f"   3. 重启系统使环境变量生效")

        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 可以启动生产模式系统")
    else:
        print("\n❌ 需要修复模拟模式问题")
