"""
测试JSON解析错误修复和重复订单检测

这个脚本测试系统对JSON解析错误的修复和重复订单检测的改进
"""
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append('.')

# 导入需要测试的模块
from app.utils.llm_client import parse_trade_instructions
from app.services.forex_trading_service import execute_trade
from app.utils.mt4_client import mt4_client

def print_header(message):
    """打印带有分隔线的标题"""
    print("\n" + "=" * 80)
    print(f" {message} ".center(80, "="))
    print("=" * 80)

def test_json_date_time_parsing():
    """测试日期时间格式解析修复"""
    print_header("测试日期时间格式解析修复")

    # 测试用例：日期时间格式错误的JSON
    test_json = """
    ```json
    {
      "action": "BUY",
      "orderType": "LIMIT",
      "entryPrice": 1.1309,
      "stopLoss": 1.1294,
      "takeProfit": 1.1335,
      "lotSize": 0.12,
      "conditions": ["15分钟RSI<65时激活", "突破1.1318后移动止损至1.1309"],
      "expiration": "2025-05-23 14:30:00",
      "riskLevel": "MEDIUM_HIGH",
      "reasoning": "价格回踩13日均线，符合右侧交易策略"
    }
    ```
    """

    # 解析JSON
    result = parse_trade_instructions(test_json)

    # 验证结果
    print(f"解析结果: {result}")
    assert result['action'] == 'BUY', "动作应该是BUY"
    assert result['orderType'] == 'LIMIT', "订单类型应该是LIMIT"
    assert result['entryPrice'] == 1.1309, "入场价格应该是1.1309"
    assert result['stopLoss'] == 1.1294, "止损应该是1.1294"
    assert result['takeProfit'] == 1.1335, "止盈应该是1.1335"
    assert result['lotSize'] == 0.12, "手数应该是0.12"
    assert len(result['conditions']) == 2, "应该有2个条件"
    assert "15分钟RSI<65时激活" in result['conditions'], "条件1应该是'15分钟RSI<65时激活'"
    assert "突破1.1318后移动止损至1.1309" in result['conditions'], "条件2应该是'突破1.1318后移动止损至1.1309'"
    assert result['expiration'] == "2025-05-23 14:30" or result['expiration'] == "2025-05-23 14:30:00", "过期时间应该是'2025-05-23 14:30'或'2025-05-23 14:30:00'"

    print("测试通过: 系统成功修复并解析了日期时间格式错误的JSON")

def test_conditions_parsing():
    """测试条件字段解析修复"""
    print_header("测试条件字段解析修复")

    # 测试用例：条件字段格式错误的JSON
    test_json = """
    ```json
    {
      "action": "BUY",
      "orderType": "LIMIT",
      "entryPrice": 1.1309,
      "stopLoss": 1.1294,
      "takeProfit": 1.1335,
      "lotSize": 0.12,
      "conditions": "15分钟RSI<65时激活",
      "expiration": "2025-05-23 14:30:00",
      "riskLevel": "MEDIUM_HIGH",
      "reasoning": "价格回踩13日均线，符合右侧交易策略"
    }
    ```
    """

    # 解析JSON
    result = parse_trade_instructions(test_json)

    # 验证结果
    print(f"解析结果: {result}")
    assert result['action'] == 'BUY', "动作应该是BUY"
    assert result['orderType'] == 'LIMIT', "订单类型应该是LIMIT"
    assert len(result['conditions']) == 1, "应该有1个条件"
    assert "15分钟RSI<65时激活" in result['conditions'], "条件应该是'15分钟RSI<65时激活'"

    print("测试通过: 系统成功修复并解析了条件字段格式错误的JSON")

def test_duplicate_order_detection():
    """测试重复订单检测"""
    print_header("测试重复订单检测")

    # 确保MT4连接
    if not mt4_client.is_connected:
        print('MT4客户端未连接，尝试连接')
        connected = mt4_client.connect()
        if not connected:
            print('无法连接到MT4客户端，测试失败')
            return

    # 获取市场信息
    market_info = mt4_client.get_market_info('EURUSD')
    if not market_info or market_info.get('status') != 'success':
        print('获取市场信息失败，测试终止')
        return

    current_price = float(market_info['data']['ask'])
    print(f'当前EURUSD价格: {current_price}')

    # 创建一个买入限价单，不带条件和过期时间
    trade_instructions1 = {
        'action': 'BUY',
        'orderType': 'LIMIT',
        'entryPrice': round(current_price - 0.0050, 5),  # 限价设置在当前价格下方50点
        'stopLoss': round(current_price - 0.0080, 5),  # 止损设置在限价下方30点
        'takeProfit': round(current_price + 0.0050, 5),  # 止盈设置在当前价格上方50点
        'lotSize': 0.01,  # 最小手数
        'riskLevel': 'LOW',  # 低风险
        'reasoning': '测试买入限价单（无条件和过期时间）'
    }

    # 创建一个相同的买入限价单，但带有条件和过期时间
    trade_instructions2 = {
        'action': 'BUY',
        'orderType': 'LIMIT',
        'entryPrice': round(current_price - 0.0050, 5),  # 相同的限价
        'stopLoss': round(current_price - 0.0080, 5),  # 相同的止损
        'takeProfit': round(current_price + 0.0050, 5),  # 相同的止盈
        'lotSize': 0.01,  # 最小手数
        'riskLevel': 'LOW',  # 低风险
        'reasoning': '测试买入限价单（带条件和过期时间）',
        'conditions': ['15分钟RSI<65时激活'],
        'expiration': (datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    }

    # 执行第一个交易
    print('执行第一个交易（无条件和过期时间）...')
    trade_result1 = execute_trade(trade_instructions1, check_duplicate=False)
    print(f'第一个交易执行结果: {trade_result1}')

    # 等待一秒
    time.sleep(1)

    # 执行第二个交易（相同价格，但带有条件和过期时间）
    print('执行第二个交易（相同价格，带条件和过期时间）...')
    trade_result2 = execute_trade(trade_instructions2, check_duplicate=True)
    print(f'第二个交易执行结果: {trade_result2}')

    # 等待一秒
    time.sleep(1)

    # 再次执行第一个交易（无条件和过期时间）
    print('再次执行第一个交易（无条件和过期时间）...')
    trade_result3 = execute_trade(trade_instructions1, check_duplicate=True)
    print(f'再次执行第一个交易结果: {trade_result3}')

    # 验证结果
    assert trade_result1['success'] == True, "第一个交易应该成功"
    assert trade_result2['success'] == True, "第二个交易应该成功（带条件和过期时间）"
    assert trade_result3['success'] == False, "第三个交易应该失败（重复订单）"
    assert trade_result3.get('duplicate') == True, "第三个交易应该标记为重复订单"

    print("测试通过: 系统成功检测到重复订单，并允许带条件和过期时间的订单执行")

def run_all_tests():
    """运行所有测试"""
    print_header("开始测试JSON解析错误修复和重复订单检测")

    try:
        # 测试日期时间格式解析修复
        test_json_date_time_parsing()

        # 测试条件字段解析修复
        test_conditions_parsing()

        # 测试重复订单检测
        test_duplicate_order_detection()

        print_header("所有测试完成")
        print("JSON解析错误修复和重复订单检测测试结果: 通过")
    except AssertionError as e:
        print(f"测试失败: {str(e)}")
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 只运行JSON解析测试，不运行重复订单检测测试
    print_header("开始测试JSON解析错误修复")
    try:
        # 测试日期时间格式解析修复
        test_json_date_time_parsing()

        # 测试条件字段解析修复
        test_conditions_parsing()

        print_header("JSON解析测试完成")
        print("JSON解析错误修复测试结果: 通过")
    except AssertionError as e:
        print(f"测试失败: {str(e)}")
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
