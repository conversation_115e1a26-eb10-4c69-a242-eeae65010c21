#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试专业策略系统
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_strategy_import():
    """测试策略模块导入"""
    try:
        print("🔍 测试策略模块导入...")
        
        from strategies.professional_portfolio_strategy import ProfessionalPortfolioStrategy
        print("✅ ProfessionalPortfolioStrategy 导入成功")
        
        from strategies.master_strategy_engine import MasterStrategyEngine
        print("✅ MasterStrategyEngine 导入成功")
        
        # 测试实例化
        portfolio_strategy = ProfessionalPortfolioStrategy()
        print("✅ ProfessionalPortfolioStrategy 实例化成功")
        
        master_strategy = MasterStrategyEngine()
        print("✅ MasterStrategyEngine 实例化成功")
        
        print("🎉 所有策略模块测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 策略模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_functionality():
    """测试策略功能"""
    try:
        print("\n🧠 测试策略功能...")
        
        from strategies.master_strategy_engine import MasterStrategyEngine
        import pandas as pd
        import numpy as np
        from datetime import datetime
        
        # 创建主策略引擎
        master_strategy = MasterStrategyEngine()
        
        # 创建模拟市场数据
        dates = pd.date_range(end=datetime.now(), periods=100, freq='1min')
        
        market_data = {
            'EURUSD': {
                'ohlcv': pd.DataFrame({
                    'open': np.random.normal(1.0850, 0.001, 100),
                    'high': np.random.normal(1.0855, 0.001, 100),
                    'low': np.random.normal(1.0845, 0.001, 100),
                    'close': np.random.normal(1.0850, 0.001, 100),
                    'volume': np.random.randint(1000, 5000, 100)
                }, index=dates),
                'current_price': 1.0850,
                'volatility': 0.001
            },
            'GBPUSD': {
                'ohlcv': pd.DataFrame({
                    'open': np.random.normal(1.2650, 0.001, 100),
                    'high': np.random.normal(1.2655, 0.001, 100),
                    'low': np.random.normal(1.2645, 0.001, 100),
                    'close': np.random.normal(1.2650, 0.001, 100),
                    'volume': np.random.randint(1000, 5000, 100)
                }, index=dates),
                'current_price': 1.2650,
                'volatility': 0.001
            }
        }
        
        # 模拟当前持仓
        current_positions = {
            'EURUSD': {
                'orders': [{'order_id': '123', 'action': 'BUY', 'size': 0.01, 'entry_price': 1.0840}],
                'total_size': 0.01,
                'total_value': 0.01084,
                'avg_entry_price': 1.0840,
                'weight': 0.5
            }
        }
        
        # 生成策略决策
        print("📊 生成策略决策...")
        master_decision = master_strategy.generate_master_decision(market_data, current_positions)
        
        print(f"✅ 策略决策生成成功:")
        print(f"   策略类型: {master_decision.strategy_used.value}")
        print(f"   市场条件: {master_decision.market_condition.value}")
        print(f"   置信度: {master_decision.confidence:.2%}")
        print(f"   决策数量: {len(master_decision.decisions)}")
        print(f"   决策说明: {master_decision.reasoning}")
        
        if master_decision.decisions:
            print("📋 具体决策:")
            for i, decision in enumerate(master_decision.decisions, 1):
                print(f"   {i}. {decision.symbol} - {decision.action.value}")
                print(f"      仓位: {decision.size:.3f}手")
                print(f"      置信度: {decision.confidence:.2%}")
        
        print("🎉 策略功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 策略功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试专业策略系统")
    print("="*50)
    
    # 测试导入
    if not test_strategy_import():
        return False
    
    # 测试功能
    if not test_strategy_functionality():
        return False
    
    print("\n🎉 所有测试通过！专业策略系统运行正常！")
    return True

if __name__ == "__main__":
    main()
