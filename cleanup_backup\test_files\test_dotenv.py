import sys
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")
print(f"系统路径: {sys.path}")

try:
    import python_dotenv
    print(f"python_dotenv路径: {python_dotenv.__file__}")
except ImportError as e:
    print(f"无法导入python_dotenv: {e}")

try:
    from dotenv import load_dotenv
    print("成功导入dotenv.load_dotenv")
except ImportError as e:
    print(f"无法导入dotenv.load_dotenv: {e}")

try:
    import pip
    print(f"pip版本: {pip.__version__}")
    print("已安装的包:")
    for pkg in pip._vendor.pkg_resources.working_set:
        if 'dotenv' in pkg.project_name.lower():
            print(f"  - {pkg.project_name} {pkg.version}")
except ImportError as e:
    print(f"无法导入pip: {e}")
