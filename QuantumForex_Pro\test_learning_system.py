#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试学习系统
验证交易记录、模式分析、参数优化功能
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_trade_recording():
    """测试交易记录功能"""
    try:
        print("🔍 测试交易记录功能...")
        
        from core.learning_system import TradeResultRecorder
        
        recorder = TradeResultRecorder()
        
        # 模拟交易开仓
        trade_data = {
            'symbol': 'EURUSD',
            'action': 'BUY',
            'entry_price': 1.0850,
            'volume': 0.01,
            'stop_loss': 1.0830,
            'take_profit': 1.0890,
            'confidence': 0.75,
            'strategy_used': 'portfolio_strategy',
            'market_condition': 'trending',
            'rsi': 65.0,
            'ma_20': 1.0845,
            'ma_50': 1.0840,
            'atr': 0.002,
            'volatility': 0.008
        }
        
        trade_id = recorder.record_trade_entry(trade_data)
        print(f"✅ 记录交易开仓: {trade_id}")
        
        # 模拟交易平仓
        time.sleep(1)  # 模拟持仓时间
        exit_data = {
            'exit_price': 1.0870,
            'exit_reason': 'take_profit',
            'max_favorable_excursion': 0.0025,
            'max_adverse_excursion': 0.0005
        }
        
        recorder.record_trade_exit(trade_id, exit_data)
        print(f"✅ 记录交易平仓: {trade_id}")
        
        # 获取统计信息
        stats = recorder.get_statistics()
        print(f"📊 交易统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 交易记录测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pattern_analysis():
    """测试模式分析功能"""
    try:
        print("\n🧠 测试模式分析功能...")
        
        from core.learning_system import TradeResultRecorder, PatternAnalyzer
        
        recorder = TradeResultRecorder()
        analyzer = PatternAnalyzer(recorder)
        
        # 创建一些模拟交易记录
        print("📝 创建模拟交易记录...")
        
        # 盈利交易模式
        for i in range(15):
            trade_data = {
                'symbol': 'EURUSD',
                'action': 'BUY',
                'entry_price': 1.0850 + i * 0.0001,
                'volume': 0.01,
                'stop_loss': 1.0830 + i * 0.0001,
                'take_profit': 1.0890 + i * 0.0001,
                'confidence': 0.8,
                'strategy_used': 'portfolio_strategy',
                'market_condition': 'trending',
                'rsi': 70.0,  # 超买区域
                'ma_20': 1.0845 + i * 0.0001,
                'ma_50': 1.0840 + i * 0.0001,
                'atr': 0.002,
                'volatility': 0.008
            }
            
            trade_id = recorder.record_trade_entry(trade_data)
            
            # 模拟盈利平仓
            exit_data = {
                'exit_price': 1.0870 + i * 0.0001,
                'exit_reason': 'take_profit'
            }
            recorder.record_trade_exit(trade_id, exit_data)
        
        # 亏损交易模式
        for i in range(8):
            trade_data = {
                'symbol': 'GBPUSD',
                'action': 'SELL',
                'entry_price': 1.2650 - i * 0.0001,
                'volume': 0.01,
                'stop_loss': 1.2670 - i * 0.0001,
                'take_profit': 1.2610 - i * 0.0001,
                'confidence': 0.6,
                'strategy_used': 'portfolio_strategy',
                'market_condition': 'ranging',
                'rsi': 30.0,  # 超卖区域
                'ma_20': 1.2655 - i * 0.0001,
                'ma_50': 1.2660 - i * 0.0001,
                'atr': 0.0025,
                'volatility': 0.012
            }
            
            trade_id = recorder.record_trade_entry(trade_data)
            
            # 模拟亏损平仓
            exit_data = {
                'exit_price': 1.2665 - i * 0.0001,
                'exit_reason': 'stop_loss'
            }
            recorder.record_trade_exit(trade_id, exit_data)
        
        print("✅ 模拟交易记录创建完成")
        
        # 分析模式
        print("🔍 开始模式分析...")
        patterns = analyzer.analyze_patterns(days=1)
        
        print(f"📊 发现 {len(patterns['winning_patterns'])} 个盈利模式:")
        for pattern in patterns['winning_patterns']:
            print(f"   ✅ {pattern.description} - 胜率: {pattern.win_rate:.1%}, 样本: {pattern.sample_size}")
        
        print(f"📊 发现 {len(patterns['losing_patterns'])} 个亏损模式:")
        for pattern in patterns['losing_patterns']:
            print(f"   ❌ {pattern.description} - 胜率: {pattern.win_rate:.1%}, 样本: {pattern.sample_size}")
        
        # 测试模式建议
        current_conditions = {
            'market_condition': 'trending',
            'rsi': 70.0,
            'confidence': 0.8
        }
        
        recommendations = analyzer.get_pattern_recommendations(current_conditions)
        print(f"💡 当前条件下的建议:")
        for rec in recommendations:
            print(f"   {rec}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模式分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_optimization():
    """测试参数优化功能"""
    try:
        print("\n🔧 测试参数优化功能...")
        
        from core.learning_system import TradeResultRecorder, PatternAnalyzer, ParameterOptimizer
        
        recorder = TradeResultRecorder()
        analyzer = PatternAnalyzer(recorder)
        optimizer = ParameterOptimizer(recorder, analyzer)
        
        # 获取当前参数
        current_params = optimizer.get_current_parameters()
        print(f"📊 当前参数: {current_params}")
        
        # 强制执行参数优化
        print("🔍 执行参数优化...")
        optimization_results = optimizer.optimize_parameters(force=True)
        
        if optimization_results:
            print(f"✅ 参数优化完成，优化了 {len(optimization_results)} 个参数:")
            for result in optimization_results:
                print(f"   📊 {result.parameter_name}: {result.old_value} → {result.new_value}")
                print(f"      改进评分: {result.improvement_score:.3f}")
                print(f"      置信度: {result.confidence:.1%}")
        else:
            print("ℹ️ 当前参数已经是最优的，无需优化")
        
        # 获取优化摘要
        summary = optimizer.get_optimization_summary()
        print(f"📈 优化摘要: {summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_learning_coordinator():
    """测试学习协调器"""
    try:
        print("\n🎛️ 测试学习协调器...")
        
        from core.learning_system import LearningCoordinator
        
        coordinator = LearningCoordinator()
        coordinator.start()
        
        print("✅ 学习协调器启动成功")
        
        # 测试交易记录
        trade_data = {
            'symbol': 'EURUSD',
            'action': 'BUY',
            'entry_price': 1.0850,
            'volume': 0.01,
            'stop_loss': 1.0830,
            'take_profit': 1.0890,
            'confidence': 0.75,
            'strategy_used': 'portfolio_strategy',
            'market_condition': 'trending'
        }
        
        trade_id = coordinator.record_trade_entry(trade_data)
        print(f"✅ 通过协调器记录交易: {trade_id}")
        
        # 测试模式分析请求
        pattern_task_id = coordinator.request_pattern_analysis()
        print(f"✅ 请求模式分析: {pattern_task_id}")
        
        # 测试参数优化请求
        optimization_task_id = coordinator.request_parameter_optimization(force=True)
        print(f"✅ 请求参数优化: {optimization_task_id}")
        
        # 等待任务执行
        time.sleep(3)
        
        # 获取学习统计
        stats = coordinator.get_learning_statistics()
        print(f"📊 学习统计: {stats}")
        
        # 停止协调器
        coordinator.stop()
        print("✅ 学习协调器停止成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 学习协调器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始学习系统测试")
    print("="*60)
    
    success_count = 0
    total_tests = 4
    
    # 测试交易记录
    if test_trade_recording():
        success_count += 1
    
    # 测试模式分析
    if test_pattern_analysis():
        success_count += 1
    
    # 测试参数优化
    if test_parameter_optimization():
        success_count += 1
    
    # 测试学习协调器
    if test_learning_coordinator():
        success_count += 1
    
    print("\n" + "="*60)
    print(f"🎯 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有学习系统测试通过！")
        print("✅ 交易记录功能正常")
        print("✅ 模式分析功能正常")
        print("✅ 参数优化功能正常")
        print("✅ 学习协调器功能正常")
        print("\n🏆 学习系统完全可用！")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
    
    return success_count == total_tests

if __name__ == "__main__":
    main()
