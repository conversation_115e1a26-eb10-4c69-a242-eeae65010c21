"""
QuantumForex MLTrainer 云服务器传输模块
处理本地训练端与腾讯云交易端之间的模型传输
"""

import os
import json
import gzip
import hashlib
import logging
import requests
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any

from config.network_config import network_config

class CloudTransferManager:
    """云服务器传输管理器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 网络配置
        self.trading_server_ip = network_config.NETWORK_CONFIG['trading_server_ip']
        self.api_base = network_config.FILE_TRANSFER_CONFIG['cloud_api_base']

        # 本地路径
        self.local_folder = Path(network_config.FILE_TRANSFER_CONFIG['local_folder'])
        self.models_folder = Path(network_config.FILE_TRANSFER_CONFIG['models_folder'])
        self.upload_folder = Path(network_config.FILE_TRANSFER_CONFIG['upload_folder'])

        # 传输配置
        self.transfer_config = network_config.FILE_TRANSFER_CONFIG['transfer_settings']

        # 创建必要文件夹
        self._create_folders()

        # 请求会话
        self.session = requests.Session()
        self.session.timeout = network_config.NETWORK_CONFIG['timeout']

    def _create_folders(self):
        """创建必要的文件夹"""
        folders = [
            self.local_folder,
            self.models_folder,
            self.upload_folder,
            self.local_folder / 'logs',
            self.local_folder / 'backup',
            self.local_folder / 'temp'
        ]

        for folder in folders:
            folder.mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"创建文件夹: {folder}")

    def test_connection(self) -> bool:
        """测试与云服务器的连接"""
        try:
            self.logger.info("🔍 测试云服务器连接...")

            # 方法1: 简单的ping测试
            import subprocess
            result = subprocess.run(['ping', '-n', '1', self.trading_server_ip],
                                   capture_output=True, text=True)

            if result.returncode == 0:
                self.logger.info(f"✅ 服务器网络可达: {self.trading_server_ip}")
            else:
                self.logger.warning(f"⚠️ 服务器ping失败: {self.trading_server_ip}")

            # 方法2: HTTP连接测试
            try:
                test_url = f"http://{self.trading_server_ip}:8081/api/health"
                response = self.session.get(test_url, timeout=10)

                if response.status_code == 200:
                    self.logger.info("✅ API接口连接正常")
                    return True
                else:
                    self.logger.warning(f"⚠️ API接口返回状态码: {response.status_code}")

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"⚠️ API接口连接失败: {e}")

            # 方法3: 基础TCP连接测试
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((self.trading_server_ip, 8081))
            sock.close()

            if result == 0:
                self.logger.info("✅ TCP端口连接正常")
                return True
            else:
                self.logger.warning("⚠️ TCP端口连接失败")

            return False

        except Exception as e:
            self.logger.error(f"❌ 连接测试失败: {e}")
            return False

    def upload_model(self, model_file: Path, model_type: str = None) -> Dict[str, Any]:
        """上传模型到云服务器"""
        try:
            self.logger.info(f"📤 开始上传模型: {model_file.name}")

            if not model_file.exists():
                raise FileNotFoundError(f"模型文件不存在: {model_file}")

            # 准备上传数据
            upload_data = self._prepare_upload_data(model_file, model_type)

            # 执行上传
            upload_result = self._execute_upload(upload_data)

            # 验证上传结果
            if upload_result['success']:
                self.logger.info(f"✅ 模型上传成功: {model_file.name}")

                # 保存上传记录
                self._save_upload_record(model_file, upload_result)
            else:
                self.logger.error(f"❌ 模型上传失败: {upload_result.get('error', 'Unknown error')}")

            return upload_result

        except Exception as e:
            self.logger.error(f"❌ 上传模型异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _prepare_upload_data(self, model_file: Path, model_type: str = None) -> Dict[str, Any]:
        """准备上传数据"""
        # 计算文件校验和
        file_checksum = self._calculate_checksum(model_file)

        # 暂时禁用压缩以简化测试
        upload_file = model_file
        compressed = False

        # 压缩文件（如果启用）
        # if self.transfer_config['compression']:
        #     compressed_file = self._compress_file(model_file)
        #     upload_file = compressed_file
        #     compressed = True
        # else:
        #     upload_file = model_file
        #     compressed = False

        # 准备元数据
        metadata = {
            'original_name': model_file.name,
            'model_type': model_type or self._detect_model_type(model_file.name),
            'file_size': model_file.stat().st_size,
            'checksum': file_checksum,
            'compressed': compressed,
            'upload_time': datetime.now().isoformat(),
            'version': self._generate_version()
        }

        return {
            'file_path': upload_file,
            'metadata': metadata,
            'compressed': compressed
        }

    def _execute_upload(self, upload_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行文件上传"""
        try:
            upload_url = f"{self.api_base}/models/upload"

            # 准备文件和数据
            files = {
                'model_file': open(upload_data['file_path'], 'rb')
            }

            data = {
                'metadata': json.dumps(upload_data['metadata'])
            }

            # 执行上传请求
            response = self.session.post(
                upload_url,
                files=files,
                data=data,
                timeout=300  # 5分钟超时
            )

            files['model_file'].close()

            # 处理响应
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'response': result,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}",
                    'timestamp': datetime.now().isoformat()
                }

        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f"网络请求失败: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"上传异常: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }

    def _calculate_checksum(self, file_path: Path) -> str:
        """计算文件校验和"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def _compress_file(self, file_path: Path) -> Path:
        """压缩文件"""
        compressed_path = self.upload_folder / f"{file_path.name}.gz"

        with open(file_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb') as f_out:
                f_out.writelines(f_in)

        self.logger.debug(f"文件已压缩: {file_path.name} → {compressed_path.name}")
        return compressed_path

    def _detect_model_type(self, filename: str) -> str:
        """检测模型类型"""
        if 'price_prediction' in filename:
            return 'price_prediction'
        elif 'risk_assessment' in filename:
            return 'risk_assessment'
        elif 'trend_classification' in filename:
            return 'trend_classification'
        elif 'volatility_prediction' in filename:
            return 'volatility_prediction'
        else:
            return 'unknown'

    def _generate_version(self) -> str:
        """生成版本号"""
        return f"2.1.{datetime.now().strftime('%Y%m%d%H%M')}"

    def _save_upload_record(self, model_file: Path, upload_result: Dict[str, Any]):
        """保存上传记录"""
        try:
            record = {
                'model_name': model_file.name,
                'upload_time': datetime.now().isoformat(),
                'file_size': model_file.stat().st_size,
                'upload_result': upload_result
            }

            records_file = self.local_folder / 'logs' / 'upload_records.json'

            # 读取现有记录
            if records_file.exists():
                with open(records_file, 'r', encoding='utf-8') as f:
                    records = json.load(f)
            else:
                records = []

            # 添加新记录
            records.append(record)

            # 保持最近100条记录
            if len(records) > 100:
                records = records[-100:]

            # 保存记录
            with open(records_file, 'w', encoding='utf-8') as f:
                json.dump(records, f, indent=2, ensure_ascii=False)

            self.logger.debug(f"上传记录已保存: {records_file}")

        except Exception as e:
            self.logger.error(f"保存上传记录失败: {e}")

    def upload_all_models(self) -> Dict[str, Any]:
        """上传所有可用模型"""
        try:
            self.logger.info("📤 开始批量上传模型...")

            # 查找所有模型文件
            model_files = list(self.models_folder.glob('*.pkl'))

            if not model_files:
                self.logger.warning("⚠️ 没有找到可上传的模型文件")
                return {
                    'success': True,
                    'uploaded_count': 0,
                    'total_count': 0,
                    'message': '没有可上传的模型'
                }

            # 执行批量上传
            upload_results = []
            success_count = 0

            for model_file in model_files:
                try:
                    result = self.upload_model(model_file)
                    upload_results.append({
                        'file': model_file.name,
                        'result': result
                    })

                    if result['success']:
                        success_count += 1

                except Exception as e:
                    self.logger.error(f"上传模型失败 {model_file.name}: {e}")
                    upload_results.append({
                        'file': model_file.name,
                        'result': {'success': False, 'error': str(e)}
                    })

            # 生成总结报告
            summary = {
                'success': success_count > 0,
                'uploaded_count': success_count,
                'total_count': len(model_files),
                'success_rate': success_count / len(model_files) if model_files else 0,
                'upload_results': upload_results,
                'timestamp': datetime.now().isoformat()
            }

            self.logger.info(f"📊 批量上传完成: {success_count}/{len(model_files)} 成功")

            # 保存批量上传报告
            self._save_batch_upload_report(summary)

            return summary

        except Exception as e:
            self.logger.error(f"❌ 批量上传失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _save_batch_upload_report(self, summary: Dict[str, Any]):
        """保存批量上传报告"""
        try:
            report_file = self.local_folder / 'logs' / f'batch_upload_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)

            self.logger.info(f"📊 批量上传报告已保存: {report_file}")

        except Exception as e:
            self.logger.error(f"保存批量上传报告失败: {e}")

    def get_cloud_server_status(self) -> Dict[str, Any]:
        """获取云服务器状态"""
        try:
            status_url = f"{self.api_base}/models/status"

            response = self.session.get(status_url, timeout=30)

            if response.status_code == 200:
                return {
                    'success': True,
                    'status': response.json(),
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}",
                    'timestamp': datetime.now().isoformat()
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            temp_folders = [
                self.upload_folder,
                self.local_folder / 'temp'
            ]

            cleaned_count = 0

            for temp_folder in temp_folders:
                if temp_folder.exists():
                    for temp_file in temp_folder.iterdir():
                        if temp_file.is_file():
                            temp_file.unlink()
                            cleaned_count += 1

            self.logger.info(f"🧹 临时文件清理完成: {cleaned_count}个文件")

        except Exception as e:
            self.logger.error(f"❌ 清理临时文件失败: {e}")
