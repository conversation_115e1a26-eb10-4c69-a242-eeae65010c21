#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能提示词系统
目标：根据市场状态、历史表现动态调整LLM提示词，提高分析准确性
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class TradingContext:
    """交易上下文"""
    market_regime: str
    volatility_level: str
    trend_strength: float
    time_of_day: str
    recent_performance: Dict
    current_positions: List[Dict]

class IntelligentPromptGenerator:
    """智能提示词生成器"""
    
    def __init__(self):
        self.base_templates = self._load_base_templates()
        self.performance_patterns = self._load_performance_patterns()
        self.market_specific_guidance = self._load_market_guidance()
    
    def generate_adaptive_prompt(self, context: TradingContext, analysis_data: Dict) -> str:
        """生成自适应提示词"""
        
        # 1. 选择基础模板
        base_template = self._select_base_template(context.market_regime)
        
        # 2. 添加市场状态特定指导
        market_guidance = self._get_market_specific_guidance(context)
        
        # 3. 添加历史学习内容
        learning_content = self._generate_learning_content(context.recent_performance)
        
        # 4. 添加风险调整建议
        risk_guidance = self._generate_risk_guidance(context)
        
        # 5. 组装最终提示词
        final_prompt = self._assemble_prompt(
            base_template, market_guidance, learning_content, 
            risk_guidance, analysis_data
        )
        
        return final_prompt
    
    def _select_base_template(self, market_regime: str) -> str:
        """根据市场状态选择基础模板"""
        templates = {
            'TRENDING': self._get_trending_template(),
            'RANGING': self._get_ranging_template(),
            'BREAKOUT': self._get_breakout_template(),
            'REVERSAL': self._get_reversal_template(),
            'UNCERTAIN': self._get_conservative_template()
        }
        
        return templates.get(market_regime, templates['UNCERTAIN'])
    
    def _get_trending_template(self) -> str:
        """趋势市场模板"""
        return """
# 趋势市场交易分析 - 13日均线右侧策略优化

## 当前市场状态：强趋势环境
**关键策略**：趋势跟随，回踩入场，严格止损

### 分析重点（按优先级排序）：
1. **13日均线趋势确认**（权重40%）
   - 15分钟和1小时均线方向一致性
   - 均线角度和分离度
   - 价格与均线的相对位置

2. **回踩机会识别**（权重30%）
   - 价格回踩13日均线的深度和速度
   - 回踩过程中的成交量变化
   - 回踩后的反弹确认信号

3. **趋势强度评估**（权重20%）
   - 价格效率指标
   - 连续性K线形态
   - 动量指标确认

4. **风险控制**（权重10%）
   - 趋势反转早期信号
   - 关键支撑阻力位
   - 仓位管理建议

### 交易执行指导：
- **入场时机**：价格回踩至13日均线附近（±5点）且出现反弹信号
- **止损设置**：均线反方向15-20点
- **止盈策略**：动态追踪，关注趋势减弱信号
- **仓位建议**：中等到较大仓位（0.08-0.15）
"""
    
    def _get_ranging_template(self) -> str:
        """震荡市场模板"""
        return """
# 震荡市场交易分析 - 支撑阻力策略

## 当前市场状态：震荡整理环境
**关键策略**：区间交易，边界反弹，快进快出

### 分析重点（按优先级排序）：
1. **支撑阻力位识别**（权重40%）
   - 关键价格水平的有效性
   - 多次测试的历史表现
   - 当前价格与边界的距离

2. **震荡区间确认**（权重30%）
   - 区间上下边界的清晰度
   - 区间内价格运行特征
   - 突破概率评估

3. **反弹信号确认**（权重20%）
   - 边界附近的价格行为
   - 成交量和动量确认
   - 反转K线形态

4. **突破风险管理**（权重10%）
   - 假突破识别
   - 真突破确认条件
   - 止损保护机制

### 交易执行指导：
- **入场时机**：价格接近支撑阻力位且出现反弹信号
- **止损设置**：支撑阻力位外5-10点
- **止盈策略**：对边界附近，快速获利
- **仓位建议**：较小仓位（0.03-0.08）
"""
    
    def _get_breakout_template(self) -> str:
        """突破市场模板"""
        return """
# 突破市场交易分析 - 突破跟随策略

## 当前市场状态：突破行情环境
**关键策略**：突破确认，快速跟随，严控风险

### 分析重点（按优先级排序）：
1. **突破有效性确认**（权重40%）
   - 突破的力度和速度
   - 成交量配合情况
   - 回测确认信号

2. **突破方向判断**（权重30%）
   - 突破前的蓄势特征
   - 多时间框架确认
   - 基本面支持情况

3. **假突破风险**（权重20%）
   - 历史假突破模式
   - 当前突破质量评估
   - 快速反转信号

4. **追踪策略**（权重10%）
   - 突破后的持续性
   - 回调买入机会
   - 获利了结时机

### 交易执行指导：
- **入场时机**：突破确认后的回测或持续突破
- **止损设置**：突破点下方10-15点
- **止盈策略**：分批获利，保护利润
- **仓位建议**：小到中等仓位（0.05-0.10）
"""
    
    def _get_reversal_template(self) -> str:
        """反转市场模板"""
        return """
# 反转市场交易分析 - 反转确认策略

## 当前市场状态：潜在反转环境
**关键策略**：反转确认，谨慎入场，快速止损

### 分析重点（按优先级排序）：
1. **反转信号确认**（权重40%）
   - 技术指标背离
   - 反转K线形态
   - 多时间框架确认

2. **反转强度评估**（权重30%）
   - 前期趋势的疲软程度
   - 反转动量的强弱
   - 关键位置的重要性

3. **确认信号等待**（权重20%）
   - 反转后的持续性
   - 回调测试表现
   - 新趋势建立信号

4. **风险控制**（权重10%）
   - 反转失败的概率
   - 原趋势恢复风险
   - 快速止损机制

### 交易执行指导：
- **入场时机**：反转确认且回调测试成功
- **止损设置**：反转失败点位外8-12点
- **止盈策略**：保守获利，分批平仓
- **仓位建议**：较小仓位（0.03-0.08）
"""
    
    def _get_conservative_template(self) -> str:
        """保守市场模板"""
        return """
# 不确定市场环境 - 保守观望策略

## 当前市场状态：方向不明确
**关键策略**：观望为主，等待明确信号

### 分析重点：
1. **信号清晰度评估**
2. **风险收益比分析**
3. **等待更好机会**
4. **资金保护优先**

### 交易执行指导：
- **建议行动**：观望或极小仓位测试
- **仓位建议**：0.01-0.03或观望
"""
    
    def _get_market_specific_guidance(self, context: TradingContext) -> str:
        """获取市场特定指导"""
        guidance_parts = []
        
        # 波动率指导
        if context.volatility_level == 'HIGH':
            guidance_parts.append("""
### 高波动率环境特别提醒：
- 扩大止损距离至20-30点，避免被噪音止损
- 减小仓位至正常的70%，控制风险
- 关注重要支撑阻力位，避免在中间位置交易
- 考虑分批入场，降低单次风险
""")
        elif context.volatility_level == 'LOW':
            guidance_parts.append("""
### 低波动率环境特别提醒：
- 缩小止损距离至8-15点，提高资金效率
- 可适当增加仓位至正常的120%
- 关注小幅突破机会，但要快速获利
- 避免长期持仓，市场可能突然变化
""")
        
        # 时间段指导
        if context.time_of_day in ['ASIAN', 'EARLY_EUROPEAN']:
            guidance_parts.append("""
### 亚洲/欧洲早盘特别提醒：
- 市场流动性相对较低，避免大仓位交易
- 关注欧洲开盘后的方向性变化
- 重要经济数据发布前保持谨慎
""")
        elif context.time_of_day == 'EUROPEAN':
            guidance_parts.append("""
### 欧洲盘特别提醒：
- 市场活跃度提升，可以适当增加交易频率
- 关注欧元相关消息和数据
- 为美盘开盘做好准备
""")
        elif context.time_of_day == 'US':
            guidance_parts.append("""
### 美国盘特别提醒：
- 市场最活跃时段，波动可能加大
- 关注美元相关数据和美联储消息
- 注意纽约收盘前的获利了结行为
""")
        
        return '\n'.join(guidance_parts)
    
    def _generate_learning_content(self, recent_performance: Dict) -> str:
        """生成历史学习内容"""
        if not recent_performance:
            return ""
        
        win_rate = recent_performance.get('win_rate', 0)
        avg_profit = recent_performance.get('avg_profit', 0)
        recent_trades = recent_performance.get('recent_trades', [])
        
        learning_parts = []
        
        # 胜率分析
        if win_rate < 40:
            learning_parts.append("""
### 近期表现分析 - 胜率偏低：
**问题诊断**：入场时机可能过于激进，需要等待更强确认信号
**改进建议**：
- 提高入场标准，等待多重确认
- 减小仓位，降低单次损失
- 重点关注高概率设置
""")
        elif win_rate > 70:
            learning_parts.append("""
### 近期表现分析 - 胜率较高：
**成功因素**：入场时机把握较好，继续保持当前策略
**优化建议**：
- 可适当增加仓位，提高收益
- 保持当前的风险控制标准
- 寻找类似的高质量机会
""")
        
        # 盈亏分析
        if avg_profit < 0:
            learning_parts.append("""
### 盈亏分析 - 需要改进：
**风险提醒**：近期交易整体亏损，需要更加谨慎
**调整策略**：
- 暂时减小仓位至最小
- 重新评估止损止盈设置
- 等待更明确的交易机会
""")
        
        # 最近交易模式分析
        if recent_trades:
            successful_patterns = self._analyze_successful_patterns(recent_trades)
            if successful_patterns:
                learning_parts.append(f"""
### 成功模式识别：
{successful_patterns}
**建议**：重点寻找类似的市场环境和信号组合
""")
        
        return '\n'.join(learning_parts)
    
    def _analyze_successful_patterns(self, recent_trades: List[Dict]) -> str:
        """分析成功交易模式"""
        # 简化的模式识别
        profitable_trades = [t for t in recent_trades if t.get('profit_loss', 0) > 0]
        
        if len(profitable_trades) < 3:
            return ""
        
        # 分析成功交易的共同特征
        patterns = []
        
        # 方向分析
        buy_profits = [t for t in profitable_trades if t.get('direction') == 'BUY']
        sell_profits = [t for t in profitable_trades if t.get('direction') == 'SELL']
        
        if len(buy_profits) > len(sell_profits) * 1.5:
            patterns.append("- 近期做多交易表现更好，关注上涨机会")
        elif len(sell_profits) > len(buy_profits) * 1.5:
            patterns.append("- 近期做空交易表现更好，关注下跌机会")
        
        # 持仓时间分析
        durations = [t.get('duration', 0) for t in profitable_trades if t.get('duration')]
        if durations:
            avg_duration = sum(durations) / len(durations)
            if avg_duration < 2:
                patterns.append("- 短线快进快出策略效果较好")
            elif avg_duration > 6:
                patterns.append("- 中线持仓策略效果较好")
        
        return '\n'.join(patterns)
    
    def _generate_risk_guidance(self, context: TradingContext) -> str:
        """生成风险指导"""
        risk_parts = []
        
        # 持仓风险
        if len(context.current_positions) > 2:
            risk_parts.append("""
### 持仓风险提醒：
- 当前持仓较多，避免过度集中风险
- 新开仓位应与现有持仓方向协调
- 考虑部分获利了结，降低整体风险
""")
        
        # 市场风险
        if context.market_regime == 'UNCERTAIN':
            risk_parts.append("""
### 市场不确定性风险：
- 当前市场方向不明确，建议观望为主
- 如需交易，使用最小仓位测试
- 设置更严格的止损保护
""")
        
        return '\n'.join(risk_parts)
    
    def _assemble_prompt(self, base_template: str, market_guidance: str, 
                        learning_content: str, risk_guidance: str, 
                        analysis_data: Dict) -> str:
        """组装最终提示词"""
        
        prompt_parts = [
            base_template,
            market_guidance,
            learning_content,
            risk_guidance,
            self._get_data_section(analysis_data),
            self._get_output_format_section()
        ]
        
        return '\n\n'.join([part for part in prompt_parts if part.strip()])
    
    def _get_data_section(self, analysis_data: Dict) -> str:
        """获取数据部分"""
        return f"""
## 市场数据
- 当前价格: {analysis_data.get('current_price', 'N/A')}
- 13日均线(15分钟): {analysis_data.get('ma13_15min', 'N/A')}
- 13日均线(1小时): {analysis_data.get('ma13_1h', 'N/A')}
- RSI: {analysis_data.get('rsi', 'N/A')}
- 市场状态: {analysis_data.get('market_regime', 'N/A')}
- 趋势强度: {analysis_data.get('trend_strength', 'N/A')}
"""
    
    def _get_output_format_section(self) -> str:
        """获取输出格式部分"""
        return """
## 输出要求
请严格按照以下JSON格式输出交易决策：

```json
{
  "action": "BUY/SELL/NONE",
  "orderType": "MARKET/LIMIT/STOP",
  "entryPrice": 数值或null,
  "stopLoss": 数值,
  "takeProfit": 数值,
  "lotSize": 0.01-0.2,
  "riskLevel": "LOW/MEDIUM/HIGH",
  "reasoning": "详细分析理由",
  "confidence": 0-100,
  "ma13Strategy": {
    "timeframe15min": "UP/DOWN/FLAT",
    "timeframe1hour": "UP/DOWN/FLAT",
    "priceToMA": "ABOVE/BELOW/NEAR",
    "retracement": true/false
  },
  "signalConfidence": "HIGH/MEDIUM/LOW"
}
```
"""
    
    def _load_base_templates(self) -> Dict:
        """加载基础模板"""
        # 实际实现中从文件加载
        return {}
    
    def _load_performance_patterns(self) -> Dict:
        """加载表现模式"""
        # 实际实现中从历史数据分析
        return {}
    
    def _load_market_guidance(self) -> Dict:
        """加载市场指导"""
        # 实际实现中从配置文件加载
        return {}

# 使用示例
def generate_intelligent_prompt(market_data: Dict, trading_history: Dict) -> str:
    """生成智能提示词"""
    
    # 构建交易上下文
    context = TradingContext(
        market_regime=market_data.get('market_regime', {}).get('regime', 'UNCERTAIN'),
        volatility_level=market_data.get('volatility_regime', 'NORMAL'),
        trend_strength=market_data.get('trend_strength', {}).get('score', 0),
        time_of_day=market_data.get('time_of_day', 'UNKNOWN'),
        recent_performance=trading_history.get('recent_performance', {}),
        current_positions=market_data.get('positions', [])
    )
    
    # 生成智能提示词
    generator = IntelligentPromptGenerator()
    prompt = generator.generate_adaptive_prompt(context, market_data)
    
    return prompt
