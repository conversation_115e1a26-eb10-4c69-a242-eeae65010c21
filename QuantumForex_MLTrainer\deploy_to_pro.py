#!/usr/bin/env python3
"""
部署兼容模型到Pro系统
"""

import shutil
import os
from pathlib import Path
from datetime import datetime

def deploy_compatible_models():
    """部署兼容模型到Pro系统"""
    print("🚀 部署兼容模型到Pro系统")
    print("="*50)
    
    # 源目录和目标目录
    trainer_models_dir = Path("data/models")
    pro_models_dir = Path("../QuantumForex_Pro/data/models")
    
    # 确保Pro模型目录存在
    pro_models_dir.mkdir(parents=True, exist_ok=True)
    
    # 查找最新的兼容模型
    compatible_models = list(trainer_models_dir.glob("*compatible*.pkl"))
    
    if not compatible_models:
        print("❌ 没有找到兼容模型")
        return False
    
    print(f"📦 找到{len(compatible_models)}个兼容模型文件")
    
    # 按类型分组
    model_types = {}
    for model_file in compatible_models:
        if 'scaler' in model_file.name:
            continue
        
        for model_type in ['price_prediction', 'trend_classification', 'volatility_prediction', 'risk_assessment']:
            if model_type in model_file.name:
                if model_type not in model_types:
                    model_types[model_type] = []
                model_types[model_type].append(model_file)
    
    # 部署每种类型的最新模型
    deployed_count = 0
    
    for model_type, models in model_types.items():
        # 选择最新的模型
        latest_model = max(models, key=lambda x: x.stat().st_mtime)
        
        # 查找对应的scaler
        scaler_file = None
        scaler_name = latest_model.name.replace('_compatible_', '_compatible_scaler_')
        scaler_path = trainer_models_dir / scaler_name
        
        if scaler_path.exists():
            scaler_file = scaler_path
        
        print(f"\n🔄 部署 {model_type}:")
        print(f"   模型: {latest_model.name}")
        
        try:
            # 复制模型文件
            target_model = pro_models_dir / f"{model_type}_model.pkl"
            shutil.copy2(latest_model, target_model)
            print(f"   ✅ 模型已复制到: {target_model.name}")
            
            # 复制scaler文件
            if scaler_file:
                target_scaler = pro_models_dir / f"{model_type}_scaler.pkl"
                shutil.copy2(scaler_file, target_scaler)
                print(f"   ✅ Scaler已复制到: {target_scaler.name}")
            else:
                print(f"   ⚠️ 未找到scaler文件")
            
            deployed_count += 1
            
        except Exception as e:
            print(f"   ❌ 部署失败: {e}")
    
    print(f"\n📊 部署统计:")
    print(f"   成功部署: {deployed_count}/{len(model_types)}")
    
    if deployed_count == len(model_types):
        print("🎉 所有模型部署成功！")
        print("💡 Pro系统现在可以使用兼容模型了")
        return True
    else:
        print("⚠️ 部分模型部署失败")
        return False

if __name__ == "__main__":
    deploy_compatible_models()
