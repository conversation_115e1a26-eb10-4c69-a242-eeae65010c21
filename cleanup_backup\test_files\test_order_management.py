"""
测试订单管理功能
"""
import os
import sys
import time
from datetime import datetime

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
try:
    from app.utils.mt4_client import mt4_client
    from app.utils.performance_evaluator import get_virtual_account, update_trade_status, TradeStatus
    from app.utils.order_result_analyzer import get_order_result_statistics, update_order_results
    from app.services.forex_trading_service import execute_trade
    
    print("模块导入成功")
except Exception as e:
    print(f"模块导入失败: {e}")
    sys.exit(1)

def test_mt4_connection():
    """测试MT4连接"""
    print("\n=== 测试MT4连接 ===")
    try:
        connected = mt4_client.connect()
        print(f"MT4连接状态: {connected}")
        return connected
    except Exception as e:
        print(f"MT4连接失败: {e}")
        return False

def get_active_orders():
    """获取活跃订单"""
    print("\n=== 获取活跃订单 ===")
    try:
        response = mt4_client.get_active_orders()
        orders = response.get('orders', [])
        print(f"活跃订单数量: {len(orders)}")
        
        # 打印订单详情
        for order in orders:
            order_id = order.get('order_id')
            symbol = order.get('symbol')
            type_str = order.get('type')
            lots = order.get('lots')
            open_price = order.get('open_price')
            sl = order.get('sl')
            tp = order.get('tp')
            
            print(f"订单ID: {order_id}, 货币对: {symbol}, 类型: {type_str}, 手数: {lots}, 开仓价: {open_price}, 止损: {sl}, 止盈: {tp}")
        
        return orders
    except Exception as e:
        print(f"获取活跃订单失败: {e}")
        return []

def get_pending_orders():
    """获取挂单"""
    print("\n=== 获取挂单 ===")
    try:
        response = mt4_client.get_pending_orders()
        orders = response.get('orders', [])
        print(f"挂单数量: {len(orders)}")
        
        # 打印订单详情
        for order in orders:
            order_id = order.get('order_id')
            symbol = order.get('symbol')
            type_str = order.get('type')
            lots = order.get('lots')
            open_price = order.get('open_price')
            sl = order.get('sl')
            tp = order.get('tp')
            
            print(f"订单ID: {order_id}, 货币对: {symbol}, 类型: {type_str}, 手数: {lots}, 开仓价: {open_price}, 止损: {sl}, 止盈: {tp}")
        
        return orders
    except Exception as e:
        print(f"获取挂单失败: {e}")
        return []

def test_close_order(order_id):
    """测试平仓"""
    print(f"\n=== 测试平仓 (订单ID: {order_id}) ===")
    try:
        # 获取当前价格
        market_info_response = mt4_client.get_market_info('EURUSD')
        if market_info_response and market_info_response.get('status') == 'success':
            current_price = float(market_info_response['data']['bid'])  # 使用bid价格作为平仓价格
            print(f"当前价格: {current_price}")
        
        # 平仓
        close_result = mt4_client.close_order(order_id)
        print(f"平仓结果: {close_result}")
        
        # 如果平仓成功，更新虚拟账户
        if close_result.get('status') == 'success':
            print("平仓成功，更新虚拟账户")
            
            # 更新虚拟账户中的交易状态
            update_result = update_trade_status(
                order_id,
                TradeStatus.CLOSED,
                current_price,
                datetime.now().isoformat()
            )
            
            if update_result:
                print("虚拟账户更新成功")
                
                # 更新订单结果分析
                update_order_results()
                print("订单结果分析更新成功")
            else:
                print("虚拟账户更新失败")
        
        return close_result.get('status') == 'success'
    except Exception as e:
        print(f"平仓失败: {e}")
        return False

def test_virtual_account():
    """测试虚拟账户"""
    print("\n=== 测试虚拟账户 ===")
    try:
        account = get_virtual_account()
        print(f"虚拟账户余额: {account['current_balance']}")
        print(f"开仓交易数量: {len(account['open_trades'])}")
        print(f"平仓交易数量: {len(account['closed_trades'])}")
        
        # 打印开仓交易详情
        print("\n开仓交易详情:")
        for trade in account['open_trades']:
            print(f"交易ID: {trade['id']}, 货币对: {trade['symbol']}, 方向: {trade['direction']}, 手数: {trade['lot_size']}, 入场价: {trade['entry_price']}, 止损: {trade['stop_loss']}, 止盈: {trade['take_profit']}")
        
        # 打印平仓交易详情
        print("\n平仓交易详情:")
        for trade in account['closed_trades']:
            print(f"交易ID: {trade['id']}, 货币对: {trade['symbol']}, 方向: {trade['direction']}, 手数: {trade['lot_size']}, 入场价: {trade['entry_price']}, 出场价: {trade['exit_price']}, 盈亏: {trade['profit_loss']}")
        
        return True
    except Exception as e:
        print(f"获取虚拟账户失败: {e}")
        return False

def test_order_result_statistics():
    """测试订单结果统计"""
    print("\n=== 测试订单结果统计 ===")
    try:
        stats = get_order_result_statistics()
        print(f"总订单数: {stats['total_count']}")
        print(f"止盈触发率: {stats['take_profit_rate']:.1f}%")
        print(f"止损触发率: {stats['stop_loss_rate']:.1f}%")
        print(f"手动平仓率: {stats['manual_close_rate']:.1f}%")
        print(f"平均持仓时间: {stats['avg_duration_hours']:.1f}小时")
        print(f"平均风险回报比: {stats['avg_risk_reward_ratio']:.2f}")
        return True
    except Exception as e:
        print(f"获取订单结果统计失败: {e}")
        return False

def run_tests():
    """运行所有测试"""
    print("=== 开始测试 ===")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试MT4连接
    mt4_connected = test_mt4_connection()
    if not mt4_connected:
        print("MT4未连接，无法继续测试")
        return
    
    # 获取活跃订单
    active_orders = get_active_orders()
    
    # 获取挂单
    pending_orders = get_pending_orders()
    
    # 测试虚拟账户
    test_virtual_account()
    
    # 测试订单结果统计
    test_order_result_statistics()
    
    # 如果有活跃订单，测试平仓
    if active_orders:
        order_to_close = active_orders[0]
        order_id = order_to_close.get('order_id')
        print(f"\n选择订单ID: {order_id} 进行平仓测试")
        
        # 测试平仓
        close_success = test_close_order(order_id)
        
        # 再次测试虚拟账户
        if close_success:
            print("\n平仓后再次测试虚拟账户")
            test_virtual_account()
            
            # 再次测试订单结果统计
            print("\n平仓后再次测试订单结果统计")
            test_order_result_statistics()
    else:
        print("\n没有活跃订单，跳过平仓测试")
    
    print("\n=== 测试完成 ===")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    run_tests()
