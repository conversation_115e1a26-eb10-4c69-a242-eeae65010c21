#!/usr/bin/env python3
"""
调试订单ID匹配问题
检查为什么CLOSE_PARTIAL操作无法执行
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def debug_order_id_matching():
    """调试订单ID匹配问题"""
    print("🔍 调试订单ID匹配问题")
    print("="*50)
    
    try:
        # 1. 检查本地活跃订单记录
        print("📊 检查本地活跃订单记录...")
        
        from core.execution_engine.trade_executor import TradeExecutor
        from core.mt4_integration.mt4_client import MT4Client
        
        # 创建MT4客户端和交易执行器
        mt4_client = MT4Client()
        trade_executor = TradeExecutor(mt4_client)
        
        # 检查本地活跃订单
        print(f"📋 本地活跃订单数量: {len(trade_executor.active_orders)}")
        
        if trade_executor.active_orders:
            print("📋 本地活跃订单详情:")
            for order_id, order_info in trade_executor.active_orders.items():
                print(f"   订单ID: {order_id}")
                print(f"   订单信息: {order_info}")
                print(f"   货币对: {order_info.get('symbol', 'N/A')}")
                print(f"   方向: {order_info.get('direction', 'N/A')}")
                print(f"   手数: {order_info.get('volume', 'N/A')}")
                print("-" * 30)
        else:
            print("⚠️ 本地没有活跃订单记录")
        
        # 2. 检查MT4实际订单
        print("\n📊 检查MT4实际订单...")
        
        if mt4_client.connect():
            print("✅ MT4连接成功")
            
            # 获取MT4当前持仓
            positions = mt4_client.get_positions()
            print(f"📋 MT4当前持仓数量: {len(positions) if positions else 0}")
            
            if positions:
                print("📋 MT4持仓详情:")
                gbpusd_positions = []
                
                for pos in positions:
                    print(f"   订单号: {pos.get('ticket', pos.get('order_id', 'N/A'))}")
                    print(f"   货币对: {pos.get('symbol', 'N/A')}")
                    print(f"   方向: {pos.get('type', pos.get('direction', 'N/A'))}")
                    print(f"   手数: {pos.get('volume', pos.get('size', 'N/A'))}")
                    print(f"   利润: {pos.get('profit', 'N/A')}")
                    
                    # 收集GBPUSD持仓
                    if pos.get('symbol') == 'GBPUSD':
                        gbpusd_positions.append(pos)
                    
                    print("-" * 30)
                
                # 3. 专门检查GBPUSD订单
                print(f"\n🎯 GBPUSD持仓分析:")
                print(f"   GBPUSD持仓数量: {len(gbpusd_positions)}")
                
                if gbpusd_positions:
                    print("   GBPUSD持仓详情:")
                    for i, pos in enumerate(gbpusd_positions):
                        order_id = pos.get('ticket', pos.get('order_id', ''))
                        print(f"   [{i+1}] 订单ID: {order_id}")
                        print(f"       类型: {pos.get('type', pos.get('direction', 'N/A'))}")
                        print(f"       手数: {pos.get('volume', pos.get('size', 'N/A'))}")
                        print(f"       利润: {pos.get('profit', 'N/A')} USD")
                        print(f"       开仓价: {pos.get('open_price', 'N/A')}")
                        print(f"       当前价: {pos.get('current_price', 'N/A')}")
                else:
                    print("   ❌ 没有找到GBPUSD持仓")
                    print("   💡 这可能是CLOSE_PARTIAL失败的原因！")
            else:
                print("⚠️ MT4没有当前持仓")
        else:
            print("❌ MT4连接失败")
        
        # 4. 检查订单ID格式匹配
        print(f"\n🔍 订单ID格式匹配分析:")
        
        # 模拟检查订单ID匹配逻辑
        if trade_executor.active_orders and positions:
            print("📋 本地订单ID vs MT4订单ID:")
            
            local_order_ids = set(trade_executor.active_orders.keys())
            mt4_order_ids = set()
            
            for pos in positions:
                order_id = str(pos.get('ticket', pos.get('order_id', '')))
                if order_id:
                    mt4_order_ids.add(order_id)
            
            print(f"   本地订单ID: {local_order_ids}")
            print(f"   MT4订单ID: {mt4_order_ids}")
            
            # 检查匹配情况
            matched_ids = local_order_ids.intersection(mt4_order_ids)
            unmatched_local = local_order_ids - mt4_order_ids
            unmatched_mt4 = mt4_order_ids - local_order_ids
            
            print(f"   匹配的ID: {matched_ids}")
            print(f"   本地独有: {unmatched_local}")
            print(f"   MT4独有: {unmatched_mt4}")
            
            if unmatched_local:
                print("⚠️ 发现本地记录与MT4不同步的订单！")
            if unmatched_mt4:
                print("⚠️ 发现MT4有本地未记录的订单！")
        
        # 5. 测试CLOSE_PARTIAL操作
        print(f"\n🧪 测试CLOSE_PARTIAL操作:")
        
        if gbpusd_positions:
            test_order_id = gbpusd_positions[0].get('ticket', gbpusd_positions[0].get('order_id', ''))
            print(f"   测试订单ID: {test_order_id}")
            
            # 模拟CLOSE_PARTIAL决策
            test_decision = {
                'symbol': 'GBPUSD',
                'action': 'CLOSE_PARTIAL',
                'volume': 0.01,
                'reasoning': '测试部分平仓',
                'confidence': 80.0
            }
            
            print(f"   测试决策: {test_decision}")
            
            # 检查是否能找到对应订单
            symbol_positions = [pos for pos in positions if pos.get('symbol') == 'GBPUSD']
            print(f"   找到GBPUSD持仓: {len(symbol_positions)}个")
            
            if symbol_positions:
                # 按盈亏排序
                sorted_positions = sorted(symbol_positions, key=lambda x: x.get('profit', 0))
                print(f"   按盈亏排序后:")
                for i, pos in enumerate(sorted_positions):
                    order_id = pos.get('ticket', pos.get('order_id', ''))
                    profit = pos.get('profit', 0)
                    volume = pos.get('volume', pos.get('size', 0))
                    print(f"     [{i+1}] ID:{order_id}, 利润:{profit}, 手数:{volume}")
                
                # 选择要平仓的订单
                target_pos = sorted_positions[0]
                target_order_id = str(target_pos.get('ticket', target_pos.get('order_id', '')))
                
                print(f"   选择平仓订单: {target_order_id}")
                print(f"   ✅ CLOSE_PARTIAL操作应该可以执行")
            else:
                print(f"   ❌ 无法找到GBPUSD持仓进行CLOSE_PARTIAL操作")
        else:
            print(f"   ❌ 没有GBPUSD持仓，无法执行CLOSE_PARTIAL")
        
        # 6. 提供修复建议
        print(f"\n💡 修复建议:")
        
        if not gbpusd_positions:
            print("   1. GBPUSD没有持仓，CLOSE_PARTIAL操作无法执行")
            print("   2. 检查是否有GBPUSD持仓被手动关闭")
            print("   3. 更新本地持仓记录与MT4同步")
        elif trade_executor.active_orders and not matched_ids:
            print("   1. 本地订单记录与MT4不同步")
            print("   2. 需要重新同步订单状态")
            print("   3. 清理过期的本地订单记录")
        else:
            print("   1. 订单ID匹配正常")
            print("   2. 检查CLOSE_PARTIAL执行逻辑")
            print("   3. 检查MT4连接状态")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_order_id_matching()
