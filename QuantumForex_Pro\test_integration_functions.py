#!/usr/bin/env python3
"""
QuantumForex Pro - 第二轮集成功能测试
测试系统各组件之间的集成和协作功能
"""

import sys
import os
import time
import threading
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_trading_workflow():
    """测试完整的交易工作流程"""
    print("🔍 开始完整交易工作流程测试...")
    print("=" * 50)

    try:
        from main import QuantumForexPro

        # 创建系统实例
        system = QuantumForexPro()
        print("✅ 系统实例创建成功")

        # 执行完整的分析周期
        print("\n🔄 执行完整分析周期...")
        success = system._execute_analysis_cycle()

        if success:
            print("✅ 完整分析周期执行成功")

            # 检查各个步骤的结果
            stats = system.system_stats
            print(f"   成功分析: {stats.get('successful_analyses', 0)}")
            print(f"   总信号数: {stats.get('total_signals', 0)}")

            # 检查MT4持仓状态
            mt4_positions = system.trade_executor._get_mt4_real_positions()
            print(f"   当前MT4持仓: {len(mt4_positions)}个")

            if mt4_positions:
                total_profit = sum(pos.get('profit', 0) for pos in mt4_positions)
                print(f"   总盈亏: ${total_profit:.2f}")
        else:
            print("❌ 完整分析周期执行失败")
            return False

        print("=" * 50)
        print("✅ 完整交易工作流程测试完成!")
        return True

    except Exception as e:
        print(f"❌ 完整交易工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_position_management_integration():
    """测试持仓管理集成功能"""
    print("\n🔍 开始持仓管理集成测试...")
    print("=" * 50)

    try:
        from main import QuantumForexPro

        # 创建系统实例
        system = QuantumForexPro()

        # 获取当前持仓
        mt4_positions = system.trade_executor._get_mt4_real_positions()
        print(f"✅ 当前MT4持仓: {len(mt4_positions)}个")

        if mt4_positions:
            # 执行持仓分析
            print("\n🔍 执行持仓分析...")
            analyses = system.position_manager.analyze_all_positions()
            print(f"✅ 持仓分析完成，生成{len(analyses)}个分析结果")

            # 显示分析结果
            for i, analysis in enumerate(analyses[:3], 1):
                print(f"   {i}. {analysis.symbol} {analysis.order_id}: {analysis.action.value}")
                print(f"      原因: {analysis.reason}")
                print(f"      优先级: {analysis.priority}")

            # 执行高优先级动作
            high_priority_actions = [a for a in analyses if a.priority >= 4]
            if high_priority_actions:
                print(f"\n⚡ 执行{len(high_priority_actions)}个高优先级动作...")
                results = system.position_manager.execute_position_actions(high_priority_actions)
                print(f"   执行结果: {results['executed']}成功, {results['failed']}失败")
            else:
                print("\n📊 无高优先级动作需要执行")
        else:
            print("📊 当前无持仓，跳过持仓管理测试")

        print("=" * 50)
        print("✅ 持仓管理集成测试完成!")
        return True

    except Exception as e:
        print(f"❌ 持仓管理集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_coordinator_integration():
    """测试系统协调器集成功能"""
    print("\n🔍 开始系统协调器集成测试...")
    print("=" * 50)

    try:
        from main import QuantumForexPro
        from core.system_coordinator import SystemPriority

        # 创建系统实例
        system = QuantumForexPro()

        # 测试任务提交和协调
        print("📋 测试任务提交和协调...")

        # 提交多个不同优先级的任务
        tasks = [
            ('risk_management', 'assess_risk', SystemPriority.EMERGENCY),
            ('position_manager', 'analyze_positions', SystemPriority.HIGH),
            ('trading_system', 'execute_trade', SystemPriority.MEDIUM),
            ('llm_analyzer', 'analyze', SystemPriority.LOW),
            ('parameter_optimizer', 'optimize', SystemPriority.BACKGROUND)
        ]

        submitted_tasks = []
        for system_name, action, priority in tasks:
            task_id = system.system_coordinator.submit_task(
                system_name=system_name,
                action=action,
                data={'test': 'data'},
                priority=priority
            )
            if task_id:
                submitted_tasks.append((task_id, system_name, action, priority))
                print(f"   ✅ 任务提交成功: {system_name}.{action} (优先级: {priority.name})")
            else:
                print(f"   ⚠️ 任务被拒绝: {system_name}.{action}")

        # 检查系统状态
        status = system.system_coordinator.get_system_status()
        print(f"\n📊 协调器状态:")
        print(f"   当前状态: {status['current_state']}")
        print(f"   活跃任务: {status['active_tasks']}")
        print(f"   队列大小: {status['queue_size']}")

        # 等待任务处理
        print("\n⏳ 等待任务处理...")
        time.sleep(3)

        # 再次检查状态
        status = system.system_coordinator.get_system_status()
        print(f"📊 处理后状态:")
        print(f"   活跃任务: {status['active_tasks']}")
        print(f"   队列大小: {status['queue_size']}")

        print("=" * 50)
        print("✅ 系统协调器集成测试完成!")
        return True

    except Exception as e:
        print(f"❌ 系统协调器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mt4_integration_comprehensive():
    """测试MT4集成的综合功能"""
    print("\n🔍 开始MT4集成综合测试...")
    print("=" * 50)

    try:
        from main import QuantumForexPro

        # 创建系统实例
        system = QuantumForexPro()

        # 测试MT4连接状态
        print("🔗 测试MT4连接状态...")
        mt4_client = system.trade_executor.mt4_client
        if mt4_client.is_connected:  # 这是一个属性，不是方法
            print("✅ MT4连接正常")
        else:
            print("⚠️ MT4连接异常，尝试重连...")
            connected = mt4_client.connect()
            if connected:
                print("✅ MT4重连成功")
            else:
                print("❌ MT4重连失败")
                return False

        # 测试市场信息获取
        print("\n📊 测试市场信息获取...")
        symbols = ['EURUSD', 'GBPUSD', 'AUDUSD']
        for symbol in symbols:
            market_info = mt4_client.get_market_info(symbol)
            if market_info and market_info.get('status') == 'success':
                data = market_info.get('data', {})
                print(f"   ✅ {symbol}: Bid={data.get('bid')}, Ask={data.get('ask')}")
            else:
                print(f"   ❌ {symbol}: 获取失败")

        # 测试持仓同步
        print("\n🔄 测试持仓同步...")
        mt4_positions = system.trade_executor._get_mt4_real_positions()
        print(f"✅ 同步到{len(mt4_positions)}个MT4持仓")

        if mt4_positions:
            # 显示持仓详情
            total_profit = 0
            for pos in mt4_positions:
                profit = pos.get('profit', 0)
                total_profit += profit
                print(f"   📋 {pos.get('symbol')} {pos.get('type')} {pos.get('volume')}手")
                print(f"      盈亏: ${profit:.2f}")

            print(f"   💰 总盈亏: ${total_profit:.2f}")

        # 测试MT4监控状态
        print("\n🔍 测试MT4监控状态...")
        mt4_status = system.mt4_monitor.get_monitoring_status()
        print(f"✅ MT4监控状态:")
        print(f"   监控运行: {'是' if mt4_status['monitoring'] else '否'}")
        print(f"   运行时间: {mt4_status['uptime']}")
        print(f"   健康评分: {mt4_status['system_health']:.1%}")

        print("=" * 50)
        print("✅ MT4集成综合测试完成!")
        return True

    except Exception as e:
        print(f"❌ MT4集成综合测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_learning_system_integration():
    """测试学习系统集成功能"""
    print("\n🔍 开始学习系统集成测试...")
    print("=" * 50)

    try:
        from main import QuantumForexPro

        # 创建系统实例
        system = QuantumForexPro()

        # 测试学习系统状态
        print("📚 测试学习系统状态...")
        learning_coordinator = system.learning_coordinator

        # 检查学习系统是否运行
        if hasattr(learning_coordinator, 'is_running'):
            print(f"✅ 学习系统运行状态: {'运行中' if learning_coordinator.is_running else '已停止'}")

        # 测试交易结果记录
        print("\n📝 测试交易结果记录...")

        # 模拟一个交易结果
        trade_result = {
            'symbol': 'EURUSD',
            'action': 'BUY',
            'volume': 0.01,
            'entry_price': 1.1350,
            'exit_price': 1.1360,
            'profit': 10.0,
            'entry_time': datetime.now() - timedelta(hours=1),
            'exit_time': datetime.now(),
            'strategy': 'test_strategy'
        }

        # 记录交易结果
        try:
            learning_coordinator.record_trade_result(trade_result)
            print("✅ 交易结果记录成功")
        except Exception as e:
            print(f"⚠️ 交易结果记录失败: {e}")

        # 测试参数优化
        print("\n🔧 测试参数优化...")
        try:
            # 获取当前参数
            current_params = learning_coordinator.get_current_parameters()
            print(f"✅ 获取当前参数成功: {len(current_params)}个参数")

            # 显示部分参数
            for key, value in list(current_params.items())[:3]:
                print(f"   {key}: {value}")
        except Exception as e:
            print(f"⚠️ 参数获取失败: {e}")

        print("=" * 50)
        print("✅ 学习系统集成测试完成!")
        return True

    except Exception as e:
        print(f"❌ 学习系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 QuantumForex Pro - 第二轮集成功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 执行所有集成测试
    tests = [
        ("完整交易工作流程", test_complete_trading_workflow),
        ("持仓管理集成", test_position_management_integration),
        ("系统协调器集成", test_system_coordinator_integration),
        ("MT4集成综合", test_mt4_integration_comprehensive),
        ("学习系统集成", test_learning_system_integration)
    ]

    results = {}
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"🔄 开始执行: {test_name}")
            print(f"{'='*60}")

            results[test_name] = test_func()

            if results[test_name]:
                print(f"✅ {test_name} - 测试通过")
            else:
                print(f"❌ {test_name} - 测试失败")

        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False

    print("\n" + "=" * 60)
    print("📊 第二轮集成功能测试结果汇总:")
    print("=" * 60)

    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False

    print("=" * 60)
    if all_passed:
        print("🎉 所有集成功能测试通过! 系统集成正常!")
        print("🚀 准备进入第三轮: 压力和稳定性测试")
        sys.exit(0)
    else:
        print("❌ 部分集成功能测试失败，请检查相关组件")
        sys.exit(1)
