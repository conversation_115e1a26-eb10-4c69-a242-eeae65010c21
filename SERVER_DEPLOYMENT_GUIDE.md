# 外汇交易系统服务器部署指南

## 🚀 快速部署

### **1. 上传项目文件**
将整个项目文件夹上传到服务器，确保包含以下关键文件：
- `run.py` (主启动文件)
- `requirements.txt` (依赖列表)
- `deploy_server.bat` (部署脚本)
- `start_server.bat` (启动脚本)
- `app/` (应用代码)
- `.env.local` (配置文件)

### **2. 运行部署脚本**
```cmd
deploy_server.bat
```

### **3. 启动系统**
```cmd
start_server.bat
```

## 🔧 问题解决

### **编码错误 (UnicodeDecodeError)**
**症状**: `'gbk' codec can't decode byte 0xb5`

**解决方案**:
1. 确保控制台编码设置为UTF-8
2. 已在启动脚本中添加 `chcp 65001`
3. 已在Python代码中设置编码处理

### **缺失依赖 (ModuleNotFoundError)**
**症状**: `No module named 'dotenv'`

**解决方案**:
1. 运行部署脚本会自动安装所有依赖
2. 如果失败，手动执行：
```cmd
call venv\Scripts\activate.bat
pip install -r requirements.txt
```

### **pip版本过旧**
**症状**: 提示升级pip版本

**解决方案**:
```cmd
python -m pip install --upgrade pip
```

## 📋 手动部署步骤

如果自动部署脚本失败，可以手动执行以下步骤：

### **1. 检查Python环境**
```cmd
python --version
```
确保Python 3.9+已安装

### **2. 创建虚拟环境**
```cmd
python -m venv venv
```

### **3. 激活虚拟环境**
```cmd
call venv\Scripts\activate.bat
```

### **4. 升级pip**
```cmd
python -m pip install --upgrade pip
```

### **5. 安装依赖**
```cmd
pip install -r requirements.txt
```

### **6. 验证关键依赖**
```cmd
python -c "import dotenv; print('✓ python-dotenv')"
python -c "import flask; print('✓ flask')"
python -c "import pandas; print('✓ pandas')"
```

### **7. 启动系统**
```cmd
python run.py
```

## 🌐 访问系统

启动成功后，系统将在以下地址运行：
- **API服务器**: http://localhost:5000
- **Web界面**: http://localhost:5000

## 📝 配置文件

确保 `.env.local` 文件包含必要的配置：
```env
# LLM API配置
LLM_API_KEY=your_api_key_here
LLM_API_URL=https://api.siliconflow.cn/v1/chat/completions

# MT4配置
MT4_SERVER_HOST=127.0.0.1
MT4_SERVER_PORT=5555

# 数据库配置
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=pizza_quotes
```

## 🔍 故障排除

### **常见错误及解决方案**

1. **端口占用**
   - 修改 `.env.local` 中的 `PORT` 设置
   - 或终止占用端口的进程

2. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证连接配置是否正确

3. **MT4连接失败**
   - 确保MT4服务器运行在指定端口
   - 检查防火墙设置

4. **LLM API调用失败**
   - 验证API密钥是否正确
   - 检查网络连接

## 📞 技术支持

如果遇到部署问题，请检查：
1. 系统日志文件 `forex_system.log`
2. 错误日志文件 `app/data/errors/`
3. 控制台输出信息

## 🔄 更新部署

更新系统时：
1. 停止当前运行的系统 (Ctrl+C)
2. 上传新的项目文件
3. 重新运行 `start_server.bat`

系统会自动检查并更新依赖包。
