#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实用交易系统优化方案
基于现有项目框架的实际可行优化
目标：在现有基础上实现稳定正收益
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class RiskLevel(Enum):
    """风险等级"""
    EMERGENCY = "紧急"
    HIGH = "高风险"
    MEDIUM = "中风险"
    LOW = "低风险"
    SAFE = "安全"

class MarketCondition(Enum):
    """市场状态"""
    TRENDING_UP = "上升趋势"
    TRENDING_DOWN = "下降趋势"
    RANGING = "震荡整理"
    VOLATILE = "高波动"
    CALM = "平静"

@dataclass
class RiskMetrics:
    """风险指标"""
    current_drawdown: float
    daily_loss: float
    position_risk: float
    portfolio_risk: float
    risk_level: RiskLevel

@dataclass
class SignalQuality:
    """信号质量"""
    technical_score: float      # 技术指标评分
    llm_confidence: float       # LLM置信度
    market_condition: MarketCondition
    signal_strength: float      # 综合信号强度
    quality_grade: str          # A/B/C/D等级

class EnhancedRiskManager:
    """增强风险管理器"""
    
    def __init__(self):
        # 风险参数
        self.risk_limits = {
            'emergency_stop': 0.10,      # 紧急止损10%
            'max_daily_loss': 0.05,      # 最大日亏损5%
            'max_position_risk': 0.02,   # 最大单笔风险2%
            'max_portfolio_risk': 0.08,  # 最大组合风险8%
            'max_consecutive_losses': 5  # 最大连续亏损次数
        }
        
        # 状态跟踪
        self.daily_pnl = 0.0
        self.max_drawdown = 0.0
        self.consecutive_losses = 0
        self.last_reset_date = datetime.now().date()
        
    def assess_risk(self, account_info: Dict, positions: List[Dict]) -> RiskMetrics:
        """评估当前风险状况"""
        
        # 重置日统计
        self._reset_daily_stats_if_needed()
        
        # 计算当前回撤
        account_balance = account_info.get('balance', 10000)
        account_equity = account_info.get('equity', account_balance)
        current_drawdown = (account_balance - account_equity) / account_balance
        
        # 计算持仓风险
        position_risk = self._calculate_position_risk(positions, account_balance)
        
        # 计算组合风险
        portfolio_risk = self._calculate_portfolio_risk(positions, account_balance)
        
        # 确定风险等级
        risk_level = self._determine_risk_level(current_drawdown, self.daily_pnl, position_risk)
        
        return RiskMetrics(
            current_drawdown=current_drawdown,
            daily_loss=abs(self.daily_pnl) if self.daily_pnl < 0 else 0,
            position_risk=position_risk,
            portfolio_risk=portfolio_risk,
            risk_level=risk_level
        )
    
    def should_allow_trading(self, risk_metrics: RiskMetrics) -> Tuple[bool, str]:
        """判断是否允许交易"""
        
        # 紧急情况：立即停止所有交易
        if risk_metrics.risk_level == RiskLevel.EMERGENCY:
            return False, "紧急风险：账户亏损过大，停止交易"
        
        # 高风险：限制新开仓
        if risk_metrics.risk_level == RiskLevel.HIGH:
            return False, "高风险状态：暂停新开仓，等待风险降低"
        
        # 日亏损限制
        if risk_metrics.daily_loss >= self.risk_limits['max_daily_loss']:
            return False, f"日亏损达到限制{self.risk_limits['max_daily_loss']:.1%}"
        
        # 连续亏损限制
        if self.consecutive_losses >= self.risk_limits['max_consecutive_losses']:
            return False, f"连续亏损{self.consecutive_losses}次，暂停交易"
        
        return True, "风险可控，允许交易"
    
    def calculate_safe_position_size(self, signal_quality: SignalQuality, 
                                   risk_metrics: RiskMetrics, 
                                   account_balance: float) -> float:
        """计算安全仓位大小"""
        
        # 基础仓位
        base_size = self.risk_limits['max_position_risk']
        
        # 根据信号质量调整
        quality_multiplier = {
            'A': 1.0,
            'B': 0.8,
            'C': 0.6,
            'D': 0.3
        }.get(signal_quality.quality_grade, 0.3)
        
        # 根据风险状态调整
        risk_multiplier = {
            RiskLevel.SAFE: 1.0,
            RiskLevel.LOW: 0.8,
            RiskLevel.MEDIUM: 0.6,
            RiskLevel.HIGH: 0.3,
            RiskLevel.EMERGENCY: 0.0
        }.get(risk_metrics.risk_level, 0.3)
        
        # 根据市场状态调整
        market_multiplier = {
            MarketCondition.TRENDING_UP: 1.0,
            MarketCondition.TRENDING_DOWN: 1.0,
            MarketCondition.RANGING: 0.7,
            MarketCondition.VOLATILE: 0.5,
            MarketCondition.CALM: 0.8
        }.get(signal_quality.market_condition, 0.5)
        
        # 计算最终仓位
        final_size = base_size * quality_multiplier * risk_multiplier * market_multiplier
        
        # 确保不超过限制
        return max(min(final_size, self.risk_limits['max_position_risk']), 0.005)
    
    def _calculate_position_risk(self, positions: List[Dict], account_balance: float) -> float:
        """计算持仓风险"""
        total_risk = 0.0
        
        for position in positions:
            entry_price = position.get('entry_price', 0)
            stop_loss = position.get('stop_loss', 0)
            lot_size = position.get('lot_size', 0)
            
            if entry_price > 0 and stop_loss > 0:
                risk_per_lot = abs(entry_price - stop_loss) * 100000  # 假设是标准手
                position_risk = (risk_per_lot * lot_size) / account_balance
                total_risk += position_risk
        
        return total_risk
    
    def _calculate_portfolio_risk(self, positions: List[Dict], account_balance: float) -> float:
        """计算组合风险"""
        # 简化计算：假设所有持仓风险相关性为0.5
        individual_risks = []
        
        for position in positions:
            entry_price = position.get('entry_price', 0)
            stop_loss = position.get('stop_loss', 0)
            lot_size = position.get('lot_size', 0)
            
            if entry_price > 0 and stop_loss > 0:
                risk_per_lot = abs(entry_price - stop_loss) * 100000
                position_risk = (risk_per_lot * lot_size) / account_balance
                individual_risks.append(position_risk)
        
        if not individual_risks:
            return 0.0
        
        # 考虑相关性的组合风险
        total_individual_risk = sum(individual_risks)
        correlation_adjustment = 0.7  # 假设70%相关性
        portfolio_risk = total_individual_risk * correlation_adjustment
        
        return portfolio_risk
    
    def _determine_risk_level(self, drawdown: float, daily_pnl: float, position_risk: float) -> RiskLevel:
        """确定风险等级"""
        
        # 紧急情况
        if (drawdown >= self.risk_limits['emergency_stop'] or 
            abs(daily_pnl) >= self.risk_limits['emergency_stop']):
            return RiskLevel.EMERGENCY
        
        # 高风险
        if (drawdown >= 0.06 or 
            abs(daily_pnl) >= 0.04 or 
            position_risk >= 0.06):
            return RiskLevel.HIGH
        
        # 中风险
        if (drawdown >= 0.03 or 
            abs(daily_pnl) >= 0.02 or 
            position_risk >= 0.04):
            return RiskLevel.MEDIUM
        
        # 低风险
        if (drawdown >= 0.01 or 
            abs(daily_pnl) >= 0.01 or 
            position_risk >= 0.02):
            return RiskLevel.LOW
        
        return RiskLevel.SAFE
    
    def _reset_daily_stats_if_needed(self):
        """重置日统计（如果需要）"""
        current_date = datetime.now().date()
        if current_date != self.last_reset_date:
            self.daily_pnl = 0.0
            self.last_reset_date = current_date

class SignalQualityAnalyzer:
    """信号质量分析器"""
    
    def __init__(self):
        self.technical_weights = {
            'trend_alignment': 0.3,
            'momentum_strength': 0.25,
            'support_resistance': 0.2,
            'volume_confirmation': 0.15,
            'volatility_level': 0.1
        }
    
    def analyze_signal_quality(self, market_data: Dict, llm_analysis: Dict) -> SignalQuality:
        """分析信号质量"""
        
        # 1. 技术指标评分
        technical_score = self._calculate_technical_score(market_data)
        
        # 2. LLM置信度
        llm_confidence = self._extract_llm_confidence(llm_analysis)
        
        # 3. 市场状态识别
        market_condition = self._identify_market_condition(market_data)
        
        # 4. 综合信号强度
        signal_strength = (technical_score * 0.6 + llm_confidence * 0.4)
        
        # 5. 质量等级
        quality_grade = self._determine_quality_grade(signal_strength, market_condition)
        
        return SignalQuality(
            technical_score=technical_score,
            llm_confidence=llm_confidence,
            market_condition=market_condition,
            signal_strength=signal_strength,
            quality_grade=quality_grade
        )
    
    def _calculate_technical_score(self, market_data: Dict) -> float:
        """计算技术指标评分"""
        score = 0.0
        
        # 趋势一致性
        ma_20 = market_data.get('ma_20', 0)
        ma_50 = market_data.get('ma_50', 0)
        current_price = market_data.get('current_price', 0)
        
        if current_price > ma_20 > ma_50:
            score += self.technical_weights['trend_alignment']
        elif current_price < ma_20 < ma_50:
            score += self.technical_weights['trend_alignment']
        else:
            score += self.technical_weights['trend_alignment'] * 0.3
        
        # 动量强度
        rsi = market_data.get('rsi', 50)
        if 30 < rsi < 70:
            score += self.technical_weights['momentum_strength']
        elif 20 < rsi < 80:
            score += self.technical_weights['momentum_strength'] * 0.7
        else:
            score += self.technical_weights['momentum_strength'] * 0.3
        
        # 支撑阻力
        # 简化：如果价格在关键位附近，给予评分
        score += self.technical_weights['support_resistance'] * 0.7
        
        # 成交量确认（简化）
        score += self.technical_weights['volume_confirmation'] * 0.6
        
        # 波动率水平
        atr = market_data.get('atr', 0.001)
        if 0.0008 < atr < 0.002:  # 正常波动率
            score += self.technical_weights['volatility_level']
        else:
            score += self.technical_weights['volatility_level'] * 0.5
        
        return min(score, 1.0)
    
    def _extract_llm_confidence(self, llm_analysis: Dict) -> float:
        """提取LLM置信度"""
        # 从LLM分析中提取置信度
        confidence_keywords = ['高度确信', '确信', '可能', '不确定', '谨慎']
        reasoning = llm_analysis.get('reasoning', '').lower()
        
        if '高度确信' in reasoning:
            return 0.9
        elif '确信' in reasoning:
            return 0.8
        elif '可能' in reasoning:
            return 0.6
        elif '不确定' in reasoning or '谨慎' in reasoning:
            return 0.4
        else:
            return 0.5  # 默认中等置信度
    
    def _identify_market_condition(self, market_data: Dict) -> MarketCondition:
        """识别市场状态"""
        current_price = market_data.get('current_price', 0)
        ma_20 = market_data.get('ma_20', current_price)
        ma_50 = market_data.get('ma_50', current_price)
        atr = market_data.get('atr', 0.001)
        rsi = market_data.get('rsi', 50)
        
        # 高波动判断
        if atr > 0.0025:
            return MarketCondition.VOLATILE
        
        # 趋势判断
        if current_price > ma_20 > ma_50 and rsi > 55:
            return MarketCondition.TRENDING_UP
        elif current_price < ma_20 < ma_50 and rsi < 45:
            return MarketCondition.TRENDING_DOWN
        
        # 震荡判断
        if abs(ma_20 - ma_50) / current_price < 0.002:
            return MarketCondition.RANGING
        
        return MarketCondition.CALM
    
    def _determine_quality_grade(self, signal_strength: float, market_condition: MarketCondition) -> str:
        """确定质量等级"""
        
        # 基础等级
        if signal_strength >= 0.8:
            base_grade = 'A'
        elif signal_strength >= 0.65:
            base_grade = 'B'
        elif signal_strength >= 0.5:
            base_grade = 'C'
        else:
            base_grade = 'D'
        
        # 市场状态调整
        if market_condition in [MarketCondition.VOLATILE]:
            # 高波动市场降级
            grade_map = {'A': 'B', 'B': 'C', 'C': 'D', 'D': 'D'}
            base_grade = grade_map[base_grade]
        elif market_condition in [MarketCondition.TRENDING_UP, MarketCondition.TRENDING_DOWN]:
            # 趋势市场保持等级
            pass
        
        return base_grade

class PracticalTradingSystem:
    """实用交易系统"""
    
    def __init__(self):
        self.risk_manager = EnhancedRiskManager()
        self.signal_analyzer = SignalQualityAnalyzer()
        
        # 系统状态
        self.system_enabled = True
        self.last_trade_time = None
        self.min_trade_interval = 300  # 最小交易间隔5分钟
        
    def process_trading_opportunity(self, market_data: Dict, llm_analysis: Dict, 
                                  account_info: Dict, current_positions: List[Dict]) -> Dict:
        """处理交易机会"""
        
        # 1. 风险评估
        risk_metrics = self.risk_manager.assess_risk(account_info, current_positions)
        
        # 2. 检查是否允许交易
        can_trade, risk_reason = self.risk_manager.should_allow_trading(risk_metrics)
        
        if not can_trade:
            return {
                'action': 'NONE',
                'reason': risk_reason,
                'risk_level': risk_metrics.risk_level.value,
                'system_status': 'RISK_PROTECTION'
            }
        
        # 3. 分析信号质量
        signal_quality = self.signal_analyzer.analyze_signal_quality(market_data, llm_analysis)
        
        # 4. 检查信号质量是否足够
        if signal_quality.quality_grade in ['D']:
            return {
                'action': 'NONE',
                'reason': f'信号质量不足：{signal_quality.quality_grade}级',
                'signal_strength': signal_quality.signal_strength,
                'system_status': 'SIGNAL_QUALITY_INSUFFICIENT'
            }
        
        # 5. 检查交易间隔
        if self._check_trade_interval():
            return {
                'action': 'NONE',
                'reason': '交易间隔不足，等待下次机会',
                'system_status': 'WAITING_INTERVAL'
            }
        
        # 6. 计算安全仓位
        account_balance = account_info.get('balance', 10000)
        safe_position_size = self.risk_manager.calculate_safe_position_size(
            signal_quality, risk_metrics, account_balance
        )
        
        # 7. 生成交易决策
        llm_action = llm_analysis.get('action', 'NONE')
        
        if llm_action in ['BUY', 'SELL'] and signal_quality.quality_grade in ['A', 'B']:
            # 更新最后交易时间
            self.last_trade_time = datetime.now()
            
            return {
                'action': llm_action,
                'orderType': 'MARKET',
                'entryPrice': market_data.get('current_price'),
                'stopLoss': llm_analysis.get('stopLoss'),
                'takeProfit': llm_analysis.get('takeProfit'),
                'lotSize': safe_position_size,
                'riskLevel': risk_metrics.risk_level.value,
                'signalQuality': signal_quality.quality_grade,
                'reasoning': f"[{signal_quality.quality_grade}级信号] {llm_analysis.get('reasoning', '')}",
                'system_status': 'TRADE_EXECUTED',
                'risk_metrics': {
                    'position_risk': safe_position_size,
                    'portfolio_risk': risk_metrics.portfolio_risk,
                    'daily_loss': risk_metrics.daily_loss
                }
            }
        else:
            return {
                'action': 'NONE',
                'reason': f'LLM建议{llm_action}，但信号质量{signal_quality.quality_grade}级不足',
                'signal_quality': signal_quality.quality_grade,
                'system_status': 'SIGNAL_FILTERED'
            }
    
    def _check_trade_interval(self) -> bool:
        """检查交易间隔"""
        if self.last_trade_time is None:
            return False
        
        time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
        return time_since_last < self.min_trade_interval
    
    def emergency_close_all_positions(self, positions: List[Dict]) -> List[Dict]:
        """紧急平仓所有持仓"""
        close_results = []
        
        for position in positions:
            # 这里应该调用实际的MT4平仓函数
            close_result = {
                'position_id': position.get('id'),
                'action': 'EMERGENCY_CLOSE',
                'timestamp': datetime.now().isoformat(),
                'reason': '紧急风险控制'
            }
            close_results.append(close_result)
        
        return close_results

# 测试函数
def test_practical_system():
    """测试实用交易系统"""
    print("🚀 实用交易系统测试")
    print("=" * 60)
    
    # 创建系统
    system = PracticalTradingSystem()
    
    # 模拟数据
    market_data = {
        'current_price': 1.1300,
        'ma_20': 1.1290,
        'ma_50': 1.1280,
        'rsi': 65,
        'atr': 0.0015
    }
    
    llm_analysis = {
        'action': 'BUY',
        'stopLoss': 1.1280,
        'takeProfit': 1.1340,
        'reasoning': '技术指标显示上升趋势，确信看多'
    }
    
    account_info = {
        'balance': 10000,
        'equity': 9950
    }
    
    current_positions = []
    
    # 处理交易机会
    result = system.process_trading_opportunity(
        market_data, llm_analysis, account_info, current_positions
    )
    
    print("📊 交易决策结果:")
    print(f"   行动: {result['action']}")
    print(f"   系统状态: {result['system_status']}")
    print(f"   理由: {result.get('reason', result.get('reasoning', ''))}")
    
    if result['action'] != 'NONE':
        print(f"   仓位大小: {result['lotSize']:.3f}")
        print(f"   信号质量: {result['signalQuality']}")
        print(f"   风险等级: {result['riskLevel']}")

if __name__ == "__main__":
    test_practical_system()
