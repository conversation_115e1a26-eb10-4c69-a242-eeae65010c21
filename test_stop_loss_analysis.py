#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
止损设置分析测试脚本
客观分析当前止损设置是否合理
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'QuantumForex_Pro'))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def generate_realistic_market_data():
    """生成更真实的市场数据（基于实际波动率）"""
    symbols = ['EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCHF', 'USDCAD', 'USDJPY']
    market_data = {}
    
    # 真实的日均波动率数据（基于历史数据）
    daily_volatility = {
        'EURUSD': 0.0080,   # 约80点日波动
        'GBPUSD': 0.0120,   # 约120点日波动
        'AUDUSD': 0.0090,   # 约90点日波动
        'NZDUSD': 0.0100,   # 约100点日波动
        'USDCHF': 0.0075,   # 约75点日波动
        'USDCAD': 0.0085,   # 约85点日波动
        'USDJPY': 0.80      # 约80点日波动（日元）
    }
    
    for symbol in symbols:
        # 基础价格
        if symbol == 'USDJPY':
            base_price = 144.0
            daily_vol = daily_volatility[symbol]
        elif symbol == 'EURUSD':
            base_price = 1.136
            daily_vol = daily_volatility[symbol]
        elif symbol == 'GBPUSD':
            base_price = 1.348
            daily_vol = daily_volatility[symbol]
        elif symbol == 'AUDUSD':
            base_price = 0.644
            daily_vol = daily_volatility[symbol]
        elif symbol == 'NZDUSD':
            base_price = 0.598
            daily_vol = daily_volatility[symbol]
        elif symbol == 'USDCHF':
            base_price = 0.823
            daily_vol = daily_volatility[symbol]
        else:  # USDCAD
            base_price = 1.381
            daily_vol = daily_volatility[symbol]
        
        # 生成500个5分钟数据点
        dates = pd.date_range(end=datetime.now(), periods=500, freq='5min')
        
        # 5分钟波动率 = 日波动率 / sqrt(288) (一天288个5分钟)
        minute_vol = daily_vol / np.sqrt(288)
        
        # 生成价格序列（随机游走）
        returns = np.random.normal(0, minute_vol, 500)
        log_prices = np.log(base_price) + np.cumsum(returns)
        close_prices = np.exp(log_prices)
        
        # 生成OHLC
        high_prices = close_prices * (1 + np.random.uniform(0, minute_vol, 500))
        low_prices = close_prices * (1 - np.random.uniform(0, minute_vol, 500))
        open_prices = np.roll(close_prices, 1)
        open_prices[0] = close_prices[0]
        
        # 计算ATR（真实波动率）
        high_low = high_prices - low_prices
        high_close = np.abs(high_prices - np.roll(close_prices, 1))
        low_close = np.abs(low_prices - np.roll(close_prices, 1))
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = np.mean(true_range[-14:])  # 14期ATR
        
        # 生成成交量
        volumes = np.random.uniform(1000, 5000, 500)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'timestamp': dates,
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'volume': volumes
        })
        
        market_data[symbol] = {
            'ohlcv': df,
            'current_price': close_prices[-1],
            'spread': 0.00002,
            'last_update': datetime.now(),
            'atr': atr,
            'daily_volatility': daily_vol
        }
    
    return market_data

def analyze_stop_loss_settings():
    """分析止损设置"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🔍 开始分析止损设置...")
    
    try:
        # 导入策略类
        from strategies.professional_portfolio_strategy import ProfessionalPortfolioStrategy
        
        # 创建策略实例
        strategy = ProfessionalPortfolioStrategy()
        
        # 生成真实市场数据
        logger.info("📊 生成真实市场数据...")
        market_data = generate_realistic_market_data()
        
        logger.info("🎯 分析各货币对的止损设置:")
        logger.info("=" * 80)
        
        results = []
        
        for symbol, data in market_data.items():
            current_price = data['current_price']
            atr = data['atr']
            daily_vol = data['daily_volatility']
            
            # 测试多头止损
            stop_loss, take_profit = strategy._calculate_practical_stop_take_profit(
                current_price, 
                strategy.PortfolioAction.ENTER_LONG if hasattr(strategy, 'PortfolioAction') else 1,
                atr, 
                symbol
            )
            
            # 计算止损距离（点数）
            if symbol == 'USDJPY':
                stop_distance_pips = abs(current_price - stop_loss) / 0.01
                tp_distance_pips = abs(take_profit - current_price) / 0.01
                pip_value = 0.01
            else:
                stop_distance_pips = abs(current_price - stop_loss) / 0.0001
                tp_distance_pips = abs(take_profit - current_price) / 0.0001
                pip_value = 0.0001
            
            # 计算相对于日波动率的比例
            daily_vol_pips = daily_vol / pip_value
            stop_vs_daily_vol = stop_distance_pips / daily_vol_pips
            
            # 计算ATR倍数
            atr_pips = atr / pip_value
            atr_multiplier = stop_distance_pips / atr_pips if atr_pips > 0 else 0
            
            # 风险回报比
            risk_reward = tp_distance_pips / stop_distance_pips if stop_distance_pips > 0 else 0
            
            logger.info(f"📈 {symbol}:")
            logger.info(f"   当前价格: {current_price:.5f}")
            logger.info(f"   止损价格: {stop_loss:.5f}")
            logger.info(f"   止盈价格: {take_profit:.5f}")
            logger.info(f"   止损距离: {stop_distance_pips:.1f} 点")
            logger.info(f"   止盈距离: {tp_distance_pips:.1f} 点")
            logger.info(f"   ATR: {atr_pips:.1f} 点")
            logger.info(f"   ATR倍数: {atr_multiplier:.2f}x")
            logger.info(f"   日波动率: {daily_vol_pips:.1f} 点")
            logger.info(f"   止损/日波动: {stop_vs_daily_vol:.2f}")
            logger.info(f"   风险回报比: 1:{risk_reward:.2f}")
            logger.info("")
            
            results.append({
                'symbol': symbol,
                'stop_pips': stop_distance_pips,
                'atr_pips': atr_pips,
                'atr_multiplier': atr_multiplier,
                'daily_vol_pips': daily_vol_pips,
                'stop_vs_daily_vol': stop_vs_daily_vol,
                'risk_reward': risk_reward
            })
        
        # 总体分析
        logger.info("📊 总体分析:")
        logger.info("=" * 50)
        
        avg_atr_multiplier = np.mean([r['atr_multiplier'] for r in results])
        avg_stop_vs_vol = np.mean([r['stop_vs_daily_vol'] for r in results])
        avg_risk_reward = np.mean([r['risk_reward'] for r in results])
        
        logger.info(f"平均ATR倍数: {avg_atr_multiplier:.2f}x")
        logger.info(f"平均止损/日波动比: {avg_stop_vs_vol:.2f}")
        logger.info(f"平均风险回报比: 1:{avg_risk_reward:.2f}")
        logger.info("")
        
        # 专业评估
        logger.info("🎯 专业评估:")
        logger.info("=" * 30)
        
        if avg_atr_multiplier < 1.5:
            logger.info("⚠️  ATR倍数偏小 - 可能过于激进")
        elif avg_atr_multiplier > 3.0:
            logger.info("⚠️  ATR倍数偏大 - 可能过于保守")
        else:
            logger.info("✅ ATR倍数合理 (1.5-3.0x)")
        
        if avg_stop_vs_vol < 0.2:
            logger.info("⚠️  止损相对日波动偏小 - 容易被噪音触发")
        elif avg_stop_vs_vol > 0.5:
            logger.info("⚠️  止损相对日波动偏大 - 可能错过趋势")
        else:
            logger.info("✅ 止损相对日波动合理 (0.2-0.5)")
        
        if avg_risk_reward < 1.5:
            logger.info("⚠️  风险回报比偏低")
        else:
            logger.info("✅ 风险回报比合理 (>1.5)")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return []

if __name__ == "__main__":
    results = analyze_stop_loss_settings()
    print(f"\n🎯 分析完成，共分析 {len(results)} 个货币对")
    input("按任意键退出...")
