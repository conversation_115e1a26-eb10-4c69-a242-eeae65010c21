#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试删除挂单功能
"""

import time
import json
from app.utils.mt4_client import MT4Client

def test_delete_pending_order():
    """测试删除挂单功能"""

    # 创建MT4客户端
    mt4_client = MT4Client()

    # 获取当前挂单
    print("获取当前挂单...")
    try:
        pending_orders = mt4_client.get_pending_orders()
        print(f"当前挂单: {json.dumps(pending_orders, indent=2, ensure_ascii=False)}")

        # 检查是否有错误
        if pending_orders.get('status') == 'error':
            print(f"获取挂单失败: {pending_orders.get('message')}")
            print("请确保MT4服务器正在运行，并且连接配置正确")
            return

        # 获取订单列表
        orders = pending_orders.get('orders', [])
    except Exception as e:
        print(f"获取挂单时出错: {e}")
        print("请确保MT4服务器正在运行，并且连接配置正确")
        return

    # 如果没有挂单，创建一个测试挂单
    if not orders or len(orders) == 0:
        print("没有挂单，创建一个测试挂单...")

        # 创建一个买入限价单
        order_result = mt4_client.place_order(
            symbol="EURUSD",
            order_type="BUY_LIMIT",
            lots=0.01,
            price=1.10,  # 低于当前价格的限价单
            stop_loss=1.09,
            take_profit=1.11,
            comment="测试挂单"
        )

        print(f"创建挂单结果: {json.dumps(order_result, indent=2, ensure_ascii=False)}")

        # 如果创建成功，获取订单ID
        if order_result.get('status') == 'success':
            order_id = order_result.get('order_id')
            print(f"成功创建挂单，订单ID: {order_id}")

            # 等待1秒，确保订单已经创建
            time.sleep(1)

            # 再次获取当前挂单
            try:
                pending_orders = mt4_client.get_pending_orders()
                print(f"更新后的挂单: {json.dumps(pending_orders, indent=2, ensure_ascii=False)}")

                # 检查是否有错误
                if pending_orders.get('status') == 'error':
                    print(f"获取挂单失败: {pending_orders.get('message')}")
                    return

                # 获取订单列表
                orders = pending_orders.get('orders', [])
            except Exception as e:
                print(f"获取挂单时出错: {e}")
                return
        else:
            print(f"创建挂单失败: {order_result.get('message')}")
            return

    # 选择第一个挂单进行删除
    if orders and len(orders) > 0:
        order_to_delete = orders[0]
        order_id = order_to_delete.get('order_id')

        print(f"准备删除挂单，订单ID: {order_id}")

        # 删除挂单
        delete_result = mt4_client.delete_order(order_id)
        print(f"删除挂单结果: {json.dumps(delete_result, indent=2, ensure_ascii=False)}")

        # 等待1秒，确保订单已经删除
        time.sleep(1)

        # 再次获取当前挂单，验证是否已删除
        try:
            pending_orders = mt4_client.get_pending_orders()
            print(f"删除后的挂单: {json.dumps(pending_orders, indent=2, ensure_ascii=False)}")

            # 检查是否有错误
            if pending_orders.get('status') == 'error':
                print(f"获取挂单失败: {pending_orders.get('message')}")
                return

            # 获取订单列表
            final_orders = pending_orders.get('orders', [])

            # 检查是否成功删除
            deleted = True
            for order in final_orders:
                if str(order.get('order_id')) == str(order_id):
                    deleted = False
                    break

            if deleted:
                print(f"挂单 {order_id} 已成功删除")
            else:
                print(f"挂单 {order_id} 删除失败，仍然存在")
        except Exception as e:
            print(f"获取挂单时出错: {e}")
            print("无法验证挂单是否已删除")
    else:
        print("没有挂单可供删除")

if __name__ == "__main__":
    test_delete_pending_order()
