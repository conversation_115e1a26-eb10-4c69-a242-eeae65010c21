@echo off
echo ========================================
echo Forex Trading System v2.0 - Server Ready Package
echo ========================================

set PACKAGE_NAME=ForexTradingSystem_v2.0.0_ServerReady
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

echo Starting package creation...
echo Timestamp: %TIMESTAMP%
echo Package name: %PACKAGE_NAME%

REM Create package directory
if exist "%PACKAGE_NAME%" rmdir /s /q "%PACKAGE_NAME%"
mkdir "%PACKAGE_NAME%"

echo.
echo Copying core files...

REM Copy app directory
echo   Copying app code...
xcopy "app" "%PACKAGE_NAME%\app\" /E /I /Y /Q
if errorlevel 1 (
    echo ERROR: Failed to copy app directory
    pause
    exit /b 1
)

REM Copy main files
echo   Copying main files...
copy "run.py" "%PACKAGE_NAME%\" >nul
copy "requirements.txt" "%PACKAGE_NAME%\" >nul
copy "README.md" "%PACKAGE_NAME%\" >nul

REM Copy config files
echo   Copying config files...
if exist ".env.local" copy ".env.local" "%PACKAGE_NAME%\" >nul
if exist "config.py" copy "config.py" "%PACKAGE_NAME%\" >nul

REM Copy deployment scripts
echo   Copying deployment scripts...
copy "start_server.bat" "%PACKAGE_NAME%\" >nul
copy "deploy_server.bat" "%PACKAGE_NAME%\" >nul

REM Copy documentation
echo   Copying documentation...
if exist "docs" xcopy "docs" "%PACKAGE_NAME%\docs\" /E /I /Y /Q >nul
copy "Windows_部署指南.md" "%PACKAGE_NAME%\" >nul 2>&1
copy "SERVER_DEPLOYMENT_GUIDE.md" "%PACKAGE_NAME%\" >nul

REM Copy templates
echo   Copying templates...
if exist "templates" xcopy "templates" "%PACKAGE_NAME%\templates\" /E /I /Y /Q >nul

REM Create directory structure
echo   Creating directory structure...
mkdir "%PACKAGE_NAME%\logs" >nul 2>&1
mkdir "%PACKAGE_NAME%\logs\error_logs" >nul 2>&1
mkdir "%PACKAGE_NAME%\app\data" >nul 2>&1
mkdir "%PACKAGE_NAME%\app\data\errors" >nul 2>&1
mkdir "%PACKAGE_NAME%\app\data\charts" >nul 2>&1
mkdir "%PACKAGE_NAME%\backups" >nul 2>&1

REM Copy update functionality
echo   Copying update functionality...
copy "remote_update_client.py" "%PACKAGE_NAME%\" >nul 2>&1
copy "update_system.bat" "%PACKAGE_NAME%\" >nul 2>&1
if exist "install_service.bat" copy "install_service.bat" "%PACKAGE_NAME%\" >nul

REM Create version info
echo   Creating version info...
echo { > "%PACKAGE_NAME%\version.json"
echo   "version": "2.0.0", >> "%PACKAGE_NAME%\version.json"
echo   "build_date": "%TIMESTAMP%", >> "%PACKAGE_NAME%\version.json"
echo   "description": "Forex Trading System - Server Ready Version", >> "%PACKAGE_NAME%\version.json"
echo   "features": [ >> "%PACKAGE_NAME%\version.json"
echo     "Fixed encoding issues", >> "%PACKAGE_NAME%\version.json"
echo     "Optimized dependency management", >> "%PACKAGE_NAME%\version.json"
echo     "Auto deployment scripts", >> "%PACKAGE_NAME%\version.json"
echo     "Enhanced server compatibility", >> "%PACKAGE_NAME%\version.json"
echo     "Improved error handling" >> "%PACKAGE_NAME%\version.json"
echo   ] >> "%PACKAGE_NAME%\version.json"
echo } >> "%PACKAGE_NAME%\version.json"

REM Create deployment readme
echo   Creating deployment readme...
echo # Forex Trading System v2.0.0 - Server Ready > "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo ## Quick Deployment >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo 1. Run deploy_server.bat >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo 2. Run start_server.bat >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo ## Version Features >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - Fixed Windows Server 2012 encoding issues >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - Optimized dependency management >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - Enhanced error handling >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - Added auto deployment scripts >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - Improved server compatibility >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo Build time: %TIMESTAMP% >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo Version: v2.0.0 >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"

REM Clean unnecessary files
echo   Cleaning unnecessary files...
if exist "%PACKAGE_NAME%\app\__pycache__" rmdir /s /q "%PACKAGE_NAME%\app\__pycache__"
for /d %%i in ("%PACKAGE_NAME%\app\*") do (
    if exist "%%i\__pycache__" rmdir /s /q "%%i\__pycache__"
)
if exist "%PACKAGE_NAME%\*.pyc" del "%PACKAGE_NAME%\*.pyc" /s /q
if exist "%PACKAGE_NAME%\*.log" del "%PACKAGE_NAME%\*.log" /q

echo.
echo Creating ZIP package...
powershell -command "Compress-Archive -Path '%PACKAGE_NAME%' -DestinationPath '%PACKAGE_NAME%.zip' -Force"

if exist "%PACKAGE_NAME%.zip" (
    echo SUCCESS: Package created!
    echo Package file: %PACKAGE_NAME%.zip
    echo Package directory: %PACKAGE_NAME%\
    echo.
    echo Deployment steps:
    echo 1. Upload %PACKAGE_NAME%.zip to server
    echo 2. Extract to target directory
    echo 3. Run deploy_server.bat
    echo 4. Run start_server.bat
    echo.
    echo See SERVER_DEPLOYMENT_GUIDE.md for detailed instructions
) else (
    echo ERROR: Package creation failed
)

echo.
echo ========================================
echo Packaging completed!
echo ========================================
pause
