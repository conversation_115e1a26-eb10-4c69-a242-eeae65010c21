{"export_timestamp": "2025-05-24T14:25:03.240258", "learning_statistics": {"total_trades_analyzed": 17, "learning_cycles_completed": 1, "last_learning_update": "2025-05-24T14:25:03.218140", "active_insights": 2, "identified_patterns": 0, "parameter_adjustments": 0, "recent_accuracy": null}, "performance_summary": {"period_days": 30, "total_trades": 17, "win_rate": 0.6470588235294118, "total_return": 0.175, "avg_return_per_trade": 0.010294117647058823, "volatility": 0.028466212595207867, "max_drawdown": 0.062, "sharpe_ratio": 0.3616258261484277, "avg_holding_time_hours": 0.7352941176470588, "avg_prediction_accuracy": 0.6955882352941176, "avg_timing_accuracy": 0.6, "analysis_date": "2025-05-24T14:25:03.240495"}, "learning_insights": [{"insight_type": "错误纠正", "category": "失败交易", "pattern_description": "止损触发频率过高(100.0%)", "confidence_level": 0.8, "supporting_evidence": ["止损交易: 6/6"], "recommended_adjustments": {"widen_stop_loss": true, "improve_entry_timing": true, "add_volatility_filter": true}, "impact_assessment": "中等影响 - 减少不必要的止损", "implementation_priority": 6}, {"insight_type": "错误纠正", "category": "失败交易", "pattern_description": "持仓时间过短导致亏损(83.3%)", "confidence_level": 0.7, "supporting_evidence": ["短期交易: 5/6"], "recommended_adjustments": {"improve_trend_confirmation": true, "add_patience_filter": true, "enhance_signal_strength_requirement": true}, "impact_assessment": "中等影响 - 提高交易质量", "implementation_priority": 5}], "performance_patterns": [], "trade_summary": [{"trade_id": "trade_001", "entry_time": "2025-05-24T12:25:03.215027", "symbol": "EURUSD", "action": "BUY", "profit_loss_pct": 0.044, "prediction_accuracy": 1.0, "timing_accuracy": 1.0, "overall_performance_score": 1.0}, {"trade_id": "trade_002", "entry_time": "2025-05-24T13:25:03.215052", "symbol": "EURUSD", "action": "SELL", "profit_loss_pct": -0.044, "prediction_accuracy": 0.3, "timing_accuracy": 0.2, "overall_performance_score": 0.21}, {"trade_id": "trade_003", "entry_time": "2025-05-24T13:25:03.217630", "symbol": "EURUSD", "action": "BUY", "profit_loss_pct": 0.044, "prediction_accuracy": 1.0, "timing_accuracy": 1.0, "overall_performance_score": 1.0}, {"trade_id": "trade_004", "entry_time": "2025-05-24T12:25:03.217743", "symbol": "EURUSD", "action": "SELL", "profit_loss_pct": -0.018, "prediction_accuracy": 0.26999999999999996, "timing_accuracy": 0.2, "overall_performance_score": 0.19499999999999998}, {"trade_id": "trade_005", "entry_time": "2025-05-24T11:25:03.217794", "symbol": "EURUSD", "action": "BUY", "profit_loss_pct": 0.009, "prediction_accuracy": 0.835, "timing_accuracy": 0.6, "overall_performance_score": 0.7974999999999999}, {"trade_id": "trade_006", "entry_time": "2025-05-24T10:25:03.217825", "symbol": "EURUSD", "action": "BUY", "profit_loss_pct": 0.044, "prediction_accuracy": 1.0, "timing_accuracy": 1.0, "overall_performance_score": 1.0}, {"trade_id": "trade_007", "entry_time": "2025-05-24T09:25:03.217850", "symbol": "EURUSD", "action": "SELL", "profit_loss_pct": -0.018, "prediction_accuracy": 0.26999999999999996, "timing_accuracy": 0.2, "overall_performance_score": 0.19499999999999998}, {"trade_id": "trade_008", "entry_time": "2025-05-24T08:25:03.217873", "symbol": "EURUSD", "action": "BUY", "profit_loss_pct": 0.009, "prediction_accuracy": 0.835, "timing_accuracy": 0.6, "overall_performance_score": 0.7974999999999999}, {"trade_id": "trade_009", "entry_time": "2025-05-24T07:25:03.217893", "symbol": "EURUSD", "action": "BUY", "profit_loss_pct": 0.044, "prediction_accuracy": 1.0, "timing_accuracy": 1.0, "overall_performance_score": 1.0}, {"trade_id": "trade_010", "entry_time": "2025-05-24T06:25:03.217914", "symbol": "EURUSD", "action": "SELL", "profit_loss_pct": -0.018, "prediction_accuracy": 0.26999999999999996, "timing_accuracy": 0.2, "overall_performance_score": 0.19499999999999998}, {"trade_id": "trade_011", "entry_time": "2025-05-24T05:25:03.218157", "symbol": "EURUSD", "action": "BUY", "profit_loss_pct": 0.009, "prediction_accuracy": 0.835, "timing_accuracy": 0.6, "overall_performance_score": 0.7974999999999999}, {"trade_id": "trade_012", "entry_time": "2025-05-24T04:25:03.218187", "symbol": "EURUSD", "action": "BUY", "profit_loss_pct": 0.044, "prediction_accuracy": 1.0, "timing_accuracy": 1.0, "overall_performance_score": 1.0}, {"trade_id": "trade_013", "entry_time": "2025-05-24T03:25:03.218210", "symbol": "EURUSD", "action": "SELL", "profit_loss_pct": -0.018, "prediction_accuracy": 0.26999999999999996, "timing_accuracy": 0.2, "overall_performance_score": 0.19499999999999998}, {"trade_id": "trade_014", "entry_time": "2025-05-24T02:25:03.218231", "symbol": "EURUSD", "action": "BUY", "profit_loss_pct": 0.009, "prediction_accuracy": 0.835, "timing_accuracy": 0.6, "overall_performance_score": 0.7974999999999999}, {"trade_id": "trade_015", "entry_time": "2025-05-24T01:25:03.218251", "symbol": "EURUSD", "action": "BUY", "profit_loss_pct": 0.044, "prediction_accuracy": 1.0, "timing_accuracy": 1.0, "overall_performance_score": 1.0}, {"trade_id": "trade_016", "entry_time": "2025-05-24T00:25:03.218272", "symbol": "EURUSD", "action": "SELL", "profit_loss_pct": -0.018, "prediction_accuracy": 0.26999999999999996, "timing_accuracy": 0.2, "overall_performance_score": 0.19499999999999998}, {"trade_id": "trade_017", "entry_time": "2025-05-23T23:25:03.218293", "symbol": "EURUSD", "action": "BUY", "profit_loss_pct": 0.009, "prediction_accuracy": 0.835, "timing_accuracy": 0.6, "overall_performance_score": 0.7974999999999999}]}