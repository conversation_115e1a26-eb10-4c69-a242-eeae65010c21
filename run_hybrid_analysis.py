"""
启动混合外汇分析系统
同时使用定时分析和基于市场变化的实时分析
"""
import os
import sys
import time
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ['ANALYSIS_MODE'] = 'hybrid'  # 设置为混合分析模式

from app.utils.forex_scheduled_tasks import start_hourly_forex_analysis, start_realtime_forex_analysis, stop_all_tasks


def main():
    """主函数"""
    print("=" * 50)
    print("外汇交易系统 - 混合分析模式")
    print("=" * 50)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 询问是否自动交易
    auto_trade = input("是否自动执行交易？(y/n): ").lower() == 'y'
    
    # 询问市场检查间隔
    try:
        check_interval = int(input("设置市场检查间隔（秒，默认60）: ") or "60")
    except ValueError:
        check_interval = 60
        print(f"输入无效，使用默认值: {check_interval}秒")
    
    # 启动每小时分析
    print(f"\n启动每小时分析，自动交易: {auto_trade}")
    start_hourly_forex_analysis(run_immediately=True, auto_trade=auto_trade)
    
    # 启动实时分析
    print(f"\n启动实时分析，检查间隔: {check_interval}秒，自动交易: {auto_trade}")
    start_realtime_forex_analysis(
        run_immediately=False,  # 不立即执行，因为每小时分析已经执行了
        auto_trade=auto_trade,
        check_interval=check_interval
    )
    
    print("\n混合分析系统已启动")
    print("系统将每小时执行一次定时分析，并在检测到市场变化时执行实时分析")
    print("按Ctrl+C停止系统")
    
    # 保持主线程运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n捕获到中断信号，正在停止系统...")
        stop_all_tasks()
        print("系统已安全停止")


if __name__ == "__main__":
    main()
