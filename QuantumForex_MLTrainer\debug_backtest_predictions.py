#!/usr/bin/env python3
"""
调试回测预测问题
深入分析为什么有效预测为0
"""

import sys
import os
import pandas as pd
import numpy as np
import joblib
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'QuantumForex_Pro'))

def debug_prediction_values():
    """调试预测值问题"""
    print("🔍 深入调试回测预测问题")
    print("="*50)

    try:
        # 1. 找到最新的AUDUSD模型（排除scaler文件）
        models_dir = Path("data/models")
        audusd_models = list(models_dir.glob("AUDUSD_price_prediction_*.pkl"))
        # 排除scaler文件
        audusd_models = [f for f in audusd_models if 'scaler' not in f.name]

        if not audusd_models:
            print("❌ 没有找到AUDUSD模型")
            return

        latest_model = max(audusd_models, key=lambda x: x.stat().st_mtime)
        print(f"📦 使用模型: {latest_model.name}")

        # 2. 加载模型和scaler
        model = joblib.load(latest_model)
        print(f"✅ 模型类型: {type(model).__name__}")

        # 查找对应的scaler
        scaler_files = list(models_dir.glob("*scaler*.pkl"))
        scaler = None
        for scaler_file in scaler_files:
            if "AUDUSD" in scaler_file.name and "price_prediction" in scaler_file.name:
                scaler = joblib.load(scaler_file)
                print(f"📦 使用scaler: {scaler_file.name}")
                print(f"✅ Scaler类型: {type(scaler).__name__}")
                break

        if scaler is None:
            from sklearn.preprocessing import StandardScaler
            scaler = StandardScaler()
            print("⚠️ 使用默认scaler")

        # 3. 获取测试数据
        from utils.db_client import execute_query

        sql = """
        SELECT
            time_date_str as timestamp,
            price as open,
            max as high,
            min as low,
            price as close,
            volume
        FROM min_quote_audusd
        ORDER BY time_min_int DESC
        LIMIT 200
        """

        raw_data = execute_query(sql)
        print(f"📊 获取AUDUSD数据: {len(raw_data)}条")

        # 转换为DataFrame
        df = pd.DataFrame(raw_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp')
        df = df.sort_index()

        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        df = df.dropna()
        print(f"✅ 处理后数据: {len(df)}条")

        # 4. 计算特征（使用与回测相同的逻辑）
        print("\n🔧 计算特征...")

        # 价格特征
        df['price_change'] = df['close'].pct_change()
        df['price_volatility'] = df['price_change'].rolling(20, min_periods=5).std()
        df['high_low_ratio'] = (df['high'] - df['low']) / df['close']

        # 移动平均
        for period in [5, 10, 20]:
            min_periods = max(1, period // 3)
            df[f'ma_{period}'] = df['close'].rolling(period, min_periods=min_periods).mean()
            df[f'price_ma_ratio_{period}'] = df['close'] / df[f'ma_{period}']

        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14, min_periods=5).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14, min_periods=5).mean()
        rs = gain / (loss + 1e-10)
        df['rsi'] = 100 - (100 / (1 + rs))

        # 布林带
        df['bb_middle'] = df['close'].rolling(20, min_periods=5).mean()
        df['bb_std'] = df['close'].rolling(20, min_periods=5).std()
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
        bb_range = df['bb_upper'] - df['bb_lower']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (bb_range + 1e-10)

        # 成交量指标
        df['volume_ma'] = df['volume'].rolling(20, min_periods=5).mean()
        df['volume_ratio'] = df['volume'] / (df['volume_ma'] + 1e-10)

        # 移除NaN
        df = df.dropna()
        print(f"✅ 特征计算完成: {len(df)}行, {len(df.columns)}列")

        # 5. 测试预测并分析值
        print(f"\n🔮 分析预测值...")

        predictions = []
        signals = []
        confidences = []

        for i in range(20, min(50, len(df))):  # 测试30个样本
            try:
                row = df.iloc[i]

                # 提取特征
                features = [
                    row['close'],
                    row.get('price_change', 0),
                    row.get('price_volatility', 0),
                    row.get('high_low_ratio', 0),
                    row.get('rsi', 50),
                    row.get('bb_position', 0.5),
                    row.get('volume_ratio', 1)
                ]

                # 移动平均比率
                for period in [5, 10, 20]:
                    features.append(row.get(f'price_ma_ratio_{period}', 1))

                # 滞后特征
                for lag in [1, 2, 3]:
                    if i >= lag:
                        lag_value = df.iloc[i-lag].get('price_change', 0)
                        features.append(lag_value)
                    else:
                        features.append(0)

                # 处理异常值
                features = np.array(features, dtype=float)
                features = np.nan_to_num(features, nan=0.0, posinf=1.0, neginf=-1.0)
                features = np.clip(features, -100, 100)

                # 标准化特征
                if hasattr(scaler, 'transform') and hasattr(scaler, 'mean_'):
                    features_scaled = scaler.transform(features.reshape(1, -1))
                else:
                    features_scaled = (features - np.mean(features)) / (np.std(features) + 1e-8)
                    features_scaled = features_scaled.reshape(1, -1)

                # 模型预测
                prediction = model.predict(features_scaled)[0]
                signal = prediction
                confidence = min(abs(prediction) * 10, 1.0)

                predictions.append(prediction)
                signals.append(signal)
                confidences.append(confidence)

                # 显示前10个详细信息
                if len(predictions) <= 10:
                    print(f"样本{i:2d}: 预测={prediction:8.6f}, 信号={signal:8.6f}, 置信度={confidence:.3f}")

            except Exception as e:
                print(f"样本{i}预测失败: {e}")
                continue

        # 6. 统计分析
        print(f"\n📊 预测值统计分析:")
        print(f"   总样本数: {len(predictions)}")

        if predictions:
            predictions = np.array(predictions)
            signals = np.array(signals)
            confidences = np.array(confidences)

            print(f"   预测值范围: [{predictions.min():.6f}, {predictions.max():.6f}]")
            print(f"   预测值均值: {predictions.mean():.6f}")
            print(f"   预测值标准差: {predictions.std():.6f}")
            print(f"   信号范围: [{signals.min():.6f}, {signals.max():.6f}]")
            print(f"   置信度范围: [{confidences.min():.3f}, {confidences.max():.3f}]")

            # 7. 测试不同的过滤条件
            print(f"\n🔍 测试不同过滤条件:")

            thresholds = [0.3, 0.1, 0.01, 0.001, 0.0001]
            for threshold in thresholds:
                valid_signals = np.sum(np.abs(signals) > threshold)
                percentage = valid_signals / len(signals) * 100
                print(f"   信号阈值 > {threshold:6.4f}: {valid_signals:2d}个 ({percentage:5.1f}%)")

            confidence_thresholds = [0.7, 0.5, 0.3, 0.1]
            for threshold in confidence_thresholds:
                valid_confidence = np.sum(confidences > threshold)
                percentage = valid_confidence / len(confidences) * 100
                print(f"   置信度 > {threshold:4.1f}: {valid_confidence:2d}个 ({percentage:5.1f}%)")

            # 8. 组合条件测试
            print(f"\n🎯 组合条件测试:")

            test_conditions = [
                (0.3, 0.5),   # 原始条件
                (0.1, 0.3),   # 中等条件
                (0.01, 0.3),  # 宽松条件
                (0.001, 0.1), # 很宽松条件
                (0.0001, 0.1) # 极宽松条件
            ]

            for signal_thresh, conf_thresh in test_conditions:
                valid_trades = np.sum((np.abs(signals) > signal_thresh) & (confidences > conf_thresh))
                percentage = valid_trades / len(signals) * 100
                print(f"   信号>{signal_thresh:6.4f} 且 置信度>{conf_thresh:.1f}: {valid_trades:2d}个 ({percentage:5.1f}%)")

            # 9. 推荐最佳阈值
            print(f"\n💡 推荐解决方案:")

            # 找到能产生合理交易数量的阈值
            target_trade_percentage = 10  # 目标：10%的数据点产生交易信号

            best_signal_thresh = 0.001
            best_conf_thresh = 0.1

            for signal_thresh in [0.1, 0.01, 0.001, 0.0001]:
                for conf_thresh in [0.5, 0.3, 0.1]:
                    valid_trades = np.sum((np.abs(signals) > signal_thresh) & (confidences > conf_thresh))
                    percentage = valid_trades / len(signals) * 100

                    if 5 <= percentage <= 20:  # 5-20%的交易频率比较合理
                        best_signal_thresh = signal_thresh
                        best_conf_thresh = conf_thresh
                        print(f"   ✅ 推荐阈值: 信号>{signal_thresh:6.4f}, 置信度>{conf_thresh:.1f} -> {valid_trades}个交易 ({percentage:.1f}%)")
                        break
                else:
                    continue
                break

            print(f"\n🔧 修复建议:")
            print(f"   1. 将信号阈值从 0.3 降低到 {best_signal_thresh}")
            print(f"   2. 将置信度阈值从 0.5 降低到 {best_conf_thresh}")
            print(f"   3. 这样可以产生合理数量的交易信号")

        else:
            print("❌ 没有生成任何预测")

    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_prediction_values()
