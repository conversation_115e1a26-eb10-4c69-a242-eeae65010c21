#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能市场监控系统
目标：零Token成本的实时市场监控，精准触发LLM完整分析
核心思想：算法监控 + 智能触发 + 成本控制
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json

class TriggerReason(Enum):
    """触发原因"""
    CRITICAL_LEVEL_BREACH = "关键位突破"
    VOLATILITY_SPIKE = "波动率异常"
    TREND_CHANGE = "趋势变化"
    MOMENTUM_DIVERGENCE = "动量背离"
    VOLUME_ANOMALY = "成交量异常"
    TIME_BASED = "时间触发"
    POSITION_RISK = "持仓风险"
    NEWS_EVENT = "新闻事件"
    PATTERN_COMPLETION = "形态完成"

class MarketState(Enum):
    """市场状态"""
    QUIET = "平静"
    ACTIVE = "活跃"
    VOLATILE = "波动"
    TRENDING = "趋势"
    RANGING = "震荡"
    BREAKOUT = "突破"

@dataclass
class TriggerEvent:
    """触发事件"""
    timestamp: datetime
    reason: TriggerReason
    urgency: float  # 0-1，紧急程度
    confidence: float  # 0-1，置信度
    description: str
    market_data: Dict
    suggested_action: str

@dataclass
class MonitoringConfig:
    """监控配置"""
    # 基础监控间隔
    base_interval: int = 30  # 秒
    
    # 触发阈值
    price_change_threshold: float = 0.15  # 价格变化阈值（%）
    volatility_spike_threshold: float = 2.0  # 波动率异常倍数
    volume_spike_threshold: float = 1.5  # 成交量异常倍数
    
    # 时间控制
    min_analysis_interval: int = 300  # 最小分析间隔（秒）
    max_quiet_period: int = 3600  # 最大静默期（秒）
    
    # 成本控制
    max_hourly_analyses: int = 6  # 每小时最大分析次数
    emergency_override: bool = True  # 紧急情况覆盖限制

class SmartMarketMonitor:
    """智能市场监控器"""
    
    def __init__(self, config: MonitoringConfig = None):
        self.config = config or MonitoringConfig()
        self.last_analysis_time = None
        self.analysis_count_hourly = 0
        self.last_hour_reset = datetime.now().hour
        self.market_state = MarketState.QUIET
        self.baseline_metrics = {}
        self.trigger_history = []
        
        # 关键位追踪
        self.key_levels = {
            'support': [],
            'resistance': [],
            'pivot_points': []
        }
        
        # 市场状态缓存
        self.market_cache = {
            'last_price': None,
            'price_history': [],
            'volatility_history': [],
            'volume_history': []
        }
    
    def monitor_market(self, market_data: Dict) -> Optional[TriggerEvent]:
        """
        主监控函数 - 零Token成本的市场监控
        
        Returns:
            TriggerEvent: 如果需要触发分析，返回触发事件；否则返回None
        """
        
        # 1. 更新市场缓存
        self._update_market_cache(market_data)
        
        # 2. 重置小时计数器
        self._reset_hourly_counter()
        
        # 3. 检查成本控制
        if not self._check_cost_limits():
            return None
        
        # 4. 多维度监控检查
        trigger_events = []
        
        # 价格突破监控
        price_trigger = self._check_price_breakout(market_data)
        if price_trigger:
            trigger_events.append(price_trigger)
        
        # 波动率监控
        volatility_trigger = self._check_volatility_spike(market_data)
        if volatility_trigger:
            trigger_events.append(volatility_trigger)
        
        # 趋势变化监控
        trend_trigger = self._check_trend_change(market_data)
        if trend_trigger:
            trigger_events.append(trend_trigger)
        
        # 动量背离监控
        momentum_trigger = self._check_momentum_divergence(market_data)
        if momentum_trigger:
            trigger_events.append(momentum_trigger)
        
        # 成交量异常监控
        volume_trigger = self._check_volume_anomaly(market_data)
        if volume_trigger:
            trigger_events.append(volume_trigger)
        
        # 时间触发监控
        time_trigger = self._check_time_trigger(market_data)
        if time_trigger:
            trigger_events.append(time_trigger)
        
        # 持仓风险监控
        position_trigger = self._check_position_risk(market_data)
        if position_trigger:
            trigger_events.append(position_trigger)
        
        # 5. 选择最重要的触发事件
        if trigger_events:
            best_trigger = self._select_best_trigger(trigger_events)
            self._record_trigger(best_trigger)
            return best_trigger
        
        return None
    
    def _update_market_cache(self, market_data: Dict):
        """更新市场数据缓存"""
        current_price = market_data.get('current_price', 0)
        
        # 更新价格历史
        self.market_cache['last_price'] = current_price
        self.market_cache['price_history'].append({
            'timestamp': datetime.now(),
            'price': current_price
        })
        
        # 保持最近100个数据点
        if len(self.market_cache['price_history']) > 100:
            self.market_cache['price_history'] = self.market_cache['price_history'][-100:]
        
        # 计算并缓存波动率
        if len(self.market_cache['price_history']) >= 20:
            prices = [p['price'] for p in self.market_cache['price_history'][-20:]]
            volatility = np.std(prices) / np.mean(prices) if np.mean(prices) > 0 else 0
            self.market_cache['volatility_history'].append(volatility)
            
            if len(self.market_cache['volatility_history']) > 50:
                self.market_cache['volatility_history'] = self.market_cache['volatility_history'][-50:]
    
    def _reset_hourly_counter(self):
        """重置小时计数器"""
        current_hour = datetime.now().hour
        if current_hour != self.last_hour_reset:
            self.analysis_count_hourly = 0
            self.last_hour_reset = current_hour
    
    def _check_cost_limits(self) -> bool:
        """检查成本限制"""
        # 检查小时限制
        if self.analysis_count_hourly >= self.config.max_hourly_analyses:
            return False
        
        # 检查最小间隔
        if self.last_analysis_time:
            time_since_last = (datetime.now() - self.last_analysis_time).total_seconds()
            if time_since_last < self.config.min_analysis_interval:
                return False
        
        return True
    
    def _check_price_breakout(self, market_data: Dict) -> Optional[TriggerEvent]:
        """检查价格突破"""
        current_price = market_data.get('current_price', 0)
        
        # 检查关键位突破
        for level in self.key_levels['resistance']:
            if current_price > level and self.market_cache['last_price'] <= level:
                return TriggerEvent(
                    timestamp=datetime.now(),
                    reason=TriggerReason.CRITICAL_LEVEL_BREACH,
                    urgency=0.9,
                    confidence=0.8,
                    description=f"价格突破阻力位 {level:.5f}",
                    market_data=market_data,
                    suggested_action="分析突破有效性和后续目标"
                )
        
        for level in self.key_levels['support']:
            if current_price < level and self.market_cache['last_price'] >= level:
                return TriggerEvent(
                    timestamp=datetime.now(),
                    reason=TriggerReason.CRITICAL_LEVEL_BREACH,
                    urgency=0.9,
                    confidence=0.8,
                    description=f"价格跌破支撑位 {level:.5f}",
                    market_data=market_data,
                    suggested_action="分析跌破有效性和后续目标"
                )
        
        # 检查价格变化幅度
        if len(self.market_cache['price_history']) >= 2:
            price_change_pct = abs(current_price - self.market_cache['price_history'][-2]['price']) / current_price * 100
            if price_change_pct > self.config.price_change_threshold:
                return TriggerEvent(
                    timestamp=datetime.now(),
                    reason=TriggerReason.CRITICAL_LEVEL_BREACH,
                    urgency=0.7,
                    confidence=0.6,
                    description=f"价格变化 {price_change_pct:.2f}% 超过阈值",
                    market_data=market_data,
                    suggested_action="分析价格异动原因"
                )
        
        return None
    
    def _check_volatility_spike(self, market_data: Dict) -> Optional[TriggerEvent]:
        """检查波动率异常"""
        if len(self.market_cache['volatility_history']) < 10:
            return None
        
        current_volatility = self.market_cache['volatility_history'][-1]
        avg_volatility = np.mean(self.market_cache['volatility_history'][-10:])
        
        if current_volatility > avg_volatility * self.config.volatility_spike_threshold:
            return TriggerEvent(
                timestamp=datetime.now(),
                reason=TriggerReason.VOLATILITY_SPIKE,
                urgency=0.8,
                confidence=0.7,
                description=f"波动率异常：当前 {current_volatility:.4f}，平均 {avg_volatility:.4f}",
                market_data=market_data,
                suggested_action="分析波动率上升原因和交易机会"
            )
        
        return None
    
    def _check_trend_change(self, market_data: Dict) -> Optional[TriggerEvent]:
        """检查趋势变化"""
        if len(self.market_cache['price_history']) < 20:
            return None
        
        # 简单的趋势检测
        recent_prices = [p['price'] for p in self.market_cache['price_history'][-20:]]
        
        # 计算短期和长期趋势
        short_trend = np.polyfit(range(10), recent_prices[-10:], 1)[0]
        long_trend = np.polyfit(range(20), recent_prices, 1)[0]
        
        # 检查趋势反转
        if (short_trend > 0 and long_trend < 0) or (short_trend < 0 and long_trend > 0):
            trend_strength = abs(short_trend - long_trend)
            if trend_strength > 0.0001:  # 阈值可调
                return TriggerEvent(
                    timestamp=datetime.now(),
                    reason=TriggerReason.TREND_CHANGE,
                    urgency=0.6,
                    confidence=0.5,
                    description=f"趋势可能反转：短期趋势 {short_trend:.6f}，长期趋势 {long_trend:.6f}",
                    market_data=market_data,
                    suggested_action="确认趋势反转信号"
                )
        
        return None
    
    def _check_momentum_divergence(self, market_data: Dict) -> Optional[TriggerEvent]:
        """检查动量背离"""
        # 简化的动量背离检测
        rsi = market_data.get('rsi', 50)
        
        if rsi > 80 or rsi < 20:
            return TriggerEvent(
                timestamp=datetime.now(),
                reason=TriggerReason.MOMENTUM_DIVERGENCE,
                urgency=0.5,
                confidence=0.6,
                description=f"RSI极值：{rsi:.1f}，可能出现反转",
                market_data=market_data,
                suggested_action="分析超买超卖反转机会"
            )
        
        return None
    
    def _check_volume_anomaly(self, market_data: Dict) -> Optional[TriggerEvent]:
        """检查成交量异常"""
        # 简化的成交量检测
        # 实际实现需要历史成交量数据
        return None
    
    def _check_time_trigger(self, market_data: Dict) -> Optional[TriggerEvent]:
        """检查时间触发"""
        if not self.last_analysis_time:
            return None
        
        time_since_last = (datetime.now() - self.last_analysis_time).total_seconds()
        
        # 最大静默期触发
        if time_since_last > self.config.max_quiet_period:
            return TriggerEvent(
                timestamp=datetime.now(),
                reason=TriggerReason.TIME_BASED,
                urgency=0.3,
                confidence=0.4,
                description=f"超过最大静默期 {self.config.max_quiet_period/60:.0f} 分钟",
                market_data=market_data,
                suggested_action="定期市场检查"
            )
        
        return None
    
    def _check_position_risk(self, market_data: Dict) -> Optional[TriggerEvent]:
        """检查持仓风险"""
        positions = market_data.get('positions', [])
        current_price = market_data.get('current_price', 0)
        
        for position in positions:
            if position.get('type') == 'BUY':
                # 检查止损距离
                stop_loss = position.get('stop_loss', 0)
                if stop_loss > 0:
                    distance_to_sl = (current_price - stop_loss) / current_price * 100
                    if distance_to_sl < 0.1:  # 距离止损10个点以内
                        return TriggerEvent(
                            timestamp=datetime.now(),
                            reason=TriggerReason.POSITION_RISK,
                            urgency=0.9,
                            confidence=0.9,
                            description=f"多头持仓接近止损，距离 {distance_to_sl:.1f}%",
                            market_data=market_data,
                            suggested_action="评估是否调整止损或平仓"
                        )
            
            elif position.get('type') == 'SELL':
                # 检查止损距离
                stop_loss = position.get('stop_loss', 0)
                if stop_loss > 0:
                    distance_to_sl = (stop_loss - current_price) / current_price * 100
                    if distance_to_sl < 0.1:  # 距离止损10个点以内
                        return TriggerEvent(
                            timestamp=datetime.now(),
                            reason=TriggerReason.POSITION_RISK,
                            urgency=0.9,
                            confidence=0.9,
                            description=f"空头持仓接近止损，距离 {distance_to_sl:.1f}%",
                            market_data=market_data,
                            suggested_action="评估是否调整止损或平仓"
                        )
        
        return None
    
    def _select_best_trigger(self, triggers: List[TriggerEvent]) -> TriggerEvent:
        """选择最重要的触发事件"""
        # 按紧急程度和置信度排序
        scored_triggers = []
        for trigger in triggers:
            score = trigger.urgency * 0.7 + trigger.confidence * 0.3
            scored_triggers.append((score, trigger))
        
        # 返回得分最高的触发事件
        return max(scored_triggers, key=lambda x: x[0])[1]
    
    def _record_trigger(self, trigger: TriggerEvent):
        """记录触发事件"""
        self.trigger_history.append(trigger)
        self.analysis_count_hourly += 1
        self.last_analysis_time = datetime.now()
        
        # 保持最近50个触发记录
        if len(self.trigger_history) > 50:
            self.trigger_history = self.trigger_history[-50:]
    
    def update_key_levels(self, support_levels: List[float], resistance_levels: List[float]):
        """更新关键位"""
        self.key_levels['support'] = support_levels
        self.key_levels['resistance'] = resistance_levels
    
    def get_monitoring_stats(self) -> Dict:
        """获取监控统计"""
        return {
            'total_triggers_today': len([t for t in self.trigger_history 
                                        if t.timestamp.date() == datetime.now().date()]),
            'hourly_analysis_count': self.analysis_count_hourly,
            'last_analysis_time': self.last_analysis_time.isoformat() if self.last_analysis_time else None,
            'market_state': self.market_state.value,
            'recent_triggers': [
                {
                    'timestamp': t.timestamp.isoformat(),
                    'reason': t.reason.value,
                    'urgency': t.urgency,
                    'description': t.description
                }
                for t in self.trigger_history[-5:]
            ]
        }

class AdaptiveMonitoringSystem:
    """自适应监控系统"""
    
    def __init__(self):
        self.monitor = SmartMarketMonitor()
        self.performance_tracker = {}
        
    def process_market_data(self, market_data: Dict) -> Dict:
        """处理市场数据并决定是否触发分析"""
        
        # 1. 更新关键位（从市场数据中提取）
        self._update_key_levels_from_data(market_data)
        
        # 2. 执行监控
        trigger_event = self.monitor.monitor_market(market_data)
        
        # 3. 生成决策
        if trigger_event:
            decision = {
                'should_analyze': True,
                'trigger_reason': trigger_event.reason.value,
                'urgency': trigger_event.urgency,
                'confidence': trigger_event.confidence,
                'description': trigger_event.description,
                'suggested_action': trigger_event.suggested_action,
                'timestamp': trigger_event.timestamp.isoformat(),
                'system_used': 'SMART_MONITOR',
                'token_cost': 0  # 零Token成本
            }
        else:
            decision = {
                'should_analyze': False,
                'trigger_reason': '无触发条件',
                'urgency': 0.0,
                'confidence': 0.0,
                'description': '市场状态平稳，继续监控',
                'suggested_action': '继续监控',
                'timestamp': datetime.now().isoformat(),
                'system_used': 'SMART_MONITOR',
                'token_cost': 0
            }
        
        # 4. 添加监控统计
        decision['monitoring_stats'] = self.monitor.get_monitoring_stats()
        
        return decision
    
    def _update_key_levels_from_data(self, market_data: Dict):
        """从市场数据中提取并更新关键位"""
        # 简化版：从技术指标中提取关键位
        current_price = market_data.get('current_price', 0)
        
        # 基于当前价格计算简单的支撑阻力位
        support_levels = [
            current_price * 0.998,  # 0.2%下方
            current_price * 0.995,  # 0.5%下方
            current_price * 0.990   # 1.0%下方
        ]
        
        resistance_levels = [
            current_price * 1.002,  # 0.2%上方
            current_price * 1.005,  # 0.5%上方
            current_price * 1.010   # 1.0%上方
        ]
        
        self.monitor.update_key_levels(support_levels, resistance_levels)

# 测试函数
def test_smart_monitor():
    """测试智能监控系统"""
    print("🔍 智能市场监控系统测试")
    print("=" * 60)
    
    # 创建监控系统
    monitoring_system = AdaptiveMonitoringSystem()
    
    # 模拟市场数据
    test_scenarios = [
        {
            'name': '正常市场',
            'data': {
                'current_price': 1.1300,
                'rsi': 45,
                'positions': [],
                'spread': 2
            }
        },
        {
            'name': '价格突破',
            'data': {
                'current_price': 1.1350,  # 大幅上涨
                'rsi': 75,
                'positions': [],
                'spread': 2
            }
        },
        {
            'name': '持仓风险',
            'data': {
                'current_price': 1.1295,
                'rsi': 50,
                'positions': [
                    {'type': 'BUY', 'stop_loss': 1.1294, 'entry_price': 1.1300}
                ],
                'spread': 2
            }
        },
        {
            'name': 'RSI极值',
            'data': {
                'current_price': 1.1300,
                'rsi': 85,  # 超买
                'positions': [],
                'spread': 2
            }
        }
    ]
    
    # 测试各种场景
    for scenario in test_scenarios:
        print(f"\n📊 测试场景: {scenario['name']}")
        decision = monitoring_system.process_market_data(scenario['data'])
        
        print(f"   是否触发分析: {'是' if decision['should_analyze'] else '否'}")
        print(f"   触发原因: {decision['trigger_reason']}")
        print(f"   紧急程度: {decision['urgency']:.2f}")
        print(f"   置信度: {decision['confidence']:.2f}")
        print(f"   描述: {decision['description']}")
        print(f"   Token成本: {decision['token_cost']}")
    
    # 显示监控统计
    print(f"\n📈 监控统计:")
    stats = monitoring_system.monitor.get_monitoring_stats()
    print(f"   今日触发次数: {stats['total_triggers_today']}")
    print(f"   本小时分析次数: {stats['hourly_analysis_count']}")
    print(f"   当前市场状态: {stats['market_state']}")

if __name__ == "__main__":
    test_smart_monitor()
