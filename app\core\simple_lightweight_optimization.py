#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单轻量级优化系统
不依赖外部库，专为微型服务器设计的基础优化功能
"""

import os
import sys
import gc
import time
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from collections import defaultdict, deque
import logging

@dataclass
class SimpleSystemHealth:
    """简单系统健康状态"""
    timestamp: datetime
    memory_usage_mb: float
    process_count: int
    system_status: str  # 'healthy', 'warning', 'critical'

class SimpleResourceMonitor:
    """简单资源监控器（不依赖psutil）"""
    
    def __init__(self):
        # 阈值设置
        self.memory_warning_mb = 100    # 内存警告阈值（MB）
        self.memory_critical_mb = 200   # 内存危险阈值（MB）
        
        # 监控历史
        self.health_history = deque(maxlen=50)
        self.alert_count = defaultdict(int)
        
        # 监控线程
        self.monitoring = False
        self.monitor_thread = None
        
        self.logger = logging.getLogger(__name__)
    
    def get_simple_memory_usage(self) -> float:
        """获取简单的内存使用情况（MB）"""
        try:
            # 使用gc获取对象数量作为内存使用的简单指标
            object_count = len(gc.get_objects())
            # 简单估算：每1000个对象约1MB（粗略估算）
            estimated_memory_mb = object_count / 1000
            return estimated_memory_mb
        except:
            return 50.0  # 默认值
    
    def get_current_health(self) -> SimpleSystemHealth:
        """获取当前系统健康状态"""
        
        memory_usage = self.get_simple_memory_usage()
        
        # 简单的进程计数（线程数）
        try:
            import threading
            process_count = threading.active_count()
        except:
            process_count = 1
        
        # 判断系统状态
        if memory_usage >= self.memory_critical_mb:
            status = 'critical'
        elif memory_usage >= self.memory_warning_mb:
            status = 'warning'
        else:
            status = 'healthy'
        
        health = SimpleSystemHealth(
            timestamp=datetime.now(),
            memory_usage_mb=memory_usage,
            process_count=process_count,
            system_status=status
        )
        
        # 记录历史
        self.health_history.append(health)
        
        return health
    
    def start_monitoring(self, interval: int = 30):
        """开始监控"""
        
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop, 
            args=(interval,), 
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info(f"简单资源监控已启动，检查间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("简单资源监控已停止")
    
    def _monitoring_loop(self, interval: int):
        """监控循环"""
        
        while self.monitoring:
            try:
                health = self.get_current_health()
                
                if health.system_status == 'critical':
                    self._handle_critical_situation(health)
                elif health.system_status == 'warning':
                    self._handle_warning_situation(health)
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(interval)
    
    def _handle_critical_situation(self, health: SimpleSystemHealth):
        """处理危险情况"""
        self.alert_count['critical'] += 1
        self.logger.warning(f"系统资源危险: 内存{health.memory_usage_mb:.1f}MB")
        self._emergency_cleanup()
    
    def _handle_warning_situation(self, health: SimpleSystemHealth):
        """处理警告情况"""
        self.alert_count['warning'] += 1
        self.logger.info(f"系统资源警告: 内存{health.memory_usage_mb:.1f}MB")
        self._optimize_resources()
    
    def _emergency_cleanup(self):
        """紧急清理"""
        gc.collect()
        self.logger.info("执行紧急资源清理")
    
    def _optimize_resources(self):
        """优化资源使用"""
        gc.collect()
        self.logger.info("执行资源优化")
    
    def get_health_summary(self) -> Dict:
        """获取健康状态摘要"""
        
        if not self.health_history:
            return {'status': 'no_data'}
        
        current = self.health_history[-1]
        recent_health = list(self.health_history)[-5:]  # 最近5次检查
        
        avg_memory = sum(h.memory_usage_mb for h in recent_health) / len(recent_health)
        
        return {
            'current_status': current.system_status,
            'current_memory_mb': current.memory_usage_mb,
            'avg_memory_mb': avg_memory,
            'process_count': current.process_count,
            'alert_counts': dict(self.alert_count),
            'last_check': current.timestamp.isoformat()
        }

class SimpleCacheManager:
    """简单缓存管理器"""
    
    def __init__(self, max_entries: int = 100):
        self.max_entries = max_entries
        self.cache = {}
        self.access_times = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0
        }
        
        self.logger = logging.getLogger(__name__)
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        
        if key in self.cache:
            entry = self.cache[key]
            
            # 检查是否过期
            if entry['expires'] > time.time():
                self.access_times[key] = time.time()
                self.cache_stats['hits'] += 1
                return entry
            else:
                # 过期，删除
                del self.cache[key]
                del self.access_times[key]
                self.cache_stats['misses'] += 1
                return None
        else:
            self.cache_stats['misses'] += 1
            return None
    
    def set(self, key: str, value: Any, ttl: int = 300):
        """设置缓存数据"""
        
        # 检查缓存大小
        if len(self.cache) >= self.max_entries:
            self._evict_old_entries()
        
        self.cache[key] = {
            'data': value,
            'expires': time.time() + ttl
        }
        self.access_times[key] = time.time()
    
    def _evict_old_entries(self):
        """清理旧条目"""
        
        current_time = time.time()
        
        # 清理过期条目
        expired_keys = [
            key for key, entry in self.cache.items()
            if entry['expires'] < current_time
        ]
        
        for key in expired_keys:
            del self.cache[key]
            if key in self.access_times:
                del self.access_times[key]
            self.cache_stats['evictions'] += 1
        
        # 如果还是太多，删除最久未访问的
        if len(self.cache) >= self.max_entries:
            sorted_keys = sorted(
                self.access_times.keys(),
                key=lambda k: self.access_times[k]
            )
            
            # 删除最久未访问的25%
            keys_to_remove = sorted_keys[:len(sorted_keys)//4 + 1]
            for key in keys_to_remove:
                if key in self.cache:
                    del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                self.cache_stats['evictions'] += 1
    
    def partial_clear(self):
        """部分清理缓存"""
        keys_to_remove = list(self.cache.keys())[::2]  # 每隔一个删除
        for key in keys_to_remove:
            if key in self.cache:
                del self.cache[key]
            if key in self.access_times:
                del self.access_times[key]
        
        self.logger.info(f"部分清理缓存，删除{len(keys_to_remove)}个条目")
    
    def emergency_clear(self):
        """紧急清理所有缓存"""
        self.cache.clear()
        self.access_times.clear()
        self.logger.warning("紧急清理所有缓存")
    
    def get_stats(self) -> Dict:
        """获取缓存统计"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = self.cache_stats['hits'] / total_requests if total_requests > 0 else 0
        
        return {
            'entry_count': len(self.cache),
            'max_entries': self.max_entries,
            'hit_rate': hit_rate,
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'evictions': self.cache_stats['evictions']
        }

class SimpleMLPredictor:
    """简单机器学习预测器"""
    
    def __init__(self):
        self.trend_patterns = defaultdict(list)
        self.success_rates = defaultdict(float)
        self.market_conditions = deque(maxlen=500)  # 保留最近500个市场状态
        
        # 简单的特征权重
        self.feature_weights = {
            'rsi_trend': 0.3,
            'ma_alignment': 0.4,
            'volume_trend': 0.2,
            'volatility': 0.1
        }
        
        self.logger = logging.getLogger(__name__)
    
    def extract_simple_features(self, market_data: Dict) -> Dict:
        """提取简单特征"""
        
        features = {}
        
        # RSI趋势特征
        rsi = market_data.get('rsi', 50)
        if rsi > 70:
            features['rsi_trend'] = 'overbought'
        elif rsi < 30:
            features['rsi_trend'] = 'oversold'
        else:
            features['rsi_trend'] = 'neutral'
        
        # 均线排列特征
        current_price = market_data.get('current_price', 0)
        ma_20 = market_data.get('ma_20', current_price)
        ma_50 = market_data.get('ma_50', current_price)
        
        if current_price > ma_20 > ma_50:
            features['ma_alignment'] = 'bullish'
        elif current_price < ma_20 < ma_50:
            features['ma_alignment'] = 'bearish'
        else:
            features['ma_alignment'] = 'mixed'
        
        # 成交量趋势
        volume = market_data.get('volume', 1000)
        avg_volume = market_data.get('avg_volume', 1000)
        volume_ratio = volume / avg_volume if avg_volume > 0 else 1
        
        if volume_ratio > 1.5:
            features['volume_trend'] = 'high'
        elif volume_ratio < 0.5:
            features['volume_trend'] = 'low'
        else:
            features['volume_trend'] = 'normal'
        
        # 波动率特征
        atr = market_data.get('atr', 0.001)
        if atr > 0.002:
            features['volatility'] = 'high'
        elif atr < 0.0005:
            features['volatility'] = 'low'
        else:
            features['volatility'] = 'normal'
        
        return features
    
    def predict_trend_probability(self, market_data: Dict) -> Dict:
        """预测趋势概率"""
        
        features = self.extract_simple_features(market_data)
        feature_key = self._features_to_key(features)
        
        # 基于历史模式计算概率
        base_probability = self.success_rates.get(feature_key, 0.5)
        
        # 基于特征权重调整
        bullish_score = 0
        bearish_score = 0
        
        # RSI影响
        if features['rsi_trend'] == 'oversold':
            bullish_score += self.feature_weights['rsi_trend']
        elif features['rsi_trend'] == 'overbought':
            bearish_score += self.feature_weights['rsi_trend']
        
        # 均线影响
        if features['ma_alignment'] == 'bullish':
            bullish_score += self.feature_weights['ma_alignment']
        elif features['ma_alignment'] == 'bearish':
            bearish_score += self.feature_weights['ma_alignment']
        
        # 成交量影响
        if features['volume_trend'] == 'high':
            if bullish_score > bearish_score:
                bullish_score += self.feature_weights['volume_trend']
            else:
                bearish_score += self.feature_weights['volume_trend']
        
        # 计算最终概率
        total_score = bullish_score + bearish_score
        if total_score > 0:
            bullish_prob = bullish_score / total_score
        else:
            bullish_prob = base_probability
        
        return {
            'bullish_probability': bullish_prob,
            'bearish_probability': 1 - bullish_prob,
            'confidence': min(total_score, 1.0),
            'features_used': features,
            'base_probability': base_probability
        }
    
    def learn_from_trade_result(self, market_data: Dict, trade_result: Dict):
        """从交易结果学习"""
        
        features = self.extract_simple_features(market_data)
        feature_key = self._features_to_key(features)
        
        # 判断交易是否成功
        profit = trade_result.get('profit_loss', 0)
        is_successful = profit > 0
        
        # 更新成功率
        if feature_key not in self.trend_patterns:
            self.trend_patterns[feature_key] = []
        
        self.trend_patterns[feature_key].append(is_successful)
        
        # 保持最近50个结果
        if len(self.trend_patterns[feature_key]) > 50:
            self.trend_patterns[feature_key] = self.trend_patterns[feature_key][-50:]
        
        # 计算成功率
        successes = sum(self.trend_patterns[feature_key])
        total = len(self.trend_patterns[feature_key])
        self.success_rates[feature_key] = successes / total
        
        # 记录市场条件
        self.market_conditions.append({
            'timestamp': datetime.now(),
            'features': features,
            'result': is_successful,
            'profit': profit
        })
        
        self.logger.info(f"学习更新: {feature_key} 成功率: {self.success_rates[feature_key]:.2%}")
    
    def _features_to_key(self, features: Dict) -> str:
        """将特征转换为键"""
        return f"{features['rsi_trend']}_{features['ma_alignment']}_{features['volume_trend']}_{features['volatility']}"
    
    def get_learning_stats(self) -> Dict:
        """获取学习统计"""
        
        total_patterns = len(self.success_rates)
        avg_success_rate = sum(self.success_rates.values()) / total_patterns if total_patterns > 0 else 0
        
        # 最佳和最差模式
        best_pattern = max(self.success_rates.items(), key=lambda x: x[1]) if self.success_rates else None
        worst_pattern = min(self.success_rates.items(), key=lambda x: x[1]) if self.success_rates else None
        
        return {
            'total_patterns_learned': total_patterns,
            'avg_success_rate': avg_success_rate,
            'best_pattern': best_pattern,
            'worst_pattern': worst_pattern,
            'total_trades_analyzed': len(self.market_conditions),
            'learning_data_size_kb': sys.getsizeof(self.trend_patterns) / 1024
        }

# 创建全局实例
simple_resource_monitor = SimpleResourceMonitor()
simple_cache_manager = SimpleCacheManager(max_entries=50)  # 限制50个条目
simple_ml_predictor = SimpleMLPredictor()
