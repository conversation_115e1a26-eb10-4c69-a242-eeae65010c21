#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的训练脚本 - 避免编码问题
"""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def main():
    """主训练流程"""
    logger = setup_logging()
    
    logger.info("QuantumForex MLTrainer 开始训练")
    logger.info("=" * 60)
    
    try:
        # 导入必要模块
        logger.info("步骤1: 导入模块...")
        from feature_engineering.technical_features import technical_engine
        from feature_engineering.market_features import market_engine
        from model_training.price_prediction_trainer import price_trainer
        from utils.cloud_transfer import CloudTransferManager
        
        logger.info("模块导入成功")
        
        # 创建测试数据
        logger.info("步骤2: 创建测试数据...")
        import pandas as pd
        import numpy as np
        
        # 创建模拟的外汇数据
        dates = pd.date_range('2024-01-01', periods=1000, freq='1min')
        np.random.seed(42)
        
        # 创建更真实的价格数据
        price_changes = np.random.randn(1000) * 0.0001
        prices = 1.1000 + np.cumsum(price_changes)
        
        test_data = pd.DataFrame({
            'close': prices,
            'open': prices + np.random.randn(1000) * 0.00005,
            'high': prices + np.abs(np.random.randn(1000)) * 0.0001,
            'low': prices - np.abs(np.random.randn(1000)) * 0.0001,
            'volume': np.random.randint(1000, 10000, 1000)
        }, index=dates)
        
        # 确保OHLC逻辑正确
        test_data['high'] = np.maximum(test_data[['open', 'close']].max(axis=1), test_data['high'])
        test_data['low'] = np.minimum(test_data[['open', 'close']].min(axis=1), test_data['low'])
        
        logger.info(f"测试数据创建完成: {len(test_data)}条记录")
        
        # 特征工程
        logger.info("步骤3: 特征工程...")
        
        # 技术指标特征
        df_with_tech = technical_engine.generate_all_features(test_data.copy())
        tech_features = technical_engine.get_feature_names()
        logger.info(f"技术指标特征: {len(tech_features)}个")
        
        # 市场特征
        df_with_market = market_engine.generate_all_features(df_with_tech)
        market_features = market_engine.get_feature_names()
        logger.info(f"市场特征: {len(market_features)}个")
        
        # 合并特征名称
        all_features = tech_features + market_features
        logger.info(f"总特征数: {len(all_features)}个")
        
        # 模型训练
        logger.info("步骤4: 模型训练...")
        
        # 确保有足够的数据
        if len(df_with_market) > 100:
            # 训练价格方向预测模型
            results = price_trainer.train_price_direction_model(
                df_with_market, all_features, horizon=5, threshold=0.0001
            )
            
            if results:
                logger.info("价格方向预测模型训练完成")
                
                # 获取最佳模型
                best_model_name, best_model = price_trainer.get_best_model(results, 'f1_score')
                if best_model_name:
                    logger.info(f"最佳模型: {best_model_name}")
            else:
                logger.warning("模型训练失败")
        else:
            logger.warning("数据量不足，跳过模型训练")
        
        # 模型上传
        logger.info("步骤5: 模型上传...")
        
        try:
            # 创建云传输管理器
            cloud_transfer = CloudTransferManager()
            
            # 测试连接
            if cloud_transfer.test_connection():
                logger.info("云服务器连接正常")
                
                # 上传所有训练好的模型
                upload_result = cloud_transfer.upload_all_models()
                
                if upload_result['success']:
                    logger.info(f"模型上传成功: {upload_result['uploaded_count']}个模型")
                else:
                    logger.warning(f"模型上传部分失败: {upload_result}")
            else:
                logger.warning("云服务器连接失败，模型将保存在本地")
        
        except Exception as e:
            logger.error(f"模型上传失败: {e}")
        
        # 生成训练报告
        logger.info("步骤6: 生成训练报告...")
        
        training_summary = {
            'timestamp': datetime.now().isoformat(),
            'data_records': len(test_data),
            'features_generated': len(all_features),
            'models_trained': len(price_trainer.models),
            'training_history': price_trainer.training_history
        }
        
        # 保存训练报告
        report_file = f"training_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path = Path("logs") / report_file
        report_path.parent.mkdir(exist_ok=True)
        
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(training_summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"训练报告已保存: {report_path}")
        
        logger.info("所有训练步骤完成！")
        logger.info("=" * 60)
        logger.info("训练摘要:")
        logger.info(f"  - 数据记录: {len(test_data)}")
        logger.info(f"  - 生成特征: {len(all_features)}")
        logger.info(f"  - 训练模型: {len(price_trainer.models)}")
        logger.info(f"  - 报告文件: {report_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"训练失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    input("\n按任意键退出...")
    sys.exit(0 if success else 1)
