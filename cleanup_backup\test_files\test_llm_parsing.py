"""
测试LLM回答解析逻辑
"""
import sys
from datetime import datetime
from app.utils.multi_round_analysis import should_perform_analysis

# 模拟分析数据
mock_data = {
    'symbol': 'EURUSD',
    'timeframe15m': [
        {'time': '2025-05-12 09:00:00', 'open': 1.0750, 'high': 1.0755, 'low': 1.0748, 'close': 1.0752, 'volume': 100},
        {'time': '2025-05-12 09:15:00', 'open': 1.0752, 'high': 1.0758, 'low': 1.0750, 'close': 1.0755, 'volume': 120}
    ],
    'indicators': {
        'rsi': 52.5,
        'macd': {
            'macdLine': [0.0002],
            'signalLine': [0.0001],
            'histogram': [0.0001]
        },
        'ma': {
            5: 1.0753,
            10: 1.0748,
            20: 1.0740,
            50: 1.0730
        }
    },
    'positions': []
}

# 测试不同格式的LLM回答
test_answers = [
    # 标准格式
    """
分析决定: 否，市场波动较小，没有明显的交易信号
下次分析间隔: 30分钟
    """,
    
    # 变体1：不同的标签
    """
需要进行完整分析: 是，价格突破了前期高点，可能有交易机会
建议的下次分析间隔: 15分钟
    """,
    
    # 变体2：不同的格式
    """
1. 是否需要进行完整分析：是，RSI指标显示超卖状态
2. 下次分析的时间间隔：10分钟
    """,
    
    # 变体3：更自由的格式
    """
根据当前市场情况，我认为不需要进行完整分析，因为市场波动很小。
建议30分钟后再次检查市场情况。
    """,
    
    # 变体4：带有额外解释
    """
分析决定: 是
市场出现了明显的价格突破，MACD指标也显示上升趋势，建议进行完整分析以捕捉潜在的交易机会。

下次分析间隔: 5分钟
考虑到市场波动较大，建议较短的分析间隔以便及时捕捉市场变化。
    """,
    
    # 变体5：非常简短
    """
是，价格突破
15分钟
    """,
    
    # 变体6：完全不符合格式
    """
当前市场状况相对平静，没有明显的交易信号。价格变化很小，技术指标也没有显示明确的方向。
    """
]

def test_parsing():
    """测试解析逻辑"""
    print("开始测试LLM回答解析逻辑...")
    print("=" * 50)
    
    for i, answer in enumerate(test_answers):
        print(f"\n测试案例 {i+1}:")
        print("-" * 30)
        print(f"LLM回答:\n{answer}")
        print("-" * 30)
        
        # 模拟LLM响应
        mock_response = {
            'choices': [
                {
                    'message': {
                        'content': answer
                    }
                }
            ]
        }
        
        # 保存原始的sys.stdout
        original_stdout = sys.stdout
        
        try:
            # 重定向stdout到一个临时文件
            with open('temp_output.txt', 'w') as f:
                sys.stdout = f
                
                # 调用解析函数
                from app.utils import llm_client
                llm_client.send_to_deepseek = lambda *args, **kwargs: mock_response
                should_analyze, reason, next_interval = should_perform_analysis(mock_data)
        finally:
            # 恢复原始的stdout
            sys.stdout = original_stdout
        
        # 打印解析结果
        print(f"解析结果:")
        print(f"  需要分析: {should_analyze}")
        print(f"  原因: {reason}")
        print(f"  下次间隔: {next_interval}分钟")
        print("=" * 50)

if __name__ == "__main__":
    test_parsing()
