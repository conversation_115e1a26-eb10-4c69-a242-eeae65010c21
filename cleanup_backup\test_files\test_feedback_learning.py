#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易结果反馈学习系统
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_feedback_learning_system():
    """测试反馈学习系统"""
    print("🧠 交易结果反馈学习系统测试")
    print("=" * 60)
    
    try:
        # 1. 测试模块导入
        print("📦 测试模块导入...")
        from app.core.feedback_learning_system import (
            FeedbackLearningSystem, LearningType, FeedbackCategory
        )
        print("   ✅ 反馈学习系统模块导入成功")
        
        # 2. 测试系统初始化
        print("\n🔧 测试系统初始化...")
        learning_system = FeedbackLearningSystem()
        print("   ✅ 反馈学习系统初始化成功")
        
        # 3. 测试交易结果记录
        print("\n📊 测试交易结果记录...")
        
        # 模拟交易数据
        test_trades = [
            {
                'trade_data': {
                    'trade_id': 'trade_001',
                    'entry_time': (datetime.now() - timedelta(hours=2)).isoformat(),
                    'exit_time': datetime.now().isoformat(),
                    'symbol': 'EURUSD',
                    'action': 'BUY',
                    'entry_price': 1.1300,
                    'exit_price': 1.1350,
                    'stop_loss': 1.1250,
                    'take_profit': 1.1400,
                    'lot_size': 0.1,
                    'profit_loss': 500,
                    'profit_loss_pct': 0.044,
                    'holding_duration_seconds': 7200,
                    'exit_reason': 'take_profit'
                },
                'original_analysis': {
                    'llm_analysis': {
                        'action': 'BUY',
                        'reasoning': '技术指标显示强烈上升趋势，确信看多'
                    },
                    'market_data': {
                        'current_price': 1.1300,
                        'atr': 0.0015
                    },
                    'signal_quality': {
                        'grade': 'A',
                        'risk_reward_ratio': 2.0
                    },
                    'market_condition': {
                        'regime': 'BULL'
                    }
                }
            },
            {
                'trade_data': {
                    'trade_id': 'trade_002',
                    'entry_time': (datetime.now() - timedelta(hours=1)).isoformat(),
                    'exit_time': datetime.now().isoformat(),
                    'symbol': 'EURUSD',
                    'action': 'SELL',
                    'entry_price': 1.1300,
                    'exit_price': 1.1250,
                    'stop_loss': 1.1350,
                    'take_profit': 1.1200,
                    'lot_size': 0.05,
                    'profit_loss': -250,
                    'profit_loss_pct': -0.044,
                    'holding_duration_seconds': 1800,
                    'exit_reason': 'stop_loss'
                },
                'original_analysis': {
                    'llm_analysis': {
                        'action': 'SELL',
                        'reasoning': '可能下跌，但信号不够明确'
                    },
                    'market_data': {
                        'current_price': 1.1300,
                        'atr': 0.0020
                    },
                    'signal_quality': {
                        'grade': 'C',
                        'risk_reward_ratio': 1.0
                    },
                    'market_condition': {
                        'regime': 'SIDEWAYS'
                    }
                }
            }
        ]
        
        # 记录交易结果
        for i, trade in enumerate(test_trades, 1):
            trade_analysis = learning_system.record_trade_result(
                trade['trade_data'], trade['original_analysis']
            )
            
            if trade_analysis:
                print(f"   ✅ 交易{i}记录成功")
                print(f"      预测准确性: {trade_analysis.prediction_accuracy:.2f}")
                print(f"      时机准确性: {trade_analysis.timing_accuracy:.2f}")
                print(f"      风险管理有效性: {trade_analysis.risk_management_effectiveness:.2f}")
                print(f"      综合表现评分: {trade_analysis.overall_performance_score:.2f}")
            else:
                print(f"   ❌ 交易{i}记录失败")
        
        # 4. 测试预测准确性分析
        print("\n🎯 测试预测准确性分析...")
        accuracy_analysis = learning_system.analyze_prediction_accuracy()
        
        if accuracy_analysis.get('status') != 'insufficient_data':
            print(f"   整体准确性: {accuracy_analysis['overall_accuracy']:.2%}")
            print(f"   方向准确性: {accuracy_analysis['direction_accuracy']:.2%}")
            print(f"   时机准确性: {accuracy_analysis['timing_accuracy']:.2%}")
            print(f"   幅度准确性: {accuracy_analysis['magnitude_accuracy']:.2%}")
            print(f"   风险评估准确性: {accuracy_analysis['risk_assessment_accuracy']:.2%}")
            print(f"   样本数量: {accuracy_analysis['sample_size']}")
        else:
            print(f"   数据不足，当前样本数: {accuracy_analysis['sample_size']}")
        
        # 5. 添加更多模拟数据以测试模式识别
        print("\n📈 添加更多模拟数据...")
        
        # 生成更多模拟交易数据
        for i in range(15):
            # 模拟不同类型的交易
            if i % 3 == 0:  # 成功的趋势交易
                trade_data = {
                    'trade_id': f'trade_{i+3:03d}',
                    'entry_time': (datetime.now() - timedelta(hours=i+1)).isoformat(),
                    'exit_time': (datetime.now() - timedelta(hours=i)).isoformat(),
                    'symbol': 'EURUSD',
                    'action': 'BUY',
                    'entry_price': 1.1300 + i * 0.001,
                    'exit_price': 1.1300 + i * 0.001 + 0.005,
                    'stop_loss': 1.1300 + i * 0.001 - 0.005,
                    'take_profit': 1.1300 + i * 0.001 + 0.010,
                    'lot_size': 0.1,
                    'profit_loss': 500,
                    'profit_loss_pct': 0.044,
                    'holding_duration_seconds': 3600,
                    'exit_reason': 'take_profit'
                }
                original_analysis = {
                    'llm_analysis': {'action': 'BUY', 'reasoning': '强趋势信号'},
                    'signal_quality': {'grade': 'A', 'risk_reward_ratio': 2.0},
                    'market_condition': {'regime': 'BULL'}
                }
            elif i % 3 == 1:  # 失败的震荡交易
                trade_data = {
                    'trade_id': f'trade_{i+3:03d}',
                    'entry_time': (datetime.now() - timedelta(hours=i+1)).isoformat(),
                    'exit_time': (datetime.now() - timedelta(hours=i)).isoformat(),
                    'symbol': 'EURUSD',
                    'action': 'SELL',
                    'entry_price': 1.1300,
                    'exit_price': 1.1320,
                    'stop_loss': 1.1330,
                    'take_profit': 1.1250,
                    'lot_size': 0.05,
                    'profit_loss': -100,
                    'profit_loss_pct': -0.018,
                    'holding_duration_seconds': 1200,
                    'exit_reason': 'stop_loss'
                }
                original_analysis = {
                    'llm_analysis': {'action': 'SELL', 'reasoning': '震荡市场信号'},
                    'signal_quality': {'grade': 'C', 'risk_reward_ratio': 1.5},
                    'market_condition': {'regime': 'SIDEWAYS'}
                }
            else:  # 中等表现交易
                trade_data = {
                    'trade_id': f'trade_{i+3:03d}',
                    'entry_time': (datetime.now() - timedelta(hours=i+1)).isoformat(),
                    'exit_time': (datetime.now() - timedelta(hours=i)).isoformat(),
                    'symbol': 'EURUSD',
                    'action': 'BUY',
                    'entry_price': 1.1300,
                    'exit_price': 1.1310,
                    'stop_loss': 1.1280,
                    'take_profit': 1.1340,
                    'lot_size': 0.08,
                    'profit_loss': 80,
                    'profit_loss_pct': 0.009,
                    'holding_duration_seconds': 2400,
                    'exit_reason': 'manual'
                }
                original_analysis = {
                    'llm_analysis': {'action': 'BUY', 'reasoning': '中等信号'},
                    'signal_quality': {'grade': 'B', 'risk_reward_ratio': 1.8},
                    'market_condition': {'regime': 'WEAK_BULL'}
                }
            
            learning_system.record_trade_result(trade_data, original_analysis)
        
        print(f"   ✅ 添加了15笔模拟交易数据")
        
        # 6. 测试表现模式识别
        print("\n🔍 测试表现模式识别...")
        patterns = learning_system.identify_performance_patterns()
        
        print(f"   识别到{len(patterns)}个表现模式:")
        for pattern in patterns[:5]:  # 显示前5个模式
            print(f"   - {pattern.pattern_name}")
            print(f"     胜率: {pattern.success_rate:.1%}")
            print(f"     平均收益: {pattern.avg_return:.2%}")
            print(f"     样本数: {pattern.sample_size}")
            print(f"     模式强度: {pattern.pattern_strength:.2f}")
        
        # 7. 测试学习洞察生成
        print("\n💡 测试学习洞察生成...")
        insights = learning_system.generate_learning_insights()
        
        print(f"   生成了{len(insights)}个学习洞察:")
        for insight in insights[:3]:  # 显示前3个洞察
            print(f"   - {insight.insight_type.value}: {insight.pattern_description}")
            print(f"     置信度: {insight.confidence_level:.1%}")
            print(f"     优先级: {insight.implementation_priority}")
            print(f"     建议调整: {insight.recommended_adjustments}")
        
        # 8. 测试LLM反馈提示词生成
        print("\n📝 测试LLM反馈提示词生成...")
        feedback_prompt = learning_system.generate_llm_feedback_prompt(10)
        
        print("   生成的反馈提示词:")
        print(f"   长度: {len(feedback_prompt)}字符")
        print("   内容预览:")
        print(feedback_prompt[:300] + "..." if len(feedback_prompt) > 300 else feedback_prompt)
        
        # 9. 测试统计功能
        print("\n📊 测试统计功能...")
        
        # 学习统计
        learning_stats = learning_system.get_learning_statistics()
        print(f"   学习统计:")
        print(f"     分析交易数: {learning_stats['total_trades_analyzed']}")
        print(f"     学习周期: {learning_stats['learning_cycles_completed']}")
        print(f"     活跃洞察: {learning_stats['active_insights']}")
        print(f"     识别模式: {learning_stats['identified_patterns']}")
        
        # 表现总结
        performance_summary = learning_system.get_performance_summary(30)
        if performance_summary.get('status') != 'no_recent_trades':
            print(f"   表现总结(30天):")
            print(f"     总交易数: {performance_summary['total_trades']}")
            print(f"     胜率: {performance_summary['win_rate']:.1%}")
            print(f"     总收益: {performance_summary['total_return']:.2%}")
            print(f"     夏普比率: {performance_summary['sharpe_ratio']:.2f}")
            print(f"     最大回撤: {performance_summary['max_drawdown']:.2%}")
        
        # 10. 测试数据导出
        print("\n💾 测试数据导出...")
        export_result = learning_system.export_learning_data()
        
        if export_result['status'] == 'success':
            print(f"   ✅ 数据导出成功: {export_result['filepath']}")
        else:
            print(f"   ❌ 数据导出失败: {export_result['error']}")
        
        print("\n🎉 交易结果反馈学习系统测试完成！")
        print("   ✅ 所有核心功能正常工作")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_feedback_learning_summary():
    """显示反馈学习系统总结"""
    print("\n📋 交易结果反馈学习系统总结")
    print("=" * 50)
    
    print("🎯 第四阶段完成：交易结果反馈学习机制")
    print("   ✅ 创建了交易结果反馈学习系统")
    print("   ✅ 实现了预测准确性分析")
    print("   ✅ 集成了表现模式识别")
    print("   ✅ 添加了学习洞察生成")
    print("   ✅ 实现了LLM反馈提示词生成")
    print("   ✅ 集成了统计分析和数据导出")
    
    print("\n🔄 系统改进效果：")
    print("   - 学习能力：从静态 → 动态自我改进")
    print("   - 错误分析：从忽略 → 系统性分析和纠正")
    print("   - 模式识别：从经验 → 数据驱动模式发现")
    print("   - 参数优化：从固定 → 基于表现动态调整")
    
    print("\n📈 预期收益提升：")
    print("   - 持续改进：通过学习不断提高分析质量")
    print("   - 错误减少：识别和纠正常见分析错误")
    print("   - 模式利用：发现和利用成功交易模式")
    print("   - 参数优化：基于实际表现优化系统参数")
    
    print("\n🔧 技术实现亮点：")
    print("   - 全面交易分析：预测准确性、时机、风险管理")
    print("   - 智能模式识别：市场条件、信号质量、时间等模式")
    print("   - 学习洞察生成：自动识别改进机会")
    print("   - LLM反馈循环：生成针对性改进建议")
    
    print("\n🚀 下一步优化方向：")
    print("   1. 多货币对组合管理")
    print("   2. 高级策略优化")
    print("   3. 机器学习模型集成")
    print("   4. 实时自适应学习")

if __name__ == "__main__":
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始交易结果反馈学习系统测试")
    
    # 执行反馈学习系统测试
    success = test_feedback_learning_system()
    
    if success:
        # 显示系统总结
        show_feedback_learning_summary()
        
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🎉 第四阶段优化完成！")
        print("交易结果反馈学习系统已成功创建，系统具备了自我学习和改进能力。")
    else:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ 测试失败，请检查系统配置。")
