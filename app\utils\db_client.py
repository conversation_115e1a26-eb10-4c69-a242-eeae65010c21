"""
数据库客户端工具
用于连接和查询pizza_quotes数据库
"""
import os
import pymysql
import pandas as pd
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('PIZZA_QUOTES_DB_HOST', 'pizza-wnet-db1.mysql.rds.aliyuncs.com'),
    'port': int(os.getenv('PIZZA_QUOTES_DB_PORT', '6688')),
    'user': os.getenv('PIZZA_QUOTES_DB_USER', 'ea_quote_srv'),
    'password': os.getenv('PIZZA_QUOTES_DB_PASSWORD', 'pizza666!'),
    'database': os.getenv('PIZZA_QUOTES_DB_NAME', 'pizza_quotes'),
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}


def get_connection():
    """
    获取数据库连接

    Returns:
        pymysql.connections.Connection: 数据库连接
    """
    return pymysql.connect(**DB_CONFIG)


def test_connection():
    """
    测试数据库连接

    Returns:
        bool: 连接是否成功
    """
    try:
        connection = get_connection()
        with connection.cursor() as cursor:
            cursor.execute('SELECT 1')
            result = cursor.fetchone()
            if result and result.get('1') == 1:
                print('pizza_quotes数据库连接成功！')
                return True
            else:
                print('pizza_quotes数据库连接失败：查询结果异常')
                return False
    except Exception as error:
        print(f'pizza_quotes数据库连接失败：{error}')
        return False
    finally:
        if 'connection' in locals() and connection:
            connection.close()


def execute_query(sql, params=None):
    """
    执行SQL查询

    Args:
        sql (str): SQL语句
        params (tuple, optional): 查询参数

    Returns:
        list: 查询结果
    """
    try:
        # 不输出SQL查询内容，只记录查询执行
        connection = get_connection()
        with connection.cursor() as cursor:
            cursor.execute(sql, params or ())
            result = cursor.fetchall()
            return result
    except Exception as error:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 数据库: 错误: SQL查询失败')
        raise
    finally:
        if 'connection' in locals() and connection:
            connection.close()


def get_eurusd_min_data(start_time=None, end_time=None, limit=1000):
    """
    获取EURUSD的1分钟数据

    Args:
        start_time (str, optional): 开始时间
        end_time (str, optional): 结束时间
        limit (int, optional): 限制条数

    Returns:
        list: EURUSD分钟数据
    """
    try:
        # 允许更大的数据量，但设置一个合理的上限以防止查询过大
        actual_limit = min(10000, int(limit))  # 将上限提高到10000条

        sql = """
            SELECT
                time_date_str as time,
                price as open,
                max as high,
                min as low,
                price as close,
                volume
            FROM min_quote_eurusd
            WHERE 1=1
        """

        params = []

        if start_time:
            sql += " AND time_date_str >= %s"
            params.append(start_time)

        if end_time:
            sql += " AND time_date_str <= %s"
            params.append(end_time)

        # 按时间倒序排列，确保获取最新的数据
        sql += f" ORDER BY time_min_int DESC LIMIT {actual_limit}"

        result = execute_query(sql, params)

        if result and len(result) > 0:
            # 检查数据是否按时间倒序排列
            is_desc_order = all(result[i]["time"] >= result[i+1]["time"] for i in range(len(result)-1))

            # 转换数据格式以适应前端需求
            formatted_result = []
            for item in result:
                # 将时间格式从 2025.05.12 19:54 转换为 2025-05-12 19:54:00
                time_str = item['time']
                try:
                    # 尝试解析时间，支持多种格式
                    try:
                        dt = datetime.strptime(time_str, "%Y.%m.%d %H:%M")
                        time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        # 如果已经是标准格式，保持不变
                        if not time_str.endswith(":00") and ":" in time_str:
                            time_str = time_str + ":00"
                except Exception as e:
                    # 不输出具体错误信息
                    pass

                formatted_result.append({
                    'time': time_str,
                    'open': float(item['open']),
                    'high': float(item['high']),
                    'low': float(item['low']),
                    'close': float(item['close']),
                    'volume': item['volume']
                })

            # 检查是否有重复的时间戳
            time_values = [item['time'] for item in formatted_result]
            unique_times = set(time_values)

            return formatted_result
        else:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 数据库: 错误: EURUSD分钟数据为空')
            raise Exception('min_quote_eurusd表中没有数据')
    except Exception as error:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 数据库: 错误: 获取EURUSD分钟数据失败')
        import traceback
        traceback.print_exc()
        raise


def get_latest_news(limit=20):
    """
    获取最新的新闻数据

    Args:
        limit (int, optional): 限制条数

    Returns:
        list: 新闻数据
    """
    try:
        # 使用硬编码的LIMIT值
        actual_limit = min(100, int(limit))

        # 使用正确的字段名称查询jin10_flash_news表
        sql = f"""
            SELECT
                id,
                news_time as time,
                content,
                importance,
                news_type,
                source
            FROM jin10_flash_news
            ORDER BY news_time DESC
            LIMIT {actual_limit}
        """

        result = execute_query(sql)

        if result and len(result) > 0:
            # 转换数据格式以适应前端需求
            formatted_result = []
            for item in result:
                formatted_result.append({
                    'id': item['id'],
                    'time': item['time'].strftime('%Y-%m-%d %H:%M:%S') if item['time'] else '',
                    'content': item['content'] or '',
                    'importance': item['importance'] or 'MEDIUM',
                    'type': item['news_type'] or '',
                    'source': item['source'] or ''
                })

            return formatted_result
        else:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 数据库: 错误: 新闻数据为空')
            raise Exception('jin10_flash_news表中没有数据')
    except Exception as error:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 数据库: 错误: 获取新闻数据失败')
        raise


def get_latest_calendar(limit=20):
    """
    获取最新的日历事件数据

    Args:
        limit (int, optional): 限制条数

    Returns:
        list: 日历事件数据
    """
    try:
        # 使用硬编码的LIMIT值
        actual_limit = min(100, int(limit))

        # 使用正确的字段名称查询jin10_calendar表
        sql = f"""
            SELECT
                id,
                event_time as time,
                currency as country,
                event_name as title,
                actual,
                forecast,
                previous,
                importance
            FROM jin10_calendar
            ORDER BY event_time DESC
            LIMIT {actual_limit}
        """

        result = execute_query(sql)

        if result and len(result) > 0:
            # 转换数据格式以适应前端需求
            formatted_result = []
            for item in result:
                formatted_result.append({
                    'id': item['id'],
                    'time': item['time'].strftime('%Y-%m-%d %H:%M:%S') if item['time'] else '',
                    'country': item['country'] or '',
                    'title': item['title'] or '',
                    'actual': item['actual'] or '',
                    'forecast': item['forecast'] or '',
                    'previous': item['previous'] or '',
                    'importance': item['importance'] or 'MEDIUM'
                })

            return formatted_result
        else:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 数据库: 错误: 日历数据为空')
            raise Exception('jin10_calendar表中没有数据')
    except Exception as error:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 数据库: 错误: 获取日历数据失败')
        raise
