#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统错误修复脚本
自动检测和修复常见的系统错误
"""

import os
import sys
import json
import time
import subprocess
from datetime import datetime

def fix_json_files():
    """修复损坏的JSON文件"""
    print("🔧 修复JSON文件...")
    
    json_files = [
        'app/data/forex_analysis_history.json',
        'app/data/token_statistics.json',
        'app/data/trade_results.json'
    ]
    
    for file_path in json_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查JSON是否有效
                json.loads(content)
                print(f"  ✅ {file_path} - JSON格式正确")
                
            except json.JSONDecodeError as e:
                print(f"  ❌ {file_path} - JSON格式错误: {e}")
                
                # 尝试修复
                try:
                    # 基本修复：补全缺失的括号
                    fixed_content = content.strip()
                    
                    # 计算括号平衡
                    open_braces = fixed_content.count('{')
                    close_braces = fixed_content.count('}')
                    open_brackets = fixed_content.count('[')
                    close_brackets = fixed_content.count(']')
                    
                    # 补全缺失的括号
                    if open_braces > close_braces:
                        fixed_content += '}' * (open_braces - close_braces)
                    if open_brackets > close_brackets:
                        fixed_content += ']' * (open_brackets - close_brackets)
                    
                    # 验证修复后的JSON
                    json.loads(fixed_content)
                    
                    # 备份原文件
                    backup_path = f"{file_path}.backup.{int(time.time())}"
                    with open(backup_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    # 写入修复后的内容
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(fixed_content)
                    
                    print(f"  ✅ {file_path} - 修复成功，原文件备份为 {backup_path}")
                    
                except Exception as fix_error:
                    print(f"  ❌ {file_path} - 修复失败: {fix_error}")
                    
                    # 创建空的有效JSON文件
                    if 'history' in file_path:
                        default_content = '{"analysis_history": []}'
                    elif 'statistics' in file_path:
                        default_content = '{"token_usage": {}}'
                    elif 'results' in file_path:
                        default_content = '{"trades": []}'
                    else:
                        default_content = '{}'
                    
                    backup_path = f"{file_path}.broken.{int(time.time())}"
                    with open(backup_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(default_content)
                    
                    print(f"  ⚠️ {file_path} - 创建新的空文件，损坏文件备份为 {backup_path}")
        else:
            print(f"  ⚠️ {file_path} - 文件不存在，跳过")

def check_port_conflicts():
    """检查端口冲突"""
    print("\n🔍 检查端口冲突...")
    
    ports_to_check = [5000, 5555, 5558]
    
    for port in ports_to_check:
        try:
            result = subprocess.run(
                ['netstat', '-ano'], 
                capture_output=True, 
                text=True, 
                shell=True
            )
            
            if f":{port}" in result.stdout:
                print(f"  ⚠️ 端口 {port} 被占用")
                # 显示占用进程
                lines = result.stdout.split('\n')
                for line in lines:
                    if f":{port}" in line and "LISTENING" in line:
                        print(f"    {line.strip()}")
            else:
                print(f"  ✅ 端口 {port} 可用")
                
        except Exception as e:
            print(f"  ❌ 检查端口 {port} 失败: {e}")

def clean_log_files():
    """清理过大的日志文件"""
    print("\n🧹 清理日志文件...")
    
    log_files = [
        'logs/error_log.json',
        'logs/operation_log.json'
    ]
    
    max_size_mb = 10  # 最大10MB
    max_size_bytes = max_size_mb * 1024 * 1024
    
    for file_path in log_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            size_mb = file_size / (1024 * 1024)
            
            if file_size > max_size_bytes:
                print(f"  ⚠️ {file_path} 过大 ({size_mb:.1f}MB)，进行清理...")
                
                try:
                    # 备份文件
                    backup_path = f"{file_path}.backup.{int(time.time())}"
                    os.rename(file_path, backup_path)
                    
                    # 创建新的空日志文件
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write('[]')
                    
                    print(f"    ✅ 清理完成，原文件备份为 {backup_path}")
                    
                except Exception as e:
                    print(f"    ❌ 清理失败: {e}")
            else:
                print(f"  ✅ {file_path} 大小正常 ({size_mb:.1f}MB)")
        else:
            print(f"  ⚠️ {file_path} 不存在")

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        'flask',
        'pandas',
        'numpy',
        'requests',
        'zmq',
        'mysql-connector-python'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package} - 已安装")
        except ImportError:
            print(f"  ❌ {package} - 未安装")
            print(f"    建议运行: pip install {package}")

def fix_permissions():
    """修复文件权限"""
    print("\n🔐 检查文件权限...")
    
    directories_to_check = [
        'app/data',
        'logs',
        'appdata'
    ]
    
    for dir_path in directories_to_check:
        if os.path.exists(dir_path):
            try:
                # 检查是否可写
                test_file = os.path.join(dir_path, 'test_write.tmp')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                print(f"  ✅ {dir_path} - 权限正常")
            except Exception as e:
                print(f"  ❌ {dir_path} - 权限问题: {e}")
        else:
            print(f"  ⚠️ {dir_path} - 目录不存在，创建中...")
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"    ✅ 目录创建成功")
            except Exception as e:
                print(f"    ❌ 目录创建失败: {e}")

def main():
    """主修复流程"""
    print("🚀 系统错误修复脚本")
    print("=" * 50)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 执行各项修复
    fix_json_files()
    check_port_conflicts()
    clean_log_files()
    check_dependencies()
    fix_permissions()
    
    print("\n" + "=" * 50)
    print("🎉 系统错误修复完成")
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    print("\n💡 建议:")
    print("1. 重启系统以应用修复")
    print("2. 检查系统日志确认问题已解决")
    print("3. 如果问题持续，请查看备份文件进行进一步诊断")

if __name__ == "__main__":
    main()
