"""
多货币对数据管理器
支持多个货币对的数据获取、技术指标计算和风险评估
"""

import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import traceback
from app.utils.data_source_adapter import DataSourceAdapter
from app.utils.technical_indicators import calculate_indicators
from app.utils.logger_manager import log_analysis, LogLevel
from app.utils.error_logger import log_error, ErrorType, OperationType

class MultiPairDataManager:
    """多货币对数据管理器"""
    
    def __init__(self):
        self.data_adapter = DataSourceAdapter()
        self.supported_symbols = [
            'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 
            'USDCHF', 'USDCAD', 'USDJPY', 'GOLD'
        ]
        
    def get_multi_pair_data(self, symbols: List[str], primary_symbol: str = None) -> Dict:
        """
        获取多货币对数据
        
        Args:
            symbols: 货币对列表
            primary_symbol: 主要货币对（用于MT4价格获取）
            
        Returns:
            Dict: 包含所有货币对数据的字典
        """
        try:
            if not symbols:
                symbols = ['EURUSD']
            
            if primary_symbol is None:
                primary_symbol = symbols[0]
                
            log_analysis(f"开始获取多货币对数据: {symbols}", LogLevel.INFO)
            log_analysis(f"主要货币对: {primary_symbol}", LogLevel.INFO)
            
            # 初始化结果
            result = {
                'symbols': symbols,
                'primary_symbol': primary_symbol,
                'pairs_data': {},
                'correlation_matrix': {},
                'portfolio_risk': {},
                'timestamp': datetime.now().isoformat()
            }
            
            # 获取每个货币对的数据
            for symbol in symbols:
                try:
                    pair_data = self._get_single_pair_data(symbol)
                    if pair_data:
                        result['pairs_data'][symbol] = pair_data
                        log_analysis(f"成功获取{symbol}数据", LogLevel.INFO)
                    else:
                        log_analysis(f"获取{symbol}数据失败", LogLevel.WARNING)
                        
                except Exception as e:
                    log_analysis(f"获取{symbol}数据异常: {e}", LogLevel.ERROR)
                    continue
            
            # 检查是否至少有一个货币对的数据
            if not result['pairs_data']:
                log_analysis("没有成功获取任何货币对数据", LogLevel.ERROR)
                return None
            
            # 如果主要货币对数据获取失败，选择第一个可用的
            if primary_symbol not in result['pairs_data']:
                available_symbols = list(result['pairs_data'].keys())
                if available_symbols:
                    result['primary_symbol'] = available_symbols[0]
                    log_analysis(f"主要货币对数据不可用，切换到: {result['primary_symbol']}", LogLevel.WARNING)
                else:
                    return None
            
            # 计算相关性矩阵（如果有多个货币对）
            if len(result['pairs_data']) > 1:
                result['correlation_matrix'] = self._calculate_correlation_matrix(result['pairs_data'])
            
            # 计算组合风险
            result['portfolio_risk'] = self._calculate_portfolio_risk(result['pairs_data'])
            
            log_analysis(f"多货币对数据获取完成，成功获取{len(result['pairs_data'])}个货币对", LogLevel.INFO)
            return result
            
        except Exception as e:
            log_analysis(f"多货币对数据获取失败: {e}", LogLevel.ERROR)
            log_error(
                error_type=ErrorType.DATA_ERROR,
                message=f'多货币对数据获取失败: {e}',
                details={'symbols': symbols, 'exception': str(e)},
                operation=OperationType.DATA_FETCH
            )
            return None
    
    def _get_single_pair_data(self, symbol: str) -> Optional[Dict]:
        """
        获取单个货币对的完整数据
        
        Args:
            symbol: 货币对符号
            
        Returns:
            Dict: 货币对数据，包括K线、技术指标等
        """
        try:
            # 获取15分钟数据
            data_15m = self.data_adapter.get_timeframe_data(symbol, '15min', 100)
            if not data_15m or len(data_15m) < 20:
                log_analysis(f"{symbol}的15分钟数据不足", LogLevel.WARNING)
                return None
            
            # 获取1小时数据
            data_1h = self.data_adapter.get_timeframe_data(symbol, '60min', 50)
            if not data_1h or len(data_1h) < 10:
                log_analysis(f"{symbol}的1小时数据不足", LogLevel.WARNING)
                # 尝试从15分钟数据聚合
                data_1h = self._aggregate_to_hourly(data_15m)
            
            if not data_1h:
                log_analysis(f"{symbol}无法获取1小时数据", LogLevel.WARNING)
                return None
            
            # 计算技术指标
            indicators_15m = self._calculate_indicators_safe(data_15m, '15min', symbol)
            indicators_1h = self._calculate_indicators_safe(data_1h, '1h', symbol)
            
            # 获取当前价格
            current_price = self._get_current_price(symbol, data_15m)
            
            # 计算额外的市场指标
            market_metrics = self._calculate_market_metrics(data_15m, data_1h)
            
            return {
                'symbol': symbol,
                'current_price': current_price,
                'timeframe_15m': data_15m,
                'timeframe_1h': data_1h,
                'indicators_15m': indicators_15m,
                'indicators_1h': indicators_1h,
                'market_metrics': market_metrics,
                'data_quality': self._assess_data_quality(data_15m, data_1h),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            log_analysis(f"获取{symbol}数据失败: {e}", LogLevel.ERROR)
            return None
    
    def _calculate_indicators_safe(self, data: List[Dict], timeframe: str, symbol: str) -> Dict:
        """安全计算技术指标"""
        try:
            # 这里需要导入并使用现有的技术指标计算函数
            # 由于循环导入问题，我们使用简化版本
            if not data or len(data) < 5:
                return {'error': 'insufficient_data', 'timeframe': timeframe}
            
            indicators = {
                'timeframe': timeframe,
                'symbol': symbol,
                'data_available': True,
                'kline_count': len(data)
            }
            
            # 计算基本指标
            try:
                # 移动平均线
                if len(data) >= 20:
                    closes = [float(d['close']) for d in data]
                    ma20 = sum(closes[-20:]) / 20
                    indicators['ma20'] = ma20
                    indicators['current_price'] = closes[-1]
                
                # RSI（简化版）
                if len(data) >= 14:
                    indicators['rsi'] = self._calculate_simple_rsi(data)
                
                # 波动率
                indicators['volatility'] = self._calculate_volatility(data)
                
            except Exception as e:
                indicators['calculation_error'] = str(e)
            
            return indicators
            
        except Exception as e:
            return {'error': str(e), 'timeframe': timeframe}
    
    def _calculate_simple_rsi(self, data: List[Dict], period: int = 14) -> float:
        """计算简化RSI"""
        try:
            if len(data) < period + 1:
                return 50.0
            
            closes = [float(d['close']) for d in data[-(period+1):]]
            gains = []
            losses = []
            
            for i in range(1, len(closes)):
                change = closes[i] - closes[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))
            
            if not gains or not losses:
                return 50.0
            
            avg_gain = sum(gains) / len(gains)
            avg_loss = sum(losses) / len(losses)
            
            if avg_loss == 0:
                return 100.0
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return max(0, min(100, rsi))
            
        except Exception:
            return 50.0
    
    def _calculate_volatility(self, data: List[Dict]) -> float:
        """计算波动率"""
        try:
            if len(data) < 10:
                return 0.0
            
            closes = [float(d['close']) for d in data[-10:]]
            returns = []
            
            for i in range(1, len(closes)):
                ret = (closes[i] - closes[i-1]) / closes[i-1]
                returns.append(ret)
            
            if not returns:
                return 0.0
            
            mean_return = sum(returns) / len(returns)
            variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
            volatility = variance ** 0.5
            
            return volatility * 100  # 转换为百分比
            
        except Exception:
            return 0.0
    
    def _get_current_price(self, symbol: str, data_15m: List[Dict]) -> float:
        """获取当前价格"""
        try:
            if data_15m and len(data_15m) > 0:
                return float(data_15m[-1]['close'])
            return 0.0
        except Exception:
            return 0.0
    
    def _aggregate_to_hourly(self, data_15m: List[Dict]) -> List[Dict]:
        """将15分钟数据聚合为1小时数据"""
        try:
            if not data_15m or len(data_15m) < 4:
                return []
            
            hourly_data = []
            i = 0
            
            while i + 3 < len(data_15m):
                # 取4根15分钟K线聚合为1根1小时K线
                four_bars = data_15m[i:i+4]
                
                hourly_bar = {
                    'time': four_bars[0]['time'],
                    'open': four_bars[0]['open'],
                    'high': max(float(bar['high']) for bar in four_bars),
                    'low': min(float(bar['low']) for bar in four_bars),
                    'close': four_bars[-1]['close'],
                    'volume': sum(float(bar.get('volume', 0)) for bar in four_bars)
                }
                
                hourly_data.append(hourly_bar)
                i += 4
            
            return hourly_data
            
        except Exception as e:
            log_analysis(f"聚合1小时数据失败: {e}", LogLevel.ERROR)
            return []
    
    def _calculate_market_metrics(self, data_15m: List[Dict], data_1h: List[Dict]) -> Dict:
        """计算市场指标"""
        try:
            metrics = {}
            
            # 趋势强度
            if len(data_15m) >= 20:
                closes_15m = [float(d['close']) for d in data_15m[-20:]]
                trend_strength = (closes_15m[-1] - closes_15m[0]) / closes_15m[0]
                metrics['trend_strength_15m'] = trend_strength
            
            # 价格动量
            if len(data_15m) >= 10:
                closes = [float(d['close']) for d in data_15m[-10:]]
                momentum = (closes[-1] - closes[0]) / closes[0]
                metrics['momentum_10bar'] = momentum
            
            # 波动率比较
            vol_15m = self._calculate_volatility(data_15m[-20:] if len(data_15m) >= 20 else data_15m)
            vol_1h = self._calculate_volatility(data_1h[-10:] if len(data_1h) >= 10 else data_1h)
            metrics['volatility_15m'] = vol_15m
            metrics['volatility_1h'] = vol_1h
            
            return metrics
            
        except Exception as e:
            return {'error': str(e)}
    
    def _assess_data_quality(self, data_15m: List[Dict], data_1h: List[Dict]) -> Dict:
        """评估数据质量"""
        try:
            quality = {
                'data_15m_count': len(data_15m) if data_15m else 0,
                'data_1h_count': len(data_1h) if data_1h else 0,
                'sufficient_15m': len(data_15m) >= 20 if data_15m else False,
                'sufficient_1h': len(data_1h) >= 10 if data_1h else False,
                'overall_quality': 'good'
            }
            
            if quality['data_15m_count'] < 20 or quality['data_1h_count'] < 10:
                quality['overall_quality'] = 'poor'
            elif quality['data_15m_count'] < 50 or quality['data_1h_count'] < 20:
                quality['overall_quality'] = 'fair'
            
            return quality
            
        except Exception:
            return {'overall_quality': 'unknown'}
    
    def _calculate_correlation_matrix(self, pairs_data: Dict) -> Dict:
        """计算货币对相关性矩阵"""
        try:
            symbols = list(pairs_data.keys())
            correlation_matrix = {}
            
            for symbol1 in symbols:
                correlation_matrix[symbol1] = {}
                for symbol2 in symbols:
                    if symbol1 == symbol2:
                        correlation_matrix[symbol1][symbol2] = 1.0
                    else:
                        corr = self._calculate_pair_correlation(
                            pairs_data[symbol1], pairs_data[symbol2]
                        )
                        correlation_matrix[symbol1][symbol2] = corr
            
            return correlation_matrix
            
        except Exception as e:
            log_analysis(f"计算相关性矩阵失败: {e}", LogLevel.ERROR)
            return {}
    
    def _calculate_pair_correlation(self, data1: Dict, data2: Dict) -> float:
        """计算两个货币对的相关性"""
        try:
            # 简化的相关性计算
            # 基于最近20根K线的收盘价
            closes1 = [float(d['close']) for d in data1['timeframe_15m'][-20:]]
            closes2 = [float(d['close']) for d in data2['timeframe_15m'][-20:]]
            
            if len(closes1) != len(closes2) or len(closes1) < 10:
                return 0.0
            
            # 计算收益率
            returns1 = [(closes1[i] - closes1[i-1]) / closes1[i-1] for i in range(1, len(closes1))]
            returns2 = [(closes2[i] - closes2[i-1]) / closes2[i-1] for i in range(1, len(closes2))]
            
            if not returns1 or not returns2:
                return 0.0
            
            # 计算相关系数
            mean1 = sum(returns1) / len(returns1)
            mean2 = sum(returns2) / len(returns2)
            
            numerator = sum((returns1[i] - mean1) * (returns2[i] - mean2) for i in range(len(returns1)))
            
            sum_sq1 = sum((r - mean1) ** 2 for r in returns1)
            sum_sq2 = sum((r - mean2) ** 2 for r in returns2)
            
            denominator = (sum_sq1 * sum_sq2) ** 0.5
            
            if denominator == 0:
                return 0.0
            
            correlation = numerator / denominator
            return max(-1.0, min(1.0, correlation))
            
        except Exception:
            return 0.0
    
    def _calculate_portfolio_risk(self, pairs_data: Dict) -> Dict:
        """计算组合风险"""
        try:
            risk_metrics = {
                'total_pairs': len(pairs_data),
                'high_volatility_pairs': 0,
                'risk_level': 'low'
            }
            
            volatilities = []
            for symbol, data in pairs_data.items():
                vol = data.get('market_metrics', {}).get('volatility_15m', 0)
                volatilities.append(vol)
                
                if vol > 0.5:  # 高波动率阈值
                    risk_metrics['high_volatility_pairs'] += 1
            
            if volatilities:
                risk_metrics['average_volatility'] = sum(volatilities) / len(volatilities)
                risk_metrics['max_volatility'] = max(volatilities)
                
                # 风险等级评估
                if risk_metrics['average_volatility'] > 0.8:
                    risk_metrics['risk_level'] = 'high'
                elif risk_metrics['average_volatility'] > 0.4:
                    risk_metrics['risk_level'] = 'medium'
            
            return risk_metrics
            
        except Exception as e:
            return {'error': str(e)}


# 全局实例
multi_pair_manager = MultiPairDataManager()

def get_multi_pair_analysis_data(symbols: List[str], primary_symbol: str = None) -> Dict:
    """
    获取多货币对分析数据的全局函数
    
    Args:
        symbols: 货币对列表
        primary_symbol: 主要货币对
        
    Returns:
        Dict: 多货币对数据
    """
    return multi_pair_manager.get_multi_pair_data(symbols, primary_symbol)
