#!/usr/bin/env python3
"""
QuantumForex Pro - 对冲修复验证测试
验证修复后的对冲逻辑是否正确工作
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_hedge_direction_calculation():
    """测试对冲方向计算"""
    print("🔍 测试对冲方向计算...")
    print("=" * 60)
    
    try:
        from core.portfolio_manager.combo_trading_manager import ComboTradingManager
        
        manager = ComboTradingManager()
        
        # 测试用例1: GBP_USD_HEDGE - 原始信号都是long
        print("📊 测试用例1: GBP_USD_HEDGE")
        hedge_signals_1 = [
            {'symbol': 'GBPUSD', 'action': 'enter_long', 'confidence': 0.8},
            {'symbol': 'USDCHF', 'action': 'enter_long', 'confidence': 0.7}
        ]
        
        directions_1 = manager._calculate_hedge_directions(hedge_signals_1)
        print(f"   原始信号: GBPUSD long, USDCHF long")
        print(f"   计算结果: GBPUSD {directions_1[0]}, USDCHF {directions_1[1]}")
        
        # 验证USD敞口
        usd_exp_1 = manager._calculate_usd_exposure('GBPUSD', directions_1[0], 0.03)
        usd_exp_2 = manager._calculate_usd_exposure('USDCHF', directions_1[1], 0.03)
        total_exp_1 = usd_exp_1 + usd_exp_2
        
        print(f"   USD敞口: GBPUSD {usd_exp_1:+.3f}, USDCHF {usd_exp_2:+.3f}")
        print(f"   总USD敞口: {total_exp_1:+.3f}")
        print(f"   对冲效果: {'✅ 已对冲' if abs(total_exp_1) < 0.001 else '❌ 未对冲'}")
        
        # 测试用例2: EUR_USD_HEDGE - 混合信号
        print("\n📊 测试用例2: EUR_USD_HEDGE")
        hedge_signals_2 = [
            {'symbol': 'EURUSD', 'action': 'enter_long', 'confidence': 0.8},
            {'symbol': 'USDCHF', 'action': 'enter_short', 'confidence': 0.7}
        ]
        
        directions_2 = manager._calculate_hedge_directions(hedge_signals_2)
        print(f"   原始信号: EURUSD long, USDCHF short")
        print(f"   计算结果: EURUSD {directions_2[0]}, USDCHF {directions_2[1]}")
        
        # 验证USD敞口
        usd_exp_3 = manager._calculate_usd_exposure('EURUSD', directions_2[0], 0.03)
        usd_exp_4 = manager._calculate_usd_exposure('USDCHF', directions_2[1], 0.03)
        total_exp_2 = usd_exp_3 + usd_exp_4
        
        print(f"   USD敞口: EURUSD {usd_exp_3:+.3f}, USDCHF {usd_exp_4:+.3f}")
        print(f"   总USD敞口: {total_exp_2:+.3f}")
        print(f"   对冲效果: {'✅ 已对冲' if abs(total_exp_2) < 0.001 else '❌ 未对冲'}")
        
        # 测试用例3: AUD_NZD_HEDGE - 都涉及USD
        print("\n📊 测试用例3: AUD_NZD_HEDGE")
        hedge_signals_3 = [
            {'symbol': 'AUDUSD', 'action': 'enter_long', 'confidence': 0.8},
            {'symbol': 'NZDUSD', 'action': 'enter_long', 'confidence': 0.7}
        ]
        
        directions_3 = manager._calculate_hedge_directions(hedge_signals_3)
        print(f"   原始信号: AUDUSD long, NZDUSD long")
        print(f"   计算结果: AUDUSD {directions_3[0]}, NZDUSD {directions_3[1]}")
        
        # 验证USD敞口
        usd_exp_5 = manager._calculate_usd_exposure('AUDUSD', directions_3[0], 0.03)
        usd_exp_6 = manager._calculate_usd_exposure('NZDUSD', directions_3[1], 0.03)
        total_exp_3 = usd_exp_5 + usd_exp_6
        
        print(f"   USD敞口: AUDUSD {usd_exp_5:+.3f}, NZDUSD {usd_exp_6:+.3f}")
        print(f"   总USD敞口: {total_exp_3:+.3f}")
        print(f"   对冲效果: {'✅ 已对冲' if abs(total_exp_3) < 0.001 else '❌ 未对冲'}")
        
        # 统计结果
        test_results = [
            abs(total_exp_1) < 0.001,
            abs(total_exp_2) < 0.001,
            abs(total_exp_3) < 0.001
        ]
        
        passed_tests = sum(test_results)
        print(f"\n📊 测试结果: {passed_tests}/3 通过")
        
        return passed_tests == 3
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_usd_exposure_calculation():
    """测试USD敞口计算函数"""
    print("\n🔍 测试USD敞口计算函数...")
    print("=" * 60)
    
    try:
        from core.portfolio_manager.combo_trading_manager import ComboTradingManager
        
        manager = ComboTradingManager()
        
        # 测试各种货币对的USD敞口计算
        test_cases = [
            # USD作为报价货币
            ('GBPUSD', 'long', 0.03, -0.03),   # 做多GBP = 做空USD
            ('GBPUSD', 'short', 0.03, 0.03),   # 做空GBP = 做多USD
            ('EURUSD', 'long', 0.03, -0.03),   # 做多EUR = 做空USD
            ('EURUSD', 'short', 0.03, 0.03),   # 做空EUR = 做多USD
            
            # USD作为基础货币
            ('USDCHF', 'long', 0.03, 0.03),    # 做多USD = 做多USD
            ('USDCHF', 'short', 0.03, -0.03),  # 做空USD = 做空USD
            ('USDJPY', 'long', 0.03, 0.03),    # 做多USD = 做多USD
            ('USDJPY', 'short', 0.03, -0.03),  # 做空USD = 做空USD
            
            # 不涉及USD
            ('GBPJPY', 'long', 0.03, 0.0),     # 不涉及USD
            ('EURGBP', 'short', 0.03, 0.0),    # 不涉及USD
        ]
        
        print("📊 USD敞口计算测试:")
        all_correct = True
        
        for symbol, action, volume, expected in test_cases:
            actual = manager._calculate_usd_exposure(symbol, action, volume)
            is_correct = abs(actual - expected) < 0.001
            status = "✅" if is_correct else "❌"
            
            print(f"   {status} {symbol} {action} {volume}: 期望{expected:+.3f}, 实际{actual:+.3f}")
            
            if not is_correct:
                all_correct = False
        
        print(f"\n📊 USD敞口计算: {'✅ 全部正确' if all_correct else '❌ 存在错误'}")
        return all_correct
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_combo_decision_with_fixed_hedge():
    """测试修复后的组合决策"""
    print("\n🔍 测试修复后的组合决策...")
    print("=" * 60)
    
    try:
        from core.portfolio_manager.combo_trading_manager import ComboTradingManager
        
        manager = ComboTradingManager()
        
        # 模拟信号
        test_signals = [
            {'symbol': 'GBPUSD', 'action': 'enter_long', 'confidence': 0.8, 'signal_strength': 0.6},
            {'symbol': 'USDCHF', 'action': 'enter_long', 'confidence': 0.7, 'signal_strength': 0.5},
            {'symbol': 'EURUSD', 'action': 'enter_short', 'confidence': 0.6, 'signal_strength': -0.4}
        ]
        
        # 模拟当前持仓（空持仓，触发对冲创建）
        current_positions = {}
        
        print("📊 测试组合决策:")
        print(f"   输入信号: {len(test_signals)}个")
        print(f"   当前持仓: {len(current_positions)}个")
        
        # 分析组合机会
        decision = manager.analyze_combo_opportunities(test_signals, current_positions)
        
        print(f"\n📋 决策结果:")
        print(f"   动作: {decision.action}")
        print(f"   原因: {decision.reason}")
        print(f"   置信度: {decision.confidence:.2f}")
        print(f"   组合数量: {len(decision.combo_trades)}")
        
        if decision.combo_trades:
            combo = decision.combo_trades[0]
            print(f"\n🔄 组合详情:")
            print(f"   组合ID: {combo.combo_id}")
            print(f"   类型: {combo.combo_type.value}")
            print(f"   货币对: {combo.symbols}")
            print(f"   方向: {combo.directions}")
            print(f"   仓位: {combo.position_sizes}")
            print(f"   描述: {combo.description}")
            
            # 验证对冲效果
            if len(combo.symbols) == 2 and len(combo.directions) == 2:
                usd_exp_1 = manager._calculate_usd_exposure(combo.symbols[0], combo.directions[0], combo.position_sizes[0])
                usd_exp_2 = manager._calculate_usd_exposure(combo.symbols[1], combo.directions[1], combo.position_sizes[1])
                total_exp = usd_exp_1 + usd_exp_2
                
                print(f"\n💰 USD敞口验证:")
                print(f"   {combo.symbols[0]} {combo.directions[0]}: {usd_exp_1:+.3f}")
                print(f"   {combo.symbols[1]} {combo.directions[1]}: {usd_exp_2:+.3f}")
                print(f"   总敞口: {total_exp:+.3f}")
                print(f"   对冲效果: {'✅ 已对冲' if abs(total_exp) < 0.001 else '❌ 未对冲'}")
                
                return abs(total_exp) < 0.001
        
        return decision.action != 'hold'
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_scenario_simulation():
    """测试真实场景模拟"""
    print("\n🔍 测试真实场景模拟...")
    print("=" * 60)
    
    try:
        from core.portfolio_manager.combo_trading_manager import ComboTradingManager
        
        manager = ComboTradingManager()
        
        # 模拟真实的市场信号（类似之前测试中的情况）
        real_signals = [
            {'symbol': 'GBPUSD', 'action': 'enter_long', 'confidence': 0.75, 'signal_strength': 0.6},
            {'symbol': 'USDCHF', 'action': 'enter_short', 'confidence': 0.70, 'signal_strength': -0.5}
        ]
        
        print("📊 真实场景模拟:")
        print("   原始信号 (修复前会产生的错误对冲):")
        print("   - GBPUSD BUY (做空USD)")
        print("   - USDCHF SELL (做空USD)")
        print("   - 结果: 双重做空USD ❌")
        
        # 测试修复后的逻辑
        directions = manager._calculate_hedge_directions(real_signals)
        
        print(f"\n🔧 修复后的对冲方向:")
        print(f"   - GBPUSD {directions[0].upper()}")
        print(f"   - USDCHF {directions[1].upper()}")
        
        # 计算修复后的USD敞口
        usd_exp_1 = manager._calculate_usd_exposure('GBPUSD', directions[0], 0.03)
        usd_exp_2 = manager._calculate_usd_exposure('USDCHF', directions[1], 0.03)
        total_exp = usd_exp_1 + usd_exp_2
        
        print(f"\n💰 修复后USD敞口:")
        print(f"   GBPUSD {directions[0]}: {usd_exp_1:+.3f}")
        print(f"   USDCHF {directions[1]}: {usd_exp_2:+.3f}")
        print(f"   总敞口: {total_exp:+.3f}")
        
        is_hedged = abs(total_exp) < 0.001
        print(f"   对冲效果: {'✅ 完美对冲' if is_hedged else '❌ 仍有敞口'}")
        
        if is_hedged:
            print("\n🎉 修复成功！")
            print("   系统现在能够正确计算对冲方向")
            print("   USD敞口得到完美平衡")
            print("   真正实现了风险对冲功能")
        
        return is_hedged
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 QuantumForex Pro - 对冲修复验证测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行所有验证测试
    tests = [
        ("对冲方向计算", test_hedge_direction_calculation),
        ("USD敞口计算", test_usd_exposure_calculation),
        ("组合决策测试", test_combo_decision_with_fixed_hedge),
        ("真实场景模拟", test_real_scenario_simulation)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"🔄 开始执行: {test_name}")
            print(f"{'='*60}")
            
            results[test_name] = test_func()
            
            if results[test_name]:
                print(f"✅ {test_name} - 测试通过")
            else:
                print(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("📊 对冲修复验证结果汇总:")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 对冲逻辑修复成功！所有验证测试通过！")
        print("✅ 系统现在能够正确执行USD敞口对冲")
        print("✅ 真正实现了风险对冲功能")
        print("🚀 可以安全地进行对冲交易")
        sys.exit(0)
    else:
        print("❌ 对冲修复验证失败，需要进一步调试")
        sys.exit(1)
