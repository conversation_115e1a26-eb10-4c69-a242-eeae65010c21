"""
数据源适配器
统一的数据获取接口，支持多种数据源
"""

import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import traceback
from app.utils import db_client
from app.utils import forex_data_processor
from app.utils.logger_manager import log_analysis, LogLevel
from app.utils.error_logger import log_error, ErrorType, OperationType

class DataSourceAdapter:
    """数据源适配器 - 统一的数据获取接口"""
    
    def __init__(self):
        self.supported_symbols = [
            'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 
            'USDCHF', 'USDCAD', 'USDJPY', 'GOLD'
        ]
        
    def get_timeframe_data(self, symbol: str, timeframe: str, count: int = 100) -> List[Dict]:
        """
        获取指定时间框架的数据
        
        Args:
            symbol: 货币对符号
            timeframe: 时间框架 ('15min', '60min', '1h', '4h', '1d')
            count: 数据条数
            
        Returns:
            List[Dict]: K线数据列表
        """
        try:
            # 标准化时间框架
            timeframe_minutes = self._parse_timeframe(timeframe)
            
            if not timeframe_minutes:
                log_analysis(f"不支持的时间框架: {timeframe}", LogLevel.ERROR)
                return []
            
            # 目前只支持EURUSD的数据获取，其他货币对返回空数据
            if symbol != 'EURUSD':
                log_analysis(f"暂不支持{symbol}的数据获取，返回空数据", LogLevel.WARNING)
                return []
            
            # 获取聚合K线数据
            data = self._get_aggregated_klines(timeframe_minutes, count)
            
            if data:
                log_analysis(f"成功获取{symbol} {timeframe}数据: {len(data)}条", LogLevel.INFO)
            else:
                log_analysis(f"获取{symbol} {timeframe}数据失败", LogLevel.WARNING)
            
            return data
            
        except Exception as e:
            log_analysis(f"获取{symbol} {timeframe}数据异常: {e}", LogLevel.ERROR)
            log_error(
                error_type=ErrorType.DATA_ERROR,
                message=f'获取{symbol} {timeframe}数据失败: {e}',
                details={'symbol': symbol, 'timeframe': timeframe, 'count': count},
                operation=OperationType.DATA_FETCH
            )
            return []
    
    def _parse_timeframe(self, timeframe: str) -> Optional[int]:
        """解析时间框架为分钟数"""
        timeframe_map = {
            '1min': 1,
            '5min': 5,
            '15min': 15,
            '30min': 30,
            '60min': 60,
            '1h': 60,
            '4h': 240,
            '1d': 1440
        }
        
        return timeframe_map.get(timeframe.lower())
    
    def _get_aggregated_klines(self, period_minutes: int, count: int) -> List[Dict]:
        """
        获取聚合K线数据
        
        Args:
            period_minutes: 周期（分钟）
            count: 数量
            
        Returns:
            List[Dict]: 聚合K线数据
        """
        try:
            # 计算需要获取的1分钟K线数量
            if period_minutes <= 15:
                min_data_count = period_minutes * count * 2
            elif period_minutes <= 30:
                min_data_count = period_minutes * count * 3
            else:
                min_data_count = period_minutes * count * 4
            
            # 确保获取足够的数据
            min_data_count = max(min_data_count, 5000)
            
            # 获取1分钟K线数据
            min_data = self._get_eurusd_min_data(limit=min_data_count)
            
            if not min_data:
                log_analysis("1分钟K线数据为空", LogLevel.ERROR)
                return []
            
            # 检查数据是否足够
            min_required = period_minutes * 20
            if len(min_data) < min_required:
                log_analysis(f"1分钟K线数据不足: {len(min_data)}/{min_required}", LogLevel.WARNING)
                return []
            
            # 数据是按时间倒序排列的，需要转为升序
            min_data.reverse()
            
            # 聚合为指定周期的K线
            aggregated_klines = forex_data_processor.aggregate_klines(min_data, period_minutes)
            
            if not aggregated_klines:
                log_analysis("聚合后K线数据为空", LogLevel.ERROR)
                return []
            
            if len(aggregated_klines) < 20:
                log_analysis(f"聚合后K线数据不足: {len(aggregated_klines)}/20", LogLevel.WARNING)
                return []
            
            # 返回指定数量的K线
            if len(aggregated_klines) > count:
                result = aggregated_klines[-count:]
            else:
                result = aggregated_klines
            
            # 确保数据是按时间升序排列的
            if len(result) > 1:
                first_time = datetime.strptime(result[0]["time"], "%Y-%m-%d %H:%M:%S")
                last_time = datetime.strptime(result[-1]["time"], "%Y-%m-%d %H:%M:%S")
                if first_time > last_time:
                    result.reverse()
            
            return result
            
        except Exception as e:
            log_analysis(f"获取聚合K线数据失败: {e}", LogLevel.ERROR)
            return []
    
    def _get_eurusd_min_data(self, start_time=None, end_time=None, limit=1000) -> List[Dict]:
        """
        获取EURUSD分钟数据
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            limit: 限制条数
            
        Returns:
            List[Dict]: EURUSD分钟数据
        """
        try:
            return db_client.get_eurusd_min_data(start_time, end_time, limit)
        except Exception as e:
            log_analysis(f"获取EURUSD分钟数据失败: {e}", LogLevel.ERROR)
            return []
    
    def get_current_price(self, symbol: str) -> float:
        """
        获取当前价格
        
        Args:
            symbol: 货币对符号
            
        Returns:
            float: 当前价格，获取失败返回0.0
        """
        try:
            # 从最新的K线数据中获取价格
            data = self.get_timeframe_data(symbol, '15min', 1)
            if data and len(data) > 0:
                return float(data[-1]['close'])
            return 0.0
        except Exception as e:
            log_analysis(f"获取{symbol}当前价格失败: {e}", LogLevel.ERROR)
            return 0.0
    
    def get_symbol_info(self, symbol: str) -> Dict:
        """
        获取货币对信息
        
        Args:
            symbol: 货币对符号
            
        Returns:
            Dict: 货币对信息
        """
        try:
            info = {
                'symbol': symbol,
                'supported': symbol in self.supported_symbols,
                'current_price': self.get_current_price(symbol),
                'data_available': symbol == 'EURUSD',  # 目前只有EURUSD有数据
                'timeframes': ['15min', '60min', '1h'] if symbol == 'EURUSD' else []
            }
            
            return info
            
        except Exception as e:
            log_analysis(f"获取{symbol}信息失败: {e}", LogLevel.ERROR)
            return {
                'symbol': symbol,
                'supported': False,
                'error': str(e)
            }
    
    def get_multiple_symbols_data(self, symbols: List[str], timeframe: str, count: int = 100) -> Dict[str, List[Dict]]:
        """
        批量获取多个货币对的数据
        
        Args:
            symbols: 货币对列表
            timeframe: 时间框架
            count: 数据条数
            
        Returns:
            Dict[str, List[Dict]]: 各货币对的数据
        """
        result = {}
        
        for symbol in symbols:
            try:
                data = self.get_timeframe_data(symbol, timeframe, count)
                result[symbol] = data
                
                if data:
                    log_analysis(f"批量获取{symbol}数据成功: {len(data)}条", LogLevel.INFO)
                else:
                    log_analysis(f"批量获取{symbol}数据失败", LogLevel.WARNING)
                    
            except Exception as e:
                log_analysis(f"批量获取{symbol}数据异常: {e}", LogLevel.ERROR)
                result[symbol] = []
        
        return result
    
    def is_symbol_supported(self, symbol: str) -> bool:
        """检查货币对是否支持"""
        return symbol in self.supported_symbols
    
    def get_supported_symbols(self) -> List[str]:
        """获取支持的货币对列表"""
        return self.supported_symbols.copy()
    
    def get_data_quality_info(self, symbol: str, timeframe: str) -> Dict:
        """
        获取数据质量信息
        
        Args:
            symbol: 货币对符号
            timeframe: 时间框架
            
        Returns:
            Dict: 数据质量信息
        """
        try:
            data = self.get_timeframe_data(symbol, timeframe, 50)
            
            quality_info = {
                'symbol': symbol,
                'timeframe': timeframe,
                'data_count': len(data) if data else 0,
                'data_available': bool(data),
                'sufficient_data': len(data) >= 20 if data else False,
                'latest_time': data[-1]['time'] if data else None,
                'quality_score': 0
            }
            
            # 计算质量评分
            if quality_info['data_available']:
                if quality_info['sufficient_data']:
                    quality_info['quality_score'] = 100
                elif quality_info['data_count'] >= 10:
                    quality_info['quality_score'] = 70
                else:
                    quality_info['quality_score'] = 30
            
            return quality_info
            
        except Exception as e:
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'error': str(e),
                'quality_score': 0
            }
