#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantumForex Pro - MT4客户端测试脚本
测试MT4客户端的连接和交易功能
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mt4_client():
    """测试MT4客户端功能"""
    print("=" * 60)
    print("🚀 QuantumForex Pro - MT4客户端功能测试")
    print("=" * 60)
    
    try:
        # 导入MT4客户端
        from utils.mt4_client import mt4_client
        
        print("\n📋 测试项目:")
        print("1. 连接测试")
        print("2. 获取市场信息")
        print("3. 获取账户信息")
        print("4. 获取活跃订单")
        print("5. 获取挂单")
        print("6. 模拟交易测试")
        
        # 1. 连接测试
        print("\n" + "─" * 40)
        print("🔗 1. 测试MT4连接...")
        connected = mt4_client.connect()
        print(f"   连接结果: {'✅ 成功' if connected else '❌ 失败'}")
        
        if not connected:
            print("❌ MT4连接失败，无法继续测试")
            return
        
        # 2. 获取市场信息
        print("\n🔗 2. 测试获取市场信息...")
        symbols = ['EURUSD', 'GBPUSD', 'AUDUSD']
        for symbol in symbols:
            try:
                market_info = mt4_client.get_market_info(symbol)
                if market_info and market_info.get('status') == 'success':
                    data = market_info.get('data', {})
                    print(f"   {symbol}: Bid={data.get('bid', 'N/A')}, Ask={data.get('ask', 'N/A')}, Spread={data.get('spread', 'N/A')}")
                else:
                    print(f"   {symbol}: ❌ 获取失败 - {market_info.get('message', '未知错误')}")
            except Exception as e:
                print(f"   {symbol}: ❌ 异常 - {e}")
        
        # 3. 获取账户信息
        print("\n💰 3. 测试获取账户信息...")
        try:
            account_info = mt4_client.get_account_info()
            if account_info and account_info.get('status') == 'success':
                data = account_info.get('data', {})
                print(f"   账户: {data.get('account', 'N/A')}")
                print(f"   余额: ${data.get('balance', 'N/A')}")
                print(f"   净值: ${data.get('equity', 'N/A')}")
                print(f"   可用保证金: ${data.get('free_margin', 'N/A')}")
            else:
                print(f"   ❌ 获取失败 - {account_info.get('message', '未知错误')}")
        except Exception as e:
            print(f"   ❌ 异常 - {e}")
        
        # 4. 获取活跃订单
        print("\n📊 4. 测试获取活跃订单...")
        try:
            active_orders = mt4_client.get_active_orders()
            if active_orders and active_orders.get('status') == 'success':
                orders = active_orders.get('orders', [])
                print(f"   活跃订单数量: {len(orders)}")
                for i, order in enumerate(orders[:3]):  # 只显示前3个
                    print(f"   订单{i+1}: {order.get('symbol', 'N/A')} {order.get('type', 'N/A')} {order.get('lots', 'N/A')}手")
            else:
                print(f"   ❌ 获取失败 - {active_orders.get('message', '未知错误')}")
        except Exception as e:
            print(f"   ❌ 异常 - {e}")
        
        # 5. 获取挂单
        print("\n📋 5. 测试获取挂单...")
        try:
            pending_orders = mt4_client.get_pending_orders()
            if pending_orders and pending_orders.get('status') == 'success':
                orders = pending_orders.get('orders', [])
                print(f"   挂单数量: {len(orders)}")
                for i, order in enumerate(orders[:3]):  # 只显示前3个
                    print(f"   挂单{i+1}: {order.get('symbol', 'N/A')} {order.get('type', 'N/A')} {order.get('lots', 'N/A')}手")
            else:
                print(f"   ❌ 获取失败 - {pending_orders.get('message', '未知错误')}")
        except Exception as e:
            print(f"   ❌ 异常 - {e}")
        
        # 6. 模拟交易测试（仅在跳过模式下进行）
        print("\n💼 6. 测试模拟交易功能...")
        try:
            # 测试买入
            print("   测试买入操作...")
            buy_response = mt4_client.buy('EURUSD', 0.01, sl=1.0800, tp=1.0900, comment='测试买入')
            if buy_response and buy_response.get('status') == 'success':
                print(f"   ✅ 买入成功 - 订单ID: {buy_response.get('order_id', 'N/A')}")
            else:
                print(f"   ❌ 买入失败 - {buy_response.get('message', '未知错误')}")
            
            # 测试卖出
            print("   测试卖出操作...")
            sell_response = mt4_client.sell('GBPUSD', 0.01, sl=1.2700, tp=1.2600, comment='测试卖出')
            if sell_response and sell_response.get('status') == 'success':
                print(f"   ✅ 卖出成功 - 订单ID: {sell_response.get('order_id', 'N/A')}")
            else:
                print(f"   ❌ 卖出失败 - {sell_response.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"   ❌ 交易测试异常 - {e}")
        
        print("\n" + "=" * 60)
        print("✅ MT4客户端测试完成！")
        print("=" * 60)
        
        # 显示连接状态总结
        print(f"\n📊 测试总结:")
        print(f"   连接状态: {'✅ 已连接' if mt4_client.is_connected else '❌ 未连接'}")
        print(f"   授权状态: {'✅ 已授权' if getattr(mt4_client, 'is_authorized', False) else '❌ 未授权'}")
        print(f"   服务器地址: {getattr(mt4_client, 'server_address', 'N/A')}")
        
        # 检查是否在跳过模式
        from utils.mt4_client import should_skip_mt4_connection
        if should_skip_mt4_connection():
            print(f"   运行模式: 🔄 模拟模式（MT4服务器跳过）")
        else:
            print(f"   运行模式: 🔗 真实连接模式")
        
    except ImportError as e:
        print(f"❌ 导入MT4客户端失败: {e}")
        print("请确保MT4客户端模块正确安装")
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_mt4_client()
