"""
QuantumForex MLTrainer 价格预测模型训练器
训练价格方向和价格变化预测模型
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Any
from sklearn.preprocessing import LabelEncoder

from .base_trainer import BaseModelTrainer

class PricePredictionTrainer(BaseModelTrainer):
    """价格预测模型训练器"""

    def __init__(self):
        super().__init__('classification')
        self.logger = logging.getLogger(__name__)

        # 预测配置
        self.prediction_horizons = [1, 3, 5, 10, 20]  # 预测时间范围（分钟）
        self.price_change_thresholds = [0.0001, 0.0005, 0.001]  # 价格变化阈值

    def create_target_variable(self, df: pd.DataFrame, method: str = 'price_direction',
                             horizon: int = 5, threshold: float = 0.0001) -> pd.Series:
        """创建价格预测目标变量"""
        try:
            self.logger.info(f"📊 创建目标变量: {method}, 时间范围={horizon}, 阈值={threshold}")

            if method == 'price_direction':
                # 价格方向预测（上涨/下跌）
                future_price = df['close'].shift(-horizon)
                current_price = df['close']
                price_change = (future_price - current_price) / current_price

                # 创建三分类：上涨(1)、下跌(-1)、横盘(0)
                target = pd.Series(index=df.index, dtype=int)
                target[price_change > threshold] = 1   # 上涨
                target[price_change < -threshold] = -1  # 下跌
                target[abs(price_change) <= threshold] = 0  # 横盘

            elif method == 'price_change_magnitude':
                # 价格变化幅度预测
                future_price = df['close'].shift(-horizon)
                current_price = df['close']
                price_change = abs((future_price - current_price) / current_price)

                # 创建变化幅度分类
                target = pd.Series(index=df.index, dtype=int)
                target[price_change <= 0.0001] = 0  # 微小变化
                target[(price_change > 0.0001) & (price_change <= 0.0005)] = 1  # 小幅变化
                target[(price_change > 0.0005) & (price_change <= 0.001)] = 2   # 中等变化
                target[price_change > 0.001] = 3  # 大幅变化

            elif method == 'support_resistance_break':
                # 支撑阻力突破预测
                target = self._create_breakout_target(df, horizon)

            else:
                raise ValueError(f"不支持的目标变量方法: {method}")

            # 删除NaN值
            target = target.dropna()

            self.logger.info(f"✅ 目标变量创建完成: {len(target)}个样本")
            self.logger.info(f"📊 类别分布: {target.value_counts().to_dict()}")

            return target

        except Exception as e:
            self.logger.error(f"❌ 创建目标变量失败: {e}")
            raise

    def _create_breakout_target(self, df: pd.DataFrame, horizon: int) -> pd.Series:
        """创建突破目标变量"""
        try:
            # 计算支撑和阻力位
            window = 20
            support = df['low'].rolling(window=window).min()
            resistance = df['high'].rolling(window=window).max()

            # 未来价格
            future_high = df['high'].shift(-horizon)
            future_low = df['low'].shift(-horizon)

            # 突破判断
            target = pd.Series(index=df.index, dtype=int)

            # 向上突破阻力位
            resistance_break = future_high > resistance * 1.001
            target[resistance_break] = 1

            # 向下突破支撑位
            support_break = future_low < support * 0.999
            target[support_break] = -1

            # 无突破
            no_break = ~(resistance_break | support_break)
            target[no_break] = 0

            return target

        except Exception as e:
            self.logger.error(f"❌ 创建突破目标变量失败: {e}")
            return pd.Series()

    def train_price_direction_model(self, df: pd.DataFrame, feature_columns: List[str],
                                  horizon: int = 5, threshold: float = 0.0001) -> Dict[str, Any]:
        """训练价格方向预测模型"""
        try:
            self.logger.info(f"🧠 训练价格方向预测模型: 时间范围={horizon}分钟")

            # 创建目标变量
            target = self.create_target_variable(df, 'price_direction', horizon, threshold)
            target.name = 'target'  # 设置目标变量名称

            # 对齐数据
            common_index = df.index.intersection(target.index)
            df_aligned = df.loc[common_index].copy()
            target_aligned = target.loc[common_index]

            # 添加目标列到DataFrame
            df_aligned['target'] = target_aligned

            # 准备数据
            X, y, feature_names = self.prepare_data(df_aligned, 'target', feature_columns)

            # 分割数据
            X_train, X_test, y_train, y_test = self.split_data(X, y)

            # 特征标准化
            X_train_scaled, X_test_scaled = self.scale_features(X_train, X_test, f'price_direction_{horizon}')

            # 训练模型
            results = self.train_models(X_train_scaled, y_train, X_test_scaled, y_test, feature_names)

            # 获取最佳模型
            best_model_name, best_model = self.get_best_model(results, 'f1_score')

            # 保存模型
            model_prefix = f'price_prediction_direction_{horizon}min'
            self.save_models(model_prefix)

            # 添加训练记录
            training_record = {
                'model_type': 'price_direction',
                'horizon': horizon,
                'threshold': threshold,
                'best_model': best_model_name,
                'results': {name: result['metrics'] for name, result in results.items()},
                'feature_count': len(feature_names),
                'sample_count': len(X)
            }
            self.training_history.append(training_record)

            return results

        except Exception as e:
            self.logger.error(f"❌ 训练价格方向预测模型失败: {e}")
            return {}

    def train_price_magnitude_model(self, df: pd.DataFrame, feature_columns: List[str],
                                  horizon: int = 5) -> Dict[str, Any]:
        """训练价格变化幅度预测模型"""
        try:
            self.logger.info(f"🧠 训练价格变化幅度预测模型: 时间范围={horizon}分钟")

            # 创建目标变量
            target = self.create_target_variable(df, 'price_change_magnitude', horizon)

            # 对齐数据
            common_index = df.index.intersection(target.index)
            df_aligned = df.loc[common_index]
            target_aligned = target.loc[common_index]

            # 准备数据
            X, y, feature_names = self.prepare_data(df_aligned, target_aligned.name, feature_columns)

            # 分割数据
            X_train, X_test, y_train, y_test = self.split_data(X, target_aligned.values)

            # 特征标准化
            X_train_scaled, X_test_scaled = self.scale_features(X_train, X_test, f'price_magnitude_{horizon}')

            # 训练模型
            results = self.train_models(X_train_scaled, y_train, X_test_scaled, y_test, feature_names)

            # 获取最佳模型
            best_model_name, best_model = self.get_best_model(results, 'f1_score')

            # 保存模型
            model_prefix = f'price_prediction_magnitude_{horizon}min'
            self.save_models(model_prefix)

            # 添加训练记录
            training_record = {
                'model_type': 'price_magnitude',
                'horizon': horizon,
                'best_model': best_model_name,
                'results': {name: result['metrics'] for name, result in results.items()},
                'feature_count': len(feature_names),
                'sample_count': len(X)
            }
            self.training_history.append(training_record)

            return results

        except Exception as e:
            self.logger.error(f"❌ 训练价格变化幅度预测模型失败: {e}")
            return {}

    def train_breakout_model(self, df: pd.DataFrame, feature_columns: List[str],
                           horizon: int = 10) -> Dict[str, Any]:
        """训练突破预测模型"""
        try:
            self.logger.info(f"🧠 训练突破预测模型: 时间范围={horizon}分钟")

            # 创建目标变量
            target = self.create_target_variable(df, 'support_resistance_break', horizon)

            # 对齐数据
            common_index = df.index.intersection(target.index)
            df_aligned = df.loc[common_index]
            target_aligned = target.loc[common_index]

            # 准备数据
            X, y, feature_names = self.prepare_data(df_aligned, target_aligned.name, feature_columns)

            # 分割数据
            X_train, X_test, y_train, y_test = self.split_data(X, target_aligned.values)

            # 特征标准化
            X_train_scaled, X_test_scaled = self.scale_features(X_train, X_test, f'breakout_{horizon}')

            # 训练模型
            results = self.train_models(X_train_scaled, y_train, X_test_scaled, y_test, feature_names)

            # 获取最佳模型
            best_model_name, best_model = self.get_best_model(results, 'f1_score')

            # 保存模型
            model_prefix = f'price_prediction_breakout_{horizon}min'
            self.save_models(model_prefix)

            # 添加训练记录
            training_record = {
                'model_type': 'breakout',
                'horizon': horizon,
                'best_model': best_model_name,
                'results': {name: result['metrics'] for name, result in results.items()},
                'feature_count': len(feature_names),
                'sample_count': len(X)
            }
            self.training_history.append(training_record)

            return results

        except Exception as e:
            self.logger.error(f"❌ 训练突破预测模型失败: {e}")
            return {}

    def train_all_price_models(self, df: pd.DataFrame, feature_columns: List[str]) -> Dict[str, Any]:
        """训练所有价格预测模型"""
        try:
            self.logger.info("🚀 开始训练所有价格预测模型...")

            all_results = {}

            # 训练不同时间范围的价格方向模型
            for horizon in [1, 5, 10]:
                for threshold in [0.0001, 0.0005]:
                    try:
                        results = self.train_price_direction_model(df, feature_columns, horizon, threshold)
                        all_results[f'direction_{horizon}min_{threshold}'] = results
                    except Exception as e:
                        self.logger.error(f"训练价格方向模型失败 (horizon={horizon}, threshold={threshold}): {e}")

            # 训练价格变化幅度模型
            for horizon in [5, 10, 20]:
                try:
                    results = self.train_price_magnitude_model(df, feature_columns, horizon)
                    all_results[f'magnitude_{horizon}min'] = results
                except Exception as e:
                    self.logger.error(f"训练价格变化幅度模型失败 (horizon={horizon}): {e}")

            # 训练突破模型
            for horizon in [10, 20]:
                try:
                    results = self.train_breakout_model(df, feature_columns, horizon)
                    all_results[f'breakout_{horizon}min'] = results
                except Exception as e:
                    self.logger.error(f"训练突破模型失败 (horizon={horizon}): {e}")

            self.logger.info(f"🎉 所有价格预测模型训练完成: {len(all_results)}个模型组合")
            return all_results

        except Exception as e:
            self.logger.error(f"❌ 训练所有价格预测模型失败: {e}")
            return {}

    def predict_price_direction(self, df: pd.DataFrame, model_name: str = 'best',
                              horizon: int = 5) -> np.ndarray:
        """预测价格方向"""
        try:
            # 这里应该加载已训练的模型进行预测
            # 简化实现，返回随机预测
            predictions = np.random.choice([-1, 0, 1], size=len(df))
            return predictions

        except Exception as e:
            self.logger.error(f"❌ 价格方向预测失败: {e}")
            return np.array([])

    def get_model_performance_summary(self) -> Dict[str, Any]:
        """获取模型性能摘要"""
        try:
            summary = {
                'total_models_trained': len(self.training_history),
                'model_types': list(set([record['model_type'] for record in self.training_history])),
                'best_models': {},
                'average_performance': {}
            }

            # 按模型类型分组统计
            for record in self.training_history:
                model_type = record['model_type']
                if model_type not in summary['best_models']:
                    summary['best_models'][model_type] = {
                        'best_model': record['best_model'],
                        'best_f1_score': 0
                    }

                # 找到最佳F1分数
                for model_name, metrics in record['results'].items():
                    if 'f1_score' in metrics:
                        f1_score = metrics['f1_score']
                        if f1_score > summary['best_models'][model_type]['best_f1_score']:
                            summary['best_models'][model_type]['best_f1_score'] = f1_score
                            summary['best_models'][model_type]['best_model'] = model_name

            return summary

        except Exception as e:
            self.logger.error(f"❌ 获取模型性能摘要失败: {e}")
            return {}

# 创建价格预测训练器实例
price_trainer = PricePredictionTrainer()
