# QuantumForex Pro 依赖包详细说明

## 📋 概述
本文档详细说明了 QuantumForex Pro 量化交易系统的所有依赖包及其作用。

## 🚀 快速安装

### 核心依赖 (必须安装)
```bash
pip install pandas numpy scikit-learn psutil scipy requests pymysql python-dateutil pyzmq statsmodels
```

### 完整安装
```bash
pip install -r requirements.txt
```

## 📦 依赖包分类

### 🔧 核心数据处理
| 包名 | 版本要求 | 状态 | 说明 |
|------|----------|------|------|
| pandas | >=1.3.0 | ✅ 已安装 | 数据分析和处理 |
| numpy | >=1.21.0 | ✅ 已安装 | 数值计算基础 |
| python-dateutil | >=2.8.0 | ✅ 已安装 | 时间处理 |

### 🤖 机器学习
| 包名 | 版本要求 | 状态 | 说明 |
|------|----------|------|------|
| scikit-learn | >=1.6.0 | ✅ 已安装 | 机器学习核心库 |
| joblib | >=1.5.0 | ✅ 已安装 | 并行计算和模型持久化 |
| threadpoolctl | >=3.6.0 | ✅ 已安装 | 线程池控制 |
| scipy | >=1.7.0 | ✅ 已安装 | 科学计算 |

**scikit-learn 依赖关系:**
- numpy (数值计算基础)
- scipy (科学计算函数)
- joblib (模型序列化和并行处理)
- threadpoolctl (BLAS/LAPACK 线程控制)

### 🗄️ 数据库连接
| 包名 | 版本要求 | 状态 | 说明 |
|------|----------|------|------|
| PyMySQL | >=1.0.2 | ✅ 已安装 | MySQL数据库连接 |
| redis | >=4.0.0 | ❌ 未安装 | Redis缓存数据库 |

### 🌐 网络通信
| 包名 | 版本要求 | 状态 | 说明 |
|------|----------|------|------|
| requests | >=2.25.0 | ✅ 已安装 | HTTP请求 |
| urllib3 | >=1.26.0 | ✅ 已安装 | HTTP客户端 |
| pyzmq | >=24.0.0 | ✅ 已安装 | ZeroMQ消息队列 (MT4通信) |
| websocket-client | >=1.2.0 | ❌ 未安装 | WebSocket客户端 |

### 🖥️ 系统监控
| 包名 | 版本要求 | 状态 | 说明 |
|------|----------|------|------|
| psutil | >=7.0.0 | ✅ 已安装 | 系统资源监控 |

### 📊 统计分析
| 包名 | 版本要求 | 状态 | 说明 |
|------|----------|------|------|
| statsmodels | >=0.13.0 | ✅ 已安装 | 统计建模 |

### 📈 技术分析 (可选)
| 包名 | 版本要求 | 状态 | 说明 |
|------|----------|------|------|
| TA-Lib | >=0.4.24 | ❌ 未安装 | 技术分析指标库 |
| pandas-ta | >=0.3.14b | ❌ 未安装 | Pandas技术分析扩展 |

### 📊 数据可视化 (可选)
| 包名 | 版本要求 | 状态 | 说明 |
|------|----------|------|------|
| matplotlib | >=3.5.0 | ❌ 未安装 | 静态图表绘制 |
| plotly | >=5.0.0 | ❌ 未安装 | 交互式图表 |

### 🔧 开发工具 (可选)
| 包名 | 版本要求 | 状态 | 说明 |
|------|----------|------|------|
| pytest | >=6.2.0 | ❌ 未安装 | 测试框架 |
| black | >=22.0.0 | ❌ 未安装 | 代码格式化 |
| flake8 | >=4.0.0 | ❌ 未安装 | 代码质量检查 |

## ✅ 当前安装状态

### 已安装的核心包 (系统可正常运行)
- pandas 2.1.1
- numpy 1.26.0
- scikit-learn 1.6.1
- joblib 1.5.1
- threadpoolctl 3.6.0
- psutil 7.0.0
- scipy 1.15.3
- requests 2.31.0
- PyMySQL 1.1.0
- python-dateutil 2.9.0.post0
- pyzmq 25.1.1
- statsmodels 0.14.4

### 可选安装包
根据需要安装以下包来扩展功能：

```bash
# 技术分析
pip install TA-Lib pandas-ta

# 数据可视化
pip install matplotlib plotly

# 缓存数据库
pip install redis

# 开发工具
pip install pytest black flake8

# 高级机器学习
pip install xgboost lightgbm
```

## 🔍 依赖关系图

```
QuantumForex Pro
├── 核心运行时
│   ├── pandas (数据处理)
│   ├── numpy (数值计算)
│   ├── requests (网络请求)
│   └── PyMySQL (数据库)
├── 机器学习
│   ├── scikit-learn
│   │   ├── numpy
│   │   ├── scipy
│   │   ├── joblib
│   │   └── threadpoolctl
│   └── statsmodels
├── 系统监控
│   └── psutil
└── 通信
    └── pyzmq (MT4连接)
```

## 📝 安装建议

1. **最小安装** (仅核心功能):
   ```bash
   pip install pandas numpy scikit-learn psutil scipy requests pymysql pyzmq statsmodels
   ```

2. **推荐安装** (包含常用功能):
   ```bash
   pip install pandas numpy scikit-learn psutil scipy requests pymysql pyzmq statsmodels matplotlib redis
   ```

3. **完整安装** (所有功能):
   ```bash
   pip install -r requirements.txt
   ```

## ⚠️ 注意事项

- **TA-Lib**: 需要预编译的二进制文件，Windows用户建议从 https://www.lfd.uci.edu/~gohlke/pythonlibs/ 下载whl文件安装
- **系统要求**: Python 3.8+ (推荐 3.11+)
- **内存要求**: 建议至少 4GB RAM
- **磁盘空间**: 建议至少 2GB 可用空间

## 🔄 更新记录

- 2025-05-28: 初始版本，验证了核心依赖包的兼容性
- 已验证 scikit-learn 1.6.1 及其依赖包正常工作
