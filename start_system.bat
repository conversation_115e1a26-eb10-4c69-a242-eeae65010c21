@echo off
chcp 65001
echo ========================================
echo 外汇交易系统 - 智能启动
echo ========================================

REM 清除可能存在的手动设置，启用智能模式
set SKIP_MT4_CONNECTION=

echo 🧠 智能模式已启用
echo 🧠 系统将根据市场时间自动判断MT4连接策略
echo.

REM 检查虚拟环境
if not exist "venv\Scripts\activate.bat" (
    echo ❌ 错误: 未找到虚拟环境
    echo 请先运行 setup_environment.bat 安装依赖
    pause
    exit /b 1
)

echo 🚀 正在启动外汇交易系统...
echo.

REM 激活虚拟环境并运行系统
call venv\Scripts\activate.bat && python run.py

echo.
echo 系统已退出
pause
