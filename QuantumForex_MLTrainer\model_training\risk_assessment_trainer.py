"""
QuantumForex MLTrainer 风险评估模型训练器
训练风险评估和分类模型
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Any
from sklearn.preprocessing import LabelEncoder

from .base_trainer import BaseModelTrainer

class RiskAssessmentTrainer(BaseModelTrainer):
    """风险评估模型训练器"""

    def __init__(self):
        super().__init__('classification')
        self.logger = logging.getLogger(__name__)

        # 风险评估配置
        self.risk_windows = [10, 20, 50]  # 风险分析窗口
        self.risk_levels = ['LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH']  # 风险等级

    def calculate_risk_metrics(self, df: pd.DataFrame, window: int = 20) -> Dict[str, pd.Series]:
        """计算风险指标"""
        try:
            risk_metrics = {}
            
            # 价格变化率
            returns = df['close'].pct_change()
            
            # 1. 波动率风险
            volatility = returns.rolling(window=window).std() * np.sqrt(window)
            risk_metrics['volatility_risk'] = volatility
            
            # 2. 最大回撤风险
            cumulative_returns = (1 + returns).cumprod()
            rolling_max = cumulative_returns.rolling(window=window).max()
            drawdown = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = drawdown.rolling(window=window).min()
            risk_metrics['drawdown_risk'] = abs(max_drawdown)
            
            # 3. VaR风险（Value at Risk）
            var_95 = returns.rolling(window=window).quantile(0.05)
            risk_metrics['var_risk'] = abs(var_95)
            
            # 4. 价格跳跃风险
            price_jumps = abs(returns) > returns.rolling(window=window).std() * 3
            jump_frequency = price_jumps.rolling(window=window).sum() / window
            risk_metrics['jump_risk'] = jump_frequency
            
            # 5. 趋势反转风险
            short_ma = df['close'].rolling(window=window//2).mean()
            long_ma = df['close'].rolling(window=window).mean()
            trend_strength = abs(short_ma - long_ma) / long_ma
            risk_metrics['trend_reversal_risk'] = 1 / (1 + trend_strength)  # 趋势越弱，反转风险越高
            
            return risk_metrics
            
        except Exception as e:
            self.logger.error(f"❌ 计算风险指标失败: {e}")
            return {}

    def create_risk_target(self, df: pd.DataFrame, method: str = 'composite_risk',
                          window: int = 20, horizon: int = 5) -> pd.Series:
        """创建风险评估目标变量"""
        try:
            self.logger.info(f"📊 创建风险目标变量: {method}, 窗口={window}, 预测范围={horizon}")

            if method == 'composite_risk':
                # 综合风险评估
                risk_metrics = self.calculate_risk_metrics(df, window)
                
                # 计算未来风险
                future_risk_scores = []
                for metric_name, metric_values in risk_metrics.items():
                    future_metric = metric_values.shift(-horizon)
                    # 标准化到0-1范围
                    normalized_metric = (future_metric - future_metric.min()) / (future_metric.max() - future_metric.min())
                    future_risk_scores.append(normalized_metric)
                
                # 综合风险评分
                composite_risk = pd.concat(future_risk_scores, axis=1).mean(axis=1)
                
                # 风险等级分类
                target = pd.Series(index=df.index, dtype=int)
                target[composite_risk <= 0.25] = 0  # LOW
                target[(composite_risk > 0.25) & (composite_risk <= 0.5)] = 1  # MEDIUM
                target[(composite_risk > 0.5) & (composite_risk <= 0.75)] = 2  # HIGH
                target[composite_risk > 0.75] = 3  # VERY_HIGH
                
            elif method == 'volatility_risk':
                # 波动率风险分类
                risk_metrics = self.calculate_risk_metrics(df, window)
                future_volatility_risk = risk_metrics['volatility_risk'].shift(-horizon)
                
                vol_33 = future_volatility_risk.quantile(0.33)
                vol_67 = future_volatility_risk.quantile(0.67)
                
                target = pd.Series(index=df.index, dtype=int)
                target[future_volatility_risk <= vol_33] = 0  # LOW
                target[(future_volatility_risk > vol_33) & (future_volatility_risk <= vol_67)] = 1  # MEDIUM
                target[future_volatility_risk > vol_67] = 2  # HIGH
                
            elif method == 'drawdown_risk':
                # 回撤风险分类
                risk_metrics = self.calculate_risk_metrics(df, window)
                future_drawdown_risk = risk_metrics['drawdown_risk'].shift(-horizon)
                
                dd_33 = future_drawdown_risk.quantile(0.33)
                dd_67 = future_drawdown_risk.quantile(0.67)
                
                target = pd.Series(index=df.index, dtype=int)
                target[future_drawdown_risk <= dd_33] = 0  # LOW
                target[(future_drawdown_risk > dd_33) & (future_drawdown_risk <= dd_67)] = 1  # MEDIUM
                target[future_drawdown_risk > dd_67] = 2  # HIGH
                
            elif method == 'market_stress':
                # 市场压力测试
                target = self._create_market_stress_target(df, window, horizon)
                
            else:
                raise ValueError(f"不支持的风险目标方法: {method}")

            # 删除NaN值
            target = target.dropna()

            self.logger.info(f"✅ 风险目标变量创建完成: {len(target)}个样本")
            self.logger.info(f"📊 风险等级分布: {target.value_counts().to_dict()}")

            return target

        except Exception as e:
            self.logger.error(f"❌ 创建风险目标变量失败: {e}")
            raise

    def _create_market_stress_target(self, df: pd.DataFrame, window: int, horizon: int) -> pd.Series:
        """创建市场压力目标变量"""
        try:
            # 计算市场压力指标
            returns = df['close'].pct_change()
            
            # 1. 连续下跌天数
            negative_returns = (returns < 0).astype(int)
            consecutive_losses = negative_returns.rolling(window=horizon).sum()
            
            # 2. 极端价格变动
            extreme_moves = abs(returns) > returns.rolling(window=window).std() * 2
            extreme_frequency = extreme_moves.rolling(window=horizon).sum()
            
            # 3. 成交量异常
            volume_ma = df['volume'].rolling(window=window).mean()
            volume_spike = df['volume'] > volume_ma * 2
            volume_stress = volume_spike.rolling(window=horizon).sum()
            
            # 综合压力评分
            stress_score = (consecutive_losses + extreme_frequency + volume_stress) / 3
            future_stress = stress_score.shift(-horizon)
            
            target = pd.Series(index=df.index, dtype=int)
            target[future_stress <= 1] = 0  # 低压力
            target[(future_stress > 1) & (future_stress <= 2)] = 1  # 中等压力
            target[future_stress > 2] = 2  # 高压力
            
            return target

        except Exception as e:
            self.logger.error(f"❌ 创建市场压力目标变量失败: {e}")
            return pd.Series()

    def train_composite_risk_model(self, df: pd.DataFrame, feature_columns: List[str],
                                  window: int = 20, horizon: int = 5) -> Dict[str, Any]:
        """训练综合风险评估模型"""
        try:
            self.logger.info(f"🧠 训练综合风险评估模型: 窗口={window}, 预测范围={horizon}")

            # 创建目标变量
            target = self.create_risk_target(df, 'composite_risk', window, horizon)
            target.name = 'target'

            # 对齐数据
            common_index = df.index.intersection(target.index)
            df_aligned = df.loc[common_index].copy()
            target_aligned = target.loc[common_index]

            # 添加目标列到DataFrame
            df_aligned['target'] = target_aligned

            # 准备数据
            X, y, feature_names = self.prepare_data(df_aligned, 'target', feature_columns)

            # 分割数据
            X_train, X_test, y_train, y_test = self.split_data(X, y)

            # 特征标准化
            X_train_scaled, X_test_scaled = self.scale_features(X_train, X_test, f'risk_composite_{window}_{horizon}')

            # 训练模型
            results = self.train_models(X_train_scaled, y_train, X_test_scaled, y_test, feature_names)

            # 获取最佳模型
            best_model_name, best_model = self.get_best_model(results, 'f1_score')

            # 保存模型
            model_prefix = f'risk_assessment_{window}min_{horizon}min'
            self.save_models(model_prefix)

            # 添加训练记录
            training_record = {
                'model_type': 'composite_risk',
                'window': window,
                'horizon': horizon,
                'best_model': best_model_name,
                'results': {name: result['metrics'] for name, result in results.items()},
                'feature_count': len(feature_names),
                'sample_count': len(X)
            }
            self.training_history.append(training_record)

            return results

        except Exception as e:
            self.logger.error(f"❌ 训练综合风险评估模型失败: {e}")
            return {}

    def train_volatility_risk_model(self, df: pd.DataFrame, feature_columns: List[str],
                                   window: int = 20, horizon: int = 5) -> Dict[str, Any]:
        """训练波动率风险模型"""
        try:
            self.logger.info(f"🧠 训练波动率风险模型: 窗口={window}, 预测范围={horizon}")

            # 创建目标变量
            target = self.create_risk_target(df, 'volatility_risk', window, horizon)
            target.name = 'target'

            # 对齐数据
            common_index = df.index.intersection(target.index)
            df_aligned = df.loc[common_index].copy()
            target_aligned = target.loc[common_index]

            # 添加目标列到DataFrame
            df_aligned['target'] = target_aligned

            # 准备数据
            X, y, feature_names = self.prepare_data(df_aligned, 'target', feature_columns)

            # 分割数据
            X_train, X_test, y_train, y_test = self.split_data(X, y)

            # 特征标准化
            X_train_scaled, X_test_scaled = self.scale_features(X_train, X_test, f'risk_volatility_{window}_{horizon}')

            # 训练模型
            results = self.train_models(X_train_scaled, y_train, X_test_scaled, y_test, feature_names)

            # 获取最佳模型
            best_model_name, best_model = self.get_best_model(results, 'f1_score')

            # 保存模型
            model_prefix = f'risk_assessment_volatility_{window}min_{horizon}min'
            self.save_models(model_prefix)

            return results

        except Exception as e:
            self.logger.error(f"❌ 训练波动率风险模型失败: {e}")
            return {}

    def train_all_risk_models(self, df: pd.DataFrame, feature_columns: List[str]) -> Dict[str, Any]:
        """训练所有风险评估模型"""
        try:
            self.logger.info("🚀 开始训练所有风险评估模型...")

            all_results = {}

            # 训练综合风险模型
            for window in [20]:
                for horizon in [5, 10]:
                    try:
                        results = self.train_composite_risk_model(df, feature_columns, window, horizon)
                        all_results[f'composite_{window}_{horizon}'] = results
                    except Exception as e:
                        self.logger.error(f"训练综合风险模型失败 (window={window}, horizon={horizon}): {e}")

            # 训练波动率风险模型
            for window in [10, 20]:
                for horizon in [5]:
                    try:
                        results = self.train_volatility_risk_model(df, feature_columns, window, horizon)
                        all_results[f'volatility_{window}_{horizon}'] = results
                    except Exception as e:
                        self.logger.error(f"训练波动率风险模型失败 (window={window}, horizon={horizon}): {e}")

            self.logger.info(f"🎉 所有风险评估模型训练完成: {len(all_results)}个模型组合")
            return all_results

        except Exception as e:
            self.logger.error(f"❌ 训练所有风险评估模型失败: {e}")
            return {}

# 创建风险评估训练器实例
risk_trainer = RiskAssessmentTrainer()
