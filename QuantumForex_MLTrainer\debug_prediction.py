#!/usr/bin/env python3
"""
调试预测生成问题
"""

import sys
import os
import pandas as pd
import numpy as np
import joblib
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'QuantumForex_Pro'))

def debug_prediction_generation():
    print("🔍 调试预测生成问题")
    print("="*40)
    
    try:
        # 1. 加载模型
        models_dir = Path("data/models")
        price_models = list(models_dir.glob("*price_prediction*compatible*.pkl"))
        price_models = [f for f in price_models if 'scaler' not in f.name]
        
        if not price_models:
            print("❌ 没有找到模型")
            return
        
        model_path = price_models[0]
        print(f"📦 使用模型: {model_path.name}")
        
        # 加载模型
        model = joblib.load(model_path)
        print(f"✅ 模型类型: {type(model).__name__}")
        
        # 2. 获取测试数据
        from utils.db_client import execute_query
        
        sql = """
        SELECT 
            time_date_str as timestamp,
            price as open,
            max as high,
            min as low,
            price as close,
            volume
        FROM min_quote_eurusd
        ORDER BY time_min_int DESC
        LIMIT 100
        """
        
        raw_data = execute_query(sql)
        print(f"📊 获取数据: {len(raw_data)}条")
        
        # 转换为DataFrame
        df = pd.DataFrame(raw_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp')
        df = df.sort_index()
        
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df = df.dropna()
        print(f"✅ 处理后数据: {len(df)}条")
        
        # 3. 计算特征
        print("\n🔧 计算特征...")
        
        # 价格特征
        df['price_change'] = df['close'].pct_change()
        df['price_volatility'] = df['price_change'].rolling(20, min_periods=5).std()
        df['high_low_ratio'] = (df['high'] - df['low']) / df['close']
        
        # 移动平均
        for period in [5, 10, 20]:
            df[f'ma_{period}'] = df['close'].rolling(period, min_periods=max(1, period//2)).mean()
            df[f'price_ma_ratio_{period}'] = df['close'] / df[f'ma_{period}']
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14, min_periods=5).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14, min_periods=5).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        df['bb_middle'] = df['close'].rolling(20, min_periods=5).mean()
        df['bb_std'] = df['close'].rolling(20, min_periods=5).std()
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # 成交量指标
        df['volume_ma'] = df['volume'].rolling(20, min_periods=5).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma']
        
        print(f"✅ 特征计算完成: {len(df.columns)}个特征")
        print(f"   特征列表: {list(df.columns)}")
        
        # 4. 检查数据质量
        print(f"\n📊 数据质量检查:")
        print(f"   总行数: {len(df)}")
        print(f"   NaN值统计:")
        for col in df.columns:
            nan_count = df[col].isnull().sum()
            if nan_count > 0:
                print(f"     {col}: {nan_count}个NaN")
        
        # 5. 尝试预测
        print(f"\n🔮 尝试预测...")
        
        predictions = 0
        valid_predictions = 0
        
        for i, (idx, row) in enumerate(df.iterrows()):
            if i < 20:  # 跳过前20行
                continue
            
            try:
                # 提取特征
                features = [
                    row['close'],
                    row['price_change'],
                    row['price_volatility'],
                    row['high_low_ratio'],
                    row['rsi'],
                    row['bb_position'],
                    row['volume_ratio']
                ]
                
                # 移动平均比率
                for period in [5, 10, 20]:
                    features.append(row[f'price_ma_ratio_{period}'])
                
                # 滞后特征
                for lag in [1, 2, 3]:
                    if i >= lag:
                        features.append(df.iloc[i-lag]['price_change'])
                    else:
                        features.append(0)
                
                # 过滤NaN值
                features = np.array(features, dtype=float)
                features = np.nan_to_num(features, nan=0.0)
                
                print(f"   行{i}: 特征数量={len(features)}")
                print(f"        特征值: {features[:5]}...")  # 显示前5个特征
                
                # 检查特征是否有效
                if np.any(np.isnan(features)) or np.any(np.isinf(features)):
                    print(f"        ❌ 特征包含NaN或Inf")
                    continue
                
                # 模型预测
                features_reshaped = features.reshape(1, -1)
                prediction = model.predict(features_reshaped)[0]
                
                print(f"        ✅ 预测值: {prediction}")
                
                predictions += 1
                if abs(prediction) > 0.000001:
                    valid_predictions += 1
                
                if predictions >= 5:  # 只测试前5个
                    break
                    
            except Exception as e:
                print(f"        ❌ 预测失败: {e}")
                continue
        
        print(f"\n📊 预测结果:")
        print(f"   总预测数: {predictions}")
        print(f"   有效预测: {valid_predictions}")
        print(f"   有效率: {valid_predictions/predictions:.1%}" if predictions > 0 else "   有效率: 0%")
        
        if predictions == 0:
            print("❌ 没有生成任何预测！")
            print("💡 可能的原因:")
            print("   1. 数据质量问题")
            print("   2. 特征计算错误")
            print("   3. 模型输入格式不匹配")
        elif valid_predictions == 0:
            print("❌ 所有预测都是0！")
            print("💡 可能的原因:")
            print("   1. 模型训练有问题")
            print("   2. 特征标准化问题")
            print("   3. 模型过拟合")
        else:
            print("✅ 预测生成正常！")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_prediction_generation()
