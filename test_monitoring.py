#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试监控系统
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_monitoring_system():
    """测试监控系统"""
    try:
        print("正在测试监控系统...")
        
        # 导入监控系统
        from app.utils.real_time_monitor import real_time_monitor
        print("✅ 监控系统导入成功")
        
        # 测试交易指标收集
        trading_metrics = real_time_monitor._collect_trading_metrics()
        print(f"✅ 交易指标收集成功:")
        print(f"   总交易数: {trading_metrics.total_trades}")
        print(f"   盈利交易: {trading_metrics.winning_trades}")
        print(f"   亏损交易: {trading_metrics.losing_trades}")
        print(f"   胜率: {trading_metrics.win_rate:.1f}%")
        print(f"   净利润: ${trading_metrics.net_profit:.2f}")
        print(f"   当前持仓: {trading_metrics.current_positions}")
        print(f"   挂单数量: {trading_metrics.pending_orders}")
        print(f"   风险等级: {trading_metrics.risk_level}")
        
        # 测试系统指标收集
        system_metrics = real_time_monitor._collect_system_metrics()
        print(f"✅ 系统指标收集成功:")
        print(f"   CPU使用率: {system_metrics.cpu_percent:.1f}%")
        print(f"   内存使用率: {system_metrics.memory_percent:.1f}%")
        print(f"   系统状态: {system_metrics.system_status}")
        
        # 测试分析指标收集
        analysis_metrics = real_time_monitor._collect_analysis_metrics()
        print(f"✅ 分析指标收集成功:")
        print(f"   总分析次数: {analysis_metrics.total_analyses}")
        print(f"   成功率: {analysis_metrics.success_rate:.1f}%")
        print(f"   Token消耗: {analysis_metrics.total_tokens}")
        print(f"   总成本: ¥{analysis_metrics.total_cost:.4f}")
        
        # 测试告警系统
        real_time_monitor.add_alert('info', 'test', '监控系统测试成功')
        print("✅ 告警系统测试成功")
        
        # 测试状态获取
        status = real_time_monitor.get_current_status()
        print(f"✅ 状态获取成功，监控状态: {status['monitoring_status']}")
        
        print("\n🎉 监控系统测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ 监控系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_monitoring_system()
