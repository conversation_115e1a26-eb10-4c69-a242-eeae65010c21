#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能出场管理器
解决固定止盈太呆板的问题，实现智能的收益保护
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class ExitSignal(Enum):
    """出场信号类型"""
    HOLD = "hold"                    # 继续持有
    PARTIAL_EXIT = "partial_exit"    # 部分出场
    FULL_EXIT = "full_exit"         # 全部出场
    TRAIL_STOP = "trail_stop"       # 移动止损

@dataclass
class ExitDecision:
    """出场决策"""
    signal: ExitSignal
    exit_ratio: float  # 出场比例 (0.0-1.0)
    new_stop_loss: Optional[float] = None
    reason: str = ""
    confidence: float = 0.0

class IntelligentExitManager:
    """智能出场管理器
    
    核心功能：
    1. 分层保护机制 - 不同收益水平采用不同保护策略
    2. 趋势变化检测 - 识别市场变盘信号
    3. 动态止损调整 - 根据市场条件移动止损
    4. 收益保护 - 在变盘时保护已有收益
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 分层保护配置
        self.protection_levels = {
            0.5: {'trail_ratio': 0.3, 'sensitivity': 0.8},   # 50%收益：保护30%，敏感度0.8
            1.0: {'trail_ratio': 0.5, 'sensitivity': 0.6},   # 100%收益：保护50%，敏感度0.6
            1.5: {'trail_ratio': 0.7, 'sensitivity': 0.4},   # 150%收益：保护70%，敏感度0.4
            2.0: {'trail_ratio': 0.8, 'sensitivity': 0.3}    # 200%收益：保护80%，敏感度0.3
        }
        
        # 趋势变化检测参数
        self.trend_change_threshold = 0.6  # 趋势变化阈值
        self.momentum_lookback = 10        # 动量回看期数
        
    def analyze_exit_conditions(self, position_info: Dict, market_data: Dict) -> ExitDecision:
        """分析出场条件
        
        Args:
            position_info: 持仓信息 {
                'symbol': str,
                'entry_price': float,
                'current_price': float,
                'stop_loss': float,
                'take_profit': float,
                'position_size': float,
                'entry_time': datetime,
                'unrealized_pnl': float,
                'unrealized_pnl_pct': float
            }
            market_data: 市场数据 {
                'ohlcv': DataFrame,
                'atr': float,
                'trend_strength': float,
                'momentum': float
            }
            
        Returns:
            ExitDecision: 出场决策
        """
        try:
            symbol = position_info['symbol']
            entry_price = position_info['entry_price']
            current_price = position_info['current_price']
            unrealized_pnl_pct = position_info.get('unrealized_pnl_pct', 0.0)
            
            # 1. 检查是否达到分层保护条件
            protection_decision = self._check_protection_levels(position_info, market_data)
            if protection_decision.signal != ExitSignal.HOLD:
                return protection_decision
            
            # 2. 检查趋势变化
            trend_decision = self._check_trend_change(position_info, market_data)
            if trend_decision.signal != ExitSignal.HOLD:
                return trend_decision
            
            # 3. 检查动量衰减
            momentum_decision = self._check_momentum_decay(position_info, market_data)
            if momentum_decision.signal != ExitSignal.HOLD:
                return momentum_decision
            
            # 4. 检查时间衰减
            time_decision = self._check_time_decay(position_info, market_data)
            if time_decision.signal != ExitSignal.HOLD:
                return time_decision
            
            # 5. 默认继续持有
            return ExitDecision(
                signal=ExitSignal.HOLD,
                exit_ratio=0.0,
                reason="所有条件正常，继续持有",
                confidence=0.7
            )
            
        except Exception as e:
            self.logger.error(f"分析出场条件失败: {e}")
            return ExitDecision(
                signal=ExitSignal.HOLD,
                exit_ratio=0.0,
                reason="分析失败，保持现状",
                confidence=0.0
            )
    
    def _check_protection_levels(self, position_info: Dict, market_data: Dict) -> ExitDecision:
        """检查分层保护条件"""
        try:
            unrealized_pnl_pct = position_info.get('unrealized_pnl_pct', 0.0)
            
            # 只对盈利仓位进行保护
            if unrealized_pnl_pct <= 0:
                return ExitDecision(ExitSignal.HOLD, 0.0)
            
            # 找到适用的保护级别
            applicable_level = None
            for level in sorted(self.protection_levels.keys(), reverse=True):
                if unrealized_pnl_pct >= level:
                    applicable_level = level
                    break
            
            if applicable_level is None:
                return ExitDecision(ExitSignal.HOLD, 0.0)
            
            config = self.protection_levels[applicable_level]
            
            # 计算移动止损位置
            entry_price = position_info['entry_price']
            current_price = position_info['current_price']
            
            # 假设是多头仓位（实际应该根据仓位方向判断）
            profit_distance = current_price - entry_price
            trail_distance = profit_distance * config['trail_ratio']
            new_stop_loss = entry_price + trail_distance
            
            # 检查是否需要调整止损
            current_stop = position_info.get('stop_loss', entry_price)
            if new_stop_loss > current_stop:
                return ExitDecision(
                    signal=ExitSignal.TRAIL_STOP,
                    exit_ratio=0.0,
                    new_stop_loss=new_stop_loss,
                    reason=f"收益{unrealized_pnl_pct:.1%}，启动{applicable_level:.1%}级保护",
                    confidence=0.8
                )
            
            return ExitDecision(ExitSignal.HOLD, 0.0)
            
        except Exception as e:
            self.logger.error(f"检查保护级别失败: {e}")
            return ExitDecision(ExitSignal.HOLD, 0.0)
    
    def _check_trend_change(self, position_info: Dict, market_data: Dict) -> ExitDecision:
        """检查趋势变化"""
        try:
            trend_strength = market_data.get('trend_strength', 0.0)
            
            # 趋势强度显著下降
            if abs(trend_strength) < self.trend_change_threshold:
                unrealized_pnl_pct = position_info.get('unrealized_pnl_pct', 0.0)
                
                # 如果有收益，部分出场保护
                if unrealized_pnl_pct > 0.3:  # 30%以上收益
                    return ExitDecision(
                        signal=ExitSignal.PARTIAL_EXIT,
                        exit_ratio=0.5,  # 出场50%
                        reason=f"趋势强度下降至{trend_strength:.2f}，部分获利了结",
                        confidence=0.7
                    )
            
            return ExitDecision(ExitSignal.HOLD, 0.0)
            
        except Exception as e:
            self.logger.error(f"检查趋势变化失败: {e}")
            return ExitDecision(ExitSignal.HOLD, 0.0)
    
    def _check_momentum_decay(self, position_info: Dict, market_data: Dict) -> ExitDecision:
        """检查动量衰减"""
        try:
            ohlcv = market_data.get('ohlcv')
            if ohlcv is None or len(ohlcv) < self.momentum_lookback:
                return ExitDecision(ExitSignal.HOLD, 0.0)
            
            # 计算最近的动量变化
            recent_closes = ohlcv['close'].tail(self.momentum_lookback).values
            momentum_changes = np.diff(recent_closes)
            
            # 检查动量是否持续衰减
            negative_momentum_count = sum(1 for change in momentum_changes[-5:] if change < 0)
            
            if negative_momentum_count >= 4:  # 最近5期中有4期下跌
                unrealized_pnl_pct = position_info.get('unrealized_pnl_pct', 0.0)
                
                if unrealized_pnl_pct > 0.5:  # 50%以上收益
                    return ExitDecision(
                        signal=ExitSignal.PARTIAL_EXIT,
                        exit_ratio=0.3,  # 出场30%
                        reason="动量持续衰减，部分获利了结",
                        confidence=0.6
                    )
            
            return ExitDecision(ExitSignal.HOLD, 0.0)
            
        except Exception as e:
            self.logger.error(f"检查动量衰减失败: {e}")
            return ExitDecision(ExitSignal.HOLD, 0.0)
    
    def _check_time_decay(self, position_info: Dict, market_data: Dict) -> ExitDecision:
        """检查时间衰减"""
        try:
            entry_time = position_info.get('entry_time')
            if entry_time is None:
                return ExitDecision(ExitSignal.HOLD, 0.0)
            
            # 计算持仓时间
            holding_hours = (datetime.now() - entry_time).total_seconds() / 3600
            
            # 长时间持仓且收益不佳
            if holding_hours > 24:  # 超过24小时
                unrealized_pnl_pct = position_info.get('unrealized_pnl_pct', 0.0)
                
                if 0 < unrealized_pnl_pct < 0.3:  # 小幅盈利
                    return ExitDecision(
                        signal=ExitSignal.PARTIAL_EXIT,
                        exit_ratio=0.5,  # 出场50%
                        reason=f"持仓{holding_hours:.1f}小时，收益{unrealized_pnl_pct:.1%}，部分获利",
                        confidence=0.5
                    )
            
            return ExitDecision(ExitSignal.HOLD, 0.0)
            
        except Exception as e:
            self.logger.error(f"检查时间衰减失败: {e}")
            return ExitDecision(ExitSignal.HOLD, 0.0)

# 创建全局实例
intelligent_exit_manager = IntelligentExitManager()
