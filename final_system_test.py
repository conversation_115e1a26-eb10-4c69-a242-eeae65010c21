#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终系统测试 - 验证所有修复和优化
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_json_fixes():
    """测试JSON修复功能"""
    print("🔧 测试JSON修复功能")
    
    import json
    import re
    
    # 测试用例
    test_cases = [
        ('正常JSON', '{"status":"success","orders":[]}'),
        ('末尾逗号', '{"status":"success","orders":[{"id":"123",}]}'),
        ('截断JSON', '{"status":"success","orders":[{"id":"123"'),
        ('不完整数组', '{"status":"success","orders":[{"id":"123"},')
    ]
    
    for name, broken_json in test_cases:
        print(f"  测试: {name}")
        try:
            result = json.loads(broken_json)
            print(f"    ✅ 直接解析成功")
        except json.JSONDecodeError:
            # 应用修复逻辑
            fixed_response = broken_json
            
            # 修复末尾多余的逗号
            fixed_response = re.sub(r',\s*}', '}', fixed_response)
            fixed_response = re.sub(r',\s*]', ']', fixed_response)
            
            # 修复截断的JSON
            if not fixed_response.endswith('}') and not fixed_response.endswith(']'):
                if '"orders":[' in fixed_response:
                    open_brackets = fixed_response.count('[')
                    close_brackets = fixed_response.count(']')
                    open_braces = fixed_response.count('{')
                    close_braces = fixed_response.count('}')
                    
                    if open_brackets > close_brackets:
                        fixed_response += ']' * (open_brackets - close_brackets)
                    if open_braces > close_braces:
                        fixed_response += '}' * (open_braces - close_braces)
                elif not fixed_response.endswith('}'):
                    fixed_response += '}'
            
            try:
                result = json.loads(fixed_response)
                print(f"    ✅ 修复后解析成功")
            except:
                print(f"    ⚠️ 修复失败，使用备用解析")

def test_mt4_connection():
    """测试MT4连接和基本操作"""
    print("\n🔌 测试MT4连接")
    
    try:
        from app.utils.mt4_client import MT4Client
        mt4_client = MT4Client()
        
        if mt4_client.connect():
            print("  ✅ MT4连接成功")
            
            # 测试市场信息
            market_info = mt4_client.get_market_info("EURUSD")
            if market_info.get('status') == 'success':
                data = market_info['data']
                print(f"  ✅ 市场信息: Bid={data['bid']}, Ask={data['ask']}")
            
            # 测试账户信息
            account_info = mt4_client.get_account_info()
            if account_info.get('status') == 'success':
                data = account_info['data']
                print(f"  ✅ 账户信息: 余额={data.get('balance')}, 净值={data.get('equity')}")
            
            # 测试订单信息
            active_orders = mt4_client.get_active_orders()
            pending_orders = mt4_client.get_pending_orders()
            
            active_count = len(active_orders.get('orders', []))
            pending_count = len(pending_orders.get('orders', []))
            
            print(f"  ✅ 订单状态: 活跃订单{active_count}个, 挂单{pending_count}个")
            
            return True, mt4_client
        else:
            print("  ❌ MT4连接失败")
            return False, None
            
    except Exception as e:
        print(f"  ❌ MT4测试失败: {e}")
        return False, None

def test_data_processing():
    """测试数据处理功能"""
    print("\n📊 测试数据处理")
    
    try:
        # 智能货币对选择
        from app.utils.intelligent_pair_selector import select_optimal_currency_pairs
        pairs = select_optimal_currency_pairs()
        print(f"  ✅ 智能选择: {pairs}")
        
        # 多货币对数据获取
        from app.utils.multi_pair_data_manager import get_multi_pair_analysis_data
        data = get_multi_pair_analysis_data(pairs)
        print(f"  ✅ 数据获取: {len(data)}个货币对")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据处理失败: {e}")
        return False

def test_trading_simulation():
    """测试交易模拟"""
    print("\n💼 测试交易模拟")
    
    try:
        # 模拟交易决策
        trade_decision = {
            'action': 'BUY',
            'symbol': 'EURUSD',
            'lotSize': 0.01,
            'stopLoss': 1.1380,
            'takeProfit': 1.1450,  # 更合理的止盈距离
            'reasoning': '测试交易决策'
        }
        
        print(f"  交易决策: {trade_decision['action']} {trade_decision['symbol']} {trade_decision['lotSize']}手")
        print(f"  止损: {trade_decision['stopLoss']}, 止盈: {trade_decision['takeProfit']}")
        
        # 模拟风险检查
        risk_level = "LOW"  # 小手数低风险
        print(f"  ✅ 风险等级: {risk_level}")
        
        # 模拟订单创建
        order_id = f"TEST_{int(trade_decision['stopLoss'] * 100000)}"
        print(f"  ✅ 模拟订单ID: {order_id}")
        
        # 模拟订单管理
        management_actions = [
            "监控订单状态",
            "检查止损止盈",
            "评估市场变化"
        ]
        
        print("  ✅ 订单管理:")
        for action in management_actions:
            print(f"    - {action}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 交易模拟失败: {e}")
        return False

def test_monitoring_system():
    """测试监控系统"""
    print("\n📈 测试监控系统")
    
    try:
        # 测试实时监控
        from app.utils.real_time_monitor import RealTimeMonitor
        monitor = RealTimeMonitor()
        
        status = monitor.get_current_status()
        print(f"  ✅ 监控状态: {status.get('monitoring_status', 'active')}")
        
        # 测试Token统计
        from app.utils.token_statistics import get_token_summary
        token_summary = get_token_summary()
        print(f"  ✅ Token统计: {token_summary}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 监控系统失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🛡️ 测试错误处理")
    
    try:
        # 测试错误收集器
        from app.utils.error_collector import make_json_serializable
        
        # 测试复杂对象序列化
        test_obj = {
            'simple': 'value',
            'complex': {'nested': 'data'},
            'list': [1, 2, 3]
        }
        
        serializable = make_json_serializable(test_obj)
        print("  ✅ JSON序列化测试通过")
        
        # 测试错误日志
        from app.utils.error_logger import log_error, ErrorType
        log_error(ErrorType.SYSTEM_ERROR, "测试错误", "test_component")
        print("  ✅ 错误日志测试通过")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 错误处理失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 最终系统测试")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("JSON修复", test_json_fixes),
        ("MT4连接", test_mt4_connection),
        ("数据处理", test_data_processing),
        ("交易模拟", test_trading_simulation),
        ("监控系统", test_monitoring_system),
        ("错误处理", test_error_handling)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            if test_name == "MT4连接":
                success, mt4_client = test_func()
                results[test_name] = success
            else:
                success = test_func()
                results[test_name] = success
        except Exception as e:
            print(f"  ❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {test_name}")
    
    success_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！系统已准备好运行")
    elif success_count >= total_count * 0.8:
        print("✅ 大部分测试通过，系统基本可用")
    else:
        print("⚠️ 多个测试失败，需要进一步检查")
    
    print("\n💡 建议:")
    if results.get("MT4连接", False):
        print("  - MT4连接正常，可以进行实时交易")
    if results.get("数据处理", False):
        print("  - 数据处理正常，分析功能可用")
    if results.get("交易模拟", False):
        print("  - 交易逻辑正常，可以执行交易决策")
    if results.get("监控系统", False):
        print("  - 监控系统正常，可以实时跟踪")

if __name__ == "__main__":
    main()
