#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级策略优化系统
基于实际可用的8个交易品种，实现智能策略优化和参数调整
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

class OptimizationMethod(Enum):
    """优化方法"""
    GENETIC_ALGORITHM = "遗传算法"
    GRID_SEARCH = "网格搜索"
    BAYESIAN_OPTIMIZATION = "贝叶斯优化"
    RANDOM_SEARCH = "随机搜索"
    GRADIENT_DESCENT = "梯度下降"

class StrategyType(Enum):
    """策略类型"""
    TREND_FOLLOWING = "趋势跟随"
    MEAN_REVERSION = "均值回归"
    MOMENTUM = "动量策略"
    BREAKOUT = "突破策略"
    SCALPING = "剥头皮"
    SWING_TRADING = "波段交易"
    CARRY_TRADE = "套息交易"
    ARBITRAGE = "套利策略"

class OptimizationTarget(Enum):
    """优化目标"""
    MAXIMIZE_RETURN = "最大化收益"
    MAXIMIZE_SHARPE = "最大化夏普比率"
    MINIMIZE_DRAWDOWN = "最小化回撤"
    MAXIMIZE_WIN_RATE = "最大化胜率"
    MAXIMIZE_PROFIT_FACTOR = "最大化盈利因子"
    MINIMIZE_VOLATILITY = "最小化波动率"

@dataclass
class StrategyParameter:
    """策略参数"""
    name: str
    current_value: float
    min_value: float
    max_value: float
    step_size: float
    parameter_type: str  # 'float', 'int', 'bool'
    description: str

@dataclass
class OptimizationResult:
    """优化结果"""
    strategy_type: StrategyType
    symbol: str
    optimization_method: OptimizationMethod
    target: OptimizationTarget
    original_parameters: Dict[str, float]
    optimized_parameters: Dict[str, float]
    original_performance: Dict[str, float]
    optimized_performance: Dict[str, float]
    improvement_percentage: float
    confidence_score: float
    backtest_period: str
    optimization_date: datetime

@dataclass
class StrategyConfiguration:
    """策略配置"""
    strategy_type: StrategyType
    symbol: str
    parameters: Dict[str, StrategyParameter]
    performance_metrics: Dict[str, float]
    last_optimization: Optional[datetime]
    optimization_count: int

class AdvancedStrategyOptimizer:
    """高级策略优化器"""

    def __init__(self):
        # 支持的交易品种（基于实际数据库）
        self.supported_symbols = [
            'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD',
            'USDCHF', 'USDCAD', 'USDJPY', 'GOLD'
        ]

        # 策略参数模板
        self.strategy_templates = {
            StrategyType.TREND_FOLLOWING: {
                'ma_fast_period': StrategyParameter('ma_fast_period', 10, 5, 50, 1, 'int', '快速移动平均周期'),
                'ma_slow_period': StrategyParameter('ma_slow_period', 30, 20, 100, 1, 'int', '慢速移动平均周期'),
                'rsi_period': StrategyParameter('rsi_period', 14, 7, 30, 1, 'int', 'RSI周期'),
                'rsi_overbought': StrategyParameter('rsi_overbought', 70, 60, 85, 1, 'float', 'RSI超买阈值'),
                'rsi_oversold': StrategyParameter('rsi_oversold', 30, 15, 40, 1, 'float', 'RSI超卖阈值'),
                'stop_loss_pips': StrategyParameter('stop_loss_pips', 50, 20, 200, 5, 'float', '止损点数'),
                'take_profit_pips': StrategyParameter('take_profit_pips', 100, 30, 300, 5, 'float', '止盈点数'),
                'position_size': StrategyParameter('position_size', 0.1, 0.01, 1.0, 0.01, 'float', '仓位大小')
            },
            StrategyType.MEAN_REVERSION: {
                'bollinger_period': StrategyParameter('bollinger_period', 20, 10, 50, 1, 'int', '布林带周期'),
                'bollinger_std': StrategyParameter('bollinger_std', 2.0, 1.5, 3.0, 0.1, 'float', '布林带标准差'),
                'rsi_period': StrategyParameter('rsi_period', 14, 7, 30, 1, 'int', 'RSI周期'),
                'rsi_entry_high': StrategyParameter('rsi_entry_high', 80, 70, 90, 1, 'float', 'RSI入场高位'),
                'rsi_entry_low': StrategyParameter('rsi_entry_low', 20, 10, 30, 1, 'float', 'RSI入场低位'),
                'stop_loss_pips': StrategyParameter('stop_loss_pips', 30, 15, 100, 5, 'float', '止损点数'),
                'take_profit_pips': StrategyParameter('take_profit_pips', 60, 20, 150, 5, 'float', '止盈点数'),
                'position_size': StrategyParameter('position_size', 0.1, 0.01, 1.0, 0.01, 'float', '仓位大小')
            },
            StrategyType.BREAKOUT: {
                'breakout_period': StrategyParameter('breakout_period', 20, 10, 50, 1, 'int', '突破周期'),
                'atr_period': StrategyParameter('atr_period', 14, 7, 30, 1, 'int', 'ATR周期'),
                'atr_multiplier': StrategyParameter('atr_multiplier', 2.0, 1.0, 4.0, 0.1, 'float', 'ATR倍数'),
                'volume_threshold': StrategyParameter('volume_threshold', 1.5, 1.0, 3.0, 0.1, 'float', '成交量阈值'),
                'stop_loss_atr': StrategyParameter('stop_loss_atr', 2.0, 1.0, 4.0, 0.1, 'float', '止损ATR倍数'),
                'take_profit_atr': StrategyParameter('take_profit_atr', 4.0, 2.0, 8.0, 0.1, 'float', '止盈ATR倍数'),
                'position_size': StrategyParameter('position_size', 0.1, 0.01, 1.0, 0.01, 'float', '仓位大小')
            },
            StrategyType.SCALPING: {
                'ema_fast': StrategyParameter('ema_fast', 5, 3, 15, 1, 'int', '快速EMA'),
                'ema_slow': StrategyParameter('ema_slow', 15, 10, 30, 1, 'int', '慢速EMA'),
                'stoch_k': StrategyParameter('stoch_k', 5, 3, 10, 1, 'int', '随机指标K值'),
                'stoch_d': StrategyParameter('stoch_d', 3, 2, 5, 1, 'int', '随机指标D值'),
                'stoch_overbought': StrategyParameter('stoch_overbought', 80, 70, 90, 1, 'float', '随机指标超买'),
                'stoch_oversold': StrategyParameter('stoch_oversold', 20, 10, 30, 1, 'float', '随机指标超卖'),
                'stop_loss_pips': StrategyParameter('stop_loss_pips', 10, 5, 30, 1, 'float', '止损点数'),
                'take_profit_pips': StrategyParameter('take_profit_pips', 15, 8, 50, 1, 'float', '止盈点数'),
                'position_size': StrategyParameter('position_size', 0.05, 0.01, 0.5, 0.01, 'float', '仓位大小')
            }
        }

        # 优化配置
        self.optimization_config = {
            'max_iterations': 100,
            'population_size': 50,
            'mutation_rate': 0.1,
            'crossover_rate': 0.8,
            'convergence_threshold': 0.001,
            'min_trades_required': 30,
            'backtest_period_days': 90
        }

        # 数据存储
        self.strategy_configurations = {}
        self.optimization_history = []
        self.performance_cache = {}

        # 日志
        self.logger = logging.getLogger(__name__)

    def initialize_strategy(self, strategy_type: StrategyType, symbol: str) -> StrategyConfiguration:
        """初始化策略配置"""

        if symbol not in self.supported_symbols:
            raise ValueError(f"不支持的交易品种: {symbol}")

        if strategy_type not in self.strategy_templates:
            raise ValueError(f"不支持的策略类型: {strategy_type}")

        # 创建策略配置
        parameters = self.strategy_templates[strategy_type].copy()

        strategy_config = StrategyConfiguration(
            strategy_type=strategy_type,
            symbol=symbol,
            parameters=parameters,
            performance_metrics={},
            last_optimization=None,
            optimization_count=0
        )

        # 存储配置
        key = f"{strategy_type.value}_{symbol}"
        self.strategy_configurations[key] = strategy_config

        return strategy_config

    def optimize_strategy(self, strategy_type: StrategyType, symbol: str,
                         optimization_method: OptimizationMethod = OptimizationMethod.GENETIC_ALGORITHM,
                         target: OptimizationTarget = OptimizationTarget.MAXIMIZE_SHARPE,
                         historical_data: List[Dict] = None) -> OptimizationResult:
        """优化策略参数"""

        # 获取或创建策略配置
        key = f"{strategy_type.value}_{symbol}"
        if key not in self.strategy_configurations:
            self.initialize_strategy(strategy_type, symbol)

        strategy_config = self.strategy_configurations[key]

        # 记录原始参数
        original_parameters = {name: param.current_value for name, param in strategy_config.parameters.items()}

        # 获取历史数据（如果没有提供，使用模拟数据）
        if historical_data is None:
            historical_data = self._generate_mock_data(symbol)

        # 计算原始性能
        original_performance = self._evaluate_strategy_performance(
            strategy_config, historical_data, original_parameters
        )

        # 执行优化
        if optimization_method == OptimizationMethod.GENETIC_ALGORITHM:
            optimized_parameters = self._genetic_algorithm_optimization(
                strategy_config, historical_data, target
            )
        elif optimization_method == OptimizationMethod.GRID_SEARCH:
            optimized_parameters = self._grid_search_optimization(
                strategy_config, historical_data, target
            )
        elif optimization_method == OptimizationMethod.RANDOM_SEARCH:
            optimized_parameters = self._random_search_optimization(
                strategy_config, historical_data, target
            )
        else:
            # 默认使用遗传算法
            optimized_parameters = self._genetic_algorithm_optimization(
                strategy_config, historical_data, target
            )

        # 计算优化后性能
        optimized_performance = self._evaluate_strategy_performance(
            strategy_config, historical_data, optimized_parameters
        )

        # 计算改进百分比
        target_metric = self._get_target_metric_name(target)
        original_value = original_performance.get(target_metric, 0)
        optimized_value = optimized_performance.get(target_metric, 0)

        if original_value != 0:
            improvement_percentage = ((optimized_value - original_value) / abs(original_value)) * 100
        else:
            improvement_percentage = 0

        # 计算置信度
        confidence_score = self._calculate_optimization_confidence(
            original_performance, optimized_performance, len(historical_data)
        )

        # 更新策略配置
        if improvement_percentage > 5 and confidence_score > 0.7:  # 只有显著改进才更新
            for name, value in optimized_parameters.items():
                if name in strategy_config.parameters:
                    strategy_config.parameters[name].current_value = value

            strategy_config.performance_metrics = optimized_performance
            strategy_config.last_optimization = datetime.now()
            strategy_config.optimization_count += 1

        # 创建优化结果
        optimization_result = OptimizationResult(
            strategy_type=strategy_type,
            symbol=symbol,
            optimization_method=optimization_method,
            target=target,
            original_parameters=original_parameters,
            optimized_parameters=optimized_parameters,
            original_performance=original_performance,
            optimized_performance=optimized_performance,
            improvement_percentage=improvement_percentage,
            confidence_score=confidence_score,
            backtest_period=f"{len(historical_data)}个数据点",
            optimization_date=datetime.now()
        )

        # 记录优化历史
        self.optimization_history.append(optimization_result)

        # 保持最近100个优化记录
        if len(self.optimization_history) > 100:
            self.optimization_history = self.optimization_history[-100:]

        return optimization_result

    def get_optimal_strategy_for_symbol(self, symbol: str) -> Optional[StrategyConfiguration]:
        """获取指定品种的最优策略"""

        if symbol not in self.supported_symbols:
            return None

        # 查找该品种的所有策略配置
        symbol_strategies = []
        for key, config in self.strategy_configurations.items():
            if config.symbol == symbol and config.performance_metrics:
                symbol_strategies.append(config)

        if not symbol_strategies:
            return None

        # 根据夏普比率选择最优策略
        best_strategy = max(symbol_strategies,
                           key=lambda x: x.performance_metrics.get('sharpe_ratio', 0))

        return best_strategy

    def batch_optimize_all_symbols(self, strategy_type: StrategyType,
                                  optimization_method: OptimizationMethod = OptimizationMethod.GENETIC_ALGORITHM,
                                  target: OptimizationTarget = OptimizationTarget.MAXIMIZE_SHARPE) -> List[OptimizationResult]:
        """批量优化所有品种的策略"""

        results = []

        for symbol in self.supported_symbols:
            try:
                self.logger.info(f"开始优化{symbol}的{strategy_type.value}策略")

                # 生成该品种的历史数据
                historical_data = self._generate_mock_data(symbol)

                # 执行优化
                result = self.optimize_strategy(
                    strategy_type, symbol, optimization_method, target, historical_data
                )

                results.append(result)

                self.logger.info(f"{symbol}策略优化完成，改进{result.improvement_percentage:.2f}%")

            except Exception as e:
                self.logger.error(f"优化{symbol}策略失败: {e}")

        return results

    def _genetic_algorithm_optimization(self, strategy_config: StrategyConfiguration,
                                      historical_data: List[Dict], target: OptimizationTarget) -> Dict[str, float]:
        """遗传算法优化"""

        parameters = strategy_config.parameters
        param_names = list(parameters.keys())

        # 初始化种群
        population_size = self.optimization_config['population_size']
        population = []

        for _ in range(population_size):
            individual = {}
            for name, param in parameters.items():
                if param.parameter_type == 'int':
                    value = np.random.randint(param.min_value, param.max_value + 1)
                elif param.parameter_type == 'float':
                    value = np.random.uniform(param.min_value, param.max_value)
                else:  # bool
                    value = np.random.choice([0, 1])
                individual[name] = value
            population.append(individual)

        # 进化过程
        max_iterations = self.optimization_config['max_iterations']
        best_fitness = float('-inf')
        best_individual = None

        for generation in range(max_iterations):
            # 评估适应度
            fitness_scores = []
            for individual in population:
                performance = self._evaluate_strategy_performance(
                    strategy_config, historical_data, individual
                )
                fitness = self._calculate_fitness(performance, target)
                fitness_scores.append(fitness)

                if fitness > best_fitness:
                    best_fitness = fitness
                    best_individual = individual.copy()

            # 选择
            selected = self._tournament_selection(population, fitness_scores)

            # 交叉
            offspring = self._crossover(selected, strategy_config)

            # 变异
            offspring = self._mutation(offspring, strategy_config)

            # 替换
            population = offspring

            # 检查收敛
            if generation > 10:
                recent_fitness = fitness_scores[-10:]
                if max(recent_fitness) - min(recent_fitness) < self.optimization_config['convergence_threshold']:
                    break

        return best_individual

    def _grid_search_optimization(self, strategy_config: StrategyConfiguration,
                                historical_data: List[Dict], target: OptimizationTarget) -> Dict[str, float]:
        """网格搜索优化"""

        parameters = strategy_config.parameters

        # 为每个参数生成候选值
        param_grids = {}
        for name, param in parameters.items():
            if param.parameter_type == 'int':
                values = list(range(int(param.min_value), int(param.max_value) + 1, max(1, int(param.step_size))))
            elif param.parameter_type == 'float':
                values = np.arange(param.min_value, param.max_value + param.step_size, param.step_size).tolist()
            else:  # bool
                values = [0, 1]

            # 限制网格大小以避免计算爆炸
            if len(values) > 10:
                values = values[::len(values)//10]

            param_grids[name] = values

        # 网格搜索
        best_fitness = float('-inf')
        best_parameters = None

        # 简化的网格搜索（限制组合数量）
        import itertools

        # 计算总组合数
        total_combinations = 1
        for values in param_grids.values():
            total_combinations *= len(values)

        # 如果组合太多，进一步减少网格大小
        if total_combinations > 1000:
            # 进一步减少每个参数的候选值
            for name, values in param_grids.items():
                if len(values) > 5:
                    param_grids[name] = values[::len(values)//5][:5]

        param_combinations = list(itertools.product(*param_grids.values()))

        # 最终安全检查，如果还是太多就随机采样
        if len(param_combinations) > 500:
            indices = np.random.choice(len(param_combinations), 500, replace=False)
            param_combinations = [param_combinations[i] for i in indices]

        for combination in param_combinations:
            parameters_dict = dict(zip(param_grids.keys(), combination))

            performance = self._evaluate_strategy_performance(
                strategy_config, historical_data, parameters_dict
            )
            fitness = self._calculate_fitness(performance, target)

            if fitness > best_fitness:
                best_fitness = fitness
                best_parameters = parameters_dict

        return best_parameters

    def _random_search_optimization(self, strategy_config: StrategyConfiguration,
                                  historical_data: List[Dict], target: OptimizationTarget) -> Dict[str, float]:
        """随机搜索优化"""

        parameters = strategy_config.parameters
        max_iterations = self.optimization_config['max_iterations']

        best_fitness = float('-inf')
        best_parameters = None

        for _ in range(max_iterations):
            # 随机生成参数组合
            random_params = {}
            for name, param in parameters.items():
                if param.parameter_type == 'int':
                    value = np.random.randint(param.min_value, param.max_value + 1)
                elif param.parameter_type == 'float':
                    value = np.random.uniform(param.min_value, param.max_value)
                else:  # bool
                    value = np.random.choice([0, 1])
                random_params[name] = value

            # 评估性能
            performance = self._evaluate_strategy_performance(
                strategy_config, historical_data, random_params
            )
            fitness = self._calculate_fitness(performance, target)

            if fitness > best_fitness:
                best_fitness = fitness
                best_parameters = random_params

        return best_parameters

    def _evaluate_strategy_performance(self, strategy_config: StrategyConfiguration,
                                     historical_data: List[Dict], parameters: Dict[str, float]) -> Dict[str, float]:
        """评估策略性能"""

        # 简化的回测逻辑
        # 在实际应用中，这里应该实现完整的回测引擎

        # 模拟交易结果
        np.random.seed(42)  # 确保结果可重现

        num_trades = len(historical_data) // 10  # 假设每10个数据点产生一笔交易

        # 基于参数生成模拟收益
        returns = []
        for i in range(num_trades):
            # 简化的收益计算
            base_return = np.random.normal(0.001, 0.02)  # 基础收益

            # 根据策略类型调整收益
            if strategy_config.strategy_type == StrategyType.TREND_FOLLOWING:
                trend_factor = parameters.get('ma_fast_period', 10) / parameters.get('ma_slow_period', 30)
                base_return *= (1 + trend_factor * 0.1)
            elif strategy_config.strategy_type == StrategyType.MEAN_REVERSION:
                reversion_factor = parameters.get('bollinger_std', 2.0) / 2.0
                base_return *= (1 + reversion_factor * 0.05)

            # 应用止损止盈
            stop_loss = parameters.get('stop_loss_pips', 50) * 0.0001
            take_profit = parameters.get('take_profit_pips', 100) * 0.0001

            if base_return < -stop_loss:
                base_return = -stop_loss
            elif base_return > take_profit:
                base_return = take_profit

            returns.append(base_return)

        if not returns:
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'volatility': 0.0,
                'num_trades': 0
            }

        # 计算性能指标
        total_return = sum(returns)
        avg_return = np.mean(returns)
        volatility = np.std(returns)
        sharpe_ratio = avg_return / volatility if volatility > 0 else 0

        # 计算最大回撤
        cumulative_returns = np.cumsum(returns)
        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = peak - cumulative_returns
        max_drawdown = np.max(drawdown)

        # 计算胜率
        winning_trades = len([r for r in returns if r > 0])
        win_rate = winning_trades / len(returns)

        # 计算盈利因子
        gross_profit = sum([r for r in returns if r > 0])
        gross_loss = abs(sum([r for r in returns if r < 0]))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'volatility': volatility,
            'num_trades': len(returns)
        }

    def _calculate_fitness(self, performance: Dict[str, float], target: OptimizationTarget) -> float:
        """计算适应度函数"""

        if target == OptimizationTarget.MAXIMIZE_RETURN:
            return performance.get('total_return', 0)
        elif target == OptimizationTarget.MAXIMIZE_SHARPE:
            return performance.get('sharpe_ratio', 0)
        elif target == OptimizationTarget.MINIMIZE_DRAWDOWN:
            return -performance.get('max_drawdown', 0)
        elif target == OptimizationTarget.MAXIMIZE_WIN_RATE:
            return performance.get('win_rate', 0)
        elif target == OptimizationTarget.MAXIMIZE_PROFIT_FACTOR:
            pf = performance.get('profit_factor', 0)
            return min(pf, 10)  # 限制最大值避免无穷大
        elif target == OptimizationTarget.MINIMIZE_VOLATILITY:
            return -performance.get('volatility', 0)
        else:
            return performance.get('sharpe_ratio', 0)

    def _get_target_metric_name(self, target: OptimizationTarget) -> str:
        """获取目标指标名称"""

        mapping = {
            OptimizationTarget.MAXIMIZE_RETURN: 'total_return',
            OptimizationTarget.MAXIMIZE_SHARPE: 'sharpe_ratio',
            OptimizationTarget.MINIMIZE_DRAWDOWN: 'max_drawdown',
            OptimizationTarget.MAXIMIZE_WIN_RATE: 'win_rate',
            OptimizationTarget.MAXIMIZE_PROFIT_FACTOR: 'profit_factor',
            OptimizationTarget.MINIMIZE_VOLATILITY: 'volatility'
        }

        return mapping.get(target, 'sharpe_ratio')

    def _calculate_optimization_confidence(self, original_performance: Dict[str, float],
                                         optimized_performance: Dict[str, float],
                                         data_points: int) -> float:
        """计算优化置信度"""

        # 基于数据量的置信度
        data_confidence = min(data_points / 1000, 1.0)

        # 基于改进幅度的置信度
        improvements = []
        for metric in ['sharpe_ratio', 'win_rate', 'profit_factor']:
            original = original_performance.get(metric, 0)
            optimized = optimized_performance.get(metric, 0)

            if original != 0:
                improvement = (optimized - original) / abs(original)
                improvements.append(improvement)

        avg_improvement = np.mean(improvements) if improvements else 0
        improvement_confidence = min(max(avg_improvement, 0), 1.0)

        # 综合置信度
        overall_confidence = (data_confidence * 0.3 + improvement_confidence * 0.7)

        return overall_confidence

    def _tournament_selection(self, population: List[Dict], fitness_scores: List[float]) -> List[Dict]:
        """锦标赛选择"""

        selected = []
        tournament_size = 3

        for _ in range(len(population)):
            # 随机选择参赛者
            tournament_indices = np.random.choice(len(population), tournament_size, replace=False)
            tournament_fitness = [fitness_scores[i] for i in tournament_indices]

            # 选择最优个体
            winner_index = tournament_indices[np.argmax(tournament_fitness)]
            selected.append(population[winner_index].copy())

        return selected

    def _crossover(self, population: List[Dict], strategy_config: StrategyConfiguration) -> List[Dict]:
        """交叉操作"""

        offspring = []
        crossover_rate = self.optimization_config['crossover_rate']

        for i in range(0, len(population), 2):
            parent1 = population[i]
            parent2 = population[i + 1] if i + 1 < len(population) else population[0]

            if np.random.random() < crossover_rate:
                # 执行交叉
                child1, child2 = {}, {}

                for param_name in parent1.keys():
                    if np.random.random() < 0.5:
                        child1[param_name] = parent1[param_name]
                        child2[param_name] = parent2[param_name]
                    else:
                        child1[param_name] = parent2[param_name]
                        child2[param_name] = parent1[param_name]

                offspring.extend([child1, child2])
            else:
                # 不交叉，直接复制
                offspring.extend([parent1.copy(), parent2.copy()])

        return offspring[:len(population)]

    def _mutation(self, population: List[Dict], strategy_config: StrategyConfiguration) -> List[Dict]:
        """变异操作"""

        mutation_rate = self.optimization_config['mutation_rate']
        parameters = strategy_config.parameters

        for individual in population:
            for param_name, param in parameters.items():
                if np.random.random() < mutation_rate:
                    # 执行变异
                    if param.parameter_type == 'int':
                        individual[param_name] = np.random.randint(param.min_value, param.max_value + 1)
                    elif param.parameter_type == 'float':
                        individual[param_name] = np.random.uniform(param.min_value, param.max_value)
                    else:  # bool
                        individual[param_name] = np.random.choice([0, 1])

        return population

    def _generate_mock_data(self, symbol: str, num_points: int = 1000) -> List[Dict]:
        """生成模拟历史数据"""

        np.random.seed(hash(symbol) % 2**32)  # 基于品种名称的种子

        # 不同品种的基础价格
        base_prices = {
            'EURUSD': 1.1000,
            'GBPUSD': 1.2500,
            'AUDUSD': 0.6500,
            'NZDUSD': 0.6000,
            'USDCHF': 0.9200,
            'USDCAD': 1.3500,
            'USDJPY': 150.0,
            'GOLD': 2000.0
        }

        base_price = base_prices.get(symbol, 1.0)

        # 生成价格序列
        data = []
        current_price = base_price

        for i in range(num_points):
            # 模拟价格变动
            change = np.random.normal(0, 0.001)  # 0.1%的标准波动
            current_price *= (1 + change)

            # 生成OHLC数据
            high = current_price * (1 + abs(np.random.normal(0, 0.0005)))
            low = current_price * (1 - abs(np.random.normal(0, 0.0005)))
            open_price = current_price * (1 + np.random.normal(0, 0.0002))
            close_price = current_price

            volume = max(1, int(np.random.normal(1000, 200)))

            data.append({
                'timestamp': datetime.now() - timedelta(minutes=num_points - i),
                'open': open_price,
                'high': high,
                'low': low,
                'close': close_price,
                'volume': volume
            })

        return data

    def get_optimization_summary(self) -> Dict:
        """获取优化总结"""

        if not self.optimization_history:
            return {
                'total_optimizations': 0,
                'avg_improvement': 0.0,
                'best_optimization': None,
                'strategy_performance': {}
            }

        # 统计优化结果
        total_optimizations = len(self.optimization_history)
        improvements = [opt.improvement_percentage for opt in self.optimization_history]
        avg_improvement = np.mean(improvements)

        # 找到最佳优化
        best_optimization = max(self.optimization_history, key=lambda x: x.improvement_percentage)

        # 按策略类型统计表现
        strategy_performance = {}
        for opt in self.optimization_history:
            strategy_name = opt.strategy_type.value
            if strategy_name not in strategy_performance:
                strategy_performance[strategy_name] = {
                    'count': 0,
                    'avg_improvement': 0.0,
                    'best_improvement': 0.0,
                    'symbols': set()
                }

            perf = strategy_performance[strategy_name]
            perf['count'] += 1
            perf['avg_improvement'] = (perf['avg_improvement'] * (perf['count'] - 1) + opt.improvement_percentage) / perf['count']
            perf['best_improvement'] = max(perf['best_improvement'], opt.improvement_percentage)
            perf['symbols'].add(opt.symbol)

        # 转换set为list以便JSON序列化
        for perf in strategy_performance.values():
            perf['symbols'] = list(perf['symbols'])

        return {
            'total_optimizations': total_optimizations,
            'avg_improvement': avg_improvement,
            'best_optimization': {
                'strategy': best_optimization.strategy_type.value,
                'symbol': best_optimization.symbol,
                'improvement': best_optimization.improvement_percentage,
                'confidence': best_optimization.confidence_score,
                'date': best_optimization.optimization_date.isoformat()
            },
            'strategy_performance': strategy_performance,
            'recent_optimizations': [
                {
                    'strategy': opt.strategy_type.value,
                    'symbol': opt.symbol,
                    'improvement': opt.improvement_percentage,
                    'confidence': opt.confidence_score,
                    'date': opt.optimization_date.isoformat()
                }
                for opt in self.optimization_history[-10:]  # 最近10次优化
            ]
        }

    def export_optimization_results(self, filepath: str = None) -> Dict:
        """导出优化结果"""

        if filepath is None:
            filepath = f"optimization_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        export_data = {
            'export_timestamp': datetime.now().isoformat(),
            'optimization_summary': self.get_optimization_summary(),
            'strategy_configurations': {
                key: {
                    'strategy_type': config.strategy_type.value,
                    'symbol': config.symbol,
                    'parameters': {
                        name: {
                            'current_value': param.current_value,
                            'min_value': param.min_value,
                            'max_value': param.max_value,
                            'description': param.description
                        }
                        for name, param in config.parameters.items()
                    },
                    'performance_metrics': config.performance_metrics,
                    'last_optimization': config.last_optimization.isoformat() if config.last_optimization else None,
                    'optimization_count': config.optimization_count
                }
                for key, config in self.strategy_configurations.items()
            },
            'optimization_history': [
                {
                    'strategy_type': opt.strategy_type.value,
                    'symbol': opt.symbol,
                    'optimization_method': opt.optimization_method.value,
                    'target': opt.target.value,
                    'original_parameters': opt.original_parameters,
                    'optimized_parameters': opt.optimized_parameters,
                    'original_performance': opt.original_performance,
                    'optimized_performance': opt.optimized_performance,
                    'improvement_percentage': opt.improvement_percentage,
                    'confidence_score': opt.confidence_score,
                    'optimization_date': opt.optimization_date.isoformat()
                }
                for opt in self.optimization_history
            ]
        }

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            return {'status': 'success', 'filepath': filepath, 'records_exported': len(export_data)}

        except Exception as e:
            return {'status': 'error', 'error': str(e)}

    def get_strategy_recommendations(self, symbol: str) -> List[Dict]:
        """获取策略建议"""

        if symbol not in self.supported_symbols:
            return []

        recommendations = []

        # 检查该品种的现有策略
        symbol_configs = [config for config in self.strategy_configurations.values()
                         if config.symbol == symbol]

        if not symbol_configs:
            # 如果没有配置，建议初始化所有策略类型
            for strategy_type in [StrategyType.TREND_FOLLOWING, StrategyType.MEAN_REVERSION,
                                StrategyType.BREAKOUT, StrategyType.SCALPING]:
                recommendations.append({
                    'action': 'initialize',
                    'strategy_type': strategy_type.value,
                    'symbol': symbol,
                    'reason': f'建议为{symbol}初始化{strategy_type.value}策略',
                    'priority': 'high'
                })
        else:
            # 检查是否需要优化
            for config in symbol_configs:
                days_since_optimization = 999
                if config.last_optimization:
                    days_since_optimization = (datetime.now() - config.last_optimization).days

                if days_since_optimization > 30:
                    recommendations.append({
                        'action': 'optimize',
                        'strategy_type': config.strategy_type.value,
                        'symbol': symbol,
                        'reason': f'{config.strategy_type.value}策略已{days_since_optimization}天未优化',
                        'priority': 'medium'
                    })

                # 检查表现
                if config.performance_metrics:
                    sharpe_ratio = config.performance_metrics.get('sharpe_ratio', 0)
                    if sharpe_ratio < 0.5:
                        recommendations.append({
                            'action': 'reoptimize',
                            'strategy_type': config.strategy_type.value,
                            'symbol': symbol,
                            'reason': f'{config.strategy_type.value}策略夏普比率过低({sharpe_ratio:.2f})',
                            'priority': 'high'
                        })

        return recommendations
