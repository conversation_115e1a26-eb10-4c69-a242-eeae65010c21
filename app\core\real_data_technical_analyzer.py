#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实数据的技术指标分析器
使用pizza_quotes数据库中的真实市场数据进行技术分析
"""

import os
import sys
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from app.utils.db_client import get_connection
    from app.core.data_source_adapter import DataSourceAdapter
except ImportError as e:
    print(f"导入模块失败: {e}")
    get_connection = None
    DataSourceAdapter = None

class RealDataTechnicalAnalyzer:
    """基于真实数据的技术指标分析器"""

    def __init__(self):
        self.data_adapter = DataSourceAdapter() if DataSourceAdapter else None
        self.logger = logging.getLogger(__name__)

        # 支持的货币对（基于数据库分析结果）
        self.supported_symbols = [
            'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD',
            'USDCHF', 'USDCAD', 'USDJPY', 'GOLD'
        ]

    def get_comprehensive_analysis(self, symbol: str, period_minutes: int = 15,
                                 count: int = 200) -> Dict:
        """获取综合技术分析"""

        if symbol not in self.supported_symbols:
            raise ValueError(f"不支持的交易品种: {symbol}")

        try:
            # 获取历史数据
            historical_data = self.data_adapter.get_historical_data(symbol, period_minutes, count)

            if not historical_data or len(historical_data) < 50:
                raise ValueError(f"数据不足，需要至少50条数据，实际获取{len(historical_data)}条")

            # 提取价格数据
            prices = [float(d['close']) for d in historical_data]
            highs = [float(d['high']) for d in historical_data]
            lows = [float(d['low']) for d in historical_data]
            volumes = [int(d['volume']) for d in historical_data]

            # 计算各种技术指标
            analysis = {
                'symbol': symbol,
                'period_minutes': period_minutes,
                'data_points': len(historical_data),
                'latest_time': historical_data[-1]['timestamp'].isoformat(),
                'current_price': prices[-1],

                # 移动平均线
                'moving_averages': self._calculate_moving_averages(prices),

                # 趋势指标
                'trend_indicators': self._calculate_trend_indicators(prices, highs, lows),

                # 动量指标
                'momentum_indicators': self._calculate_momentum_indicators(prices, highs, lows),

                # 波动率指标
                'volatility_indicators': self._calculate_volatility_indicators(prices, highs, lows),

                # 成交量指标
                'volume_indicators': self._calculate_volume_indicators(prices, volumes),

                # 支撑阻力位
                'support_resistance': self._calculate_support_resistance(prices, highs, lows),

                # 市场状态评估
                'market_state': self._assess_market_state(prices, highs, lows, volumes)
            }

            return analysis

        except Exception as e:
            self.logger.error(f"获取{symbol}技术分析失败: {e}")
            raise

    def _calculate_moving_averages(self, prices: List[float]) -> Dict:
        """计算移动平均线"""

        result = {}

        # 常用的移动平均周期
        periods = [5, 10, 13, 20, 50, 100, 200]

        for period in periods:
            if len(prices) >= period:
                ma = np.mean(prices[-period:])
                result[f'ma_{period}'] = round(ma, 5)

        # EMA指数移动平均
        if len(prices) >= 12:
            result['ema_12'] = round(self._calculate_ema(prices, 12), 5)
        if len(prices) >= 26:
            result['ema_26'] = round(self._calculate_ema(prices, 26), 5)

        # 移动平均趋势
        if 'ma_20' in result and 'ma_50' in result:
            if result['ma_20'] > result['ma_50']:
                result['ma_trend'] = 'bullish'
            elif result['ma_20'] < result['ma_50']:
                result['ma_trend'] = 'bearish'
            else:
                result['ma_trend'] = 'neutral'

        return result

    def _calculate_trend_indicators(self, prices: List[float], highs: List[float],
                                  lows: List[float]) -> Dict:
        """计算趋势指标"""

        result = {}

        # MACD
        if len(prices) >= 26:
            ema_12 = self._calculate_ema(prices, 12)
            ema_26 = self._calculate_ema(prices, 26)
            macd_line = ema_12 - ema_26

            # 计算MACD信号线（9日EMA）
            macd_values = []
            for i in range(26, len(prices)):
                ema_12_i = self._calculate_ema(prices[:i+1], 12)
                ema_26_i = self._calculate_ema(prices[:i+1], 26)
                macd_values.append(ema_12_i - ema_26_i)

            if len(macd_values) >= 9:
                signal_line = self._calculate_ema(macd_values, 9)
                histogram = macd_line - signal_line

                result['macd'] = {
                    'macd_line': round(macd_line, 6),
                    'signal_line': round(signal_line, 6),
                    'histogram': round(histogram, 6),
                    'signal': 'bullish' if macd_line > signal_line else 'bearish'
                }

        # ADX (简化版本)
        if len(prices) >= 14:
            adx = self._calculate_adx(highs, lows, prices, 14)
            result['adx'] = {
                'value': round(adx, 2),
                'strength': 'strong' if adx > 25 else 'weak' if adx < 20 else 'moderate'
            }

        return result

    def _calculate_momentum_indicators(self, prices: List[float], highs: List[float],
                                     lows: List[float]) -> Dict:
        """计算动量指标"""

        result = {}

        # RSI
        if len(prices) >= 14:
            rsi = self._calculate_rsi(prices, 14)
            result['rsi'] = {
                'value': round(rsi, 2),
                'signal': 'overbought' if rsi > 70 else 'oversold' if rsi < 30 else 'neutral'
            }

        # 随机指标 (Stochastic)
        if len(highs) >= 14 and len(lows) >= 14:
            k_percent, d_percent = self._calculate_stochastic(highs, lows, prices, 14, 3)
            result['stochastic'] = {
                'k_percent': round(k_percent, 2),
                'd_percent': round(d_percent, 2),
                'signal': 'overbought' if k_percent > 80 else 'oversold' if k_percent < 20 else 'neutral'
            }

        # 威廉指标 (Williams %R)
        if len(highs) >= 14 and len(lows) >= 14:
            williams_r = self._calculate_williams_r(highs, lows, prices, 14)
            result['williams_r'] = {
                'value': round(williams_r, 2),
                'signal': 'overbought' if williams_r > -20 else 'oversold' if williams_r < -80 else 'neutral'
            }

        return result

    def _calculate_volatility_indicators(self, prices: List[float], highs: List[float],
                                       lows: List[float]) -> Dict:
        """计算波动率指标"""

        result = {}

        # 布林带
        if len(prices) >= 20:
            bb_middle = np.mean(prices[-20:])
            bb_std = np.std(prices[-20:])
            bb_upper = bb_middle + (2 * bb_std)
            bb_lower = bb_middle - (2 * bb_std)

            current_price = prices[-1]
            bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)

            result['bollinger_bands'] = {
                'upper': round(bb_upper, 5),
                'middle': round(bb_middle, 5),
                'lower': round(bb_lower, 5),
                'position': round(bb_position, 3),
                'signal': 'overbought' if bb_position > 0.8 else 'oversold' if bb_position < 0.2 else 'neutral'
            }

        # ATR (平均真实波幅)
        if len(highs) >= 14 and len(lows) >= 14:
            atr = self._calculate_atr(highs, lows, prices, 14)
            result['atr'] = {
                'value': round(atr, 5),
                'volatility': 'high' if atr > np.mean(prices[-50:]) * 0.01 else 'low'
            }

        return result

    def _calculate_volume_indicators(self, prices: List[float], volumes: List[int]) -> Dict:
        """计算成交量指标"""

        result = {}

        if len(volumes) >= 20:
            # 成交量移动平均
            volume_ma = np.mean(volumes[-20:])
            current_volume = volumes[-1]

            result['volume_analysis'] = {
                'current_volume': current_volume,
                'average_volume': round(volume_ma, 0),
                'volume_ratio': round(current_volume / volume_ma, 2),
                'signal': 'high' if current_volume > volume_ma * 1.5 else 'low' if current_volume < volume_ma * 0.5 else 'normal'
            }

        # 价量关系
        if len(prices) >= 2 and len(volumes) >= 2:
            price_change = prices[-1] - prices[-2]
            volume_change = volumes[-1] - volumes[-2]

            if price_change > 0 and volume_change > 0:
                pv_signal = 'bullish_confirmation'
            elif price_change < 0 and volume_change > 0:
                pv_signal = 'bearish_confirmation'
            elif price_change > 0 and volume_change < 0:
                pv_signal = 'bullish_divergence'
            elif price_change < 0 and volume_change < 0:
                pv_signal = 'bearish_divergence'
            else:
                pv_signal = 'neutral'

            result['price_volume_relationship'] = pv_signal

        return result

    def _calculate_support_resistance(self, prices: List[float], highs: List[float],
                                    lows: List[float]) -> Dict:
        """计算支撑阻力位"""

        result = {}

        if len(prices) >= 50:
            # 简化的支撑阻力位计算
            recent_highs = highs[-50:]
            recent_lows = lows[-50:]

            # 阻力位（近期高点）
            resistance_levels = []
            for i in range(2, len(recent_highs) - 2):
                if (recent_highs[i] > recent_highs[i-1] and recent_highs[i] > recent_highs[i-2] and
                    recent_highs[i] > recent_highs[i+1] and recent_highs[i] > recent_highs[i+2]):
                    resistance_levels.append(recent_highs[i])

            # 支撑位（近期低点）
            support_levels = []
            for i in range(2, len(recent_lows) - 2):
                if (recent_lows[i] < recent_lows[i-1] and recent_lows[i] < recent_lows[i-2] and
                    recent_lows[i] < recent_lows[i+1] and recent_lows[i] < recent_lows[i+2]):
                    support_levels.append(recent_lows[i])

            # 取最近的几个关键位
            if resistance_levels:
                resistance_levels.sort(reverse=True)
                result['resistance_levels'] = [round(r, 5) for r in resistance_levels[:3]]

            if support_levels:
                support_levels.sort(reverse=True)
                result['support_levels'] = [round(s, 5) for s in support_levels[:3]]

        return result

    def _calculate_ema(self, prices: List[float], period: int) -> float:
        """计算指数移动平均"""
        if len(prices) < period:
            return np.mean(prices)

        multiplier = 2 / (period + 1)
        ema = prices[0]

        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))

        return ema

    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI指标"""
        if len(prices) < period + 1:
            return 50.0

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return rsi

    def _calculate_stochastic(self, highs: List[float], lows: List[float],
                            closes: List[float], k_period: int = 14,
                            d_period: int = 3) -> Tuple[float, float]:
        """计算随机指标"""
        if len(highs) < k_period:
            return 50.0, 50.0

        # 计算%K
        highest_high = max(highs[-k_period:])
        lowest_low = min(lows[-k_period:])
        current_close = closes[-1]

        if highest_high == lowest_low:
            k_percent = 50.0
        else:
            k_percent = ((current_close - lowest_low) / (highest_high - lowest_low)) * 100

        # 计算%D (简化版本，使用最近几个%K的平均值)
        k_values = []
        for i in range(max(0, len(closes) - d_period), len(closes)):
            if i >= k_period - 1:
                h_high = max(highs[i-k_period+1:i+1])
                l_low = min(lows[i-k_period+1:i+1])
                if h_high != l_low:
                    k_val = ((closes[i] - l_low) / (h_high - l_low)) * 100
                else:
                    k_val = 50.0
                k_values.append(k_val)

        d_percent = np.mean(k_values) if k_values else k_percent

        return k_percent, d_percent

    def _calculate_williams_r(self, highs: List[float], lows: List[float],
                            closes: List[float], period: int = 14) -> float:
        """计算威廉指标"""
        if len(highs) < period:
            return -50.0

        highest_high = max(highs[-period:])
        lowest_low = min(lows[-period:])
        current_close = closes[-1]

        if highest_high == lowest_low:
            return -50.0

        williams_r = ((highest_high - current_close) / (highest_high - lowest_low)) * -100

        return williams_r

    def _calculate_atr(self, highs: List[float], lows: List[float],
                      closes: List[float], period: int = 14) -> float:
        """计算平均真实波幅"""
        if len(highs) < period + 1:
            return 0.0

        true_ranges = []
        for i in range(1, len(highs)):
            high_low = highs[i] - lows[i]
            high_close = abs(highs[i] - closes[i-1])
            low_close = abs(lows[i] - closes[i-1])

            true_range = max(high_low, high_close, low_close)
            true_ranges.append(true_range)

        if len(true_ranges) >= period:
            atr = np.mean(true_ranges[-period:])
        else:
            atr = np.mean(true_ranges)

        return atr

    def _calculate_adx(self, highs: List[float], lows: List[float],
                      closes: List[float], period: int = 14) -> float:
        """计算ADX指标（简化版本）"""
        if len(highs) < period + 1:
            return 0.0

        # 计算方向移动
        plus_dm = []
        minus_dm = []

        for i in range(1, len(highs)):
            high_diff = highs[i] - highs[i-1]
            low_diff = lows[i-1] - lows[i]

            if high_diff > low_diff and high_diff > 0:
                plus_dm.append(high_diff)
            else:
                plus_dm.append(0)

            if low_diff > high_diff and low_diff > 0:
                minus_dm.append(low_diff)
            else:
                minus_dm.append(0)

        # 计算ATR
        atr = self._calculate_atr(highs, lows, closes, period)

        if atr == 0:
            return 0.0

        # 计算DI
        plus_di = (np.mean(plus_dm[-period:]) / atr) * 100
        minus_di = (np.mean(minus_dm[-period:]) / atr) * 100

        # 计算DX
        if plus_di + minus_di == 0:
            return 0.0

        dx = (abs(plus_di - minus_di) / (plus_di + minus_di)) * 100

        return dx

    def get_trading_signals(self, symbol: str) -> Dict:
        """获取交易信号"""

        try:
            # 获取多时间框架分析
            analysis_15m = self.get_comprehensive_analysis(symbol, 15, 100)
            analysis_1h = self.get_comprehensive_analysis(symbol, 60, 100)

            signals = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'timeframes': {
                    '15m': self._extract_signals(analysis_15m),
                    '1h': self._extract_signals(analysis_1h)
                }
            }

            # 综合信号
            signals['combined_signal'] = self._combine_signals(
                signals['timeframes']['15m'],
                signals['timeframes']['1h']
            )

            return signals

        except Exception as e:
            self.logger.error(f"获取{symbol}交易信号失败: {e}")
            return {'error': str(e)}

    def _extract_signals(self, analysis: Dict) -> Dict:
        """从技术分析中提取交易信号"""

        signals = {
            'trend': 'neutral',
            'momentum': 'neutral',
            'volatility': 'normal',
            'volume': 'normal',
            'overall': 'neutral',
            'strength': 0.0
        }

        # 趋势信号
        if 'moving_averages' in analysis and 'ma_trend' in analysis['moving_averages']:
            signals['trend'] = analysis['moving_averages']['ma_trend']

        # 动量信号
        momentum_signals = []
        if 'momentum_indicators' in analysis:
            if 'rsi' in analysis['momentum_indicators']:
                rsi_signal = analysis['momentum_indicators']['rsi']['signal']
                if rsi_signal == 'overbought':
                    momentum_signals.append('bearish')
                elif rsi_signal == 'oversold':
                    momentum_signals.append('bullish')

            if 'stochastic' in analysis['momentum_indicators']:
                stoch_signal = analysis['momentum_indicators']['stochastic']['signal']
                if stoch_signal == 'overbought':
                    momentum_signals.append('bearish')
                elif stoch_signal == 'oversold':
                    momentum_signals.append('bullish')

        if momentum_signals:
            if momentum_signals.count('bullish') > momentum_signals.count('bearish'):
                signals['momentum'] = 'bullish'
            elif momentum_signals.count('bearish') > momentum_signals.count('bullish'):
                signals['momentum'] = 'bearish'

        # 波动率信号
        if 'volatility_indicators' in analysis and 'atr' in analysis['volatility_indicators']:
            signals['volatility'] = analysis['volatility_indicators']['atr']['volatility']

        # 成交量信号
        if 'volume_indicators' in analysis and 'volume_analysis' in analysis['volume_indicators']:
            signals['volume'] = analysis['volume_indicators']['volume_analysis']['signal']

        # 综合信号
        bullish_count = 0
        bearish_count = 0

        if signals['trend'] == 'bullish':
            bullish_count += 2
        elif signals['trend'] == 'bearish':
            bearish_count += 2

        if signals['momentum'] == 'bullish':
            bullish_count += 1
        elif signals['momentum'] == 'bearish':
            bearish_count += 1

        if bullish_count > bearish_count:
            signals['overall'] = 'bullish'
            signals['strength'] = bullish_count / (bullish_count + bearish_count)
        elif bearish_count > bullish_count:
            signals['overall'] = 'bearish'
            signals['strength'] = bearish_count / (bullish_count + bearish_count)
        else:
            signals['overall'] = 'neutral'
            signals['strength'] = 0.5

        return signals

    def _combine_signals(self, signals_15m: Dict, signals_1h: Dict) -> Dict:
        """合并多时间框架信号"""

        combined = {
            'direction': 'neutral',
            'strength': 0.0,
            'confidence': 0.0,
            'recommendation': 'hold'
        }

        # 时间框架权重
        weight_15m = 0.3
        weight_1h = 0.7

        # 计算综合强度
        strength_15m = signals_15m.get('strength', 0.5)
        strength_1h = signals_1h.get('strength', 0.5)

        if signals_15m['overall'] == signals_1h['overall']:
            # 两个时间框架一致
            combined['direction'] = signals_15m['overall']
            combined['strength'] = (strength_15m * weight_15m + strength_1h * weight_1h)
            combined['confidence'] = 0.8

            if combined['direction'] == 'bullish' and combined['strength'] > 0.6:
                combined['recommendation'] = 'buy'
            elif combined['direction'] == 'bearish' and combined['strength'] > 0.6:
                combined['recommendation'] = 'sell'
            else:
                combined['recommendation'] = 'hold'
        else:
            # 时间框架不一致，以长时间框架为主
            combined['direction'] = signals_1h['overall']
            combined['strength'] = strength_1h * 0.7
            combined['confidence'] = 0.4
            combined['recommendation'] = 'hold'

        return combined

# 创建全局实例
real_data_analyzer = RealDataTechnicalAnalyzer()
