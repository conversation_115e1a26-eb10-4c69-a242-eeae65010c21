#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级交易系统集成方案
将新的高级交易系统集成到现有的外汇交易系统中
"""

import json
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict

@dataclass
class IntegrationConfig:
    """集成配置"""
    enable_advanced_system: bool = True
    fallback_to_ma13: bool = True
    confidence_threshold: float = 0.4
    strategy_weights: Dict[str, float] = None
    risk_management_mode: str = "DYNAMIC"  # DYNAMIC, CONSERVATIVE, AGGRESSIVE

    def __post_init__(self):
        if self.strategy_weights is None:
            self.strategy_weights = {
                'trend_following': 0.35,
                'mean_reversion': 0.25,
                'momentum': 0.20,
                'breakout': 0.20
            }

class SystemIntegrator:
    """系统集成器"""

    def __init__(self, config: IntegrationConfig):
        self.config = config
        self.advanced_system = None
        self.ma13_system = None
        self.performance_tracker = PerformanceTracker()

    def initialize_systems(self):
        """初始化交易系统"""
        # 初始化高级交易系统
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from advanced_trading_system_simple import AdvancedTradingSystem
        self.advanced_system = AdvancedTradingSystem()

        # 保留原有MA13系统作为备用
        self.ma13_system = MA13SystemWrapper()

        print("✅ 交易系统初始化完成")
        print(f"   - 高级系统: {'启用' if self.config.enable_advanced_system else '禁用'}")
        print(f"   - MA13备用: {'启用' if self.config.fallback_to_ma13 else '禁用'}")
        print(f"   - 置信度阈值: {self.config.confidence_threshold}")

    def analyze_and_decide(self, market_data: Dict) -> Dict:
        """分析市场并做出交易决策"""

        # 1. 数据预处理
        processed_data = self._preprocess_market_data(market_data)

        # 2. 高级系统分析
        advanced_decision = None
        if self.config.enable_advanced_system:
            try:
                advanced_decision = self._get_advanced_decision(processed_data)
            except Exception as e:
                print(f"⚠️ 高级系统分析失败: {e}")
                advanced_decision = None

        # 3. 决策选择逻辑
        final_decision = self._select_final_decision(
            advanced_decision, processed_data
        )

        # 4. 记录性能
        self.performance_tracker.record_decision(final_decision)

        return final_decision

    def _preprocess_market_data(self, market_data: Dict) -> Dict:
        """预处理市场数据，转换为高级系统需要的格式"""
        import pandas as pd
        import numpy as np

        # 从现有数据结构提取OHLC数据
        timeframe_15m = market_data.get('timeframe15m', [])
        timeframe_1h = market_data.get('timeframe1h', [])

        if not timeframe_15m:
            # 如果没有15分钟数据，创建模拟数据
            current_price = market_data.get('current_price', 1.1000)
            timeframe_15m = self._create_mock_data(current_price, 100)

        # 转换为DataFrame格式
        df_data = []
        for i, candle in enumerate(timeframe_15m[-100:]):  # 取最近100根K线
            df_data.append({
                'datetime': datetime.now(),
                'open': candle.get('open', candle.get('close', market_data.get('current_price', 1.1000))),
                'high': candle.get('high', candle.get('close', market_data.get('current_price', 1.1000))),
                'low': candle.get('low', candle.get('close', market_data.get('current_price', 1.1000))),
                'close': candle.get('close', market_data.get('current_price', 1.1000)),
                'volume': candle.get('volume', 1000)
            })

        df = pd.DataFrame(df_data)

        return {
            'df': df,
            'original_data': market_data
        }

    def _create_mock_data(self, current_price: float, periods: int) -> List[Dict]:
        """创建模拟数据（用于测试）"""
        import numpy as np

        np.random.seed(42)
        data = []
        price = current_price

        for i in range(periods):
            change = np.random.normal(0, 0.0005)
            new_price = price * (1 + change)

            high = new_price * (1 + abs(np.random.normal(0, 0.0002)))
            low = new_price * (1 - abs(np.random.normal(0, 0.0002)))

            data.append({
                'open': price,
                'high': high,
                'low': low,
                'close': new_price,
                'volume': np.random.randint(1000, 5000)
            })

            price = new_price

        return data

    def _get_advanced_decision(self, processed_data: Dict) -> Dict:
        """获取高级系统的决策"""
        df = processed_data['df']

        # 市场分析
        market_analysis = self.advanced_system.analyze_market(df)

        # 生成交易决策
        decision = self.advanced_system.generate_trading_decision(market_analysis)

        # 转换为标准格式
        return {
            'system': 'ADVANCED',
            'action': decision.action,
            'confidence': decision.confidence,
            'entry_price': decision.entry_price,
            'stop_loss': decision.stop_loss,
            'take_profit': decision.take_profit,
            'position_size': decision.position_size,
            'strategy_used': decision.strategy_used,
            'reasoning': decision.reasoning,
            'risk_level': decision.risk_level,
            'market_phase': decision.market_phase.value,
            'raw_decision': decision
        }

    def _get_ma13_decision(self, processed_data: Dict) -> Dict:
        """获取MA13系统的决策（备用）"""
        original_data = processed_data['original_data']

        # 使用原有的MA13逻辑
        ma13_decision = self.ma13_system.analyze(original_data)

        return {
            'system': 'MA13_BACKUP',
            'action': ma13_decision.get('action', 'NONE'),
            'confidence': 0.5,  # MA13系统的固定置信度
            'entry_price': ma13_decision.get('entry_price'),
            'stop_loss': ma13_decision.get('stop_loss'),
            'take_profit': ma13_decision.get('take_profit'),
            'position_size': ma13_decision.get('position_size', 0.1),
            'strategy_used': '13日均线右侧交易',
            'reasoning': ma13_decision.get('reasoning', '13日均线策略'),
            'risk_level': 'MEDIUM',
            'market_phase': '趋势跟随',
            'raw_decision': ma13_decision
        }

    def _select_final_decision(self, advanced_decision: Optional[Dict],
                             processed_data: Dict) -> Dict:
        """选择最终决策"""

        # 1. 如果高级系统有高置信度决策，使用高级系统
        if (advanced_decision and
            advanced_decision['confidence'] >= self.config.confidence_threshold):

            print(f"✅ 使用高级系统决策 (置信度: {advanced_decision['confidence']:.2f})")
            return self._format_final_decision(advanced_decision)

        # 2. 如果高级系统置信度不足，检查是否使用备用系统
        if self.config.fallback_to_ma13:
            print("⚠️ 高级系统置信度不足，使用MA13备用系统")
            ma13_decision = self._get_ma13_decision(processed_data)
            return self._format_final_decision(ma13_decision)

        # 3. 如果都不满足，返回观望决策
        print("⚠️ 所有系统置信度不足，建议观望")
        return self._format_final_decision({
            'system': 'NONE',
            'action': 'NONE',
            'confidence': 0.0,
            'entry_price': None,
            'stop_loss': processed_data['original_data'].get('current_price', 1.1000),
            'take_profit': processed_data['original_data'].get('current_price', 1.1000),
            'position_size': 0.0,
            'strategy_used': '观望',
            'reasoning': '市场信号不明确，等待更好机会',
            'risk_level': 'LOW',
            'market_phase': '不确定'
        })

    def _format_final_decision(self, decision: Dict) -> Dict:
        """格式化最终决策为系统标准格式"""

        # 转换为现有系统期望的JSON格式
        formatted_decision = {
            'action': decision['action'],
            'orderType': 'MARKET' if decision['action'] != 'NONE' else 'MARKET',
            'entryPrice': decision['entry_price'],
            'stopLoss': decision['stop_loss'],
            'takeProfit': decision['take_profit'],
            'lotSize': decision['position_size'],
            'riskLevel': decision['risk_level'],
            'reasoning': decision['reasoning'],
            'signalConfidence': 'HIGH' if decision['confidence'] > 0.7 else
                              'MEDIUM' if decision['confidence'] > 0.4 else 'LOW',

            # 扩展信息
            'systemUsed': decision['system'],
            'strategyUsed': decision['strategy_used'],
            'marketPhase': decision['market_phase'],
            'confidence': decision['confidence'],
            'timestamp': datetime.now().isoformat(),

            # 为了兼容性，保留ma13Strategy字段
            'ma13Strategy': {
                'timeframe15min': 'UP' if decision['action'] == 'BUY' else
                                'DOWN' if decision['action'] == 'SELL' else 'FLAT',
                'timeframe1hour': 'UP' if decision['action'] == 'BUY' else
                                'DOWN' if decision['action'] == 'SELL' else 'FLAT',
                'priceToMA': 'NEAR',
                'retracement': False
            }
        }

        return formatted_decision

class MA13SystemWrapper:
    """MA13系统包装器（简化版）"""

    def analyze(self, market_data: Dict) -> Dict:
        """分析市场数据（简化的MA13逻辑）"""

        current_price = market_data.get('current_price', 1.1000)
        ma13_15m = market_data.get('ma13_15min', {})
        ma13_1h = market_data.get('ma13_1h', {})

        # 简化的MA13逻辑
        direction_15m = ma13_15m.get('direction', 'FLAT')
        direction_1h = ma13_1h.get('direction', 'FLAT')

        if direction_15m == direction_1h == 'UP':
            action = 'BUY'
            entry_price = current_price + 0.0002
            stop_loss = current_price - 0.0015
            take_profit = current_price + 0.003
        elif direction_15m == direction_1h == 'DOWN':
            action = 'SELL'
            entry_price = current_price - 0.0002
            stop_loss = current_price + 0.0015
            take_profit = current_price - 0.003
        else:
            action = 'NONE'
            entry_price = None
            stop_loss = current_price
            take_profit = current_price

        return {
            'action': action,
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'position_size': 0.1,
            'reasoning': f'13日均线方向: 15分钟({direction_15m}), 1小时({direction_1h})'
        }

class PerformanceTracker:
    """性能跟踪器"""

    def __init__(self):
        self.decisions = []
        self.system_usage = {
            'ADVANCED': 0,
            'MA13_BACKUP': 0,
            'NONE': 0
        }

    def record_decision(self, decision: Dict):
        """记录决策"""
        self.decisions.append({
            'timestamp': datetime.now().isoformat(),
            'system': decision.get('systemUsed', 'UNKNOWN'),
            'action': decision['action'],
            'confidence': decision.get('confidence', 0)
        })

        # 统计系统使用情况
        system_used = decision.get('systemUsed', 'UNKNOWN')
        if system_used in self.system_usage:
            self.system_usage[system_used] += 1

    def get_statistics(self) -> Dict:
        """获取统计信息"""
        total_decisions = len(self.decisions)
        if total_decisions == 0:
            return {'total_decisions': 0}

        return {
            'total_decisions': total_decisions,
            'system_usage': self.system_usage,
            'system_usage_percentage': {
                system: (count / total_decisions * 100)
                for system, count in self.system_usage.items()
            },
            'recent_decisions': self.decisions[-10:] if len(self.decisions) >= 10 else self.decisions
        }

# 使用示例和测试
def test_integration():
    """测试集成系统"""
    print("🚀 开始测试高级交易系统集成")
    print("=" * 60)

    # 1. 创建集成配置
    config = IntegrationConfig(
        enable_advanced_system=True,
        fallback_to_ma13=True,
        confidence_threshold=0.4
    )

    # 2. 初始化集成器
    integrator = SystemIntegrator(config)
    integrator.initialize_systems()

    # 3. 模拟市场数据
    market_data = {
        'current_price': 1.1300,
        'ma13_15min': {'value': 1.1295, 'direction': 'UP'},
        'ma13_1h': {'value': 1.1290, 'direction': 'UP'},
        'rsi': 45,
        'timeframe15m': [],  # 空数据，将使用模拟数据
        'timeframe1h': []
    }

    # 4. 执行分析
    print("\n📊 执行市场分析...")
    decision = integrator.analyze_and_decide(market_data)

    # 5. 显示结果
    print("\n📋 交易决策结果:")
    print(f"   系统: {decision.get('systemUsed', 'UNKNOWN')}")
    print(f"   行动: {decision['action']}")
    print(f"   置信度: {decision.get('confidence', 0):.2f}")
    print(f"   策略: {decision.get('strategyUsed', 'UNKNOWN')}")
    print(f"   入场价格: {decision['entryPrice']}")
    print(f"   止损: {decision['stopLoss']:.5f}")
    print(f"   止盈: {decision['takeProfit']:.5f}")
    print(f"   仓位: {decision['lotSize']:.2f}")
    print(f"   理由: {decision['reasoning']}")

    # 6. 性能统计
    print("\n📈 系统使用统计:")
    stats = integrator.performance_tracker.get_statistics()
    for system, percentage in stats.get('system_usage_percentage', {}).items():
        print(f"   {system}: {percentage:.1f}%")

    return decision

if __name__ == "__main__":
    test_integration()
