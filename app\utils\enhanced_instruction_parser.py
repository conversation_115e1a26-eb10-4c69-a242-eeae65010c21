"""
增强的指令解析器
专门优化LLM分析结果到交易执行的解析过程
"""

import re
import json
from datetime import datetime
from typing import Dict, List, Optional


class EnhancedInstructionParser:
    """增强的指令解析器"""
    
    def __init__(self):
        # 订单管理关键词模式
        self.order_management_patterns = [
            # 调整止损模式
            r'调整现有.*?(?:SELL|BUY)?.*?订单.*?\(?(\d+)\)?.*?止损.*?至?.*?([\d\.]+)',
            r'修改.*?订单.*?\(?(\d+)\)?.*?止损.*?至?.*?([\d\.]+)',
            r'将.*?订单.*?\(?(\d+)\)?.*?止损.*?调整.*?至?.*?([\d\.]+)',
            r'建议.*?调整.*?订单.*?\(?(\d+)\)?.*?止损.*?至?.*?([\d\.]+)',
            
            # 取消订单模式 - 支持多个订单ID
            r'取消.*?([\d\.]+).*?及.*?([\d\.]+).*?的?.*?(?:BUYLIMIT|SELLLIMIT|挂单)',
            r'取消.*?(\d+).*?挂单',
            r'删除.*?订单.*?\(?(\d+)\)?',
            
            # 保留订单模式
            r'保留.*?([\d\.]+).*?的?.*?(?:BUYLIMIT|SELLLIMIT|挂单)',
            
            # 平仓模式
            r'平仓.*?订单.*?\(?(\d+)\)?',
            r'关闭.*?订单.*?\(?(\d+)\)?',
        ]
        
        # 交易动作模式
        self.action_patterns = {
            'BUY': [r'买入', r'做多', r'BUY', r'建立多头', r'开多'],
            'SELL': [r'卖出', r'做空', r'SELL', r'建立空头', r'开空'],
            'NONE': [r'观望', r'等待', r'暂停', r'不交易', r'保持现状']
        }
        
        # 价格提取模式
        self.price_patterns = {
            'entry': [r'入场价[：:]\s*([\d\.]+)', r'进场价[：:]\s*([\d\.]+)', r'开仓价[：:]\s*([\d\.]+)'],
            'stop_loss': [r'止损[：:]\s*([\d\.]+)', r'停损[：:]\s*([\d\.]+)', r'SL[：:]\s*([\d\.]+)'],
            'take_profit': [r'止盈[：:]\s*([\d\.]+)', r'获利[：:]\s*([\d\.]+)', r'TP[：:]\s*([\d\.]+)']
        }
    
    def parse_llm_response(self, llm_response: str) -> Dict:
        """
        解析LLM响应，提取交易指令
        
        Args:
            llm_response: LLM的完整响应文本
            
        Returns:
            Dict: 解析后的交易指令
        """
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🔍 开始增强解析LLM响应')
        
        # 1. 尝试JSON解析
        json_instructions = self._extract_json_instructions(llm_response)
        if json_instructions:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ✅ JSON解析成功')
            # 即使JSON解析成功，也要检查订单管理是否完整
            order_management = self._extract_order_management_from_text(llm_response)
            if order_management and not json_instructions.get('orderManagement'):
                json_instructions['orderManagement'] = order_management
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🔧 补充了{len(order_management)}个订单管理操作')
            return json_instructions
        
        # 2. 文本解析
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🔄 JSON解析失败，使用文本解析')
        text_instructions = self._extract_text_instructions(llm_response)
        
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ✅ 文本解析完成')
        return text_instructions
    
    def _extract_json_instructions(self, response: str) -> Optional[Dict]:
        """提取JSON格式的交易指令"""
        try:
            # 查找JSON块
            json_patterns = [
                r'```json\s*(.*?)\s*```',
                r'```\s*(.*?)\s*```',
                r'\{[^{}]*"action"[^{}]*\}',
                r'\{.*?"action".*?\}'
            ]
            
            for pattern in json_patterns:
                matches = re.finditer(pattern, response, re.DOTALL | re.IGNORECASE)
                for match in matches:
                    json_str = match.group(1) if '```' in pattern else match.group(0)
                    try:
                        data = json.loads(json_str)
                        if 'action' in data:
                            return data
                    except json.JSONDecodeError:
                        continue
            
            return None
            
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ❌ JSON提取失败: {e}')
            return None
    
    def _extract_text_instructions(self, response: str) -> Dict:
        """从文本中提取交易指令"""
        instructions = {
            'action': 'NONE',
            'orderType': 'MARKET',
            'entryPrice': None,
            'stopLoss': None,
            'takeProfit': None,
            'lotSize': 0.01,
            'riskLevel': 'MEDIUM',
            'reasoning': '从文本中提取的交易指令',
            'orderManagement': [],
            'isFinalDecision': True
        }
        
        # 1. 提取交易动作
        action = self._extract_action(response)
        if action:
            instructions['action'] = action
        
        # 2. 提取价格信息
        prices = self._extract_prices(response)
        instructions.update(prices)
        
        # 3. 提取订单管理
        order_management = self._extract_order_management_from_text(response)
        instructions['orderManagement'] = order_management
        
        # 4. 提取手数
        lot_size = self._extract_lot_size(response)
        if lot_size:
            instructions['lotSize'] = lot_size
        
        return instructions
    
    def _extract_action(self, response: str) -> str:
        """提取交易动作"""
        for action, patterns in self.action_patterns.items():
            for pattern in patterns:
                if re.search(pattern, response, re.IGNORECASE):
                    return action
        return 'NONE'
    
    def _extract_prices(self, response: str) -> Dict:
        """提取价格信息"""
        prices = {}
        
        for price_type, patterns in self.price_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, response)
                if match:
                    try:
                        price = float(match.group(1))
                        if price_type == 'entry':
                            prices['entryPrice'] = price
                        elif price_type == 'stop_loss':
                            prices['stopLoss'] = price
                        elif price_type == 'take_profit':
                            prices['takeProfit'] = price
                        break
                    except ValueError:
                        continue
        
        return prices
    
    def _extract_lot_size(self, response: str) -> Optional[float]:
        """提取手数"""
        patterns = [
            r'手数[：:]\s*([\d\.]+)',
            r'仓位[：:]\s*([\d\.]+)',
            r'([\d\.]+)\s*手'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, response)
            if match:
                try:
                    return float(match.group(1))
                except ValueError:
                    continue
        
        return None
    
    def _extract_order_management_from_text(self, response: str) -> List[Dict]:
        """从文本中提取订单管理操作"""
        order_management = []
        
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🔍 开始提取订单管理操作')
        
        for pattern in self.order_management_patterns:
            matches = re.finditer(pattern, response, re.IGNORECASE)
            for match in matches:
                match_text = match.group(0)
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 📝 匹配到: {match_text}')
                
                # 处理不同类型的操作
                if '取消' in match_text or '删除' in match_text:
                    # 处理取消操作
                    order_ids = self._extract_order_ids_from_match(match)
                    for order_id in order_ids:
                        order_management.append({
                            'action': 'DELETE',
                            'orderId': order_id,
                            'reason': f'LLM建议: {match_text[:50]}...'
                        })
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ➕ 添加删除操作: {order_id}')
                
                elif '调整' in match_text or '修改' in match_text:
                    # 处理修改操作
                    if len(match.groups()) >= 2:
                        order_id = match.group(1)
                        new_price = match.group(2)
                        try:
                            price_float = float(new_price)
                            operation = {
                                'action': 'MODIFY',
                                'orderId': order_id,
                                'reason': f'LLM建议: {match_text[:50]}...'
                            }
                            
                            if '止损' in match_text:
                                operation['newStopLoss'] = price_float
                            elif '止盈' in match_text:
                                operation['newTakeProfit'] = price_float
                            else:
                                operation['newStopLoss'] = price_float  # 默认为止损
                            
                            order_management.append(operation)
                            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ➕ 添加修改操作: {order_id} -> {price_float}')
                        except ValueError:
                            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ⚠️ 无法解析价格: {new_price}')
                
                elif '平仓' in match_text or '关闭' in match_text:
                    # 处理平仓操作
                    order_id = match.group(1)
                    order_management.append({
                        'action': 'CLOSE',
                        'orderId': order_id,
                        'reason': f'LLM建议: {match_text[:50]}...'
                    })
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ➕ 添加平仓操作: {order_id}')
        
        # 去重
        unique_operations = []
        seen = set()
        for op in order_management:
            key = f"{op['action']}_{op['orderId']}"
            if key not in seen:
                unique_operations.append(op)
                seen.add(key)
        
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ✅ 提取到{len(unique_operations)}个订单管理操作（去重后）')
        return unique_operations
    
    def _extract_order_ids_from_match(self, match) -> List[str]:
        """从匹配结果中提取订单ID"""
        order_ids = []
        
        # 获取所有匹配的组
        groups = match.groups()
        
        for group in groups:
            if group and group.replace('.', '').isdigit():
                # 检查是否是订单ID（通常比较长）
                if len(group) >= 6:  # 订单ID通常至少6位
                    order_ids.append(group)
                elif '.' in group and len(group.replace('.', '')) >= 4:
                    # 可能是价格中包含的数字，如1.136，转换为订单ID格式
                    potential_id = group.replace('.', '')
                    if len(potential_id) >= 4:
                        order_ids.append(potential_id)
        
        return order_ids
