"""
Token统计模块
用于统计LLM调用的token消耗
"""
import os
import json
import time
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from collections import defaultdict
import threading

# 文件路径
TOKEN_STATS_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'token_statistics.json')
TOKEN_REPORT_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'token_report.txt')
TOKEN_CHARTS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'charts')

# 确保目录存在
os.makedirs(os.path.dirname(TOKEN_STATS_FILE), exist_ok=True)
os.makedirs(TOKEN_CHARTS_DIR, exist_ok=True)

# 模型价格（元/百万token）
MODEL_PRICES = {
    'Pro/deepseek-ai/DeepSeek-R1': 16,  # R1模型价格：16元/百万token
    'Pro/deepseek-ai/DeepSeek-V3': 8     # V3模型价格：8元/百万token
}

# 线程锁，防止并发写入，使用超时机制
token_stats_lock = threading.Lock()

# 锁超时时间（秒）
LOCK_TIMEOUT = 5

def load_token_stats():
    """加载token统计数据"""
    try:
        if os.path.exists(TOKEN_STATS_FILE):
            with open(TOKEN_STATS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {
            'records': [],
            'hourly_stats': {},
            'daily_stats': {},
            'model_stats': {},
            'analysis_type_stats': {},
            'total_tokens': 0,
            'total_cost': 0,
            'last_updated': datetime.now().isoformat()
        }
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 加载token统计数据失败: {e}')
        return {
            'records': [],
            'hourly_stats': {},
            'daily_stats': {},
            'model_stats': {},
            'analysis_type_stats': {},
            'total_tokens': 0,
            'total_cost': 0,
            'last_updated': datetime.now().isoformat()
        }

def save_token_stats(stats):
    """保存token统计数据"""
    try:
        # 使用超时机制获取锁，避免死锁
        if token_stats_lock.acquire(timeout=LOCK_TIMEOUT):
            try:
                with open(TOKEN_STATS_FILE, 'w', encoding='utf-8') as f:
                    json.dump(stats, f, indent=2, ensure_ascii=False)
            finally:
                # 确保锁被释放
                token_stats_lock.release()
        else:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 警告: 获取token统计锁超时，无法保存数据')
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 保存token统计数据失败: {e}')
        import traceback
        traceback.print_exc()

def record_token_usage(model, prompt_tokens, completion_tokens, analysis_type):
    """
    记录token使用情况

    Args:
        model (str): 使用的模型名称
        prompt_tokens (int): 提示词token数
        completion_tokens (int): 生成内容token数
        analysis_type (str): 分析类型，'pre_analysis'或'full_analysis'
    """
    try:
        # 参数验证
        if not isinstance(model, str):
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 警告: model参数类型错误，应为str，实际为{type(model)}')
            model = str(model)

        if not isinstance(prompt_tokens, int):
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 警告: prompt_tokens参数类型错误，应为int，实际为{type(prompt_tokens)}')
            try:
                prompt_tokens = int(prompt_tokens)
            except:
                prompt_tokens = 0

        if not isinstance(completion_tokens, int):
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 警告: completion_tokens参数类型错误，应为int，实际为{type(completion_tokens)}')
            try:
                completion_tokens = int(completion_tokens)
            except:
                completion_tokens = 0

        if not isinstance(analysis_type, str):
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 警告: analysis_type参数类型错误，应为str，实际为{type(analysis_type)}')
            analysis_type = str(analysis_type)

        # 直接处理，不使用锁（简化版本）
        try:
            stats = load_token_stats()

            # 确保所有必需的字段都存在
            if 'hourly_stats' not in stats:
                stats['hourly_stats'] = {}
            if 'daily_stats' not in stats:
                stats['daily_stats'] = {}
            if 'model_stats' not in stats:
                stats['model_stats'] = {}
            if 'analysis_type_stats' not in stats:
                stats['analysis_type_stats'] = {}
            if 'records' not in stats:
                stats['records'] = []
            if 'total_tokens' not in stats:
                stats['total_tokens'] = 0
            if 'total_cost' not in stats:
                stats['total_cost'] = 0

            # 计算总token数和成本
            total_tokens = prompt_tokens + completion_tokens
            cost_per_million = MODEL_PRICES.get(model, 10)  # 默认10元/百万token
            cost = (total_tokens / 1000000) * cost_per_million

            # 获取当前时间
            now = datetime.now()
            timestamp = now.isoformat()
            hour_key = now.strftime('%Y-%m-%d %H:00')
            day_key = now.strftime('%Y-%m-%d')

            # 创建记录
            record = {
                'timestamp': timestamp,
                'model': model,
                'prompt_tokens': prompt_tokens,
                'completion_tokens': completion_tokens,
                'total_tokens': total_tokens,
                'cost': cost,
                'analysis_type': analysis_type,
                'hour': hour_key,
                'day': day_key
            }

            # 添加记录
            stats['records'].append(record)

            # 更新小时统计
            if hour_key not in stats['hourly_stats']:
                stats['hourly_stats'][hour_key] = {
                    'total_tokens': 0,
                    'prompt_tokens': 0,
                    'completion_tokens': 0,
                    'cost': 0,
                    'count': 0
                }
            stats['hourly_stats'][hour_key]['total_tokens'] += total_tokens
            stats['hourly_stats'][hour_key]['prompt_tokens'] += prompt_tokens
            stats['hourly_stats'][hour_key]['completion_tokens'] += completion_tokens
            stats['hourly_stats'][hour_key]['cost'] += cost
            stats['hourly_stats'][hour_key]['count'] += 1

            # 更新日统计
            if day_key not in stats['daily_stats']:
                stats['daily_stats'][day_key] = {
                    'total_tokens': 0,
                    'prompt_tokens': 0,
                    'completion_tokens': 0,
                    'cost': 0,
                    'count': 0
                }
            stats['daily_stats'][day_key]['total_tokens'] += total_tokens
            stats['daily_stats'][day_key]['prompt_tokens'] += prompt_tokens
            stats['daily_stats'][day_key]['completion_tokens'] += completion_tokens
            stats['daily_stats'][day_key]['cost'] += cost
            stats['daily_stats'][day_key]['count'] += 1

            # 更新模型统计
            if model not in stats['model_stats']:
                stats['model_stats'][model] = {
                    'total_tokens': 0,
                    'prompt_tokens': 0,
                    'completion_tokens': 0,
                    'cost': 0,
                    'count': 0
                }
            stats['model_stats'][model]['total_tokens'] += total_tokens
            stats['model_stats'][model]['prompt_tokens'] += prompt_tokens
            stats['model_stats'][model]['completion_tokens'] += completion_tokens
            stats['model_stats'][model]['cost'] += cost
            stats['model_stats'][model]['count'] += 1

            # 更新分析类型统计
            if analysis_type not in stats['analysis_type_stats']:
                stats['analysis_type_stats'][analysis_type] = {
                    'total_tokens': 0,
                    'prompt_tokens': 0,
                    'completion_tokens': 0,
                    'cost': 0,
                    'count': 0
                }
            stats['analysis_type_stats'][analysis_type]['total_tokens'] += total_tokens
            stats['analysis_type_stats'][analysis_type]['prompt_tokens'] += prompt_tokens
            stats['analysis_type_stats'][analysis_type]['completion_tokens'] += completion_tokens
            stats['analysis_type_stats'][analysis_type]['cost'] += cost
            stats['analysis_type_stats'][analysis_type]['count'] += 1

            # 更新总计
            stats['total_tokens'] += total_tokens
            stats['total_cost'] += cost
            stats['last_updated'] = timestamp

            # 保存统计数据
            try:
                save_token_stats(stats)
            except Exception as save_error:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 保存token统计数据失败: {save_error}')

            # 打印当前小时的消耗
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] Token统计: {hour_key} 消耗: {stats["hourly_stats"][hour_key]["total_tokens"]} tokens, 成本: {stats["hourly_stats"][hour_key]["cost"]:.4f} 元')

            return record
        except Exception as inner_error:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 处理token统计数据失败: {inner_error}')
            import traceback
            traceback.print_exc()
            return None
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 记录token使用情况失败: {e}')
        import traceback
        traceback.print_exc()
        return None

def get_hourly_token_stats(hours=24):
    """
    获取最近N小时的token统计

    Args:
        hours (int): 小时数，默认24小时

    Returns:
        dict: 小时统计数据
    """
    try:
        stats = load_token_stats()

        # 获取最近N小时的时间范围
        now = datetime.now()
        start_time = now - timedelta(hours=hours)

        # 过滤最近N小时的记录
        hourly_stats = {}
        for hour_key, hour_data in stats['hourly_stats'].items():
            try:
                hour_time = datetime.fromisoformat(hour_key.replace(' ', 'T'))
                if hour_time >= start_time:
                    hourly_stats[hour_key] = hour_data
            except:
                continue

        # 按时间排序
        sorted_stats = dict(sorted(hourly_stats.items()))

        return sorted_stats
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 获取小时token统计失败: {e}')
        return {}

def generate_token_report():
    """生成token统计报告"""
    try:
        stats = load_token_stats()

        # 生成报告文本
        report = "# Token消耗统计报告\n\n"
        report += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        report += "## 总体统计\n\n"
        report += f"- 总Token消耗: {stats['total_tokens']:,} tokens\n"
        report += f"- 总成本: {stats['total_cost']:.2f} 元\n"
        report += f"- 记录数: {len(stats['records'])}\n\n"

        # 模型统计
        report += "## 模型统计\n\n"
        for model, model_data in stats['model_stats'].items():
            report += f"### {model}\n"
            report += f"- 总Token: {model_data['total_tokens']:,}\n"
            report += f"- 提示词Token: {model_data['prompt_tokens']:,}\n"
            report += f"- 生成Token: {model_data['completion_tokens']:,}\n"
            report += f"- 成本: {model_data['cost']:.2f} 元\n"
            report += f"- 调用次数: {model_data['count']}\n"
            report += f"- 平均每次Token: {model_data['total_tokens'] / model_data['count']:.1f}\n\n"

        # 分析类型统计
        report += "## 分析类型统计\n\n"
        for analysis_type, type_data in stats['analysis_type_stats'].items():
            report += f"### {analysis_type}\n"
            report += f"- 总Token: {type_data['total_tokens']:,}\n"
            report += f"- 提示词Token: {type_data['prompt_tokens']:,}\n"
            report += f"- 生成Token: {type_data['completion_tokens']:,}\n"
            report += f"- 成本: {type_data['cost']:.2f} 元\n"
            report += f"- 调用次数: {type_data['count']}\n"
            report += f"- 平均每次Token: {type_data['total_tokens'] / type_data['count']:.1f}\n\n"

        # 最近24小时统计
        hourly_stats = get_hourly_token_stats(24)
        report += "## 最近24小时统计\n\n"
        for hour, hour_data in hourly_stats.items():
            report += f"- {hour}: {hour_data['total_tokens']:,} tokens, {hour_data['cost']:.2f} 元, {hour_data['count']} 次调用\n"

        # 保存报告
        with open(TOKEN_REPORT_FILE, 'w', encoding='utf-8') as f:
            f.write(report)

        # 生成图表
        generate_token_charts()

        return report
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 生成token统计报告失败: {e}')
        return None

def generate_token_charts():
    """生成token统计图表"""
    try:
        stats = load_token_stats()

        # 确保图表目录存在
        os.makedirs(TOKEN_CHARTS_DIR, exist_ok=True)

        # 生成小时统计图表
        hourly_stats = get_hourly_token_stats(24)
        if hourly_stats:
            hours = list(hourly_stats.keys())
            tokens = [data['total_tokens'] for data in hourly_stats.values()]
            costs = [data['cost'] for data in hourly_stats.values()]

            # Token消耗图表
            plt.figure(figsize=(12, 6))
            plt.bar(hours, tokens)
            plt.title('24小时Token消耗')
            plt.xlabel('小时')
            plt.ylabel('Token数')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(os.path.join(TOKEN_CHARTS_DIR, 'hourly_tokens.png'))
            plt.close()

            # 成本图表
            plt.figure(figsize=(12, 6))
            plt.bar(hours, costs)
            plt.title('24小时成本消耗')
            plt.xlabel('小时')
            plt.ylabel('成本(元)')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(os.path.join(TOKEN_CHARTS_DIR, 'hourly_costs.png'))
            plt.close()

        # 生成模型统计图表
        if stats['model_stats']:
            models = list(stats['model_stats'].keys())
            model_tokens = [data['total_tokens'] for data in stats['model_stats'].values()]
            model_costs = [data['cost'] for data in stats['model_stats'].values()]

            # 模型Token消耗图表
            plt.figure(figsize=(10, 6))
            plt.bar(models, model_tokens)
            plt.title('模型Token消耗')
            plt.xlabel('模型')
            plt.ylabel('Token数')
            plt.tight_layout()
            plt.savefig(os.path.join(TOKEN_CHARTS_DIR, 'model_tokens.png'))
            plt.close()

            # 模型成本图表
            plt.figure(figsize=(10, 6))
            plt.bar(models, model_costs)
            plt.title('模型成本消耗')
            plt.xlabel('模型')
            plt.ylabel('成本(元)')
            plt.tight_layout()
            plt.savefig(os.path.join(TOKEN_CHARTS_DIR, 'model_costs.png'))
            plt.close()

        # 生成分析类型统计图表
        if stats['analysis_type_stats']:
            types = list(stats['analysis_type_stats'].keys())
            type_tokens = [data['total_tokens'] for data in stats['analysis_type_stats'].values()]
            type_costs = [data['cost'] for data in stats['analysis_type_stats'].values()]

            # 分析类型Token消耗图表
            plt.figure(figsize=(10, 6))
            plt.bar(types, type_tokens)
            plt.title('分析类型Token消耗')
            plt.xlabel('分析类型')
            plt.ylabel('Token数')
            plt.tight_layout()
            plt.savefig(os.path.join(TOKEN_CHARTS_DIR, 'type_tokens.png'))
            plt.close()

            # 分析类型成本图表
            plt.figure(figsize=(10, 6))
            plt.bar(types, type_costs)
            plt.title('分析类型成本消耗')
            plt.xlabel('分析类型')
            plt.ylabel('成本(元)')
            plt.tight_layout()
            plt.savefig(os.path.join(TOKEN_CHARTS_DIR, 'type_costs.png'))
            plt.close()

        return True
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 生成token统计图表失败: {e}')
        return False
