"""
LLM优化的技术分析模块
专门为LLM分析设计的技术指标计算和信号解释系统
"""

import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass
from app.utils.logger_manager import log_analysis, LogLevel

@dataclass
class TechnicalSignal:
    """技术信号数据类"""
    indicator: str
    value: float
    signal: str  # 'bullish', 'bearish', 'neutral'
    strength: float  # 0-1
    description: str
    timeframe: str
    confidence: float  # 0-1

@dataclass
class MarketContext:
    """市场环境上下文"""
    trend_direction: str  # 'uptrend', 'downtrend', 'sideways'
    trend_strength: float  # 0-1
    volatility_level: str  # 'low', 'normal', 'high', 'extreme'
    market_phase: str  # 'accumulation', 'markup', 'distribution', 'markdown'
    support_resistance_quality: str  # 'strong', 'moderate', 'weak'

class LLMOptimizedTechnicalAnalyzer:
    """LLM优化的技术分析器"""

    def __init__(self):
        self.supported_timeframes = ['15min', '1h', '4h', '1d']

        # LLM友好的指标权重配置
        self.indicator_weights = {
            'trend_indicators': 0.35,      # 趋势指标权重最高
            'momentum_indicators': 0.25,   # 动量指标
            'volatility_indicators': 0.20, # 波动率指标
            'volume_indicators': 0.10,     # 成交量指标
            'pattern_indicators': 0.10     # 形态指标
        }

        # 信号强度阈值
        self.signal_thresholds = {
            'strong': 0.75,
            'moderate': 0.50,
            'weak': 0.25
        }

    def analyze_for_llm(self, data: List[Dict], timeframe: str = '15min') -> Dict:
        """
        为LLM分析优化的技术分析

        Args:
            data: K线数据
            timeframe: 时间框架

        Returns:
            Dict: LLM友好的分析结果
        """
        try:
            if not data or len(data) < 20:
                return self._create_insufficient_data_response(len(data) if data else 0)

            # 转换为DataFrame
            df = self._prepare_dataframe(data)

            # 计算所有技术指标
            indicators = self._calculate_all_indicators(df)

            # 生成技术信号
            signals = self._generate_technical_signals(indicators, timeframe)

            # 分析市场环境
            market_context = self._analyze_market_context(df, indicators)

            # 生成LLM友好的解释
            llm_analysis = self._generate_llm_friendly_analysis(
                signals, market_context, indicators, timeframe
            )

            return llm_analysis

        except Exception as e:
            log_analysis(f"LLM技术分析失败: {e}", LogLevel.ERROR)
            return self._create_error_response(str(e))

    def _prepare_dataframe(self, data: List[Dict]) -> pd.DataFrame:
        """准备DataFrame"""
        df = pd.DataFrame(data)

        # 确保列名正确
        required_columns = ['time', 'open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                if col == 'volume':
                    df[col] = 0  # 如果没有成交量数据，设为0
                else:
                    raise ValueError(f"缺少必要的数据列: {col}")

        # 转换数据类型
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # 按时间排序
        df = df.sort_values('time').reset_index(drop=True)

        return df

    def _calculate_all_indicators(self, df: pd.DataFrame) -> Dict:
        """计算所有技术指标"""
        indicators = {}

        # 1. 趋势指标
        indicators['trend'] = self._calculate_trend_indicators(df)

        # 2. 动量指标
        indicators['momentum'] = self._calculate_momentum_indicators(df)

        # 3. 波动率指标
        indicators['volatility'] = self._calculate_volatility_indicators(df)

        # 4. 成交量指标
        indicators['volume'] = self._calculate_volume_indicators(df)

        # 5. 形态指标
        indicators['patterns'] = self._calculate_pattern_indicators(df)

        # 6. 支撑阻力位
        indicators['support_resistance'] = self._calculate_support_resistance(df)

        return indicators

    def _calculate_trend_indicators(self, df: pd.DataFrame) -> Dict:
        """计算趋势指标"""
        result = {}

        # 移动平均线系统
        df['ma5'] = df['close'].rolling(window=5).mean()
        df['ma10'] = df['close'].rolling(window=10).mean()
        df['ma20'] = df['close'].rolling(window=20).mean()
        df['ma50'] = df['close'].rolling(window=50).mean() if len(df) >= 50 else None

        current_price = df['close'].iloc[-1]

        # MA排列分析
        ma_alignment = self._analyze_ma_alignment(df)

        # 趋势强度
        trend_strength = self._calculate_trend_strength(df)

        # MACD
        macd_data = self._calculate_macd(df)

        result.update({
            'ma_system': {
                'ma5': df['ma5'].iloc[-1] if not pd.isna(df['ma5'].iloc[-1]) else None,
                'ma10': df['ma10'].iloc[-1] if not pd.isna(df['ma10'].iloc[-1]) else None,
                'ma20': df['ma20'].iloc[-1] if not pd.isna(df['ma20'].iloc[-1]) else None,
                'ma50': df['ma50'].iloc[-1] if df['ma50'] is not None and not pd.isna(df['ma50'].iloc[-1]) else None,
                'current_price': current_price,
                'alignment': ma_alignment,
                'price_vs_ma20': 'above' if current_price > df['ma20'].iloc[-1] else 'below'
            },
            'trend_strength': trend_strength,
            'macd': macd_data
        })

        return result

    def _calculate_momentum_indicators(self, df: pd.DataFrame) -> Dict:
        """计算动量指标"""
        result = {}

        # RSI
        rsi = self._calculate_rsi(df['close'])

        # 随机指标
        stoch = self._calculate_stochastic(df)

        # 威廉指标
        williams_r = self._calculate_williams_r(df)

        # 动量
        momentum = self._calculate_momentum(df['close'])

        result.update({
            'rsi': {
                'value': rsi,
                'level': self._interpret_rsi_level(rsi),
                'signal': self._interpret_rsi_signal(rsi)
            },
            'stochastic': stoch,
            'williams_r': {
                'value': williams_r,
                'signal': self._interpret_williams_signal(williams_r)
            },
            'momentum': momentum
        })

        return result

    def _calculate_volatility_indicators(self, df: pd.DataFrame) -> Dict:
        """计算波动率指标"""
        result = {}

        # 布林带
        bollinger = self._calculate_bollinger_bands(df)

        # ATR
        atr = self._calculate_atr(df)

        # 历史波动率
        historical_volatility = self._calculate_historical_volatility(df)

        result.update({
            'bollinger_bands': bollinger,
            'atr': atr,
            'historical_volatility': historical_volatility
        })

        return result

    def _calculate_volume_indicators(self, df: pd.DataFrame) -> Dict:
        """计算成交量指标"""
        result = {}

        if df['volume'].sum() == 0:
            # 没有成交量数据
            result['available'] = False
            result['note'] = '外汇市场无集中成交量数据'
            return result

        # 成交量移动平均
        df['volume_ma'] = df['volume'].rolling(window=20).mean()

        # 成交量相对强度
        volume_ratio = df['volume'].iloc[-1] / df['volume_ma'].iloc[-1] if df['volume_ma'].iloc[-1] > 0 else 1

        result.update({
            'available': True,
            'current_volume': df['volume'].iloc[-1],
            'volume_ma20': df['volume_ma'].iloc[-1],
            'volume_ratio': volume_ratio,
            'volume_trend': 'high' if volume_ratio > 1.5 else 'low' if volume_ratio < 0.5 else 'normal'
        })

        return result

    def _calculate_pattern_indicators(self, df: pd.DataFrame) -> Dict:
        """计算形态指标"""
        result = {}

        # K线形态
        candlestick_patterns = self._identify_candlestick_patterns(df)

        # 价格形态
        price_patterns = self._identify_price_patterns(df)

        result.update({
            'candlestick_patterns': candlestick_patterns,
            'price_patterns': price_patterns
        })

        return result

    def _calculate_support_resistance(self, df: pd.DataFrame) -> Dict:
        """计算支撑阻力位"""
        result = {}

        # 使用最近50根K线计算支撑阻力
        recent_data = df.tail(50)

        # 寻找局部高点和低点
        highs = []
        lows = []

        for i in range(2, len(recent_data) - 2):
            # 局部高点
            if (recent_data['high'].iloc[i] > recent_data['high'].iloc[i-1] and
                recent_data['high'].iloc[i] > recent_data['high'].iloc[i-2] and
                recent_data['high'].iloc[i] > recent_data['high'].iloc[i+1] and
                recent_data['high'].iloc[i] > recent_data['high'].iloc[i+2]):
                highs.append(recent_data['high'].iloc[i])

            # 局部低点
            if (recent_data['low'].iloc[i] < recent_data['low'].iloc[i-1] and
                recent_data['low'].iloc[i] < recent_data['low'].iloc[i-2] and
                recent_data['low'].iloc[i] < recent_data['low'].iloc[i+1] and
                recent_data['low'].iloc[i] < recent_data['low'].iloc[i+2]):
                lows.append(recent_data['low'].iloc[i])

        current_price = df['close'].iloc[-1]

        # 找到最近的支撑和阻力位
        resistance_levels = [h for h in highs if h > current_price]
        support_levels = [l for l in lows if l < current_price]

        nearest_resistance = min(resistance_levels) if resistance_levels else None
        nearest_support = max(support_levels) if support_levels else None

        result.update({
            'nearest_support': nearest_support,
            'nearest_resistance': nearest_resistance,
            'support_distance': (current_price - nearest_support) / current_price * 100 if nearest_support else None,
            'resistance_distance': (nearest_resistance - current_price) / current_price * 100 if nearest_resistance else None,
            'all_support_levels': sorted(support_levels, reverse=True)[:3],
            'all_resistance_levels': sorted(resistance_levels)[:3]
        })

        return result

    def _analyze_ma_alignment(self, df: pd.DataFrame) -> str:
        """分析移动平均线排列"""
        try:
            ma5 = df['ma5'].iloc[-1]
            ma10 = df['ma10'].iloc[-1]
            ma20 = df['ma20'].iloc[-1]

            if pd.isna(ma5) or pd.isna(ma10) or pd.isna(ma20):
                return 'insufficient_data'

            if ma5 > ma10 > ma20:
                return 'bullish_alignment'
            elif ma5 < ma10 < ma20:
                return 'bearish_alignment'
            else:
                return 'mixed_alignment'
        except Exception:
            return 'unknown'

    def _calculate_trend_strength(self, df: pd.DataFrame) -> Dict:
        """计算趋势强度"""
        try:
            # 使用ADX概念计算趋势强度
            high = df['high']
            low = df['low']
            close = df['close']

            # 计算真实波幅
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

            # 计算方向性移动
            plus_dm = high.diff()
            minus_dm = -low.diff()

            plus_dm[plus_dm < 0] = 0
            minus_dm[minus_dm < 0] = 0

            # 平滑处理
            period = 14
            tr_smooth = tr.rolling(window=period).mean()
            plus_dm_smooth = plus_dm.rolling(window=period).mean()
            minus_dm_smooth = minus_dm.rolling(window=period).mean()

            # 计算DI
            plus_di = 100 * plus_dm_smooth / tr_smooth
            minus_di = 100 * minus_dm_smooth / tr_smooth

            # 计算ADX
            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
            adx = dx.rolling(window=period).mean()

            current_adx = adx.iloc[-1] if not pd.isna(adx.iloc[-1]) else 0
            current_plus_di = plus_di.iloc[-1] if not pd.isna(plus_di.iloc[-1]) else 0
            current_minus_di = minus_di.iloc[-1] if not pd.isna(minus_di.iloc[-1]) else 0

            # 趋势强度解释
            if current_adx > 50:
                strength_level = 'very_strong'
            elif current_adx > 25:
                strength_level = 'strong'
            elif current_adx > 15:
                strength_level = 'moderate'
            else:
                strength_level = 'weak'

            # 趋势方向
            if current_plus_di > current_minus_di:
                direction = 'bullish'
            elif current_minus_di > current_plus_di:
                direction = 'bearish'
            else:
                direction = 'neutral'

            return {
                'adx': current_adx,
                'plus_di': current_plus_di,
                'minus_di': current_minus_di,
                'strength_level': strength_level,
                'direction': direction
            }

        except Exception:
            return {
                'adx': 0,
                'plus_di': 0,
                'minus_di': 0,
                'strength_level': 'unknown',
                'direction': 'unknown'
            }

    def _calculate_macd(self, df: pd.DataFrame) -> Dict:
        """计算MACD"""
        try:
            close = df['close']

            # 计算EMA
            ema12 = close.ewm(span=12).mean()
            ema26 = close.ewm(span=26).mean()

            # MACD线
            macd_line = ema12 - ema26

            # 信号线
            signal_line = macd_line.ewm(span=9).mean()

            # 柱状图
            histogram = macd_line - signal_line

            current_macd = macd_line.iloc[-1]
            current_signal = signal_line.iloc[-1]
            current_histogram = histogram.iloc[-1]

            # 信号解释
            if current_macd > current_signal and current_histogram > 0:
                signal = 'bullish'
            elif current_macd < current_signal and current_histogram < 0:
                signal = 'bearish'
            else:
                signal = 'neutral'

            # 背离检测
            divergence = self._detect_macd_divergence(df, macd_line)

            return {
                'macd_line': current_macd,
                'signal_line': current_signal,
                'histogram': current_histogram,
                'signal': signal,
                'divergence': divergence
            }

        except Exception:
            return {
                'macd_line': 0,
                'signal_line': 0,
                'histogram': 0,
                'signal': 'unknown',
                'divergence': 'none'
            }

    def _calculate_rsi(self, close_prices: pd.Series, period: int = 14) -> float:
        """计算RSI"""
        try:
            delta = close_prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))

            return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
        except Exception:
            return 50.0

    def _interpret_rsi_level(self, rsi: float) -> str:
        """解释RSI水平"""
        if rsi >= 80:
            return 'extremely_overbought'
        elif rsi >= 70:
            return 'overbought'
        elif rsi >= 60:
            return 'bullish'
        elif rsi >= 40:
            return 'neutral'
        elif rsi >= 30:
            return 'bearish'
        elif rsi >= 20:
            return 'oversold'
        else:
            return 'extremely_oversold'

    def _interpret_rsi_signal(self, rsi: float) -> str:
        """解释RSI信号"""
        if rsi > 70:
            return 'sell_signal'
        elif rsi < 30:
            return 'buy_signal'
        else:
            return 'neutral'

    def _generate_technical_signals(self, indicators: Dict, timeframe: str) -> List[TechnicalSignal]:
        """生成技术信号"""
        signals = []

        try:
            # 趋势信号
            if 'trend' in indicators:
                trend_data = indicators['trend']

                # MA信号
                if 'ma_system' in trend_data:
                    ma_signal = self._generate_ma_signal(trend_data['ma_system'], timeframe)
                    if ma_signal:
                        signals.append(ma_signal)

                # MACD信号
                if 'macd' in trend_data:
                    macd_signal = self._generate_macd_signal(trend_data['macd'], timeframe)
                    if macd_signal:
                        signals.append(macd_signal)

                # 趋势强度信号
                if 'trend_strength' in trend_data:
                    trend_signal = self._generate_trend_strength_signal(trend_data['trend_strength'], timeframe)
                    if trend_signal:
                        signals.append(trend_signal)

            # 动量信号
            if 'momentum' in indicators:
                momentum_data = indicators['momentum']

                # RSI信号
                if 'rsi' in momentum_data:
                    rsi_signal = self._generate_rsi_signal(momentum_data['rsi'], timeframe)
                    if rsi_signal:
                        signals.append(rsi_signal)

            # 波动率信号
            if 'volatility' in indicators:
                volatility_data = indicators['volatility']

                # 布林带信号
                if 'bollinger_bands' in volatility_data:
                    bb_signal = self._generate_bollinger_signal(volatility_data['bollinger_bands'], timeframe)
                    if bb_signal:
                        signals.append(bb_signal)

            return signals

        except Exception as e:
            log_analysis(f"生成技术信号失败: {e}", LogLevel.ERROR)
            return []

    def _generate_ma_signal(self, ma_data: Dict, timeframe: str) -> Optional[TechnicalSignal]:
        """生成移动平均线信号"""
        try:
            alignment = ma_data.get('alignment', 'unknown')
            current_price = ma_data.get('current_price', 0)
            ma20 = ma_data.get('ma20', 0)

            if alignment == 'bullish_alignment':
                return TechnicalSignal(
                    indicator='MA_System',
                    value=current_price,
                    signal='bullish',
                    strength=0.8,
                    description=f'移动平均线呈多头排列，当前价格{current_price:.5f}位于MA20({ma20:.5f})上方',
                    timeframe=timeframe,
                    confidence=0.75
                )
            elif alignment == 'bearish_alignment':
                return TechnicalSignal(
                    indicator='MA_System',
                    value=current_price,
                    signal='bearish',
                    strength=0.8,
                    description=f'移动平均线呈空头排列，当前价格{current_price:.5f}位于MA20({ma20:.5f})下方',
                    timeframe=timeframe,
                    confidence=0.75
                )

            return None

        except Exception:
            return None

    def _generate_macd_signal(self, macd_data: Dict, timeframe: str) -> Optional[TechnicalSignal]:
        """生成MACD信号"""
        try:
            signal = macd_data.get('signal', 'neutral')
            histogram = macd_data.get('histogram', 0)
            divergence = macd_data.get('divergence', 'none')

            if signal == 'bullish':
                strength = 0.7 if divergence == 'bullish_divergence' else 0.6
                description = f'MACD金叉信号，柱状图{histogram:.6f}'
                if divergence == 'bullish_divergence':
                    description += '，检测到底背离'

                return TechnicalSignal(
                    indicator='MACD',
                    value=histogram,
                    signal='bullish',
                    strength=strength,
                    description=description,
                    timeframe=timeframe,
                    confidence=0.7
                )
            elif signal == 'bearish':
                strength = 0.7 if divergence == 'bearish_divergence' else 0.6
                description = f'MACD死叉信号，柱状图{histogram:.6f}'
                if divergence == 'bearish_divergence':
                    description += '，检测到顶背离'

                return TechnicalSignal(
                    indicator='MACD',
                    value=histogram,
                    signal='bearish',
                    strength=strength,
                    description=description,
                    timeframe=timeframe,
                    confidence=0.7
                )

            return None

        except Exception:
            return None

    def _generate_rsi_signal(self, rsi_data: Dict, timeframe: str) -> Optional[TechnicalSignal]:
        """生成RSI信号"""
        try:
            rsi_value = rsi_data.get('value', 50)
            level = rsi_data.get('level', 'neutral')
            signal = rsi_data.get('signal', 'neutral')

            if signal == 'buy_signal':
                return TechnicalSignal(
                    indicator='RSI',
                    value=rsi_value,
                    signal='bullish',
                    strength=0.8,
                    description=f'RSI超卖信号，当前值{rsi_value:.1f}，处于{level}区域',
                    timeframe=timeframe,
                    confidence=0.75
                )
            elif signal == 'sell_signal':
                return TechnicalSignal(
                    indicator='RSI',
                    value=rsi_value,
                    signal='bearish',
                    strength=0.8,
                    description=f'RSI超买信号，当前值{rsi_value:.1f}，处于{level}区域',
                    timeframe=timeframe,
                    confidence=0.75
                )

            return None

        except Exception:
            return None

    def _analyze_market_context(self, df: pd.DataFrame, indicators: Dict) -> MarketContext:
        """分析市场环境上下文"""
        try:
            # 趋势方向分析
            trend_direction = self._determine_trend_direction(indicators)

            # 趋势强度分析
            trend_strength = self._determine_trend_strength(indicators)

            # 波动率水平分析
            volatility_level = self._determine_volatility_level(indicators)

            # 市场阶段分析
            market_phase = self._determine_market_phase(df, indicators)

            # 支撑阻力质量分析
            sr_quality = self._determine_support_resistance_quality(indicators)

            return MarketContext(
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                volatility_level=volatility_level,
                market_phase=market_phase,
                support_resistance_quality=sr_quality
            )

        except Exception as e:
            log_analysis(f"分析市场环境失败: {e}", LogLevel.ERROR)
            return MarketContext(
                trend_direction='unknown',
                trend_strength=0.0,
                volatility_level='unknown',
                market_phase='unknown',
                support_resistance_quality='unknown'
            )

    def _determine_trend_direction(self, indicators: Dict) -> str:
        """确定趋势方向"""
        try:
            bullish_signals = 0
            bearish_signals = 0

            # MA排列
            if 'trend' in indicators and 'ma_system' in indicators['trend']:
                alignment = indicators['trend']['ma_system'].get('alignment', 'unknown')
                if alignment == 'bullish_alignment':
                    bullish_signals += 2
                elif alignment == 'bearish_alignment':
                    bearish_signals += 2

            # MACD
            if 'trend' in indicators and 'macd' in indicators['trend']:
                macd_signal = indicators['trend']['macd'].get('signal', 'neutral')
                if macd_signal == 'bullish':
                    bullish_signals += 1
                elif macd_signal == 'bearish':
                    bearish_signals += 1

            # 趋势强度方向
            if 'trend' in indicators and 'trend_strength' in indicators['trend']:
                direction = indicators['trend']['trend_strength'].get('direction', 'neutral')
                if direction == 'bullish':
                    bullish_signals += 1
                elif direction == 'bearish':
                    bearish_signals += 1

            if bullish_signals > bearish_signals:
                return 'uptrend'
            elif bearish_signals > bullish_signals:
                return 'downtrend'
            else:
                return 'sideways'

        except Exception:
            return 'unknown'

    def _generate_trend_strength_signal(self, trend_data: Dict, timeframe: str) -> Optional[TechnicalSignal]:
        """生成趋势强度信号"""
        try:
            strength_level = trend_data.get('strength_level', 'unknown')
            direction = trend_data.get('direction', 'unknown')
            adx = trend_data.get('adx', 0)

            if strength_level in ['strong', 'very_strong'] and direction != 'neutral':
                signal_type = 'bullish' if direction == 'bullish' else 'bearish'
                strength = 0.8 if strength_level == 'very_strong' else 0.6

                return TechnicalSignal(
                    indicator='Trend_Strength',
                    value=adx,
                    signal=signal_type,
                    strength=strength,
                    description=f'趋势强度{strength_level}，ADX值{adx:.1f}，方向{direction}',
                    timeframe=timeframe,
                    confidence=0.7
                )

            return None

        except Exception:
            return None

    def _generate_bollinger_signal(self, bb_data: Dict, timeframe: str) -> Optional[TechnicalSignal]:
        """生成布林带信号"""
        try:
            signal = bb_data.get('signal', 'neutral')
            position = bb_data.get('position', 0.5)
            squeeze = bb_data.get('squeeze', False)

            if signal == 'overbought':
                return TechnicalSignal(
                    indicator='Bollinger_Bands',
                    value=position,
                    signal='bearish',
                    strength=0.7,
                    description=f'价格触及布林带上轨，位置{position:.2f}，可能回调',
                    timeframe=timeframe,
                    confidence=0.6
                )
            elif signal == 'oversold':
                return TechnicalSignal(
                    indicator='Bollinger_Bands',
                    value=position,
                    signal='bullish',
                    strength=0.7,
                    description=f'价格触及布林带下轨，位置{position:.2f}，可能反弹',
                    timeframe=timeframe,
                    confidence=0.6
                )
            elif squeeze:
                return TechnicalSignal(
                    indicator='Bollinger_Bands',
                    value=position,
                    signal='neutral',
                    strength=0.5,
                    description='布林带收缩，可能即将出现突破',
                    timeframe=timeframe,
                    confidence=0.5
                )

            return None

        except Exception:
            return None

    def _determine_trend_strength(self, indicators: Dict) -> float:
        """确定趋势强度"""
        try:
            strength = 0.0

            # ADX强度
            if 'trend' in indicators and 'trend_strength' in indicators['trend']:
                adx = indicators['trend']['trend_strength'].get('adx', 0)
                if adx > 50:
                    strength += 0.4
                elif adx > 25:
                    strength += 0.3
                elif adx > 15:
                    strength += 0.2
                else:
                    strength += 0.1

            # MA排列强度
            if 'trend' in indicators and 'ma_system' in indicators['trend']:
                alignment = indicators['trend']['ma_system'].get('alignment', 'unknown')
                if alignment in ['bullish_alignment', 'bearish_alignment']:
                    strength += 0.3
                elif alignment == 'mixed_alignment':
                    strength += 0.1

            # MACD强度
            if 'trend' in indicators and 'macd' in indicators['trend']:
                macd_signal = indicators['trend']['macd'].get('signal', 'neutral')
                if macd_signal != 'neutral':
                    strength += 0.2

            return min(1.0, strength)

        except Exception:
            return 0.5

    def _determine_volatility_level(self, indicators: Dict) -> str:
        """确定波动率水平"""
        try:
            # ATR波动率
            if 'volatility' in indicators and 'atr' in indicators['volatility']:
                volatility_level = indicators['volatility']['atr'].get('volatility_level', 'unknown')
                return volatility_level

            # 布林带宽度
            if 'volatility' in indicators and 'bollinger_bands' in indicators['volatility']:
                width = indicators['volatility']['bollinger_bands'].get('width', 0)
                if width > 15:
                    return 'extremely_high'
                elif width > 10:
                    return 'high'
                elif width > 5:
                    return 'normal'
                elif width > 2:
                    return 'low'
                else:
                    return 'extremely_low'

            return 'unknown'

        except Exception:
            return 'unknown'

    def _determine_market_phase(self, df: pd.DataFrame, indicators: Dict) -> str:
        """确定市场阶段"""
        try:
            # 简化的市场阶段判断
            current_price = df['close'].iloc[-1]

            # 获取支撑阻力信息
            if 'support_resistance' in indicators:
                sr_data = indicators['support_resistance']
                nearest_support = sr_data.get('nearest_support')
                nearest_resistance = sr_data.get('nearest_resistance')

                # 基于价格位置判断阶段
                if nearest_support and nearest_resistance:
                    support_distance = (current_price - nearest_support) / current_price * 100
                    resistance_distance = (nearest_resistance - current_price) / current_price * 100

                    if support_distance < 1 and resistance_distance > 3:
                        return 'accumulation'
                    elif resistance_distance < 1 and support_distance > 3:
                        return 'distribution'
                    elif support_distance > 2 and resistance_distance > 2:
                        return 'markup'
                    else:
                        return 'markdown'

            # 基于趋势判断
            if 'trend' in indicators and 'ma_system' in indicators['trend']:
                alignment = indicators['trend']['ma_system'].get('alignment', 'unknown')
                if alignment == 'bullish_alignment':
                    return 'markup'
                elif alignment == 'bearish_alignment':
                    return 'markdown'
                else:
                    return 'accumulation'

            return 'unknown'

        except Exception:
            return 'unknown'

    def _determine_support_resistance_quality(self, indicators: Dict) -> str:
        """确定支撑阻力质量"""
        try:
            if 'support_resistance' in indicators:
                sr_data = indicators['support_resistance']

                # 检查支撑阻力位的数量和距离
                support_levels = sr_data.get('all_support_levels', [])
                resistance_levels = sr_data.get('all_resistance_levels', [])

                total_levels = len(support_levels) + len(resistance_levels)

                if total_levels >= 4:
                    return 'strong'
                elif total_levels >= 2:
                    return 'moderate'
                else:
                    return 'weak'

            return 'unknown'

        except Exception:
            return 'unknown'

    def _generate_llm_friendly_analysis(self, signals: List, market_context, indicators: Dict, timeframe: str) -> Dict:
        """生成LLM友好的分析结果"""
        try:
            # 尝试导入格式化器
            try:
                from app.utils.llm_analysis_formatter import LLMAnalysisFormatter
                formatter = LLMAnalysisFormatter()
                return formatter.format_for_llm(signals, market_context, indicators, timeframe)
            except ImportError:
                # 如果格式化器不可用，创建基础分析结果
                return {
                    'timeframe': timeframe,
                    'signals_count': len(signals),
                    'market_trend': market_context.trend_direction,
                    'trend_strength': market_context.trend_strength,
                    'volatility': market_context.volatility_level,
                    'analysis_available': True,
                    'note': 'LLM格式化器不可用，返回基础分析结果'
                }

        except Exception as e:
            return {
                'error': True,
                'message': f'生成LLM分析失败: {e}',
                'timeframe': timeframe,
                'fallback_data': {
                    'signals_count': len(signals) if signals else 0,
                    'market_trend': getattr(market_context, 'trend_direction', 'unknown'),
                    'analysis_available': False
                }
            }

    def _create_insufficient_data_response(self, data_count: int) -> Dict:
        """创建数据不足响应"""
        return {
            'analysis_available': False,
            'reason': 'insufficient_data',
            'data_count': data_count,
            'minimum_required': 20,
            'message': f'数据不足，当前{data_count}条，需要至少20条数据进行分析'
        }

    def _create_error_response(self, error_msg: str) -> Dict:
        """创建错误响应"""
        return {
            'analysis_available': False,
            'error': True,
            'message': error_msg,
            'timestamp': datetime.now().isoformat()
        }

    def _detect_macd_divergence(self, df: pd.DataFrame, macd_line: pd.Series) -> str:
        """检测MACD背离"""
        try:
            # 导入扩展模块中的背离检测方法
            from app.utils.llm_technical_analyzer_extended import LLMTechnicalAnalyzerExtended
            extended_analyzer = LLMTechnicalAnalyzerExtended()
            return extended_analyzer.detect_macd_divergence(df, macd_line)
        except Exception:
            return 'none'