"""
绩效反馈模块
用于生成绩效反馈，并将其集成到LLM提示词中
"""
import random
from datetime import datetime, timedelta
from app.utils.performance_evaluator import get_performance_summary, TradeResult
from app.utils.order_result_analyzer import get_order_result_statistics, OrderResultType


def generate_performance_feedback():
    """
    生成绩效反馈

    Returns:
        str: 绩效反馈
    """
    # 获取绩效摘要
    summary = get_performance_summary()

    # 账户摘要
    account_summary = summary["account_summary"]

    # 周期绩效
    period_performance = summary["period_performance"]

    # 最近交易
    recent_trades = summary["recent_trades"]

    # 绩效指标
    metrics = summary["performance_metrics"]

    # 生成绩效反馈
    feedback = []

    # 添加账户摘要
    feedback.append("## 交易绩效反馈")

    # 添加账户状态
    initial_balance = account_summary["initial_balance"]
    current_balance = account_summary["current_balance"]
    total_return = account_summary["total_return"]

    feedback.append(f"### 账户状态")
    feedback.append(f"- 初始资金: ${initial_balance:.2f}")
    feedback.append(f"- 当前余额: ${current_balance:.2f}")
    feedback.append(f"- 总收益率: {total_return:.2f}%")

    # 添加周期绩效
    feedback.append(f"\n### 周期绩效")

    today_return = period_performance["today"]["return"]
    week_return = period_performance["week"]["return"]
    month_return = period_performance["month"]["return"]

    feedback.append(f"- 今日收益率: {today_return:.2f}%")
    feedback.append(f"- 本周收益率: {week_return:.2f}%")
    feedback.append(f"- 本月收益率: {month_return:.2f}%")

    # 添加关键指标
    feedback.append(f"\n### 关键指标")

    win_rate = metrics["win_rate"]
    profit_factor = metrics["profit_factor"]
    max_drawdown = metrics["max_drawdown"]
    sharpe_ratio = metrics["sharpe_ratio"]

    feedback.append(f"- 胜率: {win_rate:.2f}%")
    feedback.append(f"- 盈亏比: {profit_factor:.2f}")
    feedback.append(f"- 最大回撤: {max_drawdown:.2f}%")
    feedback.append(f"- 夏普比率: {sharpe_ratio:.2f}")

    # 添加最近交易
    if recent_trades:
        feedback.append(f"\n### 最近交易")

        for trade in recent_trades:
            result = "盈利" if trade["result"] == TradeResult.WIN else "亏损" if trade["result"] == TradeResult.LOSS else "持平"
            direction = "买入" if trade["direction"] == "BUY" else "卖出"

            feedback.append(f"- {trade['symbol']} {direction}: {trade['profit_loss']:.2f}美元 ({trade['profit_loss_pips']:.1f}点) - {result}")

    # 添加订单结果统计
    feedback.append(f"\n### 订单结果统计")

    # 获取订单结果统计
    order_stats = get_order_result_statistics()

    total_count = order_stats.get('total_count', 0)
    take_profit_count = order_stats.get('take_profit_count', 0)
    stop_loss_count = order_stats.get('stop_loss_count', 0)
    manual_close_count = order_stats.get('manual_close_count', 0)

    take_profit_rate = order_stats.get('take_profit_rate', 0)
    stop_loss_rate = order_stats.get('stop_loss_rate', 0)
    manual_close_rate = order_stats.get('manual_close_rate', 0)

    avg_duration = order_stats.get('avg_duration_hours', 0)
    avg_risk_reward = order_stats.get('avg_risk_reward_ratio', 0)

    if total_count > 0:
        feedback.append(f"- 总订单数: {total_count}")
        feedback.append(f"- 止盈订单: {take_profit_count} ({take_profit_rate:.1f}%)")
        feedback.append(f"- 止损订单: {stop_loss_count} ({stop_loss_rate:.1f}%)")
        feedback.append(f"- 手动平仓: {manual_close_count} ({manual_close_rate:.1f}%)")
        feedback.append(f"- 平均持仓时间: {avg_duration:.1f}小时")
        feedback.append(f"- 平均风险回报比: {avg_risk_reward:.2f}")
    else:
        feedback.append("- 暂无订单结果统计数据")

    # 添加绩效评价
    feedback.append(f"\n### 绩效评价")

    # 根据绩效指标生成评价
    evaluations = []

    # 评价胜率
    if win_rate >= 60:
        evaluations.append("你的胜率表现优秀，继续保持这种高质量的交易决策。")
    elif win_rate >= 50:
        evaluations.append("你的胜率处于合理水平，但仍有提升空间。")
    elif win_rate > 0:
        evaluations.append("你的胜率偏低，需要改进交易策略或入场时机。")

    # 评价止盈/止损比例
    if total_count > 5:  # 只有当有足够的样本时才评价
        if take_profit_rate >= 60:
            evaluations.append("你的止盈触发率很高，说明你的止盈设置合理，并且善于捕捉趋势。")
        elif take_profit_rate >= 40:
            evaluations.append("你的止盈触发率处于合理水平，但可以通过优化止盈设置来提高这一比例。")
        elif take_profit_rate > 0:
            evaluations.append("你的止盈触发率偏低，需要重新评估止盈策略，可能设置得过远或市场波动性不足。")

        if stop_loss_rate <= 20:
            evaluations.append("你的止损触发率很低，说明你的入场时机选择得当，风险控制良好。")
        elif stop_loss_rate <= 40:
            evaluations.append("你的止损触发率处于合理水平，但仍有改进空间，可以通过优化入场时机来降低这一比例。")
        elif stop_loss_rate > 40:
            evaluations.append("你的止损触发率偏高，需要改进入场策略和时机，避免在不利的市场条件下交易。")

    # 评价盈亏比
    if profit_factor >= 2:
        evaluations.append("你的盈亏比表现出色，说明你善于控制亏损并让盈利奔跑。")
    elif profit_factor >= 1.5:
        evaluations.append("你的盈亏比处于健康水平，继续优化止损和止盈设置。")
    elif profit_factor >= 1:
        evaluations.append("你的盈亏比勉强维持盈利，需要提高单笔交易的平均收益。")
    elif profit_factor > 0:
        evaluations.append("你的盈亏比过低，亏损交易的损失超过了盈利交易的收益，需要重新评估风险管理策略。")

    # 评价最大回撤
    if max_drawdown <= 5:
        evaluations.append("你的最大回撤控制得很好，显示出优秀的风险管理能力。")
    elif max_drawdown <= 10:
        evaluations.append("你的最大回撤处于可接受范围，但仍需注意连续亏损的风险。")
    elif max_drawdown <= 20:
        evaluations.append("你的最大回撤略高，建议审视风险管理策略，避免过度交易。")
    elif max_drawdown > 20:
        evaluations.append("你的最大回撤过高，需要立即改进风险控制措施，减小单笔交易的风险敞口。")

    # 评价夏普比率
    if sharpe_ratio >= 2:
        evaluations.append("你的夏普比率表现优秀，显示出高回报与低风险的完美平衡。")
    elif sharpe_ratio >= 1:
        evaluations.append("你的夏普比率处于良好水平，风险调整后的收益率具有竞争力。")
    elif sharpe_ratio >= 0:
        evaluations.append("你的夏普比率偏低，需要提高收益率或降低波动性。")
    elif sharpe_ratio < 0:
        evaluations.append("你的夏普比率为负，表明策略的风险收益特性不佳，需要彻底重新评估。")

    # 随机选择2-3条评价
    if evaluations:
        selected_evaluations = random.sample(evaluations, min(3, len(evaluations)))
        feedback.extend(selected_evaluations)

    # 添加改进建议
    feedback.append(f"\n### 改进建议")

    suggestions = []

    # 根据绩效指标生成建议
    if win_rate < 50:
        suggestions.append("提高胜率：更加严格筛选交易信号，只在高概率设置下入场。")

    if profit_factor < 1.5:
        suggestions.append("提高盈亏比：扩大止盈目标，缩小止损范围，或者更早地退出亏损交易。")

    if max_drawdown > 10:
        suggestions.append("减少回撤：降低单笔交易的风险敞口，避免过度集中的头寸。")

    if metrics["consecutive_losses"] >= 3:
        suggestions.append("打破连续亏损：当遭遇连续亏损时，考虑暂时减小交易规模或暂停交易，重新评估市场环境。")

    if metrics["average_win"] < abs(metrics["average_loss"]):
        suggestions.append("扩大平均盈利：让盈利交易持续更长时间，或者设置更远的止盈目标。")

    # 随机选择2-3条建议
    if suggestions:
        selected_suggestions = random.sample(suggestions, min(3, len(suggestions)))
        feedback.extend(selected_suggestions)

    # 添加绩效目标
    feedback.append(f"\n### 绩效目标")

    # 设置合理的目标
    daily_target = 0.3
    weekly_target = 1.5
    monthly_target = 5.0

    # 根据当前绩效调整目标
    if total_return > 20:
        daily_target = 0.4
        weekly_target = 2.0
        monthly_target = 7.0
    elif total_return < 0:
        daily_target = 0.2
        weekly_target = 1.0
        monthly_target = 3.0

    # 计算目标进度
    today_progress = (today_return / daily_target) * 100 if daily_target > 0 else 0
    week_progress = (week_return / weekly_target) * 100 if weekly_target > 0 else 0
    month_progress = (month_return / monthly_target) * 100 if monthly_target > 0 else 0

    feedback.append(f"- 日目标: {daily_target:.1f}% (当前进度: {today_progress:.0f}%)")
    feedback.append(f"- 周目标: {weekly_target:.1f}% (当前进度: {week_progress:.0f}%)")
    feedback.append(f"- 月目标: {monthly_target:.1f}% (当前进度: {month_progress:.0f}%)")

    # 添加激励信息
    feedback.append(f"\n### 激励信息")

    motivational_messages = [
        "记住，持续的小胜利最终会累积成显著的成功。",
        "交易是一场马拉松，而非短跑，保持耐心和纪律。",
        "市场永远存在，不要急于每一次机会，等待最佳的设置。",
        "成功的交易者不是预测市场，而是适应市场。",
        "风险管理是交易成功的基石，永远保护你的资金。",
        "学习接受亏损是成为成功交易者的必经之路。",
        "专注于过程而非结果，好的交易习惯最终会带来好的结果。",
        "市场会给予耐心和纪律的交易者最丰厚的回报。",
        "每一笔交易都是学习的机会，无论盈亏。",
        "保持简单，复杂的策略往往不如简单有效的策略。"
    ]

    # 随机选择一条激励信息
    feedback.append(random.choice(motivational_messages))

    return "\n".join(feedback)


def format_performance_data_for_prompt():
    """
    格式化绩效数据，用于LLM提示词

    Returns:
        str: 格式化后的绩效数据
    """
    # 获取绩效摘要
    summary = get_performance_summary()

    # 账户摘要
    account_summary = summary["account_summary"]

    # 周期绩效
    period_performance = summary["period_performance"]

    # 最近交易
    recent_trades = summary["recent_trades"]

    # 绩效指标
    metrics = summary["performance_metrics"]

    # 格式化绩效数据
    formatted_data = []

    # 添加账户摘要
    formatted_data.append("### 账户状态")
    formatted_data.append(f"初始资金: ${account_summary['initial_balance']:.2f}")
    formatted_data.append(f"当前余额: ${account_summary['current_balance']:.2f}")
    formatted_data.append(f"总收益率: {account_summary['total_return']:.2f}%")
    formatted_data.append(f"胜率: {account_summary['win_rate']:.2f}%")

    # 添加周期绩效
    formatted_data.append("\n### 周期绩效")
    formatted_data.append(f"今日: {period_performance['today']['return']:.2f}%")
    formatted_data.append(f"本周: {period_performance['week']['return']:.2f}%")
    formatted_data.append(f"本月: {period_performance['month']['return']:.2f}%")

    # 添加订单结果统计
    formatted_data.append("\n### 订单结果统计")

    # 获取订单结果统计
    order_stats = get_order_result_statistics()

    total_count = order_stats.get('total_count', 0)

    if total_count > 0:
        take_profit_rate = order_stats.get('take_profit_rate', 0)
        stop_loss_rate = order_stats.get('stop_loss_rate', 0)
        avg_risk_reward = order_stats.get('avg_risk_reward_ratio', 0)

        formatted_data.append(f"止盈率: {take_profit_rate:.1f}%")
        formatted_data.append(f"止损率: {stop_loss_rate:.1f}%")
        formatted_data.append(f"风险回报比: {avg_risk_reward:.2f}")
    else:
        formatted_data.append("暂无订单结果统计数据")

    # 添加最近交易
    if recent_trades:
        formatted_data.append("\n### 最近交易")

        for trade in recent_trades[:3]:  # 只显示最近3笔交易
            result = "盈利" if trade["result"] == TradeResult.WIN else "亏损" if trade["result"] == TradeResult.LOSS else "持平"
            direction = "买入" if trade["direction"] == "BUY" else "卖出"

            formatted_data.append(f"{trade['symbol']} {direction}: {trade['profit_loss']:.2f}美元 ({trade['profit_loss_pips']:.1f}点) - {result}")

    # 添加绩效目标
    formatted_data.append("\n### 绩效目标")

    # 设置合理的目标
    daily_target = 0.3
    weekly_target = 1.5
    monthly_target = 5.0

    # 根据当前绩效调整目标
    if account_summary['total_return'] > 20:
        daily_target = 0.4
        weekly_target = 2.0
        monthly_target = 7.0
    elif account_summary['total_return'] < 0:
        daily_target = 0.2
        weekly_target = 1.0
        monthly_target = 3.0

    # 计算目标进度
    today_return = period_performance["today"]["return"]
    week_return = period_performance["week"]["return"]
    month_return = period_performance["month"]["return"]

    today_progress = (today_return / daily_target) * 100 if daily_target > 0 else 0
    week_progress = (week_return / weekly_target) * 100 if weekly_target > 0 else 0
    month_progress = (month_return / monthly_target) * 100 if monthly_target > 0 else 0

    formatted_data.append(f"日目标: {daily_target:.1f}% (进度: {today_progress:.0f}%)")
    formatted_data.append(f"周目标: {weekly_target:.1f}% (进度: {week_progress:.0f}%)")
    formatted_data.append(f"月目标: {monthly_target:.1f}% (进度: {month_progress:.0f}%)")

    return "\n".join(formatted_data)


def get_performance_reward_message():
    """
    获取绩效奖励信息

    Returns:
        str: 绩效奖励信息
    """
    # 获取绩效摘要
    summary = get_performance_summary()

    # 账户摘要
    account_summary = summary["account_summary"]

    # 周期绩效
    period_performance = summary["period_performance"]

    # 绩效指标
    metrics = summary["performance_metrics"]

    # 根据绩效生成奖励信息
    reward_messages = []

    # 总收益率奖励
    total_return = account_summary["total_return"]
    if total_return >= 20:
        reward_messages.append("🏆 **杰出表现**：你的总收益率超过20%，展现了卓越的交易能力！")
    elif total_return >= 10:
        reward_messages.append("🥇 **优秀表现**：你的总收益率超过10%，表现出色！")
    elif total_return >= 5:
        reward_messages.append("🥈 **良好表现**：你的总收益率超过5%，继续保持！")
    elif total_return > 0:
        reward_messages.append("🥉 **稳健表现**：你的账户保持盈利，继续努力！")

    # 胜率奖励
    win_rate = metrics["win_rate"]
    if win_rate >= 70:
        reward_messages.append("🎯 **精准交易**：你的胜率超过70%，展现了出色的交易判断力！")
    elif win_rate >= 60:
        reward_messages.append("👍 **高胜率**：你的胜率超过60%，交易决策质量很高！")

    # 盈亏比奖励
    profit_factor = metrics["profit_factor"]
    if profit_factor >= 3:
        reward_messages.append("💰 **卓越盈亏比**：你的盈亏比超过3，展现了出色的风险回报管理能力！")
    elif profit_factor >= 2:
        reward_messages.append("💵 **优秀盈亏比**：你的盈亏比超过2，风险管理表现出色！")

    # 连胜奖励
    consecutive_wins = metrics["consecutive_wins"]
    if consecutive_wins >= 5:
        reward_messages.append(f"🔥 **连胜记录**：你当前已经连续盈利{consecutive_wins}笔交易，保持这种势头！")
    elif consecutive_wins >= 3:
        reward_messages.append(f"📈 **连胜**：你当前已经连续盈利{consecutive_wins}笔交易，继续保持！")

    # 周期绩效奖励
    today_return = period_performance["today"]["return"]
    week_return = period_performance["week"]["return"]
    month_return = period_performance["month"]["return"]

    if today_return >= 1:
        reward_messages.append(f"📅 **今日佳绩**：今日收益率达到{today_return:.2f}%，表现优秀！")

    if week_return >= 3:
        reward_messages.append(f"📆 **本周佳绩**：本周收益率达到{week_return:.2f}%，表现出色！")

    if month_return >= 10:
        reward_messages.append(f"📊 **本月佳绩**：本月收益率达到{month_return:.2f}%，表现卓越！")

    # 添加订单结果奖励
    order_stats = get_order_result_statistics()
    total_count = order_stats.get('total_count', 0)

    if total_count >= 10:  # 只有当有足够的样本时才评价
        take_profit_rate = order_stats.get('take_profit_rate', 0)
        stop_loss_rate = order_stats.get('stop_loss_rate', 0)
        avg_risk_reward = order_stats.get('avg_risk_reward_ratio', 0)

        if take_profit_rate >= 60:
            reward_messages.append(f"🎯 **高止盈率**：你的止盈触发率达到{take_profit_rate:.1f}%，说明你善于捕捉趋势并设置合理的止盈点！")

        if stop_loss_rate <= 20 and total_count >= 20:
            reward_messages.append(f"🛡️ **低止损率**：你的止损触发率仅为{stop_loss_rate:.1f}%，展现了出色的入场时机选择能力！")

        if avg_risk_reward >= 2:
            reward_messages.append(f"⚖️ **优秀风险回报比**：你的平均风险回报比达到{avg_risk_reward:.2f}，展现了出色的风险管理能力！")

    # 如果没有任何奖励信息，添加一个默认信息
    if not reward_messages:
        reward_messages.append("💪 **持续努力**：交易是一场马拉松，保持耐心和纪律，持续改进你的策略！")

    # 随机选择1-2条奖励信息
    selected_messages = random.sample(reward_messages, min(2, len(reward_messages)))

    return "\n".join(selected_messages)
