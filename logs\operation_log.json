[{"timestamp": "2025-05-28T12:33:51.786686", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "9eb3c6e8-43b3-473a-9aed-497c4f976bf8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.41, "ask": 144.436, "point": 0.001, "digits": 3, "spread": 26.0, "time": "2025.05.28 07:33:52"}}, "error": null}, {"timestamp": "2025-05-28T12:34:42.122620", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:34:42.583500", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:34:42.621407", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "229a7171-08f8-49ba-849c-4d60ef8a9df6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:34:44.467135", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "229a7171-08f8-49ba-849c-4d60ef8a9df6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.3475, "ask": 1.34777, "point": 1e-05, "digits": 5, "spread": 27.0, "time": "2025.05.28 07:34:45"}}, "error": null}, {"timestamp": "2025-05-28T12:34:44.513170", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:34:45.189680", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:34:45.222280", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "5ae409e0-efa3-4429-97f9-f27c9b867810", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:34:45.278900", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "5ae409e0-efa3-4429-97f9-f27c9b867810", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64288, "ask": 0.64312, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 07:34:45"}}, "error": null}, {"timestamp": "2025-05-28T12:34:45.329743", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:34:53.775086", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:34:53.836115", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "be4408c9-5850-43d8-8b6a-bb72281ff5c8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:34:54.157283", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "be4408c9-5850-43d8-8b6a-bb72281ff5c8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.5958, "ask": 0.59608, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:34:54"}}, "error": null}, {"timestamp": "2025-05-28T12:34:54.200814", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:34:58.038380", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:34:58.092802", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "5c787711-7ec8-4908-86f5-9a8f750260aa", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:34:58.336892", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "5c787711-7ec8-4908-86f5-9a8f750260aa", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82811, "ask": 0.82839, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:34:58"}}, "error": null}, {"timestamp": "2025-05-28T12:34:58.376557", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:34:59.366681", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:34:59.414944", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "986fbbef-e67f-4b64-9cbf-731045ab2c55", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:01.065009", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "986fbbef-e67f-4b64-9cbf-731045ab2c55", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38305, "ask": 1.38335, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 07:35:01"}}, "error": null}, {"timestamp": "2025-05-28T12:35:01.129561", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:05.597321", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:35:05.650140", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "4fa9b768-832d-42ee-9278-ea0ffe6972a9", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:06.035368", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "4fa9b768-832d-42ee-9278-ea0ffe6972a9", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.398, "ask": 144.423, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 07:35:06"}}, "error": null}, {"timestamp": "2025-05-28T12:35:51.510345", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:52.223341", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:35:52.260620", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "4e201d9f-2cbb-4418-b8ef-0c3ef084ce0a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:52.400777", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "4e201d9f-2cbb-4418-b8ef-0c3ef084ce0a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34744, "ask": 1.34771, "point": 1e-05, "digits": 5, "spread": 27.0, "time": "2025.05.28 07:35:52"}}, "error": null}, {"timestamp": "2025-05-28T12:35:52.460168", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:53.746922", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:35:53.799787", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "69b30413-9ff9-4ed3-8096-3d0058f42765", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:54.281183", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "69b30413-9ff9-4ed3-8096-3d0058f42765", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64287, "ask": 0.6431, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:35:54"}}, "error": null}, {"timestamp": "2025-05-28T12:35:54.341511", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:55.708406", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:35:55.741349", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "57f4442c-bc3a-4f25-9d45-22d8586190ae", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:55.849183", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "57f4442c-bc3a-4f25-9d45-22d8586190ae", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59577, "ask": 0.59605, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:35:56"}}, "error": null}, {"timestamp": "2025-05-28T12:35:55.891373", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:55.971872", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:35:56.015187", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "528329c5-cec3-4879-8037-d3e9f66bdc08", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:56.602749", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "528329c5-cec3-4879-8037-d3e9f66bdc08", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82809, "ask": 0.82839, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 07:35:57"}}, "error": null}, {"timestamp": "2025-05-28T12:35:56.697489", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:58.592816", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:35:58.626321", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "26f0c5ae-d4e7-4a69-aa8e-34b9f6947401", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:35:59.442782", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "26f0c5ae-d4e7-4a69-aa8e-34b9f6947401", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38308, "ask": 1.38336, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:35:59"}}, "error": null}, {"timestamp": "2025-05-28T12:35:59.507994", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:36:00.057555", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:36:00.090480", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "0ff968c3-a125-4b9d-8f33-d6b5c9897ab3", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:36:00.211085", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "0ff968c3-a125-4b9d-8f33-d6b5c9897ab3", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.393, "ask": 144.415, "point": 0.001, "digits": 3, "spread": 22.0, "time": "2025.05.28 07:36:00"}}, "error": null}, {"timestamp": "2025-05-28T12:36:58.560416", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:36:59.434649", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:36:59.467492", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "14d1955d-ae6d-448b-8cc9-6330fa88faa1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:36:59.637494", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "14d1955d-ae6d-448b-8cc9-6330fa88faa1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34764, "ask": 1.34787, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:37:00"}}, "error": null}, {"timestamp": "2025-05-28T12:36:59.710785", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:36:59.834469", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:36:59.875319", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "338da433-8b5d-4534-bbbd-edc8b6c785b8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:37:00.894425", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "338da433-8b5d-4534-bbbd-edc8b6c785b8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64294, "ask": 0.64317, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:37:01"}}, "error": null}, {"timestamp": "2025-05-28T12:37:00.978948", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:37:01.076521", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:37:01.115269", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "76c7e971-38bc-4a7a-957f-f721e05c4e90", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:37:03.195505", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "76c7e971-38bc-4a7a-957f-f721e05c4e90", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59578, "ask": 0.59606, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:37:03"}}, "error": null}, {"timestamp": "2025-05-28T12:37:03.279276", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:37:09.020740", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:37:09.090265", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "ceffe24a-5f5e-48b8-8b41-d234b7d2991a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:37:09.377396", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "ceffe24a-5f5e-48b8-8b41-d234b7d2991a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82804, "ask": 0.82832, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:37:09"}}, "error": null}, {"timestamp": "2025-05-28T12:37:09.421704", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:37:10.408374", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:37:10.445098", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "3028cc95-1d7b-463d-9a3c-fa0ab1247488", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:37:10.528890", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "3028cc95-1d7b-463d-9a3c-fa0ab1247488", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38284, "ask": 1.38316, "point": 1e-05, "digits": 5, "spread": 32.0, "time": "2025.05.28 07:37:11"}}, "error": null}, {"timestamp": "2025-05-28T12:37:10.568499", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:37:10.826894", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:37:10.863417", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "29bb8c8c-736d-440c-bfd2-ea9eb143ee9a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:37:15.480615", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "29bb8c8c-736d-440c-bfd2-ea9eb143ee9a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.354, "ask": 144.385, "point": 0.001, "digits": 3, "spread": 31.0, "time": "2025.05.28 07:37:16"}}, "error": null}, {"timestamp": "2025-05-28T12:38:03.382900", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:38:03.735868", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:38:03.792568", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "70f9b94d-c14c-491c-a4e2-eea2a0618f3e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:38:04.480340", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "70f9b94d-c14c-491c-a4e2-eea2a0618f3e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34764, "ask": 1.34786, "point": 1e-05, "digits": 5, "spread": 22.0, "time": "2025.05.28 07:38:05"}}, "error": null}, {"timestamp": "2025-05-28T12:38:04.540621", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:38:05.219240", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:38:05.264368", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "293c889f-bf07-46c3-85fe-e42e5cb7c6e3", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:38:05.398614", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "293c889f-bf07-46c3-85fe-e42e5cb7c6e3", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.6429, "ask": 0.64313, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:38:05"}}, "error": null}, {"timestamp": "2025-05-28T12:38:05.443957", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:38:05.710260", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:38:05.743060", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "123221ef-f244-46d5-a051-089573da227f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:38:05.802696", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "123221ef-f244-46d5-a051-089573da227f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59569, "ask": 0.59597, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:38:06"}}, "error": null}, {"timestamp": "2025-05-28T12:38:05.853424", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:38:06.647503", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:38:06.722554", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "c9951618-db6e-4db3-b0c3-91be102869d2", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:38:06.906008", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "c9951618-db6e-4db3-b0c3-91be102869d2", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.8281, "ask": 0.82837, "point": 1e-05, "digits": 5, "spread": 27.0, "time": "2025.05.28 07:38:07"}}, "error": null}, {"timestamp": "2025-05-28T12:38:06.953505", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:38:07.776421", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:38:07.815887", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "2d9bd70d-b86c-47ff-9c41-77bd66c89008", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:38:07.929328", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "2d9bd70d-b86c-47ff-9c41-77bd66c89008", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38288, "ask": 1.3832, "point": 1e-05, "digits": 5, "spread": 32.0, "time": "2025.05.28 07:38:08"}}, "error": null}, {"timestamp": "2025-05-28T12:38:07.974595", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:38:08.663865", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:38:08.716600", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "cb1ae778-8b53-49d8-951d-c06ac8c597b6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:38:11.230913", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "cb1ae778-8b53-49d8-951d-c06ac8c597b6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.354, "ask": 144.383, "point": 0.001, "digits": 3, "spread": 29.0, "time": "2025.05.28 07:38:11"}}, "error": null}, {"timestamp": "2025-05-28T12:39:06.006494", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:39:07.584802", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:39:07.620380", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "df9ce7dc-3234-4e9a-ba52-81d25d18589d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:39:07.673983", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "df9ce7dc-3234-4e9a-ba52-81d25d18589d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34758, "ask": 1.34779, "point": 1e-05, "digits": 5, "spread": 21.0, "time": "2025.05.28 07:39:08"}}, "error": null}, {"timestamp": "2025-05-28T12:39:07.721260", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:39:08.179269", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:39:08.215490", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "52c9166d-300c-48a0-a26d-e65e7de47af0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:39:08.366284", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "52c9166d-300c-48a0-a26d-e65e7de47af0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64285, "ask": 0.64309, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 07:39:08"}}, "error": null}, {"timestamp": "2025-05-28T12:39:08.425963", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:39:08.800785", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:39:08.840434", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "144f0f8f-3b71-4e42-9cd9-a17382f98f3e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:39:15.240365", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "144f0f8f-3b71-4e42-9cd9-a17382f98f3e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59556, "ask": 0.59584, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:39:15"}}, "error": null}, {"timestamp": "2025-05-28T12:39:15.291691", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:39:19.291102", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:39:19.324599", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "34e428ac-ecac-4539-891c-fa543c827850", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:39:19.384772", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "34e428ac-ecac-4539-891c-fa543c827850", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82809, "ask": 0.82838, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 07:39:19"}}, "error": null}, {"timestamp": "2025-05-28T12:39:19.450666", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:39:19.540094", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:39:19.583349", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "48a08323-5d28-4ec3-a873-1f9524a85401", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:39:19.660776", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "48a08323-5d28-4ec3-a873-1f9524a85401", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38298, "ask": 1.3833, "point": 1e-05, "digits": 5, "spread": 32.0, "time": "2025.05.28 07:39:20"}}, "error": null}, {"timestamp": "2025-05-28T12:39:19.710429", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:39:19.954188", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:39:19.986908", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "51374724-a52e-41aa-a791-26b5b7388fb1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:39:20.547040", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "51374724-a52e-41aa-a791-26b5b7388fb1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.343, "ask": 144.367, "point": 0.001, "digits": 3, "spread": 24.0, "time": "2025.05.28 07:39:21"}}, "error": null}, {"timestamp": "2025-05-28T12:40:14.872548", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:40:20.260443", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:40:20.318500", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "b594277d-5a47-4e77-98b9-4d58d61d7620", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:40:21.447324", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "b594277d-5a47-4e77-98b9-4d58d61d7620", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34746, "ask": 1.34768, "point": 1e-05, "digits": 5, "spread": 22.0, "time": "2025.05.28 07:40:21"}}, "error": null}, {"timestamp": "2025-05-28T12:40:21.519857", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:40:22.371100", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:40:22.439173", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "80eaf685-cec5-4581-887b-1508c7eea02a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:40:22.568734", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "80eaf685-cec5-4581-887b-1508c7eea02a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64274, "ask": 0.64297, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:40:23"}}, "error": null}, {"timestamp": "2025-05-28T12:40:22.610488", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:40:23.284770", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:40:23.316930", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "98a37b6a-b3af-4b7c-90c3-b75c9c05c30a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:40:24.203521", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "98a37b6a-b3af-4b7c-90c3-b75c9c05c30a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59544, "ask": 0.59572, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:40:24"}}, "error": null}, {"timestamp": "2025-05-28T12:40:24.276991", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:40:26.037215", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:40:26.070853", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "0b26b42f-1c52-4330-884e-472cff1c23b0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:40:30.560060", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "0b26b42f-1c52-4330-884e-472cff1c23b0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82816, "ask": 0.82846, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 07:40:31"}}, "error": null}, {"timestamp": "2025-05-28T12:40:30.625874", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:40:30.719795", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:40:30.764321", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "9c79d7ac-dbb4-4033-a432-1eff0327350e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:40:30.906092", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "9c79d7ac-dbb4-4033-a432-1eff0327350e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38304, "ask": 1.38333, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 07:40:31"}}, "error": null}, {"timestamp": "2025-05-28T12:40:30.947656", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:40:33.974772", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:40:34.013561", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "5257e156-b6d2-42bb-b294-2ec622062fce", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:40:34.104127", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "5257e156-b6d2-42bb-b294-2ec622062fce", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.373, "ask": 144.398, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 07:40:34"}}, "error": null}, {"timestamp": "2025-05-28T12:41:18.388053", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:41:18.472685", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:41:18.519945", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "a78ac016-805c-4f5e-8b05-ccfcc7ac4960", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:41:21.283742", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "a78ac016-805c-4f5e-8b05-ccfcc7ac4960", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34741, "ask": 1.34767, "point": 1e-05, "digits": 5, "spread": 26.0, "time": "2025.05.28 07:41:21"}}, "error": null}, {"timestamp": "2025-05-28T12:41:21.332697", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:41:21.466965", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:41:21.507603", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "73e21cfe-560f-4d71-942e-0ffa4b425434", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:41:23.233522", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "73e21cfe-560f-4d71-942e-0ffa4b425434", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64269, "ask": 0.64292, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:41:23"}}, "error": null}, {"timestamp": "2025-05-28T12:41:23.273714", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:41:23.429516", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:41:23.462833", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "85a0fc8b-b2f7-4046-b09a-c9833b4a158f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:41:24.847193", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "85a0fc8b-b2f7-4046-b09a-c9833b4a158f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59533, "ask": 0.59561, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:41:25"}}, "error": null}, {"timestamp": "2025-05-28T12:41:24.907258", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:41:27.839074", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:41:27.884915", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "142ec3d7-c46f-432d-b488-72eeeefa4a93", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:41:29.255356", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "142ec3d7-c46f-432d-b488-72eeeefa4a93", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82818, "ask": 0.82847, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 07:41:29"}}, "error": null}, {"timestamp": "2025-05-28T12:41:29.349366", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:41:30.958659", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:41:30.990975", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "be04b981-6396-4374-b5b0-b535a59ff4ca", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:41:31.226845", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "be04b981-6396-4374-b5b0-b535a59ff4ca", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38312, "ask": 1.38339, "point": 1e-05, "digits": 5, "spread": 27.0, "time": "2025.05.28 07:41:31"}}, "error": null}, {"timestamp": "2025-05-28T12:41:31.280144", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:41:31.561450", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:41:31.596042", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "226c43e1-1ed1-47c7-b581-5a3d2461fe17", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:41:31.854787", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "226c43e1-1ed1-47c7-b581-5a3d2461fe17", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.379, "ask": 144.404, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 07:41:32"}}, "error": null}, {"timestamp": "2025-05-28T12:42:20.640649", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:42:22.637781", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:42:22.677200", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "08af4c3f-90be-4732-aaae-d69df0ca01ec", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:42:22.756003", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "08af4c3f-90be-4732-aaae-d69df0ca01ec", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34747, "ask": 1.34773, "point": 1e-05, "digits": 5, "spread": 26.0, "time": "2025.05.28 07:42:23"}}, "error": null}, {"timestamp": "2025-05-28T12:42:22.805630", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:42:22.987480", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:42:23.026796", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "f129af3e-832e-4489-a211-0f575219426b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:42:24.346499", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "f129af3e-832e-4489-a211-0f575219426b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64274, "ask": 0.64298, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 07:42:24"}}, "error": null}, {"timestamp": "2025-05-28T12:42:24.423639", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:42:24.477546", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:42:24.519305", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "b54b14d4-8a04-4149-99f5-e6698e3ca8ce", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:42:24.717701", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "b54b14d4-8a04-4149-99f5-e6698e3ca8ce", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59539, "ask": 0.59567, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:42:25"}}, "error": null}, {"timestamp": "2025-05-28T12:42:24.761658", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:42:25.812706", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:42:25.845984", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "451b2a6a-ea43-45fd-9291-44caf9cd1c3b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:42:25.945946", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "451b2a6a-ea43-45fd-9291-44caf9cd1c3b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82812, "ask": 0.82841, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 07:42:26"}}, "error": null}, {"timestamp": "2025-05-28T12:42:26.006100", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:42:26.910910", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:42:26.944544", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "3d9b28a7-4e87-4780-95ab-9476f055bff1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:42:27.907309", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "3d9b28a7-4e87-4780-95ab-9476f055bff1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38306, "ask": 1.38337, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 07:42:28"}}, "error": null}, {"timestamp": "2025-05-28T12:42:27.951674", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:42:28.076021", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:42:28.107636", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "d1ef1dc9-f4c9-42ce-bce7-50001cb225fc", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:42:28.245153", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "d1ef1dc9-f4c9-42ce-bce7-50001cb225fc", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.356, "ask": 144.381, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 07:42:28"}}, "error": null}, {"timestamp": "2025-05-28T12:43:26.245559", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:43:27.715444", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:43:27.762973", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "d8f1d5b7-c7a3-4718-9147-04045ecf5431", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:43:27.907483", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "d8f1d5b7-c7a3-4718-9147-04045ecf5431", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34757, "ask": 1.34778, "point": 1e-05, "digits": 5, "spread": 21.0, "time": "2025.05.28 07:43:28"}}, "error": null}, {"timestamp": "2025-05-28T12:43:27.960905", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:43:29.021557", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:43:29.072129", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "e7b500ce-81d5-496e-9514-fecadd873fbb", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:43:30.157612", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "e7b500ce-81d5-496e-9514-fecadd873fbb", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.6428, "ask": 0.64303, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:43:30"}}, "error": null}, {"timestamp": "2025-05-28T12:43:30.240757", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:43:33.989729", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:43:34.049476", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "82d0f6a7-a9e6-4f3c-981b-901964caff13", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:43:34.221563", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "82d0f6a7-a9e6-4f3c-981b-901964caff13", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59545, "ask": 0.59573, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:43:34"}}, "error": null}, {"timestamp": "2025-05-28T12:43:34.285105", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:43:35.236716", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:43:35.281839", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "a441e27a-5065-4fa0-af57-0165fb01e6b5", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:43:36.286015", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "a441e27a-5065-4fa0-af57-0165fb01e6b5", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82807, "ask": 0.82837, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 07:43:36"}}, "error": null}, {"timestamp": "2025-05-28T12:43:36.360961", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:43:36.499182", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:43:36.547762", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "eae8c3fd-21d1-4e20-b37c-7db758810578", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:43:36.652360", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "eae8c3fd-21d1-4e20-b37c-7db758810578", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38298, "ask": 1.38329, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 07:43:37"}}, "error": null}, {"timestamp": "2025-05-28T12:43:36.708334", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:43:36.806596", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:43:36.859855", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "40aafa65-582c-460b-a398-9805b85988c7", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:43:37.110950", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "40aafa65-582c-460b-a398-9805b85988c7", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.311, "ask": 144.336, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 07:43:37"}}, "error": null}, {"timestamp": "2025-05-28T12:44:30.352714", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:44:32.486865", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:44:32.565203", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "4d84080a-b15d-4d38-b5d0-07d98247f60d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:44:33.085584", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "4d84080a-b15d-4d38-b5d0-07d98247f60d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34755, "ask": 1.34778, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:44:33"}}, "error": null}, {"timestamp": "2025-05-28T12:44:33.140295", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:44:39.571788", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:44:39.632746", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "4a3bfe39-e24c-4e7f-b612-70f6700b928f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:44:41.117511", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "4a3bfe39-e24c-4e7f-b612-70f6700b928f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64274, "ask": 0.64297, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:44:41"}}, "error": null}, {"timestamp": "2025-05-28T12:44:41.172057", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:44:41.254789", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:44:41.298167", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "9e5ae38e-c5b4-44e5-a719-196bb341536f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:44:46.553296", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "9e5ae38e-c5b4-44e5-a719-196bb341536f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59538, "ask": 0.59566, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:44:47"}}, "error": null}, {"timestamp": "2025-05-28T12:44:46.605906", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:44:53.488907", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:44:53.562273", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "cde4fb15-2b1b-4b01-8c6e-b616397f5b60", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:44:54.160224", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "cde4fb15-2b1b-4b01-8c6e-b616397f5b60", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82806, "ask": 0.82836, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 07:44:54"}}, "error": null}, {"timestamp": "2025-05-28T12:44:54.212008", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:44:54.345322", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:44:54.388753", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "2ced1e59-9d68-4a20-a0e7-20fd29e9cfca", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:44:54.841163", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "2ced1e59-9d68-4a20-a0e7-20fd29e9cfca", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38302, "ask": 1.38332, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 07:44:55"}}, "error": null}, {"timestamp": "2025-05-28T12:44:54.902517", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:44:55.055622", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:44:55.098335", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "a0ca8b02-6803-493b-a97d-8a532fc693bc", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:44:55.888467", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "a0ca8b02-6803-493b-a97d-8a532fc693bc", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.317, "ask": 144.342, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 07:44:56"}}, "error": null}, {"timestamp": "2025-05-28T12:45:39.859973", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:45:41.809422", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:45:41.890972", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "3d3e3901-6658-4c24-a884-19474857135b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:45:42.947654", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "3d3e3901-6658-4c24-a884-19474857135b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34755, "ask": 1.34778, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:45:43"}}, "error": null}, {"timestamp": "2025-05-28T12:45:43.036565", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:45:45.665047", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:45:45.735854", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "a4bf9981-ff43-40b5-b903-807a4e5087b6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:45:46.018758", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "a4bf9981-ff43-40b5-b903-807a4e5087b6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64274, "ask": 0.64298, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 07:45:46"}}, "error": null}, {"timestamp": "2025-05-28T12:45:46.083536", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:45:46.240804", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:45:46.304328", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "3c73404c-2a9a-4201-9360-1cc7dbc1bd93", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:45:46.401357", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "3c73404c-2a9a-4201-9360-1cc7dbc1bd93", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.5954, "ask": 0.59568, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:45:46"}}, "error": null}, {"timestamp": "2025-05-28T12:45:46.479337", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:45:47.584150", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:45:47.652672", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "1a5f5547-4933-40da-a32b-3e4aa282d295", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:45:50.507347", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "1a5f5547-4933-40da-a32b-3e4aa282d295", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82802, "ask": 0.82833, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 07:45:51"}}, "error": null}, {"timestamp": "2025-05-28T12:45:50.601954", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:45:50.698309", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:45:50.757460", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "7f103396-83a8-4494-8d13-0c3f2be8c688", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:45:54.998523", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "7f103396-83a8-4494-8d13-0c3f2be8c688", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38296, "ask": 1.3833, "point": 1e-05, "digits": 5, "spread": 34.0, "time": "2025.05.28 07:45:55"}}, "error": null}, {"timestamp": "2025-05-28T12:45:55.075954", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:45:55.191628", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:45:55.235360", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "46ae06c9-ed22-4b6d-bfd2-f18d020fb32f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:45:58.264308", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "46ae06c9-ed22-4b6d-bfd2-f18d020fb32f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.32, "ask": 144.344, "point": 0.001, "digits": 3, "spread": 24.0, "time": "2025.05.28 07:45:58"}}, "error": null}, {"timestamp": "2025-05-28T12:46:42.639371", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:46:46.173629", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:46:46.245379", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "85a92abf-6242-4227-8737-37c59c3ef3cb", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:46:46.892863", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "85a92abf-6242-4227-8737-37c59c3ef3cb", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34755, "ask": 1.34777, "point": 1e-05, "digits": 5, "spread": 22.0, "time": "2025.05.28 07:46:47"}}, "error": null}, {"timestamp": "2025-05-28T12:46:46.944241", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:46:47.163803", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:46:47.207398", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "03ef9996-c28f-43c1-8da6-c32e5e1dae1f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:46:47.488637", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "03ef9996-c28f-43c1-8da6-c32e5e1dae1f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64277, "ask": 0.643, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:46:48"}}, "error": null}, {"timestamp": "2025-05-28T12:46:47.548932", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:46:49.543011", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:46:49.609261", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "cec23750-4038-45c4-bac9-141cd16b09a7", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:46:49.947459", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "cec23750-4038-45c4-bac9-141cd16b09a7", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59536, "ask": 0.59564, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:46:50"}}, "error": null}, {"timestamp": "2025-05-28T12:46:50.002502", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:46:51.480023", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:46:51.552514", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "b1fabab2-96e3-4abe-a872-7ff104a09c23", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:46:51.761172", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "b1fabab2-96e3-4abe-a872-7ff104a09c23", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82799, "ask": 0.82829, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 07:46:52"}}, "error": null}, {"timestamp": "2025-05-28T12:46:51.810343", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:46:54.163104", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:46:54.212638", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "d17aaaf1-1dee-4e06-be78-562fd3208780", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:46:55.305492", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "d17aaaf1-1dee-4e06-be78-562fd3208780", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38291, "ask": 1.38321, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 07:46:55"}}, "error": null}, {"timestamp": "2025-05-28T12:46:55.357828", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:46:55.600956", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:46:55.652176", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "6b1d2b51-82b6-4f4b-9cfa-26722ee155f8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:46:59.521906", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "6b1d2b51-82b6-4f4b-9cfa-26722ee155f8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.31, "ask": 144.333, "point": 0.001, "digits": 3, "spread": 23.0, "time": "2025.05.28 07:47:00"}}, "error": null}, {"timestamp": "2025-05-28T12:47:48.952473", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:47:49.950471", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:47:50.022521", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "f169bd65-8efa-4478-9844-541e357dc1be", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:47:50.457651", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "f169bd65-8efa-4478-9844-541e357dc1be", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34758, "ask": 1.34779, "point": 1e-05, "digits": 5, "spread": 21.0, "time": "2025.05.28 07:47:51"}}, "error": null}, {"timestamp": "2025-05-28T12:47:50.512967", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:47:51.428750", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:47:51.469987", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "65bd9e21-7f1d-4f3b-9b0a-6c6996a63f8a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:47:52.695050", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "65bd9e21-7f1d-4f3b-9b0a-6c6996a63f8a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64284, "ask": 0.64307, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:47:53"}}, "error": null}, {"timestamp": "2025-05-28T12:47:52.745808", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:47:54.091862", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:47:54.144801", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "f9a29e49-145f-4b0b-b8ae-5a7ed4aeee63", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:47:55.517632", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "f9a29e49-145f-4b0b-b8ae-5a7ed4aeee63", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.5955, "ask": 0.5958, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 07:47:56"}}, "error": null}, {"timestamp": "2025-05-28T12:47:55.578203", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:47:57.757491", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:47:57.800412", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "42c7e26c-8c6d-4f36-9466-d32df7509b97", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:48:02.103004", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "42c7e26c-8c6d-4f36-9466-d32df7509b97", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82803, "ask": 0.82835, "point": 1e-05, "digits": 5, "spread": 32.0, "time": "2025.05.28 07:48:02"}}, "error": null}, {"timestamp": "2025-05-28T12:48:02.155673", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:48:03.157502", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:48:03.199848", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "a9e6a7f0-a4c6-4b35-89d5-f186e6a93dc4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:48:04.538912", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "a9e6a7f0-a4c6-4b35-89d5-f186e6a93dc4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38294, "ask": 1.38326, "point": 1e-05, "digits": 5, "spread": 32.0, "time": "2025.05.28 07:48:05"}}, "error": null}, {"timestamp": "2025-05-28T12:48:04.599319", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:48:04.643189", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:48:04.687404", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "113e6489-407b-452b-a77a-4040ad39a50f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:48:05.577895", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "113e6489-407b-452b-a77a-4040ad39a50f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.33, "ask": 144.356, "point": 0.001, "digits": 3, "spread": 26.0, "time": "2025.05.28 07:48:06"}}, "error": null}, {"timestamp": "2025-05-28T12:48:52.639896", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:48:53.807576", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:48:53.875061", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "89f83007-df61-400e-a46b-cff6ef724e7d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:48:54.189359", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "89f83007-df61-400e-a46b-cff6ef724e7d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.3474, "ask": 1.34761, "point": 1e-05, "digits": 5, "spread": 21.0, "time": "2025.05.28 07:48:54"}}, "error": null}, {"timestamp": "2025-05-28T12:48:54.241889", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:48:54.470685", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:48:54.520141", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "18df2045-14b1-4202-9042-57d21abe1675", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:48:58.649513", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "18df2045-14b1-4202-9042-57d21abe1675", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64264, "ask": 0.64287, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:48:59"}}, "error": null}, {"timestamp": "2025-05-28T12:48:58.706768", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:48:58.924490", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:48:58.968360", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "ae17d4fa-4e4a-4281-865c-2608b8e1c10c", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:48:59.727901", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "ae17d4fa-4e4a-4281-865c-2608b8e1c10c", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59525, "ask": 0.59553, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:49:00"}}, "error": null}, {"timestamp": "2025-05-28T12:48:59.783462", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:49:03.253876", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:49:03.300624", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "28b3751d-a05e-4337-992f-b55c93f8ceaf", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:49:08.699383", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "28b3751d-a05e-4337-992f-b55c93f8ceaf", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.8281, "ask": 0.82841, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 07:49:09"}}, "error": null}, {"timestamp": "2025-05-28T12:49:08.778786", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:49:10.683128", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:49:10.730544", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "ccdd3168-7e8d-42af-9b36-14e83b5140f0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:49:11.325816", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "ccdd3168-7e8d-42af-9b36-14e83b5140f0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38301, "ask": 1.38332, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 07:49:11"}}, "error": null}, {"timestamp": "2025-05-28T12:49:11.395960", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:49:14.781679", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:49:14.825529", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "f62a7503-570f-4680-884c-b77d680e52c0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:49:15.205314", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "f62a7503-570f-4680-884c-b77d680e52c0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.339, "ask": 144.363, "point": 0.001, "digits": 3, "spread": 24.0, "time": "2025.05.28 07:49:15"}}, "error": null}, {"timestamp": "2025-05-28T12:50:02.101378", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:50:09.768897", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:50:09.832058", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "f55cd335-d8fa-4e70-a1bf-19c9786cb7c5", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:50:09.916884", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "f55cd335-d8fa-4e70-a1bf-19c9786cb7c5", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34721, "ask": 1.34749, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:50:10"}}, "error": null}, {"timestamp": "2025-05-28T12:50:09.967738", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:50:10.092279", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:50:10.152547", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "32da57e2-edb3-4833-9342-2b8d7f9f84e4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:50:10.572476", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "32da57e2-edb3-4833-9342-2b8d7f9f84e4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64264, "ask": 0.64287, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:50:11"}}, "error": null}, {"timestamp": "2025-05-28T12:50:10.652677", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:50:12.107974", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:50:12.173562", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "23eae2d5-9897-4ea0-a557-745984d7d64d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:50:12.468709", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "23eae2d5-9897-4ea0-a557-745984d7d64d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59519, "ask": 0.59547, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:50:13"}}, "error": null}, {"timestamp": "2025-05-28T12:50:12.521288", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:50:13.355738", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:50:13.406123", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "08ac9b5f-5f33-4695-a2ac-dceaf971b9e3", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:50:14.828378", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "08ac9b5f-5f33-4695-a2ac-dceaf971b9e3", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82818, "ask": 0.82844, "point": 1e-05, "digits": 5, "spread": 26.0, "time": "2025.05.28 07:50:15"}}, "error": null}, {"timestamp": "2025-05-28T12:50:14.905075", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:50:20.661756", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:50:20.707995", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "e0e37c46-8194-40d8-a86c-3d9b50d6d81d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:50:20.862032", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "e0e37c46-8194-40d8-a86c-3d9b50d6d81d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38305, "ask": 1.38336, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 07:50:21"}}, "error": null}, {"timestamp": "2025-05-28T12:50:20.915886", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:50:21.353635", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:50:21.415652", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "400882d3-57cc-4282-a978-75e01252c23a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:50:21.742998", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "400882d3-57cc-4282-a978-75e01252c23a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.348, "ask": 144.371, "point": 0.001, "digits": 3, "spread": 23.0, "time": "2025.05.28 07:50:22"}}, "error": null}, {"timestamp": "2025-05-28T12:51:06.273558", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:51:07.339912", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:51:07.383241", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "e44b1cb5-8d35-4863-993b-837017d57140", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:51:07.561599", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "e44b1cb5-8d35-4863-993b-837017d57140", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34722, "ask": 1.34747, "point": 1e-05, "digits": 5, "spread": 25.0, "time": "2025.05.28 07:51:08"}}, "error": null}, {"timestamp": "2025-05-28T12:51:07.626658", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:51:07.918601", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:51:08.005905", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "3f4d11a7-0c7c-41fe-9eef-0c0b781ac021", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:51:08.156191", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "3f4d11a7-0c7c-41fe-9eef-0c0b781ac021", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64263, "ask": 0.64286, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:51:08"}}, "error": null}, {"timestamp": "2025-05-28T12:51:08.260263", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:51:08.356292", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:51:08.401141", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "cb8d965a-c7ba-49e7-9596-a4f3e80a0173", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:51:08.480695", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "cb8d965a-c7ba-49e7-9596-a4f3e80a0173", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59517, "ask": 0.59545, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:51:09"}}, "error": null}, {"timestamp": "2025-05-28T12:51:08.533733", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:51:08.669702", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:51:08.713641", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "a3fb5731-9a12-4f5e-90a7-109a1bbc1ca4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:51:08.818411", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "a3fb5731-9a12-4f5e-90a7-109a1bbc1ca4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82823, "ask": 0.82849, "point": 1e-05, "digits": 5, "spread": 26.0, "time": "2025.05.28 07:51:09"}}, "error": null}, {"timestamp": "2025-05-28T12:51:08.872928", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:51:08.940398", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:51:08.982422", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "ea84f5b0-06cc-4dca-9e1d-b0b429d49bca", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:51:09.328997", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "ea84f5b0-06cc-4dca-9e1d-b0b429d49bca", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38311, "ask": 1.38339, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:51:09"}}, "error": null}, {"timestamp": "2025-05-28T12:51:09.394572", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:51:09.513026", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:51:09.556557", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "8d2b5bfa-8aa0-4a37-aa81-cb9cf04aebe7", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:51:14.596788", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "8d2b5bfa-8aa0-4a37-aa81-cb9cf04aebe7", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.364, "ask": 144.389, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 07:51:15"}}, "error": null}, {"timestamp": "2025-05-28T12:52:10.950092", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:52:12.537149", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:52:12.585715", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "402c4efe-5f0a-4f10-ac27-06e08a13ed38", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:52:12.749012", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "402c4efe-5f0a-4f10-ac27-06e08a13ed38", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34746, "ask": 1.34768, "point": 1e-05, "digits": 5, "spread": 22.0, "time": "2025.05.28 07:52:13"}}, "error": null}, {"timestamp": "2025-05-28T12:52:12.808225", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:52:13.857020", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:52:13.908114", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "67dd16f4-2d71-4475-8cdb-3a354389311a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:52:14.986546", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "67dd16f4-2d71-4475-8cdb-3a354389311a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.6428, "ask": 0.64303, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:52:15"}}, "error": null}, {"timestamp": "2025-05-28T12:52:15.058975", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:52:15.108273", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:52:15.152005", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "ffd4de63-6fb3-4dc9-abec-98a7fbbed8f2", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:52:16.325470", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "ffd4de63-6fb3-4dc9-abec-98a7fbbed8f2", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59531, "ask": 0.59559, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:52:16"}}, "error": null}, {"timestamp": "2025-05-28T12:52:16.379501", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:52:17.635876", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:52:17.681172", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "817b845b-f82f-4487-b358-81914cde732e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:52:24.938169", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "817b845b-f82f-4487-b358-81914cde732e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82807, "ask": 0.82839, "point": 1e-05, "digits": 5, "spread": 32.0, "time": "2025.05.28 07:52:25"}}, "error": null}, {"timestamp": "2025-05-28T12:52:24.997909", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:52:25.165629", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:52:25.215551", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "1073842d-8360-4578-baa6-98534ef42738", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:52:25.284270", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "1073842d-8360-4578-baa6-98534ef42738", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.3831, "ask": 1.38339, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 07:52:25"}}, "error": null}, {"timestamp": "2025-05-28T12:52:25.342173", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:52:26.358735", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:52:26.408991", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "6bc3b515-4e34-4cb8-8cb4-f9e1734002fd", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:52:29.291534", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "6bc3b515-4e34-4cb8-8cb4-f9e1734002fd", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.349, "ask": 144.375, "point": 0.001, "digits": 3, "spread": 26.0, "time": "2025.05.28 07:52:29"}}, "error": null}, {"timestamp": "2025-05-28T12:53:12.100068", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:53:13.109480", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:53:13.152122", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "3767ec1c-c0e5-4770-b852-63e4fb979d70", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:53:13.199228", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "3767ec1c-c0e5-4770-b852-63e4fb979d70", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34719, "ask": 1.34743, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 07:53:13"}}, "error": null}, {"timestamp": "2025-05-28T12:53:13.249041", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:53:16.669581", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:53:16.729504", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "e24a44f3-35d9-48e3-aa8e-7893cee0c7a5", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:53:16.837085", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "e24a44f3-35d9-48e3-aa8e-7893cee0c7a5", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64272, "ask": 0.64295, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:53:17"}}, "error": null}, {"timestamp": "2025-05-28T12:53:16.895817", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:53:17.070172", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:53:17.119634", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "d8509b5f-7369-4da0-b3b0-1dacb4f00c0f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:53:17.284082", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "d8509b5f-7369-4da0-b3b0-1dacb4f00c0f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59522, "ask": 0.5955, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:53:17"}}, "error": null}, {"timestamp": "2025-05-28T12:53:17.339394", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:53:18.451748", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:53:18.504880", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "160facbe-7162-43a4-8898-0022da116f21", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:53:18.668896", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "160facbe-7162-43a4-8898-0022da116f21", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82808, "ask": 0.82839, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 07:53:19"}}, "error": null}, {"timestamp": "2025-05-28T12:53:18.734490", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:53:18.917074", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:53:18.963399", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "88d8a7b0-b796-4939-8f2e-e914ca58b912", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:53:20.324763", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "88d8a7b0-b796-4939-8f2e-e914ca58b912", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38318, "ask": 1.38349, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 07:53:20"}}, "error": null}, {"timestamp": "2025-05-28T12:53:20.382468", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:53:20.477874", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:53:20.523602", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "37c3661d-5d6b-43f0-83b6-e590f77d45cd", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:53:20.588348", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "37c3661d-5d6b-43f0-83b6-e590f77d45cd", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.345, "ask": 144.37, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 07:53:21"}}, "error": null}, {"timestamp": "2025-05-28T12:54:16.785368", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:54:18.527497", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:54:18.585207", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "9e809e14-3fbd-4b55-b166-373baba11b87", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:54:19.003564", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "9e809e14-3fbd-4b55-b166-373baba11b87", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34716, "ask": 1.34742, "point": 1e-05, "digits": 5, "spread": 26.0, "time": "2025.05.28 07:54:19"}}, "error": null}, {"timestamp": "2025-05-28T12:54:19.059555", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:54:19.312924", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:54:19.359715", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "513d4977-cd0d-44d8-8b22-5df965ea2e76", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:54:19.606282", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "513d4977-cd0d-44d8-8b22-5df965ea2e76", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64263, "ask": 0.64286, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:54:20"}}, "error": null}, {"timestamp": "2025-05-28T12:54:19.657563", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:54:19.828429", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:54:19.882628", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "0fb3b9f8-92b4-4ee7-8f6a-2366390c76b9", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:54:20.092178", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "0fb3b9f8-92b4-4ee7-8f6a-2366390c76b9", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59507, "ask": 0.59535, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:54:20"}}, "error": null}, {"timestamp": "2025-05-28T12:54:20.178323", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:54:21.672389", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:54:21.719516", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "e0588b3e-c394-4eeb-805f-68a43531d76f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:54:21.795741", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "e0588b3e-c394-4eeb-805f-68a43531d76f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82816, "ask": 0.82846, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 07:54:22"}}, "error": null}, {"timestamp": "2025-05-28T12:54:21.851603", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:54:22.025558", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:54:22.069882", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "be1bf7f4-acd8-4647-90d0-b8ecd9058264", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:54:22.334992", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "be1bf7f4-acd8-4647-90d0-b8ecd9058264", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38325, "ask": 1.38355, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 07:54:22"}}, "error": null}, {"timestamp": "2025-05-28T12:54:22.394083", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:54:22.613290", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:54:22.656465", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "4efcece7-7c2f-416e-b2e4-8d80eba08b37", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:54:27.060698", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "4efcece7-7c2f-416e-b2e4-8d80eba08b37", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.373, "ask": 144.398, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 07:54:27"}}, "error": null}, {"timestamp": "2025-05-28T12:55:22.467977", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:55:22.672527", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:55:22.718872", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "0eb9ef45-2cc1-4289-b050-e3708acc8aa8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:55:22.894055", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "0eb9ef45-2cc1-4289-b050-e3708acc8aa8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34702, "ask": 1.34727, "point": 1e-05, "digits": 5, "spread": 25.0, "time": "2025.05.28 07:55:23"}}, "error": null}, {"timestamp": "2025-05-28T12:55:22.966612", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:55:24.574284", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:55:24.645045", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "72bd2289-bc73-4931-ad5d-92b193726da1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:55:25.364046", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "72bd2289-bc73-4931-ad5d-92b193726da1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64259, "ask": 0.64282, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 07:55:25"}}, "error": null}, {"timestamp": "2025-05-28T12:55:25.418627", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:55:25.607008", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:55:25.650629", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "66837d47-aad8-49f1-a872-4434bea9ced4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:55:26.108802", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "66837d47-aad8-49f1-a872-4434bea9ced4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.5949, "ask": 0.59518, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:55:26"}}, "error": null}, {"timestamp": "2025-05-28T12:55:26.170129", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:55:34.337011", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:55:34.406807", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "18cc01f2-2e7d-46ab-8c9b-364df0613750", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:55:36.302747", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "18cc01f2-2e7d-46ab-8c9b-364df0613750", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82819, "ask": 0.82847, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 07:55:36"}}, "error": null}, {"timestamp": "2025-05-28T12:55:36.365515", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:55:36.439180", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:55:36.480709", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "ccd24112-de75-4b97-9b51-46ad87d31821", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:55:37.857266", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "ccd24112-de75-4b97-9b51-46ad87d31821", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38327, "ask": 1.38357, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 07:55:38"}}, "error": null}, {"timestamp": "2025-05-28T12:55:37.953184", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:55:40.229470", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T12:55:40.272154", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "5f432b89-7bc8-403e-b729-8d8e87991ae2", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T12:55:41.291920", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "5f432b89-7bc8-403e-b729-8d8e87991ae2", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.392, "ask": 144.414, "point": 0.001, "digits": 3, "spread": 22.0, "time": "2025.05.28 07:55:41"}}, "error": null}, {"timestamp": "2025-05-28T12:56:25.878696", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:07.075414", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:07.573790", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:00:10.337497", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:10.836230", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:00:10.884103", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "67647b37-5617-427a-8653-4ca2a5005ad9", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:13.043912", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "67647b37-5617-427a-8653-4ca2a5005ad9", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34709, "ask": 1.34735, "point": 1e-05, "digits": 5, "spread": 26.0, "time": "2025.05.28 08:00:13"}}, "error": null}, {"timestamp": "2025-05-28T13:00:13.109772", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:13.196221", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:00:13.241137", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "e286beb7-bf1c-4709-9aab-7d59ea9c4662", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:13.362432", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "e286beb7-bf1c-4709-9aab-7d59ea9c4662", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64252, "ask": 0.64275, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:00:13"}}, "error": null}, {"timestamp": "2025-05-28T13:00:13.418298", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:14.185957", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:00:14.260712", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "a098bd7a-165a-48f8-89e6-ef414c71a7be", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:14.329561", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "a098bd7a-165a-48f8-89e6-ef414c71a7be", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59471, "ask": 0.59499, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:00:14"}}, "error": null}, {"timestamp": "2025-05-28T13:00:14.383412", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:15.184658", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:00:15.230572", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "7766e712-d6ef-470b-8319-bfddc26ad768", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:16.881763", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "7766e712-d6ef-470b-8319-bfddc26ad768", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82816, "ask": 0.82844, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:00:17"}}, "error": null}, {"timestamp": "2025-05-28T13:00:16.975934", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:20.185764", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:00:20.249596", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "2722bc79-7eb7-49e2-afe2-a33f47473b59", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:20.367325", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "2722bc79-7eb7-49e2-afe2-a33f47473b59", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38324, "ask": 1.38355, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 08:00:20"}}, "error": null}, {"timestamp": "2025-05-28T13:00:20.423332", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:20.488720", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:00:20.537555", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "ca08fbc1-c0e1-4127-b398-d55e167afbe9", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:00:20.922527", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "ca08fbc1-c0e1-4127-b398-d55e167afbe9", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.402, "ask": 144.425, "point": 0.001, "digits": 3, "spread": 23.0, "time": "2025.05.28 08:00:21"}}, "error": null}, {"timestamp": "2025-05-28T13:01:16.827901", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:01:18.600490", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:01:18.647364", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "8da8109d-1dbb-4f3f-a6af-1014defda6ec", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:01:18.708359", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "8da8109d-1dbb-4f3f-a6af-1014defda6ec", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34706, "ask": 1.34732, "point": 1e-05, "digits": 5, "spread": 26.0, "time": "2025.05.28 08:01:19"}}, "error": null}, {"timestamp": "2025-05-28T13:01:18.762263", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:01:20.263209", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:01:20.309096", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "18fc2b74-5908-4579-868e-52c0293ec746", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:01:20.930243", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "18fc2b74-5908-4579-868e-52c0293ec746", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64254, "ask": 0.64278, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 08:01:21"}}, "error": null}, {"timestamp": "2025-05-28T13:01:20.987133", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:01:21.461406", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:01:21.511274", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "2b5ea0e3-c62b-4ebc-8a46-df4a34909d94", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:01:21.658838", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "2b5ea0e3-c62b-4ebc-8a46-df4a34909d94", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59474, "ask": 0.59502, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:01:22"}}, "error": null}, {"timestamp": "2025-05-28T13:01:21.713720", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:01:22.218977", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:01:22.268868", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "6ef2fea7-4327-4112-976d-fb9497351bf8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:01:22.869269", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "6ef2fea7-4327-4112-976d-fb9497351bf8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82813, "ask": 0.82841, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:01:23"}}, "error": null}, {"timestamp": "2025-05-28T13:01:22.931225", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:01:23.524019", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:01:23.572923", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "907f66b2-5aea-4f96-aa19-0f63c0d5e00b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:01:25.381173", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "907f66b2-5aea-4f96-aa19-0f63c0d5e00b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38311, "ask": 1.38344, "point": 1e-05, "digits": 5, "spread": 33.0, "time": "2025.05.28 08:01:25"}}, "error": null}, {"timestamp": "2025-05-28T13:01:25.437023", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:01:26.439109", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:01:26.497953", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "affc35e6-fd88-4ced-b0ac-a95d29ff1cc9", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:01:27.637591", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "affc35e6-fd88-4ced-b0ac-a95d29ff1cc9", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.396, "ask": 144.421, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 08:01:28"}}, "error": null}, {"timestamp": "2025-05-28T13:02:22.361675", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:02:23.653264", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:02:23.720083", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "d1f9a907-a132-4a29-be06-8de654f438aa", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:02:23.955119", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "d1f9a907-a132-4a29-be06-8de654f438aa", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34711, "ask": 1.34738, "point": 1e-05, "digits": 5, "spread": 27.0, "time": "2025.05.28 08:02:24"}}, "error": null}, {"timestamp": "2025-05-28T13:02:24.012965", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:02:24.155576", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:02:24.204457", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "937e0857-abb5-4675-80b0-47d71c5fc176", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:02:24.513920", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "937e0857-abb5-4675-80b0-47d71c5fc176", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64258, "ask": 0.64281, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:02:25"}}, "error": null}, {"timestamp": "2025-05-28T13:02:24.576779", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:02:24.694413", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:02:24.743282", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "0ae5e2bd-6e34-4445-876b-ab088cdace30", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:02:25.743080", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "0ae5e2bd-6e34-4445-876b-ab088cdace30", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59474, "ask": 0.59502, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:02:26"}}, "error": null}, {"timestamp": "2025-05-28T13:02:25.821869", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:02:25.919178", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:02:25.964522", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "8602c759-5cfe-4be4-9a04-8127d65627b7", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:02:26.472614", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "8602c759-5cfe-4be4-9a04-8127d65627b7", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.8281, "ask": 0.82838, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:02:27"}}, "error": null}, {"timestamp": "2025-05-28T13:02:26.535492", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:02:30.398064", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:02:30.447960", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "c1491fd6-c60b-4dfb-a6e8-34c8bc1ebf0e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:02:30.567179", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "c1491fd6-c60b-4dfb-a6e8-34c8bc1ebf0e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.3832, "ask": 1.38349, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 08:02:31"}}, "error": null}, {"timestamp": "2025-05-28T13:02:30.628021", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:02:33.165830", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:02:33.216695", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "b2c63ed4-443f-4159-aafd-97cebb3b382b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:02:40.881115", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "b2c63ed4-443f-4159-aafd-97cebb3b382b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.4, "ask": 144.425, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 08:02:41"}}, "error": null}, {"timestamp": "2025-05-28T13:03:26.355630", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:03:30.672201", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:03:30.741050", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "6df64720-5191-47e4-bbcf-462f6af02ca6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:03:32.761005", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "6df64720-5191-47e4-bbcf-462f6af02ca6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34705, "ask": 1.3473, "point": 1e-05, "digits": 5, "spread": 25.0, "time": "2025.05.28 08:03:33"}}, "error": null}, {"timestamp": "2025-05-28T13:03:32.820845", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:03:32.875698", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:03:32.925565", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "17286f59-96c7-4957-9f65-ae924e71b08a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:03:37.565434", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "17286f59-96c7-4957-9f65-ae924e71b08a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64262, "ask": 0.64285, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:03:38"}}, "error": null}, {"timestamp": "2025-05-28T13:03:37.620934", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:03:45.645612", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:03:45.697477", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "3782fab4-fec7-4e19-be2d-ed66b1f01d8f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:03:46.417242", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "3782fab4-fec7-4e19-be2d-ed66b1f01d8f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59468, "ask": 0.59496, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:03:46"}}, "error": null}, {"timestamp": "2025-05-28T13:03:46.482071", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:03:47.669370", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:03:47.734200", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "c65f33a2-5804-4597-9750-2f6b701c1960", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:03:48.078566", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "c65f33a2-5804-4597-9750-2f6b701c1960", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82817, "ask": 0.82845, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:03:48"}}, "error": null}, {"timestamp": "2025-05-28T13:03:48.154952", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:03:48.280061", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:03:48.352862", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "2abe646a-fcd7-468f-8c69-f3b4fee43b7d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:03:48.500532", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "2abe646a-fcd7-468f-8c69-f3b4fee43b7d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38322, "ask": 1.38351, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 08:03:49"}}, "error": null}, {"timestamp": "2025-05-28T13:03:48.558391", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:03:48.705490", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:03:48.753368", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "7818a7c0-d4b2-49ea-8185-683095805b1e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:03:49.451106", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "7818a7c0-d4b2-49ea-8185-683095805b1e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.386, "ask": 144.41, "point": 0.001, "digits": 3, "spread": 24.0, "time": "2025.05.28 08:03:50"}}, "error": null}, {"timestamp": "2025-05-28T13:04:32.989667", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:04:35.225663", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:04:35.297459", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "a426e21d-1ecd-4125-933f-52f2fa432866", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:04:37.305241", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "a426e21d-1ecd-4125-933f-52f2fa432866", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34701, "ask": 1.34727, "point": 1e-05, "digits": 5, "spread": 26.0, "time": "2025.05.28 08:04:37"}}, "error": null}, {"timestamp": "2025-05-28T13:04:37.363089", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:04:38.147222", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:04:38.232991", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "dd325d85-d7fc-4db6-8713-c710d2454578", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:04:38.341662", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "dd325d85-d7fc-4db6-8713-c710d2454578", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64262, "ask": 0.64286, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 08:04:38"}}, "error": null}, {"timestamp": "2025-05-28T13:04:38.417145", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:04:39.833828", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:04:39.913625", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "0f865473-94ad-472c-991f-aae1c7b74ad0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:04:41.996752", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "0f865473-94ad-472c-991f-aae1c7b74ad0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59472, "ask": 0.595, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:04:42"}}, "error": null}, {"timestamp": "2025-05-28T13:04:42.082165", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:04:42.142621", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:04:42.188501", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "eef5d76b-a861-4e83-9341-186306e57851", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:04:46.984896", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "eef5d76b-a861-4e83-9341-186306e57851", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82817, "ask": 0.82846, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 08:04:47"}}, "error": null}, {"timestamp": "2025-05-28T13:04:47.062168", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:04:47.679834", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:04:47.724717", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "afb2d9e1-4fd5-4d0e-b7a8-02a50da12fdd", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:04:48.335437", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "afb2d9e1-4fd5-4d0e-b7a8-02a50da12fdd", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.3833, "ask": 1.38361, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 08:04:48"}}, "error": null}, {"timestamp": "2025-05-28T13:04:48.392314", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:04:50.639998", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:04:50.686873", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "a1cdd2ec-f52e-4b27-af79-b2cb0cb984c6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:04:50.757268", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "a1cdd2ec-f52e-4b27-af79-b2cb0cb984c6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.38, "ask": 144.405, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 08:04:51"}}, "error": null}, {"timestamp": "2025-05-28T13:05:35.435783", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:05:36.205143", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:05:36.255987", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "0b15df29-6952-4b89-bb6e-749cabcda8b1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:05:37.660443", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "0b15df29-6952-4b89-bb6e-749cabcda8b1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34692, "ask": 1.34717, "point": 1e-05, "digits": 5, "spread": 25.0, "time": "2025.05.28 08:05:38"}}, "error": null}, {"timestamp": "2025-05-28T13:05:37.724274", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:05:38.059885", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:05:38.107756", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "57e46d97-718f-42a1-b926-329d010ddeda", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:05:40.389575", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "57e46d97-718f-42a1-b926-329d010ddeda", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64262, "ask": 0.64285, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:05:40"}}, "error": null}, {"timestamp": "2025-05-28T13:05:40.458739", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:05:43.372445", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:05:43.454223", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "78709753-c0c3-4b14-ba1b-a509c0e0661b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:05:43.553378", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "78709753-c0c3-4b14-ba1b-a509c0e0661b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59483, "ask": 0.59511, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:05:44"}}, "error": null}, {"timestamp": "2025-05-28T13:05:43.628975", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:05:43.815208", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:05:43.862063", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "3efea656-25a9-4100-b493-8686f5dc9ac4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:05:44.068435", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "3efea656-25a9-4100-b493-8686f5dc9ac4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.8282, "ask": 0.82848, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:05:44"}}, "error": null}, {"timestamp": "2025-05-28T13:05:44.136280", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:05:45.652139", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:05:45.729955", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "7ac2795f-8978-4476-9fa4-b9712a5f65a6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:05:49.912639", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "7ac2795f-8978-4476-9fa4-b9712a5f65a6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38348, "ask": 1.38379, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 08:05:50"}}, "error": null}, {"timestamp": "2025-05-28T13:05:49.996721", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:05:52.439944", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:05:52.500807", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "e9a37166-b55c-482f-bae8-a30402ab06c4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:05:52.579010", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "e9a37166-b55c-482f-bae8-a30402ab06c4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.391, "ask": 144.414, "point": 0.001, "digits": 3, "spread": 23.0, "time": "2025.05.28 08:05:53"}}, "error": null}, {"timestamp": "2025-05-28T13:06:36.652204", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:06:38.243882", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:06:38.305709", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "0e0805e1-d1cb-4403-af28-0a8255569029", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:06:40.388906", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "0e0805e1-d1cb-4403-af28-0a8255569029", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34705, "ask": 1.34731, "point": 1e-05, "digits": 5, "spread": 26.0, "time": "2025.05.28 08:06:40"}}, "error": null}, {"timestamp": "2025-05-28T13:06:40.463349", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:06:41.262623", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:06:41.334426", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "b99642e3-e2b1-4144-b623-56c3ed8fda57", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:06:41.463004", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "b99642e3-e2b1-4144-b623-56c3ed8fda57", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64263, "ask": 0.64286, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:06:42"}}, "error": null}, {"timestamp": "2025-05-28T13:06:41.535556", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:06:42.857678", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:06:42.903555", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "a7c5f63d-5673-47d6-8a49-5eeec4fe0ad6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:06:45.314607", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "a7c5f63d-5673-47d6-8a49-5eeec4fe0ad6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59495, "ask": 0.59523, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:06:45"}}, "error": null}, {"timestamp": "2025-05-28T13:06:45.395418", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:06:52.053087", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:06:52.209667", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "e97b74d1-2e5c-4c4a-9547-63af93306b2b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:06:52.996185", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "e97b74d1-2e5c-4c4a-9547-63af93306b2b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82812, "ask": 0.8284, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:06:53"}}, "error": null}, {"timestamp": "2025-05-28T13:06:53.060214", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:06:53.710928", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:06:53.770769", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "7a0683bc-1d3b-45fd-b0b2-9a2982187fee", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:06:56.362249", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "7a0683bc-1d3b-45fd-b0b2-9a2982187fee", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38334, "ask": 1.3837, "point": 1e-05, "digits": 5, "spread": 36.0, "time": "2025.05.28 08:06:56"}}, "error": null}, {"timestamp": "2025-05-28T13:06:56.496595", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:06:59.508578", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:06:59.561435", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "190fd1cb-e4b6-46ca-84e6-64e3d38d7f9a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:00.742081", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "190fd1cb-e4b6-46ca-84e6-64e3d38d7f9a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.36, "ask": 144.384, "point": 0.001, "digits": 3, "spread": 24.0, "time": "2025.05.28 08:07:01"}}, "error": null}, {"timestamp": "2025-05-28T13:07:45.552682", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:46.038998", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:07:46.122773", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "08ebb18d-f3e2-46bd-b4c3-b8dbe1603984", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:47.742072", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "08ebb18d-f3e2-46bd-b4c3-b8dbe1603984", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34723, "ask": 1.34746, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:07:48"}}, "error": null}, {"timestamp": "2025-05-28T13:07:47.796956", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:47.911900", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:07:47.959736", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "f9cc561c-3c3e-4104-8977-54868f7f69fd", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:48.195995", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "f9cc561c-3c3e-4104-8977-54868f7f69fd", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64276, "ask": 0.64299, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:07:48"}}, "error": null}, {"timestamp": "2025-05-28T13:07:48.255835", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:49.859519", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:07:49.932748", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "35253910-e1ad-45fe-b937-fe27c50b15ac", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:50.090267", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "35253910-e1ad-45fe-b937-fe27c50b15ac", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59526, "ask": 0.59554, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:07:50"}}, "error": null}, {"timestamp": "2025-05-28T13:07:50.177035", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:50.534084", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:07:50.580993", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "1201942c-5454-48cf-b9f8-47120761a1d8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:50.877611", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "1201942c-5454-48cf-b9f8-47120761a1d8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82806, "ask": 0.82837, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 08:07:51"}}, "error": null}, {"timestamp": "2025-05-28T13:07:50.939446", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:52.167009", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:07:52.251781", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "ca5ca56e-8163-468b-b44b-f69d27adcb0b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:53.252261", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "ca5ca56e-8163-468b-b44b-f69d27adcb0b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38315, "ask": 1.38346, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 08:07:53"}}, "error": null}, {"timestamp": "2025-05-28T13:07:53.338326", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:53.993151", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:07:54.040027", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "19c1eeca-88ca-473d-9dfe-36e71de380aa", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:07:56.539665", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "19c1eeca-88ca-473d-9dfe-36e71de380aa", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.393, "ask": 144.418, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 08:07:57"}}, "error": null}, {"timestamp": "2025-05-28T13:08:54.310019", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:08:55.437263", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:08:55.507368", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "3a78f219-2dfe-4f99-bf30-273092569685", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:08:59.986557", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "3a78f219-2dfe-4f99-bf30-273092569685", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34708, "ask": 1.34734, "point": 1e-05, "digits": 5, "spread": 26.0, "time": "2025.05.28 08:09:00"}}, "error": null}, {"timestamp": "2025-05-28T13:09:00.051384", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:09:00.184412", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:09:00.240289", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "b7288463-d08e-46a0-acc0-0fe90adb85f3", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:09:00.645158", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "b7288463-d08e-46a0-acc0-0fe90adb85f3", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64267, "ask": 0.6429, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:09:01"}}, "error": null}, {"timestamp": "2025-05-28T13:09:00.709027", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:09:00.771819", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:09:00.823682", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "37856ad5-04b3-401c-a8f7-911741870816", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:09:04.262554", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "37856ad5-04b3-401c-a8f7-911741870816", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59464, "ask": 0.59493, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 08:09:04"}}, "error": null}, {"timestamp": "2025-05-28T13:09:04.320400", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:09:04.421355", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:09:04.466263", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "32da48b4-2f9b-464b-b25f-3296e16f2482", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:09:04.543459", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "32da48b4-2f9b-464b-b25f-3296e16f2482", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82811, "ask": 0.8284, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 08:09:05"}}, "error": null}, {"timestamp": "2025-05-28T13:09:04.614869", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:09:05.885006", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:09:05.945842", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "4a9b54da-4c12-4fe5-9f43-9e5d125f6d1a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:09:06.518552", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "4a9b54da-4c12-4fe5-9f43-9e5d125f6d1a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38331, "ask": 1.38365, "point": 1e-05, "digits": 5, "spread": 34.0, "time": "2025.05.28 08:09:07"}}, "error": null}, {"timestamp": "2025-05-28T13:09:06.572875", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:09:06.626839", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:09:06.678702", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "7fd918a1-5a73-4d14-9b40-d33e133a0aea", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:09:09.196054", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "7fd918a1-5a73-4d14-9b40-d33e133a0aea", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.401, "ask": 144.426, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 08:09:09"}}, "error": null}, {"timestamp": "2025-05-28T13:09:57.744696", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:09:57.862652", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:09:57.925484", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "86bab03c-f194-4861-99c0-400ccaeecd46", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:10:04.477776", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "86bab03c-f194-4861-99c0-400ccaeecd46", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34702, "ask": 1.34728, "point": 1e-05, "digits": 5, "spread": 26.0, "time": "2025.05.28 08:10:05"}}, "error": null}, {"timestamp": "2025-05-28T13:10:04.555594", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:10:04.808069", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:10:04.876883", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "e219a526-f2e6-4420-9901-c03c817f378e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:10:06.462577", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "e219a526-f2e6-4420-9901-c03c817f378e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64257, "ask": 0.6428, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:10:07"}}, "error": null}, {"timestamp": "2025-05-28T13:10:06.519427", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:10:07.753015", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:10:07.797898", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "1e79b563-e044-4706-bec9-7415dbd63b86", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:10:08.192769", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "1e79b563-e044-4706-bec9-7415dbd63b86", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59469, "ask": 0.59497, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:10:08"}}, "error": null}, {"timestamp": "2025-05-28T13:10:08.247583", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:10:08.366483", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:10:08.426321", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "189d259c-7fad-4609-8c6d-cc3a2f00c9d0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:10:08.513634", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "189d259c-7fad-4609-8c6d-cc3a2f00c9d0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82817, "ask": 0.82847, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 08:10:09"}}, "error": null}, {"timestamp": "2025-05-28T13:10:08.567490", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:10:08.621876", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:10:08.666737", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "6e92f9a3-ef19-4ac9-b7a5-f829ad894557", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:10:08.792601", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "6e92f9a3-ef19-4ac9-b7a5-f829ad894557", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38334, "ask": 1.38368, "point": 1e-05, "digits": 5, "spread": 34.0, "time": "2025.05.28 08:10:09"}}, "error": null}, {"timestamp": "2025-05-28T13:10:08.846530", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:10:09.039804", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:10:09.087678", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "de29d393-0c04-4453-b689-667309de4e74", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:10:09.196446", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "de29d393-0c04-4453-b689-667309de4e74", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.413, "ask": 144.438, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 08:10:09"}}, "error": null}, {"timestamp": "2025-05-28T13:11:02.773509", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:11:02.931246", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:11:02.980145", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "7d8b1de0-3a3c-407e-8029-3b9c612ad7b0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:11:03.149615", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "7d8b1de0-3a3c-407e-8029-3b9c612ad7b0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34716, "ask": 1.34741, "point": 1e-05, "digits": 5, "spread": 25.0, "time": "2025.05.28 08:11:03"}}, "error": null}, {"timestamp": "2025-05-28T13:11:03.208563", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:11:03.305407", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:11:03.349315", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "a17d3b90-fbb4-4cb4-a14e-5485ba6884bf", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:11:03.602506", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "a17d3b90-fbb4-4cb4-a14e-5485ba6884bf", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64269, "ask": 0.64292, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:11:04"}}, "error": null}, {"timestamp": "2025-05-28T13:11:03.682292", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:11:04.057356", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:11:04.131155", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "5fac6697-648c-451e-8c1e-d9cdc6c6cf3d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:11:04.328136", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "5fac6697-648c-451e-8c1e-d9cdc6c6cf3d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59478, "ask": 0.59506, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:11:04"}}, "error": null}, {"timestamp": "2025-05-28T13:11:04.388076", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:11:05.308166", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:11:05.360030", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "3426450c-8c0a-4c64-a2ce-7d11d0abbba6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:11:08.549301", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "3426450c-8c0a-4c64-a2ce-7d11d0abbba6", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82804, "ask": 0.82834, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 08:11:09"}}, "error": null}, {"timestamp": "2025-05-28T13:11:08.648016", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:11:08.755150", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:11:08.801026", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "b611700a-9d68-4922-8fe1-6df0f91934e1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:11:08.848089", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "b611700a-9d68-4922-8fe1-6df0f91934e1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38319, "ask": 1.38352, "point": 1e-05, "digits": 5, "spread": 33.0, "time": "2025.05.28 08:11:09"}}, "error": null}, {"timestamp": "2025-05-28T13:11:08.903949", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:11:09.372167", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:11:09.424027", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "5b322694-6a9f-413a-a0a0-71b90e0b34f0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:11:09.696029", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "5b322694-6a9f-413a-a0a0-71b90e0b34f0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.398, "ask": 144.424, "point": 0.001, "digits": 3, "spread": 26.0, "time": "2025.05.28 08:11:10"}}, "error": null}, {"timestamp": "2025-05-28T13:12:04.356604", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:04.408465", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:12:04.454368", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "816e4e03-ad29-4282-9267-7c08cf76eabb", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:06.306807", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "816e4e03-ad29-4282-9267-7c08cf76eabb", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34705, "ask": 1.34729, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 08:12:06"}}, "error": null}, {"timestamp": "2025-05-28T13:12:06.377611", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:06.457477", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:12:06.504354", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "69ed80a3-7b87-4432-a014-4f6bce8eee03", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:07.491783", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "69ed80a3-7b87-4432-a014-4f6bce8eee03", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.6426, "ask": 0.64283, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:12:08"}}, "error": null}, {"timestamp": "2025-05-28T13:12:07.550568", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:11.279678", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:12:11.331539", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "77bf2b4e-e761-4a52-a005-10dcb11239ec", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:11.397496", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "77bf2b4e-e761-4a52-a005-10dcb11239ec", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59461, "ask": 0.5949, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 08:12:11"}}, "error": null}, {"timestamp": "2025-05-28T13:12:11.452367", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:11.705822", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:12:11.760675", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "32c03fb8-2252-4f3b-8a6f-c64b7e881904", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:17.783858", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "32c03fb8-2252-4f3b-8a6f-c64b7e881904", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82811, "ask": 0.8284, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 08:12:18"}}, "error": null}, {"timestamp": "2025-05-28T13:12:17.846716", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:19.580195", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:12:19.630086", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "e404e293-a263-4134-a324-be36c5fe3a47", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:19.849584", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "e404e293-a263-4134-a324-be36c5fe3a47", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38337, "ask": 1.38371, "point": 1e-05, "digits": 5, "spread": 34.0, "time": "2025.05.28 08:12:20"}}, "error": null}, {"timestamp": "2025-05-28T13:12:19.905434", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:19.997347", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:12:20.042256", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "de5a9a83-092e-48cc-9380-fcfd77ae59f0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:20.538914", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "de5a9a83-092e-48cc-9380-fcfd77ae59f0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.392, "ask": 144.417, "point": 0.001, "digits": 3, "spread": 25.0, "time": "2025.05.28 08:12:21"}}, "error": null}, {"timestamp": "2025-05-28T13:12:58.462499", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:12:59.497261", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:13:02.073569", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:02.226821", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:13:02.296627", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "d6907b43-286a-4a63-8a0e-4b3eb9af28f4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:03.265386", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "d6907b43-286a-4a63-8a0e-4b3eb9af28f4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34692, "ask": 1.34716, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 08:13:03"}}, "error": null}, {"timestamp": "2025-05-28T13:13:03.325237", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:05.181291", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:13:05.266073", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "0741062f-32ed-4d47-bf03-769ec0424977", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:05.339089", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "0741062f-32ed-4d47-bf03-769ec0424977", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64251, "ask": 0.64274, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:13:05"}}, "error": null}, {"timestamp": "2025-05-28T13:13:05.433835", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:07.290479", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:13:07.339361", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "9381ae17-d4f7-41d6-9559-308408fa1238", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:07.443576", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "9381ae17-d4f7-41d6-9559-308408fa1238", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59436, "ask": 0.59464, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:13:08"}}, "error": null}, {"timestamp": "2025-05-28T13:13:07.521433", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:08.380618", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:13:08.428490", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "332c88bf-f7bd-4fd8-8430-6bd05f6e4993", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:09.462061", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "332c88bf-f7bd-4fd8-8430-6bd05f6e4993", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82813, "ask": 0.82842, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 08:13:10"}}, "error": null}, {"timestamp": "2025-05-28T13:13:09.516942", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:12.477126", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:13:12.543951", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "0793344e-70cb-489e-977a-75017674d0c0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:16.474059", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "0793344e-70cb-489e-977a-75017674d0c0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38333, "ask": 1.38366, "point": 1e-05, "digits": 5, "spread": 33.0, "time": "2025.05.28 08:13:17"}}, "error": null}, {"timestamp": "2025-05-28T13:13:16.530039", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:19.106749", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:13:19.181574", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "cea99047-0d01-4095-bb7a-0ac6687958e1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:22.111232", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "cea99047-0d01-4095-bb7a-0ac6687958e1", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.402, "ask": 144.428, "point": 0.001, "digits": 3, "spread": 26.0, "time": "2025.05.28 08:13:22"}}, "error": null}, {"timestamp": "2025-05-28T13:13:27.625670", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "EURUSD", "requestId": "c3fe6b23-47d9-410f-b8c3-e121551b0287", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:27.716723", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "EURUSD", "requestId": "c3fe6b23-47d9-410f-b8c3-e121551b0287", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.13021, "ask": 1.13039, "point": 1e-05, "digits": 5, "spread": 18.0, "time": "2025.05.28 08:13:28"}}, "error": null}, {"timestamp": "2025-05-28T13:13:27.763622", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "0b7266f7-7278-468e-b948-94844888bdfc", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:28.211348", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "0b7266f7-7278-468e-b948-94844888bdfc", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38334, "ask": 1.38367, "point": 1e-05, "digits": 5, "spread": 33.0, "time": "2025.05.28 08:13:28"}}, "error": null}, {"timestamp": "2025-05-28T13:13:28.277183", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "d94a65d9-0698-48d2-a243-88bee1589574", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:29.171123", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "d94a65d9-0698-48d2-a243-88bee1589574", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64255, "ask": 0.64278, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:13:29"}}, "error": null}, {"timestamp": "2025-05-28T13:13:29.238966", "operation_type": "MARKET_ORDER", "success": true, "message": "发送请求到MT4服务器: BUY", "parameters": {"action": "BUY", "symbol": "AUDUSD", "lot": "0.01", "sl": "0.64155", "tp": "0.6455", "comment": "QuantumForex_BUY_71%", "requestId": "bbfcbe83-5e4e-4b01-94cf-166ab11e70d3", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:13:30.601277", "operation_type": "MARKET_ORDER", "success": true, "message": "请求处理完成: BUY", "parameters": {"action": "BUY", "symbol": "AUDUSD", "lot": "0.01", "sl": "0.64155", "tp": "0.6455", "comment": "QuantumForex_BUY_71%", "requestId": "bbfcbe83-5e4e-4b01-94cf-166ab11e70d3", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": "0", "result": {"status": "success", "message": "市价单执行成功", "order_id": "0"}, "error": null}, {"timestamp": "2025-05-28T13:14:02.626788", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:14:02.688659", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:14:02.735535", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "dc7f82b0-f683-4da9-9d92-90c4305fa77b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:14:05.867396", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "dc7f82b0-f683-4da9-9d92-90c4305fa77b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34701, "ask": 1.34727, "point": 1e-05, "digits": 5, "spread": 26.0, "time": "2025.05.28 08:14:06"}}, "error": null}, {"timestamp": "2025-05-28T13:14:05.958159", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:14:06.016091", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:14:06.060969", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "b00c0eca-7abc-4a7a-a189-a5815e1ffceb", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:14:06.202115", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "b00c0eca-7abc-4a7a-a189-a5815e1ffceb", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64259, "ask": 0.64282, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:14:06"}}, "error": null}, {"timestamp": "2025-05-28T13:14:06.256981", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:14:06.625838", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:14:06.698641", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "299efa4e-33ae-48af-944f-bca96153d6d8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:14:06.793951", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "299efa4e-33ae-48af-944f-bca96153d6d8", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59446, "ask": 0.59474, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:14:07"}}, "error": null}, {"timestamp": "2025-05-28T13:14:06.847529", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:14:07.028646", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:14:07.094505", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "1a9313ea-89f4-490d-99fa-c94fe33aeee9", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:14:07.622555", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "1a9313ea-89f4-490d-99fa-c94fe33aeee9", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82813, "ask": 0.82841, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:14:08"}}, "error": null}, {"timestamp": "2025-05-28T13:14:07.690044", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:14:07.769306", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:14:07.817176", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "80f85491-5e60-419a-8336-b741098c0536", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:14:08.026491", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "80f85491-5e60-419a-8336-b741098c0536", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38332, "ask": 1.38364, "point": 1e-05, "digits": 5, "spread": 32.0, "time": "2025.05.28 08:14:08"}}, "error": null}, {"timestamp": "2025-05-28T13:14:08.119267", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:14:08.365403", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:14:08.412280", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "d1990410-07c5-419f-821d-10ebf2e0455e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:14:08.580785", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "d1990410-07c5-419f-821d-10ebf2e0455e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.399, "ask": 144.425, "point": 0.001, "digits": 3, "spread": 26.0, "time": "2025.05.28 08:14:09"}}, "error": null}, {"timestamp": "2025-05-28T13:15:04.157140", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:15:06.378541", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:15:06.435372", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "e2249a4a-b095-4fc7-939d-65d48a733ffe", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:15:06.588551", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "e2249a4a-b095-4fc7-939d-65d48a733ffe", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34706, "ask": 1.3473, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 08:15:07"}}, "error": null}, {"timestamp": "2025-05-28T13:15:06.645383", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:15:08.675150", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:15:08.756930", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "14cda667-1fd5-40ad-b522-aa1b2338155f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:15:09.833720", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "14cda667-1fd5-40ad-b522-aa1b2338155f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64267, "ask": 0.6429, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:15:10"}}, "error": null}, {"timestamp": "2025-05-28T13:15:09.890595", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:15:10.063112", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:15:10.111976", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "9f5d8099-2c5a-4e07-9642-f6f2994f2cbf", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:15:10.242626", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "9f5d8099-2c5a-4e07-9642-f6f2994f2cbf", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59445, "ask": 0.59474, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 08:15:10"}}, "error": null}, {"timestamp": "2025-05-28T13:15:10.304992", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:15:10.553856", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:15:10.604720", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "c77eab7b-88b0-4641-8989-7db1b0301397", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:15:10.727681", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "c77eab7b-88b0-4641-8989-7db1b0301397", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82819, "ask": 0.82847, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:15:11"}}, "error": null}, {"timestamp": "2025-05-28T13:15:10.798490", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:15:10.877788", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:15:10.928629", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "61424156-ce51-4253-bde6-60f14c4dd2c0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:15:11.885420", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "61424156-ce51-4253-bde6-60f14c4dd2c0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38338, "ask": 1.38372, "point": 1e-05, "digits": 5, "spread": 34.0, "time": "2025.05.28 08:15:12"}}, "error": null}, {"timestamp": "2025-05-28T13:15:11.962253", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:15:14.752461", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:15:14.807341", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "c6d639c6-34a3-4943-a8c6-701f6e6eb94a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:15:18.152755", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "c6d639c6-34a3-4943-a8c6-701f6e6eb94a", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.414, "ask": 144.436, "point": 0.001, "digits": 3, "spread": 22.0, "time": "2025.05.28 08:15:18"}}, "error": null}, {"timestamp": "2025-05-28T13:16:12.458748", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:16:12.688549", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:16:12.734431", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "ed3768a5-cb4d-44f0-8dfc-f1f1d5b5d962", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:16:15.595728", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "ed3768a5-cb4d-44f0-8dfc-f1f1d5b5d962", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.3471, "ask": 1.34733, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:16:16"}}, "error": null}, {"timestamp": "2025-05-28T13:16:15.655569", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:16:15.956224", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:16:16.033017", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "9326567a-583f-426d-9e56-a93e8442a639", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:16:16.344264", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "9326567a-583f-426d-9e56-a93e8442a639", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64274, "ask": 0.64297, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:16:16"}}, "error": null}, {"timestamp": "2025-05-28T13:16:16.431997", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:16:16.952762", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:16:17.021577", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "e86e50f9-dd98-48ab-a942-304a2b44d844", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:16:17.248203", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "e86e50f9-dd98-48ab-a942-304a2b44d844", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59444, "ask": 0.59472, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:16:17"}}, "error": null}, {"timestamp": "2025-05-28T13:16:17.308033", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:16:17.406498", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:16:17.453372", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "9347a40c-b718-4822-9f6d-2783ddadd8be", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:16:17.589697", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "9347a40c-b718-4822-9f6d-2783ddadd8be", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82823, "ask": 0.82851, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:16:18"}}, "error": null}, {"timestamp": "2025-05-28T13:16:17.648541", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:16:18.225942", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:16:18.286778", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "c384cc80-49e9-401f-80b3-41c6c87884fe", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:16:18.804131", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "c384cc80-49e9-401f-80b3-41c6c87884fe", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38334, "ask": 1.38368, "point": 1e-05, "digits": 5, "spread": 34.0, "time": "2025.05.28 08:16:19"}}, "error": null}, {"timestamp": "2025-05-28T13:16:18.894915", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:16:19.145258", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:16:19.212104", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "3c490a61-abc5-4445-b7d7-dd02eb268305", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:16:19.340849", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "3c490a61-abc5-4445-b7d7-dd02eb268305", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.415, "ask": 144.435, "point": 0.001, "digits": 3, "spread": 20.0, "time": "2025.05.28 08:16:19"}}, "error": null}, {"timestamp": "2025-05-28T13:17:15.866994", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:17:16.505839", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:17:16.561689", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "869d3095-a12d-4cf9-8c79-2b67e3042180", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:17:16.712894", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "869d3095-a12d-4cf9-8c79-2b67e3042180", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34694, "ask": 1.34718, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 08:17:17"}}, "error": null}, {"timestamp": "2025-05-28T13:17:16.786335", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:17:18.810057", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:17:18.856933", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "ba9412bc-38b1-4945-b495-5f8c89c68320", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:17:18.915065", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "ba9412bc-38b1-4945-b495-5f8c89c68320", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.6428, "ask": 0.64303, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:17:19"}}, "error": null}, {"timestamp": "2025-05-28T13:17:18.971916", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:17:19.129853", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:17:19.179728", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "85348657-a97a-4c78-8719-36c23fc8205b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:17:19.269401", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "85348657-a97a-4c78-8719-36c23fc8205b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59457, "ask": 0.59485, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:17:19"}}, "error": null}, {"timestamp": "2025-05-28T13:17:19.326245", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:17:20.233012", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:17:20.304820", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "27f1a10a-5d0f-468c-a298-e70d26e30ad0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:17:20.366322", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "27f1a10a-5d0f-468c-a298-e70d26e30ad0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82829, "ask": 0.82859, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 08:17:20"}}, "error": null}, {"timestamp": "2025-05-28T13:17:20.422197", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:17:22.339266", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:17:22.402094", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "4b46868e-0058-4046-a5e2-64dffcb7c239", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:17:24.281482", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "4b46868e-0058-4046-a5e2-64dffcb7c239", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38337, "ask": 1.38367, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 08:17:24"}}, "error": null}, {"timestamp": "2025-05-28T13:17:24.342349", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:17:24.445817", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:17:24.493706", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "b5cf33c1-3a47-419c-b50d-34616b21a1ff", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:17:25.100718", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "b5cf33c1-3a47-419c-b50d-34616b21a1ff", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.457, "ask": 144.479, "point": 0.001, "digits": 3, "spread": 22.0, "time": "2025.05.28 08:17:25"}}, "error": null}, {"timestamp": "2025-05-28T13:18:20.353536", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:18:21.458535", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:18:21.514412", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "e9d89f93-b80e-4585-bb36-8be3b91cf05e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:18:23.496360", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "e9d89f93-b80e-4585-bb36-8be3b91cf05e", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34693, "ask": 1.34717, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 08:18:24"}}, "error": null}, {"timestamp": "2025-05-28T13:18:23.560529", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:18:23.613398", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:18:23.660289", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "1608f52f-072a-4ceb-aaa0-c5bdb7a8424d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:18:23.735351", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "1608f52f-072a-4ceb-aaa0-c5bdb7a8424d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.6429, "ask": 0.64313, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:18:24"}}, "error": null}, {"timestamp": "2025-05-28T13:18:23.792109", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:18:23.907650", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:18:23.958540", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "e1511c6e-24e7-403f-be6d-99ebcdd1d0ea", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:18:24.046493", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "e1511c6e-24e7-403f-be6d-99ebcdd1d0ea", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59458, "ask": 0.59486, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:18:24"}}, "error": null}, {"timestamp": "2025-05-28T13:18:24.105328", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:18:24.864714", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:18:24.913586", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "b86a8fb8-abff-4e3d-9afb-e87c94f61a1b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:18:25.488230", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "b86a8fb8-abff-4e3d-9afb-e87c94f61a1b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82851, "ask": 0.82873, "point": 1e-05, "digits": 5, "spread": 22.0, "time": "2025.05.28 08:18:26"}}, "error": null}, {"timestamp": "2025-05-28T13:18:25.543152", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:18:26.545515", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:18:26.628318", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "6eb1e057-c268-4ba2-8a41-13b45f59cb30", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:18:26.676588", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "6eb1e057-c268-4ba2-8a41-13b45f59cb30", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38343, "ask": 1.38373, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 08:18:27"}}, "error": null}, {"timestamp": "2025-05-28T13:18:26.732465", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:18:26.785656", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:18:26.830534", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "93589bc8-1da3-459c-9b4f-68d1e4a804c4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:18:27.305729", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "93589bc8-1da3-459c-9b4f-68d1e4a804c4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.528, "ask": 144.55, "point": 0.001, "digits": 3, "spread": 22.0, "time": "2025.05.28 08:18:27"}}, "error": null}, {"timestamp": "2025-05-28T13:19:22.251179", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:19:23.572333", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:19:23.639152", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "6bdaf097-4a43-4e31-8955-ac246f8f458d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:19:23.754843", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "6bdaf097-4a43-4e31-8955-ac246f8f458d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.3468, "ask": 1.34703, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:19:24"}}, "error": null}, {"timestamp": "2025-05-28T13:19:23.809697", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:19:23.941344", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:19:23.987221", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "b73d2471-89bd-4386-840c-ff844042867b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:19:25.248847", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "b73d2471-89bd-4386-840c-ff844042867b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64298, "ask": 0.64321, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:19:25"}}, "error": null}, {"timestamp": "2025-05-28T13:19:25.303701", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:19:27.588404", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:19:27.639294", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "e60782ef-b242-4b88-98db-2307ddd919fa", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:19:28.313491", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "e60782ef-b242-4b88-98db-2307ddd919fa", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.5946, "ask": 0.59488, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:19:28"}}, "error": null}, {"timestamp": "2025-05-28T13:19:28.384302", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:19:28.509938", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:19:28.556813", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "7791f773-e6f6-4f1c-8a0d-f22837b0e925", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:19:32.710953", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "7791f773-e6f6-4f1c-8a0d-f22837b0e925", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82847, "ask": 0.8287, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:19:33"}}, "error": null}, {"timestamp": "2025-05-28T13:19:32.766804", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:19:37.289743", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:19:37.337615", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "722b9ccd-b9be-49d6-a0b6-743fe47598f4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:19:38.216029", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "722b9ccd-b9be-49d6-a0b6-743fe47598f4", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38344, "ask": 1.38373, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 08:19:38"}}, "error": null}, {"timestamp": "2025-05-28T13:19:38.321734", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:19:40.304432", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:19:40.358288", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "8f7fc91f-4411-4b8b-95ef-5d034cdbd07b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:19:44.091464", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "8f7fc91f-4411-4b8b-95ef-5d034cdbd07b", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.514, "ask": 144.543, "point": 0.001, "digits": 3, "spread": 29.0, "time": "2025.05.28 08:19:44"}}, "error": null}, {"timestamp": "2025-05-28T13:20:39.491498", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:20:39.826310", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:20:39.874185", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "23ca2120-324f-4092-a4b7-7c90f9d7dabf", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:20:39.980222", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "23ca2120-324f-4092-a4b7-7c90f9d7dabf", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34636, "ask": 1.34661, "point": 1e-05, "digits": 5, "spread": 25.0, "time": "2025.05.28 08:20:40"}}, "error": null}, {"timestamp": "2025-05-28T13:20:40.054022", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:20:40.300055", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:20:40.344963", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "bb6e4ad7-2418-4615-a02f-019068f35174", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:20:40.707288", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "bb6e4ad7-2418-4615-a02f-019068f35174", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64273, "ask": 0.64296, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:20:41"}}, "error": null}, {"timestamp": "2025-05-28T13:20:40.771163", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:20:41.054337", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:20:41.109191", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "08ce9b6b-fb58-4c09-ac07-baf5965d9bbb", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:20:41.922140", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "08ce9b6b-fb58-4c09-ac07-baf5965d9bbb", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59439, "ask": 0.59469, "point": 1e-05, "digits": 5, "spread": 30.0, "time": "2025.05.28 08:20:42"}}, "error": null}, {"timestamp": "2025-05-28T13:20:41.998927", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:20:42.308434", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:20:42.364318", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "9f672089-1298-431d-8052-efb1873c2fe0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:20:42.823451", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "9f672089-1298-431d-8052-efb1873c2fe0", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82874, "ask": 0.82898, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 08:20:43"}}, "error": null}, {"timestamp": "2025-05-28T13:20:42.889278", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:20:43.954608", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:20:44.026416", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "42ffd75c-beb5-4801-9e77-27627a606b76", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:20:44.228992", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "42ffd75c-beb5-4801-9e77-27627a606b76", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.3836, "ask": 1.38389, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 08:20:44"}}, "error": null}, {"timestamp": "2025-05-28T13:20:44.288795", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:20:44.383874", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:20:44.456682", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "da40a1f4-3c0a-4947-9d42-b6ef62d7f61d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:20:44.599510", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "da40a1f4-3c0a-4947-9d42-b6ef62d7f61d", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.675, "ask": 144.697, "point": 0.001, "digits": 3, "spread": 22.0, "time": "2025.05.28 08:20:45"}}, "error": null}, {"timestamp": "2025-05-28T13:21:48.550733", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:21:50.961428", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:21:51.009329", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "05ca3f77-e22b-4041-a8a7-2a9b59e9f4df", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:21:51.149323", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "GBPUSD", "requestId": "05ca3f77-e22b-4041-a8a7-2a9b59e9f4df", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.34652, "ask": 1.3468, "point": 1e-05, "digits": 5, "spread": 28.0, "time": "2025.05.28 08:21:51"}}, "error": null}, {"timestamp": "2025-05-28T13:21:51.209162", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:21:52.279889", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:21:52.332780", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "c0365e80-b792-4d84-b904-755194004719", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:21:52.545612", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "AUDUSD", "requestId": "c0365e80-b792-4d84-b904-755194004719", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.64303, "ask": 0.64326, "point": 1e-05, "digits": 5, "spread": 23.0, "time": "2025.05.28 08:21:53"}}, "error": null}, {"timestamp": "2025-05-28T13:21:52.618959", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:21:53.188987", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:21:53.265779", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "1241a010-e9db-429f-99d6-6fb1cb17fa98", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:21:53.380140", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "NZDUSD", "requestId": "1241a010-e9db-429f-99d6-6fb1cb17fa98", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.59466, "ask": 0.59495, "point": 1e-05, "digits": 5, "spread": 29.0, "time": "2025.05.28 08:21:53"}}, "error": null}, {"timestamp": "2025-05-28T13:21:53.444984", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:21:54.635971", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:21:54.758645", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "8ecabce9-1666-4956-8227-e9faba4e03c7", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:21:54.899378", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCHF", "requestId": "8ecabce9-1666-4956-8227-e9faba4e03c7", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 0.82858, "ask": 0.82882, "point": 1e-05, "digits": 5, "spread": 24.0, "time": "2025.05.28 08:21:55"}}, "error": null}, {"timestamp": "2025-05-28T13:21:54.955231", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:21:55.274149", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:21:55.343960", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "2208b6f3-0d18-49e3-beba-c832b388392f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:21:56.766079", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDCAD", "requestId": "2208b6f3-0d18-49e3-beba-c832b388392f", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 1.38349, "ask": 1.3838, "point": 1e-05, "digits": 5, "spread": 31.0, "time": "2025.05.28 08:21:57"}}, "error": null}, {"timestamp": "2025-05-28T13:21:56.826928", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:21:56.988691", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}, {"timestamp": "2025-05-28T13:21:57.040587", "operation_type": "OTHER", "success": true, "message": "发送请求到MT4服务器: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "ea7f012f-4483-4e11-b738-48194654b3eb", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:21:57.142451", "operation_type": "OTHER", "success": true, "message": "请求处理完成: MARKET_INFO", "parameters": {"action": "MARKET_INFO", "symbol": "USDJPY", "requestId": "ea7f012f-4483-4e11-b738-48194654b3eb", "auth_code": "test-auth-code-1", "auth_type": "client"}, "order_id": null, "result": {"status": "success", "data": {"bid": 144.569, "ask": 144.593, "point": 0.001, "digits": 3, "spread": 24.0, "time": "2025.05.28 08:21:57"}}, "error": null}, {"timestamp": "2025-05-28T13:50:27.719530", "operation_type": "CONNECTION", "success": true, "message": "尝试连接到MT4服务器: tcp://127.0.0.1:5555", "parameters": {"server_address": "tcp://127.0.0.1:5555", "timeout": 60000}, "order_id": null, "result": null, "error": null}, {"timestamp": "2025-05-28T13:50:28.228244", "operation_type": "CONNECTION", "success": true, "message": "成功连接到MT4服务器", "parameters": null, "order_id": null, "result": {"status": "success", "message": "pong"}, "error": null}]