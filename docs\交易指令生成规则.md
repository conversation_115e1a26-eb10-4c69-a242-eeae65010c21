# 交易指令生成规则

## 入场价格设置注意事项

为了确保系统生成的交易指令更加合理，特别是入场价格设置更加贴近当前市场价格，我们对LLM提示词进行了以下修改：

1. **在提示词中明确显示当前价格**：
   ```
   - 当前价格: {current_price}
   ```

2. **添加入场价格注意事项**：
   ```
   #### 入场价格注意事项：
   - **当前价格是 {current_price}，请根据你的市场分析设置合理的入场价格**
   - **如果使用市价单(MARKET)，entryPrice应设为null，系统将使用当前市场价格**
   - **如果使用限价单(LIMIT)或止损单(STOP)，请考虑市场趋势和关键价格水平**
   - **请注意，入场价格离当前价格过远可能导致订单长时间不被触发**
   - **作为专业交易者，请根据你的分析设置最合适的入场价格**
   ```

## 风险管理原则

系统已经包含了以下风险管理原则，确保生成的交易指令符合健康的交易习惯：

```
#### 风险管理原则：
- **必须设置止损和止盈，这是硬性要求**
- **止损应设置在合理的技术位置，如支撑位下方或阻力位上方**
- **止损不应太近（避免被市场噪音触发）也不应太远（避免过大风险）**
- **止盈应设置在合理的目标位置，如重要阻力位或支撑位**
- **风险回报比必须至少为1:1，即止损距离不应大于止盈距离，这是硬性要求**
- **理想的风险回报比应在1:1到1:3之间，这样可以在胜率不高的情况下仍然盈利**
- **止损点数通常应在10-50点之间，止盈点数应在10-150点之间，符合短线交易特性**
- **仓位大小(lotSize)必须在0.01到0.2之间，请根据风险评估合理设置**
```

## 交易指令格式

系统使用JSON格式提供交易指令，确保格式正确：

```json
{
  "action": "BUY或SELL或NONE",
  "orderType": "MARKET或LIMIT或STOP",
  "entryPrice": 数值或null,
  "stopLoss": 数值,
  "takeProfit": 数值,
  "lotSize": 数值,
  "riskLevel": "LOW或MEDIUM或HIGH",
  "reasoning": "详细的交易理由",
  "orderManagement": [
    {
      "action": "MODIFY或DELETE",
      "orderId": "具体订单ID（如350362172）",
      "newStopLoss": 数值或null,
      "newTakeProfit": 数值或null,
      "newEntryPrice": 数值或null,
      "reason": "详细的操作原因"
    }
  ]
}
```

## 交易指令格式说明

```
#### 交易指令格式：
- 如果建议观望（不开新仓位），将action设为"NONE"
- 如果是限价单或止损单，必须提供entryPrice
- 如果是市价单，entryPrice可以为null
- 订单管理时，orderId必须使用系统中实际存在的订单ID
- 如果不需要管理订单，可以将orderManagement设为空数组[]
- 修改订单时，只需提供要修改的参数，不需要修改的参数可以设为null
- 删除订单时，只需提供orderId，其他参数可以设为null
```

## 修改说明

本次修改主要解决了以下问题：

1. **入场价格过远问题**：之前系统生成的入场价格与当前价格相差太远，导致订单长时间不被触发，或者在市场已经发生重大变化后才被触发。通过在提示词中明确显示当前价格，并添加入场价格注意事项，引导LLM生成更合理的入场价格。

2. **提示词增强**：通过在提示词中添加更多的指导，确保LLM生成的交易指令更加合理，符合健康的交易习惯，但不过度限制LLM的决策能力。

3. **多个倒计时问题**：通过在`app/__init__.py`和`run_realtime_analysis.py`中添加停止所有现有任务的代码，确保在任何时候只有一个预分析任务在运行，避免出现多个倒计时同时显示的问题。

这些修改应该能够解决入场价格过远和多个倒计时的问题，确保系统生成的交易指令更加合理，入场价格更加贴近当前市场价格，并且在任何时候只有一个预分析任务在运行。
