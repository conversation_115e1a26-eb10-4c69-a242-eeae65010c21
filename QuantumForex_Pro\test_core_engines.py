#!/usr/bin/env python3
"""
QuantumForex Pro - 核心引擎测试
测试所有核心引擎的初始化和基本功能
"""

import sys
import os
from datetime import datetime
import pandas as pd
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """创建测试用的市场数据"""
    dates = pd.date_range(end=datetime.now(), periods=100, freq='1min')

    # 生成EURUSD测试数据
    base_price = 1.1350
    price_changes = np.random.normal(0, 0.0008, 100)
    prices = base_price + np.cumsum(price_changes)

    df = pd.DataFrame({
        'open': prices + np.random.normal(0, 0.0001, 100),
        'high': prices + np.abs(np.random.normal(0, 0.0005, 100)),
        'low': prices - np.abs(np.random.normal(0, 0.0005, 100)),
        'close': prices,
        'volume': np.random.randint(1000, 10000, 100)
    }, index=dates)

    return {
        'EURUSD': {
            'ohlcv': df,
            'current_price': prices[-1],
            'volatility': np.std(price_changes),
            'volume': df['volume'].iloc[-1],
            'data_source': 'test_data'
        }
    }

def test_technical_analyzer():
    """测试技术分析引擎"""
    print("🔍 开始技术分析引擎测试...")
    print("=" * 50)

    try:
        from core.signal_engine.simple_technical_analyzer import SimpleTechnicalAnalyzer

        # 创建技术分析引擎
        analyzer = SimpleTechnicalAnalyzer()
        print("✅ 技术分析引擎创建成功")

        # 创建测试数据
        test_data = create_test_data()

        # 执行技术分析
        analysis_result = analyzer.analyze_comprehensive(test_data['EURUSD']['ohlcv'], 'EURUSD')

        if analysis_result:
            print("✅ 技术分析执行成功")
            print(f"   分析结果类型: {type(analysis_result)}")

            # 检查分析结果内容
            if hasattr(analysis_result, 'signal_strength'):
                print(f"   信号强度: {analysis_result.signal_strength:.3f}")
            if hasattr(analysis_result, 'direction'):
                print(f"   信号方向: {analysis_result.direction}")
            if hasattr(analysis_result, 'confidence'):
                print(f"   置信度: {analysis_result.confidence:.3f}")
        else:
            print("❌ 技术分析返回空结果")
            return False

        print("=" * 50)
        print("✅ 技术分析引擎测试完成!")
        return True

    except Exception as e:
        print(f"❌ 技术分析引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_fusion_engine():
    """测试信号融合引擎"""
    print("\n🔍 开始信号融合引擎测试...")
    print("=" * 50)

    try:
        from core.signal_engine.signal_fusion_engine import SignalFusionEngine

        # 创建信号融合引擎
        fusion_engine = SignalFusionEngine()
        print("✅ 信号融合引擎创建成功")

        # 创建测试数据
        test_data = create_test_data()

        # 先执行技术分析获取结果
        from core.signal_engine.simple_technical_analyzer import SimpleTechnicalAnalyzer
        analyzer = SimpleTechnicalAnalyzer()
        technical_analysis = analyzer.analyze_comprehensive(test_data['EURUSD']['ohlcv'], 'EURUSD')

        # 执行信号融合
        fusion_result = fusion_engine.fuse_signals(technical_analysis, 'EURUSD')

        if fusion_result:
            print("✅ 信号融合执行成功")
            print(f"   融合结果类型: {type(fusion_result)}")

            # 检查融合结果内容
            if hasattr(fusion_result, 'strength'):
                print(f"   融合强度: {fusion_result.strength:.3f}")
            if hasattr(fusion_result, 'direction'):
                print(f"   融合方向: {fusion_result.direction}")
            if hasattr(fusion_result, 'confidence'):
                print(f"   融合置信度: {fusion_result.confidence:.3f}")
        else:
            print("❌ 信号融合返回空结果")
            return False

        print("=" * 50)
        print("✅ 信号融合引擎测试完成!")
        return True

    except Exception as e:
        print(f"❌ 信号融合引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ml_engine():
    """测试机器学习引擎"""
    print("\n🔍 开始机器学习引擎测试...")
    print("=" * 50)

    try:
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine

        # 创建机器学习引擎
        ml_engine = LightweightMLEngine()
        print("✅ 机器学习引擎创建成功")

        # 创建测试数据
        test_data = create_test_data()

        # 先执行技术分析获取技术指标
        from core.signal_engine.simple_technical_analyzer import SimpleTechnicalAnalyzer
        analyzer = SimpleTechnicalAnalyzer()
        technical_analysis = analyzer.analyze_comprehensive(test_data['EURUSD']['ohlcv'], 'EURUSD')

        # 执行ML预测
        ml_result = ml_engine.generate_predictions(test_data['EURUSD']['ohlcv'], technical_analysis)

        if ml_result:
            print("✅ 机器学习预测执行成功")
            print(f"   预测结果类型: {type(ml_result)}")

            # 检查预测结果内容
            print(f"   预测模型数量: {len(ml_result)}")
            for model_type, prediction in ml_result.items():
                print(f"   {model_type.value}: 置信度 {prediction.confidence:.3f}")
                if hasattr(prediction, 'prediction_value'):
                    print(f"     预测值: {prediction.prediction_value}")
                if hasattr(prediction, 'model_used'):
                    print(f"     使用模型: {prediction.model_used}")
        else:
            print("❌ 机器学习预测返回空结果")
            return False

        print("=" * 50)
        print("✅ 机器学习引擎测试完成!")
        return True

    except Exception as e:
        print(f"❌ 机器学习引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_risk_manager():
    """测试风险管理引擎"""
    print("\n🔍 开始风险管理引擎测试...")
    print("=" * 50)

    try:
        from core.risk_engine.advanced_risk_manager import AdvancedRiskManager

        # 创建风险管理引擎
        risk_manager = AdvancedRiskManager()
        print("✅ 风险管理引擎创建成功")

        # 创建测试账户信息
        account_info = {
            'balance': 10000,
            'equity': 10000,
            'initial_balance': 10000,
            'daily_pnl': 0,
            'margin_used': 0,
            'margin_free': 10000,
            'margin_level': 1000
        }

        # 创建测试持仓信息
        positions = []

        # 创建测试市场数据
        market_data = {
            'volatility': 0.01,
            'sentiment': 'NEUTRAL',
            'liquidity_risk': 0.0
        }

        # 执行风险评估
        risk_result = risk_manager.assess_comprehensive_risk(
            account_info, positions, market_data
        )

        if risk_result:
            print("✅ 风险评估执行成功")
            print(f"   风险等级: {risk_result.risk_level.name}")
            print(f"   风险评分: {risk_result.risk_score:.3f}")
            print(f"   建议行动: {risk_result.recommended_action.value}")
        else:
            print("❌ 风险评估返回空结果")
            return False

        print("=" * 50)
        print("✅ 风险管理引擎测试完成!")
        return True

    except Exception as e:
        print(f"❌ 风险管理引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trade_executor():
    """测试交易执行引擎"""
    print("\n🔍 开始交易执行引擎测试...")
    print("=" * 50)

    try:
        from core.execution_engine.trade_executor import TradeExecutor

        # 创建交易执行引擎
        trade_executor = TradeExecutor()
        print("✅ 交易执行引擎创建成功")

        # 测试获取活跃持仓
        active_positions = trade_executor.get_active_positions()
        print(f"✅ 获取活跃持仓成功，当前持仓数: {len(active_positions)}")

        # 测试获取MT4真实持仓
        mt4_positions = trade_executor._get_mt4_real_positions()
        print(f"✅ 获取MT4真实持仓成功，MT4持仓数: {len(mt4_positions)}")

        if mt4_positions:
            print("   MT4持仓详情:")
            for i, pos in enumerate(mt4_positions[:3], 1):  # 只显示前3个
                print(f"     {i}. {pos.get('symbol', 'N/A')} {pos.get('type', 'N/A')} "
                      f"{pos.get('volume', 0)} lots, 盈亏: ${pos.get('profit', 0):.2f}")

        print("=" * 50)
        print("✅ 交易执行引擎测试完成!")
        return True

    except Exception as e:
        print(f"❌ 交易执行引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_position_manager():
    """测试持仓管理器"""
    print("\n🔍 开始持仓管理器测试...")
    print("=" * 50)

    try:
        from core.position_manager import PositionManager
        from core.execution_engine.trade_executor import TradeExecutor

        # 创建交易执行引擎（持仓管理器的依赖）
        trade_executor = TradeExecutor()

        # 创建持仓管理器
        position_manager = PositionManager(trade_executor)
        print("✅ 持仓管理器创建成功")

        # 测试分析所有持仓
        analyses = position_manager.analyze_all_positions()
        print(f"✅ 持仓分析执行成功，分析结果数: {len(analyses)}")

        if analyses:
            print("   持仓分析摘要:")
            for i, analysis in enumerate(analyses[:3], 1):  # 只显示前3个
                print(f"     {i}. {analysis.symbol} {analysis.order_id}: {analysis.action.value}")
                print(f"        原因: {analysis.reason}")
                print(f"        优先级: {analysis.priority}")

        print("=" * 50)
        print("✅ 持仓管理器测试完成!")
        return True

    except Exception as e:
        print(f"❌ 持仓管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_coordinator():
    """测试系统协调器"""
    print("\n🔍 开始系统协调器测试...")
    print("=" * 50)

    try:
        from core.system_coordinator import SystemCoordinator, SystemPriority

        # 创建系统协调器
        coordinator = SystemCoordinator()
        print("✅ 系统协调器创建成功")

        # 测试提交任务
        task_id = coordinator.submit_task(
            system_name='test_system',
            action='test_action',
            data={'test': 'data'},
            priority=SystemPriority.MEDIUM
        )

        if task_id:
            print(f"✅ 任务提交成功，任务ID: {task_id}")
        else:
            print("⚠️ 任务提交被拒绝（可能由于协调规则）")

        # 测试获取系统状态
        status = coordinator.get_system_status()
        print(f"✅ 系统状态获取成功:")
        print(f"   当前状态: {status['current_state']}")
        print(f"   活跃任务: {status['active_tasks']}")
        print(f"   队列大小: {status['queue_size']}")

        print("=" * 50)
        print("✅ 系统协调器测试完成!")
        return True

    except Exception as e:
        print(f"❌ 系统协调器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 QuantumForex Pro - 核心引擎测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 执行所有测试
    tests = [
        ("技术分析引擎", test_technical_analyzer),
        ("信号融合引擎", test_signal_fusion_engine),
        ("机器学习引擎", test_ml_engine),
        ("风险管理引擎", test_risk_manager),
        ("交易执行引擎", test_trade_executor),
        ("持仓管理器", test_position_manager),
        ("系统协调器", test_system_coordinator)
    ]

    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False

    print("\n" + "=" * 60)
    print("📊 核心引擎测试结果汇总:")

    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False

    if all_passed:
        print("🎉 所有核心引擎测试通过! 系统核心功能正常!")
        sys.exit(0)
    else:
        print("❌ 部分核心引擎测试失败，请检查相关模块")
        sys.exit(1)
