"""
错误收集工具

用于收集和记录LLM分析和预分析过程中的解析错误和结果，方便后期调整。
"""
import os
import json
import traceback
from datetime import datetime
import logging
from typing import Any, Dict, List, Union

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('error_collector')

# 错误收集目录
ERROR_LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs', 'error_logs')

# 确保目录存在
if not os.path.exists(ERROR_LOG_DIR):
    os.makedirs(ERROR_LOG_DIR)

def make_json_serializable(obj: Any) -> Any:
    """
    将对象转换为可JSON序列化的格式

    Args:
        obj: 任意对象

    Returns:
        可JSON序列化的对象
    """
    if isinstance(obj, dict):
        # 处理字典
        result = {}
        for key, value in obj.items():
            result[key] = make_json_serializable(value)
        return result
    elif isinstance(obj, list):
        # 处理列表
        return [make_json_serializable(item) for item in obj]
    elif isinstance(obj, (bool, int, float, str, type(None))):
        # 基本类型直接返回
        return obj
    else:
        # 其他类型转换为字符串
        return str(obj)

def log_analysis_error(analysis_type, raw_response, parsed_result, error_info=None, additional_data=None):
    """
    记录分析错误

    Args:
        analysis_type (str): 分析类型，如'pre_analysis'或'full_analysis'
        raw_response (str): 原始LLM响应
        parsed_result (dict): 解析结果
        error_info (str, optional): 错误信息
        additional_data (dict, optional): 额外数据，如输入提示词等
    """
    try:
        # 创建日志记录
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        log_id = f"{analysis_type}_{timestamp}"

        # 使用通用序列化函数处理所有数据
        log_data = {
            "log_id": log_id,
            "timestamp": datetime.now().isoformat(),
            "analysis_type": analysis_type,
            "raw_response": raw_response,
            "parsed_result": make_json_serializable(parsed_result),
            "error_info": error_info,
            "additional_data": make_json_serializable(additional_data or {})
        }

        # 保存到文件
        log_file_path = os.path.join(ERROR_LOG_DIR, f"{log_id}.json")
        with open(log_file_path, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, ensure_ascii=False, indent=2)

        logger.info(f"已记录{analysis_type}分析日志: {log_file_path}")
        return log_file_path
    except Exception as e:
        logger.error(f"记录错误日志时发生异常: {str(e)}")
        logger.error(traceback.format_exc())
        return None

def log_pre_analysis_error(raw_response, parsed_result, error_info=None, prompt=None, market_data=None):
    """
    记录预分析错误

    Args:
        raw_response (str): 原始LLM响应
        parsed_result (dict): 解析结果
        error_info (str, optional): 错误信息
        prompt (str, optional): 输入提示词
        market_data (dict, optional): 市场数据
    """
    additional_data = {
        "prompt": prompt,
        "market_data": market_data
    }
    return log_analysis_error("pre_analysis", raw_response, parsed_result, error_info, additional_data)

def log_full_analysis_error(raw_response, parsed_result, error_info=None, prompt=None, market_data=None, previous_analysis=None):
    """
    记录完整分析错误

    Args:
        raw_response (str): 原始LLM响应
        parsed_result (dict): 解析结果
        error_info (str, optional): 错误信息
        prompt (str, optional): 输入提示词
        market_data (dict, optional): 市场数据
        previous_analysis (dict, optional): 上一次分析结果
    """
    additional_data = {
        "prompt": prompt,
        "market_data": market_data,
        "previous_analysis": previous_analysis
    }
    return log_analysis_error("full_analysis", raw_response, parsed_result, error_info, additional_data)

def get_error_logs(analysis_type=None, start_date=None, end_date=None, limit=100):
    """
    获取错误日志列表

    Args:
        analysis_type (str, optional): 分析类型，如'pre_analysis'或'full_analysis'
        start_date (str, optional): 开始日期，格式为'YYYY-MM-DD'
        end_date (str, optional): 结束日期，格式为'YYYY-MM-DD'
        limit (int, optional): 返回的最大记录数

    Returns:
        list: 错误日志列表
    """
    try:
        logs = []

        # 转换日期
        if start_date:
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
        if end_date:
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()

        # 遍历日志目录
        for filename in sorted(os.listdir(ERROR_LOG_DIR), reverse=True):
            if not filename.endswith('.json'):
                continue

            # 检查分析类型
            if analysis_type and not filename.startswith(analysis_type):
                continue

            # 读取日志文件
            file_path = os.path.join(ERROR_LOG_DIR, filename)
            with open(file_path, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            # 检查日期范围
            log_date = datetime.fromisoformat(log_data["timestamp"]).date()
            if start_date and log_date < start_date:
                continue
            if end_date and log_date > end_date:
                continue

            # 添加到结果列表
            logs.append({
                "log_id": log_data["log_id"],
                "timestamp": log_data["timestamp"],
                "analysis_type": log_data["analysis_type"],
                "error_info": log_data.get("error_info"),
                "file_path": file_path
            })

            # 检查限制
            if len(logs) >= limit:
                break

        return logs
    except Exception as e:
        logger.error(f"获取错误日志列表时发生异常: {str(e)}")
        logger.error(traceback.format_exc())
        return []

def get_error_log_detail(log_id):
    """
    获取错误日志详情

    Args:
        log_id (str): 日志ID

    Returns:
        dict: 错误日志详情
    """
    try:
        file_path = os.path.join(ERROR_LOG_DIR, f"{log_id}.json")
        if not os.path.exists(file_path):
            return None

        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"获取错误日志详情时发生异常: {str(e)}")
        logger.error(traceback.format_exc())
        return None
