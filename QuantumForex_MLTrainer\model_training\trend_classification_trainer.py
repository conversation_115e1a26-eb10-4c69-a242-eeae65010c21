"""
QuantumForex MLTrainer 趋势分类模型训练器
训练趋势识别和分类模型
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Any
from sklearn.preprocessing import LabelEncoder

from .base_trainer import BaseModelTrainer

class TrendClassificationTrainer(BaseModelTrainer):
    """趋势分类模型训练器"""

    def __init__(self):
        super().__init__('classification')
        self.logger = logging.getLogger(__name__)

        # 趋势分析配置
        self.trend_windows = [10, 20, 50]  # 趋势分析窗口
        self.trend_thresholds = [0.001, 0.002, 0.005]  # 趋势强度阈值

    def create_trend_target(self, df: pd.DataFrame, method: str = 'trend_strength',
                           window: int = 20, threshold: float = 0.002) -> pd.Series:
        """创建趋势分类目标变量"""
        try:
            self.logger.info(f"📊 创建趋势目标变量: {method}, 窗口={window}, 阈值={threshold}")

            if method == 'trend_strength':
                # 趋势强度分类（强上涨/弱上涨/横盘/弱下跌/强下跌）
                price_change = df['close'].pct_change(window)
                
                target = pd.Series(index=df.index, dtype=int)
                target[price_change > threshold * 2] = 2    # 强上涨
                target[(price_change > threshold) & (price_change <= threshold * 2)] = 1  # 弱上涨
                target[abs(price_change) <= threshold] = 0  # 横盘
                target[(price_change < -threshold) & (price_change >= -threshold * 2)] = -1  # 弱下跌
                target[price_change < -threshold * 2] = -2  # 强下跌

            elif method == 'trend_direction':
                # 简单趋势方向（上涨/下跌/横盘）
                price_change = df['close'].pct_change(window)
                
                target = pd.Series(index=df.index, dtype=int)
                target[price_change > threshold] = 1   # 上涨
                target[price_change < -threshold] = -1  # 下跌
                target[abs(price_change) <= threshold] = 0  # 横盘

            elif method == 'trend_momentum':
                # 趋势动量分类
                target = self._create_momentum_target(df, window, threshold)

            elif method == 'trend_reversal':
                # 趋势反转识别
                target = self._create_reversal_target(df, window)

            else:
                raise ValueError(f"不支持的趋势目标方法: {method}")

            # 删除NaN值
            target = target.dropna()

            self.logger.info(f"✅ 趋势目标变量创建完成: {len(target)}个样本")
            self.logger.info(f"📊 类别分布: {target.value_counts().to_dict()}")

            return target

        except Exception as e:
            self.logger.error(f"❌ 创建趋势目标变量失败: {e}")
            raise

    def _create_momentum_target(self, df: pd.DataFrame, window: int, threshold: float) -> pd.Series:
        """创建动量目标变量"""
        try:
            # 计算价格动量
            momentum = df['close'].pct_change(window)
            momentum_ma = momentum.rolling(window=5).mean()
            
            # 计算动量变化
            momentum_change = momentum - momentum_ma
            
            target = pd.Series(index=df.index, dtype=int)
            target[momentum_change > threshold] = 1    # 动量增强
            target[momentum_change < -threshold] = -1  # 动量减弱
            target[abs(momentum_change) <= threshold] = 0  # 动量稳定
            
            return target

        except Exception as e:
            self.logger.error(f"❌ 创建动量目标变量失败: {e}")
            return pd.Series()

    def _create_reversal_target(self, df: pd.DataFrame, window: int) -> pd.Series:
        """创建反转目标变量"""
        try:
            # 计算短期和长期趋势
            short_trend = df['close'].pct_change(window // 2)
            long_trend = df['close'].pct_change(window)
            
            # 识别反转信号
            target = pd.Series(index=df.index, dtype=int)
            
            # 上涨反转（长期下跌，短期上涨）
            upward_reversal = (long_trend < -0.001) & (short_trend > 0.001)
            target[upward_reversal] = 1
            
            # 下跌反转（长期上涨，短期下跌）
            downward_reversal = (long_trend > 0.001) & (short_trend < -0.001)
            target[downward_reversal] = -1
            
            # 无反转
            no_reversal = ~(upward_reversal | downward_reversal)
            target[no_reversal] = 0
            
            return target

        except Exception as e:
            self.logger.error(f"❌ 创建反转目标变量失败: {e}")
            return pd.Series()

    def train_trend_strength_model(self, df: pd.DataFrame, feature_columns: List[str],
                                  window: int = 20, threshold: float = 0.002) -> Dict[str, Any]:
        """训练趋势强度分类模型"""
        try:
            self.logger.info(f"🧠 训练趋势强度分类模型: 窗口={window}, 阈值={threshold}")

            # 创建目标变量
            target = self.create_trend_target(df, 'trend_strength', window, threshold)
            target.name = 'target'

            # 对齐数据
            common_index = df.index.intersection(target.index)
            df_aligned = df.loc[common_index].copy()
            target_aligned = target.loc[common_index]

            # 添加目标列到DataFrame
            df_aligned['target'] = target_aligned

            # 准备数据
            X, y, feature_names = self.prepare_data(df_aligned, 'target', feature_columns)

            # 分割数据
            X_train, X_test, y_train, y_test = self.split_data(X, y)

            # 特征标准化
            X_train_scaled, X_test_scaled = self.scale_features(X_train, X_test, f'trend_strength_{window}')

            # 训练模型
            results = self.train_models(X_train_scaled, y_train, X_test_scaled, y_test, feature_names)

            # 获取最佳模型
            best_model_name, best_model = self.get_best_model(results, 'f1_score')

            # 保存模型
            model_prefix = f'trend_classification_{window}min'
            self.save_models(model_prefix)

            # 添加训练记录
            training_record = {
                'model_type': 'trend_strength',
                'window': window,
                'threshold': threshold,
                'best_model': best_model_name,
                'results': {name: result['metrics'] for name, result in results.items()},
                'feature_count': len(feature_names),
                'sample_count': len(X)
            }
            self.training_history.append(training_record)

            return results

        except Exception as e:
            self.logger.error(f"❌ 训练趋势强度分类模型失败: {e}")
            return {}

    def train_trend_direction_model(self, df: pd.DataFrame, feature_columns: List[str],
                                   window: int = 20, threshold: float = 0.002) -> Dict[str, Any]:
        """训练趋势方向分类模型"""
        try:
            self.logger.info(f"🧠 训练趋势方向分类模型: 窗口={window}, 阈值={threshold}")

            # 创建目标变量
            target = self.create_trend_target(df, 'trend_direction', window, threshold)
            target.name = 'target'

            # 对齐数据
            common_index = df.index.intersection(target.index)
            df_aligned = df.loc[common_index].copy()
            target_aligned = target.loc[common_index]

            # 添加目标列到DataFrame
            df_aligned['target'] = target_aligned

            # 准备数据
            X, y, feature_names = self.prepare_data(df_aligned, 'target', feature_columns)

            # 分割数据
            X_train, X_test, y_train, y_test = self.split_data(X, y)

            # 特征标准化
            X_train_scaled, X_test_scaled = self.scale_features(X_train, X_test, f'trend_direction_{window}')

            # 训练模型
            results = self.train_models(X_train_scaled, y_train, X_test_scaled, y_test, feature_names)

            # 获取最佳模型
            best_model_name, best_model = self.get_best_model(results, 'f1_score')

            # 保存模型
            model_prefix = f'trend_classification_direction_{window}min'
            self.save_models(model_prefix)

            return results

        except Exception as e:
            self.logger.error(f"❌ 训练趋势方向分类模型失败: {e}")
            return {}

    def train_all_trend_models(self, df: pd.DataFrame, feature_columns: List[str]) -> Dict[str, Any]:
        """训练所有趋势分类模型"""
        try:
            self.logger.info("🚀 开始训练所有趋势分类模型...")

            all_results = {}

            # 训练趋势强度模型
            for window in [10, 20]:
                for threshold in [0.001, 0.002]:
                    try:
                        results = self.train_trend_strength_model(df, feature_columns, window, threshold)
                        all_results[f'strength_{window}_{threshold}'] = results
                    except Exception as e:
                        self.logger.error(f"训练趋势强度模型失败 (window={window}, threshold={threshold}): {e}")

            # 训练趋势方向模型
            for window in [5, 10, 20]:
                try:
                    results = self.train_trend_direction_model(df, feature_columns, window, 0.001)
                    all_results[f'direction_{window}'] = results
                except Exception as e:
                    self.logger.error(f"训练趋势方向模型失败 (window={window}): {e}")

            self.logger.info(f"🎉 所有趋势分类模型训练完成: {len(all_results)}个模型组合")
            return all_results

        except Exception as e:
            self.logger.error(f"❌ 训练所有趋势分类模型失败: {e}")
            return {}

# 创建趋势分类训练器实例
trend_trainer = TrendClassificationTrainer()
