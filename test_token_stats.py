#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Token统计系统
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_token_statistics():
    """测试Token统计系统"""
    try:
        print("正在测试Token统计系统...")
        
        # 导入token统计系统
        from app.utils.token_statistics import record_token_usage, load_token_stats, generate_token_report
        print("✅ Token统计系统导入成功")
        
        # 测试记录token使用
        print("测试记录token使用...")
        record = record_token_usage(
            model='Pro/deepseek-ai/DeepSeek-R1',
            prompt_tokens=1000,
            completion_tokens=500,
            analysis_type='full_analysis'
        )
        
        if record:
            print(f"✅ Token使用记录成功:")
            print(f"   模型: {record['model']}")
            print(f"   提示词Token: {record['prompt_tokens']}")
            print(f"   生成Token: {record['completion_tokens']}")
            print(f"   总Token: {record['total_tokens']}")
            print(f"   成本: ¥{record['cost']:.4f}")
            print(f"   分析类型: {record['analysis_type']}")
        else:
            print("❌ Token使用记录失败")
            return False
        
        # 测试加载统计数据
        print("\n测试加载统计数据...")
        stats = load_token_stats()
        print(f"✅ 统计数据加载成功:")
        print(f"   总Token: {stats['total_tokens']}")
        print(f"   总成本: ¥{stats['total_cost']:.4f}")
        print(f"   记录数: {len(stats['records'])}")
        
        # 测试分析类型统计
        if 'analysis_type_stats' in stats:
            print(f"   分析类型统计:")
            for analysis_type, type_data in stats['analysis_type_stats'].items():
                print(f"     {analysis_type}: {type_data['count']}次, {type_data['total_tokens']} tokens")
        
        # 测试生成报告
        print("\n测试生成报告...")
        report = generate_token_report()
        if report:
            print("✅ Token统计报告生成成功")
            print("报告内容预览:")
            print(report[:500] + "..." if len(report) > 500 else report)
        else:
            print("❌ Token统计报告生成失败")
        
        print("\n🎉 Token统计系统测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ Token统计系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_token_statistics()
