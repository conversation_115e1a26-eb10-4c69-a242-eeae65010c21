"""
外汇交易系统启动脚本
"""
import os
import sys
import traceback

# 设置UTF-8编码（必须在导入其他模块之前）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    # 注释掉直接修改stdout/stderr，避免与日志系统冲突
    # import codecs
    # sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    # sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

try:
    from dotenv import load_dotenv
    # 加载环境变量
    load_dotenv()

    # 尝试加载本地环境变量（包含授权信息）
    local_env = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env.local')
    if os.path.exists(local_env):
        load_dotenv(local_env)
except ImportError:
    print("警告: python-dotenv 未安装，跳过环境变量加载")

# 不在这里设置日志，避免与logger_manager冲突
# 日志将由logger_manager统一管理

try:
    print("正在导入应用...")
    # 导入应用
    from app import create_app

    print("创建应用...")
    app = create_app()

    if __name__ == '__main__':
        try:
            print("正在启动外汇交易系统...")
            port = int(os.environ.get('PORT', 5000))
            print(f"服务器将在 http://localhost:{port} 上运行")

            # 启用调试模式但禁用自动重新加载器
            app.run(host='0.0.0.0', port=port, debug=True, use_reloader=False)
        except Exception as run_error:
            print(f"启动服务器失败: {run_error}")
            traceback.print_exc()
except Exception as import_error:
    print(f"导入或创建应用失败: {import_error}")
    traceback.print_exc()
