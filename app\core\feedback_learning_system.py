#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易结果反馈学习系统
通过分析交易结果来改进LLM分析质量和系统参数
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

class LearningType(Enum):
    """学习类型"""
    PATTERN_RECOGNITION = "模式识别"
    PARAMETER_OPTIMIZATION = "参数优化"
    STRATEGY_REFINEMENT = "策略优化"
    ERROR_CORRECTION = "错误纠正"
    MARKET_ADAPTATION = "市场适应"

class FeedbackCategory(Enum):
    """反馈类别"""
    SUCCESSFUL_TRADE = "成功交易"
    FAILED_TRADE = "失败交易"
    PARTIAL_SUCCESS = "部分成功"
    EARLY_EXIT = "提前退出"
    MISSED_OPPORTUNITY = "错失机会"

@dataclass
class TradeRecord:
    """交易记录 - 兼容接口"""
    trade_id: str
    timestamp: datetime
    symbol: str
    action: str
    entry_price: float
    exit_price: float
    lot_size: float
    profit_loss: float
    profit_loss_pct: float
    duration_minutes: int
    predicted_direction: str
    actual_direction: str
    market_regime: str
    signal_quality: str
    risk_level: str

@dataclass
class TradeAnalysis:
    """交易分析"""
    trade_id: str
    entry_time: datetime
    exit_time: datetime
    symbol: str
    action: str
    entry_price: float
    exit_price: float
    stop_loss: float
    take_profit: float
    lot_size: float
    profit_loss: float
    profit_loss_pct: float
    holding_duration: timedelta
    exit_reason: str

    # 原始分析数据
    original_llm_analysis: Dict
    original_market_data: Dict
    original_signal_quality: Dict
    original_market_condition: Dict

    # 结果评估
    prediction_accuracy: float
    timing_accuracy: float
    risk_management_effectiveness: float
    overall_performance_score: float

@dataclass
class LearningInsight:
    """学习洞察"""
    insight_type: LearningType
    category: FeedbackCategory
    pattern_description: str
    confidence_level: float
    supporting_evidence: List[str]
    recommended_adjustments: Dict
    impact_assessment: str
    implementation_priority: int

@dataclass
class PerformancePattern:
    """表现模式"""
    pattern_name: str
    market_conditions: List[str]
    signal_characteristics: Dict
    success_rate: float
    avg_return: float
    sample_size: int
    statistical_significance: float
    pattern_strength: float

class FeedbackLearningSystem:
    """反馈学习系统"""

    def __init__(self):
        # 学习参数
        self.learning_params = {
            'min_sample_size': 10,          # 最小样本数量
            'significance_threshold': 0.05,  # 统计显著性阈值
            'confidence_threshold': 0.7,     # 置信度阈值
            'pattern_strength_threshold': 0.6, # 模式强度阈值
            'learning_rate': 0.1,            # 学习率
            'memory_decay': 0.95             # 记忆衰减率
        }

        # 数据存储
        self.trade_history = []
        self.learning_insights = []
        self.performance_patterns = []
        self.parameter_adjustments = {}

        # 学习状态
        self.last_learning_update = None
        self.learning_cycle_count = 0

        # 日志
        self.logger = logging.getLogger(__name__)

    def add_trade_record(self, trade_record: TradeRecord) -> TradeAnalysis:
        """添加交易记录 - 兼容接口"""
        # 将TradeRecord转换为内部格式
        trade_data = {
            'trade_id': trade_record.trade_id,
            'entry_time': trade_record.timestamp.isoformat(),
            'exit_time': trade_record.timestamp.isoformat(),
            'symbol': trade_record.symbol,
            'action': trade_record.action,
            'entry_price': trade_record.entry_price,
            'exit_price': trade_record.exit_price,
            'lot_size': trade_record.lot_size,
            'profit_loss': trade_record.profit_loss,
            'profit_loss_pct': trade_record.profit_loss_pct,
            'holding_duration_seconds': trade_record.duration_minutes * 60,
            'exit_reason': 'manual'
        }

        original_analysis = {
            'llm_analysis': {
                'action': trade_record.predicted_direction,
                'reasoning': f'预测方向: {trade_record.predicted_direction}'
            },
            'market_data': {},
            'signal_quality': {
                'grade': trade_record.signal_quality,
                'risk_reward_ratio': 1.5
            },
            'market_condition': {
                'regime': trade_record.market_regime
            }
        }

        return self.record_trade_result(trade_data, original_analysis)

    def record_trade_result(self, trade_data: Dict, original_analysis: Dict) -> TradeAnalysis:
        """记录交易结果"""

        try:
            # 创建交易分析对象
            trade_analysis = self._create_trade_analysis(trade_data, original_analysis)

            # 添加到历史记录
            self.trade_history.append(trade_analysis)

            # 保持最近1000笔记录
            if len(self.trade_history) > 1000:
                self.trade_history = self.trade_history[-1000:]

            # 触发学习更新
            if len(self.trade_history) % 10 == 0:  # 每10笔交易学习一次
                self._trigger_learning_update()

            return trade_analysis

        except Exception as e:
            self.logger.error(f"记录交易结果失败: {e}")
            return None

    def analyze_prediction_accuracy(self, lookback_period: int = 50) -> Dict:
        """分析预测准确性"""

        if len(self.trade_history) < self.learning_params['min_sample_size']:
            return {'status': 'insufficient_data', 'sample_size': len(self.trade_history)}

        recent_trades = self.trade_history[-lookback_period:]

        # 分析不同维度的准确性
        direction_accuracy = self._analyze_direction_accuracy(recent_trades)
        timing_accuracy = self._analyze_timing_accuracy(recent_trades)
        magnitude_accuracy = self._analyze_magnitude_accuracy(recent_trades)
        risk_assessment_accuracy = self._analyze_risk_assessment_accuracy(recent_trades)

        # 综合准确性评分
        overall_accuracy = (
            direction_accuracy * 0.4 +
            timing_accuracy * 0.3 +
            magnitude_accuracy * 0.2 +
            risk_assessment_accuracy * 0.1
        )

        return {
            'overall_accuracy': overall_accuracy,
            'direction_accuracy': direction_accuracy,
            'timing_accuracy': timing_accuracy,
            'magnitude_accuracy': magnitude_accuracy,
            'risk_assessment_accuracy': risk_assessment_accuracy,
            'sample_size': len(recent_trades),
            'analysis_date': datetime.now().isoformat()
        }

    def identify_performance_patterns(self) -> List[PerformancePattern]:
        """识别表现模式"""

        if len(self.trade_history) < self.learning_params['min_sample_size']:
            return []

        patterns = []

        # 1. 市场条件模式
        market_patterns = self._identify_market_condition_patterns()
        patterns.extend(market_patterns)

        # 2. 信号质量模式
        signal_patterns = self._identify_signal_quality_patterns()
        patterns.extend(signal_patterns)

        # 3. 时间模式
        time_patterns = self._identify_time_patterns()
        patterns.extend(time_patterns)

        # 4. 风险回报模式
        risk_reward_patterns = self._identify_risk_reward_patterns()
        patterns.extend(risk_reward_patterns)

        # 过滤和排序模式
        significant_patterns = [p for p in patterns if p.statistical_significance < self.learning_params['significance_threshold']]
        significant_patterns.sort(key=lambda x: x.pattern_strength, reverse=True)

        # 更新模式库
        self.performance_patterns = significant_patterns[:20]  # 保留前20个最强模式

        return self.performance_patterns

    def generate_learning_insights(self) -> List[LearningInsight]:
        """生成学习洞察"""

        insights = []

        # 1. 分析预测准确性
        accuracy_analysis = self.analyze_prediction_accuracy()
        if accuracy_analysis.get('overall_accuracy', 0) < 0.6:
            insights.append(self._create_accuracy_improvement_insight(accuracy_analysis))

        # 2. 分析表现模式
        patterns = self.identify_performance_patterns()
        for pattern in patterns[:5]:  # 分析前5个最强模式
            insight = self._create_pattern_insight(pattern)
            if insight:
                insights.append(insight)

        # 3. 分析常见错误
        error_insights = self._analyze_common_errors()
        insights.extend(error_insights)

        # 4. 分析参数优化机会
        param_insights = self._analyze_parameter_optimization_opportunities()
        insights.extend(param_insights)

        # 按优先级排序
        insights.sort(key=lambda x: x.implementation_priority, reverse=True)

        # 更新洞察库
        self.learning_insights = insights[:10]  # 保留前10个最重要的洞察

        return self.learning_insights

    def generate_llm_feedback_prompt(self, recent_trades: int = 10) -> str:
        """生成LLM反馈提示词"""

        if len(self.trade_history) < recent_trades:
            recent_trades = len(self.trade_history)

        if recent_trades == 0:
            return "暂无交易历史数据可供分析。"

        recent_trade_data = self.trade_history[-recent_trades:]

        # 统计基本信息
        total_trades = len(recent_trade_data)
        winning_trades = len([t for t in recent_trade_data if t.profit_loss > 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        avg_return = np.mean([t.profit_loss_pct for t in recent_trade_data])

        # 分析成功和失败的交易
        successful_trades = [t for t in recent_trade_data if t.profit_loss > 0]
        failed_trades = [t for t in recent_trade_data if t.profit_loss <= 0]

        # 构建反馈提示词
        feedback_prompt = f"""
# 交易表现反馈分析

## 基本统计
- 总交易数: {total_trades}
- 胜率: {win_rate:.1%}
- 平均收益率: {avg_return:.2%}

## 成功交易分析
"""

        if successful_trades:
            feedback_prompt += "### 成功交易特征:\n"
            for i, trade in enumerate(successful_trades[:3], 1):
                feedback_prompt += f"""
{i}. 交易时间: {trade.entry_time.strftime('%Y-%m-%d %H:%M')}
   - 方向: {trade.action}
   - 收益: {trade.profit_loss_pct:.2%}
   - 持仓时间: {trade.holding_duration}
   - 原始分析: {trade.original_llm_analysis.get('reasoning', '')[:100]}...
"""

        if failed_trades:
            feedback_prompt += "\n### 失败交易分析:\n"
            for i, trade in enumerate(failed_trades[:3], 1):
                feedback_prompt += f"""
{i}. 交易时间: {trade.entry_time.strftime('%Y-%m-%d %H:%M')}
   - 方向: {trade.action}
   - 亏损: {trade.profit_loss_pct:.2%}
   - 持仓时间: {trade.holding_duration}
   - 原始分析: {trade.original_llm_analysis.get('reasoning', '')[:100]}...
   - 退出原因: {trade.exit_reason}
"""

        # 添加学习洞察
        if self.learning_insights:
            feedback_prompt += "\n## 系统学习洞察\n"
            for insight in self.learning_insights[:3]:
                feedback_prompt += f"""
### {insight.insight_type.value}
- 模式描述: {insight.pattern_description}
- 置信度: {insight.confidence_level:.1%}
- 建议调整: {insight.recommended_adjustments}
"""

        feedback_prompt += """

## 请基于以上数据分析：
1. 识别成功交易的共同特征和模式
2. 分析失败交易的主要原因
3. 提出具体的分析改进建议
4. 建议优化的分析要点和关注重点
5. 针对不同市场条件的分析策略调整

请提供具体、可操作的改进建议，帮助提高未来分析的准确性和有效性。
"""

        return feedback_prompt

    def _create_trade_analysis(self, trade_data: Dict, original_analysis: Dict) -> TradeAnalysis:
        """创建交易分析对象"""

        # 计算表现评分
        prediction_accuracy = self._calculate_prediction_accuracy(trade_data, original_analysis)
        timing_accuracy = self._calculate_timing_accuracy(trade_data, original_analysis)
        risk_effectiveness = self._calculate_risk_management_effectiveness(trade_data, original_analysis)

        overall_score = (prediction_accuracy * 0.5 + timing_accuracy * 0.3 + risk_effectiveness * 0.2)

        return TradeAnalysis(
            trade_id=trade_data.get('trade_id', f"trade_{datetime.now().timestamp()}"),
            entry_time=datetime.fromisoformat(trade_data.get('entry_time', datetime.now().isoformat())),
            exit_time=datetime.fromisoformat(trade_data.get('exit_time', datetime.now().isoformat())),
            symbol=trade_data.get('symbol', 'EURUSD'),
            action=trade_data.get('action', 'BUY'),
            entry_price=trade_data.get('entry_price', 0.0),
            exit_price=trade_data.get('exit_price', 0.0),
            stop_loss=trade_data.get('stop_loss', 0.0),
            take_profit=trade_data.get('take_profit', 0.0),
            lot_size=trade_data.get('lot_size', 0.0),
            profit_loss=trade_data.get('profit_loss', 0.0),
            profit_loss_pct=trade_data.get('profit_loss_pct', 0.0),
            holding_duration=timedelta(seconds=trade_data.get('holding_duration_seconds', 0)),
            exit_reason=trade_data.get('exit_reason', 'unknown'),
            original_llm_analysis=original_analysis.get('llm_analysis', {}),
            original_market_data=original_analysis.get('market_data', {}),
            original_signal_quality=original_analysis.get('signal_quality', {}),
            original_market_condition=original_analysis.get('market_condition', {}),
            prediction_accuracy=prediction_accuracy,
            timing_accuracy=timing_accuracy,
            risk_management_effectiveness=risk_effectiveness,
            overall_performance_score=overall_score
        )

    def _calculate_prediction_accuracy(self, trade_data: Dict, original_analysis: Dict) -> float:
        """计算预测准确性"""

        # 方向预测准确性
        predicted_action = original_analysis.get('llm_analysis', {}).get('action', 'NONE')
        actual_profit = trade_data.get('profit_loss', 0)

        direction_correct = (
            (predicted_action == 'BUY' and actual_profit > 0) or
            (predicted_action == 'SELL' and actual_profit > 0)
        )

        direction_score = 1.0 if direction_correct else 0.0

        # 幅度预测准确性（简化）
        expected_return = abs(trade_data.get('profit_loss_pct', 0))
        magnitude_score = min(expected_return / 0.02, 1.0)  # 假设期望收益2%

        return (direction_score * 0.7 + magnitude_score * 0.3)

    def _calculate_timing_accuracy(self, trade_data: Dict, original_analysis: Dict) -> float:
        """计算时机准确性"""

        holding_duration = trade_data.get('holding_duration_seconds', 0)
        exit_reason = trade_data.get('exit_reason', '')

        # 基于持仓时间和退出原因评估时机
        if exit_reason == 'take_profit':
            return 1.0
        elif exit_reason == 'stop_loss':
            return 0.2
        elif holding_duration < 3600:  # 1小时内
            return 0.6
        elif holding_duration < 86400:  # 1天内
            return 0.8
        else:
            return 0.4

    def _calculate_risk_management_effectiveness(self, trade_data: Dict, original_analysis: Dict) -> float:
        """计算风险管理有效性"""

        profit_loss_pct = trade_data.get('profit_loss_pct', 0)
        stop_loss = trade_data.get('stop_loss', 0)
        entry_price = trade_data.get('entry_price', 0)

        # 计算实际风险
        if entry_price > 0 and stop_loss > 0:
            planned_risk = abs(entry_price - stop_loss) / entry_price
            actual_loss = abs(profit_loss_pct) if profit_loss_pct < 0 else 0

            if actual_loss <= planned_risk:
                return 1.0  # 风险控制有效
            else:
                return max(0.0, 1.0 - (actual_loss - planned_risk) / planned_risk)

        return 0.5  # 默认中等评分

    def _analyze_direction_accuracy(self, trades: List[TradeAnalysis]) -> float:
        """分析方向预测准确性"""

        if not trades:
            return 0.0

        correct_predictions = 0
        for trade in trades:
            predicted_action = trade.original_llm_analysis.get('action', 'NONE')
            actual_profit = trade.profit_loss

            if ((predicted_action == 'BUY' and actual_profit > 0) or
                (predicted_action == 'SELL' and actual_profit > 0)):
                correct_predictions += 1

        return correct_predictions / len(trades)

    def _analyze_timing_accuracy(self, trades: List[TradeAnalysis]) -> float:
        """分析时机准确性"""

        if not trades:
            return 0.0

        timing_scores = [trade.timing_accuracy for trade in trades]
        return np.mean(timing_scores)

    def _analyze_magnitude_accuracy(self, trades: List[TradeAnalysis]) -> float:
        """分析幅度预测准确性"""

        if not trades:
            return 0.0

        magnitude_scores = []
        for trade in trades:
            expected_return = 0.02  # 假设期望收益2%
            actual_return = abs(trade.profit_loss_pct)

            if actual_return > 0:
                accuracy = min(actual_return / expected_return, 2.0) / 2.0
                magnitude_scores.append(accuracy)

        return np.mean(magnitude_scores) if magnitude_scores else 0.0

    def _analyze_risk_assessment_accuracy(self, trades: List[TradeAnalysis]) -> float:
        """分析风险评估准确性"""

        if not trades:
            return 0.0

        risk_scores = [trade.risk_management_effectiveness for trade in trades]
        return np.mean(risk_scores)

    def _identify_market_condition_patterns(self) -> List[PerformancePattern]:
        """识别市场条件模式"""

        patterns = []

        # 按市场制度分组
        market_groups = {}
        for trade in self.trade_history:
            regime = trade.original_market_condition.get('regime', 'UNKNOWN')
            if regime not in market_groups:
                market_groups[regime] = []
            market_groups[regime].append(trade)

        # 分析每个市场制度的表现
        for regime, trades in market_groups.items():
            if len(trades) >= self.learning_params['min_sample_size']:
                success_rate = len([t for t in trades if t.profit_loss > 0]) / len(trades)
                avg_return = np.mean([t.profit_loss_pct for t in trades])

                # 计算统计显著性（简化）
                significance = self._calculate_statistical_significance(trades)
                pattern_strength = abs(success_rate - 0.5) * 2  # 偏离随机的程度

                pattern = PerformancePattern(
                    pattern_name=f"市场制度_{regime}",
                    market_conditions=[regime],
                    signal_characteristics={'market_regime': regime},
                    success_rate=success_rate,
                    avg_return=avg_return,
                    sample_size=len(trades),
                    statistical_significance=significance,
                    pattern_strength=pattern_strength
                )
                patterns.append(pattern)

        return patterns

    def _identify_signal_quality_patterns(self) -> List[PerformancePattern]:
        """识别信号质量模式"""

        patterns = []

        # 按信号等级分组
        signal_groups = {}
        for trade in self.trade_history:
            grade = trade.original_signal_quality.get('grade', 'UNKNOWN')
            if grade not in signal_groups:
                signal_groups[grade] = []
            signal_groups[grade].append(trade)

        # 分析每个信号等级的表现
        for grade, trades in signal_groups.items():
            if len(trades) >= self.learning_params['min_sample_size']:
                success_rate = len([t for t in trades if t.profit_loss > 0]) / len(trades)
                avg_return = np.mean([t.profit_loss_pct for t in trades])

                significance = self._calculate_statistical_significance(trades)
                pattern_strength = abs(success_rate - 0.5) * 2

                pattern = PerformancePattern(
                    pattern_name=f"信号等级_{grade}",
                    market_conditions=[],
                    signal_characteristics={'signal_grade': grade},
                    success_rate=success_rate,
                    avg_return=avg_return,
                    sample_size=len(trades),
                    statistical_significance=significance,
                    pattern_strength=pattern_strength
                )
                patterns.append(pattern)

        return patterns

    def _identify_time_patterns(self) -> List[PerformancePattern]:
        """识别时间模式"""

        patterns = []

        # 按小时分组
        hour_groups = {}
        for trade in self.trade_history:
            hour = trade.entry_time.hour
            if hour not in hour_groups:
                hour_groups[hour] = []
            hour_groups[hour].append(trade)

        # 分析每个小时的表现
        for hour, trades in hour_groups.items():
            if len(trades) >= self.learning_params['min_sample_size']:
                success_rate = len([t for t in trades if t.profit_loss > 0]) / len(trades)
                avg_return = np.mean([t.profit_loss_pct for t in trades])

                significance = self._calculate_statistical_significance(trades)
                pattern_strength = abs(success_rate - 0.5) * 2

                pattern = PerformancePattern(
                    pattern_name=f"交易时间_{hour}时",
                    market_conditions=[],
                    signal_characteristics={'entry_hour': hour},
                    success_rate=success_rate,
                    avg_return=avg_return,
                    sample_size=len(trades),
                    statistical_significance=significance,
                    pattern_strength=pattern_strength
                )
                patterns.append(pattern)

        return patterns

    def _identify_risk_reward_patterns(self) -> List[PerformancePattern]:
        """识别风险回报模式"""

        patterns = []

        # 按风险回报比分组
        rr_groups = {'low': [], 'medium': [], 'high': []}
        for trade in self.trade_history:
            rr_ratio = trade.original_signal_quality.get('risk_reward_ratio', 1.0)
            if rr_ratio < 1.5:
                rr_groups['low'].append(trade)
            elif rr_ratio < 2.5:
                rr_groups['medium'].append(trade)
            else:
                rr_groups['high'].append(trade)

        # 分析每个风险回报比组的表现
        for group_name, trades in rr_groups.items():
            if len(trades) >= self.learning_params['min_sample_size']:
                success_rate = len([t for t in trades if t.profit_loss > 0]) / len(trades)
                avg_return = np.mean([t.profit_loss_pct for t in trades])

                significance = self._calculate_statistical_significance(trades)
                pattern_strength = abs(success_rate - 0.5) * 2

                pattern = PerformancePattern(
                    pattern_name=f"风险回报比_{group_name}",
                    market_conditions=[],
                    signal_characteristics={'risk_reward_group': group_name},
                    success_rate=success_rate,
                    avg_return=avg_return,
                    sample_size=len(trades),
                    statistical_significance=significance,
                    pattern_strength=pattern_strength
                )
                patterns.append(pattern)

        return patterns

    def _calculate_statistical_significance(self, trades: List[TradeAnalysis]) -> float:
        """计算统计显著性（简化的t检验）"""

        if len(trades) < 2:
            return 1.0

        returns = [t.profit_loss_pct for t in trades]
        mean_return = np.mean(returns)
        std_return = np.std(returns)

        if std_return == 0:
            return 0.0 if mean_return != 0 else 1.0

        # 简化的t统计量
        t_stat = abs(mean_return) / (std_return / np.sqrt(len(trades)))

        # 简化的p值估算
        if t_stat > 2.576:  # 99%置信度
            return 0.01
        elif t_stat > 1.96:  # 95%置信度
            return 0.05
        elif t_stat > 1.645:  # 90%置信度
            return 0.10
        else:
            return 0.20

    def _create_accuracy_improvement_insight(self, accuracy_analysis: Dict) -> LearningInsight:
        """创建准确性改进洞察"""

        overall_accuracy = accuracy_analysis.get('overall_accuracy', 0)
        direction_accuracy = accuracy_analysis.get('direction_accuracy', 0)
        timing_accuracy = accuracy_analysis.get('timing_accuracy', 0)

        # 识别主要问题
        if direction_accuracy < 0.5:
            focus_area = "方向预测"
            adjustments = {
                'increase_trend_analysis_weight': 0.2,
                'improve_momentum_indicators': True,
                'enhance_market_sentiment_analysis': True
            }
        elif timing_accuracy < 0.5:
            focus_area = "入场时机"
            adjustments = {
                'improve_entry_timing_analysis': True,
                'add_volume_confirmation': True,
                'enhance_support_resistance_analysis': True
            }
        else:
            focus_area = "综合分析质量"
            adjustments = {
                'increase_analysis_depth': True,
                'improve_risk_assessment': True,
                'enhance_market_context_analysis': True
            }

        return LearningInsight(
            insight_type=LearningType.ERROR_CORRECTION,
            category=FeedbackCategory.FAILED_TRADE,
            pattern_description=f"预测准确性偏低({overall_accuracy:.1%})，主要问题在{focus_area}",
            confidence_level=0.8,
            supporting_evidence=[
                f"整体准确性: {overall_accuracy:.1%}",
                f"方向准确性: {direction_accuracy:.1%}",
                f"时机准确性: {timing_accuracy:.1%}"
            ],
            recommended_adjustments=adjustments,
            impact_assessment="高影响 - 直接影响交易胜率",
            implementation_priority=9
        )

    def _create_pattern_insight(self, pattern: PerformancePattern) -> Optional[LearningInsight]:
        """基于模式创建洞察"""

        if pattern.pattern_strength < self.learning_params['pattern_strength_threshold']:
            return None

        if pattern.success_rate > 0.7:
            category = FeedbackCategory.SUCCESSFUL_TRADE
            description = f"{pattern.pattern_name}表现优异(胜率{pattern.success_rate:.1%})"
            adjustments = {'increase_weight_for_pattern': pattern.pattern_name}
            priority = 8
        elif pattern.success_rate < 0.3:
            category = FeedbackCategory.FAILED_TRADE
            description = f"{pattern.pattern_name}表现较差(胜率{pattern.success_rate:.1%})"
            adjustments = {'decrease_weight_for_pattern': pattern.pattern_name}
            priority = 7
        else:
            return None

        return LearningInsight(
            insight_type=LearningType.PATTERN_RECOGNITION,
            category=category,
            pattern_description=description,
            confidence_level=pattern.pattern_strength,
            supporting_evidence=[
                f"样本数量: {pattern.sample_size}",
                f"胜率: {pattern.success_rate:.1%}",
                f"平均收益: {pattern.avg_return:.2%}",
                f"统计显著性: p={pattern.statistical_significance:.3f}"
            ],
            recommended_adjustments=adjustments,
            impact_assessment="中等影响 - 优化特定条件下的表现",
            implementation_priority=priority
        )

    def _analyze_common_errors(self) -> List[LearningInsight]:
        """分析常见错误"""

        insights = []

        if len(self.trade_history) < self.learning_params['min_sample_size']:
            return insights

        failed_trades = [t for t in self.trade_history if t.profit_loss <= 0]

        if len(failed_trades) < 5:
            return insights

        # 分析止损触发频率
        stop_loss_exits = [t for t in failed_trades if t.exit_reason == 'stop_loss']
        if len(stop_loss_exits) / len(failed_trades) > 0.6:
            insights.append(LearningInsight(
                insight_type=LearningType.ERROR_CORRECTION,
                category=FeedbackCategory.FAILED_TRADE,
                pattern_description=f"止损触发频率过高({len(stop_loss_exits)/len(failed_trades):.1%})",
                confidence_level=0.8,
                supporting_evidence=[f"止损交易: {len(stop_loss_exits)}/{len(failed_trades)}"],
                recommended_adjustments={
                    'widen_stop_loss': True,
                    'improve_entry_timing': True,
                    'add_volatility_filter': True
                },
                impact_assessment="中等影响 - 减少不必要的止损",
                implementation_priority=6
            ))

        # 分析持仓时间过短的问题
        short_trades = [t for t in failed_trades if t.holding_duration.total_seconds() < 1800]  # 30分钟
        if len(short_trades) / len(failed_trades) > 0.4:
            insights.append(LearningInsight(
                insight_type=LearningType.ERROR_CORRECTION,
                category=FeedbackCategory.FAILED_TRADE,
                pattern_description=f"持仓时间过短导致亏损({len(short_trades)/len(failed_trades):.1%})",
                confidence_level=0.7,
                supporting_evidence=[f"短期交易: {len(short_trades)}/{len(failed_trades)}"],
                recommended_adjustments={
                    'improve_trend_confirmation': True,
                    'add_patience_filter': True,
                    'enhance_signal_strength_requirement': True
                },
                impact_assessment="中等影响 - 提高交易质量",
                implementation_priority=5
            ))

        return insights

    def _analyze_parameter_optimization_opportunities(self) -> List[LearningInsight]:
        """分析参数优化机会"""

        insights = []

        if len(self.trade_history) < 20:
            return insights

        # 分析风险回报比优化
        rr_ratios = [t.original_signal_quality.get('risk_reward_ratio', 1.0) for t in self.trade_history]
        avg_rr = np.mean(rr_ratios)

        if avg_rr < 1.5:
            insights.append(LearningInsight(
                insight_type=LearningType.PARAMETER_OPTIMIZATION,
                category=FeedbackCategory.PARTIAL_SUCCESS,
                pattern_description=f"平均风险回报比偏低({avg_rr:.2f})",
                confidence_level=0.8,
                supporting_evidence=[f"平均RR: {avg_rr:.2f}", f"样本数: {len(rr_ratios)}"],
                recommended_adjustments={
                    'increase_take_profit_targets': True,
                    'tighten_stop_loss_when_appropriate': True,
                    'improve_entry_precision': True
                },
                impact_assessment="高影响 - 提高风险调整收益",
                implementation_priority=8
            ))

        # 分析仓位大小优化
        successful_trades = [t for t in self.trade_history if t.profit_loss > 0]
        failed_trades = [t for t in self.trade_history if t.profit_loss <= 0]

        if successful_trades and failed_trades:
            avg_success_size = np.mean([t.lot_size for t in successful_trades])
            avg_fail_size = np.mean([t.lot_size for t in failed_trades])

            if avg_fail_size > avg_success_size * 1.2:
                insights.append(LearningInsight(
                    insight_type=LearningType.PARAMETER_OPTIMIZATION,
                    category=FeedbackCategory.FAILED_TRADE,
                    pattern_description="失败交易的仓位普遍较大",
                    confidence_level=0.7,
                    supporting_evidence=[
                        f"成功交易平均仓位: {avg_success_size:.3f}",
                        f"失败交易平均仓位: {avg_fail_size:.3f}"
                    ],
                    recommended_adjustments={
                        'reduce_position_size_for_uncertain_signals': True,
                        'implement_progressive_position_sizing': True
                    },
                    impact_assessment="中等影响 - 优化仓位管理",
                    implementation_priority=6
                ))

        return insights

    def _trigger_learning_update(self):
        """触发学习更新"""

        try:
            # 生成学习洞察
            insights = self.generate_learning_insights()

            # 更新学习状态
            self.last_learning_update = datetime.now()
            self.learning_cycle_count += 1

            # 记录学习事件
            self.logger.info(f"学习更新完成 - 周期{self.learning_cycle_count}, 洞察数量: {len(insights)}")

        except Exception as e:
            self.logger.error(f"学习更新失败: {e}")

    def get_learning_statistics(self) -> Dict:
        """获取学习统计信息"""

        return {
            'total_trades_analyzed': len(self.trade_history),
            'learning_cycles_completed': self.learning_cycle_count,
            'last_learning_update': self.last_learning_update.isoformat() if self.last_learning_update else None,
            'active_insights': len(self.learning_insights),
            'identified_patterns': len(self.performance_patterns),
            'parameter_adjustments': len(self.parameter_adjustments),
            'recent_accuracy': self.analyze_prediction_accuracy(20) if len(self.trade_history) >= 20 else None
        }

    def get_performance_summary(self, period_days: int = 30) -> Dict:
        """获取表现总结"""

        cutoff_date = datetime.now() - timedelta(days=period_days)
        recent_trades = [t for t in self.trade_history if t.entry_time >= cutoff_date]

        if not recent_trades:
            return {'status': 'no_recent_trades', 'period_days': period_days}

        # 基本统计
        total_trades = len(recent_trades)
        winning_trades = len([t for t in recent_trades if t.profit_loss > 0])
        win_rate = winning_trades / total_trades

        returns = [t.profit_loss_pct for t in recent_trades]
        total_return = sum(returns)
        avg_return = np.mean(returns)
        volatility = np.std(returns)

        # 风险指标
        max_drawdown = self._calculate_max_drawdown(recent_trades)
        sharpe_ratio = avg_return / volatility if volatility > 0 else 0

        # 交易质量指标
        avg_holding_time = np.mean([t.holding_duration.total_seconds() / 3600 for t in recent_trades])  # 小时
        avg_prediction_accuracy = np.mean([t.prediction_accuracy for t in recent_trades])
        avg_timing_accuracy = np.mean([t.timing_accuracy for t in recent_trades])

        return {
            'period_days': period_days,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_return': total_return,
            'avg_return_per_trade': avg_return,
            'volatility': volatility,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'avg_holding_time_hours': avg_holding_time,
            'avg_prediction_accuracy': avg_prediction_accuracy,
            'avg_timing_accuracy': avg_timing_accuracy,
            'analysis_date': datetime.now().isoformat()
        }

    def _calculate_max_drawdown(self, trades: List[TradeAnalysis]) -> float:
        """计算最大回撤"""

        if not trades:
            return 0.0

        # 按时间排序
        sorted_trades = sorted(trades, key=lambda x: x.entry_time)

        # 计算累积收益
        cumulative_returns = []
        cumulative = 0.0

        for trade in sorted_trades:
            cumulative += trade.profit_loss_pct
            cumulative_returns.append(cumulative)

        # 计算最大回撤
        peak = cumulative_returns[0]
        max_drawdown = 0.0

        for return_val in cumulative_returns:
            if return_val > peak:
                peak = return_val

            drawdown = peak - return_val
            if drawdown > max_drawdown:
                max_drawdown = drawdown

        return max_drawdown

    def export_learning_data(self, filepath: str = None) -> Dict:
        """导出学习数据"""

        if filepath is None:
            filepath = f"learning_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        export_data = {
            'export_timestamp': datetime.now().isoformat(),
            'learning_statistics': self.get_learning_statistics(),
            'performance_summary': self.get_performance_summary(),
            'learning_insights': [
                {
                    'insight_type': insight.insight_type.value,
                    'category': insight.category.value,
                    'pattern_description': insight.pattern_description,
                    'confidence_level': insight.confidence_level,
                    'supporting_evidence': insight.supporting_evidence,
                    'recommended_adjustments': insight.recommended_adjustments,
                    'impact_assessment': insight.impact_assessment,
                    'implementation_priority': insight.implementation_priority
                }
                for insight in self.learning_insights
            ],
            'performance_patterns': [
                {
                    'pattern_name': pattern.pattern_name,
                    'market_conditions': pattern.market_conditions,
                    'signal_characteristics': pattern.signal_characteristics,
                    'success_rate': pattern.success_rate,
                    'avg_return': pattern.avg_return,
                    'sample_size': pattern.sample_size,
                    'statistical_significance': pattern.statistical_significance,
                    'pattern_strength': pattern.pattern_strength
                }
                for pattern in self.performance_patterns
            ],
            'trade_summary': [
                {
                    'trade_id': trade.trade_id,
                    'entry_time': trade.entry_time.isoformat(),
                    'symbol': trade.symbol,
                    'action': trade.action,
                    'profit_loss_pct': trade.profit_loss_pct,
                    'prediction_accuracy': trade.prediction_accuracy,
                    'timing_accuracy': trade.timing_accuracy,
                    'overall_performance_score': trade.overall_performance_score
                }
                for trade in self.trade_history[-100:]  # 最近100笔交易
            ]
        }

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            return {'status': 'success', 'filepath': filepath, 'records_exported': len(export_data)}

        except Exception as e:
            return {'status': 'error', 'error': str(e)}
