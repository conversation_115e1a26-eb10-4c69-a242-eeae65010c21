#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一阶段优化测试脚本
测试JSON解析修复、预分析模板简化、删除未实现功能等优化效果
"""

import sys
import os
sys.path.append('.')

def test_should_perform_analysis_return_values():
    """测试should_perform_analysis函数返回值是否正确（应该返回2个值）"""
    print("=" * 60)
    print("测试1: should_perform_analysis函数返回值")
    print("=" * 60)

    try:
        from app.utils.multi_round_analysis import should_perform_analysis

        # 模拟测试数据
        test_data = {
            'timeframe15m': [
                {'close': 1.1000, 'ma13': 1.0995},
                {'close': 1.1010, 'ma13': 1.1000}
            ],
            'timeframe1h': [
                {'close': 1.1000, 'ma13': 1.0990},
                {'close': 1.1010, 'ma13': 1.0995}
            ],
            'current_price': 1.1010
        }

        result = should_perform_analysis(test_data)
        print(f"返回值数量: {len(result)}")
        print(f"返回值: {result}")

        if len(result) == 2:
            print("✅ 成功：函数返回2个值，下次分析间隔功能已删除")
            return True
        else:
            print("❌ 失败：函数仍返回3个值，需要进一步检查")
            return False

    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def test_template_simplification():
    """测试预分析模板是否已简化"""
    print("\n" + "=" * 60)
    print("测试2: 预分析模板简化")
    print("=" * 60)

    try:
        template_path = "templates/market_change_analyzer_template.txt"
        if os.path.exists(template_path):
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                line_count = len([line for line in lines if line.strip()])

            print(f"模板行数: {line_count}")
            print(f"模板字符数: {len(content)}")

            if line_count <= 50:
                print("✅ 成功：模板已简化到50行以内")
                return True
            else:
                print("❌ 失败：模板仍然过长，需要进一步简化")
                return False
        else:
            print("❌ 模板文件不存在")
            return False

    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def test_llm_client_optimization():
    """测试LLM客户端的max_tokens优化"""
    print("\n" + "=" * 60)
    print("测试3: LLM客户端max_tokens优化")
    print("=" * 60)

    try:
        # 检查llm_client.py中是否包含预分析检测逻辑
        with open('app/utils/llm_client.py', 'r', encoding='utf-8') as f:
            content = f.read()

        checks = [
            ('预分析检测', '预分析' in content and 'needAnalysis' in content),
            ('max_tokens=800', 'max_tokens = 800' in content),
            ('max_tokens=6000', '6000 if max_tokens is None else max_tokens' in content),
            ('检测逻辑', 'if "预分析" in prompt' in content)
        ]

        all_passed = True
        for check_name, passed in checks:
            if passed:
                print(f"✅ {check_name}: 已实现")
            else:
                print(f"❌ {check_name}: 未找到")
                all_passed = False

        return all_passed

    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def test_nextInterval_removal():
    """测试nextInterval字段是否已删除"""
    print("\n" + "=" * 60)
    print("测试4: nextInterval字段删除")
    print("=" * 60)

    try:
        # 检查multi_round_analysis.py中是否还有nextInterval相关代码
        with open('app/utils/multi_round_analysis.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查是否还有nextInterval相关代码
        nextInterval_count = content.count('nextInterval')
        next_interval_count = content.count('next_interval')

        print(f"nextInterval出现次数: {nextInterval_count}")
        print(f"next_interval出现次数: {next_interval_count}")

        if nextInterval_count == 0 and next_interval_count == 0:
            print("✅ 成功：所有nextInterval相关代码已删除")
            return True
        else:
            print("❌ 失败：仍有nextInterval相关代码残留")
            return False

    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def main():
    """运行所有测试"""
    print("🚀 开始第一阶段优化测试")
    print("测试内容：JSON解析修复、预分析模板简化、删除未实现功能")

    tests = [
        test_should_perform_analysis_return_values,
        test_template_simplification,
        test_llm_client_optimization,
        test_nextInterval_removal
    ]

    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append(False)

    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)

    passed = sum(results)
    total = len(results)

    print(f"通过测试: {passed}/{total}")

    if passed == total:
        print("🎉 所有测试通过！第一阶段优化成功完成")
    else:
        print("⚠️  部分测试未通过，需要进一步检查和修复")

    return passed == total

if __name__ == "__main__":
    main()
