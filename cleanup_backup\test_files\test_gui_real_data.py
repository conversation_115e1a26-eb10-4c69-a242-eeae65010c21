#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI真实数据功能
"""

import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_gui_data_access():
    """测试GUI数据访问功能"""
    
    print("🔍 测试GUI真实数据访问...")
    print("=" * 50)
    
    try:
        # 测试数据库连接
        print("1. 测试数据库连接...")
        from app.utils.db_client import test_connection, get_eurusd_min_data
        
        if test_connection():
            print("   ✅ 数据库连接成功")
        else:
            print("   ❌ 数据库连接失败")
            return False
        
        # 测试获取最新数据
        print("\n2. 测试获取最新EURUSD数据...")
        latest_data = get_eurusd_min_data(limit=1)
        
        if latest_data and len(latest_data) > 0:
            data = latest_data[0]
            print("   ✅ 成功获取最新数据:")
            print(f"      时间: {data['time']}")
            print(f"      价格: {data['close']}")
            print(f"      最高: {data['high']}")
            print(f"      最低: {data['low']}")
            print(f"      成交量: {data['volume']}")
        else:
            print("   ❌ 无法获取最新数据")
            return False
        
        # 测试技术指标计算
        print("\n3. 测试技术指标计算...")
        indicator_data = get_eurusd_min_data(limit=100)
        
        if len(indicator_data) >= 20:
            prices = [float(d['close']) for d in indicator_data]
            
            # 计算MA20
            import numpy as np
            ma_20 = np.mean(prices[:20])
            print(f"   ✅ MA20: {ma_20:.5f}")
            
            # 计算简单RSI
            if len(prices) >= 14:
                deltas = [prices[i] - prices[i+1] for i in range(13)]
                gains = [d if d > 0 else 0 for d in deltas]
                losses = [-d if d < 0 else 0 for d in deltas]
                
                avg_gain = sum(gains) / 14
                avg_loss = sum(losses) / 14
                
                if avg_loss > 0:
                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))
                    print(f"   ✅ RSI: {rsi:.1f}")
                else:
                    print("   ⚠️ RSI: 无法计算 (avg_loss = 0)")
            
            print("   ✅ 技术指标计算成功")
        else:
            print("   ❌ 数据不足，无法计算技术指标")
            return False
        
        # 测试系统组件检查
        print("\n4. 测试系统组件检查...")
        components = {
            'data_processor': 'app.utils.db_client',
            'market_analyzer': 'app.core.market_adaptive_system',
            'pre_analyzer': 'app.utils.multi_round_analysis',
            'llm_analyzer': 'app.utils.llm_client',
            'risk_manager': 'app.core.risk_management',
            'portfolio_manager': 'app.core.portfolio_management_system'
        }
        
        component_status = {}
        for comp_name, module_path in components.items():
            try:
                if comp_name == 'data_processor':
                    from app.utils import db_client
                    component_status[comp_name] = True
                elif comp_name == 'market_analyzer':
                    from app.core.market_adaptive_system import MarketAdaptiveSystem
                    component_status[comp_name] = True
                elif comp_name == 'pre_analyzer':
                    from app.utils.multi_round_analysis import should_perform_analysis
                    component_status[comp_name] = True
                elif comp_name == 'llm_analyzer':
                    from app.utils import llm_client
                    component_status[comp_name] = True
                elif comp_name == 'risk_manager':
                    from app.core.risk_management import AdvancedRiskManager
                    component_status[comp_name] = True
                elif comp_name == 'portfolio_manager':
                    from app.core.portfolio_management_system import PortfolioManager
                    component_status[comp_name] = True
                
                print(f"   ✅ {comp_name}: 可用")
            except Exception as e:
                component_status[comp_name] = False
                print(f"   ❌ {comp_name}: 不可用 ({e})")
        
        online_count = sum(1 for status in component_status.values() if status)
        total_count = len(component_status)
        print(f"\n   组件状态: {online_count}/{total_count} 在线")
        
        # 总结
        print(f"\n🎉 GUI真实数据测试完成!")
        print(f"   ✅ 数据库连接: 正常")
        print(f"   ✅ 数据获取: 正常")
        print(f"   ✅ 技术指标: 正常")
        print(f"   ✅ 组件检查: {online_count}/{total_count}")
        print(f"\n💡 GUI现在可以显示真实的EURUSD数据!")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_performance():
    """测试GUI性能"""
    
    print("\n⚡ 测试GUI数据获取性能...")
    print("=" * 50)
    
    try:
        from app.utils.db_client import get_eurusd_min_data
        import time
        
        # 测试不同数据量的获取性能
        test_limits = [1, 10, 100, 500]
        
        for limit in test_limits:
            start_time = time.time()
            data = get_eurusd_min_data(limit=limit)
            end_time = time.time()
            
            duration = (end_time - start_time) * 1000  # 转换为毫秒
            
            print(f"   获取{limit:3d}条数据: {duration:6.1f}ms")
        
        print("\n💡 性能建议:")
        print("   - GUI每5秒更新一次，获取1条最新数据")
        print("   - 技术指标计算使用100条历史数据")
        print("   - 性能表现良好，可以实时更新")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🚀 开始GUI真实数据测试...")
    
    # 测试数据访问
    if not test_gui_data_access():
        print("❌ 数据访问测试失败")
        return
    
    # 测试性能
    if not test_gui_performance():
        print("❌ 性能测试失败")
        return
    
    print("\n🎉 所有测试通过!")
    print("GUI可视化仪表板现在使用真实的EURUSD数据!")

if __name__ == "__main__":
    main()
