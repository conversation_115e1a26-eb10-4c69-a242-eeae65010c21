#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轻量级优化系统
专为微型服务器设计的资源监控、缓存优化和轻量级学习系统
"""

import os
import sys
import psutil
import gc
import time
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from collections import defaultdict, deque
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

@dataclass
class SystemHealth:
    """系统健康状态"""
    timestamp: datetime
    memory_percent: float
    cpu_percent: float
    disk_percent: float
    memory_available_mb: float
    active_connections: int
    system_status: str  # 'healthy', 'warning', 'critical'

@dataclass
class PerformanceMetrics:
    """性能指标"""
    avg_response_time: float
    successful_operations: int
    failed_operations: int
    cache_hit_rate: float
    memory_efficiency: float

class ResourceMonitor:
    """资源监控器"""

    def __init__(self):
        # 阈值设置（适合微型服务器）
        self.memory_warning_threshold = 70    # 内存警告阈值
        self.memory_critical_threshold = 85   # 内存危险阈值
        self.cpu_warning_threshold = 80       # CPU警告阈值
        self.cpu_critical_threshold = 95      # CPU危险阈值
        self.disk_warning_threshold = 80      # 磁盘警告阈值

        # 监控历史
        self.health_history = deque(maxlen=100)  # 保留最近100次检查
        self.alert_count = defaultdict(int)

        # 监控线程
        self.monitoring = False
        self.monitor_thread = None

        self.logger = logging.getLogger(__name__)

    def get_current_health(self) -> SystemHealth:
        """获取当前系统健康状态"""

        # 获取系统资源使用情况
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        disk = psutil.disk_usage('/')

        # 计算可用内存（MB）
        memory_available_mb = memory.available / (1024 * 1024)

        # 获取网络连接数（简化）
        try:
            connections = len(psutil.net_connections())
        except:
            connections = 0

        # 判断系统状态
        status = self._determine_system_status(
            memory.percent, cpu_percent, disk.percent
        )

        health = SystemHealth(
            timestamp=datetime.now(),
            memory_percent=memory.percent,
            cpu_percent=cpu_percent,
            disk_percent=disk.percent,
            memory_available_mb=memory_available_mb,
            active_connections=connections,
            system_status=status
        )

        # 记录历史
        self.health_history.append(health)

        return health

    def _determine_system_status(self, memory_pct: float, cpu_pct: float, disk_pct: float) -> str:
        """判断系统状态"""

        if (memory_pct >= self.memory_critical_threshold or
            cpu_pct >= self.cpu_critical_threshold or
            disk_pct >= self.disk_warning_threshold):
            return 'critical'
        elif (memory_pct >= self.memory_warning_threshold or
              cpu_pct >= self.cpu_warning_threshold):
            return 'warning'
        else:
            return 'healthy'

    def start_monitoring(self, interval: int = 30):
        """开始监控"""

        if self.monitoring:
            return

        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info(f"资源监控已启动，检查间隔: {interval}秒")

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("资源监控已停止")

    def _monitoring_loop(self, interval: int):
        """监控循环"""

        while self.monitoring:
            try:
                health = self.get_current_health()

                # 检查是否需要采取行动
                if health.system_status == 'critical':
                    self._handle_critical_situation(health)
                elif health.system_status == 'warning':
                    self._handle_warning_situation(health)

                time.sleep(interval)

            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(interval)

    def _handle_critical_situation(self, health: SystemHealth):
        """处理危险情况"""

        self.alert_count['critical'] += 1
        self.logger.warning(f"系统资源危险: 内存{health.memory_percent:.1f}%, CPU{health.cpu_percent:.1f}%")

        # 触发紧急清理
        self._emergency_cleanup()

        # 如果连续多次危险，考虑重启服务
        if self.alert_count['critical'] >= 3:
            self.logger.critical("系统持续处于危险状态，建议检查系统负载")
            self.alert_count['critical'] = 0  # 重置计数

    def _handle_warning_situation(self, health: SystemHealth):
        """处理警告情况"""

        self.alert_count['warning'] += 1
        self.logger.info(f"系统资源警告: 内存{health.memory_percent:.1f}%, CPU{health.cpu_percent:.1f}%")

        # 触发优化措施
        self._optimize_resources()

    def _emergency_cleanup(self):
        """紧急清理"""

        # 强制垃圾回收
        gc.collect()

        # 清理缓存（如果有缓存系统）
        try:
            from app.core.lightweight_optimization_system import cache_manager
            cache_manager.emergency_clear()
        except:
            pass

        self.logger.info("执行紧急资源清理")

    def _optimize_resources(self):
        """优化资源使用"""

        # 执行垃圾回收
        gc.collect()

        # 清理部分缓存
        try:
            from app.core.lightweight_optimization_system import cache_manager
            cache_manager.partial_clear()
        except:
            pass

        self.logger.info("执行资源优化")

    def get_health_summary(self) -> Dict:
        """获取健康状态摘要"""

        if not self.health_history:
            return {'status': 'no_data'}

        recent_health = list(self.health_history)[-10:]  # 最近10次检查

        avg_memory = sum(h.memory_percent for h in recent_health) / len(recent_health)
        avg_cpu = sum(h.cpu_percent for h in recent_health) / len(recent_health)

        current = self.health_history[-1]

        return {
            'current_status': current.system_status,
            'current_memory': current.memory_percent,
            'current_cpu': current.cpu_percent,
            'available_memory_mb': current.memory_available_mb,
            'avg_memory_10min': avg_memory,
            'avg_cpu_10min': avg_cpu,
            'alert_counts': dict(self.alert_count),
            'last_check': current.timestamp.isoformat()
        }

class LightweightCacheManager:
    """轻量级缓存管理器"""

    def __init__(self, max_memory_mb: int = 50):
        self.max_memory_mb = max_memory_mb
        self.cache = {}
        self.access_times = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0
        }

        self.logger = logging.getLogger(__name__)

    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""

        if key in self.cache:
            self.access_times[key] = time.time()
            self.cache_stats['hits'] += 1
            return self.cache[key]
        else:
            self.cache_stats['misses'] += 1
            return None

    def set(self, key: str, value: Any, ttl: int = 300):
        """设置缓存数据"""

        # 检查内存使用
        if self._get_cache_size_mb() > self.max_memory_mb:
            self._evict_old_entries()

        self.cache[key] = {
            'data': value,
            'expires': time.time() + ttl,
            'size': sys.getsizeof(value)
        }
        self.access_times[key] = time.time()

    def _get_cache_size_mb(self) -> float:
        """获取缓存大小（MB）"""
        total_size = sum(
            entry['size'] for entry in self.cache.values()
        )
        return total_size / (1024 * 1024)

    def _evict_old_entries(self):
        """清理旧条目"""

        current_time = time.time()

        # 清理过期条目
        expired_keys = [
            key for key, entry in self.cache.items()
            if entry['expires'] < current_time
        ]

        for key in expired_keys:
            del self.cache[key]
            del self.access_times[key]
            self.cache_stats['evictions'] += 1

        # 如果还是太大，清理最久未访问的条目
        if self._get_cache_size_mb() > self.max_memory_mb:
            # 按访问时间排序，删除最久未访问的
            sorted_keys = sorted(
                self.access_times.keys(),
                key=lambda k: self.access_times[k]
            )

            # 删除最久未访问的25%
            keys_to_remove = sorted_keys[:len(sorted_keys)//4]
            for key in keys_to_remove:
                if key in self.cache:
                    del self.cache[key]
                del self.access_times[key]
                self.cache_stats['evictions'] += 1

    def partial_clear(self):
        """部分清理缓存"""
        # 清理50%的缓存
        keys_to_remove = list(self.cache.keys())[::2]  # 每隔一个删除
        for key in keys_to_remove:
            if key in self.cache:
                del self.cache[key]
            if key in self.access_times:
                del self.access_times[key]

        self.logger.info(f"部分清理缓存，删除{len(keys_to_remove)}个条目")

    def emergency_clear(self):
        """紧急清理所有缓存"""
        self.cache.clear()
        self.access_times.clear()
        self.logger.warning("紧急清理所有缓存")

    def get_stats(self) -> Dict:
        """获取缓存统计"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = self.cache_stats['hits'] / total_requests if total_requests > 0 else 0

        return {
            'cache_size_mb': self._get_cache_size_mb(),
            'entry_count': len(self.cache),
            'hit_rate': hit_rate,
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'evictions': self.cache_stats['evictions']
        }

class LightweightMLPredictor:
    """轻量级机器学习预测器"""

    def __init__(self):
        # 使用简单的统计模型，避免重型ML库
        self.trend_patterns = defaultdict(list)
        self.success_rates = defaultdict(float)
        self.market_conditions = deque(maxlen=1000)  # 保留最近1000个市场状态

        # 简单的特征权重
        self.feature_weights = {
            'rsi_trend': 0.3,
            'ma_alignment': 0.4,
            'volume_trend': 0.2,
            'volatility': 0.1
        }

        self.logger = logging.getLogger(__name__)

    def extract_simple_features(self, market_data: Dict) -> Dict:
        """提取简单特征"""

        features = {}

        # RSI趋势特征
        rsi = market_data.get('rsi', 50)
        if rsi > 70:
            features['rsi_trend'] = 'overbought'
        elif rsi < 30:
            features['rsi_trend'] = 'oversold'
        else:
            features['rsi_trend'] = 'neutral'

        # 均线排列特征
        current_price = market_data.get('current_price', 0)
        ma_20 = market_data.get('ma_20', current_price)
        ma_50 = market_data.get('ma_50', current_price)

        if current_price > ma_20 > ma_50:
            features['ma_alignment'] = 'bullish'
        elif current_price < ma_20 < ma_50:
            features['ma_alignment'] = 'bearish'
        else:
            features['ma_alignment'] = 'mixed'

        # 成交量趋势
        volume = market_data.get('volume', 1000)
        avg_volume = market_data.get('avg_volume', 1000)
        volume_ratio = volume / avg_volume if avg_volume > 0 else 1

        if volume_ratio > 1.5:
            features['volume_trend'] = 'high'
        elif volume_ratio < 0.5:
            features['volume_trend'] = 'low'
        else:
            features['volume_trend'] = 'normal'

        # 波动率特征
        atr = market_data.get('atr', 0.001)
        if atr > 0.002:
            features['volatility'] = 'high'
        elif atr < 0.0005:
            features['volatility'] = 'low'
        else:
            features['volatility'] = 'normal'

        return features

    def predict_trend_probability(self, market_data: Dict) -> Dict:
        """预测趋势概率"""

        features = self.extract_simple_features(market_data)
        feature_key = self._features_to_key(features)

        # 基于历史模式计算概率
        if feature_key in self.success_rates:
            base_probability = self.success_rates[feature_key]
        else:
            base_probability = 0.5  # 默认50%概率

        # 基于特征权重调整
        bullish_score = 0
        bearish_score = 0

        # RSI影响
        if features['rsi_trend'] == 'oversold':
            bullish_score += self.feature_weights['rsi_trend']
        elif features['rsi_trend'] == 'overbought':
            bearish_score += self.feature_weights['rsi_trend']

        # 均线影响
        if features['ma_alignment'] == 'bullish':
            bullish_score += self.feature_weights['ma_alignment']
        elif features['ma_alignment'] == 'bearish':
            bearish_score += self.feature_weights['ma_alignment']

        # 成交量影响
        if features['volume_trend'] == 'high':
            # 高成交量增强当前趋势
            if bullish_score > bearish_score:
                bullish_score += self.feature_weights['volume_trend']
            else:
                bearish_score += self.feature_weights['volume_trend']

        # 计算最终概率
        total_score = bullish_score + bearish_score
        if total_score > 0:
            bullish_prob = bullish_score / total_score
        else:
            bullish_prob = base_probability

        return {
            'bullish_probability': bullish_prob,
            'bearish_probability': 1 - bullish_prob,
            'confidence': min(total_score, 1.0),
            'features_used': features,
            'base_probability': base_probability
        }

    def learn_from_trade_result(self, market_data: Dict, trade_result: Dict):
        """从交易结果学习"""

        features = self.extract_simple_features(market_data)
        feature_key = self._features_to_key(features)

        # 判断交易是否成功
        profit = trade_result.get('profit_loss', 0)
        is_successful = profit > 0

        # 更新成功率
        if feature_key not in self.trend_patterns:
            self.trend_patterns[feature_key] = []

        self.trend_patterns[feature_key].append(is_successful)

        # 保持最近100个结果
        if len(self.trend_patterns[feature_key]) > 100:
            self.trend_patterns[feature_key] = self.trend_patterns[feature_key][-100:]

        # 计算成功率
        successes = sum(self.trend_patterns[feature_key])
        total = len(self.trend_patterns[feature_key])
        self.success_rates[feature_key] = successes / total

        # 记录市场条件
        self.market_conditions.append({
            'timestamp': datetime.now(),
            'features': features,
            'result': is_successful,
            'profit': profit
        })

        self.logger.info(f"学习更新: {feature_key} 成功率: {self.success_rates[feature_key]:.2%}")

    def _features_to_key(self, features: Dict) -> str:
        """将特征转换为键"""
        return f"{features['rsi_trend']}_{features['ma_alignment']}_{features['volume_trend']}_{features['volatility']}"

    def get_learning_stats(self) -> Dict:
        """获取学习统计"""

        total_patterns = len(self.success_rates)
        avg_success_rate = sum(self.success_rates.values()) / total_patterns if total_patterns > 0 else 0

        # 最佳和最差模式
        best_pattern = max(self.success_rates.items(), key=lambda x: x[1]) if self.success_rates else None
        worst_pattern = min(self.success_rates.items(), key=lambda x: x[1]) if self.success_rates else None

        return {
            'total_patterns_learned': total_patterns,
            'avg_success_rate': avg_success_rate,
            'best_pattern': best_pattern,
            'worst_pattern': worst_pattern,
            'total_trades_analyzed': len(self.market_conditions),
            'learning_data_size_kb': sys.getsizeof(self.trend_patterns) / 1024
        }

class AutoRecoverySystem:
    """自动恢复系统"""

    def __init__(self):
        self.error_counts = defaultdict(int)
        self.last_errors = defaultdict(datetime)
        self.recovery_actions = []

        # 错误阈值
        self.max_errors_per_hour = 10
        self.critical_error_threshold = 5

        self.logger = logging.getLogger(__name__)

    def handle_error(self, error_type: str, error_message: str, context: Dict = None):
        """处理错误"""

        current_time = datetime.now()

        # 记录错误
        self.error_counts[error_type] += 1
        self.last_errors[error_type] = current_time

        # 清理1小时前的错误计数
        self._cleanup_old_errors()

        # 判断是否需要恢复行动
        if self.error_counts[error_type] >= self.critical_error_threshold:
            self._trigger_recovery_action(error_type, error_message, context)

        self.logger.warning(f"错误处理: {error_type} - {error_message}")

    def _cleanup_old_errors(self):
        """清理旧错误记录"""

        one_hour_ago = datetime.now() - timedelta(hours=1)

        for error_type in list(self.error_counts.keys()):
            if self.last_errors[error_type] < one_hour_ago:
                self.error_counts[error_type] = 0

    def _trigger_recovery_action(self, error_type: str, error_message: str, context: Dict):
        """触发恢复行动"""

        recovery_action = {
            'timestamp': datetime.now(),
            'error_type': error_type,
            'error_message': error_message,
            'context': context,
            'action_taken': None
        }

        # 根据错误类型采取不同行动
        if 'database' in error_type.lower():
            recovery_action['action_taken'] = 'database_reconnect'
            self._recover_database_connection()
        elif 'memory' in error_type.lower():
            recovery_action['action_taken'] = 'memory_cleanup'
            self._recover_memory_issues()
        elif 'llm' in error_type.lower():
            recovery_action['action_taken'] = 'llm_fallback'
            self._recover_llm_issues()
        else:
            recovery_action['action_taken'] = 'general_cleanup'
            self._general_recovery()

        self.recovery_actions.append(recovery_action)

        # 保持最近50个恢复记录
        if len(self.recovery_actions) > 50:
            self.recovery_actions = self.recovery_actions[-50:]

        self.logger.info(f"执行恢复行动: {recovery_action['action_taken']}")

    def _recover_database_connection(self):
        """恢复数据库连接"""
        try:
            # 清理数据库连接池
            from app.utils.db_client import get_connection
            # 这里可以添加重新连接逻辑
            pass
        except Exception as e:
            self.logger.error(f"数据库恢复失败: {e}")

    def _recover_memory_issues(self):
        """恢复内存问题"""
        # 强制垃圾回收
        gc.collect()

        # 清理缓存
        cache_manager.emergency_clear()

        self.logger.info("执行内存恢复操作")

    def _recover_llm_issues(self):
        """恢复LLM问题"""
        # 这里可以添加LLM重连或切换备用模型的逻辑
        self.logger.info("执行LLM恢复操作")

    def _general_recovery(self):
        """通用恢复"""
        gc.collect()
        cache_manager.partial_clear()
        self.logger.info("执行通用恢复操作")

    def get_recovery_stats(self) -> Dict:
        """获取恢复统计"""

        recent_actions = [a for a in self.recovery_actions
                         if a['timestamp'] > datetime.now() - timedelta(hours=24)]

        action_types = defaultdict(int)
        for action in recent_actions:
            action_types[action['action_taken']] += 1

        return {
            'total_errors_24h': sum(self.error_counts.values()),
            'recovery_actions_24h': len(recent_actions),
            'action_types': dict(action_types),
            'error_types': dict(self.error_counts),
            'last_recovery': self.recovery_actions[-1]['timestamp'].isoformat() if self.recovery_actions else None
        }

# 创建全局实例
resource_monitor = ResourceMonitor()
cache_manager = LightweightCacheManager(max_memory_mb=30)  # 限制30MB缓存
ml_predictor = LightweightMLPredictor()
auto_recovery = AutoRecoverySystem()
