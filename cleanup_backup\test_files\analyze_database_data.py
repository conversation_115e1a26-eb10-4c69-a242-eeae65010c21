#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析数据库中的实际数据
检查pizza_quotes数据库中各货币对的数据情况
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_database_data():
    """分析数据库数据"""
    print("📊 数据库数据分析")
    print("=" * 60)
    
    try:
        # 1. 测试数据库连接
        print("🔌 测试数据库连接...")
        from app.utils.db_client import get_connection, test_connection
        
        if test_connection():
            print("   ✅ 数据库连接成功")
        else:
            print("   ❌ 数据库连接失败")
            return False
        
        # 2. 检查数据库表结构
        print("\n📋 检查数据库表结构...")
        connection = get_connection()
        
        with connection.cursor() as cursor:
            # 查看所有表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print(f"   数据库中共有 {len(tables)} 个表:")
            for table in tables:
                table_name = list(table.values())[0]
                print(f"     - {table_name}")
        
        # 3. 专门分析EURUSD数据
        print("\n💱 分析EURUSD数据...")
        
        with connection.cursor() as cursor:
            # 检查min_quote_eurusd表结构
            cursor.execute("DESCRIBE min_quote_eurusd")
            columns = cursor.fetchall()
            
            print("   min_quote_eurusd表结构:")
            for col in columns:
                print(f"     {col['Field']}: {col['Type']}")
            
            # 检查数据量
            cursor.execute("SELECT COUNT(*) as total FROM min_quote_eurusd")
            total_count = cursor.fetchone()['total']
            print(f"\n   总数据量: {total_count:,} 条")
            
            # 检查时间范围
            cursor.execute("""
                SELECT 
                    MIN(time_date_str) as earliest,
                    MAX(time_date_str) as latest
                FROM min_quote_eurusd
            """)
            time_range = cursor.fetchone()
            print(f"   时间范围: {time_range['earliest']} 到 {time_range['latest']}")
            
            # 检查最近的数据
            cursor.execute("""
                SELECT 
                    time_date_str,
                    price,
                    max as high,
                    min as low,
                    volume
                FROM min_quote_eurusd 
                ORDER BY time_min_int DESC 
                LIMIT 10
            """)
            recent_data = cursor.fetchall()
            
            print("\n   最近10条数据:")
            for data in recent_data:
                print(f"     {data['time_date_str']}: 价格={data['price']:.5f}, 高={data['high']:.5f}, 低={data['low']:.5f}, 量={data['volume']}")
        
        # 4. 检查其他货币对数据表
        print("\n🌍 检查其他货币对数据表...")
        
        # 可能的货币对表名
        possible_tables = [
            'min_quote_gbpusd', 'min_quote_audusd', 'min_quote_nzdusd',
            'min_quote_usdchf', 'min_quote_usdcad', 'min_quote_usdjpy',
            'min_quote_gold', 'min_quote_xauusd'
        ]
        
        existing_tables = []
        
        with connection.cursor() as cursor:
            for table_name in possible_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count = cursor.fetchone()['count']
                    existing_tables.append((table_name, count))
                    print(f"   ✅ {table_name}: {count:,} 条数据")
                except Exception as e:
                    print(f"   ❌ {table_name}: 表不存在")
        
        # 5. 分析数据质量
        print("\n🔍 分析数据质量...")
        
        with connection.cursor() as cursor:
            # 检查EURUSD数据的连续性
            cursor.execute("""
                SELECT 
                    DATE(time_date_str) as date,
                    COUNT(*) as records_per_day,
                    MIN(price) as daily_low,
                    MAX(price) as daily_high,
                    AVG(price) as daily_avg
                FROM min_quote_eurusd 
                WHERE time_date_str >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY DATE(time_date_str)
                ORDER BY date DESC
            """)
            daily_stats = cursor.fetchall()
            
            print("   最近7天的数据统计:")
            for stat in daily_stats:
                print(f"     {stat['date']}: {stat['records_per_day']}条, 低={stat['daily_low']:.5f}, 高={stat['daily_high']:.5f}, 均={stat['daily_avg']:.5f}")
        
        # 6. 检查数据的实时性
        print("\n⏰ 检查数据实时性...")
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    time_date_str,
                    TIMESTAMPDIFF(MINUTE, time_date_str, NOW()) as minutes_ago
                FROM min_quote_eurusd 
                ORDER BY time_min_int DESC 
                LIMIT 1
            """)
            latest = cursor.fetchone()
            
            if latest:
                print(f"   最新数据时间: {latest['time_date_str']}")
                print(f"   距离现在: {latest['minutes_ago']} 分钟前")
                
                if latest['minutes_ago'] < 60:
                    print("   ✅ 数据较为实时")
                elif latest['minutes_ago'] < 1440:  # 24小时
                    print("   ⚠️ 数据有一定延迟")
                else:
                    print("   ❌ 数据延迟较大")
        
        # 7. 生成数据源配置建议
        print("\n💡 数据源配置建议...")
        
        print("   基于分析结果的建议:")
        print("   1. EURUSD数据充足，可作为主要测试品种")
        
        if len(existing_tables) > 1:
            print(f"   2. 发现{len(existing_tables)}个货币对数据表，可支持多货币对分析")
            print("   3. 建议优先使用数据量较大的货币对进行系统测试")
        else:
            print("   2. 其他货币对数据表较少，建议重点优化EURUSD分析")
        
        print("   4. 建议实现数据缓存机制以提高查询性能")
        print("   5. 可以基于历史数据进行回测和策略优化")
        
        # 8. 测试数据获取性能
        print("\n⚡ 测试数据获取性能...")
        
        start_time = datetime.now()
        
        with connection.cursor() as cursor:
            # 测试获取100条最新数据的性能
            cursor.execute("""
                SELECT 
                    time_date_str,
                    price,
                    max,
                    min,
                    volume
                FROM min_quote_eurusd 
                ORDER BY time_min_int DESC 
                LIMIT 100
            """)
            data_100 = cursor.fetchall()
        
        query_time_100 = (datetime.now() - start_time).total_seconds()
        
        start_time = datetime.now()
        
        with connection.cursor() as cursor:
            # 测试获取1000条数据的性能
            cursor.execute("""
                SELECT 
                    time_date_str,
                    price,
                    max,
                    min,
                    volume
                FROM min_quote_eurusd 
                ORDER BY time_min_int DESC 
                LIMIT 1000
            """)
            data_1000 = cursor.fetchall()
        
        query_time_1000 = (datetime.now() - start_time).total_seconds()
        
        print(f"   获取100条数据耗时: {query_time_100:.3f}秒")
        print(f"   获取1000条数据耗时: {query_time_1000:.3f}秒")
        
        if query_time_1000 < 1.0:
            print("   ✅ 数据库查询性能良好")
        else:
            print("   ⚠️ 数据库查询性能一般，建议优化")
        
        connection.close()
        
        print("\n🎉 数据库数据分析完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 数据分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_optimized_data_adapter():
    """基于分析结果创建优化的数据适配器"""
    print("\n🔧 创建优化的数据适配器...")
    
    try:
        from app.utils.db_client import get_connection
        
        # 检查实际可用的货币对表
        connection = get_connection()
        available_tables = []
        
        # 检查各货币对表
        currency_tables = {
            'EURUSD': 'min_quote_eurusd',
            'GBPUSD': 'min_quote_gbpusd', 
            'AUDUSD': 'min_quote_audusd',
            'NZDUSD': 'min_quote_nzdusd',
            'USDCHF': 'min_quote_usdchf',
            'USDCAD': 'min_quote_usdcad',
            'USDJPY': 'min_quote_usdjpy',
            'GOLD': 'min_quote_gold'
        }
        
        with connection.cursor() as cursor:
            for symbol, table_name in currency_tables.items():
                try:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count = cursor.fetchone()['count']
                    if count > 1000:  # 只有数据量足够的才加入
                        available_tables.append(symbol)
                        print(f"   ✅ {symbol}: {count:,} 条数据")
                except:
                    print(f"   ❌ {symbol}: 表不存在或数据不足")
        
        connection.close()
        
        print(f"\n   实际可用的货币对: {available_tables}")
        print(f"   建议系统支持: {len(available_tables)} 个货币对")
        
        return available_tables
        
    except Exception as e:
        print(f"   ❌ 检查失败: {e}")
        return ['EURUSD']  # 至少返回EURUSD

def test_real_data_integration():
    """测试真实数据集成"""
    print("\n🔄 测试真实数据集成...")
    
    try:
        from app.utils.db_client import get_eurusd_min_data
        
        # 获取最近100条EURUSD数据
        print("   获取最近100条EURUSD数据...")
        data = get_eurusd_min_data(limit=100)
        
        if data:
            print(f"   ✅ 成功获取 {len(data)} 条数据")
            
            # 显示数据样本
            latest = data[0]  # 最新的数据
            print(f"   最新数据: {latest['time']} - 价格: {latest['close']:.5f}")
            
            # 计算基本统计
            prices = [float(d['close']) for d in data]
            avg_price = sum(prices) / len(prices)
            max_price = max(prices)
            min_price = min(prices)
            
            print(f"   价格统计: 均值={avg_price:.5f}, 最高={max_price:.5f}, 最低={min_price:.5f}")
            
            # 测试技术指标计算
            print("   测试技术指标计算...")
            
            # 简单移动平均
            if len(prices) >= 20:
                ma_20 = sum(prices[:20]) / 20
                print(f"   MA20: {ma_20:.5f}")
            
            # 价格变化
            if len(prices) >= 2:
                price_change = prices[0] - prices[1]
                price_change_pct = (price_change / prices[1]) * 100
                print(f"   最新变化: {price_change:+.5f} ({price_change_pct:+.3f}%)")
            
            return True
        else:
            print("   ❌ 未能获取数据")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始数据库数据分析")
    
    # 执行数据库分析
    success = analyze_database_data()
    
    if success:
        # 创建优化的数据适配器配置
        available_symbols = create_optimized_data_adapter()
        
        # 测试真实数据集成
        test_real_data_integration()
        
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🎉 数据库分析完成！")
        print("基于分析结果，可以进行以下优化:")
        print("1. 更新数据源适配器以支持实际可用的货币对")
        print("2. 基于真实数据优化技术指标计算")
        print("3. 实现数据缓存机制提高性能")
        print("4. 基于历史数据进行策略回测和优化")
    else:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ 数据库分析失败")
