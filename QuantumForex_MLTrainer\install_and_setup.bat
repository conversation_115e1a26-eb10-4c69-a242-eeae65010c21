@echo off
chcp 65001 >nul
echo.
echo ========================================
echo  QuantumForex MLTrainer 安装和设置
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 管理员权限已获取
) else (
    echo ⚠️  建议以管理员身份运行以设置文件共享
    echo    右键点击此文件，选择"以管理员身份运行"
    echo.
)

:: 设置工作目录
cd /d "%~dp0"
echo 📁 当前工作目录: %CD%

:: 检查Python
echo.
echo 🐍 检查Python环境...
python --version >nul 2>&1
if %errorLevel% == 0 (
    python --version
    echo ✅ Python已安装
) else (
    echo ❌ Python未安装或未添加到PATH
    echo    请先安装Python 3.8或更高版本
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 检查pip
echo.
echo 📦 检查pip...
python -m pip --version >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ pip可用
) else (
    echo ❌ pip不可用
    pause
    exit /b 1
)

:: 升级pip
echo.
echo 🔄 升级pip...
python -m pip install --upgrade pip
if %errorLevel% == 0 (
    echo ✅ pip升级完成
) else (
    echo ⚠️  pip升级失败，继续安装...
)

:: 安装依赖
echo.
echo 📦 安装依赖包...
if exist requirements.txt (
    python -m pip install -r requirements.txt
    if %errorLevel% == 0 (
        echo ✅ 依赖包安装完成
    ) else (
        echo ❌ 依赖包安装失败
        echo    请检查网络连接和requirements.txt文件
        pause
        exit /b 1
    )
) else (
    echo ❌ requirements.txt文件不存在
    pause
    exit /b 1
)

:: 运行Python设置脚本
echo.
echo 🔧 运行设置脚本...
python scripts/setup_training_server.py
if %errorLevel% == 0 (
    echo ✅ 设置脚本执行完成
) else (
    echo ❌ 设置脚本执行失败
    echo    请检查错误信息
)

:: 创建桌面快捷方式
echo.
echo 🔗 创建桌面快捷方式...
set "desktop=%USERPROFILE%\Desktop"
set "shortcut=%desktop%\QuantumForex MLTrainer.lnk"

:: 使用PowerShell创建快捷方式
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%shortcut%'); $Shortcut.TargetPath = '%CD%\start_training.bat'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.Description = 'QuantumForex MLTrainer'; $Shortcut.Save()"

if exist "%shortcut%" (
    echo ✅ 桌面快捷方式已创建
) else (
    echo ⚠️  桌面快捷方式创建失败
)

:: 显示网络信息
echo.
echo 🌐 网络配置信息:
echo    本地训练端IP: **************
echo    腾讯云交易端IP: **************
echo    连接类型: 互联网连接
echo    数据文件夹: C:\QuantumForex_MLTrainer_Data
ipconfig | findstr "IPv4"

:: 测试云服务器连接
echo.
echo 🔗 测试云服务器连接...
ping -n 1 ************** >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 云服务器网络可达: **************
) else (
    echo ⚠️  云服务器ping失败，请检查网络连接
)

:: 测试本地文件夹
echo.
echo 📁 检查本地数据文件夹...
if exist "C:\QuantumForex_MLTrainer_Data" (
    echo ✅ 数据文件夹已创建: C:\QuantumForex_MLTrainer_Data
    dir "C:\QuantumForex_MLTrainer_Data" /b
) else (
    echo ❌ 数据文件夹未创建
)

:: 完成信息
echo.
echo ========================================
echo  🎉 安装和设置完成！
echo ========================================
echo.
echo 📋 下一步操作:
echo    1. 双击桌面上的"QuantumForex MLTrainer"快捷方式
echo    2. 或者运行 start_training.bat
echo    3. 检查 C:\QuantumForex_Shared 文件夹中的模型文件
echo    4. 验证与交易端的连接
echo.
echo 📁 重要文件夹:
echo    - 数据文件夹: C:\QuantumForex_MLTrainer_Data
echo    - 模型文件: C:\QuantumForex_MLTrainer_Data\models
echo    - 上传文件夹: C:\QuantumForex_MLTrainer_Data\upload
echo    - 日志文件: C:\QuantumForex_MLTrainer_Data\logs
echo    - 备份文件: C:\QuantumForex_MLTrainer_Data\backup
echo.
echo 🔧 如果遇到问题:
echo    1. 检查防火墙设置
echo    2. 确保网络连接正常
echo    3. 查看日志文件获取详细信息
echo    4. 以管理员身份重新运行此脚本
echo.

pause
