"""
交易执行测试脚本
用于测试交易执行功能，使用预定义的交易指令
"""
import os
import sys
import time
from datetime import datetime

from app.services.forex_trading_service import execute_trade
from app.utils.mt4_client import mt4_client

def test_trade_execution():
    """测试交易执行功能"""
    try:
        print('=' * 50)
        print(f'开始测试交易执行功能，时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        print('=' * 50)

        # 步骤1: 确保MT4连接
        print('\n步骤1: 确保MT4连接')
        if not mt4_client.is_connected:
            print('MT4客户端未连接，尝试连接')
            connected = mt4_client.connect()
            if not connected:
                print('无法连接到MT4客户端，测试失败')
                return
        print('MT4连接正常')

        # 步骤2: 获取当前持仓
        print('\n步骤2: 获取当前持仓')
        positions_response = mt4_client.get_active_orders()
        positions = positions_response.get('orders', [])
        print(f'当前持仓数量: {len(positions)}')
        for position in positions:
            print(f'  - 订单ID: {position.get("order_id")}, 类型: {position.get("type")}, 货币对: {position.get("symbol")}, 手数: {position.get("lots")}, 盈亏: {position.get("profit")}')

        # 步骤3: 获取市场信息
        print('\n步骤3: 获取市场信息')
        market_info = mt4_client.get_market_info('EURUSD')
        if not market_info or market_info.get('status') != 'success':
            print('获取市场信息失败，测试终止')
            return

        current_price = float(market_info['data']['ask'])
        print(f'当前EURUSD价格: {current_price}')

        # 步骤4: 创建交易指令
        print('\n步骤4: 创建交易指令')

        # 创建一个买入市价单
        buy_market_order = {
            'action': 'BUY',
            'orderType': 'MARKET',
            'entryPrice': None,  # 市价单不需要入场价格
            'stopLoss': round(current_price - 0.0050, 5),  # 止损设置在当前价格下方50点
            'takeProfit': round(current_price + 0.0050, 5),  # 止盈设置在当前价格上方50点
            'riskLevel': 'LOW',  # 低风险
            'reasoning': '测试买入市价单'
        }

        # 创建一个卖出市价单
        sell_market_order = {
            'action': 'SELL',
            'orderType': 'MARKET',
            'entryPrice': None,  # 市价单不需要入场价格
            'stopLoss': round(current_price + 0.0050, 5),  # 止损设置在当前价格上方50点
            'takeProfit': round(current_price - 0.0050, 5),  # 止盈设置在当前价格下方50点
            'riskLevel': 'LOW',  # 低风险
            'reasoning': '测试卖出市价单'
        }

        # 创建一个买入限价单
        buy_limit_order = {
            'action': 'BUY',
            'orderType': 'LIMIT',
            'entryPrice': round(current_price - 0.0030, 5),  # 限价设置在当前价格下方30点
            'stopLoss': round(current_price - 0.0080, 5),  # 止损设置在入场价格下方50点
            'takeProfit': round(current_price + 0.0050, 5),  # 止盈设置在当前价格上方50点
            'riskLevel': 'LOW',  # 低风险
            'reasoning': '测试买入限价单'
        }

        # 创建一个买入止损单
        buy_stop_order = {
            'action': 'BUY',
            'orderType': 'STOP',
            'entryPrice': round(current_price + 0.0030, 5),  # 止损单设置在当前价格上方30点
            'stopLoss': round(current_price - 0.0020, 5),  # 止损设置在当前价格下方20点
            'takeProfit': round(current_price + 0.0080, 5),  # 止盈设置在入场价格上方50点
            'riskLevel': 'LOW',  # 低风险
            'reasoning': '测试买入止损单'
        }

        # 选择要测试的交易指令
        trade_instructions = buy_stop_order  # 可以改为其他类型的订单进行测试
        print(f'交易指令: {trade_instructions}')

        # 步骤5: 执行交易
        print('\n步骤5: 执行交易')
        trade_result = execute_trade(trade_instructions)
        print(f'交易执行结果: {trade_result}')

        # 步骤6: 验证交易结果
        print('\n步骤6: 验证交易结果')
        if trade_result.get('success'):
            print('交易执行成功')

            # 获取新的持仓
            new_positions_response = mt4_client.get_active_orders()
            new_positions = new_positions_response.get('orders', [])
            print(f'新的持仓数量: {len(new_positions)}')

            # 检查是否有新增持仓
            if len(new_positions) > len(positions):
                print('持仓数量增加，交易确认成功')

                # 显示新增的持仓
                for position in new_positions:
                    if not any(p.get('order_id') == position.get('order_id') for p in positions):
                        print(f'  - 新增持仓: 订单ID: {position.get("order_id")}, 类型: {position.get("type")}, 货币对: {position.get("symbol")}, 手数: {position.get("lots")}, 开仓价: {position.get("open_price")}, 止损: {position.get("sl")}, 止盈: {position.get("tp")}')
            else:
                print('持仓数量未增加，可能交易未成功执行或已有相同方向的持仓')

                # 检查挂单
                if trade_instructions.get('orderType') in ['LIMIT', 'STOP']:
                    print('检查挂单...')
                    pending_orders_response = mt4_client.get_pending_orders()
                    pending_orders = pending_orders_response.get('orders', [])
                    print(f'挂单数量: {len(pending_orders)}')
                    for order in pending_orders:
                        print(f'  - 挂单: 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 货币对: {order.get("symbol")}, 手数: {order.get("lots")}, 价格: {order.get("open_price")}, 止损: {order.get("sl")}, 止盈: {order.get("tp")}')
        else:
            print(f'交易执行失败: {trade_result.get("message")}')

        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_trade_execution()
