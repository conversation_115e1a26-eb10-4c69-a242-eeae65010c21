#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据交易系统可视化仪表板
连接实际的交易系统数据，而不是模拟数据
"""

import os
import sys
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from flask import Flask, render_template_string, jsonify
import sqlite3

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__)

# 全局数据存储
dashboard_data = {
    'market_data': {},
    'analysis_history': [],
    'system_status': {
        'last_update': None,
        'components_status': {},
        'error_count': 0,
        'success_count': 0,
        'start_time': datetime.now()
    },
    'monitoring': False
}

def get_real_system_data():
    """获取真实系统数据"""

    try:
        # 1. 检查系统组件状态
        components_status = check_system_components()

        # 2. 获取最新市场数据
        market_data = get_latest_market_data()

        # 3. 获取分析历史
        analysis_history = get_analysis_history()

        # 4. 获取系统统计
        system_stats = get_system_statistics()

        # 更新全局数据
        dashboard_data['system_status']['components_status'] = components_status
        dashboard_data['market_data'] = market_data
        dashboard_data['analysis_history'] = analysis_history
        dashboard_data['system_status'].update(system_stats)
        dashboard_data['system_status']['last_update'] = datetime.now().isoformat()

        return True

    except Exception as e:
        print(f"获取真实数据失败: {e}")
        return False

def check_system_components():
    """检查系统组件状态"""

    components_status = {}

    try:
        # 检查数据处理组件
        components_status['data_processor'] = check_data_processor()

        # 检查市场分析组件
        components_status['market_analyzer'] = check_market_analyzer()

        # 检查预分析组件
        components_status['pre_analyzer'] = check_pre_analyzer()

        # 检查LLM分析组件
        components_status['llm_analyzer'] = check_llm_analyzer()

        # 检查风险管理组件
        components_status['risk_manager'] = check_risk_manager()

        # 检查组合管理组件
        components_status['portfolio_manager'] = check_portfolio_manager()

        # 检查MT4连接
        components_status['mt4_connection'] = check_mt4_connection()

    except Exception as e:
        print(f"检查组件状态失败: {e}")
        # 如果检查失败，设置默认状态
        for comp in ['data_processor', 'market_analyzer', 'pre_analyzer',
                    'llm_analyzer', 'risk_manager', 'portfolio_manager', 'mt4_connection']:
            components_status[comp] = False

    return components_status

def check_data_processor():
    """检查数据处理组件"""
    try:
        from app.utils import forex_data_processor
        # 尝试调用一个简单的函数来测试组件
        return True
    except:
        return False

def check_market_analyzer():
    """检查市场分析组件"""
    try:
        from app.core.market_adaptive_system import MarketAdaptiveSystem
        return True
    except:
        return False

def check_pre_analyzer():
    """检查预分析组件"""
    try:
        from app.utils.multi_round_analysis import should_perform_analysis
        return True
    except:
        return False

def check_llm_analyzer():
    """检查LLM分析组件"""
    try:
        from app.utils import llm_client
        return True
    except:
        return False

def check_risk_manager():
    """检查风险管理组件"""
    try:
        from app.core.risk_management import AdvancedRiskManager
        return True
    except:
        return False

def check_portfolio_manager():
    """检查组合管理组件"""
    try:
        from app.core.portfolio_management_system import PortfolioManager
        return True
    except:
        return False

def check_mt4_connection():
    """检查MT4连接状态"""
    try:
        # 检查是否是周末
        is_weekend = datetime.now().weekday() >= 5
        if is_weekend:
            return False

        # 尝试检查MT4连接
        from app.utils.mt4_client import MT4Client
        # 这里可以添加实际的连接测试
        return True
    except:
        return False

def get_latest_market_data():
    """获取最新市场数据"""

    try:
        # 尝试从数据库获取最新数据
        market_data = get_data_from_database()
        if market_data:
            return market_data

        # 如果数据库没有数据，尝试从文件获取
        market_data = get_data_from_files()
        if market_data:
            return market_data

        # 如果都没有，返回空数据
        return {}

    except Exception as e:
        print(f"获取市场数据失败: {e}")
        return {}

def get_data_from_database():
    """从数据库获取数据"""

    try:
        # 尝试连接pizza_quotes数据库
        from app.utils.db_client import get_connection

        conn = get_connection()
        if not conn:
            return None

        cursor = conn.cursor()

        # 获取EURUSD最新数据 (使用1分钟数据)
        query = """
        SELECT time_date_str, price, min, max, volume, create_time
        FROM min_quote_eurusd
        ORDER BY time_min_int DESC
        LIMIT 1
        """

        cursor.execute(query)
        result = cursor.fetchone()

        if result:
            # 处理字典格式的返回值
            if isinstance(result, dict):
                time_str = result['time_date_str']
                price = float(result['price'])
                min_price = float(result['min'])
                max_price = float(result['max'])
                volume = result['volume']
                create_time = result['create_time']
            else:
                time_str, price, min_price, max_price, volume, create_time = result
                price = float(price)
                min_price = float(min_price)
                max_price = float(max_price)

            # 计算简单的技术指标
            market_data = {
                'symbol': 'EURUSD',
                'price': price,
                'open': price,  # 使用当前价格作为开盘价
                'high': max_price,
                'low': min_price,
                'volume': volume,
                'timestamp': create_time.isoformat() if create_time else time_str,
                'rsi': None,  # 需要更多数据计算
                'macd': None,
                'ma_20': None
            }

            # 尝试计算技术指标
            try:
                indicators = calculate_real_indicators(cursor)
                market_data.update(indicators)
            except:
                pass

            return market_data

        conn.close()
        return None

    except Exception as e:
        print(f"从数据库获取数据失败: {e}")
        return None

def calculate_real_indicators(cursor):
    """计算真实的技术指标"""

    try:
        # 获取最近100条1分钟数据用于计算指标
        query = """
        SELECT price FROM min_quote_eurusd
        ORDER BY time_min_int DESC
        LIMIT 100
        """

        cursor.execute(query)
        results = cursor.fetchall()

        if len(results) < 20:
            return {}

        # 处理字典格式的返回值
        closes = []
        for row in reversed(results):
            if isinstance(row, dict):
                closes.append(float(row['price']))
            else:
                closes.append(float(row[0]))

        indicators = {}

        # 计算MA20
        if len(closes) >= 20:
            indicators['ma_20'] = sum(closes[-20:]) / 20

        # 计算简单RSI
        if len(closes) >= 14:
            indicators['rsi'] = calculate_simple_rsi(closes, 14)

        return indicators

    except Exception as e:
        print(f"计算指标失败: {e}")
        return {}

def calculate_simple_rsi(prices, period=14):
    """计算简单RSI"""

    try:
        if len(prices) < period + 1:
            return None

        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        gains = [d if d > 0 else 0 for d in deltas]
        losses = [-d if d < 0 else 0 for d in deltas]

        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period

        if avg_loss == 0:
            return 100

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return round(rsi, 2)

    except:
        return None

def get_data_from_files():
    """从文件获取数据"""

    try:
        # 尝试从分析历史文件获取数据
        history_file = 'app/data/forex_analysis_history.json'
        if os.path.exists(history_file):
            with open(history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

                if data and len(data) > 0:
                    latest = data[-1]

                    market_data = {
                        'symbol': latest.get('symbol', 'EURUSD'),
                        'price': latest.get('current_price', 0),
                        'timestamp': latest.get('timestamp', datetime.now().isoformat()),
                        'rsi': latest.get('indicators', {}).get('rsi'),
                        'macd': latest.get('indicators', {}).get('macd'),
                        'ma_20': latest.get('indicators', {}).get('ma_20')
                    }

                    return market_data

        return None

    except Exception as e:
        print(f"从文件获取数据失败: {e}")
        return None

def get_analysis_history():
    """获取分析历史"""

    try:
        # 尝试从分析历史文件获取
        history_file = 'app/data/forex_analysis_history.json'
        if os.path.exists(history_file):
            with open(history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

                # 转换为仪表板格式
                analysis_history = []
                for item in data[-10:]:  # 最近10条
                    analysis_result = {
                        'timestamp': item.get('timestamp', datetime.now().isoformat()),
                        'action': item.get('action', 'HOLD'),
                        'confidence': item.get('confidence', 0.5),
                        'risk_level': item.get('risk_level', 'MEDIUM'),
                        'reasoning': item.get('reasoning', '基于历史数据分析')[:50] + '...'
                    }
                    analysis_history.append(analysis_result)

                return analysis_history

        return []

    except Exception as e:
        print(f"获取分析历史失败: {e}")
        return []

def get_system_statistics():
    """获取系统统计信息"""

    try:
        stats = {
            'success_count': 0,
            'error_count': 0
        }

        # 尝试从统计文件获取
        stats_file = 'app/data/forex_statistics.json'
        if os.path.exists(stats_file):
            with open(stats_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

                stats['success_count'] = data.get('total_analyses', 0)
                stats['error_count'] = data.get('error_count', 0)

        # 尝试从错误日志获取错误统计
        error_log_file = 'logs/error_log.json'
        if os.path.exists(error_log_file):
            with open(error_log_file, 'r', encoding='utf-8') as f:
                error_data = json.load(f)
                if isinstance(error_data, list):
                    stats['error_count'] = len(error_data)

        return stats

    except Exception as e:
        print(f"获取系统统计失败: {e}")
        return {'success_count': 0, 'error_count': 0}

def real_data_update_loop():
    """真实数据更新循环"""

    while True:
        try:
            # 获取真实系统数据
            success = get_real_system_data()

            if not success:
                print("获取真实数据失败，等待下次尝试...")

            time.sleep(10)  # 每10秒更新一次真实数据

        except Exception as e:
            print(f"数据更新循环错误: {e}")
            time.sleep(30)

# 使用与web_dashboard.py相同的HTML模板和路由
# 这里省略HTML模板代码，因为与web_dashboard.py相同

# 导入web_dashboard的HTML模板
try:
    from web_dashboard import HTML_TEMPLATE
except:
    HTML_TEMPLATE = "<h1>模板加载失败</h1>"

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/data')
def get_data():
    """获取数据API"""
    return jsonify(dashboard_data)

@app.route('/api/toggle-monitoring', methods=['POST'])
def toggle_monitoring():
    """切换监控状态"""
    dashboard_data['monitoring'] = not dashboard_data['monitoring']

    if dashboard_data['monitoring']:
        message = "✅ 真实数据监控已启动"
    else:
        message = "⏸️ 真实数据监控已暂停"

    return jsonify({
        'monitoring': dashboard_data['monitoring'],
        'message': message
    })

@app.route('/api/run-test', methods=['POST'])
def run_test():
    """运行测试"""
    try:
        # 运行真实的系统测试
        from test_complete_system import test_complete_system_flow

        def run_test_async():
            try:
                test_results, _ = test_complete_system_flow()
                passed = len([r for r in test_results if r[1] == 'PASS'])
                total = len(test_results)
                print(f"✅ 测试完成: {passed}/{total} 通过")
            except Exception as e:
                print(f"❌ 测试失败: {e}")

        # 在后台运行测试
        threading.Thread(target=run_test_async, daemon=True).start()

        return jsonify({'status': 'success', 'message': '真实系统测试已启动'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/clear-logs', methods=['POST'])
def clear_logs():
    """清空日志"""
    dashboard_data['analysis_history'].clear()
    return jsonify({'status': 'success', 'message': '日志已清空'})

def main():
    """主函数"""
    print("🚀 启动真实数据交易系统仪表板...")
    print("📱 访问地址: http://localhost:5001")
    print("📊 连接真实系统数据源...")

    # 启动真实数据更新线程
    update_thread = threading.Thread(target=real_data_update_loop, daemon=True)
    update_thread.start()

    # 启动Flask应用
    try:
        app.run(host='0.0.0.0', port=5001, debug=False)
    except Exception as e:
        print(f"❌ 真实数据仪表板启动失败: {e}")

if __name__ == "__main__":
    main()
