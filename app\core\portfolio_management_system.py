#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多货币对组合管理系统
实现多货币对交易的组合管理、风险分散和相关性分析
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

class CorrelationType(Enum):
    """相关性类型"""
    STRONG_POSITIVE = "强正相关"
    MODERATE_POSITIVE = "中等正相关"
    WEAK_POSITIVE = "弱正相关"
    NEUTRAL = "无相关"
    WEAK_NEGATIVE = "弱负相关"
    MODERATE_NEGATIVE = "中等负相关"
    STRONG_NEGATIVE = "强负相关"

class DiversificationLevel(Enum):
    """分散化水平"""
    EXCELLENT = "优秀"
    GOOD = "良好"
    MODERATE = "中等"
    POOR = "较差"
    VERY_POOR = "很差"

class AllocationStrategy(Enum):
    """配置策略"""
    EQUAL_WEIGHT = "等权重"
    RISK_PARITY = "风险平价"
    MOMENTUM_BASED = "动量导向"
    MEAN_REVERSION = "均值回归"
    CORRELATION_BASED = "相关性导向"
    VOLATILITY_ADJUSTED = "波动率调整"

@dataclass
class CurrencyPairInfo:
    """货币对信息"""
    symbol: str
    base_currency: str
    quote_currency: str
    current_price: float
    daily_volatility: float
    weekly_volatility: float
    monthly_volatility: float
    avg_spread: float
    liquidity_score: float
    trading_hours_overlap: float
    market_cap_rank: int

@dataclass
class PositionInfo:
    """持仓信息"""
    symbol: str
    action: str  # BUY/SELL
    lot_size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    position_value: float
    margin_used: float
    days_held: int
    stop_loss: float
    take_profit: float

@dataclass
class CorrelationMatrix:
    """相关性矩阵"""
    symbols: List[str]
    correlation_matrix: np.ndarray
    timeframe: str
    calculation_date: datetime
    sample_size: int

@dataclass
class PortfolioMetrics:
    """组合指标"""
    total_value: float
    total_margin_used: float
    free_margin: float
    margin_level: float
    total_unrealized_pnl: float
    total_unrealized_pnl_pct: float
    portfolio_volatility: float
    portfolio_sharpe_ratio: float
    max_drawdown: float
    diversification_ratio: float
    concentration_risk: float
    correlation_risk: float

@dataclass
class AllocationRecommendation:
    """配置建议"""
    symbol: str
    current_weight: float
    recommended_weight: float
    weight_change: float
    reasoning: str
    priority: int
    risk_impact: str

# 兼容别名
PortfolioManager = None  # 将在类定义后设置

class PortfolioManagementSystem:
    """组合管理系统"""

    def __init__(self):
        # 支持的货币对（基于实际数据库可用数据）
        self.supported_pairs = {
            'EURUSD': CurrencyPairInfo('EURUSD', 'EUR', 'USD', 0.0, 0.0, 0.0, 0.0, 1.5, 0.95, 0.9, 1),
            'GBPUSD': CurrencyPairInfo('GBPUSD', 'GBP', 'USD', 0.0, 0.0, 0.0, 0.0, 2.0, 0.90, 0.8, 2),
            'AUDUSD': CurrencyPairInfo('AUDUSD', 'AUD', 'USD', 0.0, 0.0, 0.0, 0.0, 2.5, 0.80, 0.7, 3),
            'NZDUSD': CurrencyPairInfo('NZDUSD', 'NZD', 'USD', 0.0, 0.0, 0.0, 0.0, 3.0, 0.70, 0.6, 4),
            'USDCHF': CurrencyPairInfo('USDCHF', 'USD', 'CHF', 0.0, 0.0, 0.0, 0.0, 2.2, 0.85, 0.8, 5),
            'USDCAD': CurrencyPairInfo('USDCAD', 'USD', 'CAD', 0.0, 0.0, 0.0, 0.0, 2.8, 0.75, 0.7, 6),
            'USDJPY': CurrencyPairInfo('USDJPY', 'USD', 'JPY', 0.0, 0.0, 0.0, 0.0, 1.8, 0.92, 0.9, 7),
            'GOLD': CurrencyPairInfo('GOLD', 'XAU', 'USD', 0.0, 0.0, 0.0, 0.0, 0.5, 0.85, 0.6, 8)
        }

        # 组合管理参数（基于实际支持的8个交易品种）
        self.portfolio_params = {
            'max_pairs': 8,                    # 最大货币对数量（包含GOLD）
            'max_correlation': 0.7,            # 最大相关性阈值
            'min_diversification_ratio': 0.6,  # 最小分散化比率
            'max_concentration': 0.25,         # 最大单一货币对权重（降低以适应8个品种）
            'rebalance_threshold': 0.05,       # 再平衡阈值
            'correlation_lookback': 30,        # 相关性计算回看期
            'volatility_lookback': 20,         # 波动率计算回看期
            'gold_allocation_limit': 0.15      # 黄金最大配置比例
        }

        # 数据存储
        self.current_positions = {}
        self.price_history = {}
        self.correlation_history = []
        self.portfolio_history = []

        # 当前状态
        self.current_portfolio_metrics = None
        self.current_correlation_matrix = None
        self.last_rebalance_date = None

        # 日志
        self.logger = logging.getLogger(__name__)

    def update_market_data(self, symbol: str, market_data: Dict):
        """更新市场数据"""

        if symbol not in self.supported_pairs:
            self.logger.warning(f"不支持的货币对: {symbol}")
            return

        # 更新货币对信息
        pair_info = self.supported_pairs[symbol]
        pair_info.current_price = market_data.get('current_price', pair_info.current_price)

        # 计算波动率
        if symbol not in self.price_history:
            self.price_history[symbol] = []

        self.price_history[symbol].append({
            'timestamp': datetime.now(),
            'price': pair_info.current_price,
            'volume': market_data.get('volume', 0)
        })

        # 保持最近100个价格点
        if len(self.price_history[symbol]) > 100:
            self.price_history[symbol] = self.price_history[symbol][-100:]

        # 更新波动率
        self._update_volatility(symbol)

    def update_position(self, symbol: str, position_data: Dict):
        """更新持仓信息"""

        if symbol not in self.supported_pairs:
            self.logger.warning(f"不支持的货币对: {symbol}")
            return

        if position_data.get('lot_size', 0) == 0:
            # 移除空仓位
            if symbol in self.current_positions:
                del self.current_positions[symbol]
        else:
            # 更新或添加持仓
            self.current_positions[symbol] = PositionInfo(
                symbol=symbol,
                action=position_data.get('action', 'BUY'),
                lot_size=position_data.get('lot_size', 0.0),
                entry_price=position_data.get('entry_price', 0.0),
                current_price=position_data.get('current_price', 0.0),
                unrealized_pnl=position_data.get('unrealized_pnl', 0.0),
                unrealized_pnl_pct=position_data.get('unrealized_pnl_pct', 0.0),
                position_value=position_data.get('position_value', 0.0),
                margin_used=position_data.get('margin_used', 0.0),
                days_held=position_data.get('days_held', 0),
                stop_loss=position_data.get('stop_loss', 0.0),
                take_profit=position_data.get('take_profit', 0.0)
            )

    def calculate_correlation_matrix(self, timeframe: str = 'daily') -> CorrelationMatrix:
        """计算相关性矩阵"""

        # 获取有足够数据的货币对
        valid_symbols = []
        price_data = {}

        for symbol in self.supported_pairs.keys():
            if symbol in self.price_history and len(self.price_history[symbol]) >= 20:
                valid_symbols.append(symbol)
                prices = [p['price'] for p in self.price_history[symbol][-30:]]  # 最近30个价格点
                price_data[symbol] = prices

        if len(valid_symbols) < 2:
            return None

        # 计算收益率
        returns_data = {}
        min_length = min(len(price_data[symbol]) for symbol in valid_symbols)

        for symbol in valid_symbols:
            prices = price_data[symbol][-min_length:]
            returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
            returns_data[symbol] = returns

        # 构建相关性矩阵
        n = len(valid_symbols)
        correlation_matrix = np.zeros((n, n))

        for i, symbol1 in enumerate(valid_symbols):
            for j, symbol2 in enumerate(valid_symbols):
                if i == j:
                    correlation_matrix[i, j] = 1.0
                else:
                    returns1 = returns_data[symbol1]
                    returns2 = returns_data[symbol2]

                    if len(returns1) > 1 and len(returns2) > 1:
                        correlation = np.corrcoef(returns1, returns2)[0, 1]
                        correlation_matrix[i, j] = correlation if not np.isnan(correlation) else 0.0
                    else:
                        correlation_matrix[i, j] = 0.0

        correlation_obj = CorrelationMatrix(
            symbols=valid_symbols,
            correlation_matrix=correlation_matrix,
            timeframe=timeframe,
            calculation_date=datetime.now(),
            sample_size=min_length - 1
        )

        self.current_correlation_matrix = correlation_obj
        self.correlation_history.append(correlation_obj)

        # 保持最近50个相关性记录
        if len(self.correlation_history) > 50:
            self.correlation_history = self.correlation_history[-50:]

        return correlation_obj

    def calculate_portfolio_metrics(self, account_balance: float = 10000) -> PortfolioMetrics:
        """计算组合指标"""

        if not self.current_positions:
            return PortfolioMetrics(
                total_value=account_balance,
                total_margin_used=0.0,
                free_margin=account_balance,
                margin_level=float('inf'),
                total_unrealized_pnl=0.0,
                total_unrealized_pnl_pct=0.0,
                portfolio_volatility=0.0,
                portfolio_sharpe_ratio=0.0,
                max_drawdown=0.0,
                diversification_ratio=1.0,
                concentration_risk=0.0,
                correlation_risk=0.0
            )

        # 基本指标
        total_margin_used = sum(pos.margin_used for pos in self.current_positions.values())
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.current_positions.values())
        total_value = account_balance + total_unrealized_pnl
        free_margin = account_balance - total_margin_used
        margin_level = (account_balance / total_margin_used * 100) if total_margin_used > 0 else float('inf')
        total_unrealized_pnl_pct = total_unrealized_pnl / account_balance if account_balance > 0 else 0.0

        # 组合波动率
        portfolio_volatility = self._calculate_portfolio_volatility()

        # 夏普比率（简化）
        avg_return = total_unrealized_pnl_pct
        sharpe_ratio = avg_return / portfolio_volatility if portfolio_volatility > 0 else 0.0

        # 最大回撤（基于历史数据）
        max_drawdown = self._calculate_max_drawdown()

        # 分散化比率
        diversification_ratio = self._calculate_diversification_ratio()

        # 集中度风险
        concentration_risk = self._calculate_concentration_risk()

        # 相关性风险
        correlation_risk = self._calculate_correlation_risk()

        metrics = PortfolioMetrics(
            total_value=total_value,
            total_margin_used=total_margin_used,
            free_margin=free_margin,
            margin_level=margin_level,
            total_unrealized_pnl=total_unrealized_pnl,
            total_unrealized_pnl_pct=total_unrealized_pnl_pct,
            portfolio_volatility=portfolio_volatility,
            portfolio_sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            diversification_ratio=diversification_ratio,
            concentration_risk=concentration_risk,
            correlation_risk=correlation_risk
        )

        self.current_portfolio_metrics = metrics
        self.portfolio_history.append({
            'timestamp': datetime.now(),
            'metrics': metrics
        })

        # 保持最近100个记录
        if len(self.portfolio_history) > 100:
            self.portfolio_history = self.portfolio_history[-100:]

        return metrics

    def analyze_portfolio_risk(self) -> 'PortfolioRiskAnalysis':
        """分析组合风险 - 兼容接口"""
        # 计算组合指标
        metrics = self.calculate_portfolio_metrics()

        # 分析分散化
        diversification = self.analyze_diversification()

        # 创建兼容的风险分析结果
        class PortfolioRiskAnalysis:
            def __init__(self, metrics, diversification):
                self.total_risk = metrics.portfolio_volatility
                self.diversification_ratio = diversification['score']
                self.concentration_risk = metrics.concentration_risk
                self.correlation_risk = metrics.correlation_risk
                self.risk_level = self._determine_risk_level(metrics)
                self.recommendations = diversification.get('suggestions', [])

            def _determine_risk_level(self, metrics):
                if metrics.portfolio_volatility > 0.05:
                    return "HIGH"
                elif metrics.portfolio_volatility > 0.03:
                    return "MEDIUM"
                else:
                    return "LOW"

        return PortfolioRiskAnalysis(metrics, diversification)

    def analyze_diversification(self) -> Dict:
        """分析分散化水平"""

        if not self.current_positions:
            return {
                'level': DiversificationLevel.EXCELLENT.value,
                'score': 1.0,
                'analysis': '无持仓，无分散化风险'
            }

        # 计算分散化指标
        diversification_ratio = self._calculate_diversification_ratio()
        concentration_risk = self._calculate_concentration_risk()
        correlation_risk = self._calculate_correlation_risk()

        # 综合评分
        diversification_score = (
            diversification_ratio * 0.4 +
            (1 - concentration_risk) * 0.3 +
            (1 - correlation_risk) * 0.3
        )

        # 确定分散化水平
        if diversification_score >= 0.8:
            level = DiversificationLevel.EXCELLENT
        elif diversification_score >= 0.6:
            level = DiversificationLevel.GOOD
        elif diversification_score >= 0.4:
            level = DiversificationLevel.MODERATE
        elif diversification_score >= 0.2:
            level = DiversificationLevel.POOR
        else:
            level = DiversificationLevel.VERY_POOR

        # 分析建议
        suggestions = []
        if concentration_risk > 0.3:
            suggestions.append("减少单一货币对的权重")
        if correlation_risk > 0.7:
            suggestions.append("降低高相关性货币对的同时持仓")
        if len(self.current_positions) < 3:
            suggestions.append("增加货币对数量以提高分散化")

        return {
            'level': level.value,
            'score': diversification_score,
            'diversification_ratio': diversification_ratio,
            'concentration_risk': concentration_risk,
            'correlation_risk': correlation_risk,
            'suggestions': suggestions,
            'analysis': f'分散化水平{level.value}，评分{diversification_score:.2f}'
        }

    def generate_allocation_recommendations(self, strategy: AllocationStrategy = AllocationStrategy.RISK_PARITY) -> List[AllocationRecommendation]:
        """生成配置建议"""

        recommendations = []

        if not self.current_positions:
            return recommendations

        # 计算当前权重
        total_value = sum(abs(pos.position_value) for pos in self.current_positions.values())
        current_weights = {}

        for symbol, position in self.current_positions.items():
            current_weights[symbol] = abs(position.position_value) / total_value if total_value > 0 else 0

        # 根据策略计算目标权重
        if strategy == AllocationStrategy.EQUAL_WEIGHT:
            target_weights = self._calculate_equal_weight_allocation()
        elif strategy == AllocationStrategy.RISK_PARITY:
            target_weights = self._calculate_risk_parity_allocation()
        elif strategy == AllocationStrategy.VOLATILITY_ADJUSTED:
            target_weights = self._calculate_volatility_adjusted_allocation()
        elif strategy == AllocationStrategy.CORRELATION_BASED:
            target_weights = self._calculate_correlation_based_allocation()
        else:
            target_weights = current_weights

        # 生成建议
        for symbol in current_weights.keys():
            current_weight = current_weights[symbol]
            target_weight = target_weights.get(symbol, current_weight)
            weight_change = target_weight - current_weight

            if abs(weight_change) > self.portfolio_params['rebalance_threshold']:
                priority = min(int(abs(weight_change) * 10), 10)

                if weight_change > 0:
                    reasoning = f"建议增加{symbol}权重{weight_change:.1%}以优化组合配置"
                    risk_impact = "降低组合风险" if target_weight < 0.3 else "需要监控集中度风险"
                else:
                    reasoning = f"建议减少{symbol}权重{abs(weight_change):.1%}以降低集中度风险"
                    risk_impact = "降低集中度风险"

                recommendations.append(AllocationRecommendation(
                    symbol=symbol,
                    current_weight=current_weight,
                    recommended_weight=target_weight,
                    weight_change=weight_change,
                    reasoning=reasoning,
                    priority=priority,
                    risk_impact=risk_impact
                ))

        # 按优先级排序
        recommendations.sort(key=lambda x: x.priority, reverse=True)

        return recommendations

    def should_rebalance(self) -> Tuple[bool, str]:
        """判断是否需要再平衡"""

        if not self.current_positions:
            return False, "无持仓，无需再平衡"

        # 检查时间间隔
        if self.last_rebalance_date:
            days_since_rebalance = (datetime.now() - self.last_rebalance_date).days
            if days_since_rebalance < 7:
                return False, f"距离上次再平衡仅{days_since_rebalance}天，建议等待"

        # 检查权重偏离
        recommendations = self.generate_allocation_recommendations()
        significant_changes = [r for r in recommendations if abs(r.weight_change) > self.portfolio_params['rebalance_threshold']]

        if significant_changes:
            max_deviation = max(abs(r.weight_change) for r in significant_changes)
            return True, f"权重偏离过大(最大偏离{max_deviation:.1%})，建议再平衡"

        # 检查相关性风险
        correlation_risk = self._calculate_correlation_risk()
        if correlation_risk > 0.8:
            return True, f"相关性风险过高({correlation_risk:.1%})，建议调整持仓"

        # 检查集中度风险
        concentration_risk = self._calculate_concentration_risk()
        if concentration_risk > 0.4:
            return True, f"集中度风险过高({concentration_risk:.1%})，建议分散持仓"

        return False, "组合配置良好，无需再平衡"

    def get_currency_exposure(self) -> Dict:
        """获取货币敞口分析"""

        currency_exposure = {}

        for symbol, position in self.current_positions.items():
            pair_info = self.supported_pairs[symbol]
            base_currency = pair_info.base_currency
            quote_currency = pair_info.quote_currency

            # 计算敞口
            if position.action == 'BUY':
                # 买入基础货币，卖出报价货币
                base_exposure = position.position_value
                quote_exposure = -position.position_value
            else:
                # 卖出基础货币，买入报价货币
                base_exposure = -position.position_value
                quote_exposure = position.position_value

            # 累计敞口
            if base_currency not in currency_exposure:
                currency_exposure[base_currency] = 0.0
            if quote_currency not in currency_exposure:
                currency_exposure[quote_currency] = 0.0

            currency_exposure[base_currency] += base_exposure
            currency_exposure[quote_currency] += quote_exposure

        # 计算净敞口和风险
        total_exposure = sum(abs(exp) for exp in currency_exposure.values())
        net_exposure = {}
        exposure_risk = 0.0

        for currency, exposure in currency_exposure.items():
            net_exposure[currency] = {
                'exposure': exposure,
                'percentage': exposure / total_exposure if total_exposure > 0 else 0,
                'risk_level': 'HIGH' if abs(exposure / total_exposure) > 0.3 else 'MEDIUM' if abs(exposure / total_exposure) > 0.15 else 'LOW'
            }

            # 计算敞口风险
            if abs(exposure / total_exposure) > 0.3:
                exposure_risk += 0.3

        return {
            'currency_exposure': net_exposure,
            'total_exposure': total_exposure,
            'exposure_risk': min(exposure_risk, 1.0),
            'dominant_currencies': sorted(net_exposure.items(), key=lambda x: abs(x[1]['exposure']), reverse=True)[:3]
        }

    def _update_volatility(self, symbol: str):
        """更新波动率"""

        if symbol not in self.price_history or len(self.price_history[symbol]) < 2:
            return

        prices = [p['price'] for p in self.price_history[symbol]]

        # 计算收益率
        returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]

        if len(returns) >= 5:
            # 日波动率
            daily_vol = np.std(returns[-5:]) if len(returns) >= 5 else 0.0
            # 周波动率
            weekly_vol = np.std(returns[-20:]) * np.sqrt(5) if len(returns) >= 20 else daily_vol * np.sqrt(5)
            # 月波动率
            monthly_vol = np.std(returns) * np.sqrt(20) if len(returns) >= 20 else weekly_vol * 2

            pair_info = self.supported_pairs[symbol]
            pair_info.daily_volatility = daily_vol
            pair_info.weekly_volatility = weekly_vol
            pair_info.monthly_volatility = monthly_vol

    def _calculate_portfolio_volatility(self) -> float:
        """计算组合波动率"""

        if not self.current_positions or not self.current_correlation_matrix:
            return 0.0

        # 获取权重和波动率
        symbols = list(self.current_positions.keys())
        weights = []
        volatilities = []

        total_value = sum(abs(pos.position_value) for pos in self.current_positions.values())

        for symbol in symbols:
            position = self.current_positions[symbol]
            weight = abs(position.position_value) / total_value if total_value > 0 else 0
            volatility = self.supported_pairs[symbol].daily_volatility

            weights.append(weight)
            volatilities.append(volatility)

        if len(weights) < 2:
            return volatilities[0] if volatilities else 0.0

        # 构建协方差矩阵
        n = len(symbols)
        covariance_matrix = np.zeros((n, n))

        for i in range(n):
            for j in range(n):
                if i == j:
                    covariance_matrix[i, j] = volatilities[i] ** 2
                else:
                    # 查找相关性
                    correlation = 0.0
                    if (symbols[i] in self.current_correlation_matrix.symbols and
                        symbols[j] in self.current_correlation_matrix.symbols):
                        idx_i = self.current_correlation_matrix.symbols.index(symbols[i])
                        idx_j = self.current_correlation_matrix.symbols.index(symbols[j])
                        correlation = self.current_correlation_matrix.correlation_matrix[idx_i, idx_j]

                    covariance_matrix[i, j] = correlation * volatilities[i] * volatilities[j]

        # 计算组合波动率
        weights_array = np.array(weights)
        portfolio_variance = np.dot(weights_array, np.dot(covariance_matrix, weights_array))
        portfolio_volatility = np.sqrt(portfolio_variance)

        return portfolio_volatility

    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""

        if len(self.portfolio_history) < 2:
            return 0.0

        values = [record['metrics'].total_value for record in self.portfolio_history]

        peak = values[0]
        max_drawdown = 0.0

        for value in values:
            if value > peak:
                peak = value

            drawdown = (peak - value) / peak if peak > 0 else 0.0
            if drawdown > max_drawdown:
                max_drawdown = drawdown

        return max_drawdown

    def _calculate_diversification_ratio(self) -> float:
        """计算分散化比率"""

        if not self.current_positions:
            return 1.0

        # 加权平均波动率
        total_value = sum(abs(pos.position_value) for pos in self.current_positions.values())
        weighted_avg_vol = 0.0

        for symbol, position in self.current_positions.items():
            weight = abs(position.position_value) / total_value if total_value > 0 else 0
            volatility = self.supported_pairs[symbol].daily_volatility
            weighted_avg_vol += weight * volatility

        # 组合波动率
        portfolio_vol = self._calculate_portfolio_volatility()

        # 分散化比率
        if portfolio_vol > 0:
            diversification_ratio = weighted_avg_vol / portfolio_vol
        else:
            diversification_ratio = 1.0

        return min(diversification_ratio, 2.0)  # 限制最大值

    def _calculate_concentration_risk(self) -> float:
        """计算集中度风险"""

        if not self.current_positions:
            return 0.0

        total_value = sum(abs(pos.position_value) for pos in self.current_positions.values())
        max_weight = 0.0

        for position in self.current_positions.values():
            weight = abs(position.position_value) / total_value if total_value > 0 else 0
            if weight > max_weight:
                max_weight = weight

        return max_weight

    def _calculate_correlation_risk(self) -> float:
        """计算相关性风险"""

        if not self.current_correlation_matrix or len(self.current_positions) < 2:
            return 0.0

        # 计算平均相关性
        symbols = list(self.current_positions.keys())
        correlations = []

        for i, symbol1 in enumerate(symbols):
            for j, symbol2 in enumerate(symbols):
                if i < j and symbol1 in self.current_correlation_matrix.symbols and symbol2 in self.current_correlation_matrix.symbols:
                    idx1 = self.current_correlation_matrix.symbols.index(symbol1)
                    idx2 = self.current_correlation_matrix.symbols.index(symbol2)
                    correlation = abs(self.current_correlation_matrix.correlation_matrix[idx1, idx2])
                    correlations.append(correlation)

        if correlations:
            avg_correlation = np.mean(correlations)
            return avg_correlation

        return 0.0

    def _calculate_equal_weight_allocation(self) -> Dict[str, float]:
        """计算等权重配置"""

        symbols = list(self.current_positions.keys())
        n = len(symbols)

        if n == 0:
            return {}

        equal_weight = 1.0 / n
        return {symbol: equal_weight for symbol in symbols}

    def _calculate_risk_parity_allocation(self) -> Dict[str, float]:
        """计算风险平价配置"""

        symbols = list(self.current_positions.keys())

        if not symbols:
            return {}

        # 获取波动率
        volatilities = {}
        for symbol in symbols:
            volatilities[symbol] = self.supported_pairs[symbol].daily_volatility or 0.01

        # 计算风险平价权重（与波动率成反比）
        inv_volatilities = {symbol: 1.0 / vol for symbol, vol in volatilities.items()}
        total_inv_vol = sum(inv_volatilities.values())

        risk_parity_weights = {}
        for symbol in symbols:
            risk_parity_weights[symbol] = inv_volatilities[symbol] / total_inv_vol

        return risk_parity_weights

    def _calculate_volatility_adjusted_allocation(self) -> Dict[str, float]:
        """计算波动率调整配置"""

        symbols = list(self.current_positions.keys())

        if not symbols:
            return {}

        # 获取波动率和当前权重
        total_value = sum(abs(pos.position_value) for pos in self.current_positions.values())
        current_weights = {}
        volatilities = {}

        for symbol in symbols:
            position = self.current_positions[symbol]
            current_weights[symbol] = abs(position.position_value) / total_value if total_value > 0 else 0
            volatilities[symbol] = self.supported_pairs[symbol].daily_volatility or 0.01

        # 调整权重（降低高波动率货币对的权重）
        adjusted_weights = {}
        total_adjusted = 0.0

        for symbol in symbols:
            # 波动率调整因子
            vol_factor = 1.0 / (1.0 + volatilities[symbol] * 10)  # 波动率越高，权重越低
            adjusted_weight = current_weights[symbol] * vol_factor
            adjusted_weights[symbol] = adjusted_weight
            total_adjusted += adjusted_weight

        # 标准化权重
        if total_adjusted > 0:
            for symbol in symbols:
                adjusted_weights[symbol] /= total_adjusted

        return adjusted_weights

    def _calculate_correlation_based_allocation(self) -> Dict[str, float]:
        """计算基于相关性的配置"""

        symbols = list(self.current_positions.keys())

        if not symbols or not self.current_correlation_matrix:
            return self._calculate_equal_weight_allocation()

        # 计算每个货币对与其他货币对的平均相关性
        avg_correlations = {}

        for symbol in symbols:
            if symbol in self.current_correlation_matrix.symbols:
                idx = self.current_correlation_matrix.symbols.index(symbol)
                correlations = []

                for other_symbol in symbols:
                    if other_symbol != symbol and other_symbol in self.current_correlation_matrix.symbols:
                        other_idx = self.current_correlation_matrix.symbols.index(other_symbol)
                        correlation = abs(self.current_correlation_matrix.correlation_matrix[idx, other_idx])
                        correlations.append(correlation)

                avg_correlations[symbol] = np.mean(correlations) if correlations else 0.0
            else:
                avg_correlations[symbol] = 0.0

        # 分配权重（相关性越低，权重越高）
        inv_correlations = {symbol: 1.0 / (1.0 + corr) for symbol, corr in avg_correlations.items()}
        total_inv_corr = sum(inv_correlations.values())

        correlation_weights = {}
        for symbol in symbols:
            correlation_weights[symbol] = inv_correlations[symbol] / total_inv_corr if total_inv_corr > 0 else 1.0 / len(symbols)

        return correlation_weights

    def get_portfolio_summary(self) -> Dict:
        """获取组合总结"""

        summary = {
            'timestamp': datetime.now().isoformat(),
            'positions_count': len(self.current_positions),
            'supported_pairs': list(self.supported_pairs.keys()),
            'active_pairs': list(self.current_positions.keys()) if self.current_positions else [],
            'portfolio_metrics': None,
            'diversification_analysis': None,
            'currency_exposure': None,
            'correlation_matrix': None,
            'rebalance_recommendation': None
        }

        if self.current_positions:
            # 组合指标
            summary['portfolio_metrics'] = {
                'total_value': self.current_portfolio_metrics.total_value if self.current_portfolio_metrics else 0,
                'total_margin_used': self.current_portfolio_metrics.total_margin_used if self.current_portfolio_metrics else 0,
                'margin_level': self.current_portfolio_metrics.margin_level if self.current_portfolio_metrics else 0,
                'portfolio_volatility': self.current_portfolio_metrics.portfolio_volatility if self.current_portfolio_metrics else 0,
                'sharpe_ratio': self.current_portfolio_metrics.portfolio_sharpe_ratio if self.current_portfolio_metrics else 0,
                'max_drawdown': self.current_portfolio_metrics.max_drawdown if self.current_portfolio_metrics else 0
            }

            # 分散化分析
            summary['diversification_analysis'] = self.analyze_diversification()

            # 货币敞口
            summary['currency_exposure'] = self.get_currency_exposure()

            # 相关性矩阵
            if self.current_correlation_matrix:
                summary['correlation_matrix'] = {
                    'symbols': self.current_correlation_matrix.symbols,
                    'matrix': self.current_correlation_matrix.correlation_matrix.tolist(),
                    'calculation_date': self.current_correlation_matrix.calculation_date.isoformat()
                }

            # 再平衡建议
            should_rebalance, reason = self.should_rebalance()
            summary['rebalance_recommendation'] = {
                'should_rebalance': should_rebalance,
                'reason': reason,
                'recommendations': [
                    {
                        'symbol': rec.symbol,
                        'current_weight': rec.current_weight,
                        'recommended_weight': rec.recommended_weight,
                        'weight_change': rec.weight_change,
                        'reasoning': rec.reasoning,
                        'priority': rec.priority
                    }
                    for rec in self.generate_allocation_recommendations()[:5]  # 前5个建议
                ]
            }

        return summary

    def export_portfolio_data(self, filepath: str = None) -> Dict:
        """导出组合数据"""

        if filepath is None:
            filepath = f"portfolio_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        export_data = {
            'export_timestamp': datetime.now().isoformat(),
            'portfolio_summary': self.get_portfolio_summary(),
            'supported_pairs': {
                symbol: {
                    'base_currency': info.base_currency,
                    'quote_currency': info.quote_currency,
                    'current_price': info.current_price,
                    'daily_volatility': info.daily_volatility,
                    'weekly_volatility': info.weekly_volatility,
                    'monthly_volatility': info.monthly_volatility,
                    'liquidity_score': info.liquidity_score,
                    'market_cap_rank': info.market_cap_rank
                }
                for symbol, info in self.supported_pairs.items()
            },
            'current_positions': {
                symbol: {
                    'action': pos.action,
                    'lot_size': pos.lot_size,
                    'entry_price': pos.entry_price,
                    'current_price': pos.current_price,
                    'unrealized_pnl': pos.unrealized_pnl,
                    'unrealized_pnl_pct': pos.unrealized_pnl_pct,
                    'position_value': pos.position_value,
                    'margin_used': pos.margin_used,
                    'days_held': pos.days_held
                }
                for symbol, pos in self.current_positions.items()
            },
            'correlation_history': [
                {
                    'symbols': corr.symbols,
                    'matrix': corr.correlation_matrix.tolist(),
                    'timeframe': corr.timeframe,
                    'calculation_date': corr.calculation_date.isoformat(),
                    'sample_size': corr.sample_size
                }
                for corr in self.correlation_history[-10:]  # 最近10个相关性记录
            ],
            'portfolio_history': [
                {
                    'timestamp': record['timestamp'].isoformat(),
                    'total_value': record['metrics'].total_value,
                    'portfolio_volatility': record['metrics'].portfolio_volatility,
                    'sharpe_ratio': record['metrics'].portfolio_sharpe_ratio,
                    'max_drawdown': record['metrics'].max_drawdown,
                    'diversification_ratio': record['metrics'].diversification_ratio
                }
                for record in self.portfolio_history[-50:]  # 最近50个记录
            ]
        }

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            return {'status': 'success', 'filepath': filepath, 'records_exported': len(export_data)}

        except Exception as e:
            return {'status': 'error', 'error': str(e)}

    def get_optimization_suggestions(self) -> List[str]:
        """获取优化建议"""

        suggestions = []

        if not self.current_positions:
            suggestions.append("建议开始建立多货币对组合以分散风险")
            return suggestions

        # 分散化建议
        diversification = self.analyze_diversification()
        if diversification['score'] < 0.6:
            suggestions.extend(diversification['suggestions'])

        # 相关性建议
        correlation_risk = self._calculate_correlation_risk()
        if correlation_risk > 0.7:
            suggestions.append("降低高相关性货币对的同时持仓")

        # 集中度建议
        concentration_risk = self._calculate_concentration_risk()
        if concentration_risk > 0.3:
            suggestions.append("减少单一货币对的权重，提高分散化")

        # 货币敞口建议
        currency_exposure = self.get_currency_exposure()
        if currency_exposure['exposure_risk'] > 0.5:
            suggestions.append("平衡货币敞口，避免单一货币过度暴露")

        # 波动率建议
        if self.current_portfolio_metrics and self.current_portfolio_metrics.portfolio_volatility > 0.03:
            suggestions.append("考虑降低组合波动率，增加低波动率货币对")

        # 再平衡建议
        should_rebalance, reason = self.should_rebalance()
        if should_rebalance:
            suggestions.append(f"建议进行组合再平衡：{reason}")

        return suggestions[:10]  # 最多返回10个建议

# 设置兼容别名
PortfolioManager = PortfolioManagementSystem