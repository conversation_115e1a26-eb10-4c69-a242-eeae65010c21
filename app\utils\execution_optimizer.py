"""
执行优化器
专门优化订单管理执行的成功率
"""

import re
from datetime import datetime
from typing import Dict, List


class ExecutionOptimizer:
    """执行优化器"""

    def __init__(self, mt4_client):
        self.mt4_client = mt4_client

    def optimize_order_management_execution(self, order_management) -> List[Dict]:
        """
        优化订单管理执行

        Args:
            order_management: 订单管理操作列表（可能是字符串列表或字典列表）

        Returns:
            List[Dict]: 执行结果列表
        """
        if not order_management:
            return []

        # 处理输入数据类型
        if isinstance(order_management, list) and len(order_management) > 0:
            # 如果是字符串列表，转换为字典列表
            if isinstance(order_management[0], str):
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🔧 检测到字符串格式的订单管理操作，转换为字典格式')
                order_management = self._convert_string_operations_to_dict(order_management)

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🔧 开始优化执行{len(order_management)}个订单管理操作')

        # 1. 获取当前所有订单
        current_orders = self._get_all_current_orders()

        # 2. 智能匹配订单ID
        optimized_operations = self._smart_match_order_ids(order_management, current_orders)

        # 3. 执行优化后的操作
        results = []
        for operation in optimized_operations:
            result = self._execute_single_operation(operation)
            results.append(result)

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ✅ 订单管理执行完成，成功{sum(1 for r in results if r.get("success"))}个，失败{sum(1 for r in results if not r.get("success"))}个')

        return results

    def _convert_string_operations_to_dict(self, string_operations: List[str]) -> List[Dict]:
        """
        将字符串格式的订单管理操作转换为字典格式

        Args:
            string_operations: 字符串格式的操作列表

        Returns:
            List[Dict]: 字典格式的操作列表
        """
        dict_operations = []

        for operation_str in string_operations:
            try:
                # 解析字符串操作
                operation_dict = self._parse_operation_string(operation_str)
                if operation_dict:
                    dict_operations.append(operation_dict)
                else:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ⚠️ 无法解析操作字符串: {operation_str}')
            except Exception as e:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ❌ 解析操作字符串失败: {operation_str} - {e}')

        return dict_operations

    def _parse_operation_string(self, operation_str: str) -> Dict:
        """
        解析单个操作字符串

        Args:
            operation_str: 操作字符串，如"监控352849373号空单止损1.1419"

        Returns:
            Dict: 解析后的操作字典，如果无法解析则返回None
        """
        import re

        # 监控操作：监控352849373号空单止损1.1419
        monitor_pattern = r'监控(\d+)号.*止损([\d.]+)'
        monitor_match = re.search(monitor_pattern, operation_str)
        if monitor_match:
            order_id = monitor_match.group(1)
            stop_loss = float(monitor_match.group(2))
            return {
                'action': 'MODIFY',
                'orderId': order_id,
                'newStopLoss': stop_loss,
                'description': operation_str
            }

        # 反手操作：若15分钟收盘＞1.1419反手做多
        reverse_pattern = r'若.*收盘[＞>]([\d.]+)反手做多'
        reverse_match = re.search(reverse_pattern, operation_str)
        if reverse_match:
            trigger_price = float(reverse_match.group(1))
            return {
                'action': 'CONDITIONAL_BUY',
                'triggerPrice': trigger_price,
                'description': operation_str
            }

        # 加仓操作：跌破1.1400加仓空单至0.1手
        add_position_pattern = r'跌破([\d.]+)加仓.*至([\d.]+)手'
        add_match = re.search(add_position_pattern, operation_str)
        if add_match:
            trigger_price = float(add_match.group(1))
            lot_size = float(add_match.group(2))
            return {
                'action': 'CONDITIONAL_SELL',
                'triggerPrice': trigger_price,
                'lotSize': lot_size,
                'description': operation_str
            }

        # 如果无法解析，返回通用监控操作
        return {
            'action': 'MONITOR',
            'description': operation_str
        }

    def _get_all_current_orders(self) -> Dict:
        """获取当前所有订单"""
        all_orders = {
            'positions': [],
            'pending_orders': [],
            'order_id_map': {}  # 用于模糊匹配
        }

        try:
            # 获取活跃持仓
            positions_response = self.mt4_client.get_active_orders()
            if positions_response and positions_response.get('status') == 'success':
                positions = positions_response.get('orders', [])
                all_orders['positions'] = positions

                for pos in positions:
                    order_id = str(pos.get('order_id', ''))
                    if order_id:
                        all_orders['order_id_map'][order_id] = {
                            'type': 'position',
                            'data': pos
                        }

            # 获取挂单
            pending_response = self.mt4_client.get_pending_orders()
            if pending_response and pending_response.get('status') == 'success':
                pending_orders = pending_response.get('orders', [])
                all_orders['pending_orders'] = pending_orders

                for order in pending_orders:
                    order_id = str(order.get('order_id', ''))
                    if order_id:
                        all_orders['order_id_map'][order_id] = {
                            'type': 'pending',
                            'data': order
                        }

            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 📊 当前订单状态: {len(all_orders["positions"])}个持仓, {len(all_orders["pending_orders"])}个挂单')

        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ❌ 获取当前订单失败: {e}')

        return all_orders

    def _smart_match_order_ids(self, operations: List[Dict], current_orders: Dict) -> List[Dict]:
        """智能匹配订单ID"""
        optimized_operations = []
        order_id_map = current_orders['order_id_map']

        for operation in operations:
            original_order_id = str(operation.get('orderId', ''))
            matched_order_id = None

            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🔍 匹配订单ID: {original_order_id}')

            # 1. 精确匹配
            if original_order_id in order_id_map:
                matched_order_id = original_order_id
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ✅ 精确匹配成功')

            # 2. 模糊匹配
            else:
                # 尝试不同的匹配策略
                for existing_id in order_id_map.keys():
                    # 策略1: 包含关系
                    if original_order_id in existing_id or existing_id in original_order_id:
                        matched_order_id = existing_id
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🔄 模糊匹配成功: {original_order_id} -> {existing_id}')
                        break

                    # 策略2: 数字相似性（去除小数点等）
                    clean_original = re.sub(r'[^\d]', '', original_order_id)
                    clean_existing = re.sub(r'[^\d]', '', existing_id)
                    if clean_original and clean_existing and clean_original in clean_existing:
                        matched_order_id = existing_id
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🔄 数字匹配成功: {original_order_id} -> {existing_id}')
                        break

            # 3. 创建优化后的操作
            optimized_operation = operation.copy()

            if matched_order_id:
                optimized_operation['orderId'] = matched_order_id
                optimized_operation['original_order_id'] = original_order_id
                optimized_operation['match_type'] = 'exact' if matched_order_id == original_order_id else 'fuzzy'
                optimized_operations.append(optimized_operation)
            else:
                # 如果是DELETE操作，订单不存在可能是正常的（已经被删除）
                if operation.get('action') == 'DELETE':
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ℹ️ DELETE操作：订单{original_order_id}不存在，可能已被删除')
                    optimized_operation['orderId'] = original_order_id
                    optimized_operation['match_type'] = 'not_found_ok'
                    optimized_operations.append(optimized_operation)
                else:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ⚠️ 无法匹配订单ID: {original_order_id}')
                    optimized_operation['orderId'] = original_order_id
                    optimized_operation['match_type'] = 'not_found'
                    optimized_operations.append(optimized_operation)

        return optimized_operations

    def _execute_single_operation(self, operation: Dict) -> Dict:
        """执行单个订单管理操作"""
        action = operation.get('action')
        order_id = operation.get('orderId')
        match_type = operation.get('match_type', 'unknown')

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🚀 执行操作: {action} 订单{order_id} (匹配类型: {match_type})')

        result = {
            'action': action,
            'orderId': order_id,
            'original_order_id': operation.get('original_order_id', order_id),
            'match_type': match_type,
            'success': False,
            'message': '',
            'timestamp': datetime.now().isoformat()
        }

        try:
            if action == 'DELETE':
                response = self._execute_delete(order_id, match_type)
            elif action == 'MODIFY':
                response = self._execute_modify(operation)
            elif action == 'CLOSE':
                response = self._execute_close(order_id)
            else:
                response = {'status': 'error', 'message': f'未知操作类型: {action}'}

            # 处理响应
            if response and response.get('status') == 'success':
                result['success'] = True
                result['message'] = response.get('message', '操作成功')
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ✅ 操作成功: {action} {order_id}')
            else:
                result['message'] = response.get('message', '操作失败') if response else '无响应'
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ❌ 操作失败: {action} {order_id} - {result["message"]}')

        except Exception as e:
            result['message'] = f'执行异常: {e}'
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ❌ 执行异常: {action} {order_id} - {e}')

        return result

    def _execute_delete(self, order_id: str, match_type: str) -> Dict:
        """执行删除操作"""
        if match_type == 'not_found_ok':
            # 订单不存在，但这对DELETE操作是正常的
            return {'status': 'success', 'message': '订单可能已被删除（正常情况）'}

        return self.mt4_client.delete_pending_order(order_id)

    def _execute_modify(self, operation: Dict) -> Dict:
        """执行修改操作"""
        order_id = operation.get('orderId')
        new_stop_loss = operation.get('newStopLoss')
        new_take_profit = operation.get('newTakeProfit')
        new_entry_price = operation.get('newEntryPrice')

        return self.mt4_client.modify_order(
            order_id,
            new_entry_price,
            new_stop_loss,
            new_take_profit
        )

    def _execute_close(self, order_id: str) -> Dict:
        """执行平仓操作"""
        return self.mt4_client.close_order(order_id)
