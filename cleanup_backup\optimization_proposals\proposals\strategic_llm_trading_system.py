#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
战略性LLM交易系统
基于LLM深度分析能力的多货币对冲和组合交易策略
核心理念：LLM做战略决策，算法做战术执行
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json

class TradingStrategy(Enum):
    """交易策略类型"""
    SINGLE_PAIR = "单货币对交易"
    PAIR_HEDGING = "货币对对冲"
    BASKET_TRADING = "篮子交易"
    CORRELATION_ARBITRAGE = "相关性套利"
    CARRY_TRADE = "利差交易"
    MOMENTUM_PORTFOLIO = "动量组合"
    MEAN_REVERSION_PORTFOLIO = "均值回归组合"

class MarketRegime(Enum):
    """市场状态"""
    RISK_ON = "风险偏好"
    RISK_OFF = "风险规避"
    TRENDING = "趋势市场"
    RANGING = "震荡市场"
    VOLATILE = "高波动"
    CALM = "平静市场"

@dataclass
class CurrencyPairAnalysis:
    """货币对分析"""
    pair: str
    trend_direction: str
    trend_strength: float
    volatility_level: str
    support_resistance: Dict
    correlation_score: float
    fundamental_bias: str
    technical_score: float

@dataclass
class PortfolioPosition:
    """组合持仓"""
    pair: str
    direction: str  # LONG, SHORT
    size: float
    entry_price: float
    stop_loss: float
    take_profit: float
    weight: float  # 在组合中的权重
    hedge_pair: Optional[str] = None
    hedge_ratio: Optional[float] = None

@dataclass
class StrategicDecision:
    """战略决策"""
    strategy_type: TradingStrategy
    market_regime: MarketRegime
    positions: List[PortfolioPosition]
    total_risk: float
    expected_return: float
    holding_period: str  # SHORT, MEDIUM, LONG
    reasoning: str
    confidence: float
    risk_reward_ratio: float

class MultiCurrencyAnalyzer:
    """多货币分析器"""
    
    def __init__(self):
        # 主要货币对
        self.major_pairs = [
            'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF',
            'AUDUSD', 'USDCAD', 'NZDUSD'
        ]
        
        # 货币相关性矩阵（简化版）
        self.correlation_matrix = {
            'EURUSD': {'GBPUSD': 0.7, 'USDJPY': -0.6, 'USDCHF': -0.8},
            'GBPUSD': {'EURUSD': 0.7, 'USDJPY': -0.5, 'USDCHF': -0.6},
            'USDJPY': {'EURUSD': -0.6, 'GBPUSD': -0.5, 'USDCHF': 0.7},
            'USDCHF': {'EURUSD': -0.8, 'GBPUSD': -0.6, 'USDJPY': 0.7}
        }
        
        # 风险权重
        self.risk_weights = {
            'EURUSD': 1.0, 'GBPUSD': 1.2, 'USDJPY': 1.1, 'USDCHF': 0.9,
            'AUDUSD': 1.3, 'USDCAD': 1.0, 'NZDUSD': 1.4
        }
    
    def analyze_multi_currency_environment(self, market_data: Dict) -> Dict:
        """分析多货币环境"""
        
        # 1. 分析各货币对
        pair_analyses = {}
        for pair in self.major_pairs:
            if pair.lower() in market_data:
                analysis = self._analyze_single_pair(pair, market_data[pair.lower()])
                pair_analyses[pair] = analysis
        
        # 2. 识别市场状态
        market_regime = self._identify_market_regime(pair_analyses)
        
        # 3. 计算货币强弱
        currency_strength = self._calculate_currency_strength(pair_analyses)
        
        # 4. 识别相关性机会
        correlation_opportunities = self._identify_correlation_opportunities(pair_analyses)
        
        # 5. 风险评估
        market_risk = self._assess_market_risk(pair_analyses, market_regime)
        
        return {
            'pair_analyses': pair_analyses,
            'market_regime': market_regime,
            'currency_strength': currency_strength,
            'correlation_opportunities': correlation_opportunities,
            'market_risk': market_risk,
            'analysis_timestamp': datetime.now().isoformat()
        }
    
    def _analyze_single_pair(self, pair: str, data: Dict) -> CurrencyPairAnalysis:
        """分析单个货币对"""
        
        current_price = data.get('current_price', 1.0)
        
        # 简化的技术分析
        ma_20 = data.get('ma_20', current_price)
        ma_50 = data.get('ma_50', current_price)
        rsi = data.get('rsi', 50)
        atr = data.get('atr', 0.001)
        
        # 趋势分析
        if current_price > ma_20 > ma_50:
            trend_direction = "UP"
            trend_strength = 0.8
        elif current_price < ma_20 < ma_50:
            trend_direction = "DOWN"
            trend_strength = 0.8
        else:
            trend_direction = "SIDEWAYS"
            trend_strength = 0.3
        
        # 波动率分析
        volatility_level = "HIGH" if atr > 0.002 else "NORMAL" if atr > 0.001 else "LOW"
        
        # 技术评分
        technical_score = 0.5
        if trend_direction == "UP":
            technical_score += 0.3
        elif trend_direction == "DOWN":
            technical_score -= 0.3
        
        if 30 < rsi < 70:
            technical_score += 0.2
        
        return CurrencyPairAnalysis(
            pair=pair,
            trend_direction=trend_direction,
            trend_strength=trend_strength,
            volatility_level=volatility_level,
            support_resistance={'support': current_price * 0.995, 'resistance': current_price * 1.005},
            correlation_score=0.0,  # 后续计算
            fundamental_bias="NEUTRAL",  # 简化
            technical_score=technical_score
        )
    
    def _identify_market_regime(self, pair_analyses: Dict) -> MarketRegime:
        """识别市场状态"""
        
        # 计算整体趋势强度
        total_trend_strength = sum(analysis.trend_strength for analysis in pair_analyses.values())
        avg_trend_strength = total_trend_strength / len(pair_analyses) if pair_analyses else 0
        
        # 计算波动率水平
        high_vol_count = sum(1 for analysis in pair_analyses.values() if analysis.volatility_level == "HIGH")
        vol_ratio = high_vol_count / len(pair_analyses) if pair_analyses else 0
        
        # 判断市场状态
        if vol_ratio > 0.6:
            return MarketRegime.VOLATILE
        elif avg_trend_strength > 0.6:
            return MarketRegime.TRENDING
        elif avg_trend_strength < 0.4:
            return MarketRegime.RANGING
        else:
            return MarketRegime.CALM
    
    def _calculate_currency_strength(self, pair_analyses: Dict) -> Dict:
        """计算货币强弱"""
        
        currency_scores = {'USD': 0, 'EUR': 0, 'GBP': 0, 'JPY': 0, 'CHF': 0, 'AUD': 0, 'CAD': 0, 'NZD': 0}
        
        for pair, analysis in pair_analyses.items():
            base_currency = pair[:3]
            quote_currency = pair[3:]
            
            if analysis.trend_direction == "UP":
                # 基础货币强，报价货币弱
                currency_scores[base_currency] += analysis.trend_strength
                currency_scores[quote_currency] -= analysis.trend_strength
            elif analysis.trend_direction == "DOWN":
                # 基础货币弱，报价货币强
                currency_scores[base_currency] -= analysis.trend_strength
                currency_scores[quote_currency] += analysis.trend_strength
        
        # 标准化评分
        max_score = max(abs(score) for score in currency_scores.values()) if currency_scores.values() else 1
        if max_score > 0:
            currency_scores = {curr: score/max_score for curr, score in currency_scores.items()}
        
        return currency_scores
    
    def _identify_correlation_opportunities(self, pair_analyses: Dict) -> List[Dict]:
        """识别相关性机会"""
        
        opportunities = []
        
        for pair1, analysis1 in pair_analyses.items():
            for pair2, analysis2 in pair_analyses.items():
                if pair1 >= pair2:  # 避免重复
                    continue
                
                # 获取相关性
                correlation = self.correlation_matrix.get(pair1, {}).get(pair2, 0)
                
                # 寻找背离机会
                if abs(correlation) > 0.6:  # 高相关性
                    direction_diff = (analysis1.technical_score - analysis2.technical_score)
                    
                    if abs(direction_diff) > 0.4:  # 方向背离
                        opportunities.append({
                            'type': 'CORRELATION_DIVERGENCE',
                            'pair1': pair1,
                            'pair2': pair2,
                            'correlation': correlation,
                            'divergence_score': abs(direction_diff),
                            'suggested_action': 'PAIR_TRADE'
                        })
        
        return opportunities
    
    def _assess_market_risk(self, pair_analyses: Dict, market_regime: MarketRegime) -> Dict:
        """评估市场风险"""
        
        # 计算整体风险水平
        risk_factors = []
        
        # 波动率风险
        high_vol_pairs = [pair for pair, analysis in pair_analyses.items() 
                         if analysis.volatility_level == "HIGH"]
        vol_risk = len(high_vol_pairs) / len(pair_analyses) if pair_analyses else 0
        
        # 趋势一致性风险
        up_trends = sum(1 for analysis in pair_analyses.values() if analysis.trend_direction == "UP")
        down_trends = sum(1 for analysis in pair_analyses.values() if analysis.trend_direction == "DOWN")
        trend_concentration = max(up_trends, down_trends) / len(pair_analyses) if pair_analyses else 0
        
        # 综合风险评分
        overall_risk = (vol_risk * 0.4 + trend_concentration * 0.6)
        
        return {
            'overall_risk': overall_risk,
            'volatility_risk': vol_risk,
            'trend_concentration_risk': trend_concentration,
            'high_risk_pairs': high_vol_pairs,
            'risk_level': 'HIGH' if overall_risk > 0.7 else 'MEDIUM' if overall_risk > 0.4 else 'LOW'
        }

class StrategicTradingEngine:
    """战略交易引擎"""
    
    def __init__(self):
        self.analyzer = MultiCurrencyAnalyzer()
        self.max_portfolio_risk = 0.05  # 最大组合风险5%
        self.max_single_position_risk = 0.02  # 单笔最大风险2%
        
    def generate_strategic_decision(self, multi_currency_analysis: Dict) -> StrategicDecision:
        """生成战略决策"""
        
        market_regime = multi_currency_analysis['market_regime']
        pair_analyses = multi_currency_analysis['pair_analyses']
        currency_strength = multi_currency_analysis['currency_strength']
        correlation_opportunities = multi_currency_analysis['correlation_opportunities']
        market_risk = multi_currency_analysis['market_risk']
        
        # 根据市场状态选择策略
        if market_regime == MarketRegime.TRENDING:
            return self._generate_momentum_strategy(pair_analyses, currency_strength, market_risk)
        elif market_regime == MarketRegime.RANGING:
            return self._generate_mean_reversion_strategy(pair_analyses, market_risk)
        elif market_regime == MarketRegime.VOLATILE:
            return self._generate_hedged_strategy(pair_analyses, correlation_opportunities, market_risk)
        else:
            return self._generate_conservative_strategy(pair_analyses, market_risk)
    
    def _generate_momentum_strategy(self, pair_analyses: Dict, currency_strength: Dict, market_risk: Dict) -> StrategicDecision:
        """生成动量策略"""
        
        positions = []
        
        # 选择最强和最弱的货币
        strongest_currency = max(currency_strength.items(), key=lambda x: x[1])
        weakest_currency = min(currency_strength.items(), key=lambda x: x[1])
        
        # 寻找最佳交易对
        best_pairs = []
        for pair, analysis in pair_analyses.items():
            base_curr = pair[:3]
            quote_curr = pair[3:]
            
            # 强货币对弱货币的组合
            if ((base_curr == strongest_currency[0] and quote_curr == weakest_currency[0]) or
                (base_curr == weakest_currency[0] and quote_curr == strongest_currency[0])):
                
                direction = "LONG" if base_curr == strongest_currency[0] else "SHORT"
                
                position = PortfolioPosition(
                    pair=pair,
                    direction=direction,
                    size=0.15,  # 15%仓位
                    entry_price=0,  # 由执行系统确定
                    stop_loss=0,
                    take_profit=0,
                    weight=0.6  # 主要持仓
                )
                positions.append(position)
                break
        
        # 添加辅助持仓
        for pair, analysis in pair_analyses.items():
            if len(positions) >= 3:  # 最多3个持仓
                break
                
            if analysis.trend_strength > 0.6 and pair not in [p.pair for p in positions]:
                direction = "LONG" if analysis.trend_direction == "UP" else "SHORT"
                
                position = PortfolioPosition(
                    pair=pair,
                    direction=direction,
                    size=0.08,  # 8%仓位
                    entry_price=0,
                    stop_loss=0,
                    take_profit=0,
                    weight=0.2  # 辅助持仓
                )
                positions.append(position)
        
        return StrategicDecision(
            strategy_type=TradingStrategy.MOMENTUM_PORTFOLIO,
            market_regime=MarketRegime.TRENDING,
            positions=positions,
            total_risk=0.04,
            expected_return=0.08,
            holding_period="MEDIUM",
            reasoning="趋势市场中采用动量策略，做多强势货币，做空弱势货币",
            confidence=0.75,
            risk_reward_ratio=2.0
        )
    
    def _generate_mean_reversion_strategy(self, pair_analyses: Dict, market_risk: Dict) -> StrategicDecision:
        """生成均值回归策略"""
        
        positions = []
        
        # 寻找超买超卖的货币对
        for pair, analysis in pair_analyses.items():
            if analysis.technical_score > 0.7:  # 超买
                position = PortfolioPosition(
                    pair=pair,
                    direction="SHORT",
                    size=0.06,  # 6%仓位
                    entry_price=0,
                    stop_loss=0,
                    take_profit=0,
                    weight=1.0 / min(3, len(pair_analyses))  # 平均分配
                )
                positions.append(position)
            elif analysis.technical_score < 0.3:  # 超卖
                position = PortfolioPosition(
                    pair=pair,
                    direction="LONG",
                    size=0.06,
                    entry_price=0,
                    stop_loss=0,
                    take_profit=0,
                    weight=1.0 / min(3, len(pair_analyses))
                )
                positions.append(position)
        
        return StrategicDecision(
            strategy_type=TradingStrategy.MEAN_REVERSION_PORTFOLIO,
            market_regime=MarketRegime.RANGING,
            positions=positions[:3],  # 最多3个持仓
            total_risk=0.03,
            expected_return=0.05,
            holding_period="SHORT",
            reasoning="震荡市场中采用均值回归策略，在极值位置反向交易",
            confidence=0.65,
            risk_reward_ratio=1.5
        )
    
    def _generate_hedged_strategy(self, pair_analyses: Dict, correlation_opportunities: List, market_risk: Dict) -> StrategicDecision:
        """生成对冲策略"""
        
        positions = []
        
        # 使用相关性对冲
        for opportunity in correlation_opportunities[:2]:  # 最多2个对冲组合
            pair1 = opportunity['pair1']
            pair2 = opportunity['pair2']
            correlation = opportunity['correlation']
            
            # 确定对冲方向
            analysis1 = pair_analyses[pair1]
            analysis2 = pair_analyses[pair2]
            
            if correlation > 0:  # 正相关，反向操作
                direction1 = "LONG" if analysis1.technical_score > analysis2.technical_score else "SHORT"
                direction2 = "SHORT" if direction1 == "LONG" else "LONG"
            else:  # 负相关，同向操作
                direction1 = "LONG" if analysis1.technical_score > 0.5 else "SHORT"
                direction2 = direction1
            
            # 计算对冲比例
            hedge_ratio = abs(correlation) * 0.8  # 80%对冲
            
            position1 = PortfolioPosition(
                pair=pair1,
                direction=direction1,
                size=0.08,
                entry_price=0,
                stop_loss=0,
                take_profit=0,
                weight=0.5,
                hedge_pair=pair2,
                hedge_ratio=hedge_ratio
            )
            
            position2 = PortfolioPosition(
                pair=pair2,
                direction=direction2,
                size=0.08 * hedge_ratio,
                entry_price=0,
                stop_loss=0,
                take_profit=0,
                weight=0.5,
                hedge_pair=pair1,
                hedge_ratio=hedge_ratio
            )
            
            positions.extend([position1, position2])
        
        return StrategicDecision(
            strategy_type=TradingStrategy.CORRELATION_ARBITRAGE,
            market_regime=MarketRegime.VOLATILE,
            positions=positions,
            total_risk=0.02,
            expected_return=0.04,
            holding_period="MEDIUM",
            reasoning="高波动市场中采用相关性对冲策略，降低整体风险",
            confidence=0.8,
            risk_reward_ratio=2.5
        )
    
    def _generate_conservative_strategy(self, pair_analyses: Dict, market_risk: Dict) -> StrategicDecision:
        """生成保守策略"""
        
        # 市场不明确时，选择最稳定的货币对进行小仓位交易
        positions = []
        
        # 选择波动率最低的货币对
        stable_pairs = [(pair, analysis) for pair, analysis in pair_analyses.items() 
                       if analysis.volatility_level == "LOW"]
        
        if stable_pairs:
            best_pair = max(stable_pairs, key=lambda x: abs(x[1].technical_score - 0.5))
            pair, analysis = best_pair
            
            direction = "LONG" if analysis.technical_score > 0.5 else "SHORT"
            
            position = PortfolioPosition(
                pair=pair,
                direction=direction,
                size=0.03,  # 3%小仓位
                entry_price=0,
                stop_loss=0,
                take_profit=0,
                weight=1.0
            )
            positions.append(position)
        
        return StrategicDecision(
            strategy_type=TradingStrategy.SINGLE_PAIR,
            market_regime=MarketRegime.CALM,
            positions=positions,
            total_risk=0.015,
            expected_return=0.02,
            holding_period="SHORT",
            reasoning="市场不明确时采用保守策略，小仓位交易最稳定的货币对",
            confidence=0.5,
            risk_reward_ratio=1.2
        )

# 测试函数
def test_strategic_system():
    """测试战略交易系统"""
    print("🚀 战略性LLM交易系统测试")
    print("=" * 60)
    
    # 创建系统
    analyzer = MultiCurrencyAnalyzer()
    engine = StrategicTradingEngine()
    
    # 模拟多货币数据
    market_data = {
        'eurusd': {
            'current_price': 1.0850,
            'ma_20': 1.0830,
            'ma_50': 1.0800,
            'rsi': 65,
            'atr': 0.0015
        },
        'gbpusd': {
            'current_price': 1.2650,
            'ma_20': 1.2600,
            'ma_50': 1.2550,
            'rsi': 70,
            'atr': 0.0020
        },
        'usdjpy': {
            'current_price': 150.50,
            'ma_20': 150.00,
            'ma_50': 149.50,
            'rsi': 45,
            'atr': 0.8
        }
    }
    
    # 执行分析
    print("📊 多货币环境分析...")
    multi_analysis = analyzer.analyze_multi_currency_environment(market_data)
    
    print(f"   市场状态: {multi_analysis['market_regime'].value}")
    print(f"   分析货币对: {len(multi_analysis['pair_analyses'])}")
    print(f"   相关性机会: {len(multi_analysis['correlation_opportunities'])}")
    print(f"   市场风险: {multi_analysis['market_risk']['risk_level']}")
    
    # 生成战略决策
    print("\n🎯 生成战略决策...")
    decision = engine.generate_strategic_decision(multi_analysis)
    
    print(f"   策略类型: {decision.strategy_type.value}")
    print(f"   持仓数量: {len(decision.positions)}")
    print(f"   总风险: {decision.total_risk:.1%}")
    print(f"   预期收益: {decision.expected_return:.1%}")
    print(f"   风险回报比: 1:{decision.risk_reward_ratio:.1f}")
    print(f"   持有期: {decision.holding_period}")
    print(f"   置信度: {decision.confidence:.1%}")
    print(f"   策略理由: {decision.reasoning}")
    
    # 显示具体持仓
    print("\n📋 具体持仓安排:")
    for i, position in enumerate(decision.positions, 1):
        print(f"   {i}. {position.pair} {position.direction} {position.size:.1%}")
        if position.hedge_pair:
            print(f"      对冲: {position.hedge_pair} (比例: {position.hedge_ratio:.1%})")

if __name__ == "__main__":
    test_strategic_system()
