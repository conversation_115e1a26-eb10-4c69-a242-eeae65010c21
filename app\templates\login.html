<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外汇交易系统 - 登录</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            background-color: #000;
            color: #fff;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .login-container {
            max-width: 400px;
            margin: 100px auto;
            padding: 30px;
            border-radius: 10px;
            background-color: #111;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-logo img {
            max-width: 150px;
        }
        .form-control {
            background-color: #222;
            border: 1px solid #333;
            color: #fff;
            padding: 12px;
        }
        .form-control:focus {
            background-color: #222;
            border-color: #666;
            color: #fff;
            box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
        }
        .btn-primary {
            background-color: #007bff;
            border: none;
            padding: 12px;
            width: 100%;
            margin-top: 20px;
        }
        .alert {
            margin-top: 20px;
        }
        .server-select {
            margin-top: 20px;
        }
        .server-select label {
            margin-bottom: 10px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-logo">
                <h2>外汇交易系统</h2>
                <p>专业的外汇交易分析与执行平台</p>
            </div>
            
            {% if error %}
            <div class="alert alert-danger" role="alert">
                {{ error }}
            </div>
            {% endif %}
            
            {% if success %}
            <div class="alert alert-success" role="alert">
                {{ success }}
            </div>
            {% endif %}
            
            <form method="post" action="/login">
                <div class="mb-3">
                    <label for="auth_code" class="form-label">授权码</label>
                    <input type="text" class="form-control" id="auth_code" name="auth_code" placeholder="请输入您的授权码" required>
                </div>
                
                <div class="server-select">
                    <label class="form-label">服务器选择</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="server_type" id="server_auth" value="auth" checked>
                        <label class="form-check-label" for="server_auth">
                            使用授权码对应的服务器
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="server_type" id="server_local" value="local">
                        <label class="form-check-label" for="server_local">
                            使用本地服务器 (127.0.0.1:5555)
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="server_type" id="server_custom" value="custom">
                        <label class="form-check-label" for="server_custom">
                            自定义服务器
                        </label>
                    </div>
                    <div class="mt-2" id="custom_server_div" style="display: none;">
                        <input type="text" class="form-control" id="custom_server" name="custom_server" placeholder="例如: tcp://*************:5555">
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">登录</button>
            </form>
            
            <div class="footer">
                <p>版权所有 &copy; 2025 外汇交易系统</p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示/隐藏自定义服务器输入框
        document.querySelectorAll('input[name="server_type"]').forEach(function(radio) {
            radio.addEventListener('change', function() {
                if (this.value === 'custom') {
                    document.getElementById('custom_server_div').style.display = 'block';
                } else {
                    document.getElementById('custom_server_div').style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
