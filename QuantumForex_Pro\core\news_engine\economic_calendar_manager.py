#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
经济日历管理器
管理和分析经济事件对外汇市场的影响
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class EventImportance(Enum):
    """事件重要性等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class EventType(Enum):
    """事件类型"""
    INTEREST_RATE = "interest_rate"
    EMPLOYMENT = "employment"
    INFLATION = "inflation"
    GDP = "gdp"
    PMI = "pmi"
    CENTRAL_BANK = "central_bank"
    TRADE = "trade"
    CONSUMER = "consumer"
    OTHER = "other"

@dataclass
class EconomicEvent:
    """经济事件"""
    id: str
    title: str
    country: str
    currency: str
    event_type: EventType
    importance: EventImportance
    scheduled_time: datetime
    actual_value: Optional[float] = None
    forecast_value: Optional[float] = None
    previous_value: Optional[float] = None
    unit: str = ""
    description: str = ""
    market_impact: float = 0.0  # 0.0 to 1.0
    affected_pairs: List[str] = None

class EconomicCalendarManager:
    """经济日历管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 事件缓存
        self.events_cache = []
        self.last_update = None
        
        # 货币对应国家映射
        self.currency_country_mapping = {
            'USD': 'United States',
            'EUR': 'Eurozone',
            'GBP': 'United Kingdom',
            'JPY': 'Japan',
            'AUD': 'Australia',
            'NZD': 'New Zealand',
            'CAD': 'Canada',
            'CHF': 'Switzerland'
        }
        
        # 重要事件关键词
        self.important_event_keywords = {
            EventType.INTEREST_RATE: ['利率', '央行', 'interest rate', 'fed rate', 'ecb rate'],
            EventType.EMPLOYMENT: ['就业', '失业率', '非农', 'employment', 'unemployment', 'nfp', 'jobless'],
            EventType.INFLATION: ['通胀', 'CPI', 'PPI', 'inflation', 'consumer price'],
            EventType.GDP: ['GDP', '经济增长', 'economic growth', 'gross domestic'],
            EventType.PMI: ['PMI', '制造业', 'manufacturing', 'services'],
            EventType.CENTRAL_BANK: ['央行', '货币政策', 'central bank', 'monetary policy', 'FOMC'],
            EventType.TRADE: ['贸易', '进出口', 'trade', 'export', 'import', 'trade balance'],
            EventType.CONSUMER: ['消费', '零售', 'consumer', 'retail', 'spending']
        }
        
        # 事件影响权重
        self.event_impact_weights = {
            EventType.INTEREST_RATE: 1.0,
            EventType.CENTRAL_BANK: 0.9,
            EventType.EMPLOYMENT: 0.8,
            EventType.INFLATION: 0.8,
            EventType.GDP: 0.7,
            EventType.PMI: 0.6,
            EventType.TRADE: 0.5,
            EventType.CONSUMER: 0.5,
            EventType.OTHER: 0.3
        }
    
    def collect_calendar_events(self, days_ahead: int = 7) -> List[EconomicEvent]:
        """收集经济日历事件
        
        Args:
            days_ahead: 未来天数
            
        Returns:
            List[EconomicEvent]: 事件列表
        """
        try:
            all_events = []
            
            # 1. 从数据库获取事件
            db_events = self._get_events_from_database(days_ahead)
            all_events.extend(db_events)
            
            # 2. 从外部API获取事件（模拟实现）
            api_events = self._get_events_from_apis(days_ahead)
            all_events.extend(api_events)
            
            # 3. 过滤和分析事件
            filtered_events = self._filter_forex_events(all_events)
            analyzed_events = self._analyze_events(filtered_events)
            
            # 4. 更新缓存
            self.events_cache = analyzed_events
            self.last_update = datetime.now()
            
            self.logger.info(f"收集到 {len(analyzed_events)} 个外汇相关经济事件")
            
            return analyzed_events
            
        except Exception as e:
            self.logger.error(f"收集经济日历事件失败: {e}")
            return []
    
    def _get_events_from_database(self, days_ahead: int) -> List[EconomicEvent]:
        """从数据库获取事件"""
        try:
            from utils.db_client import get_latest_calendar
            
            # 获取数据库中的日历事件
            db_events_data = get_latest_calendar(limit=100)
            
            events = []
            for item in db_events_data:
                # 解析时间
                try:
                    event_time = datetime.strptime(item['time'], '%Y-%m-%d %H:%M:%S')
                except:
                    continue
                
                # 检查时间范围
                if event_time < datetime.now() or event_time > datetime.now() + timedelta(days=days_ahead):
                    continue
                
                event = EconomicEvent(
                    id=f"db_{item['id']}",
                    title=item.get('title', ''),
                    country=item.get('country', ''),
                    currency=self._get_currency_from_country(item.get('country', '')),
                    event_type=self._classify_event_type(item.get('title', '')),
                    importance=self._parse_importance(item.get('importance', 'MEDIUM')),
                    scheduled_time=event_time,
                    actual_value=item.get('actual'),
                    forecast_value=item.get('forecast'),
                    previous_value=item.get('previous'),
                    description=item.get('description', ''),
                    affected_pairs=[]
                )
                events.append(event)
            
            return events
            
        except Exception as e:
            self.logger.error(f"从数据库获取事件失败: {e}")
            return []
    
    def _get_events_from_apis(self, days_ahead: int) -> List[EconomicEvent]:
        """从外部API获取事件（模拟实现）"""
        try:
            # 模拟重要经济事件
            mock_events = [
                {
                    'id': 'api_cal_001',
                    'title': 'FOMC利率决议',
                    'country': 'United States',
                    'currency': 'USD',
                    'event_type': 'interest_rate',
                    'importance': 'critical',
                    'scheduled_time': datetime.now() + timedelta(days=2, hours=14),
                    'forecast': 5.25,
                    'previous': 5.00,
                    'description': '美联储联邦公开市场委员会利率决议'
                },
                {
                    'id': 'api_cal_002',
                    'title': '美国非农就业人数',
                    'country': 'United States',
                    'currency': 'USD',
                    'event_type': 'employment',
                    'importance': 'high',
                    'scheduled_time': datetime.now() + timedelta(days=1, hours=21, minutes=30),
                    'forecast': 180000,
                    'previous': 150000,
                    'description': '美国非农就业人数变化'
                },
                {
                    'id': 'api_cal_003',
                    'title': '欧元区CPI年率',
                    'country': 'Eurozone',
                    'currency': 'EUR',
                    'event_type': 'inflation',
                    'importance': 'high',
                    'scheduled_time': datetime.now() + timedelta(days=3, hours=17),
                    'forecast': 2.4,
                    'previous': 2.6,
                    'description': '欧元区消费者价格指数年率'
                },
                {
                    'id': 'api_cal_004',
                    'title': '英国GDP季率',
                    'country': 'United Kingdom',
                    'currency': 'GBP',
                    'event_type': 'gdp',
                    'importance': 'medium',
                    'scheduled_time': datetime.now() + timedelta(days=4, hours=16),
                    'forecast': 0.2,
                    'previous': 0.1,
                    'description': '英国国内生产总值季度变化率'
                },
                {
                    'id': 'api_cal_005',
                    'title': '澳洲联储利率决议',
                    'country': 'Australia',
                    'currency': 'AUD',
                    'event_type': 'interest_rate',
                    'importance': 'high',
                    'scheduled_time': datetime.now() + timedelta(days=5, hours=12, minutes=30),
                    'forecast': 4.35,
                    'previous': 4.35,
                    'description': '澳大利亚储备银行利率决议'
                }
            ]
            
            events = []
            for item in mock_events:
                event = EconomicEvent(
                    id=item['id'],
                    title=item['title'],
                    country=item['country'],
                    currency=item['currency'],
                    event_type=EventType(item['event_type']),
                    importance=EventImportance(item['importance']),
                    scheduled_time=item['scheduled_time'],
                    forecast_value=item.get('forecast'),
                    previous_value=item.get('previous'),
                    description=item['description'],
                    affected_pairs=[]
                )
                events.append(event)
            
            return events
            
        except Exception as e:
            self.logger.error(f"从API获取事件失败: {e}")
            return []
    
    def _filter_forex_events(self, events: List[EconomicEvent]) -> List[EconomicEvent]:
        """过滤外汇相关事件"""
        forex_currencies = ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'NZD', 'CAD', 'CHF']
        
        filtered_events = []
        for event in events:
            if event.currency in forex_currencies:
                filtered_events.append(event)
        
        return filtered_events
    
    def _analyze_events(self, events: List[EconomicEvent]) -> List[EconomicEvent]:
        """分析事件影响"""
        analyzed_events = []
        
        for event in events:
            # 1. 计算市场影响
            event.market_impact = self._calculate_market_impact(event)
            
            # 2. 识别影响的货币对
            event.affected_pairs = self._identify_affected_pairs(event)
            
            analyzed_events.append(event)
        
        return analyzed_events
    
    def _calculate_market_impact(self, event: EconomicEvent) -> float:
        """计算市场影响分数"""
        impact = 0.0
        
        # 基于事件类型权重
        type_weight = self.event_impact_weights.get(event.event_type, 0.3)
        impact += type_weight * 0.4
        
        # 基于重要性等级
        importance_weights = {
            EventImportance.LOW: 0.2,
            EventImportance.MEDIUM: 0.5,
            EventImportance.HIGH: 0.8,
            EventImportance.CRITICAL: 1.0
        }
        importance_weight = importance_weights.get(event.importance, 0.5)
        impact += importance_weight * 0.4
        
        # 基于时间临近性
        hours_until = (event.scheduled_time - datetime.now()).total_seconds() / 3600
        if hours_until < 0:
            time_weight = 0.1  # 已过期事件
        elif hours_until < 2:
            time_weight = 1.0  # 2小时内
        elif hours_until < 24:
            time_weight = 0.8  # 24小时内
        elif hours_until < 72:
            time_weight = 0.6  # 3天内
        else:
            time_weight = 0.4  # 3天以上
        
        impact += time_weight * 0.2
        
        return min(1.0, impact)
    
    def _identify_affected_pairs(self, event: EconomicEvent) -> List[str]:
        """识别受影响的货币对"""
        affected_pairs = []
        
        # 主要货币对映射
        currency_pairs = {
            'USD': ['EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCHF', 'USDCAD', 'USDJPY'],
            'EUR': ['EURUSD', 'EURGBP', 'EURJPY', 'EURAUD', 'EURCHF'],
            'GBP': ['GBPUSD', 'EURGBP', 'GBPJPY', 'GBPAUD', 'GBPCAD'],
            'JPY': ['USDJPY', 'EURJPY', 'GBPJPY', 'AUDJPY', 'CADJPY'],
            'AUD': ['AUDUSD', 'EURAUD', 'GBPAUD', 'AUDJPY', 'AUDCAD'],
            'NZD': ['NZDUSD', 'AUDNZD', 'NZDJPY'],
            'CAD': ['USDCAD', 'GBPCAD', 'AUDCAD', 'CADJPY'],
            'CHF': ['USDCHF', 'EURCHF', 'GBPCHF', 'CHFJPY']
        }
        
        if event.currency in currency_pairs:
            affected_pairs = currency_pairs[event.currency]
        
        return affected_pairs
    
    def _get_currency_from_country(self, country: str) -> str:
        """从国家获取货币代码"""
        country_currency_map = {
            'United States': 'USD',
            'Eurozone': 'EUR',
            'Germany': 'EUR',
            'France': 'EUR',
            'Italy': 'EUR',
            'Spain': 'EUR',
            'United Kingdom': 'GBP',
            'Japan': 'JPY',
            'Australia': 'AUD',
            'New Zealand': 'NZD',
            'Canada': 'CAD',
            'Switzerland': 'CHF'
        }
        
        for country_name, currency in country_currency_map.items():
            if country_name.lower() in country.lower():
                return currency
        
        return 'USD'  # 默认
    
    def _classify_event_type(self, title: str) -> EventType:
        """分类事件类型"""
        title_lower = title.lower()
        
        for event_type, keywords in self.important_event_keywords.items():
            for keyword in keywords:
                if keyword.lower() in title_lower:
                    return event_type
        
        return EventType.OTHER
    
    def _parse_importance(self, importance_str: str) -> EventImportance:
        """解析重要性等级"""
        importance_map = {
            'LOW': EventImportance.LOW,
            'MEDIUM': EventImportance.MEDIUM,
            'HIGH': EventImportance.HIGH,
            'CRITICAL': EventImportance.CRITICAL
        }
        return importance_map.get(importance_str.upper(), EventImportance.MEDIUM)
    
    def get_upcoming_events(self, hours_ahead: int = 24) -> List[EconomicEvent]:
        """获取即将到来的事件"""
        if not self.events_cache or not self.last_update:
            return self.collect_calendar_events()
        
        # 检查缓存是否过期（1小时）
        if (datetime.now() - self.last_update).total_seconds() > 3600:
            return self.collect_calendar_events()
        
        # 过滤指定时间范围内的事件
        now = datetime.now()
        cutoff_time = now + timedelta(hours=hours_ahead)
        
        upcoming_events = [
            event for event in self.events_cache 
            if now <= event.scheduled_time <= cutoff_time
        ]
        
        # 按时间排序
        upcoming_events.sort(key=lambda x: x.scheduled_time)
        
        return upcoming_events
    
    def get_high_impact_events(self, hours_ahead: int = 48) -> List[EconomicEvent]:
        """获取高影响力事件"""
        upcoming_events = self.get_upcoming_events(hours_ahead)
        high_impact_events = [event for event in upcoming_events if event.market_impact >= 0.7]
        
        # 按影响分数排序
        high_impact_events.sort(key=lambda x: x.market_impact, reverse=True)
        
        return high_impact_events
    
    def get_events_for_currency_pair(self, symbol: str, hours_ahead: int = 24) -> List[EconomicEvent]:
        """获取影响特定货币对的事件"""
        upcoming_events = self.get_upcoming_events(hours_ahead)
        
        relevant_events = []
        for event in upcoming_events:
            if symbol in event.affected_pairs:
                relevant_events.append(event)
        
        # 按影响分数排序
        relevant_events.sort(key=lambda x: x.market_impact, reverse=True)
        
        return relevant_events

# 创建全局实例
calendar_manager = EconomicCalendarManager()
