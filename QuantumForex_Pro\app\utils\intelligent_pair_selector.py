"""
QuantumForex Pro - 智能货币对选择器
基于市场条件和相关性分析选择最优货币对组合
"""

import logging
from typing import List, Dict, Any

# 配置日志
logger = logging.getLogger(__name__)

class IntelligentPairSelector:
    """智能货币对选择器"""
    
    def __init__(self):
        # 支持的货币对（移除黄金，保留7个货币对）
        self.supported_symbols = [
            'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD',
            'USDCHF', 'USDCAD', 'USDJPY'
        ]
        
        # 货币对特性配置
        self.pair_characteristics = {
            'EURUSD': {
                'volatility': 'medium',
                'liquidity': 'high',
                'spread': 'low',
                'trading_hours': 'global',
                'correlation_group': 'eur_majors'
            },
            'GBPUSD': {
                'volatility': 'high',
                'liquidity': 'high',
                'spread': 'medium',
                'trading_hours': 'london_ny',
                'correlation_group': 'gbp_majors'
            },
            'AUDUSD': {
                'volatility': 'medium',
                'liquidity': 'medium',
                'spread': 'medium',
                'trading_hours': 'asia_pacific',
                'correlation_group': 'commodity_currencies'
            },
            'NZDUSD': {
                'volatility': 'medium',
                'liquidity': 'medium',
                'spread': 'medium',
                'trading_hours': 'asia_pacific',
                'correlation_group': 'commodity_currencies'
            },
            'USDCHF': {
                'volatility': 'medium',
                'liquidity': 'high',
                'spread': 'low',
                'trading_hours': 'european',
                'correlation_group': 'safe_haven'
            },
            'USDCAD': {
                'volatility': 'medium',
                'liquidity': 'medium',
                'spread': 'medium',
                'trading_hours': 'north_american',
                'correlation_group': 'commodity_currencies'
            },
            'USDJPY': {
                'volatility': 'medium',
                'liquidity': 'high',
                'spread': 'low',
                'trading_hours': 'asian',
                'correlation_group': 'safe_haven'
            }
        }
        
        # 相关性矩阵（简化版）
        self.correlation_matrix = {
            'EURUSD': {'GBPUSD': 0.7, 'AUDUSD': 0.6, 'NZDUSD': 0.5, 'USDCHF': -0.8, 'USDCAD': 0.3, 'USDJPY': -0.2},
            'GBPUSD': {'EURUSD': 0.7, 'AUDUSD': 0.5, 'NZDUSD': 0.4, 'USDCHF': -0.6, 'USDCAD': 0.2, 'USDJPY': -0.1},
            'AUDUSD': {'EURUSD': 0.6, 'GBPUSD': 0.5, 'NZDUSD': 0.8, 'USDCHF': -0.4, 'USDCAD': 0.6, 'USDJPY': 0.1},
            'NZDUSD': {'EURUSD': 0.5, 'GBPUSD': 0.4, 'AUDUSD': 0.8, 'USDCHF': -0.3, 'USDCAD': 0.5, 'USDJPY': 0.0},
            'USDCHF': {'EURUSD': -0.8, 'GBPUSD': -0.6, 'AUDUSD': -0.4, 'NZDUSD': -0.3, 'USDCAD': -0.2, 'USDJPY': 0.3},
            'USDCAD': {'EURUSD': 0.3, 'GBPUSD': 0.2, 'AUDUSD': 0.6, 'NZDUSD': 0.5, 'USDCHF': -0.2, 'USDJPY': 0.1},
            'USDJPY': {'EURUSD': -0.2, 'GBPUSD': -0.1, 'AUDUSD': 0.1, 'NZDUSD': 0.0, 'USDCHF': 0.3, 'USDCAD': 0.1}
        }
    
    def select_optimal_currency_pairs(self, max_pairs: int = 4) -> List[str]:
        """
        选择最优货币对组合
        
        Args:
            max_pairs (int): 最大选择数量
            
        Returns:
            List[str]: 选择的货币对列表
        """
        try:
            logger.info(f"开始智能选择最多{max_pairs}个货币对...")
            
            # 1. 基础筛选 - 确保包含主要货币对
            core_pairs = ['EURUSD', 'GBPUSD', 'USDJPY']  # 核心货币对
            selected_pairs = core_pairs.copy()
            
            # 2. 添加互补货币对
            remaining_pairs = [pair for pair in self.supported_symbols if pair not in selected_pairs]
            
            # 3. 基于相关性选择互补货币对
            for pair in remaining_pairs:
                if len(selected_pairs) >= max_pairs:
                    break
                
                # 检查与已选择货币对的相关性
                avg_correlation = self._calculate_average_correlation(pair, selected_pairs)
                
                # 如果平均相关性不太高，则添加
                if abs(avg_correlation) < 0.6:
                    selected_pairs.append(pair)
            
            # 4. 如果还没达到最大数量，添加剩余货币对
            while len(selected_pairs) < max_pairs and len(selected_pairs) < len(self.supported_symbols):
                for pair in remaining_pairs:
                    if pair not in selected_pairs:
                        selected_pairs.append(pair)
                        break
            
            logger.info(f"智能选择完成，选中货币对: {selected_pairs}")
            return selected_pairs[:max_pairs]
            
        except Exception as e:
            logger.error(f"智能选择货币对失败: {e}")
            # 返回默认组合
            return self.supported_symbols[:max_pairs]
    
    def _calculate_average_correlation(self, target_pair: str, selected_pairs: List[str]) -> float:
        """
        计算目标货币对与已选择货币对的平均相关性
        
        Args:
            target_pair (str): 目标货币对
            selected_pairs (List[str]): 已选择的货币对列表
            
        Returns:
            float: 平均相关性
        """
        if not selected_pairs or target_pair not in self.correlation_matrix:
            return 0.0
        
        correlations = []
        for pair in selected_pairs:
            if pair in self.correlation_matrix[target_pair]:
                correlations.append(self.correlation_matrix[target_pair][pair])
        
        return sum(correlations) / len(correlations) if correlations else 0.0
