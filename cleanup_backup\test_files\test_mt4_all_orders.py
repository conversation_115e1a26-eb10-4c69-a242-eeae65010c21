"""
MT4所有订单测试脚本
用于测试获取所有订单，包括活跃订单和挂单
"""
import os
import sys
import time
from app.utils.mt4_client import mt4_client

def test_mt4_all_orders():
    """测试获取MT4所有订单"""
    try:
        print('开始测试获取所有订单...')
        
        # 连接到MT4服务器
        print('连接到MT4服务器...')
        connected = mt4_client.connect()
        print(f'连接结果: {connected}')
        
        if not connected:
            print('连接MT4服务器失败，无法继续测试')
            return
        
        # 获取所有订单
        print('\n获取所有订单...')
        all_orders = mt4_client.get_all_orders()
        print(f'所有订单: {all_orders}')
        
        # 分别显示活跃订单和挂单
        print('\n活跃订单:')
        for order in all_orders.get('active_orders', []):
            print(f'  - {order}')
        
        print('\n挂单:')
        for order in all_orders.get('pending_orders', []):
            print(f'  - {order}')
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_mt4_all_orders()
