#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantumForex MLTrainer - 训练所有模型脚本
一键训练所有机器学习模型
"""

import sys
import os
import json
import logging
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def setup_logging():
    """设置日志"""
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )

    return logging.getLogger(__name__)

def main():
    """主训练流程"""
    logger = setup_logging()

    logger.info("🚀 QuantumForex MLTrainer 开始训练")
    logger.info("=" * 60)

    try:
        # 导入必要模块
        from data_collector.forex_data_collector import forex_collector
        from data_collector.trade_result_collector import trade_collector
        from feature_engineering.technical_features import technical_engine
        from feature_engineering.market_features import market_engine
        from model_training.price_prediction_trainer import price_trainer
        # from model_evaluation.performance_metrics import performance_metrics  # 暂时不使用
        from utils.cloud_transfer import CloudTransferManager

        # 1. 数据收集
        logger.info("📊 步骤1: 数据收集")

        # 测试数据库连接
        if not forex_collector.test_connection():
            logger.error("❌ 数据库连接失败")
            return False

        # 收集外汇数据
        symbols = ['EURUSD', 'GBPUSD']  # 先用两个主要货币对
        forex_data = forex_collector.collect_multiple_symbols(symbols, days=30)

        if not forex_data:
            logger.error("❌ 没有收集到外汇数据")
            return False

        logger.info(f"✅ 外汇数据收集完成: {len(forex_data)}个货币对")

        # 收集交易结果数据
        trading_data = trade_collector.collect_trading_logs(days=30)
        logger.info(f"✅ 交易数据收集完成: {len(trading_data)}条记录")

        # 2. 特征工程
        logger.info("🔧 步骤2: 特征工程")

        processed_data = {}
        all_feature_names = []

        for symbol, df in forex_data.items():
            try:
                logger.info(f"🔧 处理 {symbol} 特征工程...")

                # 技术指标特征
                df_with_tech = technical_engine.generate_all_features(df.copy())
                tech_features = technical_engine.get_feature_names()

                # 市场特征
                df_with_market = market_engine.generate_all_features(df_with_tech)
                market_features = market_engine.get_feature_names()

                # 合并特征名称
                feature_names = tech_features + market_features
                all_feature_names.extend(feature_names)

                processed_data[symbol] = {
                    'data': df_with_market,
                    'features': feature_names
                }

                logger.info(f"✅ {symbol} 特征工程完成: {len(feature_names)}个特征")

            except Exception as e:
                logger.error(f"❌ {symbol} 特征工程失败: {e}")
                continue

        if not processed_data:
            logger.error("❌ 特征工程失败，没有处理成功的数据")
            return False

        # 3. 模型训练
        logger.info("🧠 步骤3: 模型训练")

        training_results = {}

        for symbol, data_info in processed_data.items():
            try:
                logger.info(f"🧠 训练 {symbol} 模型...")

                df = data_info['data']
                features = data_info['features']

                # 确保有足够的数据
                if len(df) < 100:
                    logger.warning(f"⚠️ {symbol} 数据量不足，跳过训练")
                    continue

                # 训练价格预测模型
                results = price_trainer.train_all_price_models(df, features)
                training_results[symbol] = results

                logger.info(f"✅ {symbol} 模型训练完成")

            except Exception as e:
                logger.error(f"❌ {symbol} 模型训练失败: {e}")
                continue

        if not training_results:
            logger.error("❌ 模型训练失败，没有成功训练的模型")
            return False

        # 4. 模型评估
        logger.info("📈 步骤4: 模型评估")

        evaluation_results = {}

        for symbol, results in training_results.items():
            try:
                logger.info(f"📈 评估 {symbol} 模型...")

                symbol_evaluation = {}

                for model_group, model_results in results.items():
                    for model_name, model_result in model_results.items():
                        if 'metrics' in model_result:
                            symbol_evaluation[f"{model_group}_{model_name}"] = model_result['metrics']

                evaluation_results[symbol] = symbol_evaluation
                logger.info(f"✅ {symbol} 模型评估完成")

            except Exception as e:
                logger.error(f"❌ {symbol} 模型评估失败: {e}")
                continue

        # 5. 模型部署
        logger.info("🚀 步骤5: 模型部署")

        try:
            # 创建云传输管理器
            cloud_transfer = CloudTransferManager()

            # 测试云服务器连接
            if cloud_transfer.test_connection():
                logger.info("✅ 云服务器连接正常")

                # 上传所有训练好的模型
                upload_result = cloud_transfer.upload_all_models()

                if upload_result['success']:
                    logger.info(f"✅ 模型上传成功: {upload_result['uploaded_count']}个模型")
                else:
                    logger.warning(f"⚠️ 模型上传部分失败: {upload_result}")
            else:
                logger.warning("⚠️ 云服务器连接失败，模型将保存在本地")

        except Exception as e:
            logger.error(f"❌ 模型部署失败: {e}")

        # 生成训练报告
        logger.info("📊 生成训练报告...")

        training_summary = {
            'timestamp': datetime.now().isoformat(),
            'symbols_processed': list(processed_data.keys()),
            'total_features': len(set(all_feature_names)),
            'models_trained': len(training_results),
            'evaluation_results': evaluation_results,
            'training_history': price_trainer.training_history
        }

        # 保存训练报告
        report_file = f"training_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path = Path("logs") / report_file
        report_path.parent.mkdir(exist_ok=True)

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(training_summary, f, indent=2, ensure_ascii=False)

        logger.info(f"📊 训练报告已保存: {report_path}")

        logger.info("🎉 所有模型训练完成！")
        logger.info("=" * 60)
        logger.info("📋 训练摘要:")
        logger.info(f"  - 处理货币对: {len(processed_data)}")
        logger.info(f"  - 生成特征: {len(set(all_feature_names))}")
        logger.info(f"  - 训练模型: {len(training_results)}")
        logger.info(f"  - 报告文件: {report_path}")

        return True

    except Exception as e:
        logger.error(f"❌ 训练失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
