# Windows Server 2012 部署指南

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **硬盘**: 10GB可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Windows Server 2012 或更高版本
- **Python**: 3.9+ (推荐 3.9.x)
- **数据库**: MySQL 5.7+ 或 MariaDB 10.3+
- **网络端口**: 5000 (HTTP), 3306 (MySQL), 5555 (MT4)

## 🚀 部署步骤

### 第一步：环境准备

1. **安装Python 3.9+**
   ```cmd
   # 下载并安装Python 3.9.x
   # 确保勾选"Add Python to PATH"
   python --version
   ```

2. **安装MySQL数据库**
   ```cmd
   # 安装MySQL Server
   # 创建数据库: pizza_quotes
   # 配置用户权限
   ```

3. **配置防火墙**
   ```cmd
   # 开放端口 5000 (HTTP服务)
   netsh advfirewall firewall add rule name="ForexTradingSystem" dir=in action=allow protocol=TCP localport=5000
   ```

### 第二步：项目部署

1. **上传项目文件**
   ```cmd
   # 将项目文件上传到服务器
   # 推荐路径: C:\ForexTradingSystem\
   ```

2. **配置环境变量**
   ```cmd
   # 复制配置文件
   copy .env.example .env.local
   
   # 编辑 .env.local 文件，配置以下参数：
   # DB_HOST=localhost
   # DB_PASSWORD=your-mysql-password
   # LLM_API_KEY=your-llm-api-key
   ```

3. **安装依赖**
   ```cmd
   # 运行启动脚本（会自动安装依赖）
   start_server.bat
   ```

### 第三步：服务安装

1. **安装为Windows服务**
   ```cmd
   # 以管理员身份运行
   install_service.bat
   ```

2. **验证服务状态**
   ```cmd
   # 检查服务状态
   sc query ForexTradingSystem
   
   # 或通过服务管理器
   services.msc
   ```

### 第四步：功能验证

1. **访问Web界面**
   ```
   http://localhost:5000
   ```

2. **检查日志**
   ```cmd
   # 查看系统日志
   type logs\forex_system.log
   
   # 查看错误日志
   type logs\error_log.json
   ```

## 🔧 服务管理

### 启动服务
```cmd
sc start ForexTradingSystem
```

### 停止服务
```cmd
sc stop ForexTradingSystem
```

### 重启服务
```cmd
sc stop ForexTradingSystem
timeout /t 5 /nobreak
sc start ForexTradingSystem
```

### 卸载服务
```cmd
sc stop ForexTradingSystem
sc delete ForexTradingSystem
```

## 📊 监控和维护

### 日志文件位置
- **系统日志**: `logs\forex_system.log`
- **错误日志**: `logs\error_log.json`
- **操作日志**: `logs\operation_log.json`

### 性能监控
```cmd
# 查看进程状态
tasklist | findstr python

# 查看端口占用
netstat -an | findstr :5000

# 查看内存使用
wmic process where name="python.exe" get ProcessId,PageFileUsage
```

### 定期维护
1. **日志清理** (建议每月)
   ```cmd
   # 清理30天前的日志
   forfiles /p logs /s /m *.log /d -30 /c "cmd /c del @path"
   ```

2. **数据库维护** (建议每周)
   ```sql
   # 优化数据库表
   OPTIMIZE TABLE min_quote_eurusd;
   ```

## 🔄 线上更新

### 方式一：手动更新
1. 停止服务
2. 备份当前版本
3. 上传新版本文件
4. 重启服务

### 方式二：脚本更新
```cmd
# 运行更新脚本
update_system.bat
```

## ⚠️ 故障排除

### 常见问题

1. **服务启动失败**
   - 检查Python环境
   - 检查配置文件
   - 查看错误日志

2. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证连接参数
   - 检查防火墙设置

3. **端口占用**
   ```cmd
   netstat -ano | findstr :5000
   taskkill /PID <进程ID> /F
   ```

4. **权限问题**
   - 确保服务以适当权限运行
   - 检查文件夹写权限

### 联系支持
- 查看日志文件获取详细错误信息
- 记录错误发生的具体时间和操作

## 📈 性能优化

### 系统优化
1. **增加虚拟内存**
2. **关闭不必要的服务**
3. **定期重启系统**

### 应用优化
1. **调整工作进程数**
2. **配置缓存策略**
3. **优化数据库查询**

## 🔐 安全建议

1. **定期更新密码**
2. **启用防火墙**
3. **限制远程访问**
4. **定期备份数据**
5. **监控异常访问**
