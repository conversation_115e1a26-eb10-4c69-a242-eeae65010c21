#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试JSON修复功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.mt4_client import MT4Client
import json

def test_json_parsing():
    """测试JSON解析修复功能"""
    print("🧪 开始测试JSON解析修复功能")
    
    # 模拟有问题的JSON响应
    broken_json_1 = '{"status":"success","orders":[{"orderId":"123","symbol":"EURUSD",}]}'
    broken_json_2 = '{"status":"success","orders":[{"orderId":"123","symbol":"EURUSD"'
    broken_json_3 = '{"status":"success","orders":[{"orderId":"123","symbol":"EURUSD"},'
    
    test_cases = [
        ("末尾多余逗号", broken_json_1),
        ("截断的JSON", broken_json_2),
        ("不完整的数组", broken_json_3)
    ]
    
    for test_name, broken_json in test_cases:
        print(f"\n📝 测试案例: {test_name}")
        print(f"原始JSON: {broken_json}")
        
        try:
            # 尝试直接解析
            result = json.loads(broken_json)
            print("✅ 直接解析成功")
        except json.JSONDecodeError as e:
            print(f"❌ 直接解析失败: {e}")
            
            # 测试修复逻辑
            try:
                import re
                fixed_response = broken_json
                
                # 修复末尾多余的逗号
                fixed_response = re.sub(r',\s*}', '}', fixed_response)
                fixed_response = re.sub(r',\s*]', ']', fixed_response)
                
                # 修复截断的JSON
                if not fixed_response.endswith('}') and not fixed_response.endswith(']'):
                    if '"orders":[' in fixed_response and not fixed_response.endswith(']}'):
                        fixed_response += ']}'
                    elif not fixed_response.endswith('}'):
                        fixed_response += '}'
                
                print(f"修复后JSON: {fixed_response}")
                
                # 尝试解析修复后的JSON
                result = json.loads(fixed_response)
                print("✅ 修复后解析成功")
                print(f"解析结果: {result}")
                
            except Exception as fix_error:
                print(f"❌ 修复失败: {fix_error}")

def test_mt4_connection():
    """测试MT4连接和订单获取"""
    print("\n🔌 开始测试MT4连接")
    
    try:
        # 创建MT4客户端实例
        mt4_client = MT4Client()
        
        # 测试连接
        print("正在连接MT4服务器...")
        if mt4_client.connect():
            print("✅ MT4连接成功")
            
            # 测试获取活跃订单
            print("正在获取活跃订单...")
            active_orders = mt4_client.get_active_orders()
            print(f"活跃订单结果: {active_orders}")
            
            # 测试获取挂单
            print("正在获取挂单...")
            pending_orders = mt4_client.get_pending_orders()
            print(f"挂单结果: {pending_orders}")
            
        else:
            print("❌ MT4连接失败")
            
    except Exception as e:
        print(f"❌ MT4测试失败: {e}")

def test_system_components():
    """测试系统核心组件"""
    print("\n🔧 开始测试系统核心组件")
    
    try:
        # 测试智能货币对选择
        from app.utils.intelligent_pair_selector import select_optimal_currency_pairs
        print("正在测试智能货币对选择...")
        pairs = select_optimal_currency_pairs()
        print(f"✅ 智能选择结果: {pairs}")
        
        # 测试数据获取
        from app.utils.multi_pair_data_manager import get_multi_pair_analysis_data
        print("正在测试多货币对数据获取...")
        data = get_multi_pair_analysis_data(pairs)
        print(f"✅ 数据获取成功，包含 {len(data)} 个货币对")
        
        # 测试技术指标计算
        if data:
            first_pair = list(data.keys())[0]
            pair_data = data[first_pair]
            print(f"✅ {first_pair} 数据包含:")
            print(f"  - 15分钟K线: {len(pair_data.get('klines_15m', []))} 条")
            print(f"  - 1小时K线: {len(pair_data.get('klines_1h', []))} 条")
            print(f"  - 技术指标: {list(pair_data.get('indicators', {}).keys())}")
        
    except Exception as e:
        print(f"❌ 系统组件测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始外汇交易系统快速测试")
    print("=" * 50)
    
    # 测试JSON解析修复
    test_json_parsing()
    
    # 测试MT4连接
    test_mt4_connection()
    
    # 测试系统组件
    test_system_components()
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")
