# MT4连接指南

本文档提供了如何将MT4客户端与外汇交易系统连接的详细步骤。

## 前提条件

- 已安装MT4客户端
- 已安装ZeroMQ库
- 已安装Python 3.8+

## 安装ZeroMQ库

### Windows

1. 下载ZeroMQ库：https://zeromq.org/download/
2. 安装Visual Studio C++构建工具
3. 在项目目录中运行：`pip install pyzmq`

### Linux

```bash
sudo apt-get install libzmq3-dev
pip install pyzmq
```

### macOS

```bash
brew install zeromq
pip install pyzmq
```

## 安装MT4 ZeroMQ插件

1. 下载MT4 ZeroMQ插件：https://github.com/dingmaotu/mql-zmq
2. 将插件文件复制到MT4的`MQL4/Libraries`目录
3. 将示例EA复制到MT4的`MQL4/Experts`目录

## 配置MT4 EA

1. 打开MT4客户端
2. 打开MetaEditor（按F4）
3. 创建一个新的EA或使用示例EA
4. 确保EA包含以下代码：

```cpp
#include <Zmq/Zmq.mqh>

// ZeroMQ Context
Context context;

// Socket to talk to clients
Socket socket(context, ZMQ_REP);

// 初始化
int OnInit()
{
    // 绑定到端口5555
    socket.bind("tcp://*:5555");
    Print("Server started, waiting for requests...");
    return(INIT_SUCCEEDED);
}

// 清理
void OnDeinit(const int reason)
{
    socket.unbind("tcp://*:5555");
    Print("Server stopped");
}

// 主循环
void OnTick()
{
    // 检查是否有新消息
    if(socket.poll(0))
    {
        // 接收消息
        ZmqMsg request;
        socket.recv(request);
        string requestString = request.getData();
        Print("Received request: ", requestString);
        
        // 处理请求
        string responseString = ProcessRequest(requestString);
        
        // 发送响应
        ZmqMsg response(responseString);
        socket.send(response);
    }
}

// 处理请求
string ProcessRequest(string requestString)
{
    // 解析JSON请求
    // 执行相应的操作
    // 返回JSON响应
    return "{\"status\":\"success\",\"message\":\"Request processed\"}";
}
```

5. 编译EA
6. 将EA添加到图表

## 配置外汇交易系统

1. 打开`.env`文件
2. 设置MT4服务器地址：

```
MT4_SERVER_ADDRESS=tcp://127.0.0.1:5555
```

3. 保存文件

## 测试连接

1. 确保MT4客户端正在运行，并且EA已添加到图表
2. 启动外汇交易系统：`python run.py`
3. 测试连接：

```
curl http://localhost:5000/api/forex-trading/positions
```

如果连接成功，应该会返回当前持仓信息。

## 故障排除

### 连接失败

- 确保MT4客户端正在运行
- 确保EA已添加到图表并且正在运行
- 检查防火墙设置，确保端口5555未被阻止
- 检查ZeroMQ库是否正确安装

### 无法安装ZeroMQ

- 确保已安装Visual Studio C++构建工具（Windows）
- 确保已安装libzmq3-dev（Linux）
- 尝试使用管理员权限运行安装命令

### 交易执行失败

- 检查MT4账户是否有足够的资金
- 检查MT4账户是否允许自动交易
- 检查EA是否有足够的权限执行交易
- 检查交易参数是否有效

## 注意事项

- 确保MT4客户端始终运行，否则交易将无法执行
- 定期检查MT4日志，确保EA正常运行
- 考虑使用VPS运行MT4客户端，确保24/7可用性
- 在实盘交易前，先在模拟账户上测试系统
