"""
QuantumForex MLTrainer 网络配置
配置训练端与交易端的网络通信
"""

import os
from pathlib import Path

class NetworkConfig:
    """网络通信配置"""

    # ==================== 网络地址配置 ====================
    NETWORK_CONFIG = {
        # 训练端配置（本地）
        'training_server_ip': '************00',  # 本地IP
        'training_server_port': 8080,
        'training_public_ip': None,  # 本地无公网IP

        # 交易端配置（本地测试模式）
        'trading_server_ip': '127.0.0.1',  # 本地IP（测试模式）
        # 'trading_server_ip': '**************',  # 腾讯云公网IP（生产模式）
        'trading_server_port': 8081,
        'trading_internal_ip': None,  # 云服务器内网IP（如需要）

        # 网络基础信息
        'local_subnet': '************/24',
        'local_gateway': '************',
        'connection_type': 'internet',  # 互联网连接

        # 通信协议（本地测试模式）
        'api_protocol': 'http',   # 本地测试使用HTTP
        # 'api_protocol': 'https',  # 生产模式使用HTTPS
        'file_transfer_protocol': 'http_upload',  # HTTP文件上传
        'backup_protocol': 'ftp',  # 备用FTP传输
        'timeout': 60,  # 增加超时时间（互联网连接）
        'retry_attempts': 5,  # 增加重试次数
        'connection_pool_size': 3,  # 连接池大小
    }

    # ==================== 文件传输配置 ====================
    FILE_TRANSFER_CONFIG = {
        # 本地文件夹（本地测试模式）
        'local_folder': r'C:\QuantumForex_MLTrainer_Data',
        'models_folder': 'data/models',  # 使用项目内的模型文件夹
        'upload_folder': r'C:\QuantumForex_MLTrainer_Data\upload',
        'download_folder': r'C:\QuantumForex_MLTrainer_Data\download',

        # 云服务器路径（本地测试模式）
        'cloud_api_base': 'http://127.0.0.1:8081/api',  # 本地测试
        # 'cloud_api_base': 'https://**************:8081/api',  # 生产模式
        'cloud_upload_endpoint': '/models/upload',
        'cloud_download_endpoint': '/models/download',
        'cloud_status_endpoint': '/models/status',

        # 文件夹结构
        'folders': {
            'models': 'models',           # 训练好的模型
            'data': 'data',              # 数据交换
            'logs': 'logs',              # 日志文件
            'config': 'config',          # 配置文件
            'backup': 'backup',          # 备份文件
            'temp': 'temp'               # 临时文件
        },

        # 传输配置
        'transfer_settings': {
            'chunk_size': 1024 * 1024,   # 1MB分块上传
            'max_file_size': 100 * 1024 * 1024,  # 100MB最大文件
            'compression': True,          # 启用压缩
            'encryption': False,          # 暂不加密（内网传输）
            'verify_checksum': True       # 校验文件完整性
        }
    }

    # ==================== 模型同步配置 ====================
    MODEL_SYNC_CONFIG = {
        # 同步策略
        'sync_method': 'file_copy',      # 文件复制方式
        'sync_interval': 3600,           # 同步间隔（秒）
        'auto_sync': True,               # 自动同步

        # 模型版本管理
        'versioning': {
            'enabled': True,
            'max_versions': 10,          # 保留最多版本数
            'version_format': 'v{major}.{minor}.{patch}',
            'auto_increment': True
        },

        # 兼容性配置
        'compatibility': {
            'check_version': True,       # 检查版本兼容性
            'fallback_enabled': True,    # 启用回退机制
            'legacy_support': True,      # 支持旧版本模型
            'migration_enabled': True    # 启用模型迁移
        },

        # 模型文件配置
        'model_files': {
            'format': 'joblib',          # 模型序列化格式
            'compression': 3,            # 压缩级别
            'backup_original': True,     # 备份原始模型
            'checksum_validation': True  # 校验和验证
        }
    }

    # ==================== API接口配置 ====================
    API_CONFIG = {
        # REST API配置
        'rest_api': {
            'enabled': True,
            'host': '0.0.0.0',          # 监听所有接口
            'port': 8080,
            'debug': False,
            'threaded': True
        },

        # API端点
        'endpoints': {
            'health_check': '/api/health',
            'model_status': '/api/models/status',
            'model_info': '/api/models/info',
            'training_status': '/api/training/status',
            'training_trigger': '/api/training/trigger',
            'model_download': '/api/models/download',
            'performance_report': '/api/performance/report'
        },

        # 安全配置
        'security': {
            'api_key_required': False,   # 内网暂不需要
            'rate_limiting': False,      # 内网暂不需要
            'cors_enabled': True,        # 允许跨域
            'allowed_origins': ['*']     # 允许所有来源
        }
    }

    # ==================== 数据库连接配置 ====================
    DATABASE_CONFIG = {
        # 使用与交易端相同的数据库配置
        'pizza_quotes': {
            'host': 'pizza-wnet-db1.mysql.rds.aliyuncs.com',
            'port': 6688,
            'user': 'ea_quote_srv',
            'password': 'pizza666!',
            'database': 'pizza_quotes',
            'charset': 'utf8mb4',
            'pool_size': 5,
            'max_overflow': 10,
            'pool_timeout': 30,
            'pool_recycle': 3600
        }
    }

    # ==================== 日志配置 ====================
    LOGGING_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'handlers': {
            'file': {
                'enabled': True,
                'filename': 'logs/ml_trainer.log',
                'max_bytes': 10485760,  # 10MB
                'backup_count': 5,
                'rotation': True
            },
            'console': {
                'enabled': True,
                'level': 'INFO'
            },
            'shared': {
                'enabled': True,
                'filename': 'C:/QuantumForex_Shared/logs/ml_trainer_shared.log',
                'max_bytes': 10485760,
                'backup_count': 3
            }
        }
    }

    @classmethod
    def get_shared_folder_path(cls, subfolder: str = '') -> Path:
        """获取共享文件夹路径"""
        base_path = Path(cls.FILE_SHARE_CONFIG['shared_folder_local'])
        if subfolder:
            return base_path / subfolder
        return base_path

    @classmethod
    def get_model_sync_path(cls) -> Path:
        """获取模型同步路径"""
        return cls.get_shared_folder_path('models')

    @classmethod
    def get_trading_server_url(cls, endpoint: str = '') -> str:
        """获取交易端服务器URL"""
        base_url = f"{cls.NETWORK_CONFIG['api_protocol']}://{cls.NETWORK_CONFIG['trading_server_ip']}:{cls.NETWORK_CONFIG['trading_server_port']}"
        if endpoint:
            return f"{base_url}{endpoint}"
        return base_url

    @classmethod
    def get_training_server_url(cls, endpoint: str = '') -> str:
        """获取训练端服务器URL"""
        base_url = f"{cls.NETWORK_CONFIG['api_protocol']}://{cls.NETWORK_CONFIG['training_server_ip']}:{cls.NETWORK_CONFIG['training_server_port']}"
        if endpoint:
            return f"{base_url}{endpoint}"
        return base_url

# 创建全局配置实例
network_config = NetworkConfig()
