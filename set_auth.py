"""
授权码设置工具
用于设置MT4客户端的授权码
授权验证由MT4 Server-V2处理
"""
import os
import sys
import argparse
from datetime import datetime
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入MT4客户端
from app.utils.mt4_client import MT4Client

# 加载环境变量
load_dotenv()

def save_auth_code(auth_code, server_address=None):
    """
    保存授权码到配置文件

    Args:
        auth_code (str): 授权码
        server_address (str, optional): 服务器地址
    """
    config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env.local')

    # 读取现有配置
    config = {}
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            for line in f:
                if '=' in line:
                    key, value = line.strip().split('=', 1)
                    config[key] = value

    # 更新配置
    config['AUTH_CODE'] = auth_code
    if server_address:
        config['MT4_SERVER_ADDRESS'] = server_address

    # 保存配置
    with open(config_file, 'w', encoding='utf-8') as f:
        for key, value in config.items():
            f.write(f'{key}={value}\n')

    print(f'授权码已保存到配置文件: {config_file}')

def test_auth_code(auth_code, server_address=None):
    """
    测试授权码是否有效

    Args:
        auth_code (str): 授权码
        server_address (str, optional): 服务器地址

    Returns:
        bool: 授权码是否有效
    """
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试授权码: {auth_code}')
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 授权验证将由MT4 Server-V2处理')

    # 创建MT4客户端
    mt4_client = MT4Client(server_address=server_address, auth_code=auth_code)

    # 连接到MT4服务器
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 连接到MT4服务器: {server_address or "默认地址"}')
    connected = mt4_client.connect()
    if not connected:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 连接MT4服务器失败')
        return False

    # 检查授权状态
    if mt4_client.is_authorized:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器验证成功，用户: {mt4_client.user_info.get("username") if mt4_client.user_info else "未知"}')
        return True
    else:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器验证失败')
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MT4客户端授权码设置工具')
    parser.add_argument('--auth-code', '-a', help='授权码')
    parser.add_argument('--server', '-s', help='服务器地址，例如: tcp://*************:5555')
    parser.add_argument('--test', '-t', action='store_true', help='测试授权码是否有效')
    parser.add_argument('--save', action='store_true', help='保存授权码到配置文件')

    args = parser.parse_args()

    # 如果没有提供授权码，提示用户输入
    if not args.auth_code:
        args.auth_code = input('请输入授权码: ')

    # 如果没有提供服务器地址，使用默认值
    if not args.server:
        args.server = os.getenv('MT4_SERVER_ADDRESS', 'tcp://127.0.0.1:5555')

    # 测试授权码
    if args.test:
        is_valid = test_auth_code(args.auth_code, args.server)
        if is_valid and args.save:
            save_auth_code(args.auth_code, args.server)
    # 保存授权码
    elif args.save:
        save_auth_code(args.auth_code, args.server)
    # 默认测试并保存
    else:
        is_valid = test_auth_code(args.auth_code, args.server)
        if is_valid:
            save_auth_code(args.auth_code, args.server)

if __name__ == '__main__':
    main()
