#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试全局持仓协调器
验证重复订单防护功能
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'QuantumForex_Pro'))

def test_global_coordinator():
    """测试全局协调器功能"""
    try:
        print("🚀 开始测试全局持仓协调器...")
        print("="*60)
        
        # 导入必要的模块
        from QuantumForex_Pro.core.execution_engine.trade_executor import TradeExecutor
        
        # 创建交易执行器（会自动初始化全局协调器）
        executor = TradeExecutor()
        
        print("✅ 交易执行器初始化完成")
        
        # 检查全局协调器状态
        if executor.global_coordinator:
            print("✅ 全局协调器初始化成功")
            status = executor.global_coordinator.get_status()
            print(f"📊 协调器状态: {status}")
        else:
            print("❌ 全局协调器初始化失败")
            return
        
        print("\n" + "="*60)
        print("🧪 测试1: 正常交易请求")
        print("="*60)
        
        # 测试正常交易请求
        result1 = executor.execute_trade('EURUSD', 'BUY', 0.01, 'test_strategy_1')
        print(f"📋 测试1结果: {result1}")
        
        print("\n" + "="*60)
        print("🧪 测试2: 重复交易请求（应该被拒绝）")
        print("="*60)
        
        # 立即发送相同的交易请求（应该被拒绝）
        result2 = executor.execute_trade('EURUSD', 'BUY', 0.01, 'test_strategy_2')
        print(f"📋 测试2结果: {result2}")
        
        print("\n" + "="*60)
        print("🧪 测试3: 不同货币对交易请求")
        print("="*60)
        
        # 测试不同货币对（应该允许）
        result3 = executor.execute_trade('GBPUSD', 'BUY', 0.01, 'test_strategy_3')
        print(f"📋 测试3结果: {result3}")
        
        print("\n" + "="*60)
        print("🧪 测试4: 相反方向交易请求")
        print("="*60)
        
        # 测试相反方向（应该允许）
        result4 = executor.execute_trade('EURUSD', 'SELL', 0.01, 'test_strategy_4')
        print(f"📋 测试4结果: {result4}")
        
        print("\n" + "="*60)
        print("🧪 测试5: 多线程并发请求")
        print("="*60)
        
        # 测试多线程并发请求
        def concurrent_trade(strategy_name, delay=0):
            if delay > 0:
                time.sleep(delay)
            result = executor.execute_trade('AUDUSD', 'BUY', 0.01, strategy_name)
            print(f"📋 {strategy_name}结果: {result}")
        
        # 启动多个并发线程
        threads = []
        for i in range(3):
            thread = threading.Thread(
                target=concurrent_trade, 
                args=(f'concurrent_strategy_{i+1}', i*0.1)
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print("\n" + "="*60)
        print("📊 最终协调器状态")
        print("="*60)
        
        # 显示最终状态
        final_status = executor.global_coordinator.get_status()
        print(f"📊 最终状态: {final_status}")
        
        # 处理待执行队列
        print("\n🔄 处理待执行队列...")
        executor.global_coordinator.process_pending_orders()
        
        # 显示处理后状态
        after_process_status = executor.global_coordinator.get_status()
        print(f"📊 处理后状态: {after_process_status}")
        
        print("\n" + "="*60)
        print("✅ 全局协调器测试完成")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_coordinator_with_monitor():
    """测试协调器与监听器的配合"""
    try:
        print("\n🔗 测试协调器与监听器配合...")
        print("="*60)
        
        # 启动监听器
        from start_simple_monitor import SimpleTradingMonitor
        monitor = SimpleTradingMonitor()
        monitor.start_monitoring()
        
        print("✅ 监听器启动成功")
        
        # 等待一段时间让监听器检测现有订单
        time.sleep(5)
        
        # 创建交易执行器
        from QuantumForex_Pro.core.execution_engine.trade_executor import TradeExecutor
        executor = TradeExecutor()
        
        print("✅ 交易执行器创建成功")
        
        # 发送一些测试交易
        print("\n📤 发送测试交易...")
        
        # 测试1: 正常交易
        result1 = executor.execute_trade('EURUSD', 'BUY', 0.01, 'monitor_test_1')
        print(f"📋 监听器测试1: {result1}")
        
        # 等待监听器检测
        time.sleep(3)
        
        # 测试2: 重复交易（应该被协调器拒绝）
        result2 = executor.execute_trade('EURUSD', 'BUY', 0.01, 'monitor_test_2')
        print(f"📋 监听器测试2: {result2}")
        
        # 等待监听器检测
        time.sleep(3)
        
        print("✅ 协调器与监听器配合测试完成")
        
        # 停止监听器
        monitor.stop_monitoring()
        
    except Exception as e:
        print(f"❌ 协调器与监听器测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🎯 全局持仓协调器测试程序")
    print("🎯 目标: 解决重复订单和策略冲突问题")
    print("="*80)
    
    try:
        # 测试1: 基本协调器功能
        test_global_coordinator()
        
        # 等待一段时间
        time.sleep(2)
        
        # 测试2: 协调器与监听器配合
        test_coordinator_with_monitor()
        
        print("\n🎉 所有测试完成！")
        print("📊 如果看到重复订单被正确拒绝，说明协调器工作正常")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
