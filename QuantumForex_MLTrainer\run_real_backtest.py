#!/usr/bin/env python3
"""
运行真实回测验证
验证模型的实际交易效果
"""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model_evaluation.real_backtest_engine import RealBacktestEngine

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def find_best_models() -> list:
    """查找最佳模型"""
    models_dir = Path("data/models")
    if not models_dir.exists():
        return []
    
    # 查找所有模型文件
    model_files = []
    
    # 价格预测模型
    price_models = list(models_dir.glob("*price_prediction*.pkl"))
    price_models = [f for f in price_models if 'scaler' not in f.name]
    if price_models:
        # 选择最新的
        latest_price = max(price_models, key=lambda x: x.stat().st_mtime)
        model_files.append(latest_price)
    
    # 趋势分类模型
    trend_models = list(models_dir.glob("*trend_classification*.pkl"))
    trend_models = [f for f in trend_models if 'scaler' not in f.name]
    if trend_models:
        latest_trend = max(trend_models, key=lambda x: x.stat().st_mtime)
        model_files.append(latest_trend)
    
    # 风险评估模型
    risk_models = list(models_dir.glob("*risk_assessment*.pkl"))
    risk_models = [f for f in risk_models if 'scaler' not in f.name]
    if risk_models:
        latest_risk = max(risk_models, key=lambda x: x.stat().st_mtime)
        model_files.append(latest_risk)
    
    return model_files

def evaluate_model_effectiveness(metrics) -> str:
    """评估模型效果"""
    score = 0
    max_score = 100
    
    # 盈利能力 (30分)
    if metrics.total_return > 0:
        profit_score = min(30, metrics.total_return * 100 * 3)  # 每1%收益得3分
        score += profit_score
    
    # 胜率 (20分)
    win_rate_score = metrics.win_rate * 20
    score += win_rate_score
    
    # 预测准确性 (20分)
    accuracy_score = metrics.prediction_accuracy * 20
    score += accuracy_score
    
    # 风险控制 (15分)
    if metrics.max_drawdown < 0.1:  # 回撤小于10%
        risk_score = 15 - (metrics.max_drawdown * 100)
        score += max(0, risk_score)
    
    # 盈亏比 (10分)
    if metrics.profit_factor > 1:
        pf_score = min(10, (metrics.profit_factor - 1) * 5)
        score += pf_score
    
    # 交易频率 (5分)
    if metrics.total_trades > 5:
        freq_score = min(5, metrics.total_trades / 2)
        score += freq_score
    
    percentage = score / max_score
    
    if percentage >= 0.8:
        return f"🌟 优秀 ({percentage:.1%})"
    elif percentage >= 0.6:
        return f"✅ 良好 ({percentage:.1%})"
    elif percentage >= 0.4:
        return f"⚠️ 一般 ({percentage:.1%})"
    else:
        return f"❌ 较差 ({percentage:.1%})"

def run_comprehensive_backtest():
    """运行综合回测"""
    logger = setup_logging()
    
    print("🚀 QuantumForex 真实回测验证系统")
    print("="*60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 查找模型
        print("\n📦 查找训练好的模型...")
        model_files = find_best_models()
        
        if not model_files:
            print("❌ 没有找到可用的模型文件")
            return False
        
        print(f"✅ 找到{len(model_files)}个模型文件:")
        for model_file in model_files:
            file_size = model_file.stat().st_size
            mod_time = datetime.fromtimestamp(model_file.stat().st_mtime)
            print(f"   📄 {model_file.name}")
            print(f"      大小: {file_size:,}字节")
            print(f"      修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 2. 创建回测引擎
        print("\n🔧 初始化回测引擎...")
        backtest_engine = RealBacktestEngine()
        
        # 3. 对每个模型运行回测
        all_results = []
        
        for i, model_file in enumerate(model_files, 1):
            print(f"\n{'='*60}")
            print(f"🧪 回测模型 {i}/{len(model_files)}: {model_file.name}")
            print(f"{'='*60}")
            
            try:
                # 运行回测
                metrics = backtest_engine.run_comprehensive_backtest(
                    model_path=str(model_file),
                    data_days=14  # 使用14天数据
                )
                
                # 显示结果
                print(metrics)
                
                # 评估效果
                effectiveness = evaluate_model_effectiveness(metrics)
                print(f"🎯 模型效果评级: {effectiveness}")
                
                # 保存结果
                all_results.append({
                    'model_name': model_file.name,
                    'metrics': metrics,
                    'effectiveness': effectiveness
                })
                
                print(f"✅ 模型 {model_file.name} 回测完成")
                
            except Exception as e:
                print(f"❌ 模型 {model_file.name} 回测失败: {e}")
                logger.error(f"模型回测失败: {e}")
                continue
        
        # 4. 总结报告
        print(f"\n{'='*60}")
        print("📊 回测总结报告")
        print(f"{'='*60}")
        
        if not all_results:
            print("❌ 没有成功完成的回测")
            return False
        
        print(f"📈 回测模型数量: {len(all_results)}")
        print(f"📅 回测完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 找出最佳模型
        best_model = None
        best_score = -1
        
        for result in all_results:
            metrics = result['metrics']
            # 综合评分
            score = (metrics.total_return * 0.3 + 
                    metrics.win_rate * 0.2 + 
                    metrics.prediction_accuracy * 0.2 + 
                    (1 - metrics.max_drawdown) * 0.15 + 
                    min(metrics.profit_factor / 2, 1) * 0.15)
            
            if score > best_score:
                best_score = score
                best_model = result
        
        print(f"\n🏆 最佳模型: {best_model['model_name']}")
        print(f"🎯 综合评分: {best_score:.3f}")
        print(f"📊 效果评级: {best_model['effectiveness']}")
        
        # 给出建议
        print(f"\n💡 建议:")
        if best_score > 0.6:
            print("✅ 模型效果良好，建议投入实盘测试")
            print("📝 建议先用小资金验证，逐步增加仓位")
        elif best_score > 0.4:
            print("⚠️ 模型效果一般，需要进一步优化")
            print("📝 建议调整参数或收集更多训练数据")
        else:
            print("❌ 模型效果较差，建议重新训练")
            print("📝 建议检查特征工程和模型选择")
        
        # 保存报告
        save_backtest_report(all_results)
        
        return True
        
    except Exception as e:
        logger.error(f"回测系统失败: {e}")
        print(f"❌ 回测系统失败: {e}")
        return False

def save_backtest_report(results: list):
    """保存回测报告"""
    try:
        import json
        
        # 创建报告目录
        reports_dir = Path("logs/backtest_reports")
        reports_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成报告文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = reports_dir / f"backtest_report_{timestamp}.json"
        
        # 准备报告数据
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'total_models': len(results),
            'results': []
        }
        
        for result in results:
            metrics = result['metrics']
            report_data['results'].append({
                'model_name': result['model_name'],
                'effectiveness': result['effectiveness'],
                'metrics': {
                    'total_trades': metrics.total_trades,
                    'win_rate': metrics.win_rate,
                    'total_return': metrics.total_return,
                    'max_drawdown': metrics.max_drawdown,
                    'sharpe_ratio': metrics.sharpe_ratio,
                    'prediction_accuracy': metrics.prediction_accuracy,
                    'profit_factor': metrics.profit_factor
                }
            })
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"📄 回测报告已保存: {report_file}")
        
    except Exception as e:
        print(f"⚠️ 保存报告失败: {e}")

if __name__ == "__main__":
    success = run_comprehensive_backtest()
    
    if success:
        print("\n🎉 真实回测验证完成！")
        print("💡 现在您可以看到模型的真实交易效果了！")
        print("📊 基于真实历史数据的完整性能评估")
    else:
        print("\n❌ 真实回测验证失败！")
        print("💡 请检查模型文件和系统配置")
