"""
MT4修改订单测试脚本
用于测试修改订单的止损和止盈
"""
import os
import sys
import time
from app.utils.mt4_client import mt4_client

def test_mt4_modify_order():
    """测试修改MT4订单"""
    try:
        print('开始测试修改订单...')
        
        # 连接到MT4服务器
        print('连接到MT4服务器...')
        connected = mt4_client.connect()
        print(f'连接结果: {connected}')
        
        if not connected:
            print('连接MT4服务器失败，无法继续测试')
            return
        
        # 获取活跃订单
        print('\n获取活跃订单...')
        active_orders = mt4_client.get_active_orders()
        print(f'活跃订单: {active_orders}')
        
        # 检查是否有活跃订单
        orders = active_orders.get('orders', [])
        if not orders:
            print('没有活跃订单，无法测试修改订单')
            return
        
        # 修改第一个订单
        order = orders[0]
        order_id = order.get('order_id')
        if not order_id:
            print('订单ID为空，无法修改订单')
            return
        
        # 获取当前止损和止盈
        current_sl = float(order.get('sl', 0))
        current_tp = float(order.get('tp', 0))
        print(f'当前止损: {current_sl}, 当前止盈: {current_tp}')
        
        # 设置新的止损和止盈
        new_sl = round(current_sl + 0.0010, 5) if current_sl > 0 else round(float(order.get('open_price', 0)) - 0.0060, 5)
        new_tp = round(current_tp - 0.0010, 5) if current_tp > 0 else round(float(order.get('open_price', 0)) + 0.0060, 5)
        print(f'新止损: {new_sl}, 新止盈: {new_tp}')
        
        # 修改订单
        print(f'\n修改订单 {order_id}...')
        modify_response = mt4_client.modify_order(order_id, new_sl, new_tp)
        print(f'修改订单响应: {modify_response}')
        
        if modify_response and modify_response.get('status') == 'success':
            print('✅ 修改订单成功!')
        else:
            print(f'❌ 修改订单失败: {modify_response.get("message") if modify_response else "未知错误"}')
        
        # 再次获取活跃订单，确认订单已修改
        print('\n再次获取活跃订单...')
        active_orders = mt4_client.get_active_orders()
        print(f'活跃订单: {active_orders}')
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_mt4_modify_order()
