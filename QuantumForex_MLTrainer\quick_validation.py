#!/usr/bin/env python3
"""
快速验证现有模型
"""

import os
import joblib
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime

def quick_validate():
    print("🚀 快速模型验证")
    print("="*40)

    # 查找模型
    current_dir = Path(__file__).parent
    models_dir = current_dir / "data" / "models"

    print(f"📁 查找目录: {models_dir}")

    if not models_dir.exists():
        print("❌ 模型目录不存在")
        # 尝试其他可能的路径
        alt_paths = [
            current_dir / "models",
            Path("QuantumForex_MLTrainer/data/models"),
            Path("data/models")
        ]
        for alt_path in alt_paths:
            if alt_path.exists():
                models_dir = alt_path
                print(f"✅ 找到替代路径: {models_dir}")
                break
        else:
            return

    model_files = list(models_dir.glob("*.pkl"))
    model_files = [f for f in model_files if 'scaler' not in f.name]

    if not model_files:
        print("❌ 没有找到模型文件")
        return

    print(f"📦 找到{len(model_files)}个模型文件")

    # 测试每个模型
    for model_file in model_files[:5]:  # 只测试前5个
        print(f"\n🔍 测试: {model_file.name}")

        try:
            # 加载模型
            model = joblib.load(model_file)
            print(f"   模型类型: {type(model).__name__}")

            # 生成测试数据
            test_features = np.random.random((10, 5))  # 10个样本，5个特征

            # 测试预测
            if hasattr(model, 'predict'):
                predictions = model.predict(test_features)
                print(f"   预测成功: {len(predictions)}个预测")
                print(f"   预测范围: {np.min(predictions):.3f} ~ {np.max(predictions):.3f}")

                # 检查预测是否有变化
                if np.std(predictions) > 0.001:
                    print("   ✅ 模型有预测能力")
                else:
                    print("   ❌ 模型预测无变化")
            else:
                print("   ❌ 模型无predict方法")

        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

    print("\n✅ 快速验证完成")

if __name__ == "__main__":
    quick_validate()
