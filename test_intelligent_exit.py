#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能止盈保护系统测试
演示分层保护和变盘检测功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'QuantumForex_Pro'))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def create_test_scenarios():
    """创建测试场景"""
    scenarios = [
        {
            'name': '小幅盈利场景',
            'position_info': {
                'symbol': 'EURUSD',
                'entry_price': 1.1000,
                'current_price': 1.1030,  # 30点盈利
                'stop_loss': 1.0980,
                'take_profit': 1.1040,
                'position_size': 0.02,
                'entry_time': datetime.now() - timedelta(hours=2),
                'unrealized_pnl': 60.0,
                'unrealized_pnl_pct': 0.27  # 27%收益
            },
            'market_data': {
                'atr': 0.0008,
                'trend_strength': 0.8,
                'momentum': 0.05
            }
        },
        {
            'name': '中等盈利场景',
            'position_info': {
                'symbol': 'GBPUSD',
                'entry_price': 1.3000,
                'current_price': 1.3080,  # 80点盈利
                'stop_loss': 1.2970,
                'take_profit': 1.3060,
                'position_size': 0.02,
                'entry_time': datetime.now() - timedelta(hours=6),
                'unrealized_pnl': 160.0,
                'unrealized_pnl_pct': 0.62  # 62%收益
            },
            'market_data': {
                'atr': 0.0012,
                'trend_strength': 0.4,  # 趋势减弱
                'momentum': -0.02
            }
        },
        {
            'name': '大幅盈利场景',
            'position_info': {
                'symbol': 'AUDUSD',
                'entry_price': 0.6400,
                'current_price': 0.6520,  # 120点盈利
                'stop_loss': 0.6380,
                'take_profit': 0.6440,
                'position_size': 0.03,
                'entry_time': datetime.now() - timedelta(hours=12),
                'unrealized_pnl': 360.0,
                'unrealized_pnl_pct': 1.88  # 188%收益
            },
            'market_data': {
                'atr': 0.0009,
                'trend_strength': 0.3,  # 趋势明显减弱
                'momentum': -0.08
            }
        },
        {
            'name': '长时间持仓场景',
            'position_info': {
                'symbol': 'USDCAD',
                'entry_price': 1.3800,
                'current_price': 1.3830,  # 30点盈利
                'stop_loss': 1.3780,
                'take_profit': 1.3840,
                'position_size': 0.02,
                'entry_time': datetime.now() - timedelta(hours=30),  # 30小时
                'unrealized_pnl': 60.0,
                'unrealized_pnl_pct': 0.22  # 22%收益
            },
            'market_data': {
                'atr': 0.0007,
                'trend_strength': 0.6,
                'momentum': 0.01
            }
        },
        {
            'name': '趋势反转场景',
            'position_info': {
                'symbol': 'USDJPY',
                'entry_price': 144.00,
                'current_price': 144.60,  # 60点盈利
                'stop_loss': 143.70,
                'take_profit': 144.80,
                'position_size': 0.02,
                'entry_time': datetime.now() - timedelta(hours=8),
                'unrealized_pnl': 120.0,
                'unrealized_pnl_pct': 1.33  # 133%收益
            },
            'market_data': {
                'atr': 0.5,
                'trend_strength': -0.2,  # 趋势反转
                'momentum': -0.15
            }
        }
    ]
    
    # 为每个场景添加模拟的OHLCV数据
    for scenario in scenarios:
        # 生成模拟的价格数据
        current_price = scenario['position_info']['current_price']
        dates = pd.date_range(end=datetime.now(), periods=20, freq='5min')
        
        # 模拟价格序列
        base_prices = np.linspace(current_price * 0.995, current_price, 20)
        noise = np.random.normal(0, current_price * 0.001, 20)
        close_prices = base_prices + noise
        
        ohlcv = pd.DataFrame({
            'timestamp': dates,
            'open': close_prices,
            'high': close_prices * 1.001,
            'low': close_prices * 0.999,
            'close': close_prices,
            'volume': np.random.uniform(1000, 5000, 20)
        })
        
        scenario['market_data']['ohlcv'] = ohlcv
    
    return scenarios

def test_intelligent_exit():
    """测试智能出场系统"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🧪 开始测试智能止盈保护系统...")
    
    try:
        # 导入智能出场管理器
        from core.position_manager.intelligent_exit_manager import intelligent_exit_manager
        
        # 创建测试场景
        scenarios = create_test_scenarios()
        
        logger.info("🎯 测试不同场景下的出场决策:")
        logger.info("=" * 80)
        
        for i, scenario in enumerate(scenarios, 1):
            logger.info(f"📊 场景 {i}: {scenario['name']}")
            
            position_info = scenario['position_info']
            market_data = scenario['market_data']
            
            # 显示场景信息
            logger.info(f"   货币对: {position_info['symbol']}")
            logger.info(f"   入场价格: {position_info['entry_price']:.5f}")
            logger.info(f"   当前价格: {position_info['current_price']:.5f}")
            logger.info(f"   未实现盈亏: ${position_info['unrealized_pnl']:.2f} ({position_info['unrealized_pnl_pct']:.1%})")
            logger.info(f"   持仓时间: {(datetime.now() - position_info['entry_time']).total_seconds() / 3600:.1f}小时")
            logger.info(f"   趋势强度: {market_data['trend_strength']:.2f}")
            logger.info(f"   动量: {market_data['momentum']:.3f}")
            
            # 分析出场条件
            decision = intelligent_exit_manager.analyze_exit_conditions(position_info, market_data)
            
            # 显示决策结果
            logger.info(f"🎯 出场决策:")
            logger.info(f"   信号: {decision.signal.value}")
            logger.info(f"   出场比例: {decision.exit_ratio:.1%}")
            if decision.new_stop_loss:
                logger.info(f"   新止损: {decision.new_stop_loss:.5f}")
            logger.info(f"   理由: {decision.reason}")
            logger.info(f"   置信度: {decision.confidence:.1%}")
            logger.info("")
        
        # 总结分析
        logger.info("📈 智能止盈保护系统特点:")
        logger.info("=" * 50)
        logger.info("✅ 分层保护机制:")
        logger.info("   - 50%收益：保护30%，敏感度0.8")
        logger.info("   - 100%收益：保护50%，敏感度0.6")
        logger.info("   - 150%收益：保护70%，敏感度0.4")
        logger.info("   - 200%收益：保护80%，敏感度0.3")
        logger.info("")
        logger.info("✅ 智能检测机制:")
        logger.info("   - 趋势变化检测：防止变盘损失")
        logger.info("   - 动量衰减检测：识别上涨乏力")
        logger.info("   - 时间衰减检测：避免长期拖延")
        logger.info("   - 部分出场策略：保护收益同时保留机会")
        logger.info("")
        logger.info("✅ 平衡设计:")
        logger.info("   - 不过于敏感：避免频繁平仓")
        logger.info("   - 及时保护：变盘时保护收益")
        logger.info("   - 分层管理：不同收益水平不同策略")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_intelligent_exit()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
    input("按任意键退出...")
