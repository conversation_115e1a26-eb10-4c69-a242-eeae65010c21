#!/usr/bin/env python3
"""
Pro系统兼容的特征工程
确保Trainer和Pro使用完全相同的特征
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List

class ProCompatibleFeatureEngine:
    """Pro系统兼容的特征工程器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.feature_names = []
        
    def generate_features(self, market_data: pd.DataFrame, technical_indicators: Dict = None) -> pd.DataFrame:
        """
        生成与Pro系统完全兼容的特征
        复制Pro系统的_engineer_features方法逻辑
        """
        try:
            self.logger.info("🔧 生成Pro兼容特征...")
            
            features = pd.DataFrame(index=market_data.index)
            
            # 1. 基础价格特征 (与Pro系统完全一致)
            features['price_change'] = market_data['close'].pct_change()
            features['price_volatility'] = market_data['close'].rolling(10, min_periods=5).std()
            features['price_momentum'] = market_data['close'] / market_data['close'].shift(5) - 1
            
            # 2. 增强价格特征
            features['price_range'] = (market_data['high'] - market_data['low']) / market_data['close']
            features['price_position'] = (market_data['close'] - market_data['low']) / (market_data['high'] - market_data['low'])
            features['gap'] = (market_data['open'] - market_data['close'].shift(1)) / market_data['close'].shift(1)
            
            # 3. 多时间框架移动平均
            for period in [5, 10, 20]:
                ma = market_data['close'].rolling(period, min_periods=max(1, period//2)).mean()
                features[f'ma_{period}_ratio'] = market_data['close'] / ma - 1
                features[f'ma_{period}_slope'] = ma.diff(3) / ma.shift(3)
            
            # 4. 成交量特征
            if 'volume' in market_data.columns:
                features['volume_change'] = market_data['volume'].pct_change()
                features['volume_ma_ratio'] = market_data['volume'] / market_data['volume'].rolling(10).mean()
                features['volume_price_trend'] = features['volume_change'] * features['price_change']
            else:
                # 如果没有成交量数据，填充默认值
                features['volume_change'] = 0
                features['volume_ma_ratio'] = 1
                features['volume_price_trend'] = 0
            
            # 5. 技术指标特征 (简化版，与Pro系统一致)
            if technical_indicators:
                # 趋势分析
                trend_analysis = technical_indicators.get('trend_analysis', {})
                features['trend_score'] = trend_analysis.get('trend_score', 0)
                features['trend_strength'] = trend_analysis.get('trend_strength', 0)
                
                # 动量分析
                momentum_analysis = technical_indicators.get('momentum_analysis', {})
                features['momentum_score'] = momentum_analysis.get('momentum_score', 0)
                features['momentum_divergence'] = momentum_analysis.get('momentum_divergence', 0)
                
                # 波动率分析
                volatility_analysis = technical_indicators.get('volatility_analysis', {})
                features['volatility_score'] = volatility_analysis.get('volatility_score', 0)
                features['volatility_regime'] = volatility_analysis.get('volatility_regime', 0)
            else:
                # 默认技术指标
                features['trend_score'] = 0
                features['trend_strength'] = 0
                features['momentum_score'] = 0
                features['momentum_divergence'] = 0
                features['volatility_score'] = 0
                features['volatility_regime'] = 0
            
            # 6. 滞后特征
            for lag in [1, 2, 3, 5]:
                features[f'price_change_lag_{lag}'] = features['price_change'].shift(lag)
                features[f'volume_change_lag_{lag}'] = features.get('volume_change', 0).shift(lag)
            
            # 7. 移动平均特征
            for window in [5, 10, 20]:
                features[f'price_ma_{window}'] = market_data['close'].rolling(window).mean()
                features[f'price_std_{window}'] = market_data['close'].rolling(window).std()
            
            # 删除NaN值
            features = features.dropna()
            
            # 限制特征数量 (与Pro系统一致，最多30个特征)
            max_features = 30
            if len(features.columns) > max_features:
                # 选择最重要的特征
                important_features = [
                    'price_change', 'price_volatility', 'price_momentum',
                    'price_range', 'price_position', 'gap',
                    'ma_5_ratio', 'ma_10_ratio', 'ma_20_ratio',
                    'ma_5_slope', 'ma_10_slope', 'ma_20_slope',
                    'volume_change', 'volume_ma_ratio', 'volume_price_trend',
                    'trend_score', 'trend_strength',
                    'momentum_score', 'momentum_divergence',
                    'volatility_score', 'volatility_regime',
                    'price_change_lag_1', 'price_change_lag_2', 'price_change_lag_3',
                    'volume_change_lag_1', 'volume_change_lag_2',
                    'price_ma_5', 'price_ma_10', 'price_ma_20',
                    'price_std_5', 'price_std_10'
                ]
                
                # 只保留存在的特征
                available_features = [f for f in important_features if f in features.columns]
                features = features[available_features[:max_features]]
            
            # 保存特征名称
            self.feature_names = list(features.columns)
            
            self.logger.info(f"✅ Pro兼容特征生成完成: {len(features.columns)}个特征")
            self.logger.info(f"   特征列表: {self.feature_names}")
            
            return features
            
        except Exception as e:
            self.logger.error(f"❌ Pro兼容特征生成失败: {e}")
            return pd.DataFrame()
    
    def get_feature_names(self) -> List[str]:
        """获取特征名称列表"""
        return self.feature_names.copy()
    
    def validate_features(self, features: pd.DataFrame) -> bool:
        """验证特征是否符合Pro系统要求"""
        try:
            # 检查特征数量
            if len(features.columns) > 30:
                self.logger.warning(f"特征数量过多: {len(features.columns)} > 30")
                return False
            
            # 检查必需特征
            required_features = [
                'price_change', 'price_volatility', 'price_momentum',
                'ma_5_ratio', 'ma_10_ratio', 'ma_20_ratio'
            ]
            
            missing_features = [f for f in required_features if f not in features.columns]
            if missing_features:
                self.logger.error(f"缺少必需特征: {missing_features}")
                return False
            
            # 检查数据质量
            if features.isnull().any().any():
                self.logger.warning("特征包含NaN值")
                return False
            
            # 检查数据范围
            if features.select_dtypes(include=[np.number]).abs().max().max() > 1000:
                self.logger.warning("特征值范围过大")
                return False
            
            self.logger.info("✅ 特征验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 特征验证失败: {e}")
            return False
    
    def create_sample_technical_indicators(self) -> Dict:
        """创建示例技术指标（用于测试）"""
        return {
            'trend_analysis': {
                'trend_score': 0.6,
                'trend_strength': 0.7
            },
            'momentum_analysis': {
                'momentum_score': 0.5,
                'momentum_divergence': 0.2
            },
            'volatility_analysis': {
                'volatility_score': 0.4,
                'volatility_regime': 0.3
            }
        }

def test_pro_compatibility():
    """测试Pro系统兼容性"""
    print("🧪 测试Pro系统特征兼容性")
    print("="*40)
    
    # 创建测试数据
    dates = pd.date_range('2024-01-01', periods=100, freq='5min')
    test_data = pd.DataFrame({
        'timestamp': dates,
        'open': 1.0800 + np.random.normal(0, 0.001, 100).cumsum(),
        'high': 1.0800 + np.random.normal(0, 0.001, 100).cumsum() + 0.0005,
        'low': 1.0800 + np.random.normal(0, 0.001, 100).cumsum() - 0.0005,
        'close': 1.0800 + np.random.normal(0, 0.001, 100).cumsum(),
        'volume': np.random.randint(1000, 5000, 100)
    })
    test_data.set_index('timestamp', inplace=True)
    
    # 创建特征工程器
    feature_engine = ProCompatibleFeatureEngine()
    
    # 生成技术指标
    technical_indicators = feature_engine.create_sample_technical_indicators()
    
    # 生成特征
    features = feature_engine.generate_features(test_data, technical_indicators)
    
    print(f"📊 生成特征数量: {len(features.columns)}")
    print(f"📊 数据行数: {len(features)}")
    print(f"📊 特征列表: {list(features.columns)}")
    
    # 验证特征
    is_valid = feature_engine.validate_features(features)
    
    if is_valid:
        print("✅ 特征兼容性测试通过")
        print("💡 可以用于训练与Pro系统兼容的模型")
    else:
        print("❌ 特征兼容性测试失败")
    
    return is_valid, features

if __name__ == "__main__":
    test_pro_compatibility()
