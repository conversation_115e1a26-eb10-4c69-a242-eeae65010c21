"""
MT4简单连接测试脚本
只测试连接和获取信息，不执行交易操作
"""
import os
import sys
import time
from app.utils.mt4_client import mt4_client

def test_mt4_simple():
    """简单测试MT4连接"""
    try:
        print('开始简单测试...')
        
        # 连接到MT4服务器
        print('连接到MT4服务器...')
        connected = mt4_client.connect()
        print(f'连接结果: {connected}')
        
        if not connected:
            print('连接MT4服务器失败，无法继续测试')
            return
        
        # 获取账户信息
        print('\n获取账户信息...')
        account_info = mt4_client.get_account_info()
        print(f'账户信息: {account_info}')
        
        # 获取市场信息
        print('\n获取EURUSD市场信息...')
        market_info = mt4_client.get_market_info('EURUSD')
        print(f'市场信息: {market_info}')
        
        # 获取活跃订单
        print('\n获取活跃订单...')
        active_orders = mt4_client.get_active_orders()
        print(f'活跃订单: {active_orders}')
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_mt4_simple()
