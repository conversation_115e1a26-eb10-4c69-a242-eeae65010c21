#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
事件驱动交易模块
基于新闻和经济事件的交易决策
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .fundamental_analysis_engine import fundamental_engine, FundamentalSignal, FundamentalAnalysis
from .economic_calendar_manager import calendar_manager, EventImportance

class EventTradingAction(Enum):
    """事件交易动作"""
    ENTER_LONG = "enter_long"
    ENTER_SHORT = "enter_short"
    REDUCE_POSITION = "reduce_position"
    CLOSE_POSITION = "close_position"
    AVOID_TRADING = "avoid_trading"
    HOLD = "hold"

@dataclass
class EventTradingSignal:
    """事件交易信号"""
    symbol: str
    action: EventTradingAction
    confidence: float
    position_size_multiplier: float  # 仓位大小调整倍数
    urgency: str  # 'low', 'medium', 'high', 'critical'
    reasoning: str
    risk_level: str  # 'low', 'medium', 'high'
    time_horizon: str  # 'immediate', 'short', 'medium'
    stop_loss_adjustment: float  # 止损调整倍数
    take_profit_adjustment: float  # 止盈调整倍数

class EventDrivenTradingManager:
    """事件驱动交易管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 事件交易配置
        self.event_trading_config = {
            'pre_event_hours': 2,      # 事件前几小时开始调整
            'post_event_hours': 1,     # 事件后几小时保持调整
            'high_impact_threshold': 0.7,  # 高影响事件阈值
            'critical_impact_threshold': 0.9,  # 关键影响事件阈值
        }
        
        # 仓位调整配置
        self.position_adjustments = {
            'pre_critical_event': 0.3,    # 关键事件前减少70%仓位
            'pre_high_event': 0.5,        # 高影响事件前减少50%仓位
            'pre_medium_event': 0.8,      # 中等事件前减少20%仓位
            'strong_fundamental': 1.5,    # 强基本面信号增加50%仓位
            'weak_fundamental': 0.7       # 弱基本面信号减少30%仓位
        }
        
        # 风险调整配置
        self.risk_adjustments = {
            'stop_loss_multipliers': {
                'pre_event': 1.5,         # 事件前扩大止损
                'post_event': 1.2,        # 事件后适度扩大止损
                'high_volatility': 1.8    # 高波动期扩大止损
            },
            'take_profit_multipliers': {
                'strong_signal': 1.3,     # 强信号扩大止盈
                'weak_signal': 0.8,       # 弱信号缩小止盈
                'pre_event': 0.9          # 事件前保守止盈
            }
        }
    
    def analyze_event_trading_opportunity(self, symbol: str, current_positions: Dict) -> EventTradingSignal:
        """分析事件交易机会
        
        Args:
            symbol: 货币对符号
            current_positions: 当前持仓情况
            
        Returns:
            EventTradingSignal: 事件交易信号
        """
        try:
            # 1. 获取基本面分析
            fundamental_analysis = fundamental_engine.analyze_fundamentals(symbol, 'short')
            
            # 2. 获取即将到来的事件
            upcoming_events = calendar_manager.get_events_for_currency_pair(symbol, 24)
            
            # 3. 分析事件风险
            event_risk = self._analyze_event_risk(upcoming_events)
            
            # 4. 生成交易信号
            trading_signal = self._generate_event_trading_signal(
                symbol, fundamental_analysis, upcoming_events, event_risk, current_positions
            )
            
            self.logger.info(f"事件交易分析 {symbol}: {trading_signal.action.value}, "
                           f"置信度: {trading_signal.confidence:.2f}, "
                           f"紧急度: {trading_signal.urgency}")
            
            return trading_signal
            
        except Exception as e:
            self.logger.error(f"事件交易分析失败 {symbol}: {e}")
            return self._create_default_signal(symbol)
    
    def _analyze_event_risk(self, events: List) -> Dict:
        """分析事件风险"""
        risk_analysis = {
            'total_risk': 0.0,
            'critical_events': [],
            'high_impact_events': [],
            'time_to_next_event': None,
            'max_impact': 0.0
        }
        
        if not events:
            return risk_analysis
        
        now = datetime.now()
        
        for event in events:
            hours_until = (event.scheduled_time - now).total_seconds() / 3600
            
            # 只考虑未来48小时内的事件
            if hours_until < 0 or hours_until > 48:
                continue
            
            # 更新最大影响
            risk_analysis['max_impact'] = max(risk_analysis['max_impact'], event.market_impact)
            
            # 分类事件
            if event.market_impact >= self.event_trading_config['critical_impact_threshold']:
                risk_analysis['critical_events'].append(event)
            elif event.market_impact >= self.event_trading_config['high_impact_threshold']:
                risk_analysis['high_impact_events'].append(event)
            
            # 更新到下一个事件的时间
            if risk_analysis['time_to_next_event'] is None or hours_until < risk_analysis['time_to_next_event']:
                risk_analysis['time_to_next_event'] = hours_until
            
            # 累计风险分数
            time_weight = max(0.1, 1.0 - hours_until / 48)  # 时间越近权重越大
            risk_analysis['total_risk'] += event.market_impact * time_weight
        
        # 归一化总风险
        risk_analysis['total_risk'] = min(1.0, risk_analysis['total_risk'])
        
        return risk_analysis
    
    def _generate_event_trading_signal(self, symbol: str, fundamental_analysis: FundamentalAnalysis,
                                     events: List, event_risk: Dict, current_positions: Dict) -> EventTradingSignal:
        """生成事件交易信号"""
        
        # 1. 基础信号分析
        base_action, base_confidence = self._analyze_fundamental_signal(fundamental_analysis)
        
        # 2. 事件风险调整
        risk_adjusted_action, position_multiplier = self._adjust_for_event_risk(
            base_action, event_risk, current_positions.get(symbol)
        )
        
        # 3. 计算紧急度
        urgency = self._calculate_urgency(event_risk, fundamental_analysis)
        
        # 4. 风险等级评估
        risk_level = self._assess_risk_level(event_risk, fundamental_analysis)
        
        # 5. 时间范围确定
        time_horizon = self._determine_time_horizon(event_risk)
        
        # 6. 止损止盈调整
        stop_loss_adj, take_profit_adj = self._calculate_risk_adjustments(
            event_risk, fundamental_analysis, risk_level
        )
        
        # 7. 生成推理说明
        reasoning = self._generate_reasoning(
            fundamental_analysis, event_risk, risk_adjusted_action
        )
        
        return EventTradingSignal(
            symbol=symbol,
            action=risk_adjusted_action,
            confidence=base_confidence,
            position_size_multiplier=position_multiplier,
            urgency=urgency,
            reasoning=reasoning,
            risk_level=risk_level,
            time_horizon=time_horizon,
            stop_loss_adjustment=stop_loss_adj,
            take_profit_adjustment=take_profit_adj
        )
    
    def _analyze_fundamental_signal(self, analysis: FundamentalAnalysis) -> Tuple[EventTradingAction, float]:
        """分析基本面信号"""
        signal_map = {
            FundamentalSignal.STRONG_BULLISH: (EventTradingAction.ENTER_LONG, 0.9),
            FundamentalSignal.BULLISH: (EventTradingAction.ENTER_LONG, 0.7),
            FundamentalSignal.NEUTRAL: (EventTradingAction.HOLD, 0.3),
            FundamentalSignal.BEARISH: (EventTradingAction.ENTER_SHORT, 0.7),
            FundamentalSignal.STRONG_BEARISH: (EventTradingAction.ENTER_SHORT, 0.9)
        }
        
        action, base_confidence = signal_map.get(analysis.signal, (EventTradingAction.HOLD, 0.3))
        
        # 基于分析置信度调整
        adjusted_confidence = base_confidence * analysis.confidence
        
        return action, adjusted_confidence
    
    def _adjust_for_event_risk(self, base_action: EventTradingAction, event_risk: Dict, 
                             current_position: Optional[Dict]) -> Tuple[EventTradingAction, float]:
        """基于事件风险调整交易动作"""
        
        # 默认仓位倍数
        position_multiplier = 1.0
        adjusted_action = base_action
        
        # 检查关键事件
        if event_risk['critical_events']:
            if current_position:
                adjusted_action = EventTradingAction.REDUCE_POSITION
                position_multiplier = self.position_adjustments['pre_critical_event']
            else:
                adjusted_action = EventTradingAction.AVOID_TRADING
                position_multiplier = 0.0
        
        # 检查高影响事件
        elif event_risk['high_impact_events']:
            if event_risk['time_to_next_event'] and event_risk['time_to_next_event'] <= 2:
                # 2小时内有高影响事件
                if current_position:
                    adjusted_action = EventTradingAction.REDUCE_POSITION
                    position_multiplier = self.position_adjustments['pre_high_event']
                else:
                    # 新开仓位使用较小仓位
                    position_multiplier = self.position_adjustments['pre_high_event']
        
        # 基于总体风险调整
        if event_risk['total_risk'] >= 0.8:
            position_multiplier *= 0.5  # 高风险期减少50%仓位
        elif event_risk['total_risk'] >= 0.6:
            position_multiplier *= 0.7  # 中高风险期减少30%仓位
        
        return adjusted_action, position_multiplier
    
    def _calculate_urgency(self, event_risk: Dict, analysis: FundamentalAnalysis) -> str:
        """计算紧急度"""
        if event_risk['critical_events'] and event_risk['time_to_next_event'] and event_risk['time_to_next_event'] <= 1:
            return 'critical'
        elif event_risk['high_impact_events'] and event_risk['time_to_next_event'] and event_risk['time_to_next_event'] <= 2:
            return 'high'
        elif event_risk['total_risk'] >= 0.6 or analysis.confidence >= 0.8:
            return 'medium'
        else:
            return 'low'
    
    def _assess_risk_level(self, event_risk: Dict, analysis: FundamentalAnalysis) -> str:
        """评估风险等级"""
        if event_risk['critical_events'] or event_risk['total_risk'] >= 0.9:
            return 'high'
        elif event_risk['high_impact_events'] or event_risk['total_risk'] >= 0.6:
            return 'medium'
        else:
            return 'low'
    
    def _determine_time_horizon(self, event_risk: Dict) -> str:
        """确定时间范围"""
        if event_risk['time_to_next_event'] and event_risk['time_to_next_event'] <= 2:
            return 'immediate'
        elif event_risk['time_to_next_event'] and event_risk['time_to_next_event'] <= 12:
            return 'short'
        else:
            return 'medium'
    
    def _calculate_risk_adjustments(self, event_risk: Dict, analysis: FundamentalAnalysis, 
                                  risk_level: str) -> Tuple[float, float]:
        """计算风险调整倍数"""
        
        # 止损调整
        stop_loss_adj = 1.0
        if event_risk['critical_events'] or event_risk['high_impact_events']:
            stop_loss_adj = self.risk_adjustments['stop_loss_multipliers']['pre_event']
        elif event_risk['total_risk'] >= 0.7:
            stop_loss_adj = self.risk_adjustments['stop_loss_multipliers']['high_volatility']
        
        # 止盈调整
        take_profit_adj = 1.0
        if analysis.confidence >= 0.8:
            take_profit_adj = self.risk_adjustments['take_profit_multipliers']['strong_signal']
        elif analysis.confidence <= 0.4:
            take_profit_adj = self.risk_adjustments['take_profit_multipliers']['weak_signal']
        elif event_risk['high_impact_events']:
            take_profit_adj = self.risk_adjustments['take_profit_multipliers']['pre_event']
        
        return stop_loss_adj, take_profit_adj
    
    def _generate_reasoning(self, analysis: FundamentalAnalysis, event_risk: Dict, 
                          action: EventTradingAction) -> str:
        """生成推理说明"""
        reasons = []
        
        # 基本面因素
        if analysis.signal != FundamentalSignal.NEUTRAL:
            reasons.append(f"基本面信号: {analysis.signal.value} (置信度: {analysis.confidence:.1%})")
        
        # 事件风险因素
        if event_risk['critical_events']:
            reasons.append(f"关键事件风险: {len(event_risk['critical_events'])}个关键事件")
        elif event_risk['high_impact_events']:
            reasons.append(f"高影响事件: {len(event_risk['high_impact_events'])}个高影响事件")
        
        # 时间因素
        if event_risk['time_to_next_event'] and event_risk['time_to_next_event'] <= 2:
            reasons.append(f"临近事件: {event_risk['time_to_next_event']:.1f}小时后有重要事件")
        
        # 风险评估
        if event_risk['total_risk'] >= 0.8:
            reasons.append(f"高风险环境: 总风险评分 {event_risk['total_risk']:.1%}")
        
        return "; ".join(reasons) if reasons else "常规市场条件"
    
    def _create_default_signal(self, symbol: str) -> EventTradingSignal:
        """创建默认信号"""
        return EventTradingSignal(
            symbol=symbol,
            action=EventTradingAction.HOLD,
            confidence=0.0,
            position_size_multiplier=1.0,
            urgency='low',
            reasoning="分析失败，保持观望",
            risk_level='medium',
            time_horizon='medium',
            stop_loss_adjustment=1.0,
            take_profit_adjustment=1.0
        )
    
    def get_portfolio_event_signals(self, symbols: List[str], current_positions: Dict) -> Dict[str, EventTradingSignal]:
        """获取投资组合事件信号"""
        signals = {}
        
        for symbol in symbols:
            try:
                signal = self.analyze_event_trading_opportunity(symbol, current_positions)
                signals[symbol] = signal
            except Exception as e:
                self.logger.error(f"获取 {symbol} 事件信号失败: {e}")
                signals[symbol] = self._create_default_signal(symbol)
        
        return signals
    
    def get_market_risk_summary(self) -> Dict:
        """获取市场风险总结"""
        try:
            # 获取风险评估
            risk_assessment = fundamental_engine.get_risk_assessment(48)
            
            # 计算总体风险等级
            total_critical = len(risk_assessment['critical_events'])
            total_high_impact = len(risk_assessment['high_impact_events'])
            
            if total_critical >= 2:
                overall_risk = 'critical'
            elif total_critical >= 1 or total_high_impact >= 3:
                overall_risk = 'high'
            elif total_high_impact >= 1:
                overall_risk = 'medium'
            else:
                overall_risk = 'low'
            
            return {
                'overall_risk': overall_risk,
                'critical_events_count': total_critical,
                'high_impact_events_count': total_high_impact,
                'risk_details': risk_assessment,
                'recommendation': self._get_risk_recommendation(overall_risk)
            }
            
        except Exception as e:
            self.logger.error(f"获取市场风险总结失败: {e}")
            return {
                'overall_risk': 'unknown',
                'critical_events_count': 0,
                'high_impact_events_count': 0,
                'risk_details': {},
                'recommendation': '数据获取失败，建议谨慎交易'
            }
    
    def _get_risk_recommendation(self, risk_level: str) -> str:
        """获取风险建议"""
        recommendations = {
            'low': '正常交易，保持常规风险管理',
            'medium': '适度谨慎，考虑减少仓位规模',
            'high': '高度谨慎，建议减少新开仓位',
            'critical': '极度谨慎，建议暂停交易或大幅减仓'
        }
        
        return recommendations.get(risk_level, '保持谨慎')

# 创建全局实例
event_trading_manager = EventDrivenTradingManager()
