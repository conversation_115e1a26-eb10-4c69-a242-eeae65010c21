#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易系统可视化仪表板
提供实时的系统状态、分析结果和交易信息的可视化展示
"""

import os
import sys
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TradingDashboard:
    """交易系统可视化仪表板"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("外汇交易系统 - 实时监控仪表板")
        self.root.geometry("1400x900")
        self.root.configure(bg='#2b2b2b')
        
        # 数据存储
        self.market_data = {}
        self.analysis_history = []
        self.system_status = {
            'last_update': None,
            'components_status': {},
            'error_count': 0,
            'success_count': 0
        }
        
        # 创建界面
        self.create_widgets()
        
        # 启动数据更新线程
        self.running = True
        self.update_thread = threading.Thread(target=self.update_data_loop, daemon=True)
        self.update_thread.start()
    
    def create_widgets(self):
        """创建界面组件"""
        
        # 主标题
        title_frame = tk.Frame(self.root, bg='#2b2b2b')
        title_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = tk.Label(
            title_frame, 
            text="🚀 外汇交易系统实时监控仪表板", 
            font=('Arial', 16, 'bold'),
            fg='#00ff00',
            bg='#2b2b2b'
        )
        title_label.pack()
        
        # 创建主要区域
        main_frame = tk.Frame(self.root, bg='#2b2b2b')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 左侧面板 - 系统状态
        left_frame = tk.Frame(main_frame, bg='#3b3b3b', relief='raised', bd=2)
        left_frame.pack(side='left', fill='y', padx=(0, 5))
        
        self.create_status_panel(left_frame)
        
        # 中间面板 - 图表
        center_frame = tk.Frame(main_frame, bg='#3b3b3b', relief='raised', bd=2)
        center_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        self.create_chart_panel(center_frame)
        
        # 右侧面板 - 分析结果
        right_frame = tk.Frame(main_frame, bg='#3b3b3b', relief='raised', bd=2)
        right_frame.pack(side='right', fill='y', padx=(5, 0))
        
        self.create_analysis_panel(right_frame)
        
        # 底部状态栏
        self.create_status_bar()
    
    def create_status_panel(self, parent):
        """创建系统状态面板"""
        
        # 标题
        status_title = tk.Label(
            parent, 
            text="📊 系统状态", 
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#3b3b3b'
        )
        status_title.pack(pady=10)
        
        # 系统组件状态
        self.status_frame = tk.Frame(parent, bg='#3b3b3b')
        self.status_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 组件状态标签
        self.component_labels = {}
        components = [
            ('数据处理', 'data_processor'),
            ('市场分析', 'market_analyzer'),
            ('预分析', 'pre_analyzer'),
            ('LLM分析', 'llm_analyzer'),
            ('风险管理', 'risk_manager'),
            ('组合管理', 'portfolio_manager'),
            ('MT4连接', 'mt4_connection')
        ]
        
        for i, (name, key) in enumerate(components):
            frame = tk.Frame(self.status_frame, bg='#3b3b3b')
            frame.pack(fill='x', pady=2)
            
            label = tk.Label(
                frame,
                text=f"{name}:",
                font=('Arial', 9),
                fg='#cccccc',
                bg='#3b3b3b',
                width=12,
                anchor='w'
            )
            label.pack(side='left')
            
            status_label = tk.Label(
                frame,
                text="🔴 离线",
                font=('Arial', 9),
                fg='#ff6666',
                bg='#3b3b3b',
                anchor='w'
            )
            status_label.pack(side='left')
            
            self.component_labels[key] = status_label
        
        # 统计信息
        stats_frame = tk.Frame(parent, bg='#3b3b3b')
        stats_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(
            stats_frame,
            text="📈 运行统计",
            font=('Arial', 10, 'bold'),
            fg='#ffffff',
            bg='#3b3b3b'
        ).pack()
        
        self.stats_labels = {}
        stats = [
            ('成功分析', 'success_count'),
            ('错误次数', 'error_count'),
            ('运行时间', 'uptime')
        ]
        
        for name, key in stats:
            frame = tk.Frame(stats_frame, bg='#3b3b3b')
            frame.pack(fill='x', pady=1)
            
            tk.Label(
                frame,
                text=f"{name}:",
                font=('Arial', 9),
                fg='#cccccc',
                bg='#3b3b3b',
                width=8,
                anchor='w'
            ).pack(side='left')
            
            value_label = tk.Label(
                frame,
                text="0",
                font=('Arial', 9),
                fg='#00ff00',
                bg='#3b3b3b',
                anchor='w'
            )
            value_label.pack(side='left')
            
            self.stats_labels[key] = value_label
    
    def create_chart_panel(self, parent):
        """创建图表面板"""
        
        # 标题
        chart_title = tk.Label(
            parent,
            text="📈 市场数据与分析结果",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#3b3b3b'
        )
        chart_title.pack(pady=10)
        
        # 创建matplotlib图表
        self.fig = Figure(figsize=(10, 6), facecolor='#3b3b3b')
        self.fig.suptitle('实时市场分析', color='white', fontsize=14)
        
        # 创建子图
        self.ax1 = self.fig.add_subplot(2, 2, 1, facecolor='#2b2b2b')
        self.ax2 = self.fig.add_subplot(2, 2, 2, facecolor='#2b2b2b')
        self.ax3 = self.fig.add_subplot(2, 2, 3, facecolor='#2b2b2b')
        self.ax4 = self.fig.add_subplot(2, 2, 4, facecolor='#2b2b2b')
        
        # 设置图表样式
        for ax in [self.ax1, self.ax2, self.ax3, self.ax4]:
            ax.tick_params(colors='white')
            ax.spines['bottom'].set_color('white')
            ax.spines['top'].set_color('white')
            ax.spines['right'].set_color('white')
            ax.spines['left'].set_color('white')
        
        self.ax1.set_title('价格走势', color='white')
        self.ax2.set_title('技术指标', color='white')
        self.ax3.set_title('风险指标', color='white')
        self.ax4.set_title('分析信心度', color='white')
        
        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, parent)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=5)
    
    def create_analysis_panel(self, parent):
        """创建分析结果面板"""
        
        # 标题
        analysis_title = tk.Label(
            parent,
            text="🧠 最新分析结果",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#3b3b3b'
        )
        analysis_title.pack(pady=10)
        
        # 分析结果显示区域
        self.analysis_frame = tk.Frame(parent, bg='#3b3b3b')
        self.analysis_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 创建文本显示区域
        self.analysis_text = tk.Text(
            self.analysis_frame,
            bg='#2b2b2b',
            fg='#00ff00',
            font=('Consolas', 9),
            wrap='word',
            height=20,
            width=35
        )
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.analysis_frame, orient='vertical', command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=scrollbar.set)
        
        self.analysis_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 控制按钮
        button_frame = tk.Frame(parent, bg='#3b3b3b')
        button_frame.pack(fill='x', padx=10, pady=5)
        
        self.start_button = tk.Button(
            button_frame,
            text="▶️ 启动监控",
            command=self.toggle_monitoring,
            bg='#4CAF50',
            fg='white',
            font=('Arial', 10, 'bold')
        )
        self.start_button.pack(side='left', padx=2)
        
        self.refresh_button = tk.Button(
            button_frame,
            text="🔄 刷新数据",
            command=self.refresh_data,
            bg='#2196F3',
            fg='white',
            font=('Arial', 10, 'bold')
        )
        self.refresh_button.pack(side='left', padx=2)
        
        self.clear_button = tk.Button(
            button_frame,
            text="🗑️ 清空日志",
            command=self.clear_logs,
            bg='#f44336',
            fg='white',
            font=('Arial', 10, 'bold')
        )
        self.clear_button.pack(side='left', padx=2)
    
    def create_status_bar(self):
        """创建状态栏"""
        
        self.status_bar = tk.Frame(self.root, bg='#1b1b1b', relief='sunken', bd=1)
        self.status_bar.pack(side='bottom', fill='x')
        
        self.status_label = tk.Label(
            self.status_bar,
            text="🔴 系统离线 | 等待连接...",
            bg='#1b1b1b',
            fg='#cccccc',
            font=('Arial', 9),
            anchor='w'
        )
        self.status_label.pack(side='left', padx=10, pady=2)
        
        self.time_label = tk.Label(
            self.status_bar,
            text="",
            bg='#1b1b1b',
            fg='#cccccc',
            font=('Arial', 9),
            anchor='e'
        )
        self.time_label.pack(side='right', padx=10, pady=2)
    
    def update_data_loop(self):
        """数据更新循环"""
        
        while self.running:
            try:
                # 模拟获取系统数据
                self.simulate_system_data()
                
                # 更新界面
                self.root.after(0, self.update_display)
                
                # 更新时间
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.root.after(0, lambda: self.time_label.config(text=current_time))
                
                time.sleep(5)  # 每5秒更新一次
                
            except Exception as e:
                print(f"数据更新错误: {e}")
                time.sleep(10)
    
    def simulate_system_data(self):
        """模拟系统数据（实际使用时替换为真实数据获取）"""
        
        # 模拟市场数据
        self.market_data = {
            'symbol': 'EURUSD',
            'price': 1.13550 + np.random.normal(0, 0.0001),
            'rsi': 50 + np.random.normal(0, 10),
            'macd': np.random.normal(0, 0.001),
            'ma_20': 1.13500 + np.random.normal(0, 0.0001),
            'timestamp': datetime.now()
        }
        
        # 模拟分析结果
        analysis_result = {
            'timestamp': datetime.now(),
            'action': np.random.choice(['BUY', 'SELL', 'HOLD']),
            'confidence': np.random.uniform(0.6, 0.9),
            'risk_level': np.random.choice(['LOW', 'MEDIUM', 'HIGH']),
            'reasoning': "基于当前市场条件的分析结果..."
        }
        
        self.analysis_history.append(analysis_result)
        if len(self.analysis_history) > 50:
            self.analysis_history.pop(0)
        
        # 模拟系统状态
        self.system_status['last_update'] = datetime.now()
        self.system_status['success_count'] += 1
        
        # 随机模拟组件状态
        components = ['data_processor', 'market_analyzer', 'pre_analyzer', 
                     'llm_analyzer', 'risk_manager', 'portfolio_manager']
        
        for comp in components:
            self.system_status['components_status'][comp] = np.random.choice([True, False], p=[0.9, 0.1])
        
        # MT4连接状态（周末通常离线）
        is_weekend = datetime.now().weekday() >= 5
        self.system_status['components_status']['mt4_connection'] = not is_weekend
    
    def update_display(self):
        """更新显示内容"""
        
        # 更新组件状态
        for comp, label in self.component_labels.items():
            status = self.system_status['components_status'].get(comp, False)
            if status:
                label.config(text="🟢 在线", fg='#00ff00')
            else:
                label.config(text="🔴 离线", fg='#ff6666')
        
        # 更新统计信息
        self.stats_labels['success_count'].config(text=str(self.system_status['success_count']))
        self.stats_labels['error_count'].config(text=str(self.system_status['error_count']))
        
        if self.system_status['last_update']:
            uptime = datetime.now() - self.system_status['last_update']
            self.stats_labels['uptime'].config(text=f"{uptime.seconds}s")
        
        # 更新图表
        self.update_charts()
        
        # 更新分析结果
        self.update_analysis_display()
        
        # 更新状态栏
        online_count = sum(1 for status in self.system_status['components_status'].values() if status)
        total_count = len(self.system_status['components_status'])
        
        if online_count == total_count:
            status_text = f"🟢 系统在线 | {online_count}/{total_count} 组件正常"
            self.status_label.config(fg='#00ff00')
        elif online_count > 0:
            status_text = f"🟡 部分在线 | {online_count}/{total_count} 组件正常"
            self.status_label.config(fg='#ffff00')
        else:
            status_text = f"🔴 系统离线 | {online_count}/{total_count} 组件正常"
            self.status_label.config(fg='#ff6666')
        
        self.status_label.config(text=status_text)
    
    def update_charts(self):
        """更新图表"""
        
        if not self.analysis_history:
            return
        
        # 清空图表
        self.ax1.clear()
        self.ax2.clear()
        self.ax3.clear()
        self.ax4.clear()
        
        # 准备数据
        times = [item['timestamp'] for item in self.analysis_history[-20:]]
        confidences = [item['confidence'] for item in self.analysis_history[-20:]]
        
        # 图表1: 价格走势（模拟）
        prices = [1.13550 + i * 0.0001 + np.random.normal(0, 0.0001) for i in range(len(times))]
        self.ax1.plot(range(len(times)), prices, color='#00ff00', linewidth=2)
        self.ax1.set_title('价格走势', color='white')
        self.ax1.tick_params(colors='white')
        
        # 图表2: 技术指标
        rsi_values = [50 + np.random.normal(0, 10) for _ in range(len(times))]
        self.ax2.plot(range(len(times)), rsi_values, color='#ff6600', linewidth=2, label='RSI')
        self.ax2.axhline(y=70, color='red', linestyle='--', alpha=0.7)
        self.ax2.axhline(y=30, color='red', linestyle='--', alpha=0.7)
        self.ax2.set_title('技术指标 (RSI)', color='white')
        self.ax2.tick_params(colors='white')
        
        # 图表3: 风险指标
        risk_levels = []
        for item in self.analysis_history[-20:]:
            if item['risk_level'] == 'LOW':
                risk_levels.append(1)
            elif item['risk_level'] == 'MEDIUM':
                risk_levels.append(2)
            else:
                risk_levels.append(3)
        
        colors = ['green' if x == 1 else 'yellow' if x == 2 else 'red' for x in risk_levels]
        self.ax3.bar(range(len(risk_levels)), risk_levels, color=colors, alpha=0.7)
        self.ax3.set_title('风险等级', color='white')
        self.ax3.tick_params(colors='white')
        
        # 图表4: 分析信心度
        self.ax4.plot(range(len(confidences)), confidences, color='#0099ff', linewidth=2, marker='o')
        self.ax4.set_ylim(0, 1)
        self.ax4.set_title('分析信心度', color='white')
        self.ax4.tick_params(colors='white')
        
        # 设置图表样式
        for ax in [self.ax1, self.ax2, self.ax3, self.ax4]:
            ax.spines['bottom'].set_color('white')
            ax.spines['top'].set_color('white')
            ax.spines['right'].set_color('white')
            ax.spines['left'].set_color('white')
        
        self.canvas.draw()
    
    def update_analysis_display(self):
        """更新分析结果显示"""
        
        if not self.analysis_history:
            return
        
        # 获取最新分析结果
        latest = self.analysis_history[-1]
        
        # 格式化显示文本
        display_text = f"""
📊 最新分析结果
{'='*30}
🕐 时间: {latest['timestamp'].strftime('%H:%M:%S')}
📈 交易建议: {latest['action']}
🎯 信心度: {latest['confidence']:.2%}
⚠️ 风险等级: {latest['risk_level']}
💭 分析理由: {latest['reasoning']}

📋 历史记录 (最近10次):
{'='*30}
"""
        
        # 添加历史记录
        for i, item in enumerate(self.analysis_history[-10:]):
            display_text += f"{i+1:2d}. {item['timestamp'].strftime('%H:%M')} | {item['action']:4s} | {item['confidence']:.1%}\n"
        
        # 更新文本显示
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(1.0, display_text)
        self.analysis_text.see(tk.END)
    
    def toggle_monitoring(self):
        """切换监控状态"""
        # 这里可以添加启动/停止监控的逻辑
        if self.start_button['text'] == "▶️ 启动监控":
            self.start_button.config(text="⏸️ 暂停监控", bg='#ff9800')
            messagebox.showinfo("监控状态", "监控已启动")
        else:
            self.start_button.config(text="▶️ 启动监控", bg='#4CAF50')
            messagebox.showinfo("监控状态", "监控已暂停")
    
    def refresh_data(self):
        """刷新数据"""
        self.simulate_system_data()
        self.update_display()
        messagebox.showinfo("刷新", "数据已刷新")
    
    def clear_logs(self):
        """清空日志"""
        self.analysis_history.clear()
        self.analysis_text.delete(1.0, tk.END)
        messagebox.showinfo("清空", "日志已清空")
    
    def run(self):
        """运行仪表板"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except Exception as e:
            print(f"仪表板运行错误: {e}")
    
    def on_closing(self):
        """关闭事件处理"""
        self.running = False
        self.root.destroy()

def main():
    """主函数"""
    print("启动交易系统可视化仪表板...")
    
    try:
        dashboard = TradingDashboard()
        dashboard.run()
    except Exception as e:
        print(f"启动失败: {e}")
        messagebox.showerror("错误", f"仪表板启动失败: {e}")

if __name__ == "__main__":
    main()
