@echo off
chcp 65001
echo ========================================
echo 设置外汇交易系统智能自动模式
echo ========================================

echo 正在清除环境变量...

REM 清除MT4连接设置，让系统自动判断
set SKIP_MT4_CONNECTION=

REM 设置其他环境变量
set FLASK_ENV=production
set LOG_LEVEL=INFO

echo ✅ 智能自动模式已启用
echo.
echo 📋 当前设置:
echo   - SKIP_MT4_CONNECTION=(未设置) - 系统将根据市场时间自动判断
echo   - FLASK_ENV=production (生产环境)
echo   - LOG_LEVEL=INFO (信息级别日志)
echo.
echo 🧠 智能模式说明:
echo   - 市场开放时间: 自动连接MT4服务器
echo   - 市场关闭时间: 自动跳过MT4连接，使用模拟数据
echo   - 周一到周五: 正常交易模式
echo   - 周六到周日: 自动测试模式
echo.
echo ✨ 这是推荐的运行模式，无需手动切换！
echo.
echo 如需手动控制:
echo   - 强制测试模式: set_test_mode.bat
echo   - 强制生产模式: set_production_mode.bat
echo.
echo 现在可以运行系统了:
echo   python run.py
echo.
echo ========================================
pause
