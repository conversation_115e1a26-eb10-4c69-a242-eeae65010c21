#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版智能市场监控系统
零Token成本的实时监控 + 精准LLM触发 + 成本控制
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

class AlertLevel(Enum):
    """警报级别"""
    CRITICAL = "紧急"      # 立即触发LLM分析
    HIGH = "高"           # 优先触发LLM分析
    MEDIUM = "中"         # 条件触发LLM分析
    LOW = "低"            # 记录但不触发
    INFO = "信息"         # 仅记录

@dataclass
class MarketAlert:
    """市场警报"""
    timestamp: datetime
    level: AlertLevel
    category: str
    message: str
    data: Dict
    action_required: bool
    urgency_score: float  # 0-100

class EnhancedMarketMonitor:
    """增强版市场监控器"""
    
    def __init__(self):
        # 监控配置
        self.config = {
            # 价格监控
            'price_change_thresholds': {
                'critical': 0.5,    # 0.5%变化立即触发
                'high': 0.3,        # 0.3%变化高优先级
                'medium': 0.15      # 0.15%变化中优先级
            },
            
            # 波动率监控
            'volatility_multipliers': {
                'critical': 3.0,    # 3倍平均波动率
                'high': 2.0,        # 2倍平均波动率
                'medium': 1.5       # 1.5倍平均波动率
            },
            
            # 时间控制
            'analysis_intervals': {
                'min_interval': 180,        # 最小间隔3分钟
                'max_quiet_period': 1800,   # 最大静默30分钟
                'emergency_override': 60    # 紧急情况1分钟
            },
            
            # 成本控制
            'cost_limits': {
                'max_hourly_analyses': 8,   # 每小时最多8次
                'max_daily_analyses': 100,  # 每天最多100次
                'emergency_budget': 5       # 紧急预算5次
            }
        }
        
        # 状态跟踪
        self.state = {
            'last_analysis_time': None,
            'hourly_analysis_count': 0,
            'daily_analysis_count': 0,
            'last_hour': datetime.now().hour,
            'last_date': datetime.now().date(),
            'emergency_budget_used': 0
        }
        
        # 市场数据缓存
        self.market_cache = {
            'price_history': [],
            'volatility_history': [],
            'rsi_history': [],
            'volume_history': [],
            'key_levels': {'support': [], 'resistance': []},
            'baseline_metrics': {}
        }
        
        # 警报历史
        self.alert_history = []
    
    def monitor(self, market_data: Dict) -> Dict:
        """
        主监控函数
        
        Returns:
            Dict: 监控结果，包含是否需要触发LLM分析
        """
        
        # 1. 更新缓存和状态
        self._update_cache(market_data)
        self._update_state()
        
        # 2. 执行多维度监控
        alerts = []
        
        # 价格监控
        price_alerts = self._monitor_price_action(market_data)
        alerts.extend(price_alerts)
        
        # 技术指标监控
        technical_alerts = self._monitor_technical_indicators(market_data)
        alerts.extend(technical_alerts)
        
        # 风险监控
        risk_alerts = self._monitor_risk_factors(market_data)
        alerts.extend(risk_alerts)
        
        # 时间监控
        time_alerts = self._monitor_time_factors(market_data)
        alerts.extend(time_alerts)
        
        # 3. 评估是否需要触发LLM分析
        trigger_decision = self._evaluate_trigger_decision(alerts)
        
        # 4. 记录警报
        for alert in alerts:
            self._record_alert(alert)
        
        return trigger_decision
    
    def _monitor_price_action(self, market_data: Dict) -> List[MarketAlert]:
        """监控价格行为"""
        alerts = []
        current_price = market_data.get('current_price', 0)
        
        if len(self.market_cache['price_history']) < 2:
            return alerts
        
        # 计算价格变化
        last_price = self.market_cache['price_history'][-2]
        price_change_pct = abs(current_price - last_price) / last_price * 100
        
        # 价格变化警报
        if price_change_pct >= self.config['price_change_thresholds']['critical']:
            alerts.append(MarketAlert(
                timestamp=datetime.now(),
                level=AlertLevel.CRITICAL,
                category="价格突破",
                message=f"价格剧烈变化 {price_change_pct:.2f}%",
                data={'price_change': price_change_pct, 'current_price': current_price},
                action_required=True,
                urgency_score=95
            ))
        elif price_change_pct >= self.config['price_change_thresholds']['high']:
            alerts.append(MarketAlert(
                timestamp=datetime.now(),
                level=AlertLevel.HIGH,
                category="价格变化",
                message=f"价格显著变化 {price_change_pct:.2f}%",
                data={'price_change': price_change_pct, 'current_price': current_price},
                action_required=True,
                urgency_score=75
            ))
        
        # 关键位突破监控
        support_breach = self._check_support_breach(current_price)
        if support_breach:
            alerts.append(support_breach)
        
        resistance_breach = self._check_resistance_breach(current_price)
        if resistance_breach:
            alerts.append(resistance_breach)
        
        return alerts
    
    def _monitor_technical_indicators(self, market_data: Dict) -> List[MarketAlert]:
        """监控技术指标"""
        alerts = []
        
        # RSI监控
        rsi = market_data.get('rsi', 50)
        self.market_cache['rsi_history'].append(rsi)
        
        if rsi >= 80:
            alerts.append(MarketAlert(
                timestamp=datetime.now(),
                level=AlertLevel.HIGH,
                category="RSI超买",
                message=f"RSI达到超买区域 {rsi:.1f}",
                data={'rsi': rsi},
                action_required=True,
                urgency_score=70
            ))
        elif rsi <= 20:
            alerts.append(MarketAlert(
                timestamp=datetime.now(),
                level=AlertLevel.HIGH,
                category="RSI超卖",
                message=f"RSI达到超卖区域 {rsi:.1f}",
                data={'rsi': rsi},
                action_required=True,
                urgency_score=70
            ))
        
        # MACD监控（如果有数据）
        macd = market_data.get('macd', {})
        if macd:
            macd_line = macd.get('line', 0)
            signal_line = macd.get('signal', 0)
            
            # MACD金叉死叉
            if len(self.market_cache.get('macd_history', [])) > 0:
                last_macd = self.market_cache['macd_history'][-1]
                if (macd_line > signal_line and last_macd['line'] <= last_macd['signal']):
                    alerts.append(MarketAlert(
                        timestamp=datetime.now(),
                        level=AlertLevel.MEDIUM,
                        category="MACD金叉",
                        message="MACD出现金叉信号",
                        data={'macd': macd_line, 'signal': signal_line},
                        action_required=True,
                        urgency_score=60
                    ))
        
        return alerts
    
    def _monitor_risk_factors(self, market_data: Dict) -> List[MarketAlert]:
        """监控风险因素"""
        alerts = []
        
        # 持仓风险监控
        positions = market_data.get('positions', [])
        current_price = market_data.get('current_price', 0)
        
        for position in positions:
            # 止损风险
            stop_loss = position.get('stop_loss', 0)
            if stop_loss > 0:
                if position.get('type') == 'BUY':
                    distance_pct = (current_price - stop_loss) / current_price * 100
                else:
                    distance_pct = (stop_loss - current_price) / current_price * 100
                
                if distance_pct < 0.05:  # 距离止损5个点以内
                    alerts.append(MarketAlert(
                        timestamp=datetime.now(),
                        level=AlertLevel.CRITICAL,
                        category="止损风险",
                        message=f"持仓接近止损，距离 {distance_pct:.2f}%",
                        data={'position': position, 'distance': distance_pct},
                        action_required=True,
                        urgency_score=90
                    ))
        
        # 波动率风险
        if len(self.market_cache['volatility_history']) >= 10:
            current_vol = self.market_cache['volatility_history'][-1]
            avg_vol = np.mean(self.market_cache['volatility_history'][-10:])
            
            vol_ratio = current_vol / avg_vol if avg_vol > 0 else 1
            
            if vol_ratio >= self.config['volatility_multipliers']['critical']:
                alerts.append(MarketAlert(
                    timestamp=datetime.now(),
                    level=AlertLevel.CRITICAL,
                    category="波动率异常",
                    message=f"波动率异常升高 {vol_ratio:.1f}倍",
                    data={'volatility_ratio': vol_ratio},
                    action_required=True,
                    urgency_score=85
                ))
        
        return alerts
    
    def _monitor_time_factors(self, market_data: Dict) -> List[MarketAlert]:
        """监控时间因素"""
        alerts = []
        
        # 检查是否超过最大静默期
        if self.state['last_analysis_time']:
            time_since_last = (datetime.now() - self.state['last_analysis_time']).total_seconds()
            max_quiet = self.config['analysis_intervals']['max_quiet_period']
            
            if time_since_last > max_quiet:
                alerts.append(MarketAlert(
                    timestamp=datetime.now(),
                    level=AlertLevel.MEDIUM,
                    category="定时检查",
                    message=f"超过最大静默期 {max_quiet/60:.0f} 分钟",
                    data={'time_since_last': time_since_last},
                    action_required=True,
                    urgency_score=40
                ))
        
        # 重要时间段监控
        current_hour = datetime.now().hour
        if current_hour in [8, 14, 20]:  # 重要开盘时间
            alerts.append(MarketAlert(
                timestamp=datetime.now(),
                level=AlertLevel.LOW,
                category="重要时段",
                message=f"进入重要交易时段 {current_hour}:00",
                data={'hour': current_hour},
                action_required=False,
                urgency_score=30
            ))
        
        return alerts
    
    def _evaluate_trigger_decision(self, alerts: List[MarketAlert]) -> Dict:
        """评估是否触发LLM分析"""
        
        # 检查成本限制
        if not self._check_cost_limits(alerts):
            return {
                'should_analyze': False,
                'reason': '达到成本限制',
                'alerts_count': len(alerts),
                'max_urgency': max([a.urgency_score for a in alerts]) if alerts else 0,
                'token_cost': 0,
                'system_used': 'ENHANCED_MONITOR'
            }
        
        # 评估警报
        if not alerts:
            return {
                'should_analyze': False,
                'reason': '无警报触发',
                'alerts_count': 0,
                'max_urgency': 0,
                'token_cost': 0,
                'system_used': 'ENHANCED_MONITOR'
            }
        
        # 检查是否有紧急或高级别警报
        critical_alerts = [a for a in alerts if a.level == AlertLevel.CRITICAL]
        high_alerts = [a for a in alerts if a.level == AlertLevel.HIGH]
        
        max_urgency = max([a.urgency_score for a in alerts])
        
        # 决策逻辑
        should_analyze = False
        reason = ""
        
        if critical_alerts:
            should_analyze = True
            reason = f"紧急警报: {critical_alerts[0].message}"
        elif high_alerts and max_urgency >= 70:
            should_analyze = True
            reason = f"高级警报: {high_alerts[0].message}"
        elif max_urgency >= 60 and self._check_time_interval():
            should_analyze = True
            reason = f"中级警报达到阈值: {max_urgency}"
        else:
            reason = f"警报级别不足: 最高紧急度 {max_urgency}"
        
        # 更新状态
        if should_analyze:
            self._update_analysis_counters()
        
        return {
            'should_analyze': should_analyze,
            'reason': reason,
            'alerts_count': len(alerts),
            'critical_alerts': len(critical_alerts),
            'high_alerts': len(high_alerts),
            'max_urgency': max_urgency,
            'alerts_summary': [
                {
                    'level': a.level.value,
                    'category': a.category,
                    'message': a.message,
                    'urgency': a.urgency_score
                }
                for a in alerts[:3]  # 只显示前3个警报
            ],
            'token_cost': 0,
            'system_used': 'ENHANCED_MONITOR',
            'monitoring_stats': self._get_monitoring_stats()
        }
    
    def _check_cost_limits(self, alerts: List[MarketAlert]) -> bool:
        """检查成本限制"""
        # 紧急情况可以覆盖限制
        critical_alerts = [a for a in alerts if a.level == AlertLevel.CRITICAL]
        if critical_alerts and self.state['emergency_budget_used'] < self.config['cost_limits']['emergency_budget']:
            return True
        
        # 检查小时限制
        if self.state['hourly_analysis_count'] >= self.config['cost_limits']['max_hourly_analyses']:
            return False
        
        # 检查日限制
        if self.state['daily_analysis_count'] >= self.config['cost_limits']['max_daily_analyses']:
            return False
        
        return True
    
    def _check_time_interval(self) -> bool:
        """检查时间间隔"""
        if not self.state['last_analysis_time']:
            return True
        
        time_since_last = (datetime.now() - self.state['last_analysis_time']).total_seconds()
        min_interval = self.config['analysis_intervals']['min_interval']
        
        return time_since_last >= min_interval
    
    def _update_cache(self, market_data: Dict):
        """更新市场数据缓存"""
        current_price = market_data.get('current_price', 0)
        
        # 更新价格历史
        self.market_cache['price_history'].append(current_price)
        if len(self.market_cache['price_history']) > 100:
            self.market_cache['price_history'] = self.market_cache['price_history'][-100:]
        
        # 计算并缓存波动率
        if len(self.market_cache['price_history']) >= 20:
            prices = self.market_cache['price_history'][-20:]
            volatility = np.std(prices) / np.mean(prices) if np.mean(prices) > 0 else 0
            self.market_cache['volatility_history'].append(volatility)
            
            if len(self.market_cache['volatility_history']) > 50:
                self.market_cache['volatility_history'] = self.market_cache['volatility_history'][-50:]
        
        # 更新关键位
        self._update_key_levels(current_price)
    
    def _update_key_levels(self, current_price: float):
        """更新关键支撑阻力位"""
        # 简化的关键位计算
        self.market_cache['key_levels']['support'] = [
            current_price * 0.998,  # 0.2%下方
            current_price * 0.995,  # 0.5%下方
            current_price * 0.990   # 1.0%下方
        ]
        
        self.market_cache['key_levels']['resistance'] = [
            current_price * 1.002,  # 0.2%上方
            current_price * 1.005,  # 0.5%上方
            current_price * 1.010   # 1.0%上方
        ]
    
    def _update_state(self):
        """更新状态"""
        now = datetime.now()
        
        # 重置小时计数器
        if now.hour != self.state['last_hour']:
            self.state['hourly_analysis_count'] = 0
            self.state['last_hour'] = now.hour
        
        # 重置日计数器
        if now.date() != self.state['last_date']:
            self.state['daily_analysis_count'] = 0
            self.state['emergency_budget_used'] = 0
            self.state['last_date'] = now.date()
    
    def _update_analysis_counters(self):
        """更新分析计数器"""
        self.state['hourly_analysis_count'] += 1
        self.state['daily_analysis_count'] += 1
        self.state['last_analysis_time'] = datetime.now()
    
    def _check_support_breach(self, current_price: float) -> Optional[MarketAlert]:
        """检查支撑位突破"""
        for level in self.market_cache['key_levels']['support']:
            if current_price < level:
                return MarketAlert(
                    timestamp=datetime.now(),
                    level=AlertLevel.HIGH,
                    category="支撑突破",
                    message=f"价格跌破支撑位 {level:.5f}",
                    data={'support_level': level, 'current_price': current_price},
                    action_required=True,
                    urgency_score=80
                )
        return None
    
    def _check_resistance_breach(self, current_price: float) -> Optional[MarketAlert]:
        """检查阻力位突破"""
        for level in self.market_cache['key_levels']['resistance']:
            if current_price > level:
                return MarketAlert(
                    timestamp=datetime.now(),
                    level=AlertLevel.HIGH,
                    category="阻力突破",
                    message=f"价格突破阻力位 {level:.5f}",
                    data={'resistance_level': level, 'current_price': current_price},
                    action_required=True,
                    urgency_score=80
                )
        return None
    
    def _record_alert(self, alert: MarketAlert):
        """记录警报"""
        self.alert_history.append(alert)
        
        # 保持最近100个警报
        if len(self.alert_history) > 100:
            self.alert_history = self.alert_history[-100:]
    
    def _get_monitoring_stats(self) -> Dict:
        """获取监控统计"""
        return {
            'hourly_analysis_count': self.state['hourly_analysis_count'],
            'daily_analysis_count': self.state['daily_analysis_count'],
            'emergency_budget_remaining': self.config['cost_limits']['emergency_budget'] - self.state['emergency_budget_used'],
            'last_analysis_time': self.state['last_analysis_time'].isoformat() if self.state['last_analysis_time'] else None,
            'total_alerts_today': len([a for a in self.alert_history if a.timestamp.date() == datetime.now().date()]),
            'cache_size': {
                'price_history': len(self.market_cache['price_history']),
                'volatility_history': len(self.market_cache['volatility_history']),
                'alert_history': len(self.alert_history)
            }
        }

# 测试函数
def test_enhanced_monitor():
    """测试增强版监控系统"""
    print("🚀 增强版智能市场监控系统测试")
    print("=" * 60)
    
    monitor = EnhancedMarketMonitor()
    
    # 测试场景
    scenarios = [
        {
            'name': '正常市场',
            'data': {'current_price': 1.1300, 'rsi': 45, 'positions': []}
        },
        {
            'name': '价格剧烈变化',
            'data': {'current_price': 1.1400, 'rsi': 50, 'positions': []}  # 大幅上涨
        },
        {
            'name': 'RSI超买',
            'data': {'current_price': 1.1305, 'rsi': 85, 'positions': []}
        },
        {
            'name': '持仓风险',
            'data': {
                'current_price': 1.1299,
                'rsi': 50,
                'positions': [{'type': 'BUY', 'stop_loss': 1.1298}]
            }
        }
    ]
    
    for i, scenario in enumerate(scenarios):
        print(f"\n📊 场景 {i+1}: {scenario['name']}")
        
        # 先添加一些历史数据
        if i == 0:
            for j in range(10):
                monitor._update_cache({'current_price': 1.1300 + j * 0.0001})
        
        result = monitor.monitor(scenario['data'])
        
        print(f"   触发分析: {'是' if result['should_analyze'] else '否'}")
        print(f"   原因: {result['reason']}")
        print(f"   警报数量: {result['alerts_count']}")
        print(f"   最高紧急度: {result['max_urgency']}")
        print(f"   Token成本: {result['token_cost']}")
        
        if result.get('alerts_summary'):
            print("   主要警报:")
            for alert in result['alerts_summary']:
                print(f"     - {alert['level']}: {alert['message']} (紧急度: {alert['urgency']})")

if __name__ == "__main__":
    test_enhanced_monitor()
