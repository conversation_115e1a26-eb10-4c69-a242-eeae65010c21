# 外汇短线交易分析请求

**重要提示：我们采用短线交易策略，需要当天能够成交的订单，入场价格必须非常接近当前市场价格（最多相差10-15个点），止损止盈范围合理，目标是每天执行5-10笔交易。远离当前价格的入场点将被拒绝执行！**

## 基本信息
- 货币对: {{symbol}}
- 分析时间: {{analysis_time}}
- 当前价格: {{current_price}}
- 分析目的: 提供短线交易建议和市场分析

## 技术数据

### 15分钟图表数据
```
15分钟K线数据 (最近30根):
时间,开盘价,最高价,最低价,收盘价,成交量
{{timeframe15m_data}}
```

### 1小时图表数据
```
1小时K线数据 (最近30根):
时间,开盘价,最高价,最低价,收盘价,成交量
{{timeframe1h_data}}
```

### 技术指标
### 移动平均线
- MA5: {{ma5}}
- MA10: {{ma10}}
- MA20: {{ma20}}
- MA50: {{ma50}}

### RSI指标
- RSI(14): {{rsi}}

### MACD指标
- MACD线: {{macd_line}}
- 信号线: {{signal_line}}
- 柱状图: {{histogram}}

### 布林带
- 上轨: {{bollinger_upper}}
- 中轨: {{bollinger_middle}}
- 下轨: {{bollinger_lower}}

### 动量指标
- 动量(14): {{momentum}}

### 高级技术分析（15分钟周期）
{{advanced_analysis_15m}}

### 高级技术分析（1小时周期）
{{advanced_analysis_1h}}


## 基本面数据

### 最新相关新闻
**注意：请特别关注新闻的时间戳，只参考最近24小时内的新闻进行短线交易决策。过期新闻可能已经被市场消化，不应作为短线交易的主要依据。**
{{news_data}}

### 经济日历事件
**注意：请特别关注事件的时间戳，只参考最近24小时内已发布的经济数据和未来12小时内即将发布的重要数据。过期事件可能已经被市场消化，不应作为短线交易的主要依据。**
{{calendar_data}}

## 当前持仓情况
{{positions_data}}

## 当前挂单情况
{{pending_orders_data}}

{{history_analysis}}

## 系统状态与错误记录

### 最近错误记录
{{error_records}}

### 最近失败操作
{{failed_operations}}

## 交易绩效反馈

### 账户绩效
{{performance_data}}

### 订单结果统计
- 总订单数: {{total_orders}}
- 止盈触发率: {{take_profit_rate}}%
- 止损触发率: {{stop_loss_rate}}%
- 平均持仓时间: {{avg_duration_hours}}小时
- 平均风险回报比: {{avg_risk_reward_ratio}}

### 绩效奖励
{{performance_reward}}

## 分析要求

### 短线趋势交易策略要求
**重要：我们采用短线趋势交易策略，请遵循以下原则：**
1. **短线交易必须尊重市场趋势，避免逆势交易**：
   - 必须优先识别当前市场趋势（上升、下降或横盘）
   - 在趋势明确时，顺应趋势方向进行短线交易
   - 上升趋势中主要寻找买入机会，下降趋势中主要寻找卖出机会
   - 横盘整理中应保持谨慎，可以在支撑位附近买入或阻力位附近卖出
   - 严禁逆势交易，不要试图预测趋势反转点
   - 如果订单结果统计显示止损触发率高，这可能表明你在进行逆势交易

2. **入场价格设置必须考虑从分析到成交的时间差（非常重要）**：
   - **重要提示：分析时的技术指标和价格点位会随时间推移而变化，你必须预估成交时的市场状况**
   - 不要简单地基于当前价格设置入场点，而应该预测价格可能的移动方向和幅度
   - 对于限价单，考虑市场可能的短期波动，设置更合理的入场价格
   - 对于止损单，预估突破后的动能和速度，设置更合理的入场触发点
   - 当前价格是 {{current_price}}，但成交时价格很可能已经变化
   - 对于限价单，入场价格与当前价格的差距不得超过10-15个点
   - 对于止损单，入场价格与当前价格的差距不得超过15-20个点
   - 严禁设置远离当前价格的入场点，远离当前价格的订单将被系统拒绝执行

3. **止损和止盈设置（短线交易特点）**：
   - **止损止盈设置也必须考虑从分析到成交的时间差**：
     * 不要仅基于当前价格设置止损止盈，而应预估成交时的市场状况
     * 考虑价格可能的移动方向和关键技术位置的变化
     * 预留足够的空间应对市场波动，避免过早触发止损
   - 止损距离必须合理，不能太小，至少20-25个点
   - 止损总体范围应在当前价格的20-40个点之间
   - 止盈应设置在20-60个点之间，保持合理的风险回报比
   - 避免设置过大的止损止盈范围，因为我们做的是短线交易
   - 止损位必须设置在趋势支撑/阻力位之外，避免被正常的市场波动触发
   - 对于盈利持仓，应该使用移动止损策略保护利润，而不是过早平仓

4. **交易频率（短线交易特点）**：
   - 我们的目标是每天执行5-10笔交易，而不是等待2-3天才成交一笔
   - 积极寻找短期交易机会，不要过于保守
   - 即使市场波动较小，也要尝试找出可能的短线交易机会，但必须遵循趋势方向

### 分析框架
**🚨 持仓管理优先原则：在考虑新交易之前，必须首先评估和管理现有持仓和挂单！**

1. **首要任务：评估当前持仓和挂单情况（最高优先级）**
   - **如果有现有持仓，必须首先评估其当前状态和风险**
   - **如果有挂单，必须评估其是否仍然适合当前市场条件**
   - **持仓管理决策必须优先于新建仓位的决策**
   - 如果已有同方向持仓，请考虑是否需要继续加仓，避免过度集中风险
   - 如果已有反向持仓，请考虑是否应该先平仓再开新仓，或者保持观望
   - 如果已有挂单，请考虑是否需要修改或取消现有挂单
   - **重要：即使最近已经执行过交易，也请根据最新市场情况做出新的判断**
   - **不要因为最近已经有交易就自动选择观望，应该基于当前市场状况做出独立决策**
   - **对于盈利持仓的管理（非常重要）：**
     - 对于已经盈利的持仓，应该优先考虑保护利润而非过早平仓
     - 使用移动止损策略，随着价格向有利方向移动而调整止损位置
     - 只有在明确的反转信号出现时才考虑平仓，不要仅因小幅回调就平仓
     - 让利润奔跑，避免过早锁定小额利润而错过大行情
   - **对于挂单的风险管理（非常重要）：**
     - 确保挂单的止损距离合理，不应过小（至少20-25个点）
     - 止损位应设置在关键技术位置之外，避免被市场噪音触发
     - 避免频繁修改止损位置，特别是不要不断缩小止损范围
     - 如果市场条件变化导致原止损位置不再合适，考虑删除挂单而非缩小止损范围
   - **你拥有对所有订单的完全决策权，可以自由决定：**
     - 修改现有挂单的入场价格、止损或止盈
     - 删除不再适合当前市场情况的挂单
     - 平掉现有持仓，特别是当市场趋势发生明确变化时
     - 根据风险管理原则调整订单参数

2. **确定市场趋势和方向（短线交易的基础）**
   - 重点关注15分钟和1小时图表的最新走势
   - 明确识别当前的短期趋势方向（上升、下降或横盘）
   - 使用移动平均线确认趋势：
     * 价格在MA20上方且MA20向上倾斜 = 上升趋势
     * 价格在MA20下方且MA20向下倾斜 = 下降趋势
     * 价格在MA20附近波动且MA20平坦 = 横盘整理
   - 评估价格动能是否正在增强或减弱
   - 分析最近几根K线的形态和成交量变化
   - **短线交易必须顺应趋势方向，避免逆势交易**
   - **预测趋势的短期发展（非常重要）**：
     * 不要仅分析当前市场状况，还要预测未来1-2小时内的可能变化
     * 考虑当前技术指标可能的演变方向，而不仅是当前的静态值
     * 评估市场动能是否足以维持当前趋势，或是否有可能减弱或反转

3. **评估近期支撑和阻力水平（短线交易的关键）**
   - 重点关注短期内（未来1-2小时）可能触及的关键价格水平
   - 识别最近形成的局部高点和低点
   - 评估这些水平的强度和可能的突破情况
   - 考虑整数关口和心理价位的影响
   - 在上升趋势中，在支撑位附近寻找买入机会
   - 在下降趋势中，在阻力位附近寻找卖出机会
   - 在横盘整理中，可以在支撑位买入或阻力位卖出，但止损必须更严格

4. **综合分析技术指标信号（短线交易的触发条件）**
   - 重点关注短期指标的最新变化和交叉信号
   - 分析RSI是否显示超买或超卖状态
   - 评估MACD柱状图的方向变化和交叉信号
   - 分析价格与移动平均线的关系（是否突破、反弹或回调）
   - 考虑布林带的宽度和价格在带内的位置
   - 评估动量指标的方向和强度
   - **技术指标必须与趋势方向一致，作为短线交易的触发条件**

5. **结合基本面因素，评估短期影响**
   - **只关注最近24小时内的新闻和即将到来的事件**
   - 评估这些因素对短期价格走势的潜在影响
   - 考虑市场情绪和交易量的变化
   - 分析是否有重要的经济数据即将发布

6. **评估新交易机会（仅在持仓管理完成后考虑）**
   - 寻找符合短线交易策略的入场机会
   - 考虑技术指标的共振信号
   - 评估风险回报比和成功概率
   - 确定最佳的入场时机和价格水平
   - **只有在现有持仓风险可控的情况下才考虑新建仓位**

7. **考虑系统状态和错误记录**
   - 如果有最近的错误记录，请考虑这些错误可能对交易执行产生的影响
   - 如果某些操作频繁失败，请考虑避免或调整这些操作
   - 如果特定订单的操作失败，请考虑是否需要采取替代方案

8. **认真分析交易绩效反馈和奖惩机制（非常重要）**
   - 关注账户绩效指标，如胜率、盈亏比和总收益率
   - 考虑当前的绩效目标，努力实现或超越这些目标
   - 根据绩效反馈调整交易策略，改进不足之处
   - 注意连续亏损的风险，在必要时采取更保守的策略
   - **详细分析订单结果统计，这是评估你交易策略有效性的关键指标**：
     - 止盈触发率：{{take_profit_rate}}%
     - 止损触发率：{{stop_loss_rate}}%
     - **如果止盈触发率高（>50%），说明你的趋势判断正确，应该：**
       * 考虑设置更远的止盈目标，让利润奔跑
       * 使用移动止损策略保护已有利润，而不是过早平仓
       * 对于盈利持仓，避免过早平仓，应该让趋势充分发展
     - **如果止损触发率高（>30%），说明你可能在进行逆势交易，需要：**
       * 更严格地遵循趋势方向
       * 确保止损距离合理，不要设置过小的止损距离
       * 避免频繁修改止损位置，特别是不要不断缩小止损范围
     - **平均风险回报比：{{avg_risk_reward_ratio}}，应努力提高这一比率**
   - **绩效奖励信息是系统对你表现的评价，请认真考虑这些反馈**

9. **提供详细的分析逻辑（必须考虑时间差）**
   - 清晰说明你的分析思路和交易理由
   - 解释为什么当前市场条件适合或不适合交易
   - 说明你的入场、止损和止盈设置的依据
   - 如果建议观望，解释具体原因和可能的触发条件
   - **必须解释你如何考虑了从分析到成交的时间差**：
     * 说明你对未来1-2小时内市场可能走势的预测
     * 解释为什么你设置的入场价格、止损和止盈在成交时仍然有效
     * 分析当前技术指标可能的演变方向，而不仅是当前的静态值
     * 考虑价格可能的移动方向和关键技术位置的变化

10. **最后必须提供格式化的交易指令**，使用以下JSON格式（请确保JSON格式正确）：

交易指令示例：
```json
{
  "action": "BUY或SELL或NONE或CLOSE",
  "orderType": "MARKET或LIMIT或STOP",
  "entryPrice": 数值或null,
  "stopLoss": 数值,
  "takeProfit": 数值,
  "lotSize": 数值,
  "riskLevel": "LOW或MEDIUM或HIGH",
  "reasoning": "简短的交易理由",
  "positionManagement": "ADD或HOLD或REDUCE或CLOSE或null",
  "orderManagement": [
    {
      "action": "MODIFY或DELETE或CLOSE",
      "orderId": "订单ID",
      "newStopLoss": 数值,
      "newTakeProfit": 数值,
      "newEntryPrice": 数值或null,
      "reason": "操作原因"
    }
  ]
}
```

### 短线交易指令要求
- **入场价格设置（考虑时间差）**：
  - **必须考虑从分析到实际成交的时间差，预估成交时的市场状况**
  - **不要简单地使用当前的技术指标和价格点位，因为它们会随时间推移而变化**
  - **预测价格在未来1-2小时内的可能走势，并据此设置更合理的入场点**
  - 如果是市价单，entryPrice设为null
  - 如果是限价单，entryPrice必须考虑价格可能的短期波动方向
  - 如果是止损单，entryPrice必须考虑突破后的可能动能和速度
  - 例如：当前价格为{{current_price}}，买入限价单的entryPrice应设为{{buy_limit_low}}-{{buy_limit_high}}之间
  - 例如：当前价格为{{current_price}}，卖出限价单的entryPrice应设为{{sell_limit_low}}-{{sell_limit_high}}之间
  - 严禁设置远离当前价格的入场点，否则订单将被拒绝执行
  - **在分析中必须解释你如何考虑了时间差因素，以及为什么你认为设置的入场价格在成交时仍然有效**

- **止损和止盈设置（考虑时间差）**：
  - **必须为每个交易设置明确的止损和止盈价格，这是硬性要求**
  - **系统会拒绝执行没有止损止盈的交易指令**
  - **不要依赖系统的默认止损止盈值，它们只是在极少数情况下的应急措施**
  - **必须考虑从分析到实际成交的时间差**：
    * 不要仅基于当前的技术指标和价格点位设置止损止盈
    * 预测价格在未来1-2小时内的可能走势，并据此设置更合理的止损止盈位置
    * 考虑关键技术位置可能随时间推移而变化的情况
  - **止损距离必须合理，不能太小，至少20-25个点**：
    * 止损距离过小会导致正常市场波动就触发止损
    * 止损位必须设置在关键技术位置之外（如支撑/阻力位、近期高低点之外）
    * 避免设置"紧急止损"，这通常会导致不必要的亏损
  - 止损总体范围应在当前价格的20-40个点之间
  - 止盈必须设置在20-60个点之间
  - 例如：买入订单，当前价格为{{current_price}}，stopLoss应设为{{buy_sl_low}}-{{buy_sl_high}}之间，takeProfit应设为{{buy_tp_low}}-{{buy_tp_high}}之间
  - 例如：卖出订单，当前价格为{{current_price}}，stopLoss应设为{{sell_sl_low}}-{{sell_sl_high}}之间，takeProfit应设为{{sell_tp_low}}-{{sell_tp_high}}之间
  - 请确保风险回报比至少为1:1，理想情况下为1:1.5或更高
  - **止损止盈必须基于技术分析设置，而不是随机数值**
  - **在分析逻辑中必须解释止损止盈设置的依据，以及你如何考虑了时间差因素**
  - **对于已有订单的止损管理**：
    * 不要频繁缩小止损范围，这会增加被市场噪音触发的风险
    * 对于盈利订单，可以考虑移动止损到保本位置或轻微盈利位置
    * 如果市场条件发生重大变化，考虑平仓而不是将止损设置得过于接近当前价格

- **仓位大小设置**：
  - lotSize必须设置在0.01到0.2之间
  - 建议根据风险水平调整：HIGH风险使用0.01-0.05（高风险信号使用小仓位），MEDIUM风险使用0.05-0.1，LOW风险使用0.1-0.2（低风险信号使用大仓位）
  - 请注意：高风险交易应使用小仓位以控制风险，低风险交易可以使用大仓位以提高收益

### 其他注意事项
- 如果建议观望，action设为"NONE"
- 如果建议平仓，action设为"CLOSE"
- positionManagement字段用于管理现有持仓：
  - ADD: 建议增加持仓（加仓）
  - HOLD: 建议保持现有持仓不变
  - REDUCE: 建议减少部分持仓
  - CLOSE: 建议平掉全部持仓
  - null: 无持仓管理建议
- orderManagement字段是一个数组，用于管理现有订单：
  - MODIFY: 修改订单的止损、止盈或入场价格
  - DELETE: 删除挂单
  - CLOSE: 平掉持仓
  - 每个操作必须指定orderId
  - 修改订单时必须设置新的止损和止盈
  - 如果不需要管理订单，可以设为空数组[]或null
- 请确保JSON格式正确，这将被直接用于执行交易
- 请特别注意评估是否需要加仓，避免过度集中风险
- 你拥有对所有订单的完全决策权，可以根据市场情况自由管理订单

请提供详细、专业的分析，帮助我做出明智的交易决策。
