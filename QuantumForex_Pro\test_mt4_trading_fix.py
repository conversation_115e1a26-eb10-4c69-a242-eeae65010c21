#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MT4交易修复效果
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mt4_trading():
    """测试MT4交易功能"""
    print("🔍 测试MT4交易修复效果...")
    print("=" * 50)

    try:
        from utils.mt4_client import MT4Client

        # 创建MT4客户端
        mt4_client = MT4Client()
        print("✅ MT4客户端创建成功")

        # 连接MT4
        connected = mt4_client.connect()
        if not connected:
            print("❌ MT4连接失败")
            return False

        print("✅ MT4连接成功")

        # 测试小额交易
        symbol = 'EURUSD'
        lot = 0.01

        print(f"\n📤 测试交易: {symbol} {lot}手")

        # 执行买入
        buy_result = mt4_client.buy(
            symbol=symbol,
            lot=lot,
            sl=0,
            tp=0,
            comment='修复测试'
        )

        print(f"📋 买入结果: {buy_result}")

        if buy_result and buy_result.get('status') == 'success':
            order_id = buy_result.get('order_id')
            print(f"✅ 买入成功，订单ID: {order_id}")

            if order_id and order_id != 'UNKNOWN':
                print("✅ 成功获取真实订单ID")

                # 等待一下然后平仓
                import time
                time.sleep(3)

                print(f"📤 测试平仓: 订单{order_id}")
                close_result = mt4_client.close_order(order_id)
                print(f"📋 平仓结果: {close_result}")

                if close_result and close_result.get('status') == 'success':
                    print("✅ 平仓成功")
                    return True
                else:
                    print("❌ 平仓失败")
                    return False
            else:
                print("⚠️ 订单ID仍然是UNKNOWN")
                return False
        else:
            print("❌ 买入失败")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trade_executor():
    """测试交易执行引擎"""
    print("\n🔍 测试交易执行引擎...")
    print("=" * 50)

    try:
        from core.execution_engine.trade_executor import TradeExecutor

        # 创建交易执行器
        executor = TradeExecutor()
        print("✅ 交易执行器创建成功")

        # 创建测试交易决策
        test_decision = {
            'symbol': 'EURUSD',
            'action': 'enter_long',
            'volume': 0.01,
            'confidence': 0.75,
            'reasoning': '修复测试交易决策'
        }

        print(f"📋 测试交易决策: {test_decision}")

        # 执行交易决策
        print("📤 执行交易决策...")
        result = executor.execute_trading_decisions([test_decision])

        if result:
            print("✅ 交易决策执行成功")
            print(f"📊 执行结果: {result}")
            return True
        else:
            print("❌ 交易决策执行失败")
            return False

    except Exception as e:
        print(f"❌ 交易执行引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 QuantumForex Pro MT4交易修复测试")
    print("=" * 60)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 测试结果
    results = []

    # 1. 测试MT4交易功能
    results.append(test_mt4_trading())

    # 2. 测试交易执行引擎
    results.append(test_trade_executor())

    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print("=" * 60)

    passed = sum(results)
    total = len(results)

    print(f"✅ 通过测试: {passed}/{total}")
    print(f"❌ 失败测试: {total - passed}/{total}")

    if passed == total:
        print("\n🎉 所有测试通过！MT4交易问题已修复！")
        print("💡 现在系统应该能正常执行MT4交易")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")

    print("\n📋 修复说明:")
    print("   1. 改进了JSON响应截断处理")
    print("   2. 增强了订单ID提取逻辑")
    print("   3. 添加了从活跃订单获取真实ID的机制")
    print("   4. 改进了错误处理和重试机制")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
