#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试重复订单修复效果
验证新的订单管理逻辑是否能防止相似价格的重复订单
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'QuantumForex_Pro'))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_position_limits():
    """测试持仓限制"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 测试新的持仓限制...")
    
    try:
        from core.execution_engine.trade_executor import TradeExecutor
        
        # 创建交易执行器
        executor = TradeExecutor()
        
        # 模拟交易决策
        test_decisions = [
            {
                'symbol': 'EURUSD',
                'action': 'BUY',
                'volume': 0.02,
                'entry_price': 1.0850,
                'stop_loss': 1.0830,
                'take_profit': 1.0890,
                'confidence': 0.75
            },
            {
                'symbol': 'EURUSD',
                'action': 'BUY',
                'volume': 0.02,
                'entry_price': 1.0852,  # 相似价格
                'stop_loss': 1.0832,
                'take_profit': 1.0892,
                'confidence': 0.70
            },
            {
                'symbol': 'EURUSD',
                'action': 'SELL',
                'volume': 0.02,
                'entry_price': 1.0848,
                'stop_loss': 1.0868,
                'take_profit': 1.0808,
                'confidence': 0.65
            }
        ]
        
        logger.info("📊 新的持仓限制规则:")
        logger.info("   - 每个货币对最多2个持仓")
        logger.info("   - 同一方向最多1个持仓")
        logger.info("   - 价格相似性检查：20点阈值")
        logger.info("   - 全局最多10个持仓")
        logger.info("")
        
        # 测试每个决策
        for i, decision in enumerate(test_decisions, 1):
            logger.info(f"🎯 测试决策 {i}: {decision['symbol']} {decision['action']} @ {decision['entry_price']}")
            
            # 检查是否应该执行交易
            trade_check = executor._should_execute_trade(
                decision['symbol'], 
                decision['action'], 
                decision['volume']
            )
            
            logger.info(f"   结果: {trade_check['reason']}")
            
            if trade_check['should_execute']:
                logger.info("   ✅ 允许执行")
                # 模拟添加到持仓记录
                # 这里可以添加模拟订单到executor.active_orders
            else:
                logger.info("   ❌ 拒绝执行")
            
            logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试持仓限制失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_price_similarity_check():
    """测试价格相似性检查"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 测试价格相似性检查...")
    
    try:
        from core.execution_engine.trade_executor import TradeExecutor
        
        # 创建交易执行器
        executor = TradeExecutor()
        
        # 测试价格相似性检查
        test_cases = [
            {
                'symbol': 'EURUSD',
                'action': 'BUY',
                'volume': 0.02,
                'description': '正常价格检查'
            },
            {
                'symbol': 'GBPUSD',
                'action': 'SELL',
                'volume': 0.02,
                'description': '不同货币对检查'
            }
        ]
        
        for case in test_cases:
            logger.info(f"🔍 测试: {case['description']}")
            
            # 执行价格相似性检查
            result = executor._check_price_similarity(
                case['symbol'],
                case['action'],
                case['volume']
            )
            
            logger.info(f"   结果: {result['reason']}")
            logger.info(f"   允许: {'是' if result['allow'] else '否'}")
            logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试价格相似性检查失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_order_creation_logic():
    """测试订单创建逻辑"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 测试订单创建逻辑...")
    
    try:
        from core.execution_engine.trade_executor import TradeExecutor
        
        # 创建交易执行器
        executor = TradeExecutor()
        
        # 测试订单创建
        test_decision = {
            'symbol': 'EURUSD',
            'action': 'BUY',
            'volume': 0.02,
            'entry_price': 1.0850,
            'stop_loss': 1.0830,
            'take_profit': 1.0890,
            'confidence': 0.75
        }
        
        logger.info(f"📋 创建测试订单: {test_decision}")
        
        # 创建订单
        order = executor._create_trade_order(test_decision)
        
        if order:
            logger.info("✅ 订单创建成功")
            logger.info(f"   货币对: {order.symbol}")
            logger.info(f"   类型: {order.order_type.value}")
            logger.info(f"   数量: {order.volume}")
            logger.info(f"   入场价: {order.entry_price}")
            logger.info(f"   止损: {order.stop_loss}")
            logger.info(f"   止盈: {order.take_profit}")
            logger.info(f"   备注: {order.comment}")
        else:
            logger.warning("⚠️ 订单创建失败")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试订单创建失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 开始测试重复订单修复效果")
    logger.info("=" * 80)
    
    test_results = []
    
    # 1. 测试持仓限制
    logger.info("1️⃣ 测试新的持仓限制规则")
    test_results.append(("持仓限制", test_position_limits()))
    
    # 2. 测试价格相似性检查
    logger.info("2️⃣ 测试价格相似性检查")
    test_results.append(("价格相似性", test_price_similarity_check()))
    
    # 3. 测试订单创建逻辑
    logger.info("3️⃣ 测试订单创建逻辑")
    test_results.append(("订单创建", test_order_creation_logic()))
    
    # 总结测试结果
    logger.info("=" * 80)
    logger.info("📊 测试结果总结:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info("")
    logger.info(f"🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！重复订单问题已修复！")
        logger.info("")
        logger.info("🛡️ 新的保护机制:")
        logger.info("   ✅ 严格的持仓数量限制")
        logger.info("   ✅ 同方向单一持仓限制")
        logger.info("   ✅ 价格相似性检查（20点阈值）")
        logger.info("   ✅ 全局持仓数量控制")
        logger.info("")
        logger.info("📈 预期效果:")
        logger.info("   - 不再出现大量相似价格订单")
        logger.info("   - 每个货币对最多2个持仓")
        logger.info("   - 同方向只允许1个持仓")
        logger.info("   - 价格差异小于20点的订单被阻止")
    else:
        logger.warning(f"⚠️ {total - passed} 个测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
    input("按任意键退出...")
