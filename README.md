# 外汇交易系统 (Python版)

基于大语言模型的专业级自动化外汇交易系统，支持8个主要货币对的智能分析与交易。系统集成了先进的技术分析、基本面分析、风险管理和智能决策模块，能够实现24/7的市场监控和自动交易执行。

## 🎯 系统特点

### 核心功能
- **智能多货币对分析**：支持EURUSD、GBPUSD、AUDUSD、NZDUSD、USDCHF、USDCAD、USDJPY、GOLD等8个货币对
- **LLM优化分析引擎**：使用DeepSeek大语言模型进行深度市场分析，专门针对LLM分析时间优化
- **专业级技术分析**：包含趋势、动量、波动率、形态识别等全面技术指标
- **智能风险管理**：多层次风险控制，动态风险调整，严格的止损止盈机制
- **市场自适应策略**：基于市场条件自动选择最优交易策略（快速/标准/稳健）

### 高级特性
- **时间约束优化**：专门针对LLM 3-8分钟分析时间的策略优化
- **成本效益控制**：智能分析时机选择，避免无效的昂贵分析
- **相关性风险管理**：避免高相关性货币对同时交易
- **实时监控系统**：24/7市场监控，异常情况自动预警
- **智能MT4跳过**：周末自动检测并跳过MT4连接

## 📊 详细运行流程

系统运行包含6个主要阶段的完整流程，详细说明请参考：
**[📋 交易系统运行流程说明](./交易系统运行流程说明.md)**

### 简化流程概览
1. **系统初始化** → 检查连接状态，加载配置参数
2. **数据收集** → 智能货币对选择，获取市场数据和基本面信息
3. **策略优化** → LLM策略分析，市场条件评估，时机控制
4. **深度分析** → 多轮LLM分析，交易信号生成
5. **风险管理** → 风险评估，交易执行，指令确认
6. **监控反馈** → 实时监控，性能评估，学习优化

## 🎯 预期性能指标

### 收益率目标
- **保守目标**：月收益率4-7%，年收益率48-84%
- **理想目标**：月收益率7-12%，年收益率84-144%
- **风险控制**：最大回撤8-12%，夏普比率1.5-2.8

### 系统效率
- **LLM分析时间**：3-8分钟（针对性优化）
- **交易执行时间**：1-3秒
- **风险响应时间**：实时（<1秒）
- **系统可用性**：>99%

## 🏆 系统优势

1. **专业级量化交易水平**：达到中型量化基金的核心系统标准
2. **LLM驱动创新**：在LLM分析和智能货币对选择方面具有行业领先优势
3. **智能成本控制**：通过策略优化避免不必要的分析成本
4. **现实约束优化**：基于真实LLM分析时间的务实设计
5. **风险管理专业**：多层次风险控制体系，严格的风险参数

## 🔧 技术架构

### 核心组件
- **数据源**：MySQL数据库（pizza_quotes）+ 实时价格数据
- **分析引擎**：Python后端服务 + LLM优化模块
- **Web框架**：Flask + RESTful API
- **数据处理**：Pandas, NumPy + 高级技术分析模块
- **LLM服务**：DeepSeek API（R1模型主用，V3模型备用）
- **交易接口**：MT4客户端通过ZeroMQ通信 + 智能跳过模式

### 高级模块
- **智能货币对选择器**：`intelligent_pair_selector.py`
- **LLM优化交易策略**：`llm_optimized_trading_strategy.py`
- **多货币对数据管理器**：`multi_pair_data_manager.py`
- **市场自适应系统**：`market_adaptive_system.py`
- **风险管理系统**：`risk_management.py`
- **实时监控系统**：`real_time_monitor.py`

## 安装与配置

### 前提条件

- Python 3.8+
- MySQL数据库
- MT4客户端（可选，用于实际交易）
- ZeroMQ库（可选，用于与MT4通信）

### 安装步骤

1. 克隆仓库
```
git clone https://github.com/yourusername/forex-trading-system.git
cd forex-trading-system
```

2. 创建虚拟环境
```
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. 安装依赖
```
pip install -r requirements.txt
```

4. 配置环境变量
复制`.env.example`文件为`.env`，并根据实际情况修改配置：
```
# 服务器配置
PORT=5000

# 数据库配置
PIZZA_QUOTES_DB_HOST=your-db-host
PIZZA_QUOTES_DB_PORT=your-db-port
PIZZA_QUOTES_DB_USER=your-db-user
PIZZA_QUOTES_DB_PASSWORD=your-db-password
PIZZA_QUOTES_DB_NAME=your-db-name

# DeepSeek API配置
DEEPSEEK_API_KEY=your-deepseek-api-key

# MT4配置
MT4_SERVER_ADDRESS=tcp://127.0.0.1:5555
```

5. 启动服务器
```
python run.py
```

## API接口

### 数据接口

- `GET /api/forex-trading/eurusd/min-data` - 获取EURUSD分钟数据
- `GET /api/forex-trading/eurusd/klines` - 获取聚合K线数据
- `GET /api/forex-trading/eurusd/indicators` - 获取技术指标
- `GET /api/forex-trading/news` - 获取相关新闻
- `GET /api/forex-trading/calendar` - 获取相关日历事件
- `GET /api/forex-trading/positions` - 获取当前持仓
- `GET /api/forex-trading/market-info/<symbol>` - 获取市场信息

### 分析与交易接口

- `POST /api/forex-trading/analyze` - 执行外汇分析（遵循1小时频率限制）
- `POST /api/forex-trading/analyze-force` - 强制执行外汇分析（忽略频率限制）
- `POST /api/forex-trading/execute-trade` - 执行交易
- `POST /api/forex-trading/analyze-and-trade` - 分析并执行交易（遵循1小时频率限制）
- `POST /api/forex-trading/analyze-and-trade-force` - 强制分析并执行交易（忽略频率限制）

## MT4连接配置

要使用MT4交易功能，需要在MT4客户端中安装ZeroMQ插件，并配置相应的服务器地址。详细步骤请参考`docs/MT4连接指南.md`文件。

## 定时任务

系统会自动启动以下定时任务：

- 每小时执行一次外汇分析，更新分析结果和交易建议

## 模型备用方案

系统实现了LLM模型备用方案，确保分析服务的可靠性：

- **主要模型**：优先使用`Pro/deepseek-ai/DeepSeek-R1`模型
- **备用模型**：如果主要模型连续3次请求失败，自动切换到`Pro/deepseek-ai/DeepSeek-V3`模型
- **重试机制**：每个模型最多尝试3次，每次失败后增加延迟时间

## 交易指令解析

系统实现了强大的交易指令解析机制，确保准确理解LLM的分析结果：

- **多层次JSON识别**：识别多种格式的JSON交易指令
- **观望信号优先**：优先识别明确的观望信号
- **保守的信号解析**：只有在检测到多个一致的技术信号时才设置买入/卖出指令

## 📁 项目结构

```
ForexTradingSystem_Python/
├── app/
│   ├── __init__.py
│   ├── routes/                          # API路由
│   │   ├── forex_trading_routes.py      # 交易API
│   │   ├── monitoring_routes.py         # 监控API
│   │   └── update_routes.py             # 更新API
│   ├── services/                        # 核心服务
│   │   └── forex_trading_service.py     # 主要交易服务
│   ├── core/                           # 核心算法模块
│   │   ├── risk_management.py          # 风险管理
│   │   ├── market_adaptive_system.py   # 市场自适应
│   │   ├── portfolio_management_system.py # 组合管理
│   │   └── signal_quality_analyzer.py  # 信号质量分析
│   ├── utils/                          # 工具模块
│   │   ├── llm_optimized_trading_strategy.py    # LLM优化策略
│   │   ├── intelligent_pair_selector.py         # 智能货币对选择
│   │   ├── multi_pair_data_manager.py          # 多货币对数据管理
│   │   ├── llm_optimized_technical_analyzer.py # LLM优化技术分析
│   │   ├── real_time_monitor.py               # 实时监控
│   │   ├── llm_client.py                      # LLM客户端
│   │   ├── mt4_client.py                      # MT4客户端
│   │   ├── db_client.py                       # 数据库客户端
│   │   └── logger_manager.py                  # 日志管理
│   └── data/                           # 数据存储
│       ├── forex_analysis_history.json # 分析历史
│       ├── token_statistics.json       # Token统计
│       └── trade_results.json          # 交易结果
├── docs/                               # 文档目录
│   ├── MT4连接指南.md
│   ├── 系统开发文档.md
│   └── 交易指令生成规则.md
├── logs/                               # 日志目录
│   └── error_logs/                     # 错误日志
├── templates/                          # LLM提示词模板
├── 交易系统运行流程说明.md              # 系统运行流程说明
├── 诊断系统开发文档.md                  # 开发诊断文档
├── config.py                           # 配置文件
├── requirements.txt                    # 依赖包列表
├── run.py                             # 启动脚本
└── README.md                          # 项目说明
```

## 许可证

本项目采用MIT许可证。详情请参阅LICENSE文件。
