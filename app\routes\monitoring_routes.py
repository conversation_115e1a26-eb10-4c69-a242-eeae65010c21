#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控面板API路由
提供实时监控数据的REST API接口
"""

from flask import Blueprint, jsonify, request, render_template
from datetime import datetime, timedelta
from app.utils.real_time_monitor import real_time_monitor
import json

# 创建监控蓝图
monitoring_bp = Blueprint('monitoring', __name__, url_prefix='/monitoring')

@monitoring_bp.route('/')
def dashboard():
    """监控面板主页"""
    return render_template('monitoring_dashboard.html')

@monitoring_bp.route('/api/status')
def get_current_status():
    """获取当前系统状态"""
    try:
        status = real_time_monitor.get_current_status()
        return jsonify({
            'success': True,
            'data': status,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/dashboard')
def get_dashboard_data():
    """获取完整的仪表板数据"""
    try:
        dashboard_data = real_time_monitor.get_dashboard_data()
        return jsonify({
            'success': True,
            'data': dashboard_data,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/metrics/<metric_type>')
def get_metrics(metric_type):
    """获取特定类型的指标数据"""
    try:
        hours = request.args.get('hours', 24, type=int)

        if metric_type not in ['system', 'trading', 'analysis', 'alerts']:
            return jsonify({
                'success': False,
                'error': f'不支持的指标类型: {metric_type}'
            }), 400

        data = real_time_monitor.get_historical_data(metric_type, hours)
        return jsonify({
            'success': True,
            'data': data,
            'metric_type': metric_type,
            'hours': hours,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/alerts')
def get_alerts():
    """获取告警信息"""
    try:
        hours = request.args.get('hours', 1, type=int)
        level = request.args.get('level', None)
        category = request.args.get('category', None)

        alerts = real_time_monitor.get_historical_data('alerts', hours)

        # 过滤告警
        if level:
            alerts = [alert for alert in alerts if alert['level'] == level]
        if category:
            alerts = [alert for alert in alerts if alert['category'] == category]

        return jsonify({
            'success': True,
            'data': alerts,
            'filters': {
                'hours': hours,
                'level': level,
                'category': category
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/statistics')
def get_statistics():
    """获取统计信息"""
    try:
        # 获取最近24小时的统计数据
        system_metrics = real_time_monitor.get_historical_data('system', 24)
        trading_metrics = real_time_monitor.get_historical_data('trading', 24)
        analysis_metrics = real_time_monitor.get_historical_data('analysis', 24)
        alerts = real_time_monitor.get_historical_data('alerts', 24)

        # 计算统计信息
        stats = {
            'system': {
                'avg_cpu': sum(m['cpu_percent'] for m in system_metrics) / len(system_metrics) if system_metrics else 0,
                'avg_memory': sum(m['memory_percent'] for m in system_metrics) / len(system_metrics) if system_metrics else 0,
                'max_cpu': max((m['cpu_percent'] for m in system_metrics), default=0),
                'max_memory': max((m['memory_percent'] for m in system_metrics), default=0),
                'status_distribution': {}
            },
            'trading': {
                'total_trades': trading_metrics[-1]['total_trades'] if trading_metrics else 0,
                'win_rate': trading_metrics[-1]['win_rate'] if trading_metrics else 0,
                'net_profit': trading_metrics[-1]['net_profit'] if trading_metrics else 0,
                'max_drawdown': trading_metrics[-1]['max_drawdown'] if trading_metrics else 0,
                'profit_factor': trading_metrics[-1]['profit_factor'] if trading_metrics else 0
            },
            'analysis': {
                'total_analyses': analysis_metrics[-1]['total_analyses'] if analysis_metrics else 0,
                'success_rate': analysis_metrics[-1]['success_rate'] if analysis_metrics else 0,
                'avg_response_time': analysis_metrics[-1]['avg_response_time'] if analysis_metrics else 0,
                'total_cost': analysis_metrics[-1]['total_cost'] if analysis_metrics else 0,
                'json_parse_errors': analysis_metrics[-1]['json_parse_errors'] if analysis_metrics else 0
            },
            'alerts': {
                'total_alerts': len(alerts),
                'critical_alerts': len([a for a in alerts if a['level'] == 'critical']),
                'warning_alerts': len([a for a in alerts if a['level'] == 'warning']),
                'error_alerts': len([a for a in alerts if a['level'] == 'error']),
                'info_alerts': len([a for a in alerts if a['level'] == 'info'])
            }
        }

        # 计算系统状态分布
        status_counts = {}
        for metric in system_metrics:
            status = metric['system_status']
            status_counts[status] = status_counts.get(status, 0) + 1
        stats['system']['status_distribution'] = status_counts

        return jsonify({
            'success': True,
            'data': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/control/start')
def start_monitoring():
    """启动监控"""
    try:
        interval = request.args.get('interval', 60, type=int)
        real_time_monitor.start_monitoring(interval)
        return jsonify({
            'success': True,
            'message': f'监控已启动，间隔: {interval}秒',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/control/stop')
def stop_monitoring():
    """停止监控"""
    try:
        real_time_monitor.stop_monitoring()
        return jsonify({
            'success': True,
            'message': '监控已停止',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/risk-management')
def get_risk_management_status():
    """获取风险管理状态"""
    try:
        # 获取风险管理状态
        risk_status = real_time_monitor.get_risk_management_status()
        return jsonify({
            'success': True,
            'data': risk_status,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/strategy-optimization')
def get_strategy_optimization_status():
    """获取策略优化状态"""
    try:
        # 获取策略优化状态
        strategy_status = real_time_monitor.get_strategy_optimization_status()
        return jsonify({
            'success': True,
            'data': strategy_status,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/performance-metrics')
def get_performance_metrics():
    """获取性能评估指标"""
    try:
        # 获取性能评估数据
        performance_metrics = real_time_monitor.get_performance_metrics()
        return jsonify({
            'success': True,
            'data': performance_metrics,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/market-status')
def get_market_status():
    """获取市场状态"""
    try:
        from app.utils.market_time_checker import is_market_open, get_market_status, format_time_until_open

        is_open = is_market_open()
        status = get_market_status()
        time_until_open = format_time_until_open()

        return jsonify({
            'success': True,
            'data': {
                'is_open': is_open,
                'status': status,
                'time_until_open': time_until_open,
                'utc_time': datetime.utcnow().isoformat(),
                'local_time': datetime.now().isoformat()
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/api/test/alert')
def test_alert():
    """测试告警功能"""
    try:
        level = request.args.get('level', 'info')
        category = request.args.get('category', 'test')
        message = request.args.get('message', '这是一个测试告警')

        real_time_monitor.add_alert(level, category, message)
        return jsonify({
            'success': True,
            'message': '测试告警已发送',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

# 错误处理
@monitoring_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': '接口不存在',
        'timestamp': datetime.now().isoformat()
    }), 404

@monitoring_bp.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': '服务器内部错误',
        'timestamp': datetime.now().isoformat()
    }), 500
