"""
交易结果记录器
用于记录交易结果，包括开仓、修改、平仓等操作，以及盈亏情况
"""
import os
import json
from datetime import datetime
import uuid

# 交易结果文件路径
TRADE_RESULTS_FILE_PATH = os.path.join(os.path.dirname(__file__), '../data/trade_results.json')

def ensure_directory_exists():
    """确保目录存在"""
    directory = os.path.dirname(TRADE_RESULTS_FILE_PATH)
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)

def load_trade_results():
    """
    加载交易结果记录

    Returns:
        list: 交易结果记录列表
    """
    try:
        ensure_directory_exists()
        if not os.path.exists(TRADE_RESULTS_FILE_PATH):
            return []

        with open(TRADE_RESULTS_FILE_PATH, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f'加载交易结果记录失败: {e}')
        return []

def save_trade_results(results):
    """
    保存交易结果记录

    Args:
        results (list): 交易结果记录列表
    """
    try:
        ensure_directory_exists()
        with open(TRADE_RESULTS_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f'保存交易结果记录失败: {e}')

def record_trade_open(order_id, symbol, action, order_type, entry_price, stop_loss, take_profit, lot_size, analysis_id=None):
    """
    记录开仓操作

    Args:
        order_id (str): 订单ID
        symbol (str): 交易品种
        action (str): 交易方向（BUY/SELL）
        order_type (str): 订单类型（MARKET/LIMIT/STOP）
        entry_price (float): 入场价格
        stop_loss (float): 止损价格
        take_profit (float): 止盈价格
        lot_size (float): 仓位大小
        analysis_id (str, optional): 关联的分析ID
    """
    try:
        results = load_trade_results()

        # 生成唯一的交易ID
        trade_id = str(uuid.uuid4())

        # 创建交易记录
        trade_record = {
            'trade_id': trade_id,
            'order_id': order_id,
            'symbol': symbol,
            'action': action,
            'order_type': order_type,
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'lot_size': lot_size,
            'open_time': datetime.now().isoformat(),
            'close_time': None,
            'close_price': None,
            'profit_loss': None,
            'status': 'OPEN',
            'close_reason': None,
            'analysis_id': analysis_id,
            'history': [
                {
                    'time': datetime.now().isoformat(),
                    'action': 'OPEN',
                    'details': {
                        'entry_price': entry_price,
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'lot_size': lot_size
                    }
                }
            ]
        }

        # 添加到记录列表
        results.append(trade_record)

        # 保存记录
        save_trade_results(results)

        print(f'记录开仓操作成功: 订单ID={order_id}, 交易ID={trade_id}')
        return trade_id
    except Exception as e:
        print(f'记录开仓操作失败: {e}')
        return None

def record_trade_modify(order_id, new_stop_loss=None, new_take_profit=None):
    """
    记录修改订单操作

    Args:
        order_id (str): 订单ID
        new_stop_loss (float, optional): 新的止损价格
        new_take_profit (float, optional): 新的止盈价格
    """
    try:
        results = load_trade_results()

        # 查找对应的交易记录
        for trade in results:
            if trade['order_id'] == order_id and trade['status'] == 'OPEN':
                # 更新止损止盈
                if new_stop_loss is not None:
                    trade['stop_loss'] = new_stop_loss
                if new_take_profit is not None:
                    trade['take_profit'] = new_take_profit

                # 添加历史记录
                trade['history'].append({
                    'time': datetime.now().isoformat(),
                    'action': 'MODIFY',
                    'details': {
                        'new_stop_loss': new_stop_loss,
                        'new_take_profit': new_take_profit
                    }
                })

                # 保存记录
                save_trade_results(results)
                print(f'记录修改订单操作成功: 订单ID={order_id}')
                return True

        print(f'未找到对应的开仓交易记录: 订单ID={order_id}')
        return False
    except Exception as e:
        print(f'记录修改订单操作失败: {e}')
        return False

def record_trade_close(order_id, close_price, profit_loss, close_reason='MANUAL'):
    """
    记录平仓操作

    Args:
        order_id (str): 订单ID
        close_price (float): 平仓价格
        profit_loss (float): 盈亏金额
        close_reason (str, optional): 平仓原因（MANUAL/STOP_LOSS/TAKE_PROFIT）
    """
    try:
        results = load_trade_results()

        # 查找对应的交易记录
        for trade in results:
            if trade['order_id'] == order_id and trade['status'] == 'OPEN':
                # 更新平仓信息
                trade['close_time'] = datetime.now().isoformat()
                trade['close_price'] = close_price
                trade['profit_loss'] = profit_loss
                trade['status'] = 'CLOSED'
                trade['close_reason'] = close_reason

                # 添加历史记录
                trade['history'].append({
                    'time': datetime.now().isoformat(),
                    'action': 'CLOSE',
                    'details': {
                        'close_price': close_price,
                        'profit_loss': profit_loss,
                        'close_reason': close_reason
                    }
                })

                # 保存记录
                save_trade_results(results)
                print(f'记录平仓操作成功: 订单ID={order_id}, 盈亏={profit_loss}')
                return True

        print(f'未找到对应的开仓交易记录: 订单ID={order_id}')
        return False
    except Exception as e:
        print(f'记录平仓操作失败: {e}')
        return False

def get_recent_trade_results(limit=5):
    """
    获取最近的交易结果

    Args:
        limit (int): 获取的记录数量

    Returns:
        list: 交易结果记录列表
    """
    try:
        results = load_trade_results()

        # 按照开仓时间倒序排序
        results.sort(key=lambda x: x.get('open_time', ''), reverse=True)

        return results[:limit]
    except Exception as e:
        print(f'获取最近交易结果失败: {e}')
        return []

def format_trade_results_for_prompt(limit=5):
    """
    格式化交易结果为提示词 - 增强版，突出交易结果的重要性

    Args:
        limit (int): 获取的记录数量

    Returns:
        str: 格式化后的交易结果
    """
    try:
        results = get_recent_trade_results(limit)

        if not results:
            return "无交易记录"

        formatted = "### 最近交易结果\n\n"
        formatted += "**重要提示：请仔细分析以下交易结果，从中吸取经验教训，保持交易策略的连续性和一致性。**\n\n"

        # 统计成功和失败的交易
        successful_trades = [t for t in results if t['status'] == 'CLOSED' and t.get('profit_loss', 0) > 0]
        failed_trades = [t for t in results if t['status'] == 'CLOSED' and t.get('profit_loss', 0) <= 0]
        open_trades = [t for t in results if t['status'] != 'CLOSED']

        # 添加交易结果摘要
        formatted += "#### 交易结果摘要\n"
        formatted += f"- 总交易数: {len(results)}\n"
        formatted += f"- 成功交易: {len(successful_trades)} ({len(successful_trades)/len(results)*100:.1f}% 如果有交易)\n"
        formatted += f"- 失败交易: {len(failed_trades)} ({len(failed_trades)/len(results)*100:.1f}% 如果有交易)\n"
        formatted += f"- 当前持仓: {len(open_trades)}\n\n"

        # 添加交易策略有效性评估
        if successful_trades or failed_trades:
            formatted += "#### 交易策略有效性评估\n"

            # 计算平均盈利和亏损
            avg_profit = sum([t.get('profit_loss', 0) for t in successful_trades]) / len(successful_trades) if successful_trades else 0
            avg_loss = sum([t.get('profit_loss', 0) for t in failed_trades]) / len(failed_trades) if failed_trades else 0

            formatted += f"- 平均盈利: {avg_profit:.2f}\n"
            formatted += f"- 平均亏损: {avg_loss:.2f}\n"

            # 计算平均持仓时间
            all_closed_trades = successful_trades + failed_trades
            if all_closed_trades:
                holding_times = []
                for trade in all_closed_trades:
                    open_dt = datetime.fromisoformat(trade['open_time'].replace('Z', '+00:00'))
                    close_dt = datetime.fromisoformat(trade['close_time'].replace('Z', '+00:00'))
                    duration = close_dt - open_dt
                    hours = duration.total_seconds() / 3600
                    holding_times.append(hours)

                avg_holding_time = sum(holding_times) / len(holding_times)
                formatted += f"- 平均持仓时间: {avg_holding_time:.2f}小时\n"

            # 添加策略建议
            formatted += "\n**策略建议：**\n"
            if len(successful_trades) > len(failed_trades):
                formatted += "- 当前交易策略整体表现良好，建议保持策略的连续性和一致性\n"
            else:
                formatted += "- 当前交易策略需要改进，但应避免频繁大幅度改变策略方向\n"

            if avg_profit > abs(avg_loss):
                formatted += "- 盈利交易的平均收益大于亏损交易的平均损失，说明风险回报比较好\n"
            else:
                formatted += "- 需要提高盈利交易的平均收益或减少亏损交易的平均损失\n"

            formatted += "\n"

        # 详细交易记录
        formatted += "#### 详细交易记录\n\n"

        for i, trade in enumerate(results):
            # 基本信息
            open_time = datetime.fromisoformat(trade['open_time'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            close_time = "尚未平仓" if trade['close_time'] is None else datetime.fromisoformat(trade['close_time'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')

            formatted += f"##### {i+1}. {trade['symbol']} {trade['action']} 订单\n"
            formatted += f"- 开仓时间: {open_time}\n"
            formatted += f"- 入场价格: {trade['entry_price']}\n"
            formatted += f"- 仓位大小: {trade['lot_size']}\n"
            formatted += f"- 止损价格: {trade['stop_loss']}\n"
            formatted += f"- 止盈价格: {trade['take_profit']}\n"

            # 计算风险回报比
            if trade['stop_loss'] and trade['take_profit'] and trade['entry_price']:
                if trade['action'] == 'BUY':
                    risk = abs(trade['entry_price'] - trade['stop_loss'])
                    reward = abs(trade['take_profit'] - trade['entry_price'])
                else:  # SELL
                    risk = abs(trade['stop_loss'] - trade['entry_price'])
                    reward = abs(trade['entry_price'] - trade['take_profit'])

                if risk > 0:
                    risk_reward_ratio = reward / risk
                    formatted += f"- 风险回报比: 1:{risk_reward_ratio:.2f}\n"

            # 平仓信息
            if trade['status'] == 'CLOSED':
                formatted += f"- 平仓时间: {close_time}\n"
                formatted += f"- 平仓价格: {trade['close_price']}\n"
                formatted += f"- 盈亏金额: {trade['profit_loss']}\n"
                formatted += f"- 平仓原因: {trade['close_reason']}\n"

                # 计算持仓时间
                open_dt = datetime.fromisoformat(trade['open_time'].replace('Z', '+00:00'))
                close_dt = datetime.fromisoformat(trade['close_time'].replace('Z', '+00:00'))
                duration = close_dt - open_dt
                hours = duration.total_seconds() / 3600
                formatted += f"- 持仓时间: {hours:.2f}小时\n"

                # 交易结果评估
                if trade['profit_loss'] > 0:
                    formatted += f"- 交易结果: 盈利 ✅\n"

                    # 添加成功经验
                    formatted += "- 成功经验: "
                    if trade['close_reason'] == 'TAKE_PROFIT':
                        formatted += "止盈被触发，说明目标价位设置合理，可以继续采用类似的目标价位设置策略。\n"
                    elif trade['close_reason'] == 'MANUAL':
                        formatted += "手动平仓获利，说明交易决策和时机把握较好。\n"
                    else:
                        formatted += "交易获利，继续保持良好的交易策略。\n"
                else:
                    formatted += f"- 交易结果: 亏损 ❌\n"

                    # 添加失败教训
                    formatted += "- 失败教训: "
                    if trade['close_reason'] == 'STOP_LOSS':
                        formatted += "止损被触发，需要评估止损位置是否合理，或者入场时机是否恰当。\n"
                    elif trade['close_reason'] == 'MANUAL':
                        formatted += "手动平仓亏损，需要评估交易决策和时机把握。\n"
                    else:
                        formatted += "交易亏损，需要从中吸取教训，改进交易策略。\n"
            else:
                formatted += f"- 状态: 持仓中\n"

                # 添加持仓建议
                formatted += "- 持仓建议: 继续监控此持仓，评估是否需要调整止损止盈或平仓。\n"

            # 添加分析ID关联
            if 'analysis_id' in trade and trade['analysis_id']:
                formatted += f"- 关联分析ID: {trade['analysis_id']}\n"

            formatted += "\n"

        # 添加总结和建议
        formatted += "#### 交易结果总结与建议\n\n"
        formatted += "请基于以上交易结果，评估当前交易策略的有效性，并在保持策略连续性和一致性的前提下，提出改进建议。\n"
        formatted += "避免频繁大幅度改变交易策略，而应该在现有策略框架内进行微调和优化。\n"

        return formatted
    except Exception as e:
        print(f'格式化交易结果失败: {e}')
        return "获取交易记录失败"
