#!/usr/bin/env python3
"""
测试新的兼容模型
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_models():
    print("🧪 测试新的兼容模型")
    print("="*40)
    
    try:
        # 导入ML引擎
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine, ModelType
        
        print("🤖 初始化ML引擎...")
        ml_engine = LightweightMLEngine()
        
        # 检查模型状态
        print(f"\n📊 模型状态:")
        compatible_models = 0
        for model_type, model in ml_engine.models.items():
            if model is not None:
                model_name = type(model).__name__
                performance = ml_engine.model_performance.get(model_type, 0.5)
                
                if performance >= 0.9:
                    compatible_models += 1
                    status = "🎯 兼容模型"
                elif performance >= 0.8:
                    status = "🤖 Trainer高级模型"
                else:
                    status = "📊 标准模型"
                
                print(f"   {model_type.value}: {status} (性能: {performance:.3f})")
        
        print(f"\n✅ 加载了{len(ml_engine.models)}个模型，其中{compatible_models}个是兼容模型")
        
        # 创建测试数据
        print(f"\n🔮 测试模型预测能力...")
        
        # 创建测试市场数据
        test_data = pd.DataFrame({
            'close': [1.0850, 1.0852, 1.0848, 1.0855, 1.0860, 1.0858],
            'high': [1.0855, 1.0857, 1.0853, 1.0860, 1.0865, 1.0863],
            'low': [1.0845, 1.0847, 1.0843, 1.0850, 1.0855, 1.0853],
            'open': [1.0848, 1.0850, 1.0850, 1.0848, 1.0855, 1.0860],
            'volume': [2500, 2600, 2400, 2700, 2800, 2650]
        })
        
        # 创建技术指标
        test_indicators = {
            'trend_analysis': {'trend_score': 0.6, 'trend_strength': 0.7},
            'momentum_analysis': {'momentum_score': 0.5, 'momentum_divergence': 0.2},
            'volatility_analysis': {'volatility_score': 0.4, 'volatility_regime': 0.3}
        }
        
        # 获取预测
        predictions = ml_engine.generate_predictions(test_data, test_indicators)
        
        if predictions:
            print("✅ 模型预测成功！")
            
            # 显示预测结果
            for model_type, prediction in predictions.items():
                print(f"\n📈 {model_type}:")
                print(f"   预测值: {prediction.prediction:.6f}")
                print(f"   置信度: {prediction.confidence:.3f}")
                print(f"   模型准确性: {prediction.model_accuracy:.3f}")
                
                # 检查预测质量
                if abs(prediction.prediction) > 0.000001:
                    print(f"   ✅ 预测有效")
                else:
                    print(f"   ❌ 预测无效")
        else:
            print("❌ 模型预测失败")
            return False
        
        # 评估预测质量
        valid_predictions = 0
        for model_type, prediction in predictions.items():
            if abs(prediction.prediction) > 0.000001 and prediction.confidence > 0.1:
                valid_predictions += 1
        
        print(f"\n🎯 预测质量评估:")
        print(f"   有效预测: {valid_predictions}/{len(predictions)}")
        print(f"   预测质量: {valid_predictions/len(predictions):.1%}")
        
        if valid_predictions >= len(predictions) * 0.75:
            print("✅ 模型预测质量良好")
            return True
        elif valid_predictions >= len(predictions) * 0.5:
            print("⚠️ 模型预测质量一般")
            return True
        else:
            print("❌ 模型预测质量较差")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_new_models()
    
    if success:
        print("\n🎉 新模型测试成功！")
        print("💡 兼容模型工作正常")
    else:
        print("\n❌ 新模型测试失败！")
        print("💡 需要检查模型兼容性")
