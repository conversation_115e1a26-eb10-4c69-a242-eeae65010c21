#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Pro端数据收集器
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_pro_data_collector():
    """测试Pro端数据收集器"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🧪 开始测试Pro端数据收集器...")
    
    try:
        from data_collector.pro_data_collector import pro_data_collector
        
        # 测试收集所有数据
        result = pro_data_collector.collect_all_data(days=1)
        
        logger.info("✅ 数据收集完成！")
        logger.info("📊 收集结果摘要:")
        logger.info(f"  - 交易记录: {len(result.get('trade_records', []))}条")
        logger.info(f"  - 参数优化: {len(result.get('parameter_optimizations', []))}条")
        logger.info(f"  - LLM分析: {len(result.get('llm_analyses', []))}条")
        
        # 显示一些示例数据
        if result.get('trade_records'):
            logger.info("📈 交易记录示例:")
            trade = result['trade_records'][0]
            logger.info(f"  - 交易ID: {trade.get('trade_id')}")
            logger.info(f"  - 货币对: {trade.get('symbol')}")
            logger.info(f"  - 操作: {trade.get('action')}")
            logger.info(f"  - 盈亏: {trade.get('profit_loss'):.2f}")
        
        if result.get('parameter_optimizations'):
            logger.info("⚙️ 参数优化示例:")
            opt = result['parameter_optimizations'][0]
            logger.info(f"  - 参数: {opt.get('parameter_name')}")
            logger.info(f"  - 旧值: {opt.get('old_value'):.3f}")
            logger.info(f"  - 新值: {opt.get('new_value'):.3f}")
            logger.info(f"  - 改进分数: {opt.get('improvement_score'):.3f}")
        
        if result.get('llm_analyses'):
            logger.info("🤖 LLM分析示例:")
            analysis = result['llm_analyses'][0]
            logger.info(f"  - 分析类型: {analysis.get('analysis_type')}")
            logger.info(f"  - 风险评分: {analysis.get('risk_score'):.2f}")
            logger.info(f"  - 建议数量: {len(analysis.get('recommendations', []))}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_pro_data_collector()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    input("按任意键退出...")
