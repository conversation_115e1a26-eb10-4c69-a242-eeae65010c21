"""
测试MT4连接失败后的系统恢复能力
"""
import time
import sys
import threading
from datetime import datetime, timedelta

from app.utils.forex_scheduled_tasks import start_realtime_forex_analysis, tasks, stop_all_tasks
from app.utils.mt4_client import mt4_client

def disconnect_mt4():
    """断开MT4连接"""
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 手动断开MT4连接')
    mt4_client.disconnect()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4连接已断开')

def reconnect_mt4():
    """重新连接MT4"""
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 手动重新连接MT4')
    connected = mt4_client.connect()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4重新连接结果: {connected}')

if __name__ == "__main__":
    print("开始测试MT4连接失败后的系统恢复能力...")
    print("当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 确保输出立即显示
    sys.stdout.flush()
    
    # 启动实时外汇分析任务，不立即执行，这样就会启动预分析线程
    print("启动实时外汇分析任务...")
    start_realtime_forex_analysis(run_immediately=False, auto_trade=False, check_interval=10)
    
    print("实时外汇分析任务已启动，主线程等待30秒...")
    print("任务状态:", tasks)
    
    # 确保输出立即显示
    sys.stdout.flush()
    
    # 等待30秒，让预分析线程启动并执行一次
    time.sleep(30)
    print(f"已等待30秒，任务状态: detector_running={tasks['realtime_analysis']['detector_running']}, running={tasks['realtime_analysis']['running']}")
    
    # 断开MT4连接
    disconnect_mt4()
    
    # 等待60秒，观察预分析线程的行为
    print("断开MT4连接后，等待60秒观察预分析线程行为...")
    for i in range(2):
        time.sleep(30)
        print(f"已等待 {(i+1)*30} 秒，任务状态: detector_running={tasks['realtime_analysis']['detector_running']}, running={tasks['realtime_analysis']['running']}")
        sys.stdout.flush()
    
    # 重新连接MT4
    reconnect_mt4()
    
    # 再等待60秒，观察预分析线程的行为
    print("重新连接MT4后，等待60秒观察预分析线程行为...")
    for i in range(2):
        time.sleep(30)
        print(f"已等待 {(i+1)*30} 秒，任务状态: detector_running={tasks['realtime_analysis']['detector_running']}, running={tasks['realtime_analysis']['running']}")
        sys.stdout.flush()
    
    # 停止所有任务
    print("停止所有任务...")
    stop_all_tasks()
    
    print("测试完成，退出程序")
