#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级交易系统 - 简化版（不依赖TA-Lib）
基于现代量化交易理念，融合多维度信号，实现自适应交易决策
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class MarketPhase(Enum):
    """市场阶段"""
    ACCUMULATION = "吸筹阶段"      # 底部整理，机构建仓
    MARKUP = "上升阶段"           # 明确上涨趋势
    DISTRIBUTION = "派发阶段"      # 顶部整理，机构出货
    MARKDOWN = "下跌阶段"         # 明确下跌趋势
    CONSOLIDATION = "整理阶段"     # 横盘震荡
    BREAKOUT = "突破阶段"         # 关键位置突破

class SignalStrength(Enum):
    """信号强度"""
    VERY_STRONG = 5
    STRONG = 4
    MODERATE = 3
    WEAK = 2
    VERY_WEAK = 1

@dataclass
class TradingDecision:
    """交易决策"""
    action: str                    # BUY, SELL, NONE
    confidence: float              # 0-1
    entry_price: Optional[float]   # 入场价格
    stop_loss: float              # 止损价格
    take_profit: float            # 止盈价格
    position_size: float          # 仓位大小
    strategy_used: str            # 使用的策略
    reasoning: str                # 决策理由
    risk_level: str               # 风险等级
    market_phase: MarketPhase     # 市场阶段

class TechnicalIndicators:
    """技术指标计算类"""
    
    @staticmethod
    def sma(data: np.ndarray, period: int) -> np.ndarray:
        """简单移动平均"""
        return pd.Series(data).rolling(window=period).mean().values
    
    @staticmethod
    def ema(data: np.ndarray, period: int) -> np.ndarray:
        """指数移动平均"""
        return pd.Series(data).ewm(span=period).mean().values
    
    @staticmethod
    def rsi(data: np.ndarray, period: int = 14) -> np.ndarray:
        """相对强弱指数"""
        delta = np.diff(data)
        gain = np.where(delta > 0, delta, 0)
        loss = np.where(delta < 0, -delta, 0)
        
        avg_gain = pd.Series(gain).rolling(window=period).mean()
        avg_loss = pd.Series(loss).rolling(window=period).mean()
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        # 在开头添加NaN以匹配原始数据长度
        result = np.full(len(data), np.nan)
        result[period:] = rsi[period-1:]
        return result
    
    @staticmethod
    def macd(data: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """MACD指标"""
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    @staticmethod
    def bollinger_bands(data: np.ndarray, period: int = 20, std_dev: float = 2) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """布林带"""
        sma = TechnicalIndicators.sma(data, period)
        std = pd.Series(data).rolling(window=period).std().values
        
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        
        return upper, sma, lower
    
    @staticmethod
    def atr(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """平均真实波幅"""
        high_low = high - low
        high_close = np.abs(high - np.roll(close, 1))
        low_close = np.abs(low - np.roll(close, 1))
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = pd.Series(true_range).rolling(window=period).mean().values
        
        return atr

class AdvancedTradingSystem:
    """高级交易系统"""
    
    def __init__(self):
        self.indicators = TechnicalIndicators()
        
        # 策略权重
        self.strategy_weights = {
            'trend_following': 0.35,
            'mean_reversion': 0.25,
            'momentum': 0.20,
            'breakout': 0.20
        }
        
        # 风险参数
        self.risk_params = {
            'max_position_size': 0.2,
            'min_position_size': 0.01,
            'base_risk_per_trade': 0.02,
            'volatility_adjustment': True
        }
    
    def analyze_market(self, df: pd.DataFrame) -> Dict:
        """全面市场分析"""
        
        # 1. 基础数据准备
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        current_price = close[-1]
        
        # 2. 技术指标计算
        indicators = self._calculate_indicators(df)
        
        # 3. 市场结构分析
        market_structure = self._analyze_market_structure(df, indicators)
        
        # 4. 趋势分析
        trend_analysis = self._analyze_trend(indicators, current_price)
        
        # 5. 动量分析
        momentum_analysis = self._analyze_momentum(indicators)
        
        # 6. 支撑阻力分析
        sr_analysis = self._analyze_support_resistance(df)
        
        # 7. 波动率分析
        volatility_analysis = self._analyze_volatility(df, indicators)
        
        return {
            'current_price': current_price,
            'indicators': indicators,
            'market_structure': market_structure,
            'trend_analysis': trend_analysis,
            'momentum_analysis': momentum_analysis,
            'support_resistance': sr_analysis,
            'volatility_analysis': volatility_analysis
        }
    
    def _calculate_indicators(self, df: pd.DataFrame) -> Dict:
        """计算技术指标"""
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        
        indicators = {}
        
        # 移动平均线
        indicators['sma_20'] = self.indicators.sma(close, 20)
        indicators['sma_50'] = self.indicators.sma(close, 50)
        indicators['sma_200'] = self.indicators.sma(close, 200)
        indicators['ema_12'] = self.indicators.ema(close, 12)
        indicators['ema_26'] = self.indicators.ema(close, 26)
        
        # 动量指标
        indicators['rsi'] = self.indicators.rsi(close, 14)
        macd, signal, hist = self.indicators.macd(close)
        indicators['macd'] = macd
        indicators['macd_signal'] = signal
        indicators['macd_histogram'] = hist
        
        # 波动率指标
        bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(close)
        indicators['bb_upper'] = bb_upper
        indicators['bb_middle'] = bb_middle
        indicators['bb_lower'] = bb_lower
        
        indicators['atr'] = self.indicators.atr(high, low, close)
        
        return indicators
    
    def _analyze_market_structure(self, df: pd.DataFrame, indicators: Dict) -> Dict:
        """分析市场结构"""
        close = df['close'].values
        current_price = close[-1]
        
        # 均线排列分析
        sma_20 = indicators['sma_20'][-1] if not np.isnan(indicators['sma_20'][-1]) else current_price
        sma_50 = indicators['sma_50'][-1] if not np.isnan(indicators['sma_50'][-1]) else current_price
        sma_200 = indicators['sma_200'][-1] if not np.isnan(indicators['sma_200'][-1]) else current_price
        
        # 市场阶段判断
        if current_price > sma_20 > sma_50 > sma_200:
            phase = MarketPhase.MARKUP
            structure_score = 0.8
        elif current_price < sma_20 < sma_50 < sma_200:
            phase = MarketPhase.MARKDOWN
            structure_score = -0.8
        elif sma_20 > sma_50 and current_price < sma_20:
            phase = MarketPhase.DISTRIBUTION
            structure_score = -0.4
        elif sma_20 < sma_50 and current_price > sma_20:
            phase = MarketPhase.ACCUMULATION
            structure_score = 0.4
        else:
            phase = MarketPhase.CONSOLIDATION
            structure_score = 0.0
        
        return {
            'phase': phase,
            'structure_score': structure_score,
            'ma_alignment': {
                'sma_20': sma_20,
                'sma_50': sma_50,
                'sma_200': sma_200
            }
        }
    
    def _analyze_trend(self, indicators: Dict, current_price: float) -> Dict:
        """分析趋势"""
        ema_12 = indicators['ema_12'][-1] if not np.isnan(indicators['ema_12'][-1]) else current_price
        ema_26 = indicators['ema_26'][-1] if not np.isnan(indicators['ema_26'][-1]) else current_price
        macd = indicators['macd'][-1] if not np.isnan(indicators['macd'][-1]) else 0
        macd_signal = indicators['macd_signal'][-1] if not np.isnan(indicators['macd_signal'][-1]) else 0
        
        # 趋势方向
        if ema_12 > ema_26 and macd > macd_signal:
            direction = "UP"
            strength = 0.8
        elif ema_12 < ema_26 and macd < macd_signal:
            direction = "DOWN"
            strength = 0.8
        else:
            direction = "SIDEWAYS"
            strength = 0.3
        
        return {
            'direction': direction,
            'strength': strength,
            'ema_cross': ema_12 > ema_26,
            'macd_cross': macd > macd_signal
        }
    
    def _analyze_momentum(self, indicators: Dict) -> Dict:
        """分析动量"""
        rsi = indicators['rsi'][-1] if not np.isnan(indicators['rsi'][-1]) else 50
        macd_hist = indicators['macd_histogram'][-1] if not np.isnan(indicators['macd_histogram'][-1]) else 0
        
        # 动量评分
        momentum_score = 0
        
        if 40 <= rsi <= 60:
            momentum_score += 0.3  # RSI中性区间
        elif rsi > 70:
            momentum_score -= 0.5  # 超买
        elif rsi < 30:
            momentum_score += 0.5  # 超卖反弹机会
        
        if macd_hist > 0:
            momentum_score += 0.3
        else:
            momentum_score -= 0.3
        
        return {
            'rsi': rsi,
            'momentum_score': momentum_score,
            'overbought': rsi > 70,
            'oversold': rsi < 30
        }
    
    def _analyze_support_resistance(self, df: pd.DataFrame) -> Dict:
        """分析支撑阻力"""
        high = df['high'].values
        low = df['low'].values
        current_price = df['close'].values[-1]
        
        # 简化的支撑阻力识别
        recent_highs = []
        recent_lows = []
        
        # 寻找最近20个周期的局部极值
        for i in range(max(0, len(high) - 20), len(high) - 2):
            if i >= 2:
                # 局部高点
                if high[i] > high[i-1] and high[i] > high[i+1]:
                    recent_highs.append(high[i])
                
                # 局部低点
                if low[i] < low[i-1] and low[i] < low[i+1]:
                    recent_lows.append(low[i])
        
        # 过滤接近当前价格的水平
        resistance_levels = [h for h in recent_highs if h > current_price and (h - current_price) / current_price < 0.02]
        support_levels = [l for l in recent_lows if l < current_price and (current_price - l) / current_price < 0.02]
        
        return {
            'resistance_levels': sorted(resistance_levels)[:3],
            'support_levels': sorted(support_levels, reverse=True)[:3],
            'nearest_resistance': min(resistance_levels) if resistance_levels else None,
            'nearest_support': max(support_levels) if support_levels else None
        }
    
    def _analyze_volatility(self, df: pd.DataFrame, indicators: Dict) -> Dict:
        """分析波动率"""
        atr = indicators['atr'][-1] if not np.isnan(indicators['atr'][-1]) else 0.001
        atr_ma = np.nanmean(indicators['atr'][-20:])
        
        volatility_ratio = atr / atr_ma if atr_ma > 0 else 1
        
        if volatility_ratio > 1.5:
            regime = "HIGH"
        elif volatility_ratio < 0.7:
            regime = "LOW"
        else:
            regime = "NORMAL"
        
        return {
            'atr': atr,
            'volatility_ratio': volatility_ratio,
            'regime': regime
        }
    
    def generate_trading_decision(self, market_analysis: Dict) -> TradingDecision:
        """生成交易决策"""
        
        current_price = market_analysis['current_price']
        market_structure = market_analysis['market_structure']
        trend_analysis = market_analysis['trend_analysis']
        momentum_analysis = market_analysis['momentum_analysis']
        sr_analysis = market_analysis['support_resistance']
        volatility_analysis = market_analysis['volatility_analysis']
        
        # 策略评分
        strategy_scores = self._calculate_strategy_scores(market_analysis)
        
        # 选择最佳策略
        best_strategy = max(strategy_scores.items(), key=lambda x: abs(x[1]))
        strategy_name, strategy_score = best_strategy
        
        # 生成交易信号
        if abs(strategy_score) < 0.3:
            # 信号不够强，观望
            return TradingDecision(
                action="NONE",
                confidence=0.0,
                entry_price=None,
                stop_loss=current_price,
                take_profit=current_price,
                position_size=0.0,
                strategy_used="观望",
                reasoning="市场信号不明确，等待更好机会",
                risk_level="LOW",
                market_phase=market_structure['phase']
            )
        
        # 确定交易方向
        action = "BUY" if strategy_score > 0 else "SELL"
        
        # 计算入场价格
        entry_price = self._calculate_entry_price(current_price, action, sr_analysis)
        
        # 计算止损止盈
        stop_loss, take_profit = self._calculate_stop_take_profit(
            entry_price, action, sr_analysis, volatility_analysis
        )
        
        # 计算仓位大小
        position_size = self._calculate_position_size(
            abs(strategy_score), volatility_analysis, entry_price, stop_loss
        )
        
        # 风险等级
        risk_level = self._determine_risk_level(volatility_analysis, abs(strategy_score))
        
        return TradingDecision(
            action=action,
            confidence=abs(strategy_score),
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            position_size=position_size,
            strategy_used=strategy_name,
            reasoning=self._generate_reasoning(strategy_name, strategy_score, market_analysis),
            risk_level=risk_level,
            market_phase=market_structure['phase']
        )
    
    def _calculate_strategy_scores(self, market_analysis: Dict) -> Dict:
        """计算各策略评分"""
        trend_analysis = market_analysis['trend_analysis']
        momentum_analysis = market_analysis['momentum_analysis']
        market_structure = market_analysis['market_structure']
        sr_analysis = market_analysis['support_resistance']
        
        scores = {}
        
        # 趋势跟随策略
        trend_score = 0
        if trend_analysis['direction'] == "UP":
            trend_score = trend_analysis['strength']
        elif trend_analysis['direction'] == "DOWN":
            trend_score = -trend_analysis['strength']
        scores['趋势跟随'] = trend_score * self.strategy_weights['trend_following']
        
        # 均值回归策略
        mean_reversion_score = 0
        if momentum_analysis['oversold']:
            mean_reversion_score = 0.6
        elif momentum_analysis['overbought']:
            mean_reversion_score = -0.6
        scores['均值回归'] = mean_reversion_score * self.strategy_weights['mean_reversion']
        
        # 动量策略
        momentum_score = momentum_analysis['momentum_score']
        scores['动量交易'] = momentum_score * self.strategy_weights['momentum']
        
        # 突破策略
        breakout_score = 0
        current_price = market_analysis['current_price']
        if sr_analysis['nearest_resistance']:
            resistance_distance = (sr_analysis['nearest_resistance'] - current_price) / current_price
            if resistance_distance < 0.005:  # 接近阻力位
                breakout_score = 0.5
        if sr_analysis['nearest_support']:
            support_distance = (current_price - sr_analysis['nearest_support']) / current_price
            if support_distance < 0.005:  # 接近支撑位
                breakout_score = -0.5
        scores['突破交易'] = breakout_score * self.strategy_weights['breakout']
        
        return scores
    
    def _calculate_entry_price(self, current_price: float, action: str, sr_analysis: Dict) -> float:
        """计算入场价格"""
        # 简化版：使用当前价格附近的价格
        if action == "BUY":
            return current_price + 0.0001  # 稍高于当前价格
        else:
            return current_price - 0.0001  # 稍低于当前价格
    
    def _calculate_stop_take_profit(self, entry_price: float, action: str, 
                                  sr_analysis: Dict, volatility_analysis: Dict) -> Tuple[float, float]:
        """计算止损止盈"""
        atr = volatility_analysis['atr']
        
        # 基于ATR的止损距离
        stop_distance = atr * 2
        
        if action == "BUY":
            stop_loss = entry_price - stop_distance
            take_profit = entry_price + (stop_distance * 2)  # 1:2风险回报比
        else:
            stop_loss = entry_price + stop_distance
            take_profit = entry_price - (stop_distance * 2)
        
        return stop_loss, take_profit
    
    def _calculate_position_size(self, confidence: float, volatility_analysis: Dict, 
                               entry_price: float, stop_loss: float) -> float:
        """计算仓位大小"""
        base_size = 0.1
        
        # 根据信号置信度调整
        confidence_multiplier = 0.5 + (confidence * 0.5)
        
        # 根据波动率调整
        volatility_multiplier = 1.0
        if volatility_analysis['regime'] == "HIGH":
            volatility_multiplier = 0.7
        elif volatility_analysis['regime'] == "LOW":
            volatility_multiplier = 1.2
        
        final_size = base_size * confidence_multiplier * volatility_multiplier
        return max(min(final_size, self.risk_params['max_position_size']), 
                  self.risk_params['min_position_size'])
    
    def _determine_risk_level(self, volatility_analysis: Dict, confidence: float) -> str:
        """确定风险等级"""
        if volatility_analysis['regime'] == "HIGH" or confidence < 0.5:
            return "HIGH"
        elif volatility_analysis['regime'] == "LOW" and confidence > 0.7:
            return "LOW"
        else:
            return "MEDIUM"
    
    def _generate_reasoning(self, strategy_name: str, strategy_score: float, market_analysis: Dict) -> str:
        """生成决策理由"""
        trend = market_analysis['trend_analysis']['direction']
        phase = market_analysis['market_structure']['phase'].value
        rsi = market_analysis['momentum_analysis']['rsi']
        
        reasoning = f"采用{strategy_name}策略（评分: {strategy_score:.2f}）。"
        reasoning += f"当前趋势: {trend}，市场阶段: {phase}，RSI: {rsi:.1f}。"
        
        if abs(strategy_score) > 0.6:
            reasoning += "信号强度较高，建议执行。"
        else:
            reasoning += "信号强度中等，谨慎执行。"
        
        return reasoning

# 测试函数
def test_advanced_trading_system():
    """测试高级交易系统"""
    # 创建模拟数据
    dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
    np.random.seed(42)
    
    # 生成模拟价格数据
    base_price = 1.1000
    price_changes = np.random.normal(0, 0.0005, 100)
    prices = [base_price]
    
    for change in price_changes:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    prices = prices[1:]  # 移除初始价格
    
    # 创建OHLC数据
    df = pd.DataFrame({
        'datetime': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.0002))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.0002))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000, 10000, 100)
    })
    
    # 初始化交易系统
    trading_system = AdvancedTradingSystem()
    
    print("🚀 高级交易系统测试")
    print("=" * 60)
    
    # 市场分析
    market_analysis = trading_system.analyze_market(df)
    
    print(f"当前价格: {market_analysis['current_price']:.5f}")
    print(f"市场阶段: {market_analysis['market_structure']['phase'].value}")
    print(f"趋势方向: {market_analysis['trend_analysis']['direction']}")
    print(f"趋势强度: {market_analysis['trend_analysis']['strength']:.2f}")
    print(f"RSI: {market_analysis['momentum_analysis']['rsi']:.1f}")
    print(f"波动率状态: {market_analysis['volatility_analysis']['regime']}")
    
    # 生成交易决策
    decision = trading_system.generate_trading_decision(market_analysis)
    
    print("\n📊 交易决策:")
    print(f"行动: {decision.action}")
    print(f"置信度: {decision.confidence:.2f}")
    print(f"策略: {decision.strategy_used}")
    print(f"入场价格: {decision.entry_price:.5f}" if decision.entry_price else "无入场价格")
    print(f"止损: {decision.stop_loss:.5f}")
    print(f"止盈: {decision.take_profit:.5f}")
    print(f"仓位大小: {decision.position_size:.2f}")
    print(f"风险等级: {decision.risk_level}")
    print(f"决策理由: {decision.reasoning}")
    
    return decision

if __name__ == "__main__":
    test_advanced_trading_system()
