2025-05-29 22:50:10,978 - __main__ - INFO - 🚀 启动 QuantumForex Pro 云端模型接收服务
2025-05-29 22:50:10,978 - __main__ - INFO - ============================================================
2025-05-29 22:50:10,981 - __main__ - INFO - 🌐 启动API服务器...
2025-05-29 22:50:10,981 - __main__ - INFO - 📍 监听地址: 0.0.0.0:8081
2025-05-29 22:50:10,982 - __main__ - INFO - 🔗 API端点:
2025-05-29 22:50:10,982 - __main__ - INFO -    - 健康检查: http://127.0.0.1:8081/api/health
2025-05-29 22:50:10,982 - __main__ - INFO -    - 模型状态: http://127.0.0.1:8081/api/models/status
2025-05-29 22:50:10,983 - __main__ - INFO -    - 模型上传: http://127.0.0.1:8081/api/models/upload
2025-05-29 22:50:10,983 - __main__ - INFO -    - 模型列表: http://127.0.0.1:8081/api/models/list
2025-05-29 22:50:10,983 - __main__ - INFO -    📊 数据导出端点:
2025-05-29 22:50:10,984 - __main__ - INFO -    - 交易记录: http://127.0.0.1:8081/api/trading/records
2025-05-29 22:50:10,984 - __main__ - INFO -    - 参数优化: http://127.0.0.1:8081/api/optimization/history
2025-05-29 22:50:10,984 - __main__ - INFO -    - LLM分析: http://127.0.0.1:8081/api/llm/analysis_history
2025-05-29 22:50:10,984 - utils.cloud_model_receiver - INFO - 🚀 启动云端模型接收服务器: 0.0.0.0:8081
2025-05-29 22:50:11,006 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://***************:8081
2025-05-29 22:50:11,006 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 22:51:03,322 - core.learning_system.trade_result_recorder - INFO - 交易记录数据库初始化完成
2025-05-29 22:51:03,323 - utils.cloud_model_receiver - INFO - 获取交易记录: 0条 (最近1天)
2025-05-29 22:51:03,324 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:03] "GET /api/trading/records?days=1 HTTP/1.1" 200 -
2025-05-29 22:51:03,351 - utils.cloud_model_receiver - ERROR - 获取参数优化历史失败: ParameterOptimizer.__init__() missing 2 required positional arguments: 'trade_recorder' and 'pattern_analyzer'
2025-05-29 22:51:03,352 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:03] "GET /api/optimization/history?days=1 HTTP/1.1" 200 -
2025-05-29 22:51:03,366 - utils.cloud_model_receiver - INFO - 获取LLM分析历史: 0条 (功能待实现)
2025-05-29 22:51:03,367 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:03] "GET /api/llm/analysis_history?days=1 HTTP/1.1" 200 -
2025-05-29 22:51:51,005 - core.learning_system.trade_result_recorder - INFO - 交易记录数据库初始化完成
2025-05-29 22:51:51,007 - utils.cloud_model_receiver - INFO - 获取交易记录: 48条 (最近7天)
2025-05-29 22:51:51,010 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:51] "GET /api/trading/records?days=7 HTTP/1.1" 200 -
2025-05-29 22:51:51,029 - utils.cloud_model_receiver - ERROR - 获取参数优化历史失败: ParameterOptimizer.__init__() missing 2 required positional arguments: 'trade_recorder' and 'pattern_analyzer'
2025-05-29 22:51:51,029 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:51] "GET /api/optimization/history?days=7 HTTP/1.1" 200 -
2025-05-29 22:51:51,035 - utils.cloud_model_receiver - INFO - 获取LLM分析历史: 0条 (功能待实现)
2025-05-29 22:51:51,035 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:51] "GET /api/llm/analysis_history?days=7 HTTP/1.1" 200 -
2025-05-29 22:51:55,104 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:55] "GET /api/health HTTP/1.1" 200 -
2025-05-29 22:51:55,110 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:51:55,138 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_lightgbm_20250529_221032.pkl.backup_20250529_225155
2025-05-29 22:51:55,140 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_221032.pkl
2025-05-29 22:51:55,339 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_221032.pkl
2025-05-29 22:51:55,340 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:55] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:51:55,356 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:51:55,377 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_lightgbm_20250529_223818.pkl.backup_20250529_225155
2025-05-29 22:51:55,378 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_223818.pkl
2025-05-29 22:51:55,407 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_223818.pkl
2025-05-29 22:51:55,408 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:55] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:51:55,424 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:51:55,442 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_lightgbm_20250529_224208.pkl.backup_20250529_225155
2025-05-29 22:51:55,444 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_224208.pkl
2025-05-29 22:51:55,473 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_224208.pkl
2025-05-29 22:51:55,474 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:55] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:51:55,499 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:51:55,517 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 22:51:55,518 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_225155.pkl
2025-05-29 22:51:55,547 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_225155.pkl
2025-05-29 22:51:55,548 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:55] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:51:55,566 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:51:55,589 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_random_forest_20250529_221032.pkl.backup_20250529_225155
2025-05-29 22:51:55,591 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_221032.pkl
2025-05-29 22:51:55,644 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_221032.pkl
2025-05-29 22:51:55,645 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:55] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:51:55,669 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:51:55,699 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_random_forest_20250529_223818.pkl.backup_20250529_225155
2025-05-29 22:51:55,713 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_223818.pkl
2025-05-29 22:51:55,783 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_223818.pkl
2025-05-29 22:51:55,792 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:55] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:51:55,823 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:51:55,887 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_random_forest_20250529_224208.pkl.backup_20250529_225155
2025-05-29 22:51:55,890 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_224208.pkl
2025-05-29 22:51:55,935 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_224208.pkl
2025-05-29 22:51:55,936 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:55] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:51:55,970 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:51:55,998 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 22:51:56,000 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_225155.pkl
2025-05-29 22:51:56,044 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_225155.pkl
2025-05-29 22:51:56,045 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:56] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:51:56,062 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:51:56,083 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_xgboost_20250529_224208.pkl.backup_20250529_225156
2025-05-29 22:51:56,084 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_xgboost_20250529_224208.pkl
2025-05-29 22:51:56,143 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_xgboost_20250529_224208.pkl
2025-05-29 22:51:56,144 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:56] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:51:56,760 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:51:56,776 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 22:51:56,778 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_xgboost_20250529_225155.pkl
2025-05-29 22:51:56,798 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_xgboost_20250529_225155.pkl
2025-05-29 22:51:56,799 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:51:56] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:20,955 - core.learning_system.trade_result_recorder - INFO - 交易记录数据库初始化完成
2025-05-29 23:01:20,958 - utils.cloud_model_receiver - INFO - 获取交易记录: 48条 (最近7天)
2025-05-29 23:01:20,959 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:20] "GET /api/trading/records?days=7 HTTP/1.1" 200 -
2025-05-29 23:01:20,978 - utils.cloud_model_receiver - ERROR - 获取参数优化历史失败: ParameterOptimizer.__init__() missing 2 required positional arguments: 'trade_recorder' and 'pattern_analyzer'
2025-05-29 23:01:20,978 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:20] "GET /api/optimization/history?days=7 HTTP/1.1" 200 -
2025-05-29 23:01:20,984 - utils.cloud_model_receiver - INFO - 获取LLM分析历史: 0条 (功能待实现)
2025-05-29 23:01:20,985 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:20] "GET /api/llm/analysis_history?days=7 HTTP/1.1" 200 -
2025-05-29 23:01:25,457 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:25] "GET /api/health HTTP/1.1" 200 -
2025-05-29 23:01:25,464 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:25,490 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_lightgbm_20250529_221032.pkl.backup_20250529_230125
2025-05-29 23:01:25,491 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_221032.pkl
2025-05-29 23:01:25,516 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_221032.pkl
2025-05-29 23:01:25,517 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:25] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:25,535 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:25,555 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_lightgbm_20250529_223818.pkl.backup_20250529_230125
2025-05-29 23:01:25,556 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_223818.pkl
2025-05-29 23:01:25,725 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_223818.pkl
2025-05-29 23:01:25,760 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:25] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:25,841 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:25,863 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_lightgbm_20250529_224208.pkl.backup_20250529_230125
2025-05-29 23:01:25,866 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_224208.pkl
2025-05-29 23:01:25,891 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_224208.pkl
2025-05-29 23:01:25,892 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:25] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:25,909 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:25,928 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_lightgbm_20250529_225155.pkl.backup_20250529_230125
2025-05-29 23:01:25,933 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_225155.pkl
2025-05-29 23:01:25,952 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_225155.pkl
2025-05-29 23:01:25,953 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:25] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:25,979 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:25,997 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 23:01:25,998 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_230125.pkl
2025-05-29 23:01:26,027 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_230125.pkl
2025-05-29 23:01:26,027 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:26] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:26,046 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:26,066 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_random_forest_20250529_221032.pkl.backup_20250529_230126
2025-05-29 23:01:26,068 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_221032.pkl
2025-05-29 23:01:26,119 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_221032.pkl
2025-05-29 23:01:26,120 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:26] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:26,142 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:26,167 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_random_forest_20250529_223818.pkl.backup_20250529_230126
2025-05-29 23:01:26,169 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_223818.pkl
2025-05-29 23:01:26,219 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_223818.pkl
2025-05-29 23:01:26,220 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:26] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:26,263 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:26,294 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_random_forest_20250529_224208.pkl.backup_20250529_230126
2025-05-29 23:01:26,297 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_224208.pkl
2025-05-29 23:01:26,352 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_224208.pkl
2025-05-29 23:01:26,353 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:26] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:26,380 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:26,410 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_random_forest_20250529_225155.pkl.backup_20250529_230126
2025-05-29 23:01:26,413 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_225155.pkl
2025-05-29 23:01:26,470 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_225155.pkl
2025-05-29 23:01:26,471 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:26] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:26,504 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:26,567 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 23:01:26,593 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_230125.pkl
2025-05-29 23:01:26,667 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_230125.pkl
2025-05-29 23:01:26,669 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:26] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:26,691 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:26,720 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_xgboost_20250529_224208.pkl.backup_20250529_230126
2025-05-29 23:01:26,722 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_xgboost_20250529_224208.pkl
2025-05-29 23:01:26,760 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_xgboost_20250529_224208.pkl
2025-05-29 23:01:26,760 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:26] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:26,779 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:26,798 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_xgboost_20250529_225155.pkl.backup_20250529_230126
2025-05-29 23:01:26,799 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_xgboost_20250529_225155.pkl
2025-05-29 23:01:26,832 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_xgboost_20250529_225155.pkl
2025-05-29 23:01:26,833 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:26] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 23:01:27,544 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 23:01:27,581 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 23:01:27,584 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_xgboost_20250529_230125.pkl
2025-05-29 23:01:27,613 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_xgboost_20250529_230125.pkl
2025-05-29 23:01:27,614 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 23:01:27] "POST /api/models/upload HTTP/1.1" 200 -
