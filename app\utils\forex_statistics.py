"""
外汇交易统计评估模块
用于对分析、命令和结果进行统计和评估
"""
import os
import json
import pandas as pd
from datetime import datetime, timedelta
import numpy as np
from collections import Counter

# 尝试导入matplotlib，如果不可用则禁用图表功能
try:
    import matplotlib.pyplot as plt
    from matplotlib.font_manager import FontProperties
    MATPLOTLIB_AVAILABLE = True

    # 设置中文字体
    try:
        font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf")
    except:
        font = None
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib未安装，图表功能将被禁用。请安装matplotlib以启用图表功能。")

# 数据目录
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
HISTORY_FILE_PATH = os.path.join(DATA_DIR, 'forex_analysis_history.json')
OPERATIONS_FILE_PATH = os.path.join(DATA_DIR, 'forex_operations.json')
STATISTICS_FILE_PATH = os.path.join(DATA_DIR, 'forex_statistics.json')
STATISTICS_REPORT_PATH = os.path.join(DATA_DIR, 'forex_statistics_report.txt')
STATISTICS_CHARTS_DIR = os.path.join(DATA_DIR, 'charts')

# 确保目录存在
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)
if not os.path.exists(STATISTICS_CHARTS_DIR):
    os.makedirs(STATISTICS_CHARTS_DIR)


def load_analysis_history():
    """加载分析历史记录"""
    try:
        if not os.path.exists(HISTORY_FILE_PATH):
            return []

        with open(HISTORY_FILE_PATH, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载分析历史记录失败: {e}")
        return []


def load_operations():
    """加载操作记录"""
    try:
        from app.utils.error_logger import get_all_operations
        return get_all_operations()
    except Exception as e:
        print(f"加载操作记录失败: {e}")
        return []


def calculate_statistics():
    """计算统计数据"""
    try:
        # 加载数据
        analysis_history = load_analysis_history()
        operations = load_operations()

        # 初始化统计结果
        stats = {
            'timestamp': datetime.now().isoformat(),
            'analysis_count': len(analysis_history),
            'operation_count': len(operations),
            'action_distribution': {},
            'success_rate': 0,
            'profit_loss': 0,
            'avg_profit_per_trade': 0,
            'win_rate': 0,
            'loss_rate': 0,
            'risk_reward_ratio': 0,
            'max_drawdown': 0,
            'consecutive_wins': 0,
            'consecutive_losses': 0,
            'order_management_stats': {
                'modify_count': 0,
                'close_count': 0,
                'delete_count': 0,
                'success_rate': 0
            },
            'time_distribution': {
                'hourly': [0] * 24,
                'daily': [0] * 7
            },
            'decision_consistency': 0,
            'execution_delay': 0
        }

        # 分析行为分布
        actions = [record.get('tradeInstructions', {}).get('action', 'UNKNOWN') for record in analysis_history]
        action_counter = Counter(actions)
        stats['action_distribution'] = {action: count for action, count in action_counter.items()}

        # 分析订单管理操作
        order_management_actions = []
        for record in analysis_history:
            order_management = record.get('tradeInstructions', {}).get('orderManagement', [])
            for action in order_management:
                if isinstance(action, dict):
                    order_management_actions.append(action.get('action', 'UNKNOWN'))

        order_management_counter = Counter(order_management_actions)
        stats['order_management_stats']['modify_count'] = order_management_counter.get('MODIFY', 0)
        stats['order_management_stats']['close_count'] = order_management_counter.get('CLOSE', 0)
        stats['order_management_stats']['delete_count'] = order_management_counter.get('DELETE', 0)

        # 分析操作成功率
        successful_operations = [op for op in operations if op.get('status') == 'SUCCESS']
        if operations:
            stats['success_rate'] = round(len(successful_operations) / len(operations) * 100, 2)

        # 分析时间分布
        for record in analysis_history:
            try:
                timestamp = record.get('timestamp', '')
                if timestamp:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    stats['time_distribution']['hourly'][dt.hour] += 1
                    stats['time_distribution']['daily'][dt.weekday()] += 1
            except Exception as e:
                print(f"分析时间分布时出错: {e}")

        # 分析决策一致性（最终决策与初始分析的一致性）
        consistent_decisions = 0
        for record in analysis_history:
            if isinstance(record.get('analysis'), dict) and 'initial' in record.get('analysis', {}):
                initial_analysis = record['analysis'].get('initial', '')
                final_analysis = record['analysis'].get('final', '')

                # 简单比较是否包含相同的关键词
                initial_keywords = ['买入', '卖出', '观望', 'BUY', 'SELL', 'HOLD', 'NONE']
                final_keywords = ['买入', '卖出', '观望', 'BUY', 'SELL', 'HOLD', 'NONE']

                initial_action = None
                final_action = None

                for keyword in initial_keywords:
                    if keyword in initial_analysis:
                        initial_action = keyword
                        break

                for keyword in final_keywords:
                    if keyword in final_analysis:
                        final_action = keyword
                        break

                if initial_action and final_action:
                    if initial_action == final_action:
                        consistent_decisions += 1

        if len(analysis_history) > 0:
            stats['decision_consistency'] = round(consistent_decisions / len(analysis_history) * 100, 2)

        # 分析执行延迟
        execution_delays = []
        for record in analysis_history:
            if record.get('tradeExecuted', False):
                analysis_time = datetime.fromisoformat(record.get('timestamp', '').replace('Z', '+00:00'))

                # 查找对应的操作记录
                matching_operations = []
                for op in operations:
                    op_time = datetime.fromisoformat(op.get('timestamp', '').replace('Z', '+00:00'))
                    time_diff = (op_time - analysis_time).total_seconds()

                    # 允许5分钟的时间差
                    if 0 <= time_diff < 300:
                        matching_operations.append((op, time_diff))

                if matching_operations:
                    # 使用最早的操作记录计算延迟
                    matching_operations.sort(key=lambda x: x[1])
                    execution_delays.append(matching_operations[0][1])

        if execution_delays:
            stats['execution_delay'] = round(sum(execution_delays) / len(execution_delays), 2)

        # 保存统计结果
        with open(STATISTICS_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        return stats

    except Exception as e:
        print(f"计算统计数据失败: {e}")
        return None


def generate_statistics_report(stats=None):
    """生成统计报告"""
    try:
        if stats is None:
            # 如果没有提供统计数据，尝试从文件加载
            if os.path.exists(STATISTICS_FILE_PATH):
                with open(STATISTICS_FILE_PATH, 'r', encoding='utf-8') as f:
                    stats = json.load(f)
            else:
                # 如果文件不存在，重新计算
                stats = calculate_statistics()

        if not stats:
            return "无法生成统计报告：统计数据为空"

        # 生成报告文本
        report = "# 外汇交易系统统计报告\n\n"
        report += f"生成时间: {datetime.fromisoformat(stats['timestamp']).strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        report += "## 基本统计\n\n"
        report += f"- 分析次数: {stats['analysis_count']}\n"
        report += f"- 操作次数: {stats['operation_count']}\n"
        report += f"- 操作成功率: {stats['success_rate']}%\n\n"

        report += "## 交易行为分布\n\n"
        for action, count in stats['action_distribution'].items():
            percentage = round(count / stats['analysis_count'] * 100, 2) if stats['analysis_count'] > 0 else 0
            report += f"- {action}: {count} ({percentage}%)\n"
        report += "\n"

        report += "## 订单管理统计\n\n"
        report += f"- 修改订单: {stats['order_management_stats']['modify_count']}\n"
        report += f"- 平仓操作: {stats['order_management_stats']['close_count']}\n"
        report += f"- 删除挂单: {stats['order_management_stats']['delete_count']}\n\n"

        report += "## 时间分布\n\n"
        report += "### 小时分布\n\n"
        for hour, count in enumerate(stats['time_distribution']['hourly']):
            report += f"- {hour}时: {count}\n"
        report += "\n"

        report += "### 星期分布\n\n"
        weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        for day, count in enumerate(stats['time_distribution']['daily']):
            report += f"- {weekdays[day]}: {count}\n"
        report += "\n"

        report += "## 决策一致性与执行效率\n\n"
        report += f"- 决策一致性: {stats['decision_consistency']}%\n"
        report += f"- 平均执行延迟: {stats['execution_delay']}秒\n\n"

        # 保存报告
        with open(STATISTICS_REPORT_PATH, 'w', encoding='utf-8') as f:
            f.write(report)

        # 生成图表
        generate_charts(stats)

        return report

    except Exception as e:
        error_msg = f"生成统计报告失败: {e}"
        print(error_msg)
        return error_msg


def generate_charts(stats):
    """生成统计图表"""
    if not MATPLOTLIB_AVAILABLE:
        print("matplotlib未安装，跳过图表生成")
        return

    try:
        # 设置中文显示
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

        # 1. 交易行为分布饼图
        plt.figure(figsize=(10, 6))
        actions = list(stats['action_distribution'].keys())
        counts = list(stats['action_distribution'].values())

        plt.pie(counts, labels=actions, autopct='%1.1f%%', startangle=90)
        plt.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle
        plt.title('交易行为分布')
        plt.savefig(os.path.join(STATISTICS_CHARTS_DIR, 'action_distribution.png'))
        plt.close()

        # 2. 订单管理操作柱状图
        plt.figure(figsize=(10, 6))
        management_actions = ['MODIFY', 'CLOSE', 'DELETE']
        management_counts = [
            stats['order_management_stats']['modify_count'],
            stats['order_management_stats']['close_count'],
            stats['order_management_stats']['delete_count']
        ]

        plt.bar(management_actions, management_counts)
        plt.title('订单管理操作分布')
        plt.xlabel('操作类型')
        plt.ylabel('次数')
        plt.savefig(os.path.join(STATISTICS_CHARTS_DIR, 'order_management.png'))
        plt.close()

        # 3. 小时分布柱状图
        plt.figure(figsize=(12, 6))
        hours = list(range(24))
        hour_counts = stats['time_distribution']['hourly']

        plt.bar(hours, hour_counts)
        plt.title('分析时间小时分布')
        plt.xlabel('小时')
        plt.ylabel('次数')
        plt.xticks(hours)
        plt.savefig(os.path.join(STATISTICS_CHARTS_DIR, 'hourly_distribution.png'))
        plt.close()

        # 4. 星期分布柱状图
        plt.figure(figsize=(10, 6))
        weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        day_counts = stats['time_distribution']['daily']

        plt.bar(weekdays, day_counts)
        plt.title('分析时间星期分布')
        plt.xlabel('星期')
        plt.ylabel('次数')
        plt.savefig(os.path.join(STATISTICS_CHARTS_DIR, 'daily_distribution.png'))
        plt.close()

        print("统计图表生成完成")

    except Exception as e:
        print(f"生成统计图表失败: {e}")


def run_statistics_analysis():
    """运行统计分析"""
    try:
        print("开始运行统计分析...")
        stats = calculate_statistics()
        if stats:
            report = generate_statistics_report(stats)
            print("统计分析完成")
            return report
        else:
            print("统计分析失败：无法计算统计数据")
            return None
    except Exception as e:
        print(f"运行统计分析失败: {e}")
        return None
