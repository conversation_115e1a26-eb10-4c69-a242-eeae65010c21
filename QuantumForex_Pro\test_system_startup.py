#!/usr/bin/env python3
"""
QuantumForex Pro - 系统启动测试
测试主系统的启动和初始化过程
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_system_initialization():
    """测试系统初始化"""
    print("🔍 开始系统初始化测试...")
    print("=" * 50)
    
    try:
        # 导入主系统类
        from main import QuantumForexPro
        print("✅ 主系统类导入成功")
        
        # 创建系统实例（这会触发所有初始化）
        print("🚀 开始创建系统实例...")
        system = QuantumForexPro()
        print("✅ 系统实例创建成功")
        
        # 检查各个组件是否正确初始化
        components = [
            ('technical_analyzer', '技术分析引擎'),
            ('signal_fusion', '信号融合引擎'),
            ('ml_engine', '机器学习引擎'),
            ('risk_manager', '风险管理引擎'),
            ('trade_executor', '交易执行引擎'),
            ('position_manager', '持仓管理器'),
            ('system_coordinator', '系统协调器'),
            ('master_strategy', '主策略引擎'),
            ('learning_coordinator', '学习系统'),
            ('mt4_monitor', 'MT4监控')
        ]
        
        print("\n📊 组件初始化检查:")
        all_components_ok = True
        
        for attr_name, display_name in components:
            if hasattr(system, attr_name):
                component = getattr(system, attr_name)
                if component is not None:
                    print(f"   ✅ {display_name}: 已初始化")
                else:
                    print(f"   ❌ {display_name}: 初始化失败")
                    all_components_ok = False
            else:
                print(f"   ❌ {display_name}: 属性不存在")
                all_components_ok = False
        
        # 检查系统统计
        if hasattr(system, 'system_stats'):
            stats = system.system_stats
            print(f"\n📈 系统统计:")
            print(f"   总信号数: {stats.get('total_signals', 0)}")
            print(f"   成功分析: {stats.get('successful_analyses', 0)}")
            print(f"   失败分析: {stats.get('failed_analyses', 0)}")
            print(f"   启动时间: {system.system_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("=" * 50)
        if all_components_ok:
            print("✅ 系统初始化测试完成!")
            return True, system
        else:
            print("❌ 部分组件初始化失败")
            return False, system
        
    except Exception as e:
        print(f"❌ 系统初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_market_data_acquisition(system):
    """测试市场数据获取"""
    print("\n🔍 开始市场数据获取测试...")
    print("=" * 50)
    
    try:
        # 测试获取市场数据
        market_data = system._get_market_data()
        
        if market_data:
            print(f"✅ 市场数据获取成功，包含{len(market_data)}个货币对")
            
            for symbol, data in market_data.items():
                print(f"   {symbol}:")
                print(f"     当前价格: {data.get('current_price', 'N/A')}")
                print(f"     波动率: {data.get('volatility', 'N/A')}")
                print(f"     数据源: {data.get('data_source', 'N/A')}")
                print(f"     数据质量: {data.get('data_quality', 'N/A')}")
                
                # 检查OHLCV数据
                if 'ohlcv' in data:
                    ohlcv = data['ohlcv']
                    print(f"     数据点数: {len(ohlcv)}")
                    print(f"     时间范围: {ohlcv.index[0]} 到 {ohlcv.index[-1]}")
                
                print()
        else:
            print("❌ 市场数据获取失败")
            return False
        
        print("=" * 50)
        print("✅ 市场数据获取测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 市场数据获取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_cycle(system):
    """测试分析周期"""
    print("\n🔍 开始分析周期测试...")
    print("=" * 50)
    
    try:
        # 执行一次完整的分析周期
        print("🔄 执行分析周期...")
        success = system._execute_analysis_cycle()
        
        if success:
            print("✅ 分析周期执行成功")
            
            # 检查系统统计更新
            stats = system.system_stats
            print(f"   成功分析次数: {stats.get('successful_analyses', 0)}")
            print(f"   失败分析次数: {stats.get('failed_analyses', 0)}")
            print(f"   总信号数: {stats.get('total_signals', 0)}")
            
            if stats.get('last_analysis_time'):
                print(f"   最后分析时间: {stats['last_analysis_time'].strftime('%H:%M:%S')}")
        else:
            print("❌ 分析周期执行失败")
            return False
        
        print("=" * 50)
        print("✅ 分析周期测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 分析周期测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_health_check(system):
    """测试系统健康检查"""
    print("\n🔍 开始系统健康检查测试...")
    print("=" * 50)
    
    try:
        # 测试市场时间检查
        current_time = datetime.now()
        market_open = system._is_market_open(current_time)
        print(f"✅ 市场时间检查: {'开放' if market_open else '关闭'}")
        
        # 测试系统健康状态
        system_healthy = system._is_system_healthy()
        print(f"✅ 系统健康状态: {'健康' if system_healthy else '异常'}")
        
        # 测试分析条件
        should_run = system._should_run_analysis(current_time, current_time)
        print(f"✅ 分析条件检查: {'满足' if should_run else '不满足'}")
        
        # 显示系统状态
        system._display_system_status()
        
        print("=" * 50)
        print("✅ 系统健康检查测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 系统健康检查测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_short_run(system):
    """测试短时间运行"""
    print("\n🔍 开始短时间运行测试...")
    print("=" * 50)
    
    try:
        print("🚀 启动系统运行（10秒）...")
        
        # 设置运行标志
        system.running = True
        
        # 创建运行线程
        def run_system():
            try:
                # 模拟主循环运行10秒
                start_time = time.time()
                while system.running and (time.time() - start_time) < 10:
                    # 检查是否需要分析
                    current_time = datetime.now()
                    if system._should_run_analysis(current_time, current_time):
                        print("🔄 执行分析...")
                        system._execute_analysis_cycle()
                    
                    # 更新系统统计
                    system._update_system_stats()
                    
                    # 短暂休眠
                    time.sleep(2)
                
                print("⏹️ 系统运行结束")
                
            except Exception as e:
                print(f"❌ 系统运行异常: {e}")
        
        # 启动运行线程
        run_thread = threading.Thread(target=run_system, daemon=True)
        run_thread.start()
        
        # 等待运行完成
        run_thread.join(timeout=15)
        
        # 停止系统
        system.running = False
        
        # 显示最终统计
        stats = system.system_stats
        print(f"\n📊 运行统计:")
        print(f"   运行时间: {stats.get('uptime', 0):.1f}秒")
        print(f"   成功分析: {stats.get('successful_analyses', 0)}")
        print(f"   失败分析: {stats.get('failed_analyses', 0)}")
        print(f"   总信号数: {stats.get('total_signals', 0)}")
        
        print("=" * 50)
        print("✅ 短时间运行测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 短时间运行测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 QuantumForex Pro - 系统启动测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行所有测试
    tests = [
        ("系统初始化", test_system_initialization),
    ]
    
    results = {}
    system = None
    
    # 首先测试系统初始化
    init_success, system = test_system_initialization()
    results["系统初始化"] = init_success
    
    if init_success and system:
        # 如果初始化成功，继续其他测试
        additional_tests = [
            ("市场数据获取", lambda: test_market_data_acquisition(system)),
            ("分析周期", lambda: test_analysis_cycle(system)),
            ("系统健康检查", lambda: test_system_health_check(system)),
            ("短时间运行", lambda: test_short_run(system))
        ]
        
        for test_name, test_func in additional_tests:
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}")
                results[test_name] = False
    
    print("\n" + "=" * 60)
    print("📊 系统启动测试结果汇总:")
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("🎉 所有系统启动测试通过! 系统准备就绪!")
        sys.exit(0)
    else:
        print("❌ 部分系统启动测试失败，请检查相关组件")
        sys.exit(1)
