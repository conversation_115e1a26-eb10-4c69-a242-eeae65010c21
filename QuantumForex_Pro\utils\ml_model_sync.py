"""
QuantumForex Pro ML模型同步管理器
与训练端同步新训练的模型，确保兼容性和平滑过渡
"""

import os
import json
import shutil
import logging
import joblib
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any

from config.config import config

class MLModelSyncManager:
    """ML模型同步管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 配置信息
        self.ml_config = config.ML_TRAINER_CONFIG
        
        # 路径配置
        self.local_models_path = Path(self.ml_config['model_paths']['local_models'])
        self.shared_folder_remote = self.ml_config['shared_folder_remote']
        self.shared_folder_local = Path(self.ml_config['shared_folder_local'])
        
        # 确保本地路径存在
        self.local_models_path.mkdir(parents=True, exist_ok=True)
        self.shared_folder_local.mkdir(parents=True, exist_ok=True)
        
        # 支持的模型类型
        self.model_types = [
            'price_prediction_model',
            'risk_assessment_model',
            'trend_classification_model',
            'volatility_prediction_model'
        ]
        
        # 兼容性检查
        self.compatibility_enabled = self.ml_config['compatibility']['check_version']
        self.fallback_enabled = self.ml_config['compatibility']['fallback_enabled']
        self.legacy_support = self.ml_config['compatibility']['legacy_support']
    
    def check_trainer_connection(self) -> bool:
        """检查与训练端的连接"""
        try:
            self.logger.info("🔍 检查训练端连接...")
            
            # 方法1: 检查共享文件夹
            if self._check_shared_folder():
                self.logger.info("✅ 共享文件夹连接正常")
                return True
            
            # 方法2: 尝试网络连接
            trainer_ip = self.ml_config['trainer_ip']
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((trainer_ip, 80))
            sock.close()
            
            if result == 0:
                self.logger.info(f"✅ 训练端网络可达: {trainer_ip}")
                return True
            
            self.logger.warning("⚠️ 训练端连接失败")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ 检查训练端连接失败: {e}")
            return False
    
    def _check_shared_folder(self) -> bool:
        """检查共享文件夹访问"""
        try:
            # 检查远程共享文件夹
            remote_path = Path(self.shared_folder_remote)
            if remote_path.exists():
                return True
            
            # 检查本地映射文件夹
            if self.shared_folder_local.exists():
                return True
            
            return False
            
        except Exception as e:
            self.logger.debug(f"共享文件夹检查失败: {e}")
            return False
    
    def sync_models_from_trainer(self) -> Dict[str, Any]:
        """从训练端同步模型"""
        try:
            self.logger.info("🔄 开始同步模型...")
            
            sync_result = {
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'synced_models': [],
                'failed_models': [],
                'backup_created': False,
                'errors': []
            }
            
            # 检查连接
            if not self.check_trainer_connection():
                sync_result['errors'].append('训练端连接失败')
                return sync_result
            
            # 获取可用的新模型
            available_models = self._get_available_models()
            if not available_models:
                self.logger.info("📊 没有发现新模型")
                sync_result['success'] = True
                return sync_result
            
            # 备份现有模型
            if self.ml_config['update_strategy']['auto_backup_current']:
                backup_success = self._backup_current_models()
                sync_result['backup_created'] = backup_success
            
            # 同步每个模型
            for model_info in available_models:
                try:
                    success = self._sync_single_model(model_info)
                    if success:
                        sync_result['synced_models'].append(model_info['name'])
                        self.logger.info(f"✅ 模型同步成功: {model_info['name']}")
                    else:
                        sync_result['failed_models'].append(model_info['name'])
                        self.logger.error(f"❌ 模型同步失败: {model_info['name']}")
                        
                except Exception as e:
                    sync_result['failed_models'].append(model_info['name'])
                    sync_result['errors'].append(f"同步{model_info['name']}失败: {str(e)}")
                    self.logger.error(f"❌ 模型同步异常 {model_info['name']}: {e}")
            
            # 验证同步结果
            if sync_result['synced_models']:
                sync_result['success'] = True
                self.logger.info(f"🎉 模型同步完成: {len(sync_result['synced_models'])}个成功")
            
            # 保存同步报告
            self._save_sync_report(sync_result)
            
            return sync_result
            
        except Exception as e:
            self.logger.error(f"❌ 模型同步失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'error': str(e)
            }
    
    def _get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用的新模型"""
        try:
            available_models = []
            
            # 检查共享文件夹中的模型
            model_folders = [
                Path(self.shared_folder_remote) / 'models' / 'current',
                self.shared_folder_local / 'models' / 'current'
            ]
            
            for model_folder in model_folders:
                if not model_folder.exists():
                    continue
                
                # 查找模型文件
                for model_file in model_folder.glob('*.pkl'):
                    model_name = model_file.stem
                    
                    # 检查是否是支持的模型类型
                    if any(model_type in model_name for model_type in self.model_types):
                        model_info = {
                            'name': model_name,
                            'file_path': str(model_file),
                            'file_size': model_file.stat().st_size,
                            'modified_time': datetime.fromtimestamp(model_file.stat().st_mtime),
                            'is_newer': self._is_model_newer(model_file)
                        }
                        
                        if model_info['is_newer']:
                            available_models.append(model_info)
                            self.logger.info(f"📦 发现新模型: {model_name}")
            
            return available_models
            
        except Exception as e:
            self.logger.error(f"❌ 获取可用模型失败: {e}")
            return []
    
    def _is_model_newer(self, model_file: Path) -> bool:
        """检查模型是否比本地版本更新"""
        try:
            local_model_path = self.local_models_path / model_file.name
            
            if not local_model_path.exists():
                return True  # 本地没有，认为是新的
            
            # 比较修改时间
            remote_mtime = model_file.stat().st_mtime
            local_mtime = local_model_path.stat().st_mtime
            
            return remote_mtime > local_mtime
            
        except Exception as e:
            self.logger.debug(f"检查模型时间失败 {model_file}: {e}")
            return True  # 出错时认为是新的，尝试同步
    
    def _backup_current_models(self) -> bool:
        """备份当前模型"""
        try:
            backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_folder = self.local_models_path.parent / 'backup' / f'models_{backup_timestamp}'
            backup_folder.mkdir(parents=True, exist_ok=True)
            
            backed_up_count = 0
            
            for model_file in self.local_models_path.glob('*.pkl'):
                try:
                    backup_file = backup_folder / model_file.name
                    shutil.copy2(model_file, backup_file)
                    backed_up_count += 1
                    self.logger.debug(f"已备份: {model_file.name}")
                except Exception as e:
                    self.logger.warning(f"备份失败 {model_file.name}: {e}")
            
            self.logger.info(f"📦 模型备份完成: {backed_up_count}个文件")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 备份当前模型失败: {e}")
            return False
    
    def _sync_single_model(self, model_info: Dict[str, Any]) -> bool:
        """同步单个模型"""
        try:
            source_path = Path(model_info['file_path'])
            target_path = self.local_models_path / source_path.name
            
            # 兼容性检查
            if self.compatibility_enabled:
                if not self._check_model_compatibility(source_path):
                    self.logger.warning(f"⚠️ 模型兼容性检查失败: {model_info['name']}")
                    if not self.fallback_enabled:
                        return False
            
            # 复制模型文件
            shutil.copy2(source_path, target_path)
            
            # 验证复制结果
            if target_path.exists() and target_path.stat().st_size > 0:
                self.logger.debug(f"模型文件复制成功: {target_path}")
                
                # 测试模型加载
                if self.ml_config['update_strategy']['test_new_model']:
                    if self._test_model_loading(target_path):
                        return True
                    else:
                        self.logger.error(f"❌ 模型加载测试失败: {model_info['name']}")
                        return False
                else:
                    return True
            else:
                self.logger.error(f"❌ 模型文件复制失败: {target_path}")
                return False
            
        except Exception as e:
            self.logger.error(f"❌ 同步单个模型失败 {model_info['name']}: {e}")
            return False
    
    def _check_model_compatibility(self, model_path: Path) -> bool:
        """检查模型兼容性"""
        try:
            # 尝试加载模型
            model = joblib.load(model_path)
            
            # 检查模型类型
            model_type = type(model).__name__
            
            # 检查必要的方法
            required_methods = ['predict']
            for method in required_methods:
                if not hasattr(model, method):
                    self.logger.warning(f"模型缺少必要方法: {method}")
                    return False
            
            self.logger.debug(f"模型兼容性检查通过: {model_type}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型兼容性检查失败: {e}")
            return False
    
    def _test_model_loading(self, model_path: Path) -> bool:
        """测试模型加载"""
        try:
            model = joblib.load(model_path)
            
            # 简单的功能测试
            if hasattr(model, 'predict'):
                # 这里可以添加更详细的测试
                pass
            
            self.logger.debug(f"模型加载测试通过: {model_path.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型加载测试失败: {e}")
            return False
    
    def _save_sync_report(self, sync_result: Dict[str, Any]):
        """保存同步报告"""
        try:
            report_folder = self.local_models_path.parent / 'logs'
            report_folder.mkdir(exist_ok=True)
            
            report_file = report_folder / f'model_sync_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(sync_result, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📊 同步报告已保存: {report_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 保存同步报告失败: {e}")
    
    def rollback_to_previous_models(self) -> bool:
        """回滚到上一个版本的模型"""
        try:
            self.logger.info("🔄 开始模型回滚...")
            
            # 查找最新的备份
            backup_folder = self.local_models_path.parent / 'backup'
            if not backup_folder.exists():
                self.logger.error("❌ 没有找到备份文件夹")
                return False
            
            # 获取最新的备份文件夹
            backup_dirs = [d for d in backup_folder.iterdir() if d.is_dir() and d.name.startswith('models_')]
            if not backup_dirs:
                self.logger.error("❌ 没有找到模型备份")
                return False
            
            latest_backup = max(backup_dirs, key=lambda x: x.stat().st_mtime)
            self.logger.info(f"📦 使用备份: {latest_backup.name}")
            
            # 备份当前模型（以防回滚失败）
            current_backup = self.local_models_path.parent / 'backup' / f'current_before_rollback_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
            current_backup.mkdir(parents=True, exist_ok=True)
            
            for model_file in self.local_models_path.glob('*.pkl'):
                shutil.copy2(model_file, current_backup / model_file.name)
            
            # 执行回滚
            rollback_count = 0
            for backup_file in latest_backup.glob('*.pkl'):
                try:
                    target_file = self.local_models_path / backup_file.name
                    shutil.copy2(backup_file, target_file)
                    rollback_count += 1
                    self.logger.debug(f"已回滚: {backup_file.name}")
                except Exception as e:
                    self.logger.error(f"回滚失败 {backup_file.name}: {e}")
            
            self.logger.info(f"✅ 模型回滚完成: {rollback_count}个文件")
            return rollback_count > 0
            
        except Exception as e:
            self.logger.error(f"❌ 模型回滚失败: {e}")
            return False
    
    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        try:
            status = {
                'trainer_connection': self.check_trainer_connection(),
                'local_models_count': len(list(self.local_models_path.glob('*.pkl'))),
                'shared_folder_accessible': self._check_shared_folder(),
                'last_sync_time': 'unknown',
                'sync_enabled': self.ml_config['model_sync_enabled'],
                'auto_update_enabled': self.ml_config['auto_update_models']
            }
            
            # 查找最新的同步报告
            log_folder = self.local_models_path.parent / 'logs'
            if log_folder.exists():
                sync_reports = list(log_folder.glob('model_sync_*.json'))
                if sync_reports:
                    latest_report = max(sync_reports, key=lambda x: x.stat().st_mtime)
                    try:
                        with open(latest_report, 'r', encoding='utf-8') as f:
                            report_data = json.load(f)
                            status['last_sync_time'] = report_data.get('timestamp', 'unknown')
                    except:
                        pass
            
            return status
            
        except Exception as e:
            self.logger.error(f"❌ 获取同步状态失败: {e}")
            return {'error': str(e)}
