#!/usr/bin/env python3
"""
测试重新平衡决策的最小交易单位修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategies.professional_portfolio_strategy import ProfessionalPortfolioStrategy, PortfolioAction

def test_rebalance_fix():
    """测试重新平衡决策修复"""
    print("🧪 测试重新平衡决策的最小交易单位修复")
    print("="*60)

    # 创建策略实例
    strategy = ProfessionalPortfolioStrategy()

    # 模拟持仓数据（模拟之前的问题场景）
    # 需要模拟多个持仓来触发重新平衡逻辑
    positions = {
        'AUDUSD': {
            'orders': [{'order_id': '123', 'action': 'SELL', 'size': 0.03, 'entry_price': 0.64341}],
            'total_size': 0.03,
            'total_value': 0.03 * 0.64341,
            'avg_entry_price': 0.64341,
            'size': 0.03,  # 添加size字段
            'value': 0.03 * 0.64341  # 添加value字段
        },
        'USDCAD': {
            'orders': [{'order_id': '124', 'action': 'SELL', 'size': 0.01, 'entry_price': 1.38209}],
            'total_size': 0.01,
            'total_value': 0.01 * 1.38209,
            'avg_entry_price': 1.38209,
            'size': 0.01,  # 添加size字段
            'value': 0.01 * 1.38209  # 添加value字段
        }
    }

    # 模拟市场分析数据
    analyses = {
        'AUDUSD': type('Analysis', (), {
            'symbol': 'AUDUSD',
            'trend_strength': 0.3,
            'volatility': 0.001,
            'momentum': 0.2,
            'support_resistance': {},
            'correlation_score': 0.1
        })()
    }

    print("📊 测试场景:")
    print(f"   AUDUSD持仓: 0.03手")
    print(f"   USDCAD持仓: 0.01手")
    print(f"   总价值: {0.03 * 0.64341 + 0.01 * 1.38209:.5f}")
    print(f"   AUDUSD权重: {(0.03 * 0.64341) / (0.03 * 0.64341 + 0.01 * 1.38209):.1%}")
    print(f"   目标权重: 50%")
    print(f"   权重差异: {abs((0.03 * 0.64341) / (0.03 * 0.64341 + 0.01 * 1.38209) - 0.5):.1%}")
    print()

    # 执行重新平衡检查
    print("🔍 执行重新平衡检查...")
    decisions = strategy._check_rebalancing_needs(positions, analyses)

    print(f"📋 生成决策数量: {len(decisions)}")

    if decisions:
        for i, decision in enumerate(decisions, 1):
            print(f"\n📋 决策 {i}:")
            print(f"   货币对: {decision.symbol}")
            print(f"   动作: {decision.action.value}")
            print(f"   仓位: {decision.size:.3f}手")
            print(f"   置信度: {decision.confidence:.1%}")
            print(f"   说明: {decision.reasoning}")

            # 验证修复效果
            if decision.action == PortfolioAction.REBALANCE:
                abs_size = abs(decision.size)
                if abs_size >= 0.01:
                    print(f"   ✅ 修复成功: 仓位{abs_size:.3f}手 >= 0.01手最小单位")

                    # 检查是否是0.01的倍数
                    if abs_size % 0.01 == 0:
                        print(f"   ✅ 精度正确: 仓位是0.01手的倍数")
                    else:
                        print(f"   ⚠️ 精度问题: 仓位不是0.01手的倍数")
                else:
                    print(f"   ❌ 修复失败: 仓位{abs_size:.3f}手 < 0.01手最小单位")
    else:
        print("   📝 无重新平衡决策生成")

    print("\n" + "="*60)
    print("🧪 测试完成")

if __name__ == "__main__":
    test_rebalance_fix()
