@echo off
chcp 65001
echo ========================================
echo 🚀 强制设置QuantumForex Pro生产模式
echo ========================================

echo 正在设置生产环境变量...

REM 强制设置生产模式，禁用模拟
set SKIP_MT4_CONNECTION=false

REM 设置生产环境配置
set FLASK_ENV=production
set LOG_LEVEL=INFO
set DEPLOYMENT_ENV=production

echo ✅ 生产模式已强制启用
echo.
echo 📋 当前设置:
echo   - SKIP_MT4_CONNECTION=false (强制连接真实MT4服务器)
echo   - FLASK_ENV=production (生产环境)
echo   - LOG_LEVEL=INFO (信息级别日志)
echo   - DEPLOYMENT_ENV=production (部署环境)
echo.
echo 🔥 注意: 这是强制生产模式，无论市场是否开放都会尝试连接MT4
echo 🔥 系统将使用真实MT4服务器进行交易
echo 🔥 请确保MT4服务器正在运行并且可以连接
echo.
echo 现在可以启动生产系统:
echo   python main.py
echo.
echo ========================================
pause
