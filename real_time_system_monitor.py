#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实时系统监听器
监听正在运行的Pro和Trainer系统的实际状况
实时检测偏差并发出警告
"""

import sys
import os
import logging
import time
import json
import threading
import requests
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum
import psutil

class AlertLevel(Enum):
    """警告级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class SystemAlert:
    """系统警告"""
    timestamp: datetime
    component: str
    alert_type: str
    level: AlertLevel
    message: str
    details: Dict = None

class RealTimeSystemMonitor:
    """实时系统监听器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 监听状态
        self.is_monitoring = False
        self.monitor_threads = []
        
        # 警告记录
        self.alerts = []
        self.last_alerts = {}
        
        # 监听配置
        self.config = {
            'check_interval': 30,  # 30秒检查一次
            'pro_api_url': 'http://127.0.0.1:8081/api',
            'trainer_path': 'QuantumForex_MLTrainer',
            'pro_path': 'QuantumForex_Pro',
            'max_alerts_history': 1000
        }
        
        # 预期行为基准
        self.expected_behavior = {
            'pro_analysis_interval': {'min': 1800, 'max': 7200},  # 30分钟-2小时
            'pro_trading_frequency': {'max_per_hour': 3},
            'pro_risk_per_trade': {'max': 0.02},
            'trainer_check_interval': {'max': 3600},  # 1小时
            'system_cpu_threshold': 85,
            'system_memory_threshold': 85,
            'database_response_time': {'max': 5.0}  # 5秒
        }
        
        # 状态缓存
        self.last_states = {
            'pro_last_analysis': None,
            'pro_last_trade': None,
            'trainer_last_check': None,
            'last_database_check': None
        }
    
    def start_monitoring(self):
        """启动实时监听"""
        try:
            self.logger.info("🚀 启动实时系统监听器")
            self.logger.info("🔍 开始监听Pro和Trainer的实际运行状况...")
            
            self.is_monitoring = True
            
            # 启动各个监听线程
            self._start_pro_monitor()
            self._start_trainer_monitor()
            self._start_system_monitor()
            self._start_database_monitor()
            
            self.logger.info("✅ 实时监听器启动成功")
            self.logger.info("📊 监听组件:")
            self.logger.info("   - Pro系统实时状态")
            self.logger.info("   - Trainer调度状态")
            self.logger.info("   - 系统资源使用")
            self.logger.info("   - 数据库连接状态")
            
        except Exception as e:
            self.logger.error(f"❌ 启动实时监听器失败: {e}")
    
    def stop_monitoring(self):
        """停止监听"""
        try:
            self.logger.info("🛑 停止实时系统监听器")
            self.is_monitoring = False
            
            # 等待所有线程结束
            for thread in self.monitor_threads:
                if thread.is_alive():
                    thread.join(timeout=5)
            
            # 生成监听报告
            self._generate_monitoring_report()
            
            self.logger.info("✅ 实时监听器已停止")
            
        except Exception as e:
            self.logger.error(f"❌ 停止监听器失败: {e}")
    
    def _start_pro_monitor(self):
        """启动Pro系统监听"""
        def monitor_pro():
            while self.is_monitoring:
                try:
                    self._check_pro_status()
                    self._check_pro_analysis_frequency()
                    self._check_pro_trading_behavior()
                    time.sleep(self.config['check_interval'])
                except Exception as e:
                    self._add_alert("Pro监听", "监听异常", AlertLevel.ERROR, f"Pro监听线程异常: {e}")
                    time.sleep(60)
        
        thread = threading.Thread(target=monitor_pro, daemon=True)
        thread.start()
        self.monitor_threads.append(thread)
        self.logger.info("🔄 Pro系统监听线程已启动")
    
    def _start_trainer_monitor(self):
        """启动Trainer系统监听"""
        def monitor_trainer():
            while self.is_monitoring:
                try:
                    self._check_trainer_status()
                    self._check_trainer_scheduler()
                    self._check_model_updates()
                    time.sleep(self.config['check_interval'])
                except Exception as e:
                    self._add_alert("Trainer监听", "监听异常", AlertLevel.ERROR, f"Trainer监听线程异常: {e}")
                    time.sleep(60)
        
        thread = threading.Thread(target=monitor_trainer, daemon=True)
        thread.start()
        self.monitor_threads.append(thread)
        self.logger.info("🔄 Trainer系统监听线程已启动")
    
    def _start_system_monitor(self):
        """启动系统资源监听"""
        def monitor_system():
            while self.is_monitoring:
                try:
                    self._check_system_resources()
                    self._check_process_status()
                    time.sleep(self.config['check_interval'])
                except Exception as e:
                    self._add_alert("系统监听", "监听异常", AlertLevel.ERROR, f"系统监听线程异常: {e}")
                    time.sleep(60)
        
        thread = threading.Thread(target=monitor_system, daemon=True)
        thread.start()
        self.monitor_threads.append(thread)
        self.logger.info("🔄 系统资源监听线程已启动")
    
    def _start_database_monitor(self):
        """启动数据库监听"""
        def monitor_database():
            while self.is_monitoring:
                try:
                    self._check_database_connection()
                    self._check_database_performance()
                    time.sleep(self.config['check_interval'] * 2)  # 数据库检查频率低一些
                except Exception as e:
                    self._add_alert("数据库监听", "监听异常", AlertLevel.ERROR, f"数据库监听线程异常: {e}")
                    time.sleep(60)
        
        thread = threading.Thread(target=monitor_database, daemon=True)
        thread.start()
        self.monitor_threads.append(thread)
        self.logger.info("🔄 数据库监听线程已启动")
    
    def _check_pro_status(self):
        """检查Pro系统状态"""
        try:
            # 检查Pro API是否响应
            response = requests.get(f"{self.config['pro_api_url']}/health", timeout=5)
            if response.status_code == 200:
                # Pro正常运行
                pass
            else:
                self._add_alert("Pro系统", "API异常", AlertLevel.ERROR, 
                              f"Pro API响应异常: {response.status_code}")
        except requests.exceptions.ConnectionError:
            self._add_alert("Pro系统", "连接失败", AlertLevel.CRITICAL, 
                          "无法连接到Pro API服务器")
        except requests.exceptions.Timeout:
            self._add_alert("Pro系统", "响应超时", AlertLevel.WARNING, 
                          "Pro API响应超时")
        except Exception as e:
            self._add_alert("Pro系统", "状态检查异常", AlertLevel.ERROR, f"检查异常: {e}")
    
    def _check_pro_analysis_frequency(self):
        """检查Pro分析频率"""
        try:
            # 检查分析日志或状态
            # 这里需要根据实际的Pro系统实现来获取分析时间
            
            # 模拟检查分析频率
            now = datetime.now()
            if self.last_states['pro_last_analysis']:
                interval = (now - self.last_states['pro_last_analysis']).total_seconds()
                
                min_interval = self.expected_behavior['pro_analysis_interval']['min']
                max_interval = self.expected_behavior['pro_analysis_interval']['max']
                
                if interval > max_interval:
                    self._add_alert("Pro分析", "频率过低", AlertLevel.WARNING,
                                  f"分析间隔过长: {interval/60:.1f}分钟 (最大: {max_interval/60:.1f}分钟)")
                elif interval < min_interval:
                    self._add_alert("Pro分析", "频率过高", AlertLevel.WARNING,
                                  f"分析间隔过短: {interval/60:.1f}分钟 (最小: {min_interval/60:.1f}分钟)")
            
            # 更新最后分析时间（这里应该从实际系统获取）
            # self.last_states['pro_last_analysis'] = now
            
        except Exception as e:
            self._add_alert("Pro分析", "频率检查异常", AlertLevel.ERROR, f"检查异常: {e}")
    
    def _check_pro_trading_behavior(self):
        """检查Pro交易行为"""
        try:
            # 检查交易频率
            # 这里需要从实际的交易记录获取数据
            
            # 模拟检查交易行为
            current_hour_trades = 1  # 应该从实际系统获取
            max_trades_per_hour = self.expected_behavior['pro_trading_frequency']['max_per_hour']
            
            if current_hour_trades > max_trades_per_hour:
                self._add_alert("Pro交易", "交易频率过高", AlertLevel.WARNING,
                              f"当前小时交易次数: {current_hour_trades} (限制: {max_trades_per_hour})")
            
            # 检查风险控制
            # 这里应该检查实际的仓位大小和风险
            
        except Exception as e:
            self._add_alert("Pro交易", "交易检查异常", AlertLevel.ERROR, f"检查异常: {e}")
    
    def _check_trainer_status(self):
        """检查Trainer系统状态"""
        try:
            # 检查Trainer目录和关键文件
            trainer_path = self.config['trainer_path']
            
            if not os.path.exists(trainer_path):
                self._add_alert("Trainer系统", "目录不存在", AlertLevel.CRITICAL,
                              f"Trainer目录不存在: {trainer_path}")
                return
            
            # 检查调度器状态文件或进程
            # 这里需要根据实际实现来检查
            
        except Exception as e:
            self._add_alert("Trainer系统", "状态检查异常", AlertLevel.ERROR, f"检查异常: {e}")
    
    def _check_trainer_scheduler(self):
        """检查Trainer调度器"""
        try:
            # 检查调度器是否正常运行
            # 这里需要检查实际的调度器状态
            
            # 模拟检查调度器状态
            scheduler_running = True  # 应该从实际系统获取
            
            if not scheduler_running:
                self._add_alert("Trainer调度", "调度器停止", AlertLevel.ERROR,
                              "训练调度器未运行")
            
        except Exception as e:
            self._add_alert("Trainer调度", "调度检查异常", AlertLevel.ERROR, f"检查异常: {e}")
    
    def _check_model_updates(self):
        """检查模型更新"""
        try:
            # 检查模型文件的更新时间
            models_path = os.path.join(self.config['trainer_path'], 'data', 'models')
            
            if os.path.exists(models_path):
                # 获取最新模型文件的时间
                latest_model_time = None
                for file in os.listdir(models_path):
                    if file.endswith('.pkl'):
                        file_path = os.path.join(models_path, file)
                        file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                        if not latest_model_time or file_time > latest_model_time:
                            latest_model_time = file_time
                
                # 检查模型是否太久没更新
                if latest_model_time:
                    hours_since_update = (datetime.now() - latest_model_time).total_seconds() / 3600
                    if hours_since_update > 48:  # 48小时没更新
                        self._add_alert("模型更新", "更新延迟", AlertLevel.WARNING,
                                      f"模型已 {hours_since_update:.1f} 小时未更新")
            
        except Exception as e:
            self._add_alert("模型更新", "检查异常", AlertLevel.ERROR, f"检查异常: {e}")
    
    def _check_system_resources(self):
        """检查系统资源"""
        try:
            # 检查CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > self.expected_behavior['system_cpu_threshold']:
                self._add_alert("系统资源", "CPU使用率高", AlertLevel.WARNING,
                              f"CPU使用率: {cpu_percent:.1f}% (阈值: {self.expected_behavior['system_cpu_threshold']}%)")
            
            # 检查内存使用率
            memory = psutil.virtual_memory()
            if memory.percent > self.expected_behavior['system_memory_threshold']:
                self._add_alert("系统资源", "内存使用率高", AlertLevel.WARNING,
                              f"内存使用率: {memory.percent:.1f}% (阈值: {self.expected_behavior['system_memory_threshold']}%)")
            
            # 检查磁盘空间
            disk = psutil.disk_usage('/')
            if disk.percent > 90:
                self._add_alert("系统资源", "磁盘空间不足", AlertLevel.ERROR,
                              f"磁盘使用率: {disk.percent:.1f}%")
            
        except Exception as e:
            self._add_alert("系统资源", "资源检查异常", AlertLevel.ERROR, f"检查异常: {e}")
    
    def _check_process_status(self):
        """检查进程状态"""
        try:
            # 检查Python进程数量
            python_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'python' in proc.info['name'].lower():
                        python_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # 检查是否有过多的Python进程
            if len(python_processes) > 20:
                self._add_alert("进程状态", "Python进程过多", AlertLevel.WARNING,
                              f"Python进程数量: {len(python_processes)}")
            
        except Exception as e:
            self._add_alert("进程状态", "进程检查异常", AlertLevel.ERROR, f"检查异常: {e}")
    
    def _check_database_connection(self):
        """检查数据库连接"""
        try:
            # 检查数据库连接时间
            start_time = time.time()
            
            # 这里应该连接实际的数据库
            # 模拟数据库连接检查
            time.sleep(0.1)  # 模拟连接时间
            
            connection_time = time.time() - start_time
            max_response_time = self.expected_behavior['database_response_time']['max']
            
            if connection_time > max_response_time:
                self._add_alert("数据库", "响应时间长", AlertLevel.WARNING,
                              f"数据库响应时间: {connection_time:.2f}秒 (阈值: {max_response_time}秒)")
            
        except Exception as e:
            self._add_alert("数据库", "连接检查异常", AlertLevel.ERROR, f"检查异常: {e}")
    
    def _check_database_performance(self):
        """检查数据库性能"""
        try:
            # 检查数据库查询性能
            # 这里应该执行一些测试查询来检查性能
            pass
            
        except Exception as e:
            self._add_alert("数据库", "性能检查异常", AlertLevel.ERROR, f"检查异常: {e}")
    
    def _add_alert(self, component: str, alert_type: str, level: AlertLevel, message: str, details: Dict = None):
        """添加警告"""
        # 避免重复警告
        alert_key = f"{component}_{alert_type}"
        now = datetime.now()
        
        # 检查是否是重复警告（5分钟内）
        if alert_key in self.last_alerts:
            last_time = self.last_alerts[alert_key]
            if (now - last_time).total_seconds() < 300:  # 5分钟内不重复
                return
        
        # 创建警告
        alert = SystemAlert(
            timestamp=now,
            component=component,
            alert_type=alert_type,
            level=level,
            message=message,
            details=details
        )
        
        self.alerts.append(alert)
        self.last_alerts[alert_key] = now
        
        # 限制警告历史数量
        if len(self.alerts) > self.config['max_alerts_history']:
            self.alerts = self.alerts[-self.config['max_alerts_history']:]
        
        # 实时输出警告
        level_icons = {
            AlertLevel.INFO: "ℹ️",
            AlertLevel.WARNING: "⚠️",
            AlertLevel.ERROR: "❌",
            AlertLevel.CRITICAL: "🚨"
        }
        
        icon = level_icons.get(level, "📢")
        self.logger.warning(f"{icon} [{component}] {alert_type}: {message}")
    
    def _generate_monitoring_report(self):
        """生成监听报告"""
        try:
            # 统计警告
            total_alerts = len(self.alerts)
            critical_alerts = len([a for a in self.alerts if a.level == AlertLevel.CRITICAL])
            error_alerts = len([a for a in self.alerts if a.level == AlertLevel.ERROR])
            warning_alerts = len([a for a in self.alerts if a.level == AlertLevel.WARNING])
            
            # 生成报告
            report = {
                'monitoring_summary': {
                    'start_time': self.start_time.isoformat() if hasattr(self, 'start_time') else None,
                    'end_time': datetime.now().isoformat(),
                    'total_alerts': total_alerts,
                    'critical_alerts': critical_alerts,
                    'error_alerts': error_alerts,
                    'warning_alerts': warning_alerts
                },
                'recent_alerts': [
                    {
                        'timestamp': alert.timestamp.isoformat(),
                        'component': alert.component,
                        'type': alert.alert_type,
                        'level': alert.level.value,
                        'message': alert.message
                    }
                    for alert in self.alerts[-20:]  # 最近20条警告
                ]
            }
            
            # 保存报告
            report_file = f"real_time_monitoring_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            # 输出总结
            self.logger.info("=" * 80)
            self.logger.info("📊 实时监听报告")
            self.logger.info("=" * 80)
            self.logger.info(f"🚨 严重警告: {critical_alerts}")
            self.logger.info(f"❌ 错误警告: {error_alerts}")
            self.logger.info(f"⚠️ 一般警告: {warning_alerts}")
            self.logger.info(f"📄 详细报告: {report_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 生成监听报告失败: {e}")
    
    def get_current_status(self) -> Dict:
        """获取当前监听状态"""
        recent_alerts = self.alerts[-10:] if self.alerts else []
        
        return {
            'is_monitoring': self.is_monitoring,
            'total_alerts': len(self.alerts),
            'recent_alerts': [
                {
                    'timestamp': alert.timestamp.isoformat(),
                    'component': alert.component,
                    'level': alert.level.value,
                    'message': alert.message
                }
                for alert in recent_alerts
            ]
        }

def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 实时系统监听器")
    logger.info("📋 监听正在运行的Pro和Trainer系统")
    logger.info("🔍 实时检测偏差并发出警告")
    logger.info("=" * 80)
    
    try:
        # 创建监听器
        monitor = RealTimeSystemMonitor()
        monitor.start_time = datetime.now()
        
        # 启动监听
        monitor.start_monitoring()
        
        logger.info("🔍 实时监听进行中...")
        logger.info("💡 按 Ctrl+C 停止监听")
        
        try:
            while monitor.is_monitoring:
                time.sleep(10)
                
                # 每10秒显示一次状态
                status = monitor.get_current_status()
                if status['recent_alerts']:
                    logger.info(f"📊 当前状态: 总警告 {status['total_alerts']} 条")
                
        except KeyboardInterrupt:
            logger.info("👋 用户中断，正在停止监听...")
            monitor.stop_monitoring()
        
    except Exception as e:
        logger.error(f"❌ 实时监听器运行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
