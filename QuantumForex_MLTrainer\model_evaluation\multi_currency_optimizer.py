#!/usr/bin/env python3
"""
多货币回测优化器
对7种主要货币对进行回测，找出最适合的交易货币
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'QuantumForex_Pro'))

from real_backtest_engine import RealBacktestEngine, BacktestMetrics

@dataclass
class CurrencyPerformance:
    """货币对性能评估"""
    symbol: str
    model_type: str

    # 回测指标
    total_return: float
    annual_return: float
    win_rate: float
    max_drawdown: float
    sharpe_ratio: float
    prediction_accuracy: float

    # 交易统计
    total_trades: int
    avg_trade_duration: float
    profit_factor: float

    # 综合评分
    overall_score: float
    risk_adjusted_score: float

    # 推荐权重
    recommended_weight: float

class MultiCurrencyOptimizer:
    """多货币回测优化器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 支持的货币对
        self.currency_pairs = [
            'EURUSD',  # 欧元/美元 - 最大流动性
            'GBPUSD',  # 英镑/美元 - 高波动性
            'AUDUSD',  # 澳元/美元 - 商品货币
            'NZDUSD',  # 纽元/美元 - 高收益货币
            'USDCHF',  # 美元/瑞郎 - 避险货币
            'USDCAD',  # 美元/加元 - 石油相关
            'USDJPY'   # 美元/日元 - 套利交易
        ]

        # 数据库表映射
        self.db_table_mapping = {
            'EURUSD': 'min_quote_eurusd',
            'GBPUSD': 'min_quote_gbpusd',
            'AUDUSD': 'min_quote_audusd',
            'NZDUSD': 'min_quote_nzdusd',
            'USDCHF': 'min_quote_usdchf',
            'USDCAD': 'min_quote_usdcad',
            'USDJPY': 'min_quote_usdjpy'
        }

        # 评分权重
        self.score_weights = {
            'total_return': 0.25,      # 总收益率 25%
            'win_rate': 0.20,          # 胜率 20%
            'prediction_accuracy': 0.20, # 预测准确率 20%
            'sharpe_ratio': 0.15,      # 夏普比率 15%
            'max_drawdown': 0.10,      # 最大回撤 10% (负向)
            'profit_factor': 0.10      # 盈亏比 10%
        }

    def optimize_currency_allocation(self, model_path: str, days_for_backtest: int = 7) -> Dict[str, CurrencyPerformance]:
        """
        优化货币对分配
        对所有货币对进行回测，找出最适合的交易组合
        """
        try:
            self.logger.info("🌍 开始多货币回测优化...")
            self.logger.info(f"📊 测试货币对: {', '.join(self.currency_pairs)}")

            currency_performances = {}

            # 对每个货币对进行回测
            for symbol in self.currency_pairs:
                self.logger.info(f"\n🔍 回测货币对: {symbol}")

                try:
                    # 获取货币对数据
                    data = self._get_currency_data(symbol, days_for_backtest)

                    if data.empty:
                        self.logger.warning(f"❌ {symbol} 数据不足，跳过")
                        continue

                    # 运行回测
                    performance = self._backtest_currency(symbol, model_path, data)

                    if performance:
                        currency_performances[symbol] = performance
                        self.logger.info(f"✅ {symbol} 回测完成: {performance.overall_score:.3f}分")
                    else:
                        self.logger.warning(f"❌ {symbol} 回测失败")

                except Exception as e:
                    self.logger.error(f"❌ {symbol} 回测异常: {e}")
                    continue

            # 计算推荐权重
            if currency_performances:
                currency_performances = self._calculate_recommended_weights(currency_performances)

                # 保存优化结果
                self._save_currency_optimization_results(currency_performances)

                # 显示结果摘要
                self._display_optimization_summary(currency_performances)

            return currency_performances

        except Exception as e:
            self.logger.error(f"❌ 多货币优化失败: {e}")
            return {}

    def _get_currency_data(self, symbol: str, days: int) -> pd.DataFrame:
        """获取指定货币对的历史数据"""
        try:
            # 获取对应的数据库表名
            table_name = self.db_table_mapping.get(symbol)
            if not table_name:
                self.logger.error(f"❌ 不支持的货币对: {symbol}")
                return pd.DataFrame()

            try:
                from utils.db_client import execute_query

                # 计算需要的数据量
                data_limit = days * 24 * 60  # days * 24小时 * 60分钟

                # 查询SQL
                sql = f"""
                SELECT
                    time_date_str as timestamp,
                    price as open,
                    max as high,
                    min as low,
                    price as close,
                    volume
                FROM {table_name}
                ORDER BY time_min_int DESC
                LIMIT {min(data_limit, 5000)}
                """

                self.logger.info(f"📈 查询{symbol}数据: {min(data_limit, 5000)}条")

                raw_data = execute_query(sql)

                if raw_data and len(raw_data) > 100:
                    # 转换为DataFrame
                    df = pd.DataFrame(raw_data)

                    # 转换时间戳
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df = df.set_index('timestamp')

                    # 按时间正序排列
                    df = df.sort_index()

                    # 数据类型转换
                    for col in ['open', 'high', 'low', 'close', 'volume']:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                    # 移除NaN值
                    df = df.dropna()

                    # 添加symbol列
                    df['symbol'] = symbol

                    self.logger.info(f"✅ {symbol}数据获取成功: {len(df)}条")
                    return df
                else:
                    self.logger.warning(f"⚠️ {symbol}数据不足: {len(raw_data) if raw_data else 0}条")
                    return pd.DataFrame()

            except Exception as e:
                self.logger.error(f"❌ {symbol}数据库查询失败: {e}")

                # 备用方案：生成模拟数据用于测试
                if symbol == 'EURUSD':
                    self.logger.info(f"🔧 为{symbol}生成测试数据...")
                    return self._generate_test_data(symbol, days)
                else:
                    return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"❌ {symbol}数据获取失败: {e}")
            return pd.DataFrame()

    def _generate_test_data(self, symbol: str, days: int) -> pd.DataFrame:
        """生成测试数据"""
        try:
            periods = days * 24 * 12  # 5分钟数据
            dates = pd.date_range('2024-01-01', periods=periods, freq='5min')

            # 不同货币对的基础价格
            base_prices = {
                'EURUSD': 1.0800, 'GBPUSD': 1.2500, 'AUDUSD': 0.6500,
                'NZDUSD': 0.6000, 'USDCHF': 0.9200, 'USDCAD': 1.3500,
                'USDJPY': 150.00
            }

            base_price = base_prices.get(symbol, 1.0000)

            # 生成价格序列
            np.random.seed(42)
            returns = np.random.normal(0, 0.0001, periods)

            prices = [base_price]
            for i in range(periods - 1):
                new_price = prices[-1] * (1 + returns[i])
                prices.append(max(0.1, new_price))

            # 生成OHLC数据
            data = []
            for i in range(1, len(prices)):
                open_price = prices[i-1]
                close_price = prices[i]

                volatility = abs(returns[i-1]) * 5
                high_price = max(open_price, close_price) + volatility
                low_price = min(open_price, close_price) - volatility

                data.append({
                    'timestamp': dates[i],
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': np.random.randint(1000, 5000),
                    'symbol': symbol
                })

            df = pd.DataFrame(data)
            df.set_index('timestamp', inplace=True)

            return df

        except Exception as e:
            self.logger.error(f"❌ {symbol}测试数据生成失败: {e}")
            return pd.DataFrame()

    def _backtest_currency(self, symbol: str, model_path: str, data: pd.DataFrame) -> Optional[CurrencyPerformance]:
        """对单个货币对进行回测"""
        try:
            # 创建回测引擎
            backtest_engine = RealBacktestEngine()

            # 运行回测 (使用自定义数据)
            metrics = self._run_custom_backtest(backtest_engine, model_path, data)

            if not metrics:
                return None

            # 计算综合评分
            overall_score = self._calculate_currency_score(metrics)
            risk_adjusted_score = overall_score * (1 - metrics.max_drawdown)

            # 创建性能记录
            performance = CurrencyPerformance(
                symbol=symbol,
                model_type=os.path.basename(model_path),
                total_return=metrics.total_return,
                annual_return=metrics.annual_return,
                win_rate=metrics.win_rate,
                max_drawdown=metrics.max_drawdown,
                sharpe_ratio=metrics.sharpe_ratio,
                prediction_accuracy=metrics.prediction_accuracy,
                total_trades=metrics.total_trades,
                avg_trade_duration=0,  # 简化
                profit_factor=metrics.profit_factor,
                overall_score=overall_score,
                risk_adjusted_score=risk_adjusted_score,
                recommended_weight=0  # 稍后计算
            )

            return performance

        except Exception as e:
            self.logger.error(f"❌ {symbol}回测失败: {e}")
            return None

    def _run_custom_backtest(self, engine, model_path: str, data: pd.DataFrame):
        """运行自定义回测"""
        try:
            # 加载模型
            model, scaler = engine._load_model(model_path)
            if model is None:
                return None

            # 生成预测
            predictions = engine._generate_model_predictions(model, scaler, data)
            if not predictions:
                return None

            # 验证预测准确性
            prediction_accuracy = engine._validate_prediction_accuracy(predictions, data)

            # 模拟交易
            trades = engine._simulate_trading(predictions, data)

            # 计算性能指标
            metrics = engine._calculate_comprehensive_metrics(
                trades, data, prediction_accuracy
            )

            return metrics

        except Exception as e:
            self.logger.error(f"❌ 自定义回测失败: {e}")
            return None

    def _calculate_currency_score(self, metrics: BacktestMetrics) -> float:
        """计算货币对评分"""
        try:
            score = 0.0

            # 总收益率
            return_score = max(0, min(1, metrics.total_return * 10))
            score += return_score * self.score_weights['total_return']

            # 胜率
            win_rate_score = metrics.win_rate
            score += win_rate_score * self.score_weights['win_rate']

            # 预测准确率
            accuracy_score = metrics.prediction_accuracy
            score += accuracy_score * self.score_weights['prediction_accuracy']

            # 夏普比率
            sharpe_score = max(0, min(1, metrics.sharpe_ratio / 2))
            score += sharpe_score * self.score_weights['sharpe_ratio']

            # 最大回撤 (负向指标)
            drawdown_score = max(0, 1 - metrics.max_drawdown * 5)
            score += drawdown_score * self.score_weights['max_drawdown']

            # 盈亏比
            pf_score = max(0, min(1, (metrics.profit_factor - 1) / 2))
            score += pf_score * self.score_weights['profit_factor']

            return min(1.0, score)

        except Exception as e:
            self.logger.error(f"❌ 评分计算失败: {e}")
            return 0.0

    def _calculate_recommended_weights(self, performances: Dict[str, CurrencyPerformance]) -> Dict[str, CurrencyPerformance]:
        """计算推荐权重"""
        try:
            # 基于风险调整后评分计算权重
            total_risk_adjusted_score = sum(p.risk_adjusted_score for p in performances.values())

            if total_risk_adjusted_score > 0:
                for symbol, performance in performances.items():
                    # 基础权重
                    base_weight = performance.risk_adjusted_score / total_risk_adjusted_score

                    # 调整权重 (避免过度集中)
                    adjusted_weight = min(base_weight, 0.4)  # 单个货币对最多40%

                    performance.recommended_weight = adjusted_weight
            else:
                # 如果所有评分都很低，平均分配
                equal_weight = 1.0 / len(performances)
                for performance in performances.values():
                    performance.recommended_weight = equal_weight

            # 重新标准化权重
            total_weight = sum(p.recommended_weight for p in performances.values())
            if total_weight > 0:
                for performance in performances.values():
                    performance.recommended_weight /= total_weight

            return performances

        except Exception as e:
            self.logger.error(f"❌ 权重计算失败: {e}")
            return performances

    def _display_optimization_summary(self, performances: Dict[str, CurrencyPerformance]):
        """显示优化结果摘要"""
        try:
            print(f"\n{'='*80}")
            print("🌍 多货币回测优化结果")
            print(f"{'='*80}")

            # 按评分排序
            sorted_performances = sorted(
                performances.items(),
                key=lambda x: x[1].overall_score,
                reverse=True
            )

            print(f"📊 货币对性能排名:")
            print(f"{'排名':<4} {'货币对':<8} {'综合评分':<10} {'收益率':<10} {'胜率':<8} {'回撤':<8} {'推荐权重':<10}")
            print("-" * 80)

            for i, (symbol, perf) in enumerate(sorted_performances, 1):
                print(f"{i:<4} {symbol:<8} {perf.overall_score:<10.3f} "
                      f"{perf.total_return:<10.1%} {perf.win_rate:<8.1%} "
                      f"{perf.max_drawdown:<8.1%} {perf.recommended_weight:<10.1%}")

            # 推荐交易组合
            print(f"\n💡 推荐交易组合:")
            recommended_pairs = [symbol for symbol, perf in sorted_performances
                               if perf.overall_score >= 0.3 and perf.recommended_weight >= 0.05]

            if recommended_pairs:
                print(f"   优先交易: {', '.join(recommended_pairs[:3])}")
                print(f"   备选交易: {', '.join(recommended_pairs[3:])}")

                # 风险评估
                total_recommended_weight = sum(performances[symbol].recommended_weight
                                             for symbol in recommended_pairs)
                avg_drawdown = sum(performances[symbol].max_drawdown * performances[symbol].recommended_weight
                                 for symbol in recommended_pairs) / total_recommended_weight if total_recommended_weight > 0 else 0

                print(f"   组合预期回撤: {avg_drawdown:.1%}")
                print(f"   建议资金分配: {total_recommended_weight:.1%}")
            else:
                print("   ⚠️ 没有找到符合标准的推荐货币对")
                print("   💡 建议重新训练模型或调整策略参数")

        except Exception as e:
            self.logger.error(f"❌ 结果显示失败: {e}")

    def _save_currency_optimization_results(self, performances: Dict[str, CurrencyPerformance]):
        """保存货币优化结果"""
        try:
            # 创建结果目录
            results_dir = Path("logs/currency_optimization")
            results_dir.mkdir(parents=True, exist_ok=True)

            # 生成结果文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = results_dir / f"currency_optimization_{timestamp}.json"

            # 准备结果数据
            results = {
                'timestamp': datetime.now().isoformat(),
                'optimization_type': 'multi_currency_backtest',
                'total_currencies': len(performances),
                'currencies': {}
            }

            for symbol, perf in performances.items():
                results['currencies'][symbol] = {
                    'overall_score': perf.overall_score,
                    'risk_adjusted_score': perf.risk_adjusted_score,
                    'total_return': perf.total_return,
                    'annual_return': perf.annual_return,
                    'win_rate': perf.win_rate,
                    'max_drawdown': perf.max_drawdown,
                    'sharpe_ratio': perf.sharpe_ratio,
                    'prediction_accuracy': perf.prediction_accuracy,
                    'total_trades': perf.total_trades,
                    'profit_factor': perf.profit_factor,
                    'recommended_weight': perf.recommended_weight
                }

            # 保存结果
            import json
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)

            self.logger.info(f"📄 货币优化结果已保存: {result_file}")

            # 创建当前推荐配置
            config_file = results_dir / "current_currency_allocation.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)

            self.logger.info(f"📄 当前货币分配配置: {config_file}")

        except Exception as e:
            self.logger.error(f"❌ 保存货币优化结果失败: {e}")

def run_multi_currency_optimization():
    """运行多货币优化"""
    print("🌍 多货币回测优化系统")
    print("="*60)
    print("🎯 对7种主要货币对进行回测，找出最适合的交易组合")

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    try:
        # 查找最新的兼容模型
        models_dir = Path("data/models")
        if not models_dir.exists():
            print("❌ 模型目录不存在")
            return False

        # 查找价格预测模型 (排除scaler文件)
        price_models = list(models_dir.glob("*price_prediction*compatible*.pkl"))
        price_models = [f for f in price_models if 'scaler' not in f.name]

        if not price_models:
            print("❌ 没有找到兼容的价格预测模型")
            return False

        # 选择最新的模型
        latest_model = max(price_models, key=lambda x: x.stat().st_mtime)
        print(f"📦 使用模型: {latest_model.name}")

        # 创建优化器
        optimizer = MultiCurrencyOptimizer()

        # 运行多货币优化
        performances = optimizer.optimize_currency_allocation(
            model_path=str(latest_model),
            days_for_backtest=7
        )

        if performances:
            print(f"\n🎉 多货币优化完成！")
            print(f"✅ 成功评估{len(performances)}个货币对")
            print(f"💡 Pro系统现在可以根据回测结果选择最优货币对交易")
            return True
        else:
            print(f"\n❌ 多货币优化失败！")
            print(f"💡 请检查数据连接和模型文件")
            return False

    except Exception as e:
        print(f"❌ 多货币优化系统失败: {e}")
        return False

if __name__ == "__main__":
    run_multi_currency_optimization()
