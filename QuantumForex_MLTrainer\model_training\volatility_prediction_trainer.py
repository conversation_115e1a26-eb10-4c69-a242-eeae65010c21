"""
QuantumForex MLTrainer 波动率预测模型训练器
训练波动率预测和分类模型
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Any
from sklearn.preprocessing import LabelEncoder

from .base_trainer import BaseModelTrainer

class VolatilityPredictionTrainer(BaseModelTrainer):
    """波动率预测模型训练器"""

    def __init__(self):
        super().__init__('regression')  # 波动率预测主要是回归问题
        self.logger = logging.getLogger(__name__)

        # 波动率分析配置
        self.volatility_windows = [5, 10, 20, 50]  # 波动率计算窗口
        self.volatility_horizons = [5, 10, 20]     # 预测时间范围

    def calculate_volatility(self, df: pd.DataFrame, window: int = 20, method: str = 'std') -> pd.Series:
        """计算波动率"""
        try:
            if method == 'std':
                # 标准差波动率
                returns = df['close'].pct_change()
                volatility = returns.rolling(window=window).std() * np.sqrt(window)
                
            elif method == 'atr':
                # 平均真实范围（ATR）
                high_low = df['high'] - df['low']
                high_close = abs(df['high'] - df['close'].shift(1))
                low_close = abs(df['low'] - df['close'].shift(1))
                
                true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                volatility = true_range.rolling(window=window).mean()
                
            elif method == 'garch':
                # 简化的GARCH波动率
                returns = df['close'].pct_change()
                volatility = returns.rolling(window=window).std()
                
                # 简单的GARCH效应
                for i in range(1, len(volatility)):
                    if not pd.isna(volatility.iloc[i-1]):
                        volatility.iloc[i] = 0.1 * returns.iloc[i]**2 + 0.9 * volatility.iloc[i-1]
                        
            else:
                raise ValueError(f"不支持的波动率计算方法: {method}")
                
            return volatility
            
        except Exception as e:
            self.logger.error(f"❌ 计算波动率失败: {e}")
            return pd.Series()

    def create_volatility_target(self, df: pd.DataFrame, method: str = 'future_volatility',
                                window: int = 20, horizon: int = 5) -> pd.Series:
        """创建波动率预测目标变量"""
        try:
            self.logger.info(f"📊 创建波动率目标变量: {method}, 窗口={window}, 预测范围={horizon}")

            if method == 'future_volatility':
                # 未来波动率预测
                current_volatility = self.calculate_volatility(df, window, 'std')
                future_volatility = current_volatility.shift(-horizon)
                target = future_volatility
                
            elif method == 'volatility_change':
                # 波动率变化预测
                current_volatility = self.calculate_volatility(df, window, 'std')
                future_volatility = current_volatility.shift(-horizon)
                volatility_change = (future_volatility - current_volatility) / current_volatility
                target = volatility_change
                
            elif method == 'volatility_regime':
                # 波动率状态分类（低/中/高波动率）
                volatility = self.calculate_volatility(df, window, 'std')
                future_volatility = volatility.shift(-horizon)
                
                # 计算波动率分位数
                vol_25 = volatility.quantile(0.25)
                vol_75 = volatility.quantile(0.75)
                
                target = pd.Series(index=df.index, dtype=int)
                target[future_volatility <= vol_25] = 0  # 低波动率
                target[(future_volatility > vol_25) & (future_volatility <= vol_75)] = 1  # 中波动率
                target[future_volatility > vol_75] = 2  # 高波动率
                
            elif method == 'volatility_spike':
                # 波动率突增预测
                target = self._create_volatility_spike_target(df, window, horizon)
                
            else:
                raise ValueError(f"不支持的波动率目标方法: {method}")

            # 删除NaN值
            target = target.dropna()

            self.logger.info(f"✅ 波动率目标变量创建完成: {len(target)}个样本")
            if method == 'volatility_regime' or method == 'volatility_spike':
                self.logger.info(f"📊 类别分布: {target.value_counts().to_dict()}")
            else:
                self.logger.info(f"📊 目标统计: 均值={target.mean():.6f}, 标准差={target.std():.6f}")

            return target

        except Exception as e:
            self.logger.error(f"❌ 创建波动率目标变量失败: {e}")
            raise

    def _create_volatility_spike_target(self, df: pd.DataFrame, window: int, horizon: int) -> pd.Series:
        """创建波动率突增目标变量"""
        try:
            # 计算当前和未来波动率
            current_volatility = self.calculate_volatility(df, window, 'std')
            future_volatility = current_volatility.shift(-horizon)
            
            # 计算波动率变化倍数
            volatility_ratio = future_volatility / current_volatility
            
            target = pd.Series(index=df.index, dtype=int)
            target[volatility_ratio > 1.5] = 1   # 波动率突增
            target[volatility_ratio < 0.7] = -1  # 波动率下降
            target[(volatility_ratio >= 0.7) & (volatility_ratio <= 1.5)] = 0  # 波动率稳定
            
            return target

        except Exception as e:
            self.logger.error(f"❌ 创建波动率突增目标变量失败: {e}")
            return pd.Series()

    def train_volatility_prediction_model(self, df: pd.DataFrame, feature_columns: List[str],
                                        window: int = 20, horizon: int = 5) -> Dict[str, Any]:
        """训练波动率预测模型"""
        try:
            self.logger.info(f"🧠 训练波动率预测模型: 窗口={window}, 预测范围={horizon}")

            # 创建目标变量
            target = self.create_volatility_target(df, 'future_volatility', window, horizon)
            target.name = 'target'

            # 对齐数据
            common_index = df.index.intersection(target.index)
            df_aligned = df.loc[common_index].copy()
            target_aligned = target.loc[common_index]

            # 添加目标列到DataFrame
            df_aligned['target'] = target_aligned

            # 准备数据
            X, y, feature_names = self.prepare_data(df_aligned, 'target', feature_columns)

            # 分割数据
            X_train, X_test, y_train, y_test = self.split_data(X, y)

            # 特征标准化
            X_train_scaled, X_test_scaled = self.scale_features(X_train, X_test, f'volatility_{window}_{horizon}')

            # 训练模型
            results = self.train_models(X_train_scaled, y_train, X_test_scaled, y_test, feature_names)

            # 获取最佳模型
            best_model_name, best_model = self.get_best_model(results, 'r2_score')

            # 保存模型
            model_prefix = f'volatility_prediction_{window}min_{horizon}min'
            self.save_models(model_prefix)

            # 添加训练记录
            training_record = {
                'model_type': 'volatility_prediction',
                'window': window,
                'horizon': horizon,
                'best_model': best_model_name,
                'results': {name: result['metrics'] for name, result in results.items()},
                'feature_count': len(feature_names),
                'sample_count': len(X)
            }
            self.training_history.append(training_record)

            return results

        except Exception as e:
            self.logger.error(f"❌ 训练波动率预测模型失败: {e}")
            return {}

    def train_volatility_regime_model(self, df: pd.DataFrame, feature_columns: List[str],
                                    window: int = 20, horizon: int = 5) -> Dict[str, Any]:
        """训练波动率状态分类模型"""
        try:
            self.logger.info(f"🧠 训练波动率状态分类模型: 窗口={window}, 预测范围={horizon}")

            # 创建目标变量
            target = self.create_volatility_target(df, 'volatility_regime', window, horizon)
            target.name = 'target'

            # 对齐数据
            common_index = df.index.intersection(target.index)
            df_aligned = df.loc[common_index].copy()
            target_aligned = target.loc[common_index]

            # 添加目标列到DataFrame
            df_aligned['target'] = target_aligned

            # 准备数据
            X, y, feature_names = self.prepare_data(df_aligned, 'target', feature_columns)

            # 分割数据
            X_train, X_test, y_train, y_test = self.split_data(X, y)

            # 特征标准化
            X_train_scaled, X_test_scaled = self.scale_features(X_train, X_test, f'volatility_regime_{window}_{horizon}')

            # 训练模型
            results = self.train_models(X_train_scaled, y_train, X_test_scaled, y_test, feature_names)

            # 获取最佳模型
            best_model_name, best_model = self.get_best_model(results, 'f1_score')

            # 保存模型
            model_prefix = f'volatility_prediction_regime_{window}min_{horizon}min'
            self.save_models(model_prefix)

            return results

        except Exception as e:
            self.logger.error(f"❌ 训练波动率状态分类模型失败: {e}")
            return {}

    def train_all_volatility_models(self, df: pd.DataFrame, feature_columns: List[str]) -> Dict[str, Any]:
        """训练所有波动率预测模型"""
        try:
            self.logger.info("🚀 开始训练所有波动率预测模型...")

            all_results = {}

            # 训练波动率预测模型
            for window in [10, 20]:
                for horizon in [5, 10]:
                    try:
                        results = self.train_volatility_prediction_model(df, feature_columns, window, horizon)
                        all_results[f'prediction_{window}_{horizon}'] = results
                    except Exception as e:
                        self.logger.error(f"训练波动率预测模型失败 (window={window}, horizon={horizon}): {e}")

            # 训练波动率状态分类模型
            for window in [20]:
                for horizon in [5, 10]:
                    try:
                        results = self.train_volatility_regime_model(df, feature_columns, window, horizon)
                        all_results[f'regime_{window}_{horizon}'] = results
                    except Exception as e:
                        self.logger.error(f"训练波动率状态模型失败 (window={window}, horizon={horizon}): {e}")

            self.logger.info(f"🎉 所有波动率预测模型训练完成: {len(all_results)}个模型组合")
            return all_results

        except Exception as e:
            self.logger.error(f"❌ 训练所有波动率预测模型失败: {e}")
            return {}

# 创建波动率预测训练器实例
volatility_trainer = VolatilityPredictionTrainer()
