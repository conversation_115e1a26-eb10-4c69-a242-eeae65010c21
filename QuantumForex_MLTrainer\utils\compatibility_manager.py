"""
QuantumForex MLTrainer 兼容性管理器
确保新训练的模型与现有系统兼容，支持平滑过渡
"""

import os
import json
import joblib
import shutil
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

@dataclass
class ModelMetadata:
    """模型元数据"""
    name: str
    version: str
    created_at: str
    model_type: str
    features: List[str]
    performance_metrics: Dict[str, float]
    compatibility_version: str
    file_path: str
    file_size: int
    checksum: str

class CompatibilityManager:
    """兼容性管理器"""
    
    def __init__(self, shared_folder: str = r'C:\QuantumForex_Shared'):
        self.shared_folder = Path(shared_folder)
        self.models_folder = self.shared_folder / 'models'
        self.backup_folder = self.shared_folder / 'backup'
        self.metadata_file = self.models_folder / 'model_metadata.json'
        
        # 创建必要的文件夹
        self.models_folder.mkdir(parents=True, exist_ok=True)
        self.backup_folder.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 兼容性版本
        self.COMPATIBILITY_VERSION = "2.0.0"
        self.LEGACY_VERSIONS = ["1.0.0", "1.1.0", "1.2.0"]
        
        # 支持的模型类型
        self.SUPPORTED_MODEL_TYPES = [
            'price_prediction',
            'risk_assessment', 
            'trend_classification',
            'volatility_prediction'
        ]
    
    def initialize_compatibility_system(self) -> bool:
        """初始化兼容性系统"""
        try:
            self.logger.info("🔧 初始化兼容性管理系统...")
            
            # 创建共享文件夹结构
            self._create_folder_structure()
            
            # 检查现有模型
            existing_models = self._scan_existing_models()
            
            # 创建或更新元数据文件
            self._initialize_metadata(existing_models)
            
            # 备份现有模型
            self._backup_existing_models(existing_models)
            
            self.logger.info("✅ 兼容性管理系统初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 兼容性系统初始化失败: {e}")
            return False
    
    def _create_folder_structure(self):
        """创建文件夹结构"""
        folders = [
            'models/current',      # 当前使用的模型
            'models/new',          # 新训练的模型
            'models/legacy',       # 旧版本模型
            'backup/models',       # 模型备份
            'backup/metadata',     # 元数据备份
            'logs',               # 日志文件
            'temp'                # 临时文件
        ]
        
        for folder in folders:
            folder_path = self.shared_folder / folder
            folder_path.mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"创建文件夹: {folder_path}")
    
    def _scan_existing_models(self) -> List[str]:
        """扫描现有模型文件"""
        existing_models = []
        
        # 扫描交易端可能的模型位置
        possible_locations = [
            r'../QuantumForex_Pro/data/models',
            self.models_folder / 'current',
            self.models_folder / 'legacy'
        ]
        
        for location in possible_locations:
            location_path = Path(location)
            if location_path.exists():
                for model_file in location_path.glob('*.pkl'):
                    existing_models.append(str(model_file))
                    self.logger.info(f"发现现有模型: {model_file}")
        
        return existing_models
    
    def _initialize_metadata(self, existing_models: List[str]):
        """初始化元数据文件"""
        metadata = {
            'compatibility_version': self.COMPATIBILITY_VERSION,
            'last_updated': datetime.now().isoformat(),
            'models': {},
            'legacy_models': {},
            'migration_history': []
        }
        
        # 为现有模型创建元数据
        for model_path in existing_models:
            model_name = Path(model_path).stem
            metadata['legacy_models'][model_name] = {
                'original_path': model_path,
                'backed_up_at': datetime.now().isoformat(),
                'compatibility_status': 'legacy',
                'migration_required': True
            }
        
        # 保存元数据
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"元数据文件已创建: {self.metadata_file}")
    
    def _backup_existing_models(self, existing_models: List[str]):
        """备份现有模型"""
        backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = self.backup_folder / f'models_{backup_timestamp}'
        backup_dir.mkdir(exist_ok=True)
        
        for model_path in existing_models:
            try:
                model_file = Path(model_path)
                if model_file.exists():
                    backup_path = backup_dir / model_file.name
                    shutil.copy2(model_file, backup_path)
                    self.logger.info(f"模型已备份: {model_file.name} → {backup_path}")
            except Exception as e:
                self.logger.error(f"备份模型失败 {model_path}: {e}")
    
    def register_new_model(self, model_path: str, model_type: str, 
                          features: List[str], performance_metrics: Dict[str, float],
                          version: str = None) -> bool:
        """注册新训练的模型"""
        try:
            model_file = Path(model_path)
            if not model_file.exists():
                raise FileNotFoundError(f"模型文件不存在: {model_path}")
            
            # 生成版本号
            if not version:
                version = self._generate_version()
            
            # 计算文件校验和
            checksum = self._calculate_checksum(model_path)
            
            # 创建模型元数据
            metadata = ModelMetadata(
                name=model_file.stem,
                version=version,
                created_at=datetime.now().isoformat(),
                model_type=model_type,
                features=features,
                performance_metrics=performance_metrics,
                compatibility_version=self.COMPATIBILITY_VERSION,
                file_path=str(model_file),
                file_size=model_file.stat().st_size,
                checksum=checksum
            )
            
            # 复制模型到新模型文件夹
            new_model_path = self.models_folder / 'new' / f"{metadata.name}_{version}.pkl"
            shutil.copy2(model_path, new_model_path)
            
            # 更新元数据文件
            self._update_metadata(metadata)
            
            self.logger.info(f"✅ 新模型已注册: {metadata.name} v{version}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 注册新模型失败: {e}")
            return False
    
    def deploy_model(self, model_name: str, version: str = None) -> bool:
        """部署模型到交易端"""
        try:
            # 获取模型信息
            model_info = self._get_model_info(model_name, version)
            if not model_info:
                raise ValueError(f"找不到模型: {model_name} v{version}")
            
            # 检查兼容性
            if not self._check_compatibility(model_info):
                raise ValueError(f"模型不兼容: {model_name}")
            
            # 备份当前模型
            self._backup_current_model(model_name)
            
            # 复制新模型到当前模型文件夹
            source_path = Path(model_info['file_path'])
            target_path = self.models_folder / 'current' / f"{model_name}.pkl"
            
            shutil.copy2(source_path, target_path)
            
            # 创建兼容性符号链接（如果交易端需要特定路径）
            self._create_compatibility_links(model_name, target_path)
            
            # 更新部署状态
            self._update_deployment_status(model_name, version, 'deployed')
            
            self.logger.info(f"✅ 模型部署成功: {model_name} v{version}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 模型部署失败: {e}")
            return False
    
    def rollback_model(self, model_name: str) -> bool:
        """回滚到上一个版本"""
        try:
            # 获取上一个版本信息
            previous_version = self._get_previous_version(model_name)
            if not previous_version:
                raise ValueError(f"没有可回滚的版本: {model_name}")
            
            # 执行回滚
            success = self.deploy_model(model_name, previous_version)
            
            if success:
                self.logger.info(f"✅ 模型回滚成功: {model_name} → v{previous_version}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ 模型回滚失败: {e}")
            return False
    
    def _generate_version(self) -> str:
        """生成版本号"""
        # 简单的版本生成策略
        timestamp = datetime.now().strftime('%Y%m%d%H%M')
        return f"2.1.{timestamp}"
    
    def _calculate_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        import hashlib
        
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _update_metadata(self, metadata: ModelMetadata):
        """更新元数据文件"""
        # 读取现有元数据
        if self.metadata_file.exists():
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        else:
            data = {'models': {}, 'legacy_models': {}}
        
        # 添加新模型信息
        data['models'][f"{metadata.name}_{metadata.version}"] = {
            'name': metadata.name,
            'version': metadata.version,
            'created_at': metadata.created_at,
            'model_type': metadata.model_type,
            'features': metadata.features,
            'performance_metrics': metadata.performance_metrics,
            'compatibility_version': metadata.compatibility_version,
            'file_path': metadata.file_path,
            'file_size': metadata.file_size,
            'checksum': metadata.checksum,
            'status': 'ready'
        }
        
        data['last_updated'] = datetime.now().isoformat()
        
        # 保存更新的元数据
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def _get_model_info(self, model_name: str, version: str = None) -> Optional[Dict]:
        """获取模型信息"""
        if not self.metadata_file.exists():
            return None
        
        with open(self.metadata_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        models = data.get('models', {})
        
        if version:
            key = f"{model_name}_{version}"
            return models.get(key)
        else:
            # 返回最新版本
            matching_models = {k: v for k, v in models.items() if v['name'] == model_name}
            if matching_models:
                latest_key = max(matching_models.keys(), key=lambda x: matching_models[x]['created_at'])
                return matching_models[latest_key]
        
        return None
    
    def _check_compatibility(self, model_info: Dict) -> bool:
        """检查模型兼容性"""
        model_version = model_info.get('compatibility_version', '1.0.0')
        
        # 检查版本兼容性
        if model_version == self.COMPATIBILITY_VERSION:
            return True
        elif model_version in self.LEGACY_VERSIONS:
            self.logger.warning(f"使用旧版本模型: {model_version}")
            return True
        else:
            self.logger.error(f"不兼容的模型版本: {model_version}")
            return False
    
    def _backup_current_model(self, model_name: str):
        """备份当前模型"""
        current_model_path = self.models_folder / 'current' / f"{model_name}.pkl"
        
        if current_model_path.exists():
            backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = self.backup_folder / f"{model_name}_backup_{backup_timestamp}.pkl"
            shutil.copy2(current_model_path, backup_path)
            self.logger.info(f"当前模型已备份: {backup_path}")
    
    def _create_compatibility_links(self, model_name: str, target_path: Path):
        """创建兼容性链接"""
        # 为交易端创建预期的文件路径
        legacy_paths = [
            self.shared_folder / f"{model_name}.pkl",
            self.models_folder / f"{model_name}.pkl"
        ]
        
        for legacy_path in legacy_paths:
            try:
                if legacy_path.exists():
                    legacy_path.unlink()  # 删除现有文件
                
                # 创建硬链接或复制文件
                shutil.copy2(target_path, legacy_path)
                self.logger.debug(f"创建兼容性链接: {legacy_path}")
                
            except Exception as e:
                self.logger.warning(f"创建兼容性链接失败 {legacy_path}: {e}")
    
    def _update_deployment_status(self, model_name: str, version: str, status: str):
        """更新部署状态"""
        if not self.metadata_file.exists():
            return
        
        with open(self.metadata_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        key = f"{model_name}_{version}"
        if key in data.get('models', {}):
            data['models'][key]['status'] = status
            data['models'][key]['deployed_at'] = datetime.now().isoformat()
            
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
    
    def _get_previous_version(self, model_name: str) -> Optional[str]:
        """获取上一个版本"""
        if not self.metadata_file.exists():
            return None
        
        with open(self.metadata_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        models = data.get('models', {})
        matching_models = {k: v for k, v in models.items() if v['name'] == model_name and v.get('status') == 'deployed'}
        
        if len(matching_models) >= 2:
            # 按时间排序，返回倒数第二个
            sorted_models = sorted(matching_models.items(), key=lambda x: x[1]['created_at'], reverse=True)
            return sorted_models[1][1]['version']
        
        return None
    
    def get_model_status(self) -> Dict:
        """获取所有模型状态"""
        if not self.metadata_file.exists():
            return {}
        
        with open(self.metadata_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return {
            'compatibility_version': data.get('compatibility_version'),
            'last_updated': data.get('last_updated'),
            'total_models': len(data.get('models', {})),
            'legacy_models': len(data.get('legacy_models', {})),
            'models': data.get('models', {})
        }
