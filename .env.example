# 外汇交易系统环境配置示例
# 复制此文件为 .env.local 并填入实际值

# 应用配置
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-secret-key-here

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=pizza_quotes
DB_USER=your-db-username
DB_PASSWORD=your-db-password

# LLM API配置
LLM_API_URL=https://api.siliconflow.cn/v1/chat/completions
LLM_API_KEY=your-llm-api-key
LLM_MODEL=Qwen/Qwen2.5-72B-Instruct

# MT4连接配置
MT4_SERVER_HOST=127.0.0.1
MT4_SERVER_PORT=5555
MT4_CONNECTION_TIMEOUT=30

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/forex_system.log
ERROR_LOG_PATH=logs/error_log.json

# 安全配置
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:3000,http://localhost:5000

# 性能配置
MAX_WORKERS=2
REQUEST_TIMEOUT=120
ANALYSIS_CACHE_TTL=300

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 部署配置
DEPLOYMENT_ENV=production
SERVER_NAME=forex-trading-system
