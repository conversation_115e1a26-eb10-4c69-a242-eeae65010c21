#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web版交易系统可视化仪表板
使用Flask提供Web界面
"""

import os
import sys
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from flask import Flask, render_template_string, jsonify
import random

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__)

# 全局数据存储
dashboard_data = {
    'market_data': {},
    'analysis_history': [],
    'system_status': {
        'last_update': None,
        'components_status': {},
        'error_count': 0,
        'success_count': 0,
        'start_time': datetime.now()
    },
    'monitoring': False
}

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外汇交易系统 - 实时监控仪表板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid #00ff00;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .panel {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .panel h2 {
            color: #00ff00;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #00ff00;
            padding-bottom: 5px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-online {
            color: #00ff00;
            font-weight: bold;
        }

        .status-offline {
            color: #ff6666;
            font-weight: bold;
        }

        .market-data {
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
        }

        .analysis-log {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            padding: 15px;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            border: 1px solid #00ff00;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-start {
            background: #4CAF50;
            color: white;
        }

        .btn-stop {
            background: #ff9800;
            color: white;
        }

        .btn-refresh {
            background: #2196F3;
            color: white;
        }

        .btn-test {
            background: #FF9800;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .stat-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #00ff00;
        }

        .footer {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px 20px;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
            }

            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 外汇交易系统实时监控仪表板</h1>
        <p>实时监控 | 智能分析 | 风险管理</p>
    </div>

    <div class="container">
        <!-- 系统状态面板 -->
        <div class="panel">
            <h2>📊 系统状态</h2>
            <div id="system-status">
                <!-- 系统状态将通过JavaScript更新 -->
            </div>

            <div class="stats-grid">
                <div class="stat-item">
                    <div>成功分析</div>
                    <div class="stat-value" id="success-count">0</div>
                </div>
                <div class="stat-item">
                    <div>错误次数</div>
                    <div class="stat-value" id="error-count">0</div>
                </div>
                <div class="stat-item">
                    <div>运行时间</div>
                    <div class="stat-value" id="uptime">00:00:00</div>
                </div>
                <div class="stat-item">
                    <div>监控状态</div>
                    <div class="stat-value" id="monitoring-status">待机</div>
                </div>
            </div>
        </div>

        <!-- 主要内容面板 -->
        <div class="panel">
            <h2>📈 市场数据与分析</h2>

            <!-- 市场数据 -->
            <div class="market-data">
                <h3 style="color: #00ff00; margin-bottom: 10px;">实时市场数据</h3>
                <div id="market-data">
                    <!-- 市场数据将通过JavaScript更新 -->
                </div>
            </div>

            <!-- 分析日志 -->
            <h3 style="color: #00ff00; margin: 20px 0 10px 0;">分析日志</h3>
            <div class="analysis-log" id="analysis-log">
                等待分析结果...
            </div>

            <!-- 控制按钮 -->
            <div class="controls">
                <button class="btn btn-start" onclick="toggleMonitoring()">▶️ 启动监控</button>
                <button class="btn btn-refresh" onclick="refreshData()">🔄 刷新数据</button>
                <button class="btn btn-test" onclick="runTest()">🧪 测试分析</button>
                <button class="btn btn-refresh" onclick="clearLogs()">🗑️ 清空日志</button>
            </div>
        </div>

        <!-- 详细信息面板 -->
        <div class="panel">
            <h2>💡 系统信息</h2>
            <div id="system-info">
                <p><strong>系统版本:</strong> v1.0.0</p>
                <p><strong>更新频率:</strong> 3秒</p>
                <p><strong>分析周期:</strong> 15分钟</p>
                <br>
                <p><strong>支持货币对:</strong></p>
                <ul style="margin-left: 20px; margin-top: 5px;">
                    <li>EURUSD</li>
                    <li>GBPUSD</li>
                    <li>AUDUSD</li>
                    <li>NZDUSD</li>
                    <li>USDCHF</li>
                    <li>USDCAD</li>
                    <li>USDJPY</li>
                    <li>GOLD</li>
                </ul>
            </div>

            <h3 style="color: #00ff00; margin: 20px 0 10px 0;">最新分析</h3>
            <div id="latest-analysis" style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.9em;">
                等待分析结果...
            </div>
        </div>
    </div>

    <div class="footer">
        <p>© 2024 外汇交易系统 | 最后更新: <span id="last-update">--:--:--</span></p>
    </div>

    <script>
        let monitoring = false;

        // 更新数据
        function updateData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    updateSystemStatus(data.system_status);
                    updateMarketData(data.market_data);
                    updateAnalysisLog(data.analysis_history);
                    updateLastUpdate();
                })
                .catch(error => {
                    console.error('Error fetching data:', error);
                });
        }

        // 更新系统状态
        function updateSystemStatus(status) {
            const statusDiv = document.getElementById('system-status');
            let html = '';

            const components = [
                ['数据处理', 'data_processor'],
                ['市场分析', 'market_analyzer'],
                ['预分析', 'pre_analyzer'],
                ['LLM分析', 'llm_analyzer'],
                ['风险管理', 'risk_manager'],
                ['组合管理', 'portfolio_manager'],
                ['MT4连接', 'mt4_connection']
            ];

            components.forEach(([name, key]) => {
                const isOnline = status.components_status[key] || false;
                const statusClass = isOnline ? 'status-online' : 'status-offline';
                const statusText = isOnline ? '🟢 在线' : '🔴 离线';

                html += `<div class="status-item">
                    <span>${name}:</span>
                    <span class="${statusClass}">${statusText}</span>
                </div>`;
            });

            statusDiv.innerHTML = html;

            // 更新统计信息
            document.getElementById('success-count').textContent = status.success_count;
            document.getElementById('error-count').textContent = status.error_count;

            // 计算运行时间
            if (status.start_time) {
                const startTime = new Date(status.start_time);
                const now = new Date();
                const diff = Math.floor((now - startTime) / 1000);
                const hours = Math.floor(diff / 3600);
                const minutes = Math.floor((diff % 3600) / 60);
                const seconds = diff % 60;
                document.getElementById('uptime').textContent =
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }

            // 更新监控状态
            document.getElementById('monitoring-status').textContent = monitoring ? '监控中' : '待机';
        }

        // 更新市场数据
        function updateMarketData(marketData) {
            const marketDiv = document.getElementById('market-data');

            if (marketData && Object.keys(marketData).length > 0) {
                const html = `
                    <div class="status-item"><span>交易品种:</span> <span>${marketData.symbol || 'N/A'}</span></div>
                    <div class="status-item"><span>当前价格:</span> <span>${marketData.price ? marketData.price.toFixed(5) : 'N/A'}</span></div>
                    <div class="status-item"><span>RSI指标:</span> <span>${marketData.rsi || 'N/A'}</span></div>
                    <div class="status-item"><span>MACD:</span> <span>${marketData.macd ? marketData.macd.toFixed(6) : 'N/A'}</span></div>
                    <div class="status-item"><span>MA20:</span> <span>${marketData.ma_20 ? marketData.ma_20.toFixed(5) : 'N/A'}</span></div>
                    <div class="status-item"><span>更新时间:</span> <span>${marketData.timestamp ? new Date(marketData.timestamp).toLocaleTimeString() : 'N/A'}</span></div>
                `;
                marketDiv.innerHTML = html;
            } else {
                marketDiv.innerHTML = '<div class="status-item"><span>等待市场数据...</span></div>';
            }
        }

        // 更新分析日志
        function updateAnalysisLog(analysisHistory) {
            const logDiv = document.getElementById('analysis-log');
            const latestDiv = document.getElementById('latest-analysis');

            if (analysisHistory && analysisHistory.length > 0) {
                let logText = '📊 分析历史记录\\n' + '='.repeat(40) + '\\n';

                analysisHistory.slice(-10).forEach((item, index) => {
                    const time = new Date(item.timestamp).toLocaleTimeString();
                    const action = item.action;
                    const confidence = (item.confidence * 100).toFixed(1);
                    const risk = item.risk_level;

                    logText += `${index + 1:2d}. ${time} | ${action} | ${confidence}% | ${risk}\\n`;
                });

                logDiv.textContent = logText;

                // 更新最新分析
                const latest = analysisHistory[analysisHistory.length - 1];
                const latestText = `🕐 时间: ${new Date(latest.timestamp).toLocaleTimeString()}
📈 建议: ${latest.action}
🎯 信心: ${(latest.confidence * 100).toFixed(1)}%
⚠️ 风险: ${latest.risk_level}
💭 理由: ${latest.reasoning || '基于技术指标分析'}`;

                latestDiv.textContent = latestText;
            } else {
                logDiv.textContent = '等待分析结果...';
                latestDiv.textContent = '等待分析结果...';
            }
        }

        // 更新最后更新时间
        function updateLastUpdate() {
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
        }

        // 切换监控状态
        function toggleMonitoring() {
            fetch('/api/toggle-monitoring', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    monitoring = data.monitoring;
                    const btn = document.querySelector('.btn-start');
                    if (monitoring) {
                        btn.textContent = '⏸️ 暂停监控';
                        btn.className = 'btn btn-stop';
                    } else {
                        btn.textContent = '▶️ 启动监控';
                        btn.className = 'btn btn-start';
                    }
                    alert(data.message);
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('❌ 操作失败: ' + error.message);
                });
        }

        // 刷新数据
        function refreshData() {
            updateData();
            alert('✅ 数据已刷新');
        }

        // 运行测试
        function runTest() {
            alert('🧪 测试分析已启动，请查看控制台输出');
            fetch('/api/run-test', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert('✅ ' + data.message);
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('❌ 测试失败: ' + error.message);
                });
        }

        // 清空日志
        function clearLogs() {
            fetch('/api/clear-logs', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('analysis-log').textContent = '日志已清空，等待新的分析结果...';
                    document.getElementById('latest-analysis').textContent = '等待分析结果...';
                    alert('🗑️ 日志已清空');
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('❌ 清空失败: ' + error.message);
                });
        }

        // 定期更新数据
        setInterval(updateData, 3000);

        // 初始加载
        updateData();
    </script>
</body>
</html>
"""

def simulate_system_data():
    """模拟系统数据"""

    # 模拟市场数据
    base_price = 1.13550
    price_change = random.uniform(-0.0005, 0.0005)

    dashboard_data['market_data'] = {
        'symbol': 'EURUSD',
        'price': base_price + price_change,
        'rsi': round(random.uniform(30, 70), 1),
        'macd': round(random.uniform(-0.002, 0.002), 6),
        'ma_20': round(base_price + random.uniform(-0.0002, 0.0002), 5),
        'timestamp': datetime.now().isoformat()
    }

    # 模拟分析结果
    if dashboard_data['monitoring']:
        actions = ['BUY', 'SELL', 'HOLD']
        risk_levels = ['LOW', 'MEDIUM', 'HIGH']

        analysis_result = {
            'timestamp': datetime.now().isoformat(),
            'action': random.choice(actions),
            'confidence': round(random.uniform(0.6, 0.95), 3),
            'risk_level': random.choice(risk_levels),
            'reasoning': f"基于RSI({dashboard_data['market_data']['rsi']})和MACD分析"
        }

        dashboard_data['analysis_history'].append(analysis_result)
        if len(dashboard_data['analysis_history']) > 20:
            dashboard_data['analysis_history'].pop(0)

        dashboard_data['system_status']['success_count'] += 1

    # 模拟系统状态
    dashboard_data['system_status']['last_update'] = datetime.now().isoformat()

    # 随机模拟组件状态
    components = ['data_processor', 'market_analyzer', 'pre_analyzer',
                 'llm_analyzer', 'risk_manager', 'portfolio_manager']

    for comp in components:
        dashboard_data['system_status']['components_status'][comp] = random.random() > 0.1

    # MT4连接状态（周末通常离线）
    is_weekend = datetime.now().weekday() >= 5
    dashboard_data['system_status']['components_status']['mt4_connection'] = not is_weekend and random.random() > 0.2

def data_update_loop():
    """数据更新循环"""
    while True:
        try:
            simulate_system_data()
            time.sleep(3)
        except Exception as e:
            print(f"数据更新错误: {e}")
            time.sleep(10)

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/data')
def get_data():
    """获取数据API"""
    return jsonify(dashboard_data)

@app.route('/api/toggle-monitoring', methods=['POST'])
def toggle_monitoring():
    """切换监控状态"""
    dashboard_data['monitoring'] = not dashboard_data['monitoring']

    if dashboard_data['monitoring']:
        message = "✅ 监控已启动"
    else:
        message = "⏸️ 监控已暂停"

    return jsonify({
        'monitoring': dashboard_data['monitoring'],
        'message': message
    })

@app.route('/api/run-test', methods=['POST'])
def run_test():
    """运行测试"""
    try:
        # 这里可以调用实际的测试函数
        print("🧪 运行测试分析...")
        return jsonify({'status': 'success', 'message': '测试已启动'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/clear-logs', methods=['POST'])
def clear_logs():
    """清空日志"""
    dashboard_data['analysis_history'].clear()
    return jsonify({'status': 'success', 'message': '日志已清空'})

def main():
    """主函数"""
    print("🚀 启动Web版交易系统仪表板...")
    print("📱 访问地址: http://localhost:5000")

    # 启动数据更新线程
    update_thread = threading.Thread(target=data_update_loop, daemon=True)
    update_thread.start()

    # 启动Flask应用
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except Exception as e:
        print(f"❌ Web仪表板启动失败: {e}")

if __name__ == "__main__":
    main()
