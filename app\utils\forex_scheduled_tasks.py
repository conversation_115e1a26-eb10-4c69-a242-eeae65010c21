"""
外汇定时任务工具
用于定时执行外汇分析、交易和统计
支持基于市场变化的实时分析
"""
import time
import threading
import schedule
import traceback
import sys
from datetime import datetime, timedelta
import os
from app.utils.error_logger import log_error, ErrorType, OperationType
from app.utils.market_time_checker import is_market_open, get_market_status, get_next_market_open_time
from app.utils.logger_manager import log_scheduler, log_system, log_analysis, LogLevel

# 全局锁，用于确保同一时间只有一个分析任务在执行
analysis_lock = threading.Lock()

# 预分析锁，用于确保同一时间只有一个预分析任务在执行
# 将预分析和完整分析的锁分开，避免预分析完成后触发的完整分析无法获取锁
pre_analysis_lock = threading.Lock()

# 移除倒计时相关代码，简化系统逻辑

# 全局变量，用于存储任务状态
tasks = {
    'hourly_analysis': {
        'running': False,
        'last_run': None,
        'thread': None
    },
    'daily_statistics': {
        'running': False,
        'last_run': None,
        'thread': None
    },
    'realtime_analysis': {
        'running': False,
        'last_run': None,
        'thread': None,
        'detector_running': False
    }
}

# 全局变量，用于防止重复初始化调度器
scheduler_initialized = False

# 分析模式
class AnalysisMode:
    HOURLY = "HOURLY"  # 每小时分析
    REALTIME = "REALTIME"  # 实时分析（基于市场变化）
    HYBRID = "HYBRID"  # 混合模式（定时+实时）

# 当前分析模式
current_analysis_mode = AnalysisMode.HOURLY


def start_hourly_forex_analysis(run_immediately=False, auto_trade=False):
    """
    启动每小时外汇分析任务

    Args:
        run_immediately (bool): 是否立即执行一次
        auto_trade (bool): 是否自动执行交易
    """
    global scheduler_initialized

    # 如果调度器已经初始化，直接返回
    if scheduler_initialized:
        print('调度器已经初始化，跳过重复初始化')
        return

    from app.services.forex_trading_service import analyze_forex, execute_trade

    print(f'启动每小时外汇分析任务，立即执行: {run_immediately}，自动交易: {auto_trade}')

    # 定义任务函数
    def task():
        try:
            current_time = datetime.now()
            print(f'[{current_time.strftime("%Y-%m-%d %H:%M:%S")}] 执行定时外汇分析任务')

            # 更新最后分析时间记录（不限制执行频率）
            from app import last_analysis_time

            # 更新任务状态
            tasks['hourly_analysis']['running'] = True
            tasks['hourly_analysis']['last_run'] = current_time

            # 检查是否有最近的分析记录
            from app.utils.forex_analysis_history import get_latest_analysis_record
            recent_analysis = get_latest_analysis_record()

            # 如果有最近的分析记录，记录时间信息（但不跳过分析）
            if recent_analysis and 'timestamp' in recent_analysis:
                try:
                    record_time = datetime.fromisoformat(recent_analysis['timestamp'].replace('Z', '+00:00'))
                    now = datetime.now()
                    time_diff = now - record_time
                    minutes_diff = time_diff.total_seconds() / 60

                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 找到有效的最新分析记录，时间: {record_time.strftime("%Y-%m-%d %H:%M:%S")}')
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 距离上次分析已经过去 {minutes_diff:.1f} 分钟')

                    # 记录是否已经执行过交易，但不跳过分析
                    if auto_trade and recent_analysis.get('tradeExecuted'):
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 上次分析已执行交易，本次将由AI决定是否需要新的交易')
                except Exception as time_error:
                    print(f'检查分析记录时间时出错: {time_error}')

            # 先检查和修复无止损订单
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行止损检查，修复无止损订单')
            from app.utils.stop_loss_checker import check_and_fix_orders
            check_result = check_and_fix_orders()

            if check_result['success']:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 止损检查完成: {check_result["message"]}')

                # 如果有修复的订单，打印详细信息
                fixed_orders = check_result.get('fixed_orders', [])
                if fixed_orders:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修复了 {len(fixed_orders)} 个无止损订单')
                    for order in fixed_orders:
                        print(f'  - 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 新止损: {order.get("new_sl")}')
            else:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 止损检查失败: {check_result["message"]}')

            # 执行分析
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 开始执行外汇分析...')
            analysis_result = analyze_forex(force=True)
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 分析完成，结果:', analysis_result.get('tradeInstructions', {}))

            # 更新最后分析时间
            from app import last_analysis_time
            import app
            app.last_analysis_time = now

            # 如果启用自动交易，执行交易或订单管理
            if auto_trade and analysis_result.get('tradeInstructions'):
                trade_instructions = analysis_result.get('tradeInstructions', {})

                # 检查是否是最终决策的交易指令
                is_final_decision = trade_instructions.get('isFinalDecision', False)

                now = datetime.now()
                if not is_final_decision:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告：这不是最终决策的交易指令，跳过执行')

                    # 记录日志
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 交易指令详情: {trade_instructions}')

                    # 检查是否是多轮分析结果
                    is_multi_round = isinstance(analysis_result.get('analysis'), dict) and 'initial' in analysis_result.get('analysis', {})
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 是否是多轮分析结果: {is_multi_round}')

                    return

                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 确认这是最终决策的交易指令，准备执行')

                # 确保所有订单管理指令都被标记为最终决策
                if trade_instructions.get('orderManagement'):
                    for order_action in trade_instructions.get('orderManagement', []):
                        if isinstance(order_action, dict):
                            order_action['isFinalDecision'] = True

                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 订单管理指令已标记为最终决策')

                # 无论是否有新的交易指令，都执行订单管理操作
                if trade_instructions.get('orderManagement'):
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行订单管理操作...')
                    # 使用风险管理的交易执行
                    try:
                        from app.services.forex_trading_service import execute_trade_with_risk_management
                        trade_result = execute_trade_with_risk_management(trade_instructions, check_duplicate=True)
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 使用风险管理交易执行')
                    except ImportError:
                        from app.services.forex_trading_service import execute_trade
                        trade_result = execute_trade(trade_instructions, check_duplicate=True)
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 使用标准交易执行')
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 订单管理执行结果:', trade_result)

                    # 标记已执行交易
                    analysis_result['tradeExecuted'] = True

                    # 更新分析记录
                    from app.utils.forex_analysis_history import save_analysis_record
                    save_analysis_record(analysis_result)
                # 检查是否有有效的交易指令
                elif trade_instructions.get('action') != 'NONE':
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行自动交易...')
                    # 使用风险管理的交易执行
                    try:
                        from app.services.forex_trading_service import execute_trade_with_risk_management
                        trade_result = execute_trade_with_risk_management(trade_instructions, check_duplicate=True)
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 使用风险管理交易执行')
                    except ImportError:
                        from app.services.forex_trading_service import execute_trade
                        trade_result = execute_trade(trade_instructions, check_duplicate=True)
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 使用标准交易执行')
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 交易执行结果:', trade_result)

                    # 标记已执行交易
                    analysis_result['tradeExecuted'] = True

                    # 更新分析记录
                    from app.utils.forex_analysis_history import save_analysis_record
                    save_analysis_record(analysis_result)
                else:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 交易指令为观望，不执行新交易')

            # 更新任务状态
            tasks['hourly_analysis']['running'] = False

            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 分析任务完成')

        except Exception as error:
            print(f'执行定时外汇分析任务失败: {error}')
            tasks['hourly_analysis']['running'] = False

    # 创建任务线程
    def run_threaded(job_func):
        if tasks['hourly_analysis']['running']:
            print('上一个分析任务仍在运行，跳过本次执行')
            return

        job_thread = threading.Thread(target=job_func)
        job_thread.start()
        tasks['hourly_analysis']['thread'] = job_thread

    # 每小时执行一次
    schedule.every().hour.at(':00').do(run_threaded, task)
    print('已设置每小时整点执行外汇分析任务')

    # 如果需要立即执行一次
    if run_immediately:
        print('立即执行一次外汇分析任务')
        run_threaded(task)

    # 启动调度器线程
    def run_scheduler():
        while True:
            schedule.run_pending()
            time.sleep(1)

    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    print('调度器线程已启动')

    # 标记调度器已初始化
    scheduler_initialized = True


def start_daily_statistics(run_immediately=False, report_time='17:00'):
    """
    启动每日统计分析任务

    Args:
        run_immediately (bool): 是否立即执行一次
        report_time (str): 每日执行时间，格式为'HH:MM'
    """
    global scheduler_initialized

    # 如果调度器已经初始化，直接返回
    if scheduler_initialized:
        print('调度器已经初始化，跳过重复初始化')
        return

    from app.utils.forex_statistics import run_statistics_analysis

    print(f'启动每日统计分析任务，立即执行: {run_immediately}，执行时间: {report_time}')

    # 定义任务函数
    def task():
        try:
            current_time = datetime.now()
            print(f'[{current_time.strftime("%Y-%m-%d %H:%M:%S")}] 执行每日统计分析任务')

            # 更新任务状态
            tasks['daily_statistics']['running'] = True
            tasks['daily_statistics']['last_run'] = current_time

            # 执行统计分析
            report = run_statistics_analysis()
            if report:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 统计分析完成，报告已生成')

                # 保存报告路径
                report_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'forex_statistics_report.txt')
                charts_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'charts')

                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 报告保存路径: {report_path}')
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 图表保存路径: {charts_path}')
            else:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 统计分析失败')

            # 更新任务状态
            tasks['daily_statistics']['running'] = False

            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 统计分析任务完成')

        except Exception as error:
            print(f'执行每日统计分析任务失败: {error}')
            tasks['daily_statistics']['running'] = False

    # 创建任务线程
    def run_threaded(job_func):
        if tasks['daily_statistics']['running']:
            print('上一个统计分析任务仍在运行，跳过本次执行')
            return

        job_thread = threading.Thread(target=job_func)
        job_thread.start()
        tasks['daily_statistics']['thread'] = job_thread

    # 每天执行一次
    schedule.every().day.at(report_time).do(run_threaded, task)
    print(f'已设置每天 {report_time} 执行统计分析任务')

    # 如果需要立即执行一次
    if run_immediately:
        print('立即执行一次统计分析任务')
        run_threaded(task)


def start_realtime_forex_analysis(run_immediately=False, auto_trade=False, check_interval=60, hourly_force_analysis=False):
    """
    启动实时外汇分析任务（基于市场变化）

    Args:
        run_immediately (bool): 是否立即执行一次
        auto_trade (bool): 是否自动执行交易
        check_interval (int): 市场检查间隔（秒）
        hourly_force_analysis (bool): 是否每小时强制执行一次完整分析
    """
    global scheduler_initialized, current_analysis_mode

    # 如果调度器已经初始化，先停止现有任务
    if scheduler_initialized and tasks['realtime_analysis']['detector_running']:
        print('实时分析任务已经初始化，先停止现有任务')
        # 停止市场变化检测
        tasks['realtime_analysis']['detector_running'] = False
        print('已停止市场变化检测')

        # 等待任务完成
        if tasks['realtime_analysis']['running'] and tasks['realtime_analysis']['thread']:
            print('等待正在运行的实时分析任务完成...')
            tasks['realtime_analysis']['thread'].join(timeout=10)

        # 重置任务状态
        tasks['realtime_analysis']['running'] = False
        tasks['realtime_analysis']['detector_running'] = False
        print('现有任务已停止，准备启动新任务')

    from app.services.forex_trading_service import analyze_forex, execute_trade

    print(f'启动实时外汇分析任务，立即执行: {run_immediately}，自动交易: {auto_trade}，检查间隔: {check_interval}秒，每小时强制分析: {hourly_force_analysis}')

    # 设置当前分析模式
    if current_analysis_mode == AnalysisMode.HOURLY:
        current_analysis_mode = AnalysisMode.REALTIME
    else:
        current_analysis_mode = AnalysisMode.HYBRID

    # 如果启用每小时强制分析，设置每小时整点执行一次完整分析
    if hourly_force_analysis:
        def hourly_force_task():
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 执行每小时强制分析任务')
            market_change_callback("每小时强制分析", force_analysis=True)

        # 每小时执行一次
        schedule.every().hour.at(':00').do(hourly_force_task)
        print('已设置每小时整点执行强制分析任务')

    # 定义市场变化回调函数
    def market_change_callback(change_reason, force_analysis=True):
        """
        市场变化回调函数
        当检测到市场变化时，触发分析和交易

        Args:
            change_reason (str): 触发原因
            force_analysis (bool): 是否强制分析（默认为True，完全跳过预分析）
        """
        # 尝试获取全局锁，如果无法获取，说明已有分析任务在执行
        if not analysis_lock.acquire(blocking=False):
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5: 完整分析锁被占用，跳过本次分析')
            return

        try:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5: 开始完整分析: {change_reason} (强制={force_analysis})')

            # 检查是否有分析任务正在运行（双重检查）
            if tasks['realtime_analysis']['running']:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.1: 已有分析任务正在运行，跳过本次分析')
                return

            # 如果是定期分析，检查最近的分析记录和动态时间间隔
            if change_reason == "定期分析（预分析功能已移除）":
                from app.utils.forex_analysis_history import get_latest_analysis_record
                recent_analysis = get_latest_analysis_record()

                # 检查是否有突发情况需要立即分析
                emergency_analysis_needed = False
                emergency_reason = ""

                try:
                    # 获取当前市场数据进行突发情况检测
                    from app.services.forex_trading_service import get_analysis_data
                    current_data = get_analysis_data()

                    if current_data and recent_analysis:
                        # 获取当前价格和上次分析时的价格
                        current_price = current_data.get('current_price', 0)
                        last_price = recent_analysis.get('currentPrice', 0)

                        if current_price > 0 and last_price > 0:
                            # 计算价格变化百分比
                            price_change_percent = abs(current_price - last_price) / last_price * 100

                            # 突发情况1：价格突变超过0.5%
                            if price_change_percent > 0.5:
                                emergency_analysis_needed = True
                                emergency_reason = f"价格突变{price_change_percent:.2f}%，触发紧急分析"

                            # 突发情况2：检查技术指标突破
                            indicators = current_data.get('indicators', {})
                            if indicators:
                                rsi = indicators.get('rsi')
                                if rsi and (rsi > 85 or rsi < 15):  # 极端超买超卖
                                    emergency_analysis_needed = True
                                    emergency_reason = f"RSI极端值{rsi:.1f}，触发紧急分析"

                                # MACD突破检测
                                macd = indicators.get('macd', {})
                                if macd and 'macdLine' in macd and 'signalLine' in macd:
                                    macd_line = macd['macdLine'][-1] if macd['macdLine'] else 0
                                    signal_line = macd['signalLine'][-1] if macd['signalLine'] else 0
                                    if abs(macd_line - signal_line) > 0.001:  # MACD线差距较大
                                        emergency_analysis_needed = True
                                        emergency_reason = f"MACD信号突破，触发紧急分析"

                        # 突发情况3：检查持仓风险
                        positions = current_data.get('positions', [])
                        if positions:
                            for position in positions:
                                # 检查是否接近止损或止盈
                                entry_price = position.get('openPrice', 0)
                                stop_loss = position.get('stopLoss', 0)
                                take_profit = position.get('takeProfit', 0)

                                if entry_price > 0 and current_price > 0:
                                    if stop_loss > 0:
                                        distance_to_sl = abs(current_price - stop_loss) / entry_price * 100
                                        if distance_to_sl < 0.1:  # 距离止损不到0.1%
                                            emergency_analysis_needed = True
                                            emergency_reason = f"接近止损位，触发紧急分析"

                                    if take_profit > 0:
                                        distance_to_tp = abs(current_price - take_profit) / entry_price * 100
                                        if distance_to_tp < 0.1:  # 距离止盈不到0.1%
                                            emergency_analysis_needed = True
                                            emergency_reason = f"接近止盈位，触发紧急分析"

                except Exception as e:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 突发情况检测失败: {e}')

                # 如果有突发情况，立即执行分析
                if emergency_analysis_needed:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 🚨 突发情况检测: {emergency_reason}')
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 🚨 无视时间间隔，立即执行紧急分析')
                    # 继续执行分析，不返回
                elif recent_analysis and 'timestamp' in recent_analysis:
                    try:
                        record_time = datetime.fromisoformat(recent_analysis['timestamp'].replace('Z', '+00:00'))
                        now = datetime.now()
                        time_diff = now - record_time
                        minutes_diff = time_diff.total_seconds() / 60

                        # 获取策略优化建议的动态间隔
                        strategy_optimization = recent_analysis.get('strategy_optimization', {})
                        recommended_interval = 30  # 默认30分钟（标准策略）
                        strategy_mode = 'standard'

                        if strategy_optimization:
                            trading_recommendation = strategy_optimization.get('trading_recommendation', {})
                            strategy_mode = trading_recommendation.get('strategy_mode', 'standard')

                            # 根据策略模式确定最小分析间隔
                            if strategy_mode == 'fast':
                                recommended_interval = 15  # 快速策略：15分钟间隔
                            elif strategy_mode == 'standard':
                                recommended_interval = 30  # 标准策略：30分钟间隔
                            elif strategy_mode == 'conservative':
                                recommended_interval = 60  # 稳健策略：60分钟间隔

                        # 检查是否满足动态间隔要求
                        if minutes_diff < recommended_interval:
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.2: 距离上次分析仅{minutes_diff:.1f}分钟，跳过本次定期分析（{strategy_mode}策略要求{recommended_interval}分钟间隔）')
                            return
                        else:
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.2: 距离上次分析{minutes_diff:.1f}分钟，满足{strategy_mode}策略{recommended_interval}分钟间隔，继续分析')
                    except Exception as e:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.2: 检查分析时间间隔失败: {e}，继续分析')

            # 更新任务状态
            tasks['realtime_analysis']['running'] = True
            tasks['realtime_analysis']['last_run'] = datetime.now()

            # 执行分析
            analysis_start_time = datetime.now()
            analysis_result = analyze_forex(force=force_analysis)
            analysis_duration = (datetime.now() - analysis_start_time).total_seconds()
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.2: 分析完成，耗时: {analysis_duration:.2f}秒')

            # 检查是否需要回退
            if analysis_result and isinstance(analysis_result, dict) and analysis_result.get('status') == 'FALLBACK_REQUIRED':
                retry_after = analysis_result.get('retry_after', 300)  # 默认5分钟
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 检测到需要回退，{retry_after}秒后重试')

                # 安排延迟任务
                def delayed_retry():
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 执行延迟回退任务')
                    market_change_callback(f"API调用失败回退重试", force_analysis=force_analysis)

                # 使用threading.Timer安排延迟任务
                retry_timer = threading.Timer(retry_after, delayed_retry)
                retry_timer.daemon = True
                retry_timer.start()

                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 已安排{retry_after}秒后的回退重试任务')
                tasks['realtime_analysis']['running'] = False

                # 释放锁并返回，避免在finally块中再次释放
                analysis_lock.release()
                return

            # 更新最后分析时间
            try:
                import app
                app.last_analysis_time = datetime.now()
            except ImportError:
                pass

            # 检查分析结果
            if not analysis_result:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.3: 错误: 分析结果为空')
                tasks['realtime_analysis']['running'] = False
                return

            trade_instructions = analysis_result.get('tradeInstructions', {})
            if not trade_instructions:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.3: 错误: 无交易指令')
                tasks['realtime_analysis']['running'] = False
                return

            action = trade_instructions.get('action', 'NONE')
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.3: 分析结果: 操作={action}')

            # 如果启用自动交易，执行交易或订单管理
            if auto_trade:
                # 检查是否是最终决策的交易指令
                is_final_decision = trade_instructions.get('isFinalDecision', False)

                if not is_final_decision:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.4: 警告: 非最终决策指令，跳过执行')

                    # 更新任务状态
                    tasks['realtime_analysis']['running'] = False
                    return

                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.4: 执行最终决策交易指令')

                # 确保所有订单管理指令都被标记为最终决策
                if trade_instructions.get('orderManagement'):
                    for order_action in trade_instructions.get('orderManagement', []):
                        if isinstance(order_action, dict):
                            order_action['isFinalDecision'] = True

                # 处理订单管理操作
                if trade_instructions.get('orderManagement'):
                    order_count = len(trade_instructions.get("orderManagement", []))
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.5: 执行订单管理: {order_count}个指令')

                    # 执行订单管理
                    trade_start_time = datetime.now()
                    # 使用风险管理的交易执行
                    try:
                        from app.services.forex_trading_service import execute_trade_with_risk_management
                        trade_result = execute_trade_with_risk_management(trade_instructions, check_duplicate=True)
                    except ImportError:
                        trade_result = execute_trade(trade_instructions, check_duplicate=True)
                    trade_duration = (datetime.now() - trade_start_time).total_seconds()

                    # 输出结果
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.6: 订单管理完成: {trade_result} (耗时: {trade_duration:.2f}秒)')

                    # 标记已执行交易
                    analysis_result['tradeExecuted'] = True

                    # 更新分析记录
                    from app.utils.forex_analysis_history import save_analysis_record
                    save_analysis_record(analysis_result)

                # 处理新交易指令
                elif action != 'NONE':
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.5: 执行新交易: {action} {trade_instructions.get("orderType")} 入场={trade_instructions.get("entryPrice")} 止损={trade_instructions.get("stopLoss")} 止盈={trade_instructions.get("takeProfit")} 手数={trade_instructions.get("lotSize")}')

                    # 执行交易
                    trade_start_time = datetime.now()
                    # 使用风险管理的交易执行
                    try:
                        from app.services.forex_trading_service import execute_trade_with_risk_management
                        trade_result = execute_trade_with_risk_management(trade_instructions, check_duplicate=True)
                    except ImportError:
                        trade_result = execute_trade(trade_instructions, check_duplicate=True)
                    trade_duration = (datetime.now() - trade_start_time).total_seconds()

                    # 输出结果
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.6: 交易完成: {trade_result} (耗时: {trade_duration:.2f}秒)')

                    # 标记已执行交易
                    analysis_result['tradeExecuted'] = True

                    # 更新分析记录
                    from app.utils.forex_analysis_history import save_analysis_record
                    save_analysis_record(analysis_result)
                else:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.5: 交易指令: 观望')
            else:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.5: 自动交易未启用')

            # 更新任务状态
            tasks['realtime_analysis']['running'] = False

        except Exception as error:
            traceback.print_exc()

            # 记录错误
            log_error(
                error_type=ErrorType.SYSTEM_ERROR,
                message=f'执行实时外汇分析任务失败: {error}',
                details={'exception': str(error), 'traceback': traceback.format_exc()},
                operation=OperationType.ANALYSIS
            )
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤5.7: 错误: 分析任务失败')
        finally:
            # 无论成功还是失败，都确保释放锁
            analysis_lock.release()
            tasks['realtime_analysis']['running'] = False

    # 启动市场变化检测线程
    def run_market_analyzer():
        """
        运行市场变化分析线程
        使用预分析功能判断是否需要进行完整分析，并根据LLM设置的时间间隔调整下次分析时间
        """
        try:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 启动市场变化分析线程')
            sys.stdout.flush()  # 确保输出立即显示

            # 移除倒计时初始化代码

            # 启动时强制分析功能已移除，设置首次分析时间
            if run_immediately:
                # 即使设置了run_immediately，也不再执行强制分析，而是等待定期分析
                next_analysis_time = datetime.now() + timedelta(minutes=5)
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 启动时强制分析已移除，5分钟后执行首次定期分析')
            else:
                # 立即开始定期分析循环，保持高频监听
                next_analysis_time = datetime.now()
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 开始定期分析循环，立即开始高频监听')

            # 确保输出立即显示
            sys.stdout.flush()

            # 分析计数器
            analysis_count = 0
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] [错误] 初始化市场变化分析线程时出错: {e}')
            traceback.print_exc()
            # 设置默认的下次分析时间
            next_analysis_time = datetime.now() + timedelta(minutes=5)
            analysis_count = 0

        while tasks['realtime_analysis']['detector_running']:
            try:
                now = datetime.now()

                # 检查市场是否开放
                if not is_market_open():
                    # 市场关闭时，延长等待时间并显示状态
                    market_status = get_market_status()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 市场状态: {market_status}，暂停分析')

                    # 计算到下次市场开放的时间
                    next_open_time = get_next_market_open_time()
                    if next_open_time:
                        try:
                            # 确保时间对象都是同一时区
                            if next_open_time.tzinfo is not None and now.tzinfo is None:
                                # next_open_time有时区信息，now没有，将now转换为UTC
                                import pytz
                                now = pytz.UTC.localize(now)
                            elif next_open_time.tzinfo is None and now.tzinfo is not None:
                                # now有时区信息，next_open_time没有，将next_open_time转换为UTC
                                import pytz
                                next_open_time = pytz.UTC.localize(next_open_time)

                            time_to_open = (next_open_time - now).total_seconds()
                            if time_to_open > 3600:  # 如果超过1小时，等待1小时后再检查
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 距离市场开放还有{time_to_open/3600:.1f}小时，1小时后再检查')
                                time.sleep(3600)
                            else:
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 距离市场开放还有{time_to_open/60:.0f}分钟，等待中...')
                                time.sleep(min(1800, max(60, time_to_open)))  # 最多等待30分钟，最少等待1分钟
                        except Exception as e:
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 时间计算错误: {e}，等待30分钟后再检查')
                            time.sleep(1800)
                    else:
                        # 如果无法确定下次开放时间，等待30分钟后再检查
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 无法确定下次开放时间，30分钟后再检查')
                        time.sleep(1800)
                    continue

                # 检查是否到达下次分析时间
                if now < next_analysis_time:
                    # 计算距离下次分析的时间（秒）
                    total_wait_seconds = (next_analysis_time - now).total_seconds()

                    # 简化等待逻辑，不显示倒计时
                    # 如果距离下次分析时间超过30秒，等待30秒
                    # 否则等待实际时间
                    wait_seconds = min(30, max(1, total_wait_seconds))

                    # 等待指定时间
                    if wait_seconds > 0:
                        time.sleep(wait_seconds)
                        continue

                # 到达分析时间，开始新的分析周期
                analysis_count += 1

                # 尝试获取预分析锁，如果无法获取，说明已有预分析任务在执行
                if not pre_analysis_lock.acquire(blocking=False):
                    next_analysis_time = datetime.now() + timedelta(minutes=5)
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤3: 预分析锁被占用，5分钟后重试')
                    continue

                try:
                    # 获取分析数据
                    from app.services.forex_trading_service import get_analysis_data
                    analysis_data = get_analysis_data()

                    # 检查分析数据是否获取成功
                    if analysis_data is None:
                        next_analysis_time = datetime.now() + timedelta(minutes=5)
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤3.1: 分析数据获取失败，5分钟后重试')
                        # 不使用continue，让代码继续执行到finally块，确保锁被正确释放
                        # 设置一个标志，表示不需要继续执行后续的预分析步骤
                        skip_analysis = True
                    else:
                        skip_analysis = False

                    # 只有在不跳过分析的情况下才执行后续步骤
                    if not skip_analysis:
                        # 预分析功能已移除，直接执行完整分析
                        # 设置下次分析时间为5分钟后，保持高频监听
                        next_analysis_time = datetime.now() + timedelta(minutes=5)

                        # 直接触发完整分析
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤3.2: 执行定期分析（预分析功能已移除）')
                        market_change_callback("定期分析（预分析功能已移除）", force_analysis=True)

                    # 系统会根据next_analysis_time自动等待适当的时间
                except Exception as error:
                    traceback.print_exc()
                    # 设置5分钟后重试
                    next_analysis_time = datetime.now() + timedelta(minutes=5)
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤3.3: 预分析出错，5分钟后重试')
                finally:
                    # 无论成功还是失败，都确保释放预分析锁
                    pre_analysis_lock.release()

            except KeyboardInterrupt:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤3.4: 用户中断分析线程')
                break
            except Exception as error:
                traceback.print_exc()

                # 记录错误
                log_error(
                    error_type=ErrorType.SYSTEM_ERROR,
                    message=f'市场变化分析线程出错: {error}',
                    details={'exception': str(error), 'traceback': traceback.format_exc()},
                    operation=OperationType.ANALYSIS
                )

                # 出错后等待一段时间再重试
                time.sleep(60)

                # 重置下次分析时间（出错后5分钟再尝试）
                next_analysis_time = datetime.now() + timedelta(minutes=5)
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤3.4: 分析线程出错，5分钟后重试')

    # 启动时强制分析功能已移除，系统将通过智能货币对选择和定期分析机制自动开始分析
    if run_immediately:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤4: 启动时强制分析已移除')
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 系统将通过智能货币对选择和定期分析机制自动开始分析')
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 首次分析将在{check_interval//60}分钟后自动执行')

    # 创建并启动市场变化分析线程
    analyzer_thread = threading.Thread(target=run_market_analyzer, daemon=True)
    analyzer_thread.start()
    tasks['realtime_analysis']['detector_running'] = True
    tasks['realtime_analysis']['thread'] = analyzer_thread

    # 标记调度器已初始化
    scheduler_initialized = True


def stop_all_tasks():
    """停止所有定时任务"""
    print('停止所有定时任务')

    # 清除所有任务
    schedule.clear()

    # 停止市场变化检测
    tasks['realtime_analysis']['detector_running'] = False
    print('已停止市场变化检测')

    # 等待任务完成
    if tasks['hourly_analysis']['running'] and tasks['hourly_analysis']['thread']:
        print('等待正在运行的分析任务完成...')
        tasks['hourly_analysis']['thread'].join(timeout=10)

    if tasks['daily_statistics']['running'] and tasks['daily_statistics']['thread']:
        print('等待正在运行的统计分析任务完成...')
        tasks['daily_statistics']['thread'].join(timeout=10)

    if tasks['realtime_analysis']['running'] and tasks['realtime_analysis']['thread']:
        print('等待正在运行的实时分析任务完成...')
        tasks['realtime_analysis']['thread'].join(timeout=10)

    print('所有定时任务已停止')
