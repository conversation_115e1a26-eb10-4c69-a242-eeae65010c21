"""
LLM技术分析器扩展功能
包含更多的技术分析方法和LLM友好的解释功能
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from app.utils.logger_manager import log_analysis, LogLevel

class LLMTechnicalAnalyzerExtended:
    """LLM技术分析器扩展功能"""
    
    def __init__(self):
        pass
    
    def calculate_stochastic(self, df: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> Dict:
        """计算随机指标"""
        try:
            high = df['high']
            low = df['low']
            close = df['close']
            
            # 计算%K
            lowest_low = low.rolling(window=k_period).min()
            highest_high = high.rolling(window=k_period).max()
            
            k_percent = 100 * (close - lowest_low) / (highest_high - lowest_low)
            
            # 计算%D
            d_percent = k_percent.rolling(window=d_period).mean()
            
            current_k = k_percent.iloc[-1] if not pd.isna(k_percent.iloc[-1]) else 50
            current_d = d_percent.iloc[-1] if not pd.isna(d_percent.iloc[-1]) else 50
            
            # 信号解释
            if current_k > 80 and current_d > 80:
                signal = 'overbought'
            elif current_k < 20 and current_d < 20:
                signal = 'oversold'
            elif current_k > current_d:
                signal = 'bullish'
            elif current_k < current_d:
                signal = 'bearish'
            else:
                signal = 'neutral'
            
            return {
                'k_percent': current_k,
                'd_percent': current_d,
                'signal': signal
            }
            
        except Exception:
            return {
                'k_percent': 50,
                'd_percent': 50,
                'signal': 'unknown'
            }
    
    def calculate_williams_r(self, df: pd.DataFrame, period: int = 14) -> float:
        """计算威廉指标"""
        try:
            high = df['high']
            low = df['low']
            close = df['close']
            
            highest_high = high.rolling(window=period).max()
            lowest_low = low.rolling(window=period).min()
            
            williams_r = -100 * (highest_high - close) / (highest_high - lowest_low)
            
            return williams_r.iloc[-1] if not pd.isna(williams_r.iloc[-1]) else -50.0
        except Exception:
            return -50.0
    
    def interpret_williams_signal(self, williams_r: float) -> str:
        """解释威廉指标信号"""
        if williams_r > -20:
            return 'overbought'
        elif williams_r < -80:
            return 'oversold'
        else:
            return 'neutral'
    
    def calculate_momentum(self, close_prices: pd.Series, period: int = 10) -> Dict:
        """计算动量指标"""
        try:
            momentum = close_prices.diff(period)
            current_momentum = momentum.iloc[-1] if not pd.isna(momentum.iloc[-1]) else 0
            
            # 动量强度
            momentum_strength = abs(current_momentum) / close_prices.iloc[-1] * 100
            
            # 动量方向
            if current_momentum > 0:
                direction = 'bullish'
            elif current_momentum < 0:
                direction = 'bearish'
            else:
                direction = 'neutral'
            
            return {
                'value': current_momentum,
                'strength': momentum_strength,
                'direction': direction
            }
            
        except Exception:
            return {
                'value': 0,
                'strength': 0,
                'direction': 'unknown'
            }
    
    def calculate_bollinger_bands(self, df: pd.DataFrame, period: int = 20, std_dev: float = 2) -> Dict:
        """计算布林带"""
        try:
            close = df['close']
            
            # 中轨（移动平均线）
            middle = close.rolling(window=period).mean()
            
            # 标准差
            std = close.rolling(window=period).std()
            
            # 上轨和下轨
            upper = middle + (std * std_dev)
            lower = middle - (std * std_dev)
            
            current_price = close.iloc[-1]
            current_upper = upper.iloc[-1]
            current_middle = middle.iloc[-1]
            current_lower = lower.iloc[-1]
            
            # 布林带位置
            bb_position = (current_price - current_lower) / (current_upper - current_lower)
            
            # 布林带宽度
            bb_width = (current_upper - current_lower) / current_middle * 100
            
            # 信号解释
            if bb_position > 0.8:
                signal = 'overbought'
            elif bb_position < 0.2:
                signal = 'oversold'
            else:
                signal = 'neutral'
            
            # 挤压检测
            squeeze = bb_width < 10  # 布林带宽度小于10%认为是挤压
            
            return {
                'upper': current_upper,
                'middle': current_middle,
                'lower': current_lower,
                'position': bb_position,
                'width': bb_width,
                'signal': signal,
                'squeeze': squeeze
            }
            
        except Exception:
            return {
                'upper': 0,
                'middle': 0,
                'lower': 0,
                'position': 0.5,
                'width': 0,
                'signal': 'unknown',
                'squeeze': False
            }
    
    def calculate_atr(self, df: pd.DataFrame, period: int = 14) -> Dict:
        """计算平均真实波幅"""
        try:
            high = df['high']
            low = df['low']
            close = df['close']
            
            # 计算真实波幅
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # 计算ATR
            atr = tr.rolling(window=period).mean()
            
            current_atr = atr.iloc[-1] if not pd.isna(atr.iloc[-1]) else 0
            current_price = close.iloc[-1]
            
            # ATR百分比
            atr_percentage = current_atr / current_price * 100
            
            # 波动率水平
            if atr_percentage > 2.0:
                volatility_level = 'extremely_high'
            elif atr_percentage > 1.5:
                volatility_level = 'high'
            elif atr_percentage > 1.0:
                volatility_level = 'normal'
            elif atr_percentage > 0.5:
                volatility_level = 'low'
            else:
                volatility_level = 'extremely_low'
            
            return {
                'value': current_atr,
                'percentage': atr_percentage,
                'volatility_level': volatility_level
            }
            
        except Exception:
            return {
                'value': 0,
                'percentage': 0,
                'volatility_level': 'unknown'
            }
    
    def calculate_historical_volatility(self, df: pd.DataFrame, period: int = 20) -> Dict:
        """计算历史波动率"""
        try:
            close = df['close']
            
            # 计算对数收益率
            log_returns = np.log(close / close.shift(1))
            
            # 计算历史波动率
            volatility = log_returns.rolling(window=period).std() * np.sqrt(252) * 100  # 年化波动率
            
            current_volatility = volatility.iloc[-1] if not pd.isna(volatility.iloc[-1]) else 0
            
            # 波动率趋势
            volatility_trend = volatility.iloc[-5:].mean() - volatility.iloc[-10:-5].mean()
            
            if volatility_trend > 0:
                trend = 'increasing'
            elif volatility_trend < 0:
                trend = 'decreasing'
            else:
                trend = 'stable'
            
            return {
                'current': current_volatility,
                'trend': trend,
                'level': 'high' if current_volatility > 20 else 'low' if current_volatility < 10 else 'normal'
            }
            
        except Exception:
            return {
                'current': 0,
                'trend': 'unknown',
                'level': 'unknown'
            }
    
    def identify_candlestick_patterns(self, df: pd.DataFrame) -> Dict:
        """识别K线形态"""
        try:
            patterns = {}
            
            if len(df) < 3:
                return {'available': False, 'reason': 'insufficient_data'}
            
            # 获取最近3根K线
            recent = df.tail(3)
            
            # 当前K线
            current = recent.iloc[-1]
            prev1 = recent.iloc[-2]
            prev2 = recent.iloc[-3] if len(recent) >= 3 else None
            
            # 计算实体和影线
            current_body = abs(current['close'] - current['open'])
            current_upper_shadow = current['high'] - max(current['open'], current['close'])
            current_lower_shadow = min(current['open'], current['close']) - current['low']
            current_range = current['high'] - current['low']
            
            # 十字星
            if current_body / current_range < 0.1:
                patterns['doji'] = True
            
            # 锤子线
            if (current_lower_shadow > current_body * 2 and 
                current_upper_shadow < current_body * 0.1):
                patterns['hammer'] = True
            
            # 上吊线
            if (current_upper_shadow > current_body * 2 and 
                current_lower_shadow < current_body * 0.1):
                patterns['hanging_man'] = True
            
            # 吞没形态
            if prev1 is not None:
                prev1_body = abs(prev1['close'] - prev1['open'])
                
                # 看涨吞没
                if (current['close'] > current['open'] and  # 当前阳线
                    prev1['close'] < prev1['open'] and      # 前一根阴线
                    current['open'] < prev1['close'] and    # 当前开盘低于前一收盘
                    current['close'] > prev1['open']):      # 当前收盘高于前一开盘
                    patterns['bullish_engulfing'] = True
                
                # 看跌吞没
                if (current['close'] < current['open'] and  # 当前阴线
                    prev1['close'] > prev1['open'] and      # 前一根阳线
                    current['open'] > prev1['close'] and    # 当前开盘高于前一收盘
                    current['close'] < prev1['open']):      # 当前收盘低于前一开盘
                    patterns['bearish_engulfing'] = True
            
            patterns['available'] = True
            return patterns
            
        except Exception:
            return {'available': False, 'reason': 'calculation_error'}
    
    def identify_price_patterns(self, df: pd.DataFrame) -> Dict:
        """识别价格形态"""
        try:
            patterns = {}
            
            if len(df) < 20:
                return {'available': False, 'reason': 'insufficient_data'}
            
            # 获取最近20根K线
            recent = df.tail(20)
            highs = recent['high']
            lows = recent['low']
            closes = recent['close']
            
            # 双顶/双底检测
            patterns.update(self._detect_double_patterns(highs, lows))
            
            # 三角形形态检测
            patterns.update(self._detect_triangle_patterns(highs, lows))
            
            # 通道形态检测
            patterns.update(self._detect_channel_patterns(highs, lows))
            
            patterns['available'] = True
            return patterns
            
        except Exception:
            return {'available': False, 'reason': 'calculation_error'}
    
    def _detect_double_patterns(self, highs: pd.Series, lows: pd.Series) -> Dict:
        """检测双顶双底形态"""
        patterns = {}
        
        try:
            # 简化的双顶双底检测
            recent_highs = highs.tail(10)
            recent_lows = lows.tail(10)
            
            # 双顶检测
            max_high = recent_highs.max()
            high_count = (recent_highs > max_high * 0.995).sum()  # 接近最高点的数量
            
            if high_count >= 2:
                patterns['potential_double_top'] = True
            
            # 双底检测
            min_low = recent_lows.min()
            low_count = (recent_lows < min_low * 1.005).sum()  # 接近最低点的数量
            
            if low_count >= 2:
                patterns['potential_double_bottom'] = True
            
        except Exception:
            pass
        
        return patterns
    
    def _detect_triangle_patterns(self, highs: pd.Series, lows: pd.Series) -> Dict:
        """检测三角形形态"""
        patterns = {}
        
        try:
            # 简化的三角形检测
            recent_highs = highs.tail(10)
            recent_lows = lows.tail(10)
            
            # 计算高点和低点的趋势
            high_trend = np.polyfit(range(len(recent_highs)), recent_highs, 1)[0]
            low_trend = np.polyfit(range(len(recent_lows)), recent_lows, 1)[0]
            
            # 上升三角形
            if abs(high_trend) < 0.0001 and low_trend > 0:
                patterns['ascending_triangle'] = True
            
            # 下降三角形
            elif abs(low_trend) < 0.0001 and high_trend < 0:
                patterns['descending_triangle'] = True
            
            # 对称三角形
            elif high_trend < 0 and low_trend > 0:
                patterns['symmetrical_triangle'] = True
            
        except Exception:
            pass
        
        return patterns
    
    def _detect_channel_patterns(self, highs: pd.Series, lows: pd.Series) -> Dict:
        """检测通道形态"""
        patterns = {}
        
        try:
            # 简化的通道检测
            recent_highs = highs.tail(15)
            recent_lows = lows.tail(15)
            
            # 计算通道宽度的变化
            channel_width = recent_highs - recent_lows
            width_trend = np.polyfit(range(len(channel_width)), channel_width, 1)[0]
            
            # 平行通道
            if abs(width_trend) < 0.0001:
                patterns['parallel_channel'] = True
            
            # 扩散通道
            elif width_trend > 0:
                patterns['expanding_channel'] = True
            
            # 收缩通道
            elif width_trend < 0:
                patterns['contracting_channel'] = True
            
        except Exception:
            pass
        
        return patterns
    
    def detect_macd_divergence(self, df: pd.DataFrame, macd_line: pd.Series) -> str:
        """检测MACD背离"""
        try:
            if len(df) < 20 or len(macd_line) < 20:
                return 'insufficient_data'
            
            # 获取最近20个数据点
            recent_prices = df['close'].tail(20)
            recent_macd = macd_line.tail(20)
            
            # 寻找价格和MACD的高点低点
            price_peaks = []
            macd_peaks = []
            
            for i in range(2, len(recent_prices) - 2):
                # 价格高点
                if (recent_prices.iloc[i] > recent_prices.iloc[i-1] and 
                    recent_prices.iloc[i] > recent_prices.iloc[i+1]):
                    price_peaks.append((i, recent_prices.iloc[i]))
                
                # MACD高点
                if (recent_macd.iloc[i] > recent_macd.iloc[i-1] and 
                    recent_macd.iloc[i] > recent_macd.iloc[i+1]):
                    macd_peaks.append((i, recent_macd.iloc[i]))
            
            # 检测背离
            if len(price_peaks) >= 2 and len(macd_peaks) >= 2:
                # 最近两个高点
                price_peak1, price_peak2 = price_peaks[-2], price_peaks[-1]
                macd_peak1, macd_peak2 = macd_peaks[-2], macd_peaks[-1]
                
                # 顶背离：价格创新高，MACD不创新高
                if (price_peak2[1] > price_peak1[1] and 
                    macd_peak2[1] < macd_peak1[1]):
                    return 'bearish_divergence'
                
                # 底背离：价格创新低，MACD不创新低
                elif (price_peak2[1] < price_peak1[1] and 
                      macd_peak2[1] > macd_peak1[1]):
                    return 'bullish_divergence'
            
            return 'none'
            
        except Exception:
            return 'unknown'
