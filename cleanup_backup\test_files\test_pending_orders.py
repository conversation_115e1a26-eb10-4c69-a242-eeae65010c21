"""
挂单测试脚本
用于测试挂单功能
"""
import os
import sys
import time
from datetime import datetime

from app.utils.mt4_client import mt4_client

def test_pending_orders():
    """测试挂单功能"""
    try:
        print('=' * 50)
        print(f'开始测试挂单功能，时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        print('=' * 50)
        
        # 步骤1: 确保MT4连接
        print('\n步骤1: 确保MT4连接')
        if not mt4_client.is_connected:
            print('MT4客户端未连接，尝试连接')
            connected = mt4_client.connect()
            if not connected:
                print('无法连接到MT4客户端，测试失败')
                return
        print('MT4连接正常')
        
        # 步骤2: 获取当前挂单
        print('\n步骤2: 获取当前挂单')
        pending_orders_response = mt4_client.get_pending_orders()
        pending_orders = pending_orders_response.get('orders', [])
        print(f'当前挂单数量: {len(pending_orders)}')
        for order in pending_orders:
            print(f'  - 挂单: 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 货币对: {order.get("symbol")}, 手数: {order.get("lots")}, 价格: {order.get("open_price")}, 止损: {order.get("sl")}, 止盈: {order.get("tp")}')
        
        # 步骤3: 获取市场信息
        print('\n步骤3: 获取市场信息')
        market_info = mt4_client.get_market_info('EURUSD')
        if not market_info or market_info.get('status') != 'success':
            print('获取市场信息失败，测试终止')
            return
        
        current_price = float(market_info['data']['ask'])
        print(f'当前EURUSD价格: {current_price}')
        
        # 步骤4: 创建卖出限价单
        print('\n步骤4: 创建卖出限价单')
        limit_price = round(current_price + 0.0050, 5)  # 限价设置在当前价格上方50点
        stop_loss = round(limit_price + 0.0030, 5)  # 止损设置在限价上方30点
        take_profit = round(limit_price - 0.0050, 5)  # 止盈设置在限价下方50点
        
        print(f'创建卖出限价单: EURUSD, 手数: 0.01, 价格: {limit_price}, 止损: {stop_loss}, 止盈: {take_profit}')
        response = mt4_client.sell_limit('EURUSD', 0.01, limit_price, stop_loss, take_profit)
        print(f'卖出限价单响应: {response}')
        
        if response and response.get('status') == 'success':
            print('卖出限价单创建成功')
        else:
            print('卖出限价单创建失败')
            return
        
        # 步骤5: 再次获取挂单
        print('\n步骤5: 再次获取挂单')
        time.sleep(1)  # 等待一秒，确保订单已经被处理
        
        pending_orders_response = mt4_client.get_pending_orders()
        pending_orders = pending_orders_response.get('orders', [])
        print(f'当前挂单数量: {len(pending_orders)}')
        for order in pending_orders:
            print(f'  - 挂单: 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 货币对: {order.get("symbol")}, 手数: {order.get("lots")}, 价格: {order.get("open_price")}, 止损: {order.get("sl")}, 止盈: {order.get("tp")}')
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_pending_orders()
