"""
启动实时外汇分析系统
基于市场变化触发分析，而不是固定时间间隔
"""
import os
import sys
import time
import traceback
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ['ANALYSIS_MODE'] = 'realtime'  # 设置为实时分析模式

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'

try:
    print("正在导入必要的模块...")
    from app.utils.forex_scheduled_tasks import start_realtime_forex_analysis, stop_all_tasks
    print("模块导入成功")
except Exception as e:
    print(f"导入模块失败: {e}")
    traceback.print_exc()
    sys.exit(1)


def main():
    """主函数"""
    try:
        print("=" * 50)
        print("外汇交易系统 - 实时分析模式")
        print("=" * 50)
        print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 询问是否自动交易
        try:
            auto_trade_input = input("是否自动执行交易？(y/n): ").lower()
            auto_trade = auto_trade_input == 'y'
            print(f"用户输入: {auto_trade_input}, 自动交易设置为: {auto_trade}")
        except Exception as e:
            print(f"读取用户输入失败: {e}")
            auto_trade = False
            print(f"默认设置自动交易为: {auto_trade}")

        # 询问市场检查间隔
        try:
            check_interval_input = input("设置市场检查间隔（秒，默认60）: ")
            print(f"用户输入的检查间隔: {check_interval_input}")
            check_interval = int(check_interval_input) if check_interval_input.strip() else 60
        except ValueError:
            check_interval = 60
            print(f"输入无效，使用默认值: {check_interval}秒")
        except Exception as e:
            print(f"处理检查间隔输入时出错: {e}")
            check_interval = 60
            print(f"使用默认检查间隔: {check_interval}秒")

        # 先停止所有现有任务，确保没有多个任务同时运行
        print("\n停止所有现有任务...")
        stop_all_tasks()
        print("所有现有任务已停止")

        # 启动实时分析
        print(f"\n启动实时分析，检查间隔: {check_interval}秒，自动交易: {auto_trade}")
        try:
            start_realtime_forex_analysis(
                run_immediately=True,
                auto_trade=auto_trade,
                check_interval=check_interval
            )

            print("\n实时分析系统已启动")
            print("系统将在检测到市场变化时自动执行分析")
            print("按Ctrl+C停止系统")
        except Exception as e:
            print(f"启动实时分析失败: {e}")
            traceback.print_exc()
            return

        # 保持主线程运行
        try:
            print("主线程开始运行...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n捕获到中断信号，正在停止系统...")
            try:
                stop_all_tasks()
                print("系统已安全停止")
            except Exception as e:
                print(f"停止任务时出错: {e}")
                traceback.print_exc()
        except Exception as e:
            print(f"主线程运行出错: {e}")
            traceback.print_exc()
            try:
                stop_all_tasks()
                print("系统已安全停止")
            except Exception as stop_error:
                print(f"停止任务时出错: {stop_error}")
                traceback.print_exc()
    except Exception as main_error:
        print(f"主函数运行出错: {main_error}")
        traceback.print_exc()


if __name__ == "__main__":
    try:
        # 设置更详细的日志输出
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),  # 输出到控制台
                logging.FileHandler('forex_realtime_debug.log', encoding='utf-8')  # 输出到文件
            ]
        )
        logger = logging.getLogger('forex_realtime')
        logger.info("Starting realtime analysis system")

        # 添加控制台输出
        print("===== FOREX TRADING SYSTEM - DEBUG MODE =====")
        print("Starting realtime analysis system...")

        # 运行主函数
        main()
    except KeyboardInterrupt:
        print("\nInterrupt signal detected, stopping system...")
        try:
            stop_all_tasks()
            print("System safely stopped")
        except Exception as stop_error:
            print(f"Error stopping system: {stop_error}")
            import traceback
            traceback.print_exc()
    except Exception as error:
        print(f"Runtime error: {error}")
        import traceback
        traceback.print_exc()
        try:
            stop_all_tasks()
            print("System safely stopped")
        except Exception as stop_error:
            print(f"Error stopping system: {stop_error}")
            traceback.print_exc()
