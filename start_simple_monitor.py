#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版交易监听器
专门为Windows控制台优化，无emoji字符
"""

import sys
import os
import logging
import time
import threading
import requests
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'QuantumForex_Pro'))

class SimpleTradingMonitor:
    """简化版交易监听器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 监听状态
        self.is_monitoring = False
        self.monitor_thread = None

        # 交易数据缓存
        self.current_orders = {}
        self.alerts = []

        # 监听配置
        self.config = {
            'check_interval': 5,  # 5秒检查一次
            'duplicate_price_threshold': 0.0020,  # 20点价格差异阈值
            'max_orders_per_symbol': 2,
            'max_same_direction_orders': 1,
            'max_total_orders': 10
        }

        # 统计数据
        self.stats = {
            'total_orders_detected': 0,
            'duplicate_orders_detected': 0,
            'rule_violations': 0
        }

        # 上次检查的状态
        self.last_order_count = 0

    def start_monitoring(self):
        """启动交易监听"""
        try:
            self.logger.info("[START] 启动交易逻辑实时监听器")
            self.logger.info("[TARGET] 专门监听交易决策和订单执行逻辑")

            self.is_monitoring = True

            # 启动监听线程
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()

            self.logger.info("[OK] 交易逻辑监听器启动成功")
            self.logger.info("[DATA] 监听内容:")
            self.logger.info("   - 实时订单创建/修改/平仓")
            self.logger.info("   - 重复订单检测")
            self.logger.info("   - 持仓限制检查")
            self.logger.info("   - 风险管理合规性")
            self.logger.info("   - 异常交易行为检测")

        except Exception as e:
            self.logger.error(f"[ERROR] 启动交易逻辑监听器失败: {e}")

    def stop_monitoring(self):
        """停止监听"""
        try:
            self.logger.info("[STOP] 停止交易逻辑监听器")
            self.is_monitoring = False

            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=10)

            # 生成监听报告
            self._generate_report()

            self.logger.info("[OK] 交易逻辑监听器已停止")

        except Exception as e:
            self.logger.error(f"[ERROR] 停止监听器失败: {e}")

    def _monitor_loop(self):
        """监听主循环"""
        while self.is_monitoring:
            try:
                # 获取当前订单状态
                current_orders = self._get_current_orders()

                if current_orders is not None:
                    # 检测订单变化
                    self._detect_order_changes(current_orders)

                    # 执行各种检查
                    self._check_duplicate_orders(current_orders)
                    self._check_position_limits(current_orders)
                    self._check_risk_management(current_orders)

                    # 输出监听状态
                    if len(current_orders) != self.last_order_count:
                        self._log_order_status(current_orders)
                        self.last_order_count = len(current_orders)

                time.sleep(self.config['check_interval'])

            except Exception as e:
                self.logger.error(f"[ERROR] 监听循环异常: {e}")
                time.sleep(30)  # 异常时等待更长时间

    def _get_current_orders(self):
        """获取当前订单"""
        try:
            # 尝试直接连接MT4
            from utils.mt4_client import MT4Client
            mt4_client = MT4Client()

            if mt4_client.connect():
                orders_data = mt4_client.get_active_orders()
                if orders_data and orders_data.get('status') == 'success':
                    return orders_data.get('orders', [])

            return []

        except Exception as e:
            self.logger.debug(f"获取订单失败: {e}")
            return None

    def _detect_order_changes(self, current_orders):
        """检测订单变化"""
        try:
            current_order_ids = {order.get('order_id', '') for order in current_orders}
            previous_order_ids = set(self.current_orders.keys())

            # 检测新订单
            new_orders = current_order_ids - previous_order_ids
            for order_id in new_orders:
                order_data = next((o for o in current_orders if o.get('order_id') == order_id), None)
                if order_data:
                    self._on_new_order(order_data)

            # 检测平仓订单
            closed_orders = previous_order_ids - current_order_ids
            for order_id in closed_orders:
                self._on_order_closed(order_id)

            # 更新当前订单缓存
            self.current_orders = {}
            for order in current_orders:
                order_id = order.get('order_id', '')
                if order_id:
                    # 修复字段映射问题
                    normalized_order = {
                        'order_id': order_id,
                        'symbol': order.get('symbol', ''),
                        'action': order.get('type', ''),  # MT4返回的是'type'字段
                        'volume': order.get('lots', 0),   # MT4返回的是'lots'字段
                        'entry_price': order.get('open_price', 0),  # MT4返回的是'open_price'字段
                        'stop_loss': order.get('sl', 0),  # MT4返回的是'sl'字段
                        'take_profit': order.get('tp', 0),  # MT4返回的是'tp'字段
                        'profit': order.get('profit', 0),
                        'comment': order.get('comment', ''),
                        'status': 'active'
                    }
                    self.current_orders[order_id] = normalized_order

        except Exception as e:
            self.logger.error(f"[ERROR] 检测订单变化失败: {e}")

    def _on_new_order(self, order_data):
        """处理新订单"""
        try:
            # 使用正确的字段映射
            symbol = order_data.get('symbol', '')
            action = order_data.get('type', '')  # MT4返回的是'type'字段
            volume = order_data.get('lots', 0)   # MT4返回的是'lots'字段
            entry_price = order_data.get('open_price', 0)  # MT4返回的是'open_price'字段
            order_id = order_data.get('order_id', '')

            self.logger.info(f"[NEW] 检测到新订单: {symbol} {action} {volume}手 @ {entry_price} (ID: {order_id})")

            self.stats['total_orders_detected'] += 1

            # 立即检查新订单是否违反规则
            self._check_new_order_rules(order_data)

        except Exception as e:
            self.logger.error(f"[ERROR] 处理新订单失败: {e}")

    def _on_order_closed(self, order_id):
        """处理订单平仓"""
        try:
            if order_id in self.current_orders:
                order = self.current_orders[order_id]
                symbol = order.get('symbol', '')
                action = order.get('action', '')
                self.logger.info(f"[END] 检测到订单平仓: {symbol} {action} (ID: {order_id})")

        except Exception as e:
            self.logger.error(f"[ERROR] 处理订单平仓失败: {e}")

    def _check_new_order_rules(self, order_data):
        """检查新订单是否违反规则"""
        try:
            symbol = order_data.get('symbol', '')
            entry_price = order_data.get('open_price', 0)  # 使用正确的字段名

            # 检查是否有相似价格的订单
            similar_orders = []
            for existing_order in self.current_orders.values():
                if existing_order.get('symbol') == symbol:
                    existing_price = existing_order.get('entry_price', 0)
                    price_diff = abs(existing_price - entry_price)
                    if price_diff <= self.config['duplicate_price_threshold']:
                        similar_orders.append(existing_order)

            if similar_orders:
                self.logger.warning(f"[WARN] 检测到相似价格订单: 新订单@{entry_price}, 已有{len(similar_orders)}个相似订单")
                self.stats['duplicate_orders_detected'] += 1

        except Exception as e:
            self.logger.error(f"[ERROR] 检查新订单规则失败: {e}")

    def _check_duplicate_orders(self, current_orders):
        """检查重复订单"""
        try:
            # 按货币对分组检查
            symbol_orders = {}
            for order in current_orders:
                symbol = order.get('symbol', '')
                if symbol not in symbol_orders:
                    symbol_orders[symbol] = []
                symbol_orders[symbol].append(order)

            for symbol, orders in symbol_orders.items():
                if len(orders) > 1:
                    # 检查价格相似性
                    for i, order1 in enumerate(orders):
                        for order2 in orders[i+1:]:
                            price1 = order1.get('open_price', 0)  # 使用正确的字段名
                            price2 = order2.get('open_price', 0)  # 使用正确的字段名
                            price_diff = abs(price1 - price2)
                            if price_diff <= self.config['duplicate_price_threshold']:
                                self.logger.warning(f"[WARN] 发现相似价格订单: {symbol} 价差{price_diff:.5f}")

        except Exception as e:
            self.logger.error(f"[ERROR] 检查重复订单失败: {e}")

    def _check_position_limits(self, current_orders):
        """检查持仓限制"""
        try:
            # 检查总订单数量
            if len(current_orders) > self.config['max_total_orders']:
                self.logger.error(f"[ALERT] 总订单数量超限: {len(current_orders)} > {self.config['max_total_orders']}")
                self.stats['rule_violations'] += 1

            # 按货币对检查
            symbol_counts = {}
            symbol_directions = {}

            for order in current_orders:
                symbol = order.get('symbol', '')
                action = order.get('type', '')  # 使用正确的字段名

                # 统计每个货币对的订单数量
                symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1

                # 统计每个货币对每个方向的订单数量
                if symbol not in symbol_directions:
                    symbol_directions[symbol] = {'BUY': 0, 'SELL': 0, '0': 0, '1': 0}  # 支持MT4的数字格式
                # 处理MT4的订单类型格式
                if action in ['0', 'OP_BUY']:
                    action = 'BUY'
                elif action in ['1', 'OP_SELL']:
                    action = 'SELL'
                symbol_directions[symbol][action] = symbol_directions[symbol].get(action, 0) + 1

            # 检查每个货币对的限制
            for symbol, count in symbol_counts.items():
                if count > self.config['max_orders_per_symbol']:
                    self.logger.warning(f"[WARN] 货币对订单数量超限: {symbol} {count} > {self.config['max_orders_per_symbol']}")
                    self.stats['rule_violations'] += 1

            # 检查同方向订单限制
            for symbol, directions in symbol_directions.items():
                for direction, count in directions.items():
                    if count > self.config['max_same_direction_orders']:
                        self.logger.warning(f"[WARN] {symbol} {direction}方向订单数量超限: {count} > {self.config['max_same_direction_orders']}")
                        self.stats['rule_violations'] += 1

        except Exception as e:
            self.logger.error(f"[ERROR] 检查持仓限制失败: {e}")

    def _check_risk_management(self, current_orders):
        """检查风险管理"""
        try:
            for order in current_orders:
                symbol = order.get('symbol', '')
                stop_loss = order.get('sl', 0)        # 使用正确的字段名
                take_profit = order.get('tp', 0)      # 使用正确的字段名
                volume = order.get('lots', 0)         # 使用正确的字段名
                order_id = order.get('order_id', '')

                # 检查是否设置了止损
                if stop_loss == 0:
                    self.logger.warning(f"[WARN] 订单未设置止损: {symbol} (ID: {order_id})")

                # 检查仓位大小是否合理
                if volume > 0.1:  # 超过0.1手
                    self.logger.warning(f"[WARN] 仓位较大: {symbol} {volume}手")

        except Exception as e:
            self.logger.error(f"[ERROR] 检查风险管理失败: {e}")

    def _log_order_status(self, current_orders):
        """记录订单状态"""
        try:
            self.logger.info(f"[DATA] 当前订单状态: {len(current_orders)}个活跃订单")

            # 按货币对统计
            symbol_counts = {}
            for order in current_orders:
                symbol = order.get('symbol', '')
                symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1

            for symbol, count in symbol_counts.items():
                self.logger.info(f"   {symbol}: {count}个订单")

        except Exception as e:
            self.logger.debug(f"记录订单状态失败: {e}")

    def _generate_report(self):
        """生成监听报告"""
        try:
            self.logger.info("=" * 80)
            self.logger.info("[DATA] 交易逻辑监听报告")
            self.logger.info("=" * 80)
            self.logger.info(f"[DATA] 检测到订单: {self.stats['total_orders_detected']}")
            self.logger.info(f"[WARN] 重复订单: {self.stats['duplicate_orders_detected']}")
            self.logger.info(f"[ALERT] 规则违反: {self.stats['rule_violations']}")
            self.logger.info(f"[DATA] 总警告数: {len(self.alerts)}")

        except Exception as e:
            self.logger.error(f"[ERROR] 生成监听报告失败: {e}")

    def get_current_status(self):
        """获取当前监听状态"""
        return {
            'is_monitoring': self.is_monitoring,
            'current_orders_count': len(self.current_orders),
            'total_orders_detected': self.stats['total_orders_detected'],
            'duplicate_orders_detected': self.stats['duplicate_orders_detected'],
            'rule_violations': self.stats['rule_violations'],
            'total_alerts': len(self.alerts)
        }

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'simple_trading_monitor_{datetime.now().strftime("%Y%m%d")}.log', encoding='utf-8')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("[START] 简化版交易逻辑监听器")
    logger.info("[INFO] 专门监听交易决策和订单执行逻辑")
    logger.info("[SCAN] 实时检测重复订单、规则违反、异常行为")
    logger.info("=" * 80)

    # 创建监听器
    monitor = SimpleTradingMonitor()

    try:
        # 启动监听
        monitor.start_monitoring()

        logger.info("[SCAN] 交易逻辑监听进行中...")
        logger.info("[TIP] 按 Ctrl+C 停止监听")

        # 定期输出状态
        while monitor.is_monitoring:
            time.sleep(30)  # 每30秒输出一次状态
            status = monitor.get_current_status()
            logger.info(f"[DATA] 监听状态: 订单{status['current_orders_count']}个, 警告{status['total_alerts']}条, 违规{status['rule_violations']}次")

    except KeyboardInterrupt:
        logger.info("[STOP] 收到停止信号")
    except Exception as e:
        logger.error(f"[ERROR] 监听器运行异常: {e}")
    finally:
        # 停止监听
        monitor.stop_monitoring()

if __name__ == "__main__":
    main()
