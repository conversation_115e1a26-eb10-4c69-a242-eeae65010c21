#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统运行测试
测试整个交易系统的完整流程（除MT4连接外）
"""

import os
import sys
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_mock_market_data():
    """创建模拟市场数据"""
    base_time = datetime.now()
    mock_data = []

    # 创建100个K线数据点
    base_price = 1.13550
    for i in range(100):
        timestamp = base_time - timedelta(minutes=15 * (100 - i))
        price_change = (i % 20 - 10) * 0.0001  # 模拟价格波动

        open_price = base_price + price_change
        high_price = open_price + abs(price_change) * 0.5
        low_price = open_price - abs(price_change) * 0.5
        close_price = open_price + price_change * 0.3

        mock_data.append({
            'timestamp': timestamp.isoformat(),
            'open': round(open_price, 5),
            'high': round(high_price, 5),
            'low': round(low_price, 5),
            'close': round(close_price, 5),
            'volume': 1000 + i * 10
        })

    return mock_data

def test_complete_system_flow():
    """测试完整系统流程"""
    print("🚀 开始完整系统运行测试")
    print("=" * 80)

    test_results = []

    try:
        # 1. 初始化所有核心组件
        print("📦 步骤1：初始化核心组件")
        print("-" * 40)

        # 导入所有必要的模块
        from app.core.market_adaptive_system import MarketAdaptiveSystem
        from app.core.feedback_learning_system import FeedbackLearningSystem
        from app.core.portfolio_management_system import PortfolioManager
        from app.core.risk_management import AdvancedRiskManager
        from app.utils import forex_data_processor
        from app.utils import llm_client
        from app.utils.multi_round_analysis import should_perform_analysis

        # 初始化组件
        market_adaptive = MarketAdaptiveSystem()
        feedback_system = FeedbackLearningSystem()
        portfolio_manager = PortfolioManager()
        risk_manager = AdvancedRiskManager()
        # 注意：LLM分析器和预分析器使用函数而不是类

        print("   ✅ 所有核心组件初始化成功")
        test_results.append(('组件初始化', 'PASS', '所有核心组件成功初始化'))

    except Exception as e:
        print(f"   ❌ 组件初始化失败: {e}")
        test_results.append(('组件初始化', 'FAIL', str(e)))
        return test_results

    try:
        # 2. 数据处理流程
        print("\n📊 步骤2：数据处理流程")
        print("-" * 40)

        # 创建模拟市场数据
        mock_klines = create_mock_market_data()
        print(f"   📈 创建了 {len(mock_klines)} 个K线数据点")

        # 计算技术指标
        indicators = forex_data_processor.calculate_technical_indicators(mock_klines, 'M15')
        print(f"   📊 计算了 {len(indicators)} 个技术指标")
        print(f"      RSI: {indicators.get('rsi', 'N/A')}")
        print(f"      MA20: {indicators.get('ma_20', 'N/A')}")
        print(f"      MACD: {indicators.get('macd', 'N/A')}")

        # 构建当前市场数据
        current_market_data = {
            'symbol': 'EURUSD',
            'current_price': mock_klines[-1]['close'],
            'timestamp': mock_klines[-1]['timestamp'],
            'spread': 2,
            **indicators
        }

        print("   ✅ 数据处理流程完成")
        test_results.append(('数据处理', 'PASS', f'处理了{len(mock_klines)}个数据点，计算了{len(indicators)}个指标'))

    except Exception as e:
        print(f"   ❌ 数据处理失败: {e}")
        test_results.append(('数据处理', 'FAIL', str(e)))
        return test_results

    try:
        # 3. 市场分析流程
        print("\n🔍 步骤3：市场分析流程")
        print("-" * 40)

        # 市场自适应分析
        market_condition = market_adaptive.analyze_market_regime(current_market_data, mock_klines)
        print(f"   🎯 市场制度: {market_condition.market_regime.value}")
        print(f"   📈 推荐策略: {market_condition.recommended_strategy.value}")
        print(f"   📊 趋势强度: {market_condition.trend_strength:.3f}")
        print(f"   🎲 波动水平: {market_condition.volatility_level:.3f}")
        print(f"   🎯 信心度: {market_condition.confidence:.3f}")

        print("   ✅ 市场分析流程完成")
        test_results.append(('市场分析', 'PASS', f'市场制度: {market_condition.market_regime.value}'))

    except Exception as e:
        print(f"   ❌ 市场分析失败: {e}")
        test_results.append(('市场分析', 'FAIL', str(e)))
        return test_results

    try:
        # 4. 预分析流程
        print("\n🔍 步骤4：预分析流程")
        print("-" * 40)

        # 模拟上次分析时间（2小时前）
        last_analysis_time = datetime.now() - timedelta(hours=2, minutes=5)

        # 执行预分析
        should_analyze, reason = should_perform_analysis(current_market_data)

        pre_analysis_result = {
            'should_analyze': should_analyze,
            'reason': reason,
            'time_since_last': str(datetime.now() - last_analysis_time),
            'confidence': 0.8 if should_analyze else 0.3
        }

        print(f"   🤖 预分析结果: {pre_analysis_result['should_analyze']}")
        print(f"   📝 分析原因: {pre_analysis_result['reason']}")
        print(f"   ⏰ 上次分析: {pre_analysis_result['time_since_last']}")
        print(f"   🎯 信心度: {pre_analysis_result['confidence']:.3f}")

        print("   ✅ 预分析流程完成")
        test_results.append(('预分析', 'PASS', f"触发分析: {pre_analysis_result['should_analyze']}"))

    except Exception as e:
        print(f"   ❌ 预分析失败: {e}")
        test_results.append(('预分析', 'FAIL', str(e)))
        # 继续执行，预分析失败不影响后续流程

    try:
        # 5. LLM完整分析流程
        print("\n🧠 步骤5：LLM完整分析流程")
        print("-" * 40)

        # 准备分析数据
        analysis_data = {
            'market_data': current_market_data,
            'historical_data': mock_klines[-50:],  # 最近50个数据点
            'market_condition': {
                'regime': market_condition.market_regime.value,
                'trend_strength': market_condition.trend_strength,
                'volatility_level': market_condition.volatility_level,
                'confidence': market_condition.confidence
            },
            'technical_indicators': indicators
        }

        # 执行LLM分析
        print("   🤖 正在执行LLM分析...")
        # 使用实际的LLM分析函数
        from app.utils.multi_round_analysis import perform_multi_round_analysis

        # 准备LLM分析数据
        llm_analysis_data = {
            'symbol': current_market_data['symbol'],
            'current_price': current_market_data['current_price'],
            'indicators': indicators,
            'market_condition': market_condition.__dict__ if hasattr(market_condition, '__dict__') else {},
            'historical_data': mock_klines[-50:]
        }

        # 执行多轮分析
        llm_result = perform_multi_round_analysis(llm_analysis_data)

        print(f"   📊 分析结果:")
        print(f"      交易建议: {llm_result.get('action', 'N/A')}")
        print(f"      信心度: {llm_result.get('confidence', 'N/A')}")
        print(f"      风险等级: {llm_result.get('risk_level', 'N/A')}")
        print(f"      目标价位: {llm_result.get('target_price', 'N/A')}")
        print(f"      止损价位: {llm_result.get('stop_loss', 'N/A')}")

        if 'reasoning' in llm_result:
            reasoning_preview = llm_result['reasoning'][:100] + "..." if len(llm_result['reasoning']) > 100 else llm_result['reasoning']
            print(f"      分析理由: {reasoning_preview}")

        print("   ✅ LLM分析流程完成")
        test_results.append(('LLM分析', 'PASS', f"交易建议: {llm_result.get('action', 'N/A')}"))

    except Exception as e:
        print(f"   ❌ LLM分析失败: {e}")
        test_results.append(('LLM分析', 'FAIL', str(e)))
        # 创建模拟分析结果以继续测试
        llm_result = {
            'action': 'BUY',
            'confidence': 0.75,
            'risk_level': 'MEDIUM',
            'target_price': current_market_data['current_price'] + 0.001,
            'stop_loss': current_market_data['current_price'] - 0.0005,
            'reasoning': '模拟分析结果用于测试'
        }

    # 继续测试其他组件
    try:
        # 6. 风险管理流程
        print("\n🛡️ 步骤6：风险管理流程")
        print("-" * 40)

        # 模拟账户信息
        account_info = {
            'balance': 10000.0,
            'equity': 9950.0,
            'margin': 500.0,
            'free_margin': 9450.0
        }

        # 模拟当前持仓
        current_positions = []

        # 风险评估
        risk_metrics = risk_manager.assess_comprehensive_risk(
            account_info, current_positions, current_market_data
        )

        print(f"   📊 风险评估结果:")
        print(f"      风险等级: {risk_metrics.risk_level.value}")
        print(f"      账户回撤: {risk_metrics.account_drawdown:.3f}")
        print(f"      持仓风险: {risk_metrics.position_risk:.3f}")
        print(f"      组合风险: {risk_metrics.portfolio_risk:.3f}")
        print(f"      波动风险: {risk_metrics.volatility_risk:.3f}")
        print(f"      推荐操作: {risk_metrics.recommended_action.value}")

        print("   ✅ 风险管理流程完成")
        test_results.append(('风险管理', 'PASS', f"风险等级: {risk_metrics.risk_level.value}"))

    except Exception as e:
        print(f"   ❌ 风险管理失败: {e}")
        test_results.append(('风险管理', 'FAIL', str(e)))

    try:
        # 7. 组合管理流程
        print("\n💼 步骤7：组合管理流程")
        print("-" * 40)

        # 模拟持仓数据
        from app.core.portfolio_management_system import PositionInfo

        mock_positions = {
            'EURUSD': PositionInfo(
                symbol='EURUSD',
                action='BUY',
                lot_size=0.1,
                entry_price=1.13500,
                current_price=current_market_data['current_price'],
                unrealized_pnl=50.0,
                unrealized_pnl_pct=0.044,
                position_value=11355.0,
                margin_used=1135.5,
                days_held=1,
                stop_loss=1.13400,
                take_profit=1.13700
            )
        }

        portfolio_manager.current_positions = mock_positions

        # 组合分析
        portfolio_analysis = portfolio_manager.analyze_portfolio_risk()

        print(f"   📊 组合分析结果:")
        print(f"      总风险: {portfolio_analysis.total_risk:.3f}")
        print(f"      分散化比率: {portfolio_analysis.diversification_ratio:.3f}")
        print(f"      集中度风险: {portfolio_analysis.concentration_risk:.3f}")
        print(f"      相关性风险: {portfolio_analysis.correlation_risk:.3f}")
        print(f"      风险等级: {portfolio_analysis.risk_level}")

        # 计算组合指标
        portfolio_metrics = portfolio_manager.calculate_portfolio_metrics()
        print(f"      组合价值: ${portfolio_metrics.total_value:.2f}")
        print(f"      总盈亏: ${getattr(portfolio_metrics, 'total_pnl', 0.0):.2f}")
        print(f"      盈亏比例: {getattr(portfolio_metrics, 'total_pnl_pct', 0.0):.2%}")

        print("   ✅ 组合管理流程完成")
        test_results.append(('组合管理', 'PASS', f"组合价值: ${portfolio_metrics.total_value:.2f}"))

    except Exception as e:
        print(f"   ❌ 组合管理失败: {e}")
        test_results.append(('组合管理', 'FAIL', str(e)))

    try:
        # 8. 系统集成测试
        print("\n🔄 步骤8：系统集成测试")
        print("-" * 40)

        print("   🎯 模拟完整交易决策流程...")
        print("      数据处理 → 市场分析 → 预分析 → LLM分析 → 风险评估 → 组合管理")

        # 验证数据流
        data_flow_valid = (
            len(mock_klines) > 0 and
            len(indicators) > 0 and
            market_condition is not None and
            'pre_analysis_result' in locals() and pre_analysis_result['should_analyze'] is not None and
            llm_result is not None
        )

        if data_flow_valid:
            print("   ✅ 数据流验证成功")
            print("   ✅ 所有组件协同工作正常")
            test_results.append(('系统集成', 'PASS', '完整交易决策流程正常'))
        else:
            print("   ❌ 数据流验证失败")
            test_results.append(('系统集成', 'FAIL', '数据流不完整'))

    except Exception as e:
        print(f"   ❌ 系统集成测试失败: {e}")
        test_results.append(('系统集成', 'FAIL', str(e)))

    return test_results, {
        'market_data': current_market_data,
        'market_condition': market_condition,
        'llm_result': llm_result,
        'risk_metrics': risk_metrics if 'risk_metrics' in locals() else None,
        'portfolio_analysis': portfolio_analysis if 'portfolio_analysis' in locals() else None
    }

def main():
    """主测试函数"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始完整系统运行测试")

    # 执行完整系统流程测试
    test_results, system_data = test_complete_system_flow()

    # 统计结果
    total_tests = len(test_results)
    passed_tests = len([r for r in test_results if r[1] == 'PASS'])
    failed_tests = len([r for r in test_results if r[1] == 'FAIL'])

    print(f"\n📊 完整系统测试结果")
    print("=" * 80)
    print(f"总测试数: {total_tests}")
    print(f"测试通过: {passed_tests}")
    print(f"测试失败: {failed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")

    print(f"\n📋 详细结果:")
    for test_name, status, details in test_results:
        status_icon = "✅" if status == "PASS" else "❌"
        print(f"   {status_icon} {test_name}: {status} - {details}")

    if passed_tests == total_tests:
        print(f"\n🎉 完整系统测试成功！")
        print("   ✅ 所有核心组件正常工作")
        print("   ✅ 数据处理流程完整")
        print("   ✅ 市场分析功能正常")
        print("   ✅ 预分析系统工作")
        print("   ✅ LLM分析系统工作")
        print("   ✅ 风险管理系统正常")
        print("   ✅ 组合管理功能完整")
        print("   ✅ 系统集成测试通过")
        print("   ✅ 系统各组件协同工作良好")
        print("   ⚠️  只有MT4连接功能未测试（服务器关闭）")

        print(f"\n🚀 系统状态总结:")
        print("   📦 所有核心模块已加载并初始化")
        print("   📊 数据处理和技术指标计算正常")
        print("   🧠 AI分析系统（LLM + 预分析）工作正常")
        print("   🛡️ 风险管理和组合管理系统正常")
        print("   🔄 系统各组件协同工作良好")
        print("   ⚠️  只有MT4连接功能未测试（服务器关闭）")

        print(f"\n✨ 系统已准备就绪，可以在MT4服务器开放时进行实盘测试！")
        return True
    else:
        print(f"\n⚠️ 部分系统功能需要进一步检查")
        failed_components = [r[0] for r in test_results if r[1] == 'FAIL']
        print(f"   失败组件: {', '.join(failed_components)}")
        return False

if __name__ == "__main__":
    success = main()

    if success:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🎉 完整系统测试成功！")
        print("系统已准备就绪，除MT4连接外所有功能正常！")
    else:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ⚠️ 系统测试发现问题，需要进一步检查")