<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外汇交易系统监控面板</title>
    <!-- 引入Chart.js用于图表显示 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.4em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-weight: 500;
            color: #555;
        }

        .status-value {
            font-weight: bold;
            color: #333;
        }

        .status-good {
            color: #28a745;
        }

        .status-warning {
            color: #ffc107;
        }

        .status-error {
            color: #dc3545;
        }

        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 25px;
            font-size: 16px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .error {
            color: #dc3545;
            text-align: center;
            font-weight: bold;
        }

        .timestamp {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 20px;
        }

        .strategy-details {
            margin-top: 15px;
        }

        .strategy-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }

        .strategy-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .strategy-info {
            font-size: 0.9em;
            color: #666;
        }

        .risk-level {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }

        .risk-low { background: #d4edda; color: #155724; }
        .risk-medium { background: #fff3cd; color: #856404; }
        .risk-high { background: #f8d7da; color: #721c24; }
        .risk-critical { background: #f5c6cb; color: #721c24; }
        .risk-emergency { background: #f1b0b7; color: #721c24; }

        /* 新增样式 */
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 15px;
        }

        .small-chart-container {
            position: relative;
            height: 200px;
            margin-top: 15px;
        }

        .gauge-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 150px;
            margin: 15px 0;
        }

        .gauge {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(
                #28a745 0deg 120deg,
                #ffc107 120deg 240deg,
                #dc3545 240deg 360deg
            );
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .gauge-inner {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            font-size: 14px;
        }

        .signal-strength {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }

        .signal-bars {
            display: flex;
            margin-left: 10px;
        }

        .signal-bar {
            width: 8px;
            height: 20px;
            margin: 0 2px;
            background: #ddd;
            border-radius: 2px;
        }

        .signal-bar.active-1 { background: #dc3545; }
        .signal-bar.active-2 { background: #ffc107; }
        .signal-bar.active-3 { background: #28a745; }
        .signal-bar.active-4 { background: #007bff; }
        .signal-bar.active-5 { background: #6f42c1; }

        .market-status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .market-open { background: #28a745; }
        .market-closed { background: #dc3545; }
        .market-pre { background: #ffc107; }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 5px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .metric-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9em;
            color: #666;
        }

        .timeline {
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .timeline-time {
            font-size: 0.8em;
            color: #666;
            min-width: 80px;
        }

        .timeline-content {
            flex: 1;
            margin-left: 10px;
        }

        .timeline-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #667eea;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 外汇交易系统监控面板</h1>
            <p>实时监控系统状态、风险管理、策略优化和性能指标</p>
        </div>

        <div class="dashboard-grid">
            <!-- 系统状态 -->
            <div class="card">
                <h3>📊 系统状态</h3>
                <div id="system-status" class="loading">正在加载...</div>
                <div class="gauge-container">
                    <div class="gauge">
                        <div class="gauge-inner" id="system-health-gauge">100%</div>
                    </div>
                </div>
            </div>

            <!-- 市场状态 -->
            <div class="card">
                <h3>🌍 市场状态</h3>
                <div id="market-status" class="loading">正在加载...</div>
                <div class="small-chart-container">
                    <canvas id="market-hours-chart"></canvas>
                </div>
            </div>

            <!-- Token消耗统计 -->
            <div class="card">
                <h3>💎 Token消耗统计</h3>
                <div id="token-statistics" class="loading">正在加载...</div>
                <div class="small-chart-container">
                    <canvas id="token-chart"></canvas>
                </div>
            </div>

            <!-- 风险管理 -->
            <div class="card">
                <h3>🛡️ 风险管理</h3>
                <div id="risk-management" class="loading">正在加载...</div>
                <div class="signal-strength">
                    <span>风险等级:</span>
                    <div class="signal-bars" id="risk-level-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                </div>
            </div>

            <!-- 策略优化 -->
            <div class="card">
                <h3>🎯 策略优化</h3>
                <div id="strategy-optimization" class="loading">正在加载...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="strategy-efficiency" style="width: 0%"></div>
                </div>
                <div style="text-align: center; margin-top: 5px; font-size: 0.9em; color: #666;">
                    策略效率
                </div>
            </div>

            <!-- 交易信号强度 -->
            <div class="card">
                <h3>📡 交易信号强度</h3>
                <div id="signal-strength" class="loading">正在加载...</div>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="signal-buy">0</div>
                        <div class="metric-label">买入信号</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="signal-sell">0</div>
                        <div class="metric-label">卖出信号</div>
                    </div>
                </div>
            </div>

            <!-- 性能指标 -->
            <div class="card">
                <h3>📈 性能指标</h3>
                <div id="performance-metrics" class="loading">正在加载...</div>
                <div class="chart-container">
                    <canvas id="performance-chart"></canvas>
                </div>
            </div>

            <!-- 交易统计 -->
            <div class="card">
                <h3>💰 交易统计</h3>
                <div id="trading-stats" class="loading">正在加载...</div>
                <div class="small-chart-container">
                    <canvas id="trading-chart"></canvas>
                </div>
            </div>

            <!-- 分析历史时间线 -->
            <div class="card">
                <h3>📋 分析历史</h3>
                <div id="analysis-timeline" class="loading">正在加载...</div>
                <div class="timeline" id="timeline-container">
                    <!-- 时间线内容将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 最近告警 -->
            <div class="card">
                <h3>⚠️ 最近告警</h3>
                <div id="recent-alerts" class="loading">正在加载...</div>
            </div>
        </div>

        <div class="timestamp" id="last-update">
            最后更新时间: --
        </div>
    </div>

    <button class="refresh-btn" onclick="loadDashboardData()">🔄 刷新数据</button>

    <script>
        // 全局图表对象
        let charts = {};

        // 加载仪表板数据
        async function loadDashboardData() {
            try {
                const response = await fetch('/monitoring/api/dashboard');
                const result = await response.json();

                if (result.success) {
                    updateSystemStatus(result.data.current_status);
                    updateMarketStatus(result.data.current_status);
                    updateTokenStatistics(result.data.performance_metrics);
                    updateRiskManagement(result.data.risk_management);
                    updateStrategyOptimization(result.data.strategy_optimization);
                    updateSignalStrength(result.data.current_status);
                    updatePerformanceMetrics(result.data.performance_metrics);
                    updateTradingStats(result.data.current_status);
                    updateAnalysisTimeline(result.data.recent_alerts);
                    updateRecentAlerts(result.data.recent_alerts);

                    document.getElementById('last-update').textContent =
                        `最后更新时间: ${new Date(result.timestamp).toLocaleString()}`;
                } else {
                    showError('加载数据失败: ' + result.error);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        // 更新系统状态
        function updateSystemStatus(status) {
            const container = document.getElementById('system-status');
            if (!status) {
                container.innerHTML = '<div class="error">系统状态数据不可用</div>';
                return;
            }

            // 计算系统健康度
            const cpuHealth = Math.max(0, 100 - (status.cpu_percent || 0));
            const memoryHealth = Math.max(0, 100 - (status.memory_percent || 0));
            const systemHealth = Math.round((cpuHealth + memoryHealth) / 2);

            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">系统状态</span>
                    <span class="status-value status-good">${status.system_status || '运行中'}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">CPU使用率</span>
                    <span class="status-value">${status.cpu_percent || 0}%</span>
                </div>
                <div class="status-item">
                    <span class="status-label">内存使用率</span>
                    <span class="status-value">${status.memory_percent || 0}%</span>
                </div>
                <div class="status-item">
                    <span class="status-label">监控状态</span>
                    <span class="status-value status-good">${status.monitoring_status || '活跃'}</span>
                </div>
            `;

            // 更新系统健康度仪表盘
            document.getElementById('system-health-gauge').textContent = systemHealth + '%';
        }

        // 更新市场状态
        async function updateMarketStatus(status) {
            const container = document.getElementById('market-status');
            if (!status) {
                container.innerHTML = '<div class="error">市场状态数据不可用</div>';
                return;
            }

            try {
                // 从后端API获取真实的市场状态
                const response = await fetch('/monitoring/api/market-status');
                const result = await response.json();

                let marketStatus = 'closed';
                let marketText = '休市';
                let timeUntilOpen = '未知';

                if (result.success && result.data) {
                    const marketData = result.data;
                    marketStatus = marketData.is_open ? 'open' : 'closed';
                    marketText = marketData.status || '未知';
                    timeUntilOpen = marketData.time_until_open || '市场已开放';
                } else {
                    // 如果API失败，使用本地时间简化判断
                    const now = new Date();
                    const hour = now.getHours();
                    if (hour >= 5 && hour < 17) {
                        marketStatus = 'open';
                        marketText = '开市';
                        timeUntilOpen = '市场已开放';
                    }
                }

                container.innerHTML = `
                    <div class="status-item">
                        <span class="status-label">市场状态</span>
                        <span class="status-value">
                            <span class="market-status-indicator market-${marketStatus}"></span>
                            ${marketText}
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">距离开市</span>
                        <span class="status-value">${timeUntilOpen}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">当前时间</span>
                        <span class="status-value">${new Date().toLocaleString()}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">主要货币对</span>
                        <span class="status-value">EURUSD, GBPUSD, USDJPY</span>
                    </div>
                `;

                // 创建市场时间图表
                createMarketHoursChart(marketStatus);
            } catch (error) {
                console.error('获取市场状态失败:', error);
                container.innerHTML = '<div class="error">获取市场状态失败</div>';
            }
        }

        // 更新Token统计
        function updateTokenStatistics(performanceData) {
            const container = document.getElementById('token-statistics');
            if (!performanceData || !performanceData.llm_performance) {
                container.innerHTML = '<div class="error">Token统计数据不可用</div>';
                return;
            }

            const llm = performanceData.llm_performance;
            const totalTokens = llm.total_tokens || 0;
            const totalCost = llm.total_cost || 0;
            const analysisCount = llm.total_analyses || 0;
            const avgTokensPerAnalysis = analysisCount > 0 ? Math.round(totalTokens / analysisCount) : 0;

            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">总Token消耗</span>
                    <span class="status-value">${totalTokens.toLocaleString()}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">总成本</span>
                    <span class="status-value">¥${totalCost.toFixed(4)}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">分析次数</span>
                    <span class="status-value">${analysisCount}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">平均Token/次</span>
                    <span class="status-value">${avgTokensPerAnalysis.toLocaleString()}</span>
                </div>
            `;

            // 创建Token消耗图表
            createTokenChart(totalTokens, totalCost, analysisCount);
        }

        // 更新风险管理
        function updateRiskManagement(riskData) {
            const container = document.getElementById('risk-management');
            if (!riskData || riskData.status === 'error') {
                container.innerHTML = '<div class="error">风险管理数据不可用</div>';
                return;
            }

            const limits = riskData.risk_limits || {};
            const stats = riskData.daily_stats || {};

            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">检查间隔</span>
                    <span class="status-value">${riskData.check_interval}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">单笔最大风险</span>
                    <span class="status-value">${(limits.max_single_position * 100).toFixed(1)}%</span>
                </div>
                <div class="status-item">
                    <span class="status-label">组合最大风险</span>
                    <span class="status-value">${(limits.max_portfolio_risk * 100).toFixed(1)}%</span>
                </div>
                <div class="status-item">
                    <span class="status-label">日最大亏损</span>
                    <span class="status-value">${(limits.max_daily_loss * 100).toFixed(1)}%</span>
                </div>
                <div class="status-item">
                    <span class="status-label">今日交易次数</span>
                    <span class="status-value">${stats.trades_count || 0}/${limits.max_daily_trades || 5}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">连续亏损</span>
                    <span class="status-value">${stats.consecutive_losses || 0}/${limits.max_consecutive_losses || 4}</span>
                </div>
            `;

            // 更新风险等级指示器
            updateRiskLevelBars('low'); // 默认低风险
        }

        // 更新风险等级指示器
        function updateRiskLevelBars(riskLevel) {
            const bars = document.querySelectorAll('#risk-level-bars .signal-bar');
            bars.forEach(bar => bar.className = 'signal-bar'); // 重置

            const levels = { 'low': 1, 'medium': 2, 'high': 3, 'critical': 4, 'emergency': 5 };
            const level = levels[riskLevel] || 1;

            for (let i = 0; i < level; i++) {
                if (bars[i]) {
                    bars[i].classList.add(`active-${i + 1}`);
                }
            }
        }

        // 更新策略优化
        function updateStrategyOptimization(strategyData) {
            const container = document.getElementById('strategy-optimization');
            if (!strategyData || strategyData.status === 'error') {
                container.innerHTML = '<div class="error">策略优化数据不可用</div>';
                return;
            }

            const constraints = strategyData.llm_constraints || {};

            let strategiesHtml = '';
            if (strategyData.strategy_details) {
                Object.entries(strategyData.strategy_details).forEach(([key, strategy]) => {
                    const isActive = key === strategyData.current_strategy;
                    strategiesHtml += `
                        <div class="strategy-item" style="${isActive ? 'border-left-color: #28a745; background: #d4edda;' : ''}">
                            <div class="strategy-name">${key.toUpperCase()}策略 ${isActive ? '(当前)' : ''}</div>
                            <div class="strategy-info">
                                时间框架: ${strategy.timeframe} |
                                最小间隔: ${strategy.min_interval} |
                                风险回报比: ${strategy.risk_reward}
                            </div>
                            <div class="strategy-info">适用: ${strategy.scenario}</div>
                        </div>
                    `;
                });
            }

            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">当前策略</span>
                    <span class="status-value status-good">${strategyData.current_strategy?.toUpperCase() || '未知'}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">LLM分析时间</span>
                    <span class="status-value">${constraints.min_analysis_time || 3}-${constraints.max_analysis_time || 8}分钟</span>
                </div>
                <div class="status-item">
                    <span class="status-label">上次分析</span>
                    <span class="status-value">${strategyData.last_analysis_time ? new Date(strategyData.last_analysis_time).toLocaleString() : '无'}</span>
                </div>
                <div class="strategy-details">
                    <strong>可用策略:</strong>
                    ${strategiesHtml}
                </div>
            `;

            // 更新策略效率进度条
            const efficiency = Math.floor(Math.random() * 100); // 模拟效率数据
            document.getElementById('strategy-efficiency').style.width = efficiency + '%';
        }

        // 更新性能指标
        function updatePerformanceMetrics(performanceData) {
            const container = document.getElementById('performance-metrics');
            if (!performanceData || performanceData.status === 'error') {
                container.innerHTML = '<div class="error">性能指标数据不可用</div>';
                return;
            }

            const trading = performanceData.trading_performance || {};
            const llm = performanceData.llm_performance || {};
            const system = performanceData.system_performance || {};

            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">总交易次数</span>
                    <span class="status-value">${trading.total_trades || 0}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">胜率</span>
                    <span class="status-value ${trading.win_rate > 60 ? 'status-good' : trading.win_rate > 40 ? 'status-warning' : 'status-error'}">${trading.win_rate || 0}%</span>
                </div>
                <div class="status-item">
                    <span class="status-label">LLM分析次数</span>
                    <span class="status-value">${llm.total_analyses || 0}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">分析成功率</span>
                    <span class="status-value ${llm.success_rate > 90 ? 'status-good' : llm.success_rate > 70 ? 'status-warning' : 'status-error'}">${llm.success_rate || 0}%</span>
                </div>
                <div class="status-item">
                    <span class="status-label">系统健康度</span>
                    <span class="status-value status-good">${system.system_health || 'healthy'}</span>
                </div>
            `;

            // 创建性能图表
            createPerformanceChart(performanceData);
        }

        // 更新交易统计
        function updateTradingStats(status) {
            const container = document.getElementById('trading-stats');
            if (!status) {
                container.innerHTML = '<div class="error">交易统计数据不可用</div>';
                return;
            }

            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">当前持仓</span>
                    <span class="status-value">${status.current_positions || 0}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">挂单数量</span>
                    <span class="status-value">${status.pending_orders || 0}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">净利润</span>
                    <span class="status-value ${status.net_profit > 0 ? 'status-good' : status.net_profit < 0 ? 'status-error' : ''}">${status.net_profit || 0}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">最大回撤</span>
                    <span class="status-value ${status.max_drawdown > 15 ? 'status-error' : status.max_drawdown > 10 ? 'status-warning' : 'status-good'}">${status.max_drawdown || 0}%</span>
                </div>
                <div class="status-item">
                    <span class="status-label">风险等级</span>
                    <span class="status-value risk-level risk-${(status.risk_level || 'low').toLowerCase()}">${status.risk_level || 'LOW'}</span>
                </div>
            `;

            // 创建交易图表
            createTradingChart(status);
        }

        // 更新最近告警
        function updateRecentAlerts(alerts) {
            const container = document.getElementById('recent-alerts');
            if (!alerts || alerts.length === 0) {
                container.innerHTML = '<div class="status-good">暂无告警信息</div>';
                return;
            }

            let alertsHtml = '';
            alerts.slice(0, 5).forEach(alert => {
                const time = new Date(alert.timestamp).toLocaleString();
                alertsHtml += `
                    <div class="status-item">
                        <span class="status-label">${time}</span>
                        <span class="status-value status-${alert.level}">${alert.message}</span>
                    </div>
                `;
            });

            container.innerHTML = alertsHtml;
        }

        // 更新信号强度
        function updateSignalStrength(status) {
            const container = document.getElementById('signal-strength');
            if (!status) {
                container.innerHTML = '<div class="error">信号强度数据不可用</div>';
                return;
            }

            // 模拟信号强度数据
            const buySignal = Math.floor(Math.random() * 10);
            const sellSignal = Math.floor(Math.random() * 10);
            const signalStrength = Math.max(buySignal, sellSignal);

            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">信号强度</span>
                    <span class="status-value">${signalStrength}/10</span>
                </div>
                <div class="status-item">
                    <span class="status-label">信号类型</span>
                    <span class="status-value">${buySignal > sellSignal ? '买入倾向' : '卖出倾向'}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">信号可靠性</span>
                    <span class="status-value">${signalStrength > 7 ? '高' : signalStrength > 4 ? '中' : '低'}</span>
                </div>
            `;

            // 更新信号指示器
            document.getElementById('signal-buy').textContent = buySignal;
            document.getElementById('signal-sell').textContent = sellSignal;
        }

        // 更新分析时间线
        function updateAnalysisTimeline(alerts) {
            const container = document.getElementById('analysis-timeline');
            const timelineContainer = document.getElementById('timeline-container');

            if (!alerts || alerts.length === 0) {
                container.innerHTML = '<div class="status-good">暂无分析记录</div>';
                timelineContainer.innerHTML = '';
                return;
            }

            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">最近分析</span>
                    <span class="status-value">${alerts.length} 条记录</span>
                </div>
                <div class="status-item">
                    <span class="status-label">分析频率</span>
                    <span class="status-value">每30分钟</span>
                </div>
            `;

            // 生成时间线
            let timelineHtml = '';
            alerts.slice(0, 10).forEach(alert => {
                const time = new Date(alert.timestamp).toLocaleTimeString();
                timelineHtml += `
                    <div class="timeline-item">
                        <div class="timeline-time">${time}</div>
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">${alert.message}</div>
                    </div>
                `;
            });

            timelineContainer.innerHTML = timelineHtml;
        }

        // 创建市场时间图表
        function createMarketHoursChart(marketStatus) {
            const ctx = document.getElementById('market-hours-chart').getContext('2d');

            if (charts.marketHours) {
                charts.marketHours.destroy();
            }

            charts.marketHours = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['开市时间', '休市时间'],
                    datasets: [{
                        data: marketStatus === 'open' ? [70, 30] : [30, 70],
                        backgroundColor: ['#28a745', '#dc3545'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: { size: 10 }
                            }
                        }
                    }
                }
            });
        }

        // 创建Token图表
        function createTokenChart(totalTokens, totalCost, analysisCount) {
            const ctx = document.getElementById('token-chart').getContext('2d');

            if (charts.token) {
                charts.token.destroy();
            }

            charts.token = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Token消耗', '成本(¥)', '分析次数'],
                    datasets: [{
                        label: '统计数据',
                        data: [totalTokens / 1000, totalCost * 100, analysisCount],
                        backgroundColor: ['#667eea', '#764ba2', '#f093fb'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });
        }

        // 创建性能图表
        function createPerformanceChart(performanceData) {
            const ctx = document.getElementById('performance-chart').getContext('2d');

            if (charts.performance) {
                charts.performance.destroy();
            }

            const trading = performanceData.trading_performance || {};
            const llm = performanceData.llm_performance || {};

            charts.performance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['胜率', 'LLM成功率', '系统健康度'],
                    datasets: [{
                        label: '性能指标 (%)',
                        data: [trading.win_rate || 0, llm.success_rate || 0, 95],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        // 创建交易图表
        function createTradingChart(tradingData) {
            const ctx = document.getElementById('trading-chart').getContext('2d');

            if (charts.trading) {
                charts.trading.destroy();
            }

            charts.trading = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['盈利交易', '亏损交易', '待定交易'],
                    datasets: [{
                        data: [60, 30, 10],
                        backgroundColor: ['#28a745', '#dc3545', '#ffc107'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: { size: 10 }
                            }
                        }
                    }
                }
            });
        }

        // 显示错误
        function showError(message) {
            document.querySelectorAll('.loading').forEach(el => {
                el.innerHTML = `<div class="error">${message}</div>`;
            });
        }

        // 页面加载时自动获取数据
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();

            // 每30秒自动刷新一次
            setInterval(loadDashboardData, 30000);
        });
    </script>
</body>
</html>
