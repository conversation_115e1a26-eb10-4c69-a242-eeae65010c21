"""
QuantumForex MLTrainer 交易端集成模块
处理与QuantumForex_Pro交易端的数据同步和模型部署
"""

import os
import json
import shutil
import logging
import requests
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any

from config.network_config import network_config
from utils.compatibility_manager import CompatibilityManager

class TradingIntegration:
    """交易端集成管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.compatibility_manager = CompatibilityManager()
        
        # 网络配置
        self.training_ip = network_config.NETWORK_CONFIG['training_server_ip']
        self.trading_ip = network_config.NETWORK_CONFIG['trading_server_ip']
        
        # 文件路径配置
        self.shared_folder = Path(network_config.FILE_SHARE_CONFIG['shared_folder_local'])
        self.models_folder = self.shared_folder / 'models'
        
        # 交易端模型路径（需要根据实际情况调整）
        self.trading_models_paths = [
            Path(r'../QuantumForex_Pro/data/models'),
            Path(r'C:\QuantumForex_Pro\data\models'),  # 如果在不同盘符
        ]
        
        # 支持的模型类型
        self.model_types = [
            'price_prediction_model',
            'risk_assessment_model',
            'trend_classification_model',
            'volatility_prediction_model'
        ]
    
    def detect_trading_server(self) -> bool:
        """检测交易端服务器"""
        try:
            self.logger.info("🔍 检测交易端服务器...")
            
            # 方法1: 尝试找到交易端模型文件夹
            trading_models_path = None
            for path in self.trading_models_paths:
                if path.exists():
                    trading_models_path = path
                    self.logger.info(f"✅ 发现交易端模型路径: {path}")
                    break
            
            if not trading_models_path:
                self.logger.warning("⚠️ 未找到交易端模型路径")
                return False
            
            # 方法2: 检查交易端模型文件
            existing_models = list(trading_models_path.glob('*.pkl'))
            self.logger.info(f"📊 发现现有模型: {len(existing_models)}个")
            
            for model_file in existing_models:
                self.logger.info(f"  - {model_file.name}")
            
            # 方法3: 尝试网络连接（如果交易端有API）
            try:
                # 这里可以添加对交易端API的检测
                pass
            except:
                pass
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 检测交易端服务器失败: {e}")
            return False
    
    def backup_existing_models(self) -> bool:
        """备份现有模型"""
        try:
            self.logger.info("💾 备份现有模型...")
            
            backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_folder = self.shared_folder / 'backup' / f'trading_models_{backup_timestamp}'
            backup_folder.mkdir(parents=True, exist_ok=True)
            
            backed_up_count = 0
            
            for trading_path in self.trading_models_paths:
                if not trading_path.exists():
                    continue
                
                for model_file in trading_path.glob('*.pkl'):
                    try:
                        backup_file = backup_folder / model_file.name
                        shutil.copy2(model_file, backup_file)
                        self.logger.info(f"✅ 已备份: {model_file.name}")
                        backed_up_count += 1
                    except Exception as e:
                        self.logger.error(f"❌ 备份失败 {model_file.name}: {e}")
            
            # 备份scaler文件
            for trading_path in self.trading_models_paths:
                if not trading_path.exists():
                    continue
                
                for scaler_file in trading_path.glob('*scaler*.pkl'):
                    try:
                        backup_file = backup_folder / scaler_file.name
                        shutil.copy2(scaler_file, backup_file)
                        self.logger.info(f"✅ 已备份scaler: {scaler_file.name}")
                        backed_up_count += 1
                    except Exception as e:
                        self.logger.error(f"❌ 备份scaler失败 {scaler_file.name}: {e}")
            
            self.logger.info(f"📊 备份完成: {backed_up_count}个文件")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 备份现有模型失败: {e}")
            return False
    
    def create_compatibility_bridge(self) -> bool:
        """创建兼容性桥接"""
        try:
            self.logger.info("🌉 创建兼容性桥接...")
            
            # 创建模型映射配置
            model_mapping = {
                'price_prediction_model.pkl': {
                    'new_name': 'price_prediction_model_v2.pkl',
                    'compatibility_wrapper': True,
                    'feature_mapping': True
                },
                'risk_assessment_model.pkl': {
                    'new_name': 'risk_assessment_model_v2.pkl',
                    'compatibility_wrapper': True,
                    'feature_mapping': True
                },
                'trend_classification_model.pkl': {
                    'new_name': 'trend_classification_model_v2.pkl',
                    'compatibility_wrapper': True,
                    'feature_mapping': True
                },
                'volatility_prediction_model.pkl': {
                    'new_name': 'volatility_prediction_model_v2.pkl',
                    'compatibility_wrapper': True,
                    'feature_mapping': True
                }
            }
            
            # 保存映射配置
            mapping_file = self.shared_folder / 'config' / 'model_mapping.json'
            mapping_file.parent.mkdir(exist_ok=True)
            
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(model_mapping, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ 模型映射配置已保存: {mapping_file}")
            
            # 创建兼容性检查脚本
            self._create_compatibility_checker()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 创建兼容性桥接失败: {e}")
            return False
    
    def deploy_models_to_trading_server(self, model_files: List[str] = None) -> bool:
        """部署模型到交易端服务器"""
        try:
            self.logger.info("🚀 部署模型到交易端...")
            
            if not model_files:
                # 获取所有可用的新模型
                model_files = list(self.models_folder.glob('current/*.pkl'))
            
            if not model_files:
                self.logger.warning("⚠️ 没有找到可部署的模型")
                return False
            
            deployed_count = 0
            
            for model_file in model_files:
                if isinstance(model_file, str):
                    model_file = Path(model_file)
                
                success = self._deploy_single_model(model_file)
                if success:
                    deployed_count += 1
            
            self.logger.info(f"📊 部署完成: {deployed_count}/{len(model_files)}个模型")
            
            # 创建部署报告
            self._create_deployment_report(model_files, deployed_count)
            
            return deployed_count > 0
            
        except Exception as e:
            self.logger.error(f"❌ 部署模型失败: {e}")
            return False
    
    def _deploy_single_model(self, model_file: Path) -> bool:
        """部署单个模型"""
        try:
            model_name = model_file.stem
            
            # 找到目标交易端路径
            target_trading_path = None
            for trading_path in self.trading_models_paths:
                if trading_path.exists():
                    target_trading_path = trading_path
                    break
            
            if not target_trading_path:
                self.logger.error("❌ 找不到交易端模型路径")
                return False
            
            # 确定目标文件名（保持兼容性）
            target_file = target_trading_path / f"{model_name}.pkl"
            
            # 备份现有文件
            if target_file.exists():
                backup_file = target_file.with_suffix(f'.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pkl')
                shutil.copy2(target_file, backup_file)
                self.logger.info(f"📦 已备份现有模型: {backup_file.name}")
            
            # 复制新模型
            shutil.copy2(model_file, target_file)
            self.logger.info(f"✅ 模型已部署: {model_name}")
            
            # 验证部署
            if target_file.exists() and target_file.stat().st_size > 0:
                self.logger.info(f"✅ 部署验证成功: {target_file}")
                return True
            else:
                self.logger.error(f"❌ 部署验证失败: {target_file}")
                return False
            
        except Exception as e:
            self.logger.error(f"❌ 部署单个模型失败 {model_file}: {e}")
            return False
    
    def _create_compatibility_checker(self):
        """创建兼容性检查脚本"""
        checker_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
'''
QuantumForex 模型兼容性检查器
检查新模型与交易端的兼容性
'''

import joblib
import numpy as np
from pathlib import Path

def check_model_compatibility(model_path):
    '''检查模型兼容性'''
    try:
        # 加载模型
        model = joblib.load(model_path)
        
        # 检查模型类型
        model_type = type(model).__name__
        print(f"模型类型: {model_type}")
        
        # 检查模型方法
        required_methods = ['predict', 'predict_proba']
        available_methods = [method for method in required_methods if hasattr(model, method)]
        print(f"可用方法: {available_methods}")
        
        # 检查特征数量（如果可能）
        if hasattr(model, 'n_features_in_'):
            print(f"特征数量: {model.n_features_in_}")
        
        return True
        
    except Exception as e:
        print(f"兼容性检查失败: {e}")
        return False

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        model_path = sys.argv[1]
        check_model_compatibility(model_path)
    else:
        print("用法: python compatibility_checker.py <model_path>")
"""
        
        checker_file = self.shared_folder / 'scripts' / 'compatibility_checker.py'
        checker_file.parent.mkdir(exist_ok=True)
        
        with open(checker_file, 'w', encoding='utf-8') as f:
            f.write(checker_script)
        
        self.logger.info(f"✅ 兼容性检查脚本已创建: {checker_file}")
    
    def _create_deployment_report(self, model_files: List[Path], deployed_count: int):
        """创建部署报告"""
        report = {
            'deployment_time': datetime.now().isoformat(),
            'total_models': len(model_files),
            'deployed_models': deployed_count,
            'success_rate': deployed_count / len(model_files) if model_files else 0,
            'models': []
        }
        
        for model_file in model_files:
            if isinstance(model_file, str):
                model_file = Path(model_file)
            
            model_info = {
                'name': model_file.stem,
                'file_size': model_file.stat().st_size if model_file.exists() else 0,
                'modified_time': datetime.fromtimestamp(model_file.stat().st_mtime).isoformat() if model_file.exists() else None
            }
            report['models'].append(model_info)
        
        # 保存报告
        report_file = self.shared_folder / 'logs' / f'deployment_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📊 部署报告已保存: {report_file}")
    
    def sync_data_from_trading_server(self) -> bool:
        """从交易端同步数据"""
        try:
            self.logger.info("🔄 从交易端同步数据...")
            
            # 这里可以添加从交易端获取交易结果数据的逻辑
            # 例如：读取交易日志、获取性能指标等
            
            # 示例：同步交易结果文件
            trading_data_paths = [
                Path(r'../QuantumForex_Pro/logs'),
                Path(r'../QuantumForex_Pro/data/trading_results'),
            ]
            
            sync_folder = self.shared_folder / 'data' / 'trading_sync'
            sync_folder.mkdir(parents=True, exist_ok=True)
            
            synced_files = 0
            
            for data_path in trading_data_paths:
                if not data_path.exists():
                    continue
                
                # 同步日志文件
                for log_file in data_path.glob('*.log'):
                    try:
                        target_file = sync_folder / log_file.name
                        if not target_file.exists() or log_file.stat().st_mtime > target_file.stat().st_mtime:
                            shutil.copy2(log_file, target_file)
                            synced_files += 1
                    except Exception as e:
                        self.logger.warning(f"同步文件失败 {log_file}: {e}")
                
                # 同步JSON数据文件
                for json_file in data_path.glob('*.json'):
                    try:
                        target_file = sync_folder / json_file.name
                        if not target_file.exists() or json_file.stat().st_mtime > target_file.stat().st_mtime:
                            shutil.copy2(json_file, target_file)
                            synced_files += 1
                    except Exception as e:
                        self.logger.warning(f"同步JSON文件失败 {json_file}: {e}")
            
            self.logger.info(f"📊 数据同步完成: {synced_files}个文件")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 数据同步失败: {e}")
            return False
    
    def verify_integration(self) -> Dict[str, Any]:
        """验证集成状态"""
        try:
            self.logger.info("🔍 验证集成状态...")
            
            verification_result = {
                'timestamp': datetime.now().isoformat(),
                'trading_server_detected': False,
                'shared_folder_accessible': False,
                'models_deployed': 0,
                'compatibility_status': 'unknown',
                'issues': []
            }
            
            # 检查交易端服务器
            verification_result['trading_server_detected'] = self.detect_trading_server()
            
            # 检查共享文件夹
            verification_result['shared_folder_accessible'] = self.shared_folder.exists()
            
            # 检查已部署的模型
            deployed_models = 0
            for trading_path in self.trading_models_paths:
                if trading_path.exists():
                    deployed_models += len(list(trading_path.glob('*.pkl')))
            verification_result['models_deployed'] = deployed_models
            
            # 检查兼容性状态
            compatibility_status = self.compatibility_manager.get_model_status()
            verification_result['compatibility_status'] = 'ok' if compatibility_status else 'error'
            
            # 收集问题
            if not verification_result['trading_server_detected']:
                verification_result['issues'].append('交易端服务器未检测到')
            
            if not verification_result['shared_folder_accessible']:
                verification_result['issues'].append('共享文件夹不可访问')
            
            if verification_result['models_deployed'] == 0:
                verification_result['issues'].append('没有已部署的模型')
            
            # 保存验证报告
            report_file = self.shared_folder / 'logs' / f'integration_verification_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            report_file.parent.mkdir(exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(verification_result, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📊 验证报告已保存: {report_file}")
            return verification_result
            
        except Exception as e:
            self.logger.error(f"❌ 验证集成状态失败: {e}")
            return {'error': str(e)}
    
    def get_integration_status(self) -> Dict[str, Any]:
        """获取集成状态摘要"""
        try:
            status = {
                'training_server_ip': self.training_ip,
                'trading_server_ip': self.trading_ip,
                'shared_folder': str(self.shared_folder),
                'models_available': len(list(self.models_folder.glob('**/*.pkl'))),
                'last_sync': 'unknown',
                'compatibility_version': self.compatibility_manager.COMPATIBILITY_VERSION
            }
            
            return status
            
        except Exception as e:
            self.logger.error(f"❌ 获取集成状态失败: {e}")
            return {'error': str(e)}
