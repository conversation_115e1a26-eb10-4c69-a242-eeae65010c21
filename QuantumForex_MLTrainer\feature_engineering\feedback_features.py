#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
反馈特征工程
基于Pro端实时交易数据生成新的训练特征
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from pathlib import Path

class FeedbackFeatureEngine:
    """反馈特征工程引擎"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.feature_names = []

    def generate_feedback_features(self, market_data: pd.DataFrame,
                                 trade_records: List[Dict],
                                 parameter_history: List[Dict],
                                 llm_analyses: List[Dict]) -> pd.DataFrame:
        """生成基于反馈的特征"""
        try:
            # 检查是否有真实数据
            has_trade_data = len(trade_records) > 0
            has_param_data = len(parameter_history) > 0
            has_llm_data = len(llm_analyses) > 0

            if not (has_trade_data or has_param_data or has_llm_data):
                self.logger.warning("⚠️ 没有可用的反馈数据，跳过反馈特征生成")
                return market_data

            self.logger.info("🔧 开始生成反馈特征...")
            self.logger.info(f"  - 交易记录: {len(trade_records)}条")
            self.logger.info(f"  - 参数优化: {len(parameter_history)}条")
            self.logger.info(f"  - LLM分析: {len(llm_analyses)}条")

            df = market_data.copy()

            # 只有真实数据时才生成对应特征
            if has_trade_data:
                # 1. 交易成功率特征
                df = self._add_trade_success_features(df, trade_records)

                # 4. 市场条件匹配特征
                df = self._add_market_condition_features(df, trade_records)

                # 5. 策略有效性特征
                df = self._add_strategy_effectiveness_features(df, trade_records)

            if has_param_data:
                # 2. 参数优化特征
                df = self._add_parameter_optimization_features(df, parameter_history)

            if has_llm_data:
                # 3. LLM分析特征
                df = self._add_llm_analysis_features(df, llm_analyses)

            self.logger.info(f"✅ 反馈特征生成完成: {len(self.feature_names)}个特征")
            return df

        except Exception as e:
            self.logger.error(f"❌ 生成反馈特征失败: {e}")
            return market_data

    def _add_trade_success_features(self, df: pd.DataFrame, trade_records: List[Dict]) -> pd.DataFrame:
        """添加交易成功率特征"""
        try:
            if not trade_records:
                return df

            # 转换交易记录为DataFrame
            trades_df = pd.DataFrame(trade_records)
            trades_df['entry_time'] = pd.to_datetime(trades_df['entry_time'])
            trades_df.set_index('entry_time', inplace=True)

            # 按时间窗口计算成功率
            windows = [5, 15, 30, 60]  # 分钟

            for window in windows:
                # 计算滚动成功率
                success_rate = self._calculate_rolling_success_rate(trades_df, window)

                # 对齐到市场数据时间
                aligned_rate = self._align_to_market_data(df, success_rate)

                feature_name = f'trade_success_rate_{window}min'
                df[feature_name] = aligned_rate
                self.feature_names.append(feature_name)

                # 计算平均盈利
                avg_profit = self._calculate_rolling_avg_profit(trades_df, window)
                aligned_profit = self._align_to_market_data(df, avg_profit)

                feature_name = f'avg_profit_{window}min'
                df[feature_name] = aligned_profit
                self.feature_names.append(feature_name)

            return df

        except Exception as e:
            self.logger.error(f"添加交易成功率特征失败: {e}")
            return df

    def _add_parameter_optimization_features(self, df: pd.DataFrame, parameter_history: List[Dict]) -> pd.DataFrame:
        """添加参数优化特征"""
        try:
            if not parameter_history:
                return df

            # 参数稳定性指标
            param_stability = self._calculate_parameter_stability(parameter_history)
            df['parameter_stability'] = param_stability
            self.feature_names.append('parameter_stability')

            # 最近优化效果
            recent_improvement = self._calculate_recent_improvement(parameter_history)
            df['recent_optimization_score'] = recent_improvement
            self.feature_names.append('recent_optimization_score')

            # 参数变化频率
            change_frequency = self._calculate_parameter_change_frequency(parameter_history)
            df['parameter_change_frequency'] = change_frequency
            self.feature_names.append('parameter_change_frequency')

            return df

        except Exception as e:
            self.logger.error(f"添加参数优化特征失败: {e}")
            return df

    def _add_llm_analysis_features(self, df: pd.DataFrame, llm_analyses: List[Dict]) -> pd.DataFrame:
        """添加LLM分析特征"""
        try:
            if not llm_analyses:
                return df

            # LLM风险评分趋势
            risk_trend = self._calculate_llm_risk_trend(llm_analyses)
            df['llm_risk_trend'] = risk_trend
            self.feature_names.append('llm_risk_trend')

            # LLM建议一致性
            recommendation_consistency = self._calculate_recommendation_consistency(llm_analyses)
            df['llm_recommendation_consistency'] = recommendation_consistency
            self.feature_names.append('llm_recommendation_consistency')

            # LLM分析频率
            analysis_frequency = self._calculate_llm_analysis_frequency(llm_analyses)
            df['llm_analysis_frequency'] = analysis_frequency
            self.feature_names.append('llm_analysis_frequency')

            return df

        except Exception as e:
            self.logger.error(f"添加LLM分析特征失败: {e}")
            return df

    def _add_market_condition_features(self, df: pd.DataFrame, trade_records: List[Dict]) -> pd.DataFrame:
        """添加市场条件匹配特征"""
        try:
            if not trade_records:
                return df

            # 当前市场条件与历史成功交易的匹配度
            market_match_score = self._calculate_market_condition_match(df, trade_records)
            df['market_condition_match'] = market_match_score
            self.feature_names.append('market_condition_match')

            # 相似市场条件下的历史表现
            historical_performance = self._calculate_historical_performance_in_similar_conditions(df, trade_records)
            df['similar_condition_performance'] = historical_performance
            self.feature_names.append('similar_condition_performance')

            return df

        except Exception as e:
            self.logger.error(f"添加市场条件特征失败: {e}")
            return df

    def _add_strategy_effectiveness_features(self, df: pd.DataFrame, trade_records: List[Dict]) -> pd.DataFrame:
        """添加策略有效性特征"""
        try:
            if not trade_records:
                return df

            # 不同策略的近期表现
            strategy_performance = self._calculate_strategy_performance(trade_records)

            for strategy, performance in strategy_performance.items():
                feature_name = f'strategy_{strategy}_performance'
                df[feature_name] = performance
                self.feature_names.append(feature_name)

            # 策略多样性指标
            strategy_diversity = self._calculate_strategy_diversity(trade_records)
            df['strategy_diversity'] = strategy_diversity
            self.feature_names.append('strategy_diversity')

            return df

        except Exception as e:
            self.logger.error(f"添加策略有效性特征失败: {e}")
            return df

    def _calculate_rolling_success_rate(self, trades_df: pd.DataFrame, window_minutes: int) -> pd.Series:
        """计算滚动成功率"""
        try:
            if 'profit_loss' not in trades_df.columns:
                return pd.Series(dtype=float)

            # 创建成功标记
            trades_df['is_success'] = trades_df['profit_loss'] > 0

            # 按时间窗口重采样并计算成功率
            window_str = f'{window_minutes}min'
            success_rate = trades_df['is_success'].resample(window_str).mean()

            return success_rate.fillna(0.5)  # 默认50%成功率

        except Exception as e:
            self.logger.error(f"计算滚动成功率失败: {e}")
            return pd.Series(dtype=float)

    def _calculate_rolling_avg_profit(self, trades_df: pd.DataFrame, window_minutes: int) -> pd.Series:
        """计算滚动平均盈利"""
        try:
            if 'profit_loss' not in trades_df.columns:
                return pd.Series(dtype=float)

            window_str = f'{window_minutes}min'
            avg_profit = trades_df['profit_loss'].resample(window_str).mean()

            return avg_profit.fillna(0)

        except Exception as e:
            self.logger.error(f"计算滚动平均盈利失败: {e}")
            return pd.Series(dtype=float)

    def _align_to_market_data(self, market_df: pd.DataFrame, feature_series: pd.Series) -> pd.Series:
        """将特征序列对齐到市场数据时间"""
        try:
            if feature_series.empty:
                return pd.Series(0.5, index=market_df.index)

            # 使用前向填充对齐
            aligned = feature_series.reindex(market_df.index, method='ffill')

            # 填充缺失值
            aligned = aligned.bfill().fillna(0.5)

            return aligned

        except Exception as e:
            self.logger.error(f"对齐特征序列失败: {e}")
            return pd.Series(0.5, index=market_df.index)

    def _calculate_parameter_stability(self, parameter_history: List[Dict]) -> float:
        """计算参数稳定性"""
        try:
            if len(parameter_history) < 2:
                return 1.0

            # 计算参数变化的标准差
            changes = []
            for i in range(1, len(parameter_history)):
                prev_params = parameter_history[i-1]
                curr_params = parameter_history[i]

                if 'improvement_score' in curr_params:
                    changes.append(abs(curr_params['improvement_score']))

            if not changes:
                return 1.0

            # 稳定性 = 1 - 标准化的变化幅度
            stability = 1.0 - min(1.0, np.std(changes) / np.mean(changes) if np.mean(changes) > 0 else 0)
            return max(0.0, stability)

        except Exception as e:
            self.logger.error(f"计算参数稳定性失败: {e}")
            return 0.5

    def _calculate_recent_improvement(self, parameter_history: List[Dict]) -> float:
        """计算最近优化效果"""
        try:
            if not parameter_history:
                return 0.0

            # 取最近的优化记录
            recent_optimizations = parameter_history[-5:]  # 最近5次

            improvements = [opt.get('improvement_score', 0) for opt in recent_optimizations]

            return np.mean(improvements) if improvements else 0.0

        except Exception as e:
            self.logger.error(f"计算最近优化效果失败: {e}")
            return 0.0

    def _calculate_parameter_change_frequency(self, parameter_history: List[Dict]) -> float:
        """计算参数变化频率"""
        try:
            if len(parameter_history) < 2:
                return 0.0

            # 计算时间跨度
            times = [datetime.fromisoformat(opt['optimization_time']) for opt in parameter_history if 'optimization_time' in opt]

            if len(times) < 2:
                return 0.0

            time_span = (max(times) - min(times)).total_seconds() / 3600  # 小时

            if time_span == 0:
                return 0.0

            # 变化频率 = 变化次数 / 时间跨度
            frequency = len(parameter_history) / time_span

            return min(1.0, frequency / 24)  # 标准化到每天最多1次

        except Exception as e:
            self.logger.error(f"计算参数变化频率失败: {e}")
            return 0.0

    def _calculate_market_condition_match(self, df: pd.DataFrame, trade_records: List[Dict]) -> pd.Series:
        """计算市场条件匹配度"""
        try:
            if not trade_records:
                return pd.Series(0.5, index=df.index)

            # 简化的市场条件匹配计算
            # 基于价格波动率匹配
            df_volatility = df['close'].rolling(20).std()

            # 计算成功交易的平均波动率
            successful_trades = [t for t in trade_records if t.get('profit_loss', 0) > 0]
            if successful_trades:
                avg_success_volatility = 0.001  # 默认值
            else:
                avg_success_volatility = 0.001

            # 匹配度 = 1 - abs(当前波动率 - 成功交易波动率) / 成功交易波动率
            match_score = 1 - abs(df_volatility - avg_success_volatility) / avg_success_volatility
            match_score = match_score.clip(0, 1).fillna(0.5)

            return match_score

        except Exception as e:
            self.logger.error(f"计算市场条件匹配度失败: {e}")
            return pd.Series(0.5, index=df.index)

    def _calculate_historical_performance_in_similar_conditions(self, df: pd.DataFrame, trade_records: List[Dict]) -> pd.Series:
        """计算相似条件下的历史表现"""
        try:
            if not trade_records:
                return pd.Series(0.0, index=df.index)

            # 简化计算：基于整体胜率
            total_trades = len(trade_records)
            winning_trades = len([t for t in trade_records if t.get('profit_loss', 0) > 0])

            if total_trades > 0:
                win_rate = winning_trades / total_trades
            else:
                win_rate = 0.5

            return pd.Series(win_rate, index=df.index)

        except Exception as e:
            self.logger.error(f"计算历史表现失败: {e}")
            return pd.Series(0.0, index=df.index)

    def _calculate_strategy_performance(self, trade_records: List[Dict]) -> Dict[str, float]:
        """计算策略表现"""
        try:
            if not trade_records:
                return {}

            strategy_performance = {}

            # 按策略分组
            strategies = {}
            for trade in trade_records:
                strategy = trade.get('strategy_used', 'unknown')
                if strategy not in strategies:
                    strategies[strategy] = []
                strategies[strategy].append(trade)

            # 计算每个策略的表现
            for strategy, trades in strategies.items():
                if len(trades) > 0:
                    winning_trades = len([t for t in trades if t.get('profit_loss', 0) > 0])
                    win_rate = winning_trades / len(trades)
                    strategy_performance[strategy] = win_rate

            return strategy_performance

        except Exception as e:
            self.logger.error(f"计算策略表现失败: {e}")
            return {}

    def _calculate_strategy_diversity(self, trade_records: List[Dict]) -> float:
        """计算策略多样性"""
        try:
            if not trade_records:
                return 0.0

            # 计算使用的不同策略数量
            strategies = set(trade.get('strategy_used', 'unknown') for trade in trade_records)

            # 多样性 = 策略数量 / 最大可能策略数量
            max_strategies = 5  # 假设最多5种策略
            diversity = len(strategies) / max_strategies

            return min(1.0, diversity)

        except Exception as e:
            self.logger.error(f"计算策略多样性失败: {e}")
            return 0.0

    def _calculate_llm_risk_trend(self, llm_analyses: List[Dict]) -> float:
        """计算LLM风险趋势"""
        try:
            if not llm_analyses:
                return 0.5

            # 计算最近分析的平均风险评分
            recent_scores = [analysis.get('risk_score', 0.5) for analysis in llm_analyses[-10:]]

            if recent_scores:
                return sum(recent_scores) / len(recent_scores)
            else:
                return 0.5

        except Exception as e:
            self.logger.error(f"计算LLM风险趋势失败: {e}")
            return 0.5

    def _calculate_recommendation_consistency(self, llm_analyses: List[Dict]) -> float:
        """计算LLM建议一致性"""
        try:
            if len(llm_analyses) < 2:
                return 1.0

            # 简化计算：基于风险评分的一致性
            scores = [analysis.get('risk_score', 0.5) for analysis in llm_analyses[-5:]]

            if len(scores) > 1:
                # 计算标准差，一致性 = 1 - 标准差
                import numpy as np
                consistency = 1.0 - min(1.0, np.std(scores))
                return max(0.0, consistency)
            else:
                return 1.0

        except Exception as e:
            self.logger.error(f"计算建议一致性失败: {e}")
            return 1.0

    def _calculate_llm_analysis_frequency(self, llm_analyses: List[Dict]) -> float:
        """计算LLM分析频率"""
        try:
            if not llm_analyses:
                return 0.0

            # 计算每天的分析次数
            from datetime import datetime, timedelta

            now = datetime.now()
            one_day_ago = now - timedelta(days=1)

            recent_analyses = []
            for analysis in llm_analyses:
                try:
                    analysis_time = datetime.fromisoformat(analysis.get('analysis_time', ''))
                    if analysis_time >= one_day_ago:
                        recent_analyses.append(analysis)
                except:
                    continue

            # 频率 = 最近24小时分析次数 / 24
            frequency = len(recent_analyses) / 24.0
            return min(1.0, frequency)

        except Exception as e:
            self.logger.error(f"计算分析频率失败: {e}")
            return 0.0

    def get_feature_names(self) -> List[str]:
        """获取特征名称列表"""
        return self.feature_names.copy()

# 创建全局实例
feedback_engine = FeedbackFeatureEngine()
