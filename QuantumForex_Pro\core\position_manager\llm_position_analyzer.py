"""
LLM持仓分析模块
使用LLM进行复杂的持仓分析和策略建议
"""

import json
import requests
from typing import List, Dict, Optional
from datetime import datetime

class LLMPositionAnalyzer:
    def __init__(self):
        # 使用项目配置中的LLM设置
        import os
        from dotenv import load_dotenv
        load_dotenv()

        self.api_url = os.getenv('LLM_API_URL', 'https://api.siliconflow.cn/v1/chat/completions')
        self.api_key = os.getenv('DEEPSEEK_API_KEY', 'sk-dplvjslhezcjinavtmaporlyumqqwnowcbjwyvmetxychflk')
        self.model = os.getenv('LLM_MODEL', 'Pro/deepseek-ai/DeepSeek-R1')

    def analyze_portfolio(self, positions: List[Dict], market_data: Dict) -> Dict:
        """使用LLM分析整个持仓组合"""
        try:
            print("🤖 启动LLM持仓组合分析...")

            # 准备分析数据
            analysis_data = self._prepare_analysis_data(positions, market_data)

            # 构建LLM提示
            prompt = self._build_portfolio_prompt(analysis_data)

            # 调用LLM
            response = self._call_llm(prompt)

            if response:
                print("✅ LLM持仓分析完成")
                return self._parse_llm_response(response)
            else:
                print("❌ LLM分析失败")
                return self._get_fallback_analysis()

        except Exception as e:
            print(f"❌ LLM持仓分析异常: {e}")
            return self._get_fallback_analysis()

    def _prepare_analysis_data(self, positions: List[Dict], market_data: Dict) -> Dict:
        """准备LLM分析数据"""
        try:
            # 计算组合统计
            total_profit = sum(pos.get('profit', 0) for pos in positions)
            total_volume = sum(pos.get('volume', 0) for pos in positions)

            # 按货币对分组
            by_symbol = {}
            for pos in positions:
                symbol = pos['symbol']
                if symbol not in by_symbol:
                    by_symbol[symbol] = []
                by_symbol[symbol].append(pos)

            # 计算每个货币对的统计
            symbol_stats = {}
            for symbol, symbol_positions in by_symbol.items():
                symbol_stats[symbol] = {
                    'count': len(symbol_positions),
                    'total_profit': sum(p.get('profit', 0) for p in symbol_positions),
                    'total_volume': sum(p.get('volume', 0) for p in symbol_positions),
                    'avg_profit': sum(p.get('profit', 0) for p in symbol_positions) / len(symbol_positions),
                    'directions': [p.get('action', 'UNKNOWN') for p in symbol_positions]
                }

            return {
                'positions': positions,
                'market_data': market_data,
                'portfolio_stats': {
                    'total_positions': len(positions),
                    'total_profit': total_profit,
                    'total_volume': total_volume,
                    'avg_profit_per_position': total_profit / len(positions) if positions else 0
                },
                'symbol_stats': symbol_stats,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            print(f"❌ 准备分析数据失败: {e}")
            return {}

    def _build_portfolio_prompt(self, data: Dict) -> str:
        """构建LLM分析提示"""
        try:
            portfolio_stats = data.get('portfolio_stats', {})
            symbol_stats = data.get('symbol_stats', {})

            prompt = f"""
你是一个专业的外汇交易持仓管理专家。请分析以下持仓组合并提供专业建议。

## 持仓组合概况
- 总持仓数量: {portfolio_stats.get('total_positions', 0)}
- 总盈亏: ${portfolio_stats.get('total_profit', 0):.2f}
- 总交易量: {portfolio_stats.get('total_volume', 0)} 手
- 平均每仓盈亏: ${portfolio_stats.get('avg_profit_per_position', 0):.2f}

## 各货币对详情
"""

            for symbol, stats in symbol_stats.items():
                prompt += f"""
### {symbol}
- 持仓数量: {stats['count']}
- 总盈亏: ${stats['total_profit']:.2f}
- 平均盈亏: ${stats['avg_profit']:.2f}
- 交易方向: {', '.join(stats['directions'])}
"""

            prompt += """

## 分析要求
请从以下角度进行分析：
1. **风险评估**: 当前持仓组合的整体风险水平
2. **盈亏分析**: 盈利和亏损持仓的分布情况
3. **货币对集中度**: 是否存在过度集中风险
4. **方向性风险**: 同一方向持仓是否过多
5. **管理建议**: 具体的持仓管理建议

## 重要提示
- 当前持仓数量过多，需要主动清理
- 优先平仓亏损订单和长期持仓
- 建议将持仓数量控制在15个以内
- 重点关注风险控制而非盈利最大化

## 输出格式
请以JSON格式输出分析结果，必须包含具体的平仓建议：
{
    "overall_risk": "LOW/MEDIUM/HIGH/VERY_HIGH",
    "risk_score": 0.0-1.0,
    "key_issues": ["持仓数量过多", "风险集中"],
    "recommendations": [
        {
            "action": "close",
            "symbol": "EURUSD",
            "reason": "亏损订单，建议平仓",
            "priority": 4
        },
        {
            "action": "close",
            "symbol": "ALL",
            "reason": "持仓数量过多，需要清理",
            "priority": 3
        }
    ],
    "portfolio_advice": "减少持仓数量，控制风险",
    "risk_management": "优先平仓亏损订单"
}

请确保分析专业、客观，重点关注风险控制和盈利保护。
"""

            return prompt

        except Exception as e:
            print(f"❌ 构建提示失败: {e}")
            return ""

    def _call_llm(self, prompt: str) -> Optional[str]:
        """调用LLM API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 2000
            }

            print("📡 发送LLM分析请求...")
            response = requests.post(self.api_url, headers=headers, json=data, timeout=30)

            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print("✅ LLM响应成功")
                return content
            else:
                print(f"❌ LLM API错误: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ LLM调用失败: {e}")
            return None

    def _parse_llm_response(self, response: str) -> Dict:
        """解析LLM响应"""
        try:
            # 尝试提取JSON部分
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx]
                result = json.loads(json_str)

                # 验证必要字段
                required_fields = ['overall_risk', 'risk_score', 'recommendations']
                if all(field in result for field in required_fields):
                    return result

            print("⚠️ LLM响应格式不正确，使用备用分析")
            return self._get_fallback_analysis()

        except Exception as e:
            print(f"❌ 解析LLM响应失败: {e}")
            return self._get_fallback_analysis()

    def _get_fallback_analysis(self) -> Dict:
        """备用分析结果"""
        return {
            "overall_risk": "MEDIUM",
            "risk_score": 0.5,
            "key_issues": ["LLM分析不可用"],
            "recommendations": [
                {
                    "action": "hold",
                    "symbol": "ALL",
                    "reason": "等待LLM分析恢复",
                    "priority": 1
                }
            ],
            "portfolio_advice": "建议使用算法规则进行基础风险管理",
            "risk_management": "密切监控持仓，及时止损"
        }

    def print_llm_analysis(self, analysis: Dict):
        """打印LLM分析结果"""
        print("\n" + "="*80)
        print("🤖 LLM持仓组合分析报告")
        print("="*80)

        print(f"🛡️ 整体风险等级: {analysis.get('overall_risk', 'UNKNOWN')}")
        print(f"📊 风险评分: {analysis.get('risk_score', 0):.2f}")

        key_issues = analysis.get('key_issues', [])
        if key_issues:
            print(f"⚠️ 关键问题:")
            for issue in key_issues:
                print(f"   • {issue}")

        recommendations = analysis.get('recommendations', [])
        if recommendations:
            print(f"💡 LLM建议:")
            for rec in recommendations:
                print(f"   🎯 {rec.get('symbol', 'UNKNOWN')}: {rec.get('action', 'unknown')}")
                print(f"      原因: {rec.get('reason', '未知')}")
                print(f"      优先级: {rec.get('priority', 1)}/5")

        portfolio_advice = analysis.get('portfolio_advice', '')
        if portfolio_advice:
            print(f"📈 组合建议: {portfolio_advice}")

        risk_management = analysis.get('risk_management', '')
        if risk_management:
            print(f"🛡️ 风险管理: {risk_management}")

        print("="*80 + "\n")
