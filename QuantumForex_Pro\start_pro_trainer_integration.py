#!/usr/bin/env python3
"""
启动QuantumForex Pro与MLTrainer的完整集成
实现无人值守的自动ML流水线
"""

import sys
import os
import time
import threading
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.config import config
from utils.ml_model_sync import MLModelSyncManager
from utils.cloud_model_receiver import CloudModelReceiver
from core.ml_engine.lightweight_ml_engine import LightweightMLEngine

class ProTrainerIntegrationManager:
    """Pro-Trainer集成管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 配置信息
        self.ml_config = config.ML_TRAINER_CONFIG
        self.sync_interval = self.ml_config.get('model_sync_interval', 3600)  # 1小时
        
        # 组件初始化
        self.model_sync_manager = MLModelSyncManager()
        self.cloud_receiver = CloudModelReceiver()
        self.ml_engine = None
        
        # 运行状态
        self.running = False
        self.sync_thread = None
        self.last_sync_time = None
        self.sync_count = 0
        
        # 性能监控
        self.performance_history = []
        self.performance_threshold = 0.75  # 性能下降阈值
        
    def start_integration(self):
        """启动Pro-Trainer集成"""
        try:
            print("🚀 启动QuantumForex Pro-Trainer集成系统")
            print("="*60)
            
            # 1. 检查配置
            if not self._check_configuration():
                print("❌ 配置检查失败，无法启动集成")
                return False
            
            # 2. 初始化ML引擎
            print("🤖 初始化ML引擎...")
            self.ml_engine = LightweightMLEngine()
            print(f"✅ ML引擎初始化完成，已加载{len(self.ml_engine.models)}个模型")
            
            # 3. 检查Trainer连接
            print("🔍 检查Trainer连接...")
            if self.model_sync_manager.check_trainer_connection():
                print("✅ Trainer连接正常")
            else:
                print("⚠️ Trainer连接失败，将使用本地模式")
            
            # 4. 执行初始同步
            print("🔄 执行初始模型同步...")
            sync_result = self.model_sync_manager.sync_models_from_trainer()
            if sync_result['success']:
                print(f"✅ 初始同步完成：{len(sync_result['synced_models'])}个模型")
            else:
                print("⚠️ 初始同步失败，使用现有模型")
            
            # 5. 启动自动同步线程
            if self.ml_config.get('auto_update_models', True):
                print("⚡ 启动自动同步线程...")
                self.running = True
                self.sync_thread = threading.Thread(target=self._sync_loop, daemon=True)
                self.sync_thread.start()
                print("✅ 自动同步线程已启动")
            
            # 6. 显示状态信息
            self._display_status()
            
            print("\n🎉 Pro-Trainer集成系统启动成功！")
            print("📊 功能特性：")
            print("   ✅ 自动模型同步 - 定时检查新模型")
            print("   ✅ 性能监控 - 检测模型性能下降")
            print("   ✅ 自动重训练触发 - 性能下降时通知Trainer")
            print("   ✅ 无人值守运行 - 完全自动化ML流水线")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 启动集成系统失败: {e}")
            print(f"❌ 启动失败: {e}")
            return False
    
    def _check_configuration(self) -> bool:
        """检查配置"""
        try:
            print("📋 检查配置...")
            
            # 检查ML_TRAINER_CONFIG
            if not hasattr(config, 'ML_TRAINER_CONFIG'):
                print("❌ 缺少ML_TRAINER_CONFIG配置")
                return False
            
            # 检查关键配置项
            required_keys = ['trainer_ip', 'model_sync_enabled', 'auto_update_models']
            for key in required_keys:
                if key not in self.ml_config:
                    print(f"❌ 缺少配置项: {key}")
                    return False
            
            # 检查模型同步是否启用
            if not self.ml_config.get('model_sync_enabled', False):
                print("❌ 模型同步功能未启用")
                return False
            
            print("✅ 配置检查通过")
            return True
            
        except Exception as e:
            print(f"❌ 配置检查失败: {e}")
            return False
    
    def _sync_loop(self):
        """自动同步循环"""
        while self.running:
            try:
                # 等待同步间隔
                time.sleep(self.sync_interval)
                
                if not self.running:
                    break
                
                print(f"\n🔄 [{datetime.now().strftime('%H:%M:%S')}] 执行定时模型同步...")
                
                # 执行同步
                sync_result = self.model_sync_manager.sync_models_from_trainer()
                
                if sync_result['success']:
                    if sync_result['synced_models']:
                        print(f"✅ 同步完成：{len(sync_result['synced_models'])}个新模型")
                        
                        # 重新加载ML引擎
                        self._reload_ml_engine()
                        
                        self.sync_count += 1
                    else:
                        print("📊 没有新模型需要同步")
                else:
                    print(f"❌ 同步失败：{sync_result.get('errors', ['未知错误'])}")
                
                self.last_sync_time = datetime.now()
                
                # 检查性能并触发重训练
                self._check_performance_and_retrain()
                
            except Exception as e:
                self.logger.error(f"同步循环异常: {e}")
                print(f"⚠️ 同步异常: {e}")
                time.sleep(60)  # 出错时等待1分钟再重试
    
    def _reload_ml_engine(self):
        """重新加载ML引擎"""
        try:
            print("🔄 重新加载ML引擎...")
            self.ml_engine = LightweightMLEngine()
            print(f"✅ ML引擎重新加载完成，当前模型数：{len(self.ml_engine.models)}")
        except Exception as e:
            print(f"❌ ML引擎重新加载失败: {e}")
    
    def _check_performance_and_retrain(self):
        """检查性能并触发重训练"""
        try:
            if not self.ml_engine:
                return
            
            # 获取当前模型性能
            current_performance = {}
            for model_type, performance in self.ml_engine.model_performance.items():
                current_performance[model_type.value] = performance
            
            # 记录性能历史
            self.performance_history.append({
                'timestamp': datetime.now(),
                'performance': current_performance
            })
            
            # 保留最近24小时的记录
            cutoff_time = datetime.now() - timedelta(hours=24)
            self.performance_history = [
                record for record in self.performance_history 
                if record['timestamp'] > cutoff_time
            ]
            
            # 检查是否需要重训练
            if len(self.performance_history) >= 2:
                latest_perf = self.performance_history[-1]['performance']
                previous_perf = self.performance_history[-2]['performance']
                
                for model_type, current_perf in latest_perf.items():
                    previous = previous_perf.get(model_type, current_perf)
                    
                    # 如果性能下降超过阈值
                    if current_perf < previous * self.performance_threshold:
                        print(f"⚠️ 检测到{model_type}性能下降：{previous:.3f} → {current_perf:.3f}")
                        self._trigger_retrain(model_type)
            
        except Exception as e:
            self.logger.error(f"性能检查失败: {e}")
    
    def _trigger_retrain(self, model_type: str):
        """触发重训练"""
        try:
            print(f"🔥 触发{model_type}重训练...")
            
            # 这里可以通过API调用Trainer端触发重训练
            # 目前先记录日志
            retrain_request = {
                'timestamp': datetime.now().isoformat(),
                'model_type': model_type,
                'reason': 'performance_degradation',
                'trigger_source': 'pro_system'
            }
            
            print(f"📝 重训练请求已记录：{model_type}")
            
            # TODO: 实现实际的Trainer API调用
            # self._call_trainer_api('/retrain', retrain_request)
            
        except Exception as e:
            self.logger.error(f"触发重训练失败: {e}")
    
    def _display_status(self):
        """显示状态信息"""
        try:
            print("\n📊 系统状态:")
            print("-"*40)
            
            # 配置状态
            print(f"🔧 配置状态:")
            print(f"   Trainer IP: {self.ml_config.get('trainer_ip', 'N/A')}")
            print(f"   同步间隔: {self.sync_interval}秒")
            print(f"   自动更新: {'✅' if self.ml_config.get('auto_update_models') else '❌'}")
            
            # 连接状态
            connection_ok = self.model_sync_manager.check_trainer_connection()
            print(f"   Trainer连接: {'✅' if connection_ok else '❌'}")
            
            # 模型状态
            if self.ml_engine:
                print(f"\n🤖 模型状态:")
                for model_type, performance in self.ml_engine.model_performance.items():
                    print(f"   {model_type.value}: {performance:.3f}")
            
            # 同步状态
            print(f"\n🔄 同步状态:")
            print(f"   同步次数: {self.sync_count}")
            print(f"   上次同步: {self.last_sync_time or '未同步'}")
            print(f"   运行状态: {'🟢 运行中' if self.running else '🔴 已停止'}")
            
        except Exception as e:
            print(f"❌ 显示状态失败: {e}")
    
    def stop_integration(self):
        """停止集成"""
        print("\n🛑 停止Pro-Trainer集成...")
        self.running = False
        
        if self.sync_thread and self.sync_thread.is_alive():
            self.sync_thread.join(timeout=5)
        
        print("✅ 集成系统已停止")

def main():
    """主函数"""
    integration_manager = ProTrainerIntegrationManager()
    
    try:
        # 启动集成
        if integration_manager.start_integration():
            print("\n🔄 集成系统正在运行...")
            print("按 Ctrl+C 停止系统")
            
            # 保持运行
            while True:
                time.sleep(10)
                
        else:
            print("❌ 集成系统启动失败")
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 收到停止信号...")
        integration_manager.stop_integration()
        
    except Exception as e:
        print(f"\n❌ 系统异常: {e}")
        integration_manager.stop_integration()

if __name__ == "__main__":
    main()
