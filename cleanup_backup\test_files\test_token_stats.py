"""
测试token统计功能
"""
import os
import sys
import json
import traceback
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
from app.utils.token_statistics import record_token_usage, load_token_stats, generate_token_report

def test_token_stats():
    """测试token统计功能"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 开始测试token统计功能...")
    
    try:
        # 测试记录token使用情况
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试记录token使用情况...")
        record = record_token_usage('Pro/deepseek-ai/DeepSeek-R1', 100, 50, 'test_analysis')
        
        if record:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 记录token使用情况成功")
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 记录内容: {json.dumps(record, indent=2)}")
        else:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 记录token使用情况失败")
        
        # 测试加载token统计数据
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试加载token统计数据...")
        stats = load_token_stats()
        
        if stats:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 加载token统计数据成功")
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 总token数: {stats['total_tokens']}")
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 总成本: {stats['total_cost']:.4f} 元")
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 记录数: {len(stats['records'])}")
        else:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 加载token统计数据失败")
        
        # 测试生成token统计报告
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试生成token统计报告...")
        report = generate_token_report()
        
        if report:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 生成token统计报告成功")
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 报告预览:\n{report[:500]}...")
        else:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 生成token统计报告失败")
        
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试token统计功能完成")
        return True
    except Exception as e:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试token统计功能失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_token_stats()
