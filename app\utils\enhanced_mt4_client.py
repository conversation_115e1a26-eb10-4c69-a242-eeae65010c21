"""
增强版MT4客户端
使用MT4连接管理器提供更可靠的连接
"""
import time
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple, Union, Callable

# 导入MT4客户端和连接管理器
from app.utils.mt4_client import mt4_client
from app.utils.mt4_connection_manager import mt4_manager
from app.utils.error_logger import log_error, ErrorType, OperationType

class EnhancedMT4Client:
    """增强版MT4客户端，提供更可靠的连接"""
    
    def __init__(self, connection_manager=mt4_manager):
        """
        初始化增强版MT4客户端
        
        Args:
            connection_manager: MT4连接管理器实例
        """
        self.connection_manager = connection_manager
        self.last_operation_time = None
        self.operation_count = 0
        self.successful_operations = 0
        self.failed_operations = 0
    
    def log(self, message: str) -> None:
        """记录日志"""
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] [增强版MT4客户端] {message}')
    
    def _execute_with_retry(self, operation: Callable, *args, **kwargs) -> Dict[str, Any]:
        """
        执行MT4操作，支持重试
        
        Args:
            operation: 要执行的操作函数
            *args: 操作函数的位置参数
            **kwargs: 操作函数的关键字参数
        
        Returns:
            Dict[str, Any]: 操作结果
        """
        self.operation_count += 1
        self.last_operation_time = datetime.now()
        
        # 记录操作信息
        operation_name = operation.__name__
        self.log(f"执行操作: {operation_name}, 参数: {args}, {kwargs}")
        
        # 使用连接管理器执行操作
        result, exception = self.connection_manager.execute_operation(operation, *args, **kwargs)
        
        if exception:
            # 操作失败
            self.failed_operations += 1
            self.log(f"操作失败: {operation_name}, 错误: {exception}")
            
            # 记录错误
            log_error(
                error_type=ErrorType.MT4_ERROR,
                message=f"MT4操作失败: {operation_name}, 错误: {exception}",
                details={
                    'operation': operation_name,
                    'args': str(args),
                    'kwargs': str(kwargs),
                    'exception': str(exception)
                },
                operation=OperationType.OTHER
            )
            
            # 返回错误结果
            return {
                'status': 'error',
                'message': f"操作失败: {exception}",
                'error': str(exception)
            }
        else:
            # 操作成功
            self.successful_operations += 1
            self.log(f"操作成功: {operation_name}")
            
            # 返回结果
            return result
    
    def connect(self) -> bool:
        """
        连接到MT4服务器
        
        Returns:
            bool: 连接是否成功
        """
        return self.connection_manager.connect()
    
    def disconnect(self) -> bool:
        """
        断开与MT4服务器的连接
        
        Returns:
            bool: 断开连接是否成功
        """
        return self.connection_manager.disconnect()
    
    def reconnect(self) -> bool:
        """
        重新连接到MT4服务器
        
        Returns:
            bool: 重连是否成功
        """
        return self.connection_manager.reconnect()
    
    def check_connection(self) -> bool:
        """
        检查与MT4服务器的连接状态
        
        Returns:
            bool: 连接是否正常
        """
        return self.connection_manager.check_connection()
    
    def get_market_info(self, symbol: str) -> Dict[str, Any]:
        """
        获取市场信息
        
        Args:
            symbol: 交易品种
        
        Returns:
            Dict[str, Any]: 市场信息
        """
        return self._execute_with_retry(mt4_client.get_market_info, symbol)
    
    def get_active_orders(self) -> Dict[str, Any]:
        """
        获取活跃订单
        
        Returns:
            Dict[str, Any]: 活跃订单
        """
        return self._execute_with_retry(mt4_client.get_active_orders)
    
    def get_pending_orders(self) -> Dict[str, Any]:
        """
        获取挂单
        
        Returns:
            Dict[str, Any]: 挂单
        """
        return self._execute_with_retry(mt4_client.get_pending_orders)
    
    def get_account_info(self) -> Dict[str, Any]:
        """
        获取账户信息
        
        Returns:
            Dict[str, Any]: 账户信息
        """
        return self._execute_with_retry(mt4_client.get_account_info)
    
    def buy(self, symbol: str, lots: float, stop_loss: float = 0, take_profit: float = 0, comment: str = "") -> Dict[str, Any]:
        """
        市价买入
        
        Args:
            symbol: 交易品种
            lots: 手数
            stop_loss: 止损价格
            take_profit: 止盈价格
            comment: 注释
        
        Returns:
            Dict[str, Any]: 买入结果
        """
        return self._execute_with_retry(mt4_client.buy, symbol, lots, stop_loss, take_profit, comment)
    
    def sell(self, symbol: str, lots: float, stop_loss: float = 0, take_profit: float = 0, comment: str = "") -> Dict[str, Any]:
        """
        市价卖出
        
        Args:
            symbol: 交易品种
            lots: 手数
            stop_loss: 止损价格
            take_profit: 止盈价格
            comment: 注释
        
        Returns:
            Dict[str, Any]: 卖出结果
        """
        return self._execute_with_retry(mt4_client.sell, symbol, lots, stop_loss, take_profit, comment)
    
    def buy_limit(self, symbol: str, lots: float, price: float, stop_loss: float = 0, take_profit: float = 0, comment: str = "") -> Dict[str, Any]:
        """
        限价买入
        
        Args:
            symbol: 交易品种
            lots: 手数
            price: 入场价格
            stop_loss: 止损价格
            take_profit: 止盈价格
            comment: 注释
        
        Returns:
            Dict[str, Any]: 限价买入结果
        """
        return self._execute_with_retry(mt4_client.buy_limit, symbol, lots, price, stop_loss, take_profit, comment)
    
    def sell_limit(self, symbol: str, lots: float, price: float, stop_loss: float = 0, take_profit: float = 0, comment: str = "") -> Dict[str, Any]:
        """
        限价卖出
        
        Args:
            symbol: 交易品种
            lots: 手数
            price: 入场价格
            stop_loss: 止损价格
            take_profit: 止盈价格
            comment: 注释
        
        Returns:
            Dict[str, Any]: 限价卖出结果
        """
        return self._execute_with_retry(mt4_client.sell_limit, symbol, lots, price, stop_loss, take_profit, comment)
    
    def buy_stop(self, symbol: str, lots: float, price: float, stop_loss: float = 0, take_profit: float = 0, comment: str = "") -> Dict[str, Any]:
        """
        突破买入
        
        Args:
            symbol: 交易品种
            lots: 手数
            price: 入场价格
            stop_loss: 止损价格
            take_profit: 止盈价格
            comment: 注释
        
        Returns:
            Dict[str, Any]: 突破买入结果
        """
        return self._execute_with_retry(mt4_client.buy_stop, symbol, lots, price, stop_loss, take_profit, comment)
    
    def sell_stop(self, symbol: str, lots: float, price: float, stop_loss: float = 0, take_profit: float = 0, comment: str = "") -> Dict[str, Any]:
        """
        突破卖出
        
        Args:
            symbol: 交易品种
            lots: 手数
            price: 入场价格
            stop_loss: 止损价格
            take_profit: 止盈价格
            comment: 注释
        
        Returns:
            Dict[str, Any]: 突破卖出结果
        """
        return self._execute_with_retry(mt4_client.sell_stop, symbol, lots, price, stop_loss, take_profit, comment)
    
    def close_order(self, order_id: Union[int, str]) -> Dict[str, Any]:
        """
        关闭订单
        
        Args:
            order_id: 订单ID
        
        Returns:
            Dict[str, Any]: 关闭结果
        """
        return self._execute_with_retry(mt4_client.close_order, order_id)
    
    def delete_order(self, order_id: Union[int, str]) -> Dict[str, Any]:
        """
        删除挂单
        
        Args:
            order_id: 订单ID
        
        Returns:
            Dict[str, Any]: 删除结果
        """
        return self._execute_with_retry(mt4_client.delete_order, order_id)
    
    def modify_position(self, order_id: Union[int, str], stop_loss: float, take_profit: float) -> Dict[str, Any]:
        """
        修改持仓
        
        Args:
            order_id: 订单ID
            stop_loss: 止损价格
            take_profit: 止盈价格
        
        Returns:
            Dict[str, Any]: 修改结果
        """
        return self._execute_with_retry(mt4_client.modify_position, order_id, stop_loss, take_profit)
    
    def modify_order(self, order_id: Union[int, str], stop_loss: float, take_profit: float) -> Dict[str, Any]:
        """
        修改挂单
        
        Args:
            order_id: 订单ID
            stop_loss: 止损价格
            take_profit: 止盈价格
        
        Returns:
            Dict[str, Any]: 修改结果
        """
        return self._execute_with_retry(mt4_client.modify_order, order_id, stop_loss, take_profit)

# 创建全局增强版MT4客户端实例
enhanced_mt4_client = EnhancedMT4Client(mt4_manager)
