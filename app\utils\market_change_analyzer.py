"""
市场变化分析器
使用DeepSeek模型判断是否需要进行完整分析
"""
import time
import json
import traceback
from datetime import datetime

from app.utils import mt4_client
from app.utils import llm_client
from app.services.forex_trading_service import get_aggregated_klines, get_relevant_news, get_relevant_calendar

# 全局变量，用于存储最近的市场数据
last_price = None
last_analysis_time = None

# 最小分析间隔（分钟）
MIN_ANALYSIS_INTERVAL = 30

def get_current_price():
    """
    获取当前价格

    Returns:
        float: 当前价格
    """
    try:
        # 检查MT4客户端是否连接
        if not mt4_client.mt4_client.is_connected:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端未连接，尝试连接...')
            mt4_client.mt4_client.connect()
            time.sleep(1)  # 等待连接建立

        # 获取市场信息
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 从MT4获取市场信息...')
        market_info_response = mt4_client.mt4_client.get_market_info('EURUSD')

        # 打印响应，便于调试
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4响应: {market_info_response}')

        if market_info_response and market_info_response.get('status') == 'success':
            # 检查数据是否完整
            if 'data' in market_info_response and 'bid' in market_info_response['data'] and 'ask' in market_info_response['data']:
                bid = float(market_info_response['data']['bid'])
                ask = float(market_info_response['data']['ask'])
                price = (bid + ask) / 2
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 成功获取价格: bid={bid}, ask={ask}, price={price}')
                return price
            else:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4响应数据不完整: {market_info_response}')
                return None
        else:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4响应状态不成功: {market_info_response}')
            return None
    except Exception as error:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 获取当前价格失败: {error}')
        traceback.print_exc()
        return None

def get_basic_market_data():
    """
    获取基本市场数据

    Returns:
        dict: 基本市场数据
    """
    try:
        # 获取当前价格
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 获取当前价格...')
        current_price = get_current_price()
        if current_price is None:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 无法获取当前价格')
            return None
        else:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 当前价格: {current_price}')

        # 获取最近的K线数据（只获取少量数据）
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 获取K线数据...')
        klines = get_aggregated_klines(15, 5)  # 只获取5根15分钟K线
        if not klines:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 无法获取K线数据')
            return None
        elif len(klines) < 3:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] K线数据不足，只有 {len(klines)} 根')
            return None
        else:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 成功获取 {len(klines)} 根K线数据')

        # 计算简单的技术指标
        try:
            latest_kline = klines[-1]
            prev_kline = klines[-2]

            # 打印K线数据，便于调试
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 最新K线: {latest_kline}')
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 前一K线: {prev_kline}')

            # 计算简单的价格变化
            price_change = current_price - float(latest_kline['close'])
            price_change_percent = price_change / float(latest_kline['close']) * 100

            # 计算简单的波动性
            high_low_range = float(latest_kline['high']) - float(latest_kline['low'])
            volatility = high_low_range / float(latest_kline['close']) * 100

            # 判断趋势方向
            trend = "上涨" if float(latest_kline['close']) > float(prev_kline['close']) else "下跌"

            # 获取相关新闻和日历事件
            try:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 获取相关新闻...')
                news = get_relevant_news(5)  # 只获取5条最新相关新闻
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 成功获取{len(news)}条相关新闻')

                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 获取相关日历事件...')
                calendar = get_relevant_calendar(5)  # 只获取5条最新相关日历事件
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 成功获取{len(calendar)}条相关日历事件')

                # 检查是否有重要新闻或日历事件
                has_important_news = False
                has_important_calendar = False

                # 检查新闻重要性
                for item in news:
                    if '重要' in item.get('title', '') or '关键' in item.get('title', '') or '央行' in item.get('title', ''):
                        has_important_news = True
                        break

                # 检查日历事件重要性
                for item in calendar:
                    importance = item.get('importance', '').lower()
                    if importance == 'high' or importance == '高' or '重要' in importance:
                        has_important_calendar = True
                        break
            except Exception as e:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 获取新闻和日历事件失败: {e}')
                news = []
                calendar = []
                has_important_news = False
                has_important_calendar = False

            # 构建市场数据
            market_data = {
                "current_price": current_price,
                "latest_close": float(latest_kline['close']),
                "price_change": price_change,
                "price_change_percent": price_change_percent,
                "volatility": volatility,
                "trend": trend,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "news": news,
                "calendar": calendar,
                "has_important_news": has_important_news,
                "has_important_calendar": has_important_calendar
            }

            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 成功构建市场数据: {market_data}')
            return market_data
        except Exception as calc_error:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 计算技术指标失败: {calc_error}')
            traceback.print_exc()
            return None
    except Exception as error:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 获取基本市场数据失败: {error}')
        traceback.print_exc()
        return None

def should_perform_analysis():
    """
    判断是否应该执行完整分析

    Returns:
        tuple: (是否应该分析, 原因)
    """
    global last_price, last_analysis_time

    # 打印当前时间，便于日志跟踪
    now = datetime.now()
    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 开始判断是否需要执行完整分析')

    # 检查上次分析时间，避免过于频繁的分析
    if last_analysis_time:
        minutes_since_last_analysis = (now - last_analysis_time).total_seconds() / 60
        print(f'距离上次分析已过去 {minutes_since_last_analysis:.1f} 分钟')
        if minutes_since_last_analysis < MIN_ANALYSIS_INTERVAL:
            return False, f"距离上次分析仅过去{minutes_since_last_analysis:.1f}分钟，未达到最小间隔{MIN_ANALYSIS_INTERVAL}分钟"

    # 获取基本市场数据
    market_data = get_basic_market_data()
    if not market_data:
        return False, "无法获取基本市场数据"

    # 如果是首次检查，保存数据并返回
    if last_price is None:
        last_price = market_data["current_price"]
        return False, "首次检查，建立基准数据"

    # 导入模板管理器
    from app.utils import prompt_template_manager

    # 准备模板数据
    template_data = {
        'current_price': market_data['current_price'],
        'latest_close': market_data['latest_close'],
        'price_change': f"{market_data['price_change']:.5f}",
        'price_change_percent': f"{market_data['price_change_percent']:.2f}",
        'volatility': f"{market_data['volatility']:.2f}",
        'trend': market_data['trend'],
        'timestamp': market_data['timestamp'],
        'last_price': last_price,
        'price_diff': f"{abs(market_data['current_price'] - last_price):.5f}",
        'has_important_news': market_data.get('has_important_news', False),
        'has_important_calendar': market_data.get('has_important_calendar', False),
        'news_count': len(market_data.get('news', [])),
        'calendar_count': len(market_data.get('calendar', []))
    }

    try:
        # 使用模板管理器渲染模板
        prompt = prompt_template_manager.render_template('market_change_analyzer_template', template_data)
        print(f'使用模板渲染提示词，长度: {len(prompt)} 字符')
    except Exception as e:
        print(f'使用模板渲染失败: {e}，回退到硬编码提示词')
        # 如果模板渲染失败，回退到硬编码提示词
        prompt = f"""
你是一个外汇交易分析助手。请根据以下简要市场数据，判断是否需要进行完整的市场分析。
只需回答"是"或"否"，并给出一个简短理由（不超过20字）。

当前EURUSD价格: {market_data['current_price']}
最近收盘价: {market_data['latest_close']}
价格变化: {market_data['price_change']:.5f} ({market_data['price_change_percent']:.2f}%)
波动率: {market_data['volatility']:.2f}%
趋势方向: {market_data['trend']}
时间: {market_data['timestamp']}

上次记录价格: {last_price}
价格差异: {abs(market_data['current_price'] - last_price):.5f}

新闻和事件信息:
- 相关新闻数量: {len(market_data.get('news', []))}
- 是否有重要新闻: {'是' if market_data.get('has_important_news', False) else '否'}
- 相关日历事件数量: {len(market_data.get('calendar', []))}
- 是否有重要日历事件: {'是' if market_data.get('has_important_calendar', False) else '否'}

注意：你的回答必须以"是"或"否"开头，然后给出简短理由。
例如："是，价格突破关键阻力位" 或 "否，市场波动较小"

回答格式:
是/否，简短理由
"""

    try:
        # 使用较低的temperature以获得更确定的回答
        print(f'发送LLM判断请求，提示词长度: {len(prompt)} 字符')
        response = llm_client.send_to_deepseek(prompt, temperature=0.1, max_tokens=50)

        # 检查响应是否有效
        if not response or 'choices' not in response or not response['choices'] or 'message' not in response['choices'][0]:
            print(f'LLM响应无效: {response}')
            raise ValueError("LLM响应格式无效")

        answer = response['choices'][0]['message']['content'].strip()
        print(f'LLM回答: {answer}')

        # 解析回答
        if answer.lower().startswith('是') or '是' in answer[:5]:
            # 更新最后的价格和分析时间
            last_price = market_data["current_price"]
            last_analysis_time = now
            reason = answer.replace('是', '', 1).strip().strip('，').strip(',').strip('。').strip('.')
            if not reason:
                reason = "市场变化需要分析"
            return True, f"LLM建议进行分析: {reason}"
        else:
            # 仍然更新最后的价格，但不更新分析时间
            last_price = market_data["current_price"]
            reason = answer.replace('否', '', 1).strip().strip('，').strip(',').strip('。').strip('.')
            if not reason:
                reason = "市场稳定"
            return False, f"LLM建议不进行分析: {reason}"
    except Exception as error:
        print(f'LLM判断失败: {error}')
        import traceback
        traceback.print_exc()

        # 如果LLM判断失败，使用简单的价格变化规则作为备选
        try:
            price_change = abs(market_data["current_price"] - last_price)
            print(f'使用备选方案，价格变化: {price_change:.5f}')

            # 更新最后的价格
            last_price = market_data["current_price"]

            # 判断价格变化是否显著
            if price_change >= 0.0005:  # 5个点的变化
                last_analysis_time = now
                return True, f"价格变化显著: {price_change:.5f}"
            else:
                return False, f"价格变化不显著: {price_change:.5f}"
        except Exception as backup_error:
            print(f'备选方案也失败了: {backup_error}')
            traceback.print_exc()

            # 如果备选方案也失败了，返回一个安全的默认值
            return False, f"判断失败，默认不进行分析"
