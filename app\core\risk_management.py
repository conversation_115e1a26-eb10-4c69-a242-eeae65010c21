#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心风险管理系统
实现全面的风险控制、动态仓位管理和紧急保护机制
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

class RiskLevel(Enum):
    """风险等级"""
    EMERGENCY = "紧急"
    CRITICAL = "严重"
    HIGH = "高风险"
    MEDIUM = "中风险"
    LOW = "低风险"
    SAFE = "安全"

class TradingAction(Enum):
    """交易行动"""
    EMERGENCY_CLOSE_ALL = "紧急平仓所有"
    REDUCE_POSITIONS = "减少持仓"
    STOP_NEW_TRADES = "停止新交易"
    REDUCE_POSITION_SIZE = "减少仓位"
    NORMAL_TRADING = "正常交易"

@dataclass
class RiskMetrics:
    """风险指标"""
    account_drawdown: float         # 账户回撤
    daily_pnl: float               # 日盈亏
    weekly_pnl: float              # 周盈亏
    position_risk: float           # 持仓风险
    portfolio_risk: float          # 组合风险
    volatility_risk: float         # 波动率风险
    correlation_risk: float        # 相关性风险
    risk_level: RiskLevel          # 风险等级
    recommended_action: TradingAction  # 建议行动

@dataclass
class PositionRisk:
    """单个持仓风险"""
    symbol: str
    position_size: float
    entry_price: float
    current_price: float
    stop_loss: float
    unrealized_pnl: float
    risk_amount: float
    risk_percentage: float

class AdvancedRiskManager:
    """高级风险管理器"""

    def __init__(self):
        # 风险限制参数
        self.risk_limits = {
            # 账户级别风险
            'emergency_drawdown': 0.15,      # 15%紧急止损
            'critical_drawdown': 0.10,       # 10%严重警告
            'max_daily_loss': 0.05,          # 5%日最大亏损
            'max_weekly_loss': 0.12,         # 12%周最大亏损

            # 持仓级别风险
            'max_single_position': 0.03,     # 3%单笔最大风险
            'max_portfolio_risk': 0.08,      # 8%组合最大风险
            'max_correlation_exposure': 0.15, # 15%最大相关性暴露

            # 交易频率控制
            'max_daily_trades': 5,           # 日最大交易次数
            'min_trade_interval': 300,       # 最小交易间隔(秒)
            'max_consecutive_losses': 4,     # 最大连续亏损次数

            # 波动率控制
            'max_volatility_multiplier': 2.5, # 最大波动率倍数
            'volatility_adjustment': True,    # 启用波动率调整
        }

        # 状态跟踪
        self.daily_stats = {
            'pnl': 0.0,
            'trades_count': 0,
            'consecutive_losses': 0,
            'last_trade_time': None,
            'reset_date': datetime.now().date()
        }

        self.weekly_stats = {
            'pnl': 0.0,
            'reset_date': datetime.now().date()
        }

        # 历史数据
        self.risk_history = []
        self.performance_history = []

        # 日志
        self.logger = logging.getLogger(__name__)

    def assess_comprehensive_risk(self, account_info: Dict, positions: List[Dict],
                                market_data: Dict) -> RiskMetrics:
        """全面风险评估"""

        # 重置统计（如果需要）
        self._reset_stats_if_needed()

        # 1. 账户风险评估
        account_risk = self._assess_account_risk(account_info)

        # 2. 持仓风险评估
        position_risks = self._assess_position_risks(positions, account_info, market_data)

        # 3. 组合风险评估
        portfolio_risk = self._assess_portfolio_risk(position_risks, market_data)

        # 4. 市场风险评估
        market_risk = self._assess_market_risk(market_data)

        # 5. 综合风险等级
        risk_level = self._determine_comprehensive_risk_level(
            account_risk, portfolio_risk, market_risk
        )

        # 6. 推荐行动
        recommended_action = self._determine_recommended_action(risk_level, account_risk)

        risk_metrics = RiskMetrics(
            account_drawdown=account_risk['drawdown'],
            daily_pnl=self.daily_stats.get('pnl', 0.0),
            weekly_pnl=self.weekly_stats.get('pnl', 0.0),
            position_risk=portfolio_risk['total_position_risk'],
            portfolio_risk=portfolio_risk['portfolio_risk'],
            volatility_risk=market_risk['volatility_risk'],
            correlation_risk=portfolio_risk['correlation_risk'],
            risk_level=risk_level,
            recommended_action=recommended_action
        )

        # 记录风险历史
        self._record_risk_metrics(risk_metrics)

        return risk_metrics

    def calculate_optimal_position_size(self, signal_confidence: float,
                                      market_volatility: float,
                                      risk_metrics: RiskMetrics,
                                      account_balance: float) -> float:
        """计算最优仓位大小"""

        # 基础风险预算
        base_risk = self.risk_limits['max_single_position']

        # 1. 信号质量调整
        confidence_adj = 0.3 + (signal_confidence * 0.7)  # 30%-100%

        # 2. 波动率调整
        if self.risk_limits['volatility_adjustment']:
            normal_volatility = 0.0015  # 正常波动率基准
            volatility_adj = min(normal_volatility / max(market_volatility, 0.0005),
                                self.risk_limits['max_volatility_multiplier'])
        else:
            volatility_adj = 1.0

        # 3. 风险状态调整
        risk_adj = {
            RiskLevel.SAFE: 1.0,
            RiskLevel.LOW: 0.8,
            RiskLevel.MEDIUM: 0.6,
            RiskLevel.HIGH: 0.3,
            RiskLevel.CRITICAL: 0.1,
            RiskLevel.EMERGENCY: 0.0
        }.get(risk_metrics.risk_level, 0.3)

        # 4. 组合风险调整
        portfolio_adj = max(0.2, 1.0 - (risk_metrics.portfolio_risk / 0.1))

        # 5. 连续亏损调整
        loss_adj = max(0.3, 1.0 - (self.daily_stats['consecutive_losses'] * 0.2))

        # 计算最终仓位
        final_size = (base_risk * confidence_adj * volatility_adj *
                     risk_adj * portfolio_adj * loss_adj)

        # 确保在合理范围内
        min_size = 0.005  # 最小0.5%
        max_size = self.risk_limits['max_single_position']

        return max(min(final_size, max_size), min_size)

    def should_allow_trading(self, risk_metrics: RiskMetrics) -> Tuple[bool, str]:
        """判断是否允许交易"""

        # 紧急情况：禁止所有交易
        if risk_metrics.recommended_action == TradingAction.EMERGENCY_CLOSE_ALL:
            return False, "紧急风险：账户亏损过大，禁止所有交易"

        # 严重风险：禁止新交易
        if risk_metrics.recommended_action in [TradingAction.STOP_NEW_TRADES,
                                             TradingAction.REDUCE_POSITIONS]:
            return False, f"风险控制：{risk_metrics.recommended_action.value}"

        # 日交易次数限制
        if self.daily_stats['trades_count'] >= self.risk_limits['max_daily_trades']:
            return False, f"日交易次数达到限制({self.risk_limits['max_daily_trades']})"

        # 交易间隔限制
        if self._check_trade_interval():
            return False, "交易间隔不足，请等待"

        # 连续亏损限制
        if self.daily_stats['consecutive_losses'] >= self.risk_limits['max_consecutive_losses']:
            return False, f"连续亏损{self.daily_stats['consecutive_losses']}次，暂停交易"

        return True, "风险可控，允许交易"

    def update_trade_result(self, trade_result: Dict):
        """更新交易结果"""

        profit_loss = trade_result.get('profit_loss', 0)

        # 更新日统计
        self.daily_stats['pnl'] += profit_loss
        self.daily_stats['trades_count'] += 1
        self.daily_stats['last_trade_time'] = datetime.now()

        # 更新周统计
        self.weekly_stats['pnl'] += profit_loss

        # 更新连续亏损
        if profit_loss < 0:
            self.daily_stats['consecutive_losses'] += 1
        else:
            self.daily_stats['consecutive_losses'] = 0

        # 记录交易历史
        self.performance_history.append({
            'timestamp': datetime.now().isoformat(),
            'profit_loss': profit_loss,
            'daily_pnl': self.daily_stats['pnl'],
            'consecutive_losses': self.daily_stats['consecutive_losses']
        })

        # 保持最近100笔记录
        if len(self.performance_history) > 100:
            self.performance_history = self.performance_history[-100:]

    def get_emergency_actions(self, risk_metrics: RiskMetrics) -> List[Dict]:
        """获取紧急行动建议"""

        actions = []

        if risk_metrics.recommended_action == TradingAction.EMERGENCY_CLOSE_ALL:
            actions.append({
                'action': 'CLOSE_ALL_POSITIONS',
                'priority': 'CRITICAL',
                'reason': f'账户回撤{risk_metrics.account_drawdown:.1%}，触发紧急止损'
            })

        elif risk_metrics.recommended_action == TradingAction.REDUCE_POSITIONS:
            actions.append({
                'action': 'REDUCE_POSITION_SIZES',
                'priority': 'HIGH',
                'reason': f'风险等级{risk_metrics.risk_level.value}，建议减仓50%'
            })

        elif risk_metrics.recommended_action == TradingAction.STOP_NEW_TRADES:
            actions.append({
                'action': 'PAUSE_NEW_TRADES',
                'priority': 'MEDIUM',
                'reason': f'日亏损{abs(risk_metrics.daily_pnl):.1%}，暂停新交易'
            })

        return actions

    def _assess_account_risk(self, account_info: Dict) -> Dict:
        """评估账户风险"""

        balance = account_info.get('balance', 10000)
        equity = account_info.get('equity', balance)

        # 计算回撤
        drawdown = max(0, (balance - equity) / balance)

        # 计算浮动盈亏
        floating_pnl = equity - balance
        floating_pnl_pct = floating_pnl / balance

        return {
            'drawdown': drawdown,
            'floating_pnl': floating_pnl,
            'floating_pnl_pct': floating_pnl_pct,
            'balance': balance,
            'equity': equity
        }

    def _assess_position_risks(self, positions: List[Dict], account_info: Dict,
                             market_data: Dict) -> List[PositionRisk]:
        """评估持仓风险"""

        position_risks = []
        account_balance = account_info.get('balance', 10000)
        current_price = market_data.get('current_price', 0)

        for position in positions:
            symbol = position.get('symbol', 'UNKNOWN')
            position_size = position.get('lot_size', 0)
            entry_price = position.get('entry_price', 0)
            stop_loss = position.get('stop_loss', 0)
            position_type = position.get('type', 'BUY')

            if entry_price > 0 and stop_loss > 0:
                # 计算未实现盈亏
                if position_type == 'BUY':
                    unrealized_pnl = (current_price - entry_price) * position_size * 100000
                else:
                    unrealized_pnl = (entry_price - current_price) * position_size * 100000

                # 计算风险金额
                risk_amount = abs(entry_price - stop_loss) * position_size * 100000
                risk_percentage = risk_amount / account_balance

                position_risk = PositionRisk(
                    symbol=symbol,
                    position_size=position_size,
                    entry_price=entry_price,
                    current_price=current_price,
                    stop_loss=stop_loss,
                    unrealized_pnl=unrealized_pnl,
                    risk_amount=risk_amount,
                    risk_percentage=risk_percentage
                )

                position_risks.append(position_risk)

        return position_risks

    def _assess_portfolio_risk(self, position_risks: List[PositionRisk],
                             market_data: Dict) -> Dict:
        """评估组合风险"""

        if not position_risks:
            return {
                'total_position_risk': 0.0,
                'portfolio_risk': 0.0,
                'correlation_risk': 0.0,
                'concentration_risk': 0.0
            }

        # 总持仓风险
        total_position_risk = sum(pos.risk_percentage for pos in position_risks)

        # 相关性风险（简化计算）
        # 假设同方向持仓相关性0.7，反方向相关性-0.3
        correlation_adjustment = 0.8  # 简化的相关性调整
        portfolio_risk = total_position_risk * correlation_adjustment

        # 集中度风险
        max_single_risk = max(pos.risk_percentage for pos in position_risks) if position_risks else 0
        concentration_risk = max_single_risk / total_position_risk if total_position_risk > 0 else 0

        # 相关性风险评估
        correlation_risk = min(total_position_risk * 0.3, 0.05)  # 简化计算

        return {
            'total_position_risk': total_position_risk,
            'portfolio_risk': portfolio_risk,
            'correlation_risk': correlation_risk,
            'concentration_risk': concentration_risk
        }

    def _assess_market_risk(self, market_data: Dict) -> Dict:
        """评估市场风险"""

        # 波动率风险
        current_atr = market_data.get('atr', 0.001)
        normal_atr = 0.0015  # 正常ATR基准
        volatility_ratio = current_atr / normal_atr

        volatility_risk = min(max(volatility_ratio - 1, 0) * 0.5, 0.3)

        # 流动性风险（简化）
        spread = market_data.get('spread', 2)
        liquidity_risk = min(max(spread - 3, 0) * 0.01, 0.1)

        return {
            'volatility_risk': volatility_risk,
            'liquidity_risk': liquidity_risk,
            'market_stress': volatility_risk + liquidity_risk
        }

    def _determine_comprehensive_risk_level(self, account_risk: Dict,
                                          portfolio_risk: Dict,
                                          market_risk: Dict) -> RiskLevel:
        """确定综合风险等级"""

        drawdown = account_risk['drawdown']
        daily_loss = abs(self.daily_stats['pnl']) if self.daily_stats['pnl'] < 0 else 0
        portfolio_risk_val = portfolio_risk['portfolio_risk']

        # 紧急情况
        if (drawdown >= self.risk_limits['emergency_drawdown'] or
            daily_loss >= self.risk_limits['emergency_drawdown']):
            return RiskLevel.EMERGENCY

        # 严重风险
        if (drawdown >= self.risk_limits['critical_drawdown'] or
            daily_loss >= self.risk_limits['critical_drawdown'] or
            portfolio_risk_val >= 0.12):
            return RiskLevel.CRITICAL

        # 高风险
        if (drawdown >= 0.06 or
            daily_loss >= 0.04 or
            portfolio_risk_val >= 0.10 or
            self.daily_stats['consecutive_losses'] >= 3):
            return RiskLevel.HIGH

        # 中风险
        if (drawdown >= 0.03 or
            daily_loss >= 0.02 or
            portfolio_risk_val >= 0.06):
            return RiskLevel.MEDIUM

        # 低风险
        if (drawdown >= 0.01 or
            daily_loss >= 0.01 or
            portfolio_risk_val >= 0.03):
            return RiskLevel.LOW

        return RiskLevel.SAFE

    def _determine_recommended_action(self, risk_level: RiskLevel,
                                    account_risk: Dict) -> TradingAction:
        """确定推荐行动"""

        if risk_level == RiskLevel.EMERGENCY:
            return TradingAction.EMERGENCY_CLOSE_ALL
        elif risk_level == RiskLevel.CRITICAL:
            return TradingAction.REDUCE_POSITIONS
        elif risk_level == RiskLevel.HIGH:
            return TradingAction.STOP_NEW_TRADES
        elif risk_level == RiskLevel.MEDIUM:
            return TradingAction.REDUCE_POSITION_SIZE
        else:
            return TradingAction.NORMAL_TRADING

    def _check_trade_interval(self) -> bool:
        """检查交易间隔"""
        if self.daily_stats['last_trade_time'] is None:
            return False

        time_since_last = (datetime.now() - self.daily_stats['last_trade_time']).total_seconds()
        return time_since_last < self.risk_limits['min_trade_interval']

    def _reset_stats_if_needed(self):
        """重置统计（如果需要）"""
        current_date = datetime.now().date()

        # 重置日统计
        if current_date != self.daily_stats['reset_date']:
            self.daily_stats.update({
                'pnl': 0.0,
                'trades_count': 0,
                'reset_date': current_date
            })

        # 重置周统计
        days_since_week_reset = (current_date - self.weekly_stats['reset_date']).days
        if days_since_week_reset >= 7:
            self.weekly_stats.update({
                'pnl': 0.0,
                'reset_date': current_date
            })

    def _record_risk_metrics(self, risk_metrics: RiskMetrics):
        """记录风险指标"""
        self.risk_history.append({
            'timestamp': datetime.now().isoformat(),
            'risk_level': risk_metrics.risk_level.value,
            'account_drawdown': risk_metrics.account_drawdown,
            'portfolio_risk': risk_metrics.portfolio_risk,
            'daily_pnl': risk_metrics.daily_pnl
        })

        # 保持最近200条记录
        if len(self.risk_history) > 200:
            self.risk_history = self.risk_history[-200:]

    def get_risk_statistics(self) -> Dict:
        """获取风险统计"""
        return {
            'current_risk_level': self.risk_history[-1]['risk_level'] if self.risk_history else 'UNKNOWN',
            'daily_stats': self.daily_stats.copy(),
            'weekly_stats': self.weekly_stats.copy(),
            'risk_limits': self.risk_limits.copy(),
            'recent_performance': self.performance_history[-10:] if self.performance_history else []
        }
