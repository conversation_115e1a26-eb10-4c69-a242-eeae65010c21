#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版交易系统可视化仪表板
不依赖matplotlib，使用纯tkinter实现
"""

import os
import sys
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import tkinter as tk
from tkinter import ttk, messagebox
import random

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SimpleTradingDashboard:
    """简化版交易系统可视化仪表板"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("外汇交易系统 - 实时监控仪表板")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')

        # 数据存储
        self.market_data = {}
        self.analysis_history = []
        self.system_status = {
            'last_update': None,
            'components_status': {},
            'error_count': 0,
            'success_count': 0,
            'start_time': datetime.now()
        }

        # 监控状态
        self.monitoring = False

        # 创建界面
        self.create_widgets()

        # 启动数据更新线程
        self.running = True
        self.update_thread = threading.Thread(target=self.update_data_loop, daemon=True)
        self.update_thread.start()

    def create_widgets(self):
        """创建界面组件"""

        # 主标题
        title_frame = tk.Frame(self.root, bg='#2b2b2b')
        title_frame.pack(fill='x', padx=10, pady=5)

        title_label = tk.Label(
            title_frame,
            text="🚀 外汇交易系统实时监控仪表板",
            font=('Arial', 18, 'bold'),
            fg='#00ff00',
            bg='#2b2b2b'
        )
        title_label.pack()

        # 创建主要区域
        main_frame = tk.Frame(self.root, bg='#2b2b2b')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # 上半部分 - 系统状态和市场数据
        top_frame = tk.Frame(main_frame, bg='#2b2b2b')
        top_frame.pack(fill='x', pady=(0, 10))

        self.create_status_section(top_frame)
        self.create_market_section(top_frame)

        # 下半部分 - 分析结果和控制
        bottom_frame = tk.Frame(main_frame, bg='#2b2b2b')
        bottom_frame.pack(fill='both', expand=True)

        self.create_analysis_section(bottom_frame)
        self.create_control_section(bottom_frame)

        # 底部状态栏
        self.create_status_bar()

    def create_status_section(self, parent):
        """创建系统状态区域"""

        status_frame = tk.LabelFrame(
            parent,
            text="📊 系统状态",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#3b3b3b',
            relief='raised',
            bd=2
        )
        status_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))

        # 组件状态网格
        components_frame = tk.Frame(status_frame, bg='#3b3b3b')
        components_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # 组件状态标签
        self.component_labels = {}
        components = [
            ('数据处理', 'data_processor'),
            ('市场分析', 'market_analyzer'),
            ('预分析', 'pre_analyzer'),
            ('LLM分析', 'llm_analyzer'),
            ('风险管理', 'risk_manager'),
            ('组合管理', 'portfolio_manager'),
            ('MT4连接', 'mt4_connection')
        ]

        for i, (name, key) in enumerate(components):
            row = i // 2
            col = i % 2

            frame = tk.Frame(components_frame, bg='#3b3b3b')
            frame.grid(row=row, column=col, sticky='ew', padx=5, pady=2)

            components_frame.grid_columnconfigure(col, weight=1)

            label = tk.Label(
                frame,
                text=f"{name}:",
                font=('Arial', 10),
                fg='#cccccc',
                bg='#3b3b3b',
                width=10,
                anchor='w'
            )
            label.pack(side='left')

            status_label = tk.Label(
                frame,
                text="🔴 离线",
                font=('Arial', 10, 'bold'),
                fg='#ff6666',
                bg='#3b3b3b',
                anchor='w'
            )
            status_label.pack(side='left', padx=(5, 0))

            self.component_labels[key] = status_label

        # 统计信息
        stats_frame = tk.Frame(status_frame, bg='#3b3b3b')
        stats_frame.pack(fill='x', padx=10, pady=(10, 5))

        tk.Label(
            stats_frame,
            text="📈 运行统计",
            font=('Arial', 11, 'bold'),
            fg='#ffffff',
            bg='#3b3b3b'
        ).pack()

        self.stats_labels = {}
        stats = [
            ('成功分析', 'success_count'),
            ('错误次数', 'error_count'),
            ('运行时间', 'uptime')
        ]

        for name, key in stats:
            frame = tk.Frame(stats_frame, bg='#3b3b3b')
            frame.pack(fill='x', pady=1)

            tk.Label(
                frame,
                text=f"{name}:",
                font=('Arial', 10),
                fg='#cccccc',
                bg='#3b3b3b',
                width=10,
                anchor='w'
            ).pack(side='left')

            value_label = tk.Label(
                frame,
                text="0",
                font=('Arial', 10, 'bold'),
                fg='#00ff00',
                bg='#3b3b3b',
                anchor='w'
            )
            value_label.pack(side='left', padx=(5, 0))

            self.stats_labels[key] = value_label

    def create_market_section(self, parent):
        """创建市场数据区域"""

        market_frame = tk.LabelFrame(
            parent,
            text="📈 市场数据",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#3b3b3b',
            relief='raised',
            bd=2
        )
        market_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))

        # 市场数据显示
        self.market_labels = {}
        market_data = [
            ('交易品种', 'symbol', 'EURUSD'),
            ('当前价格', 'price', '1.13550'),
            ('RSI指标', 'rsi', '65.2'),
            ('MACD', 'macd', '0.0012'),
            ('MA20', 'ma_20', '1.1340'),
            ('更新时间', 'timestamp', '--:--:--')
        ]

        for i, (name, key, default) in enumerate(market_data):
            frame = tk.Frame(market_frame, bg='#3b3b3b')
            frame.pack(fill='x', padx=10, pady=5)

            tk.Label(
                frame,
                text=f"{name}:",
                font=('Arial', 11),
                fg='#cccccc',
                bg='#3b3b3b',
                width=12,
                anchor='w'
            ).pack(side='left')

            value_label = tk.Label(
                frame,
                text=default,
                font=('Arial', 11, 'bold'),
                fg='#00ff00',
                bg='#3b3b3b',
                anchor='w'
            )
            value_label.pack(side='left', padx=(10, 0))

            self.market_labels[key] = value_label

    def create_analysis_section(self, parent):
        """创建分析结果区域"""

        analysis_frame = tk.LabelFrame(
            parent,
            text="🧠 分析结果",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#3b3b3b',
            relief='raised',
            bd=2
        )
        analysis_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))

        # 创建文本显示区域
        text_frame = tk.Frame(analysis_frame, bg='#3b3b3b')
        text_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.analysis_text = tk.Text(
            text_frame,
            bg='#2b2b2b',
            fg='#00ff00',
            font=('Consolas', 10),
            wrap='word',
            height=15
        )

        # 添加滚动条
        scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=scrollbar.set)

        self.analysis_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # 初始化显示
        self.analysis_text.insert(1.0, "等待分析结果...\n")

    def create_control_section(self, parent):
        """创建控制区域"""

        control_frame = tk.LabelFrame(
            parent,
            text="🎛️ 系统控制",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#3b3b3b',
            relief='raised',
            bd=2
        )
        control_frame.pack(side='right', fill='y', padx=(5, 0))

        # 控制按钮
        button_frame = tk.Frame(control_frame, bg='#3b3b3b')
        button_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.start_button = tk.Button(
            button_frame,
            text="▶️ 启动监控",
            command=self.toggle_monitoring,
            bg='#4CAF50',
            fg='white',
            font=('Arial', 12, 'bold'),
            width=15,
            height=2
        )
        self.start_button.pack(pady=5)

        self.refresh_button = tk.Button(
            button_frame,
            text="🔄 刷新数据",
            command=self.refresh_data,
            bg='#2196F3',
            fg='white',
            font=('Arial', 12, 'bold'),
            width=15,
            height=2
        )
        self.refresh_button.pack(pady=5)

        self.test_button = tk.Button(
            button_frame,
            text="🧪 测试分析",
            command=self.run_test_analysis,
            bg='#FF9800',
            fg='white',
            font=('Arial', 12, 'bold'),
            width=15,
            height=2
        )
        self.test_button.pack(pady=5)

        self.clear_button = tk.Button(
            button_frame,
            text="🗑️ 清空日志",
            command=self.clear_logs,
            bg='#f44336',
            fg='white',
            font=('Arial', 12, 'bold'),
            width=15,
            height=2
        )
        self.clear_button.pack(pady=5)

        # 系统信息显示
        info_frame = tk.Frame(control_frame, bg='#3b3b3b')
        info_frame.pack(fill='x', padx=10, pady=(20, 10))

        tk.Label(
            info_frame,
            text="💡 系统信息",
            font=('Arial', 11, 'bold'),
            fg='#ffffff',
            bg='#3b3b3b'
        ).pack()

        self.info_text = tk.Text(
            info_frame,
            bg='#2b2b2b',
            fg='#cccccc',
            font=('Consolas', 9),
            wrap='word',
            height=8,
            width=25
        )
        self.info_text.pack(fill='both', expand=True, pady=(5, 0))

        # 初始化系统信息
        self.update_system_info()

    def create_status_bar(self):
        """创建状态栏"""

        self.status_bar = tk.Frame(self.root, bg='#1b1b1b', relief='sunken', bd=1)
        self.status_bar.pack(side='bottom', fill='x')

        self.status_label = tk.Label(
            self.status_bar,
            text="🔴 系统离线 | 等待连接...",
            bg='#1b1b1b',
            fg='#cccccc',
            font=('Arial', 10),
            anchor='w'
        )
        self.status_label.pack(side='left', padx=10, pady=3)

        self.time_label = tk.Label(
            self.status_bar,
            text="",
            bg='#1b1b1b',
            fg='#cccccc',
            font=('Arial', 10),
            anchor='e'
        )
        self.time_label.pack(side='right', padx=10, pady=3)

    def update_data_loop(self):
        """数据更新循环"""

        while self.running:
            try:
                # 获取真实系统数据
                self.simulate_system_data()

                # 生成分析结果
                self.generate_analysis_result()

                # 更新界面
                self.root.after(0, self.update_display)

                # 更新时间
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.root.after(0, lambda: self.time_label.config(text=current_time))

                time.sleep(5)  # 每5秒更新一次

            except Exception as e:
                print(f"数据更新错误: {e}")
                time.sleep(10)

    def simulate_system_data(self):
        """获取run.py系统的真实运行数据"""

        try:
            # 从run.py的API获取数据
            self.get_data_from_api()

        except Exception as e:
            print(f"从API获取数据失败: {e}")
            # 使用备用数据
            self.use_fallback_data()

    def get_data_from_api(self):
        """从run.py的API获取数据"""

        import requests

        try:
            # 1. 获取最新的分析结果
            analysis_url = "http://localhost:5000/api/forex-trading/analyze"
            response = requests.get(analysis_url, timeout=5)

            if response.status_code == 200:
                analysis_data = response.json()
                if analysis_data.get('success'):
                    result = analysis_data.get('result', {})

                    # 更新市场数据
                    market_data = result.get('marketData', {})
                    if market_data:
                        self.market_data = {
                            'symbol': market_data.get('symbol', 'EURUSD'),
                            'price': market_data.get('currentPrice', 0),
                            'timestamp': datetime.now(),
                            'rsi': None,
                            'macd': None,
                            'ma_20': None
                        }

                        # 从技术指标中提取数据
                        indicators = result.get('indicators', {})
                        if indicators:
                            self.market_data['rsi'] = indicators.get('rsi')
                            self.market_data['macd'] = indicators.get('macd')
                            self.market_data['ma_20'] = indicators.get('ma_20')

                    # 更新分析历史
                    if self.monitoring:
                        analysis_result = {
                            'timestamp': datetime.now(),
                            'action': result.get('tradeInstructions', {}).get('action', 'HOLD'),
                            'confidence': result.get('confidence', 0.5),
                            'risk_level': result.get('riskLevel', 'MEDIUM'),
                            'reasoning': result.get('analysis', '')[:100] + '...' if result.get('analysis') else '基于API分析结果'
                        }

                        # 避免重复添加相同的分析结果
                        if not self.analysis_history or self.analysis_history[-1]['reasoning'] != analysis_result['reasoning']:
                            self.analysis_history.append(analysis_result)
                            if len(self.analysis_history) > 20:
                                self.analysis_history.pop(0)

                            self.system_status['success_count'] += 1

                    return True

            # 如果API调用失败，尝试获取K线数据
            klines_url = "http://localhost:5000/api/forex-trading/eurusd/klines?period=15&count=1"
            response = requests.get(klines_url, timeout=5)

            if response.status_code == 200:
                klines_data = response.json()
                if klines_data.get('success') and klines_data.get('data'):
                    latest_kline = klines_data['data'][0]

                    self.market_data = {
                        'symbol': 'EURUSD',
                        'price': latest_kline.get('close', 0),
                        'open': latest_kline.get('open', 0),
                        'high': latest_kline.get('high', 0),
                        'low': latest_kline.get('low', 0),
                        'volume': latest_kline.get('volume', 0),
                        'timestamp': datetime.now(),
                        'rsi': None,
                        'macd': None,
                        'ma_20': None
                    }

                    return True

            return False

        except requests.exceptions.RequestException as e:
            print(f"API请求失败: {e}")
            return False
        except Exception as e:
            print(f"处理API响应失败: {e}")
            return False

    def use_fallback_data(self):
        """使用备用数据"""
        base_price = 1.13550
        price_change = random.uniform(-0.0005, 0.0005)

        self.market_data = {
            'symbol': 'EURUSD',
            'price': base_price + price_change,
            'rsi': round(random.uniform(30, 70), 1),
            'macd': round(random.uniform(-0.002, 0.002), 6),
            'ma_20': round(base_price + random.uniform(-0.0002, 0.0002), 5),
            'timestamp': datetime.now()
        }

    def calculate_real_indicators(self):
        """计算真实的技术指标"""

        try:
            from app.utils.db_client import get_eurusd_min_data
            import numpy as np

            # 获取最近100条数据用于计算指标
            data = get_eurusd_min_data(limit=100)

            if len(data) >= 20:
                prices = [float(d['close']) for d in data]

                # 计算MA20
                if len(prices) >= 20:
                    self.market_data['ma_20'] = round(np.mean(prices[:20]), 5)

                # 计算简单RSI
                if len(prices) >= 14:
                    self.market_data['rsi'] = self.calculate_rsi(prices[:14])

                # 计算简单MACD
                if len(prices) >= 26:
                    self.market_data['macd'] = self.calculate_simple_macd(prices[:26])

        except Exception as e:
            print(f"计算技术指标失败: {e}")

    def calculate_rsi(self, prices, period=14):
        """计算RSI"""
        try:
            if len(prices) < period + 1:
                return None

            deltas = [prices[i] - prices[i+1] for i in range(len(prices)-1)]
            gains = [d if d > 0 else 0 for d in deltas]
            losses = [-d if d < 0 else 0 for d in deltas]

            avg_gain = sum(gains[:period]) / period
            avg_loss = sum(losses[:period]) / period

            if avg_loss == 0:
                return 100

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            return round(rsi, 1)
        except:
            return None

    def calculate_simple_macd(self, prices):
        """计算简单MACD"""
        try:
            import numpy as np

            if len(prices) < 26:
                return None

            # 简单的EMA计算
            ema_12 = np.mean(prices[:12])
            ema_26 = np.mean(prices[:26])

            macd = ema_12 - ema_26
            return round(macd, 6)
        except:
            return None

    def generate_analysis_result(self):
        """生成分析结果"""

        if self.monitoring:
            # 模拟分析结果
            actions = ['BUY', 'SELL', 'HOLD']
            risk_levels = ['LOW', 'MEDIUM', 'HIGH']

            analysis_result = {
                'timestamp': datetime.now(),
                'action': random.choice(actions),
                'confidence': round(random.uniform(0.6, 0.95), 3),
                'risk_level': random.choice(risk_levels),
                'target_price': self.market_data['price'] + random.uniform(-0.001, 0.001),
                'stop_loss': self.market_data['price'] + random.uniform(-0.0005, 0.0005),
                'reasoning': f"基于RSI({self.market_data.get('rsi', 'N/A')})和MACD({self.market_data.get('macd', 'N/A')})的分析"
            }

            self.analysis_history.append(analysis_result)
            if len(self.analysis_history) > 20:
                self.analysis_history.pop(0)

            self.system_status['success_count'] += 1

        # 更新系统状态
        self.system_status['last_update'] = datetime.now()

        # 检查系统组件状态
        self.check_system_components()

    def check_system_components(self):
        """检查系统组件状态"""

        components = ['data_processor', 'market_analyzer', 'pre_analyzer',
                     'llm_analyzer', 'risk_manager', 'portfolio_manager']

        for comp in components:
            try:
                # 尝试导入相关模块来检查组件状态
                if comp == 'data_processor':
                    from app.utils import db_client
                    self.system_status['components_status'][comp] = True
                elif comp == 'market_analyzer':
                    from app.core.market_adaptive_system import MarketAdaptiveSystem
                    self.system_status['components_status'][comp] = True
                elif comp == 'pre_analyzer':
                    from app.utils.multi_round_analysis import should_perform_analysis
                    self.system_status['components_status'][comp] = True
                elif comp == 'llm_analyzer':
                    from app.utils import llm_client
                    self.system_status['components_status'][comp] = True
                elif comp == 'risk_manager':
                    from app.core.risk_management import AdvancedRiskManager
                    self.system_status['components_status'][comp] = True
                elif comp == 'portfolio_manager':
                    from app.core.portfolio_management_system import PortfolioManager
                    self.system_status['components_status'][comp] = True
                else:
                    self.system_status['components_status'][comp] = random.random() > 0.1
            except:
                self.system_status['components_status'][comp] = False

        # MT4连接状态（周末通常离线）
        is_weekend = datetime.now().weekday() >= 5
        self.system_status['components_status']['mt4_connection'] = not is_weekend

    def update_display(self):
        """更新显示内容"""

        # 更新组件状态
        for comp, label in self.component_labels.items():
            status = self.system_status['components_status'].get(comp, False)
            if status:
                label.config(text="🟢 在线", fg='#00ff00')
            else:
                label.config(text="🔴 离线", fg='#ff6666')

        # 更新统计信息
        self.stats_labels['success_count'].config(text=str(self.system_status['success_count']))
        self.stats_labels['error_count'].config(text=str(self.system_status['error_count']))

        # 计算运行时间
        uptime = datetime.now() - self.system_status['start_time']
        hours, remainder = divmod(uptime.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        uptime_str = f"{uptime.days}d {hours:02d}:{minutes:02d}:{seconds:02d}"
        self.stats_labels['uptime'].config(text=uptime_str)

        # 更新市场数据
        if self.market_data:
            self.market_labels['symbol'].config(text=self.market_data['symbol'])
            self.market_labels['price'].config(text=f"{self.market_data['price']:.5f}")
            self.market_labels['rsi'].config(text=f"{self.market_data['rsi']}")
            self.market_labels['macd'].config(text=f"{self.market_data['macd']:.6f}")
            self.market_labels['ma_20'].config(text=f"{self.market_data['ma_20']:.5f}")
            self.market_labels['timestamp'].config(text=self.market_data['timestamp'].strftime('%H:%M:%S'))

        # 更新分析结果
        self.update_analysis_display()

        # 更新状态栏
        online_count = sum(1 for status in self.system_status['components_status'].values() if status)
        total_count = len(self.system_status['components_status'])

        if online_count == total_count:
            status_text = f"🟢 系统在线 | {online_count}/{total_count} 组件正常"
            self.status_label.config(fg='#00ff00')
        elif online_count > 0:
            status_text = f"🟡 部分在线 | {online_count}/{total_count} 组件正常"
            self.status_label.config(fg='#ffff00')
        else:
            status_text = f"🔴 系统离线 | {online_count}/{total_count} 组件正常"
            self.status_label.config(fg='#ff6666')

        if self.monitoring:
            status_text += " | 监控中..."
        else:
            status_text += " | 待机中"

        self.status_label.config(text=status_text)

    def update_analysis_display(self):
        """更新分析结果显示"""

        if not self.analysis_history:
            return

        # 获取最新分析结果
        latest = self.analysis_history[-1]

        # 格式化显示文本
        display_text = f"""📊 最新分析结果
{'='*40}
🕐 时间: {latest['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}
📈 交易建议: {latest['action']}
🎯 信心度: {latest['confidence']:.1%}
⚠️ 风险等级: {latest['risk_level']}
🎯 目标价位: {latest['target_price']:.5f}
🛡️ 止损价位: {latest['stop_loss']:.5f}
💭 分析理由: {latest['reasoning']}

📋 历史记录 (最近10次):
{'='*40}
"""

        # 添加历史记录
        for i, item in enumerate(self.analysis_history[-10:]):
            action_color = "📈" if item['action'] == 'BUY' else "📉" if item['action'] == 'SELL' else "⏸️"
            display_text += f"{i+1:2d}. {item['timestamp'].strftime('%H:%M')} | {action_color} {item['action']:4s} | {item['confidence']:.1%} | {item['risk_level']}\n"

        # 更新文本显示
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(1.0, display_text)
        self.analysis_text.see(tk.END)

    def update_system_info(self):
        """更新系统信息"""

        info_text = f"""🔗 连接到run.py系统

系统版本: v1.0.0
Python版本: {sys.version.split()[0]}
GUI启动时间: {self.system_status['start_time'].strftime('%H:%M:%S')}

📡 数据源:
• run.py API服务器
• 地址: localhost:5000

支持货币对:
• EURUSD (主要)
• GBPUSD
• AUDUSD
• NZDUSD
• USDCHF
• USDCAD
• USDJPY
• GOLD

🔄 更新方式:
• API调用: 每5秒
• 分析结果: 实时获取
• 系统状态: 动态检查

💡 使用说明:
1. 启动监控查看实时数据
2. 测试分析调用API
3. 确保run.py正在运行
"""

        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, info_text)

    def toggle_monitoring(self):
        """切换监控状态"""

        self.monitoring = not self.monitoring

        if self.monitoring:
            self.start_button.config(text="⏸️ 暂停监控", bg='#ff9800')
            messagebox.showinfo("监控状态", "✅ 监控已启动\n系统将开始实时分析市场数据")
        else:
            self.start_button.config(text="▶️ 启动监控", bg='#4CAF50')
            messagebox.showinfo("监控状态", "⏸️ 监控已暂停\n系统停止实时分析")

    def refresh_data(self):
        """刷新数据"""

        self.simulate_system_data()
        self.update_display()
        messagebox.showinfo("刷新", "✅ 数据已刷新")

    def run_test_analysis(self):
        """运行测试分析 - 调用run.py的API"""

        try:
            messagebox.showinfo("测试分析", "🧪 开始调用run.py的分析API...\n请查看控制台输出")

            # 在后台线程中运行API调用
            def run_api_test():
                try:
                    import requests

                    # 调用强制分析API
                    analysis_url = "http://localhost:5000/api/forex-trading/analyze-force"
                    response = requests.post(analysis_url, timeout=30)

                    if response.status_code == 200:
                        result = response.json()
                        if result.get('success'):
                            analysis_data = result.get('result', {})
                            action = analysis_data.get('tradeInstructions', {}).get('action', 'NONE')
                            confidence = analysis_data.get('confidence', 0)

                            self.root.after(0, lambda: messagebox.showinfo(
                                "分析完成",
                                f"✅ API分析完成\n"
                                f"交易建议: {action}\n"
                                f"信心度: {confidence:.1%}\n"
                                f"来源: run.py系统"
                            ))
                        else:
                            self.root.after(0, lambda: messagebox.showerror(
                                "分析失败",
                                f"❌ API返回失败:\n{result.get('message', '未知错误')}"
                            ))
                    else:
                        self.root.after(0, lambda: messagebox.showerror(
                            "连接失败",
                            f"❌ 无法连接到run.py系统\n"
                            f"状态码: {response.status_code}\n"
                            f"请确保run.py正在运行"
                        ))

                except requests.exceptions.RequestException as e:
                    self.root.after(0, lambda: messagebox.showerror(
                        "连接错误",
                        f"❌ 无法连接到run.py系统:\n{str(e)}\n\n"
                        f"请确保:\n"
                        f"1. run.py正在运行\n"
                        f"2. 服务器在localhost:5000"
                    ))
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror(
                        "测试失败",
                        f"❌ API测试失败:\n{str(e)}"
                    ))

            threading.Thread(target=run_api_test, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"❌ 启动API测试失败:\n{str(e)}")

    def clear_logs(self):
        """清空日志"""

        self.analysis_history.clear()
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(1.0, "日志已清空，等待新的分析结果...\n")
        messagebox.showinfo("清空", "🗑️ 日志已清空")

    def run(self):
        """运行仪表板"""

        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except Exception as e:
            print(f"仪表板运行错误: {e}")

    def on_closing(self):
        """关闭事件处理"""

        self.running = False
        self.root.destroy()

def main():
    """主函数"""

    print("🚀 启动交易系统可视化仪表板...")

    try:
        dashboard = SimpleTradingDashboard()
        dashboard.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        try:
            messagebox.showerror("错误", f"仪表板启动失败:\n{e}")
        except:
            pass

if __name__ == "__main__":
    main()
