"""
QuantumForex Pro - 数据优化工具
检查和优化历史数据，确保ML模型能够正常训练
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.utils.db_client import execute_query
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class DataOptimizer:
    """数据优化器"""
    
    def __init__(self):
        self.supported_pairs = ['EURUSD', 'GBPUSD', 'AUDUSD', 'USDCHF', 'USDCAD', 'USDJPY']
    
    def check_database_status(self):
        """检查数据库状态"""
        print("🔍 检查数据库状态...")
        
        try:
            # 检查EURUSD数据
            sql = """
            SELECT 
                COUNT(*) as total_records,
                MIN(time_date_str) as earliest_date,
                MAX(time_date_str) as latest_date,
                COUNT(DISTINCT DATE(time_date_str)) as trading_days
            FROM min_quote_eurusd
            """
            
            result = execute_query(sql)
            
            if result:
                data = result[0]
                print(f"📊 EURUSD数据统计:")
                print(f"   总记录数: {data['total_records']:,}")
                print(f"   最早日期: {data['earliest_date']}")
                print(f"   最新日期: {data['latest_date']}")
                print(f"   交易天数: {data['trading_days']}")
                
                # 计算数据密度
                if data['total_records'] > 0 and data['trading_days'] > 0:
                    avg_records_per_day = data['total_records'] / data['trading_days']
                    print(f"   平均每日记录数: {avg_records_per_day:.1f}")
                    
                    if avg_records_per_day >= 1000:
                        print("   ✅ 数据密度优秀 (>=1000条/天)")
                    elif avg_records_per_day >= 500:
                        print("   ✅ 数据密度良好 (>=500条/天)")
                    elif avg_records_per_day >= 100:
                        print("   ⚠️ 数据密度中等 (>=100条/天)")
                    else:
                        print("   ❌ 数据密度较低 (<100条/天)")
                
                return data
            else:
                print("❌ 无法获取数据库统计信息")
                return None
                
        except Exception as e:
            print(f"❌ 数据库检查失败: {e}")
            return None
    
    def get_optimized_data(self, symbol='EURUSD', limit=1000):
        """获取优化的历史数据"""
        try:
            print(f"📈 获取{symbol}优化数据 (最多{limit}条)...")
            
            # 获取最近的高质量数据
            sql = f"""
            SELECT time_date_str, price, min as low_price, max as high_price, volume, create_time
            FROM min_quote_{symbol.lower()}
            WHERE price IS NOT NULL 
            AND price > 0
            AND time_date_str IS NOT NULL
            ORDER BY time_min_int DESC 
            LIMIT {limit}
            """
            
            raw_data = execute_query(sql)
            
            if not raw_data:
                print(f"❌ 无法获取{symbol}数据")
                return None
            
            print(f"✅ 获取到{len(raw_data)}条原始数据")
            
            # 数据清洗和转换
            cleaned_data = self._clean_and_process_data(raw_data)
            
            if cleaned_data is not None and len(cleaned_data) > 0:
                print(f"✅ 清洗后得到{len(cleaned_data)}条有效数据")
                
                # 数据质量评估
                quality_score = self._assess_data_quality(cleaned_data)
                print(f"📊 数据质量评分: {quality_score:.2f}/10")
                
                return cleaned_data
            else:
                print("❌ 数据清洗后无有效数据")
                return None
                
        except Exception as e:
            print(f"❌ 数据获取失败: {e}")
            return None
    
    def _clean_and_process_data(self, raw_data):
        """清洗和处理数据"""
        try:
            df_data = []
            
            for row in reversed(raw_data):  # 反转以获得时间顺序
                try:
                    # 解析时间
                    time_str = row['time_date_str']
                    timestamp = pd.to_datetime(time_str)
                    
                    # 价格数据验证
                    close_price = float(row['price'])
                    if close_price <= 0:
                        continue
                    
                    high_price = float(row['high_price']) if row['high_price'] and float(row['high_price']) > 0 else close_price
                    low_price = float(row['low_price']) if row['low_price'] and float(row['low_price']) > 0 else close_price
                    
                    # 确保价格逻辑正确
                    high_price = max(high_price, close_price)
                    low_price = min(low_price, close_price)
                    
                    volume = int(row['volume']) if row['volume'] and int(row['volume']) > 0 else 1000
                    
                    # 估算开盘价
                    open_price = close_price
                    
                    df_data.append({
                        'timestamp': timestamp,
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'volume': volume
                    })
                    
                except Exception as e:
                    continue
            
            if not df_data:
                return None
            
            # 创建DataFrame
            df = pd.DataFrame(df_data)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            # 修正开盘价 (使用前一个收盘价)
            df['open'] = df['close'].shift(1).fillna(df['close'])
            
            # 数据验证
            df = df[df['close'] > 0]  # 移除无效价格
            df = df[df['high'] >= df['low']]  # 移除逻辑错误的数据
            
            # 移除异常值 (价格变化超过10%的数据点)
            price_change = df['close'].pct_change().abs()
            df = df[price_change < 0.1]
            
            return df
            
        except Exception as e:
            print(f"❌ 数据清洗失败: {e}")
            return None
    
    def _assess_data_quality(self, df):
        """评估数据质量"""
        try:
            score = 0
            
            # 1. 数据量评分 (30%)
            data_count = len(df)
            if data_count >= 500:
                score += 3.0
            elif data_count >= 200:
                score += 2.5
            elif data_count >= 100:
                score += 2.0
            elif data_count >= 50:
                score += 1.5
            else:
                score += 1.0
            
            # 2. 数据连续性评分 (25%)
            time_diff = df.index.to_series().diff().dt.total_seconds()
            median_interval = time_diff.median()
            if median_interval <= 60:  # 1分钟间隔
                score += 2.5
            elif median_interval <= 300:  # 5分钟间隔
                score += 2.0
            elif median_interval <= 900:  # 15分钟间隔
                score += 1.5
            else:
                score += 1.0
            
            # 3. 价格数据完整性评分 (25%)
            missing_data = df.isnull().sum().sum()
            if missing_data == 0:
                score += 2.5
            elif missing_data < len(df) * 0.01:  # <1%缺失
                score += 2.0
            elif missing_data < len(df) * 0.05:  # <5%缺失
                score += 1.5
            else:
                score += 1.0
            
            # 4. 价格合理性评分 (20%)
            price_changes = df['close'].pct_change().abs()
            extreme_changes = (price_changes > 0.05).sum()  # >5%变化
            if extreme_changes == 0:
                score += 2.0
            elif extreme_changes < len(df) * 0.01:
                score += 1.5
            else:
                score += 1.0
            
            return min(score, 10.0)
            
        except Exception:
            return 5.0  # 默认中等评分
    
    def optimize_for_ml_training(self, symbol='EURUSD'):
        """为ML训练优化数据"""
        print(f"🤖 为{symbol} ML训练优化数据...")
        
        try:
            # 获取更多数据用于训练
            data = self.get_optimized_data(symbol, limit=2000)
            
            if data is None or len(data) < 50:
                print("❌ 数据不足，无法进行ML训练优化")
                return None
            
            # 特征工程预处理
            features = self._create_ml_features(data)
            
            if features is not None and len(features) >= 50:
                print(f"✅ ML特征工程完成: {len(features)}个样本, {len(features.columns)}个特征")
                
                # 保存优化后的数据
                self._save_optimized_data(symbol, data, features)
                
                return {
                    'raw_data': data,
                    'features': features,
                    'sample_count': len(features),
                    'feature_count': len(features.columns)
                }
            else:
                print("❌ 特征工程失败")
                return None
                
        except Exception as e:
            print(f"❌ ML训练优化失败: {e}")
            return None
    
    def _create_ml_features(self, data):
        """创建ML特征"""
        try:
            features = pd.DataFrame(index=data.index)
            
            # 基础特征
            features['price_change'] = data['close'].pct_change()
            features['volatility'] = data['close'].rolling(10, min_periods=5).std()
            features['volume_change'] = data['volume'].pct_change()
            
            # 技术指标特征
            for period in [5, 10, 20]:
                ma = data['close'].rolling(period, min_periods=max(1, period//2)).mean()
                features[f'ma_{period}_ratio'] = data['close'] / ma - 1
            
            # 移除NaN
            features = features.dropna()
            
            return features
            
        except Exception as e:
            print(f"❌ 特征创建失败: {e}")
            return None
    
    def _save_optimized_data(self, symbol, raw_data, features):
        """保存优化后的数据"""
        try:
            data_dir = "QuantumForex_Pro/data/optimized"
            os.makedirs(data_dir, exist_ok=True)
            
            # 保存原始数据
            raw_data.to_csv(f"{data_dir}/{symbol}_raw_data.csv")
            
            # 保存特征数据
            features.to_csv(f"{data_dir}/{symbol}_features.csv")
            
            print(f"✅ 优化数据已保存到 {data_dir}/")
            
        except Exception as e:
            print(f"⚠️ 数据保存失败: {e}")

def main():
    """主函数"""
    print("🚀 QuantumForex Pro 数据优化工具")
    print("=" * 50)
    
    optimizer = DataOptimizer()
    
    # 1. 检查数据库状态
    db_status = optimizer.check_database_status()
    
    if db_status:
        print("\n" + "=" * 50)
        
        # 2. 优化EURUSD数据
        result = optimizer.optimize_for_ml_training('EURUSD')
        
        if result:
            print(f"\n🎯 优化结果:")
            print(f"   原始数据: {len(result['raw_data'])}条")
            print(f"   ML特征: {result['sample_count']}个样本, {result['feature_count']}个特征")
            print(f"   状态: 可用于ML训练")
        else:
            print("\n❌ 数据优化失败")
    
    print("\n" + "=" * 50)
    print("✅ 数据优化完成")

if __name__ == "__main__":
    main()
