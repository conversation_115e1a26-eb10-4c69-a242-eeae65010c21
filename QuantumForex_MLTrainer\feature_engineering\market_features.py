"""
QuantumForex MLTrainer 市场特征工程
计算价格、时间、波动率等市场特征
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Dict, List

from config.training_config import training_config

class MarketFeatureEngine:
    """市场特征工程器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 特征配置
        self.price_config = training_config.FEATURE_CONFIG['price_features']
        self.time_config = training_config.FEATURE_CONFIG['time_features']
        
        # 生成的特征列表
        self.feature_columns = []
    
    def generate_all_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成所有市场特征"""
        try:
            self.logger.info("🔧 开始生成市场特征...")
            
            # 价格特征
            df = self._add_price_features(df)
            
            # 时间特征
            df = self._add_time_features(df)
            
            # 波动率特征
            df = self._add_volatility_features(df)
            
            # 成交量特征
            df = self._add_volume_features(df)
            
            # OHLC比率特征
            df = self._add_ohlc_features(df)
            
            # 删除包含NaN的行
            df = df.dropna()
            
            self.logger.info(f"✅ 市场特征生成完成: {len(self.feature_columns)}个特征")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 生成市场特征失败: {e}")
            return df
    
    def _add_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加价格特征"""
        try:
            periods = self.price_config['price_changes']
            
            for period in periods:
                # 价格变化
                col_name = f'Price_Change_{period}'
                df[col_name] = df['close'].diff(period)
                self.feature_columns.append(col_name)
                
                # 价格变化百分比
                col_name = f'Price_Change_Pct_{period}'
                df[col_name] = df['close'].pct_change(period)
                self.feature_columns.append(col_name)
                
                # 高低价差
                col_name = f'HL_Diff_{period}'
                df[col_name] = (df['high'] - df['low']).rolling(window=period).mean()
                self.feature_columns.append(col_name)
                
                # 开收价差
                col_name = f'OC_Diff_{period}'
                df[col_name] = (df['close'] - df['open']).rolling(window=period).mean()
                self.feature_columns.append(col_name)
            
            # 收益率特征
            returns_periods = self.price_config['returns']
            for period in returns_periods:
                # 对数收益率
                col_name = f'Log_Return_{period}'
                df[col_name] = np.log(df['close'] / df['close'].shift(period))
                self.feature_columns.append(col_name)
                
                # 累积收益率
                col_name = f'Cumulative_Return_{period}'
                df[col_name] = (df['close'] / df['close'].shift(period) - 1).rolling(window=period).sum()
                self.feature_columns.append(col_name)
            
            return df
            
        except Exception as e:
            self.logger.error(f"添加价格特征失败: {e}")
            return df
    
    def _add_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加时间特征"""
        try:
            if not isinstance(df.index, pd.DatetimeIndex):
                self.logger.warning("索引不是时间类型，跳过时间特征")
                return df
            
            # 小时特征
            if self.time_config['hour_of_day']:
                df['Hour'] = df.index.hour
                df['Hour_Sin'] = np.sin(2 * np.pi * df['Hour'] / 24)
                df['Hour_Cos'] = np.cos(2 * np.pi * df['Hour'] / 24)
                self.feature_columns.extend(['Hour', 'Hour_Sin', 'Hour_Cos'])
            
            # 星期特征
            if self.time_config['day_of_week']:
                df['DayOfWeek'] = df.index.dayofweek
                df['DayOfWeek_Sin'] = np.sin(2 * np.pi * df['DayOfWeek'] / 7)
                df['DayOfWeek_Cos'] = np.cos(2 * np.pi * df['DayOfWeek'] / 7)
                self.feature_columns.extend(['DayOfWeek', 'DayOfWeek_Sin', 'DayOfWeek_Cos'])
            
            # 月份中的天
            if self.time_config['day_of_month']:
                df['DayOfMonth'] = df.index.day
                df['DayOfMonth_Sin'] = np.sin(2 * np.pi * df['DayOfMonth'] / 31)
                df['DayOfMonth_Cos'] = np.cos(2 * np.pi * df['DayOfMonth'] / 31)
                self.feature_columns.extend(['DayOfMonth', 'DayOfMonth_Sin', 'DayOfMonth_Cos'])
            
            # 月份特征
            if self.time_config['month_of_year']:
                df['Month'] = df.index.month
                df['Month_Sin'] = np.sin(2 * np.pi * df['Month'] / 12)
                df['Month_Cos'] = np.cos(2 * np.pi * df['Month'] / 12)
                self.feature_columns.extend(['Month', 'Month_Sin', 'Month_Cos'])
            
            # 季度特征
            if self.time_config['quarter']:
                df['Quarter'] = df.index.quarter
                self.feature_columns.append('Quarter')
            
            # 周末标识
            if self.time_config['is_weekend']:
                df['IsWeekend'] = (df.index.dayofweek >= 5).astype(int)
                self.feature_columns.append('IsWeekend')
            
            # 市场时段
            if self.time_config['market_session']:
                df = self._add_market_session_features(df)
            
            return df
            
        except Exception as e:
            self.logger.error(f"添加时间特征失败: {e}")
            return df
    
    def _add_market_session_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加市场时段特征"""
        try:
            # 定义市场时段（UTC时间）
            # 亚洲时段: 00:00-09:00
            # 欧洲时段: 07:00-16:00
            # 美洲时段: 13:00-22:00
            
            hour = df.index.hour
            
            # 亚洲时段
            df['Asian_Session'] = ((hour >= 0) & (hour < 9)).astype(int)
            
            # 欧洲时段
            df['European_Session'] = ((hour >= 7) & (hour < 16)).astype(int)
            
            # 美洲时段
            df['American_Session'] = ((hour >= 13) & (hour < 22)).astype(int)
            
            # 重叠时段
            df['Asian_European_Overlap'] = ((hour >= 7) & (hour < 9)).astype(int)
            df['European_American_Overlap'] = ((hour >= 13) & (hour < 16)).astype(int)
            
            # 市场活跃度
            df['Market_Activity'] = (
                df['Asian_Session'] * 1 + 
                df['European_Session'] * 2 + 
                df['American_Session'] * 3
            )
            
            self.feature_columns.extend([
                'Asian_Session', 'European_Session', 'American_Session',
                'Asian_European_Overlap', 'European_American_Overlap',
                'Market_Activity'
            ])
            
            return df
            
        except Exception as e:
            self.logger.error(f"添加市场时段特征失败: {e}")
            return df
    
    def _add_volatility_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加波动率特征"""
        try:
            windows = self.price_config['volatility_windows']
            
            for window in windows:
                # 价格波动率（标准差）
                col_name = f'Price_Volatility_{window}'
                df[col_name] = df['close'].rolling(window=window).std()
                self.feature_columns.append(col_name)
                
                # 收益率波动率
                col_name = f'Return_Volatility_{window}'
                returns = df['close'].pct_change()
                df[col_name] = returns.rolling(window=window).std()
                self.feature_columns.append(col_name)
                
                # 真实波动率（基于高低价）
                col_name = f'True_Range_{window}'
                tr1 = df['high'] - df['low']
                tr2 = abs(df['high'] - df['close'].shift(1))
                tr3 = abs(df['low'] - df['close'].shift(1))
                true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
                df[col_name] = true_range.rolling(window=window).mean()
                self.feature_columns.append(col_name)
                
                # 波动率比率
                if window > 5:
                    col_name = f'Volatility_Ratio_{window}_5'
                    df[col_name] = df[f'Price_Volatility_{window}'] / df[f'Price_Volatility_5']
                    self.feature_columns.append(col_name)
            
            return df
            
        except Exception as e:
            self.logger.error(f"添加波动率特征失败: {e}")
            return df
    
    def _add_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加成交量特征"""
        try:
            if 'volume' not in df.columns:
                return df
            
            if not self.price_config['volume_features']:
                return df
            
            # 成交量变化
            periods = [1, 3, 5, 10]
            for period in periods:
                # 成交量变化
                col_name = f'Volume_Change_{period}'
                df[col_name] = df['volume'].diff(period)
                self.feature_columns.append(col_name)
                
                # 成交量变化率
                col_name = f'Volume_Change_Pct_{period}'
                df[col_name] = df['volume'].pct_change(period)
                self.feature_columns.append(col_name)
            
            # 价量关系
            windows = [5, 10, 20]
            for window in windows:
                # 价格-成交量相关性
                col_name = f'Price_Volume_Corr_{window}'
                df[col_name] = df['close'].rolling(window=window).corr(df['volume'])
                self.feature_columns.append(col_name)
                
                # 成交量加权平均价格
                col_name = f'VWAP_{window}'
                typical_price = (df['high'] + df['low'] + df['close']) / 3
                df[col_name] = (typical_price * df['volume']).rolling(window=window).sum() / df['volume'].rolling(window=window).sum()
                self.feature_columns.append(col_name)
            
            return df
            
        except Exception as e:
            self.logger.error(f"添加成交量特征失败: {e}")
            return df
    
    def _add_ohlc_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加OHLC比率特征"""
        try:
            if not self.price_config['ohlc_ratios']:
                return df
            
            # 基本OHLC比率
            df['HL_Ratio'] = (df['high'] - df['low']) / df['close']
            df['OC_Ratio'] = (df['close'] - df['open']) / df['close']
            df['HC_Ratio'] = (df['high'] - df['close']) / df['close']
            df['LC_Ratio'] = (df['close'] - df['low']) / df['close']
            
            self.feature_columns.extend(['HL_Ratio', 'OC_Ratio', 'HC_Ratio', 'LC_Ratio'])
            
            # 蜡烛图模式特征
            df['Bullish_Candle'] = (df['close'] > df['open']).astype(int)
            df['Bearish_Candle'] = (df['close'] < df['open']).astype(int)
            df['Doji_Candle'] = (abs(df['close'] - df['open']) / df['close'] < 0.001).astype(int)
            
            self.feature_columns.extend(['Bullish_Candle', 'Bearish_Candle', 'Doji_Candle'])
            
            # 上下影线比率
            df['Upper_Shadow'] = (df['high'] - np.maximum(df['open'], df['close'])) / df['close']
            df['Lower_Shadow'] = (np.minimum(df['open'], df['close']) - df['low']) / df['close']
            df['Body_Size'] = abs(df['close'] - df['open']) / df['close']
            
            self.feature_columns.extend(['Upper_Shadow', 'Lower_Shadow', 'Body_Size'])
            
            # 连续蜡烛图模式
            windows = [3, 5]
            for window in windows:
                # 连续上涨/下跌
                col_name = f'Consecutive_Bullish_{window}'
                df[col_name] = df['Bullish_Candle'].rolling(window=window).sum()
                self.feature_columns.append(col_name)
                
                col_name = f'Consecutive_Bearish_{window}'
                df[col_name] = df['Bearish_Candle'].rolling(window=window).sum()
                self.feature_columns.append(col_name)
            
            return df
            
        except Exception as e:
            self.logger.error(f"添加OHLC特征失败: {e}")
            return df
    
    def get_feature_names(self) -> List[str]:
        """获取所有特征名称"""
        return self.feature_columns.copy()
    
    def get_feature_importance_groups(self) -> Dict[str, List[str]]:
        """获取特征重要性分组"""
        groups = {
            'price': [col for col in self.feature_columns if any(x in col for x in ['Price', 'Return', 'HL', 'OC'])],
            'time': [col for col in self.feature_columns if any(x in col for x in ['Hour', 'Day', 'Month', 'Quarter', 'Weekend', 'Session'])],
            'volatility': [col for col in self.feature_columns if any(x in col for x in ['Volatility', 'Range'])],
            'volume': [col for col in self.feature_columns if any(x in col for x in ['Volume', 'VWAP'])],
            'candle_patterns': [col for col in self.feature_columns if any(x in col for x in ['Candle', 'Shadow', 'Body', 'Consecutive'])]
        }
        
        return groups

# 创建全局实例
market_engine = MarketFeatureEngine()
