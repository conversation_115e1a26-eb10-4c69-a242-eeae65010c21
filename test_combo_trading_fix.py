#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试组合交易修复效果
验证组合交易是否能防止重复执行
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'QuantumForex_Pro'))

def test_hedge_combo_duplication_prevention():
    """测试对冲组合重复防止"""
    print("🧪 测试对冲组合重复防止...")
    
    try:
        from core.portfolio_manager.combo_trading_manager import ComboTradingManager
        
        # 创建组合交易管理器
        manager = ComboTradingManager()
        
        # 模拟信号
        signals = [
            {
                'symbol': 'AUDUSD',
                'action': 'enter_long',
                'signal_strength': 0.7,
                'confidence': 0.8,
                'position_size': 0.03
            },
            {
                'symbol': 'NZDUSD',
                'action': 'enter_short',
                'signal_strength': -0.6,
                'confidence': 0.7,
                'position_size': 0.03
            }
        ]
        
        print("📊 测试场景:")
        print("   信号: AUDUSD LONG, NZDUSD SHORT")
        print("   这是典型的AUD/NZD对冲组合")
        print("")
        
        # 场景1：无现有持仓，第一次创建对冲组合
        print("🔍 场景1: 无现有持仓，第一次创建对冲组合")
        current_positions_empty = {}
        
        decision1 = manager.analyze_combo_opportunities(signals, current_positions_empty)
        print(f"   决策: {decision1.action}")
        print(f"   原因: {decision1.reason}")
        print(f"   组合数量: {len(decision1.combo_trades)}")
        
        if decision1.combo_trades:
            combo = decision1.combo_trades[0]
            print(f"   组合ID: {combo.combo_id}")
            print(f"   货币对: {combo.symbols}")
            print(f"   方向: {combo.directions}")
        print("")
        
        # 场景2：已有AUD/NZD持仓，尝试再次创建对冲组合
        print("🔍 场景2: 已有AUD/NZD持仓，尝试再次创建对冲组合")
        current_positions_with_aud_nzd = {
            'AUDUSD': [{'volume': 0.03, 'action': 'BUY'}],
            'NZDUSD': [{'volume': 0.03, 'action': 'SELL'}]
        }
        
        decision2 = manager.analyze_combo_opportunities(signals, current_positions_with_aud_nzd)
        print(f"   决策: {decision2.action}")
        print(f"   原因: {decision2.reason}")
        print(f"   组合数量: {len(decision2.combo_trades)}")
        print("")
        
        # 场景3：时间间隔不足的情况
        print("🔍 场景3: 时间间隔不足的情况")
        # 手动设置最近的对冲时间
        manager.last_combo_time['HEDGE'] = datetime.now() - timedelta(minutes=10)  # 10分钟前
        
        decision3 = manager.analyze_combo_opportunities(signals, {})
        print(f"   决策: {decision3.action}")
        print(f"   原因: {decision3.reason}")
        print(f"   组合数量: {len(decision3.combo_trades)}")
        print("")
        
        # 验证结果
        success_criteria = [
            decision1.action == 'create_combo',  # 第一次应该创建
            decision2.action == 'hold',          # 已有持仓时应该拒绝
            decision3.action == 'hold'           # 时间间隔不足时应该拒绝
        ]
        
        print("📊 测试结果验证:")
        print(f"   第一次创建: {'✅ 通过' if success_criteria[0] else '❌ 失败'}")
        print(f"   重复拒绝: {'✅ 通过' if success_criteria[1] else '❌ 失败'}")
        print(f"   时间限制: {'✅ 通过' if success_criteria[2] else '❌ 失败'}")
        
        return all(success_criteria)
        
    except Exception as e:
        print(f"❌ 测试对冲组合重复防止失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pair_combo_detection():
    """测试货币对组合检测"""
    print("\n🧪 测试货币对组合检测...")
    
    try:
        from core.portfolio_manager.combo_trading_manager import ComboTradingManager
        
        # 创建组合交易管理器
        manager = ComboTradingManager()
        
        # 测试不同的持仓组合
        test_cases = [
            {
                'name': '无持仓',
                'positions': {},
                'pair_symbols': ['AUDUSD', 'NZDUSD'],
                'expected': False
            },
            {
                'name': '部分持仓（1个）',
                'positions': {'AUDUSD': [{'volume': 0.03}]},
                'pair_symbols': ['AUDUSD', 'NZDUSD'],
                'expected': True  # 超过一半
            },
            {
                'name': '完整持仓（2个）',
                'positions': {
                    'AUDUSD': [{'volume': 0.03}],
                    'NZDUSD': [{'volume': 0.03}]
                },
                'pair_symbols': ['AUDUSD', 'NZDUSD'],
                'expected': True
            },
            {
                'name': '不相关持仓',
                'positions': {'EURUSD': [{'volume': 0.03}]},
                'pair_symbols': ['AUDUSD', 'NZDUSD'],
                'expected': False
            }
        ]
        
        print("📊 测试货币对组合检测:")
        
        all_passed = True
        for case in test_cases:
            result = manager._has_existing_pair_combo(case['pair_symbols'], case['positions'])
            passed = result == case['expected']
            all_passed = all_passed and passed
            
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"   {case['name']}: {status} (预期: {case['expected']}, 实际: {result})")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试货币对组合检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_time_interval_check():
    """测试时间间隔检查"""
    print("\n🧪 测试时间间隔检查...")
    
    try:
        from core.portfolio_manager.combo_trading_manager import ComboTradingManager
        
        # 创建组合交易管理器
        manager = ComboTradingManager()
        
        # 测试时间间隔检查
        test_cases = [
            {
                'name': '无历史记录',
                'last_time': None,
                'min_interval': 30,
                'expected': True
            },
            {
                'name': '间隔充足（60分钟前）',
                'last_time': datetime.now() - timedelta(minutes=60),
                'min_interval': 30,
                'expected': True
            },
            {
                'name': '间隔不足（10分钟前）',
                'last_time': datetime.now() - timedelta(minutes=10),
                'min_interval': 30,
                'expected': False
            },
            {
                'name': '刚好达到间隔（30分钟前）',
                'last_time': datetime.now() - timedelta(minutes=30),
                'min_interval': 30,
                'expected': True
            }
        ]
        
        print("📊 测试时间间隔检查:")
        
        all_passed = True
        for case in test_cases:
            # 设置测试时间
            if case['last_time']:
                manager.last_combo_time['TEST'] = case['last_time']
            else:
                manager.last_combo_time.pop('TEST', None)
            
            result = manager._check_combo_time_interval('TEST', case['min_interval'])
            passed = result == case['expected']
            all_passed = all_passed and passed
            
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"   {case['name']}: {status} (预期: {case['expected']}, 实际: {result})")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试时间间隔检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 组合交易修复效果测试")
    print("=" * 80)
    
    test_results = []
    
    # 1. 测试对冲组合重复防止
    print("1️⃣ 测试对冲组合重复防止")
    test_results.append(("对冲组合重复防止", test_hedge_combo_duplication_prevention()))
    
    # 2. 测试货币对组合检测
    print("2️⃣ 测试货币对组合检测")
    test_results.append(("货币对组合检测", test_pair_combo_detection()))
    
    # 3. 测试时间间隔检查
    print("3️⃣ 测试时间间隔检查")
    test_results.append(("时间间隔检查", test_time_interval_check()))
    
    # 总结测试结果
    print("\n" + "=" * 80)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print("")
    print(f"🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！组合交易修复成功！")
        print("\n🛠️ 修复内容:")
        print("   ✅ 增加了对冲组合重复检查")
        print("   ✅ 增加了货币对组合存在性检测")
        print("   ✅ 增加了时间间隔限制（30分钟）")
        print("   ✅ 增加了活跃组合跟踪机制")
        print("\n📈 预期效果:")
        print("   - 不再重复创建相同的对冲组合")
        print("   - AUD/NZD组合只会创建一次")
        print("   - 30分钟内不会重复创建对冲")
        print("   - 避免3单AUD、2单NZD的情况")
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
    input("按任意键退出...")
