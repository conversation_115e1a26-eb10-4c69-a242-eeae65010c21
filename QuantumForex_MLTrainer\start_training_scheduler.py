#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动训练调度器
实现自动化的训练触发机制
"""

import sys
import os
import logging
import signal
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def setup_logging():
    """设置日志"""
    import sys

    # 设置控制台编码为UTF-8
    if sys.platform == 'win32':
        import os
        os.system('chcp 65001 > nul')

    # 创建logs目录
    os.makedirs('logs', exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/training_scheduler.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def signal_handler(signum, frame):
    """信号处理器"""
    logger = logging.getLogger(__name__)
    logger.info(f"收到信号 {signum}，正在关闭调度器...")

    # 停止调度器
    from core.training_scheduler import training_scheduler
    training_scheduler.stop_scheduler()

    logger.info("调度器已关闭")
    sys.exit(0)

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("🚀 QuantumForex MLTrainer 训练调度器")
    logger.info("=" * 60)

    try:
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # 导入调度器
        from core.training_scheduler import training_scheduler

        # 启动调度器
        training_scheduler.start_scheduler()

        logger.info("📅 调度器功能:")
        logger.info("   ✅ 定时触发 - 根据配置的频率自动训练")
        logger.info("   ✅ 数据驱动 - 检测到足够新数据时自动训练")
        logger.info("   ✅ 性能驱动 - 模型性能下降时自动重训练")
        logger.info("   ✅ 手动触发 - 支持手动强制触发训练")
        logger.info("")
        logger.info("🔧 控制命令:")
        logger.info("   Ctrl+C: 停止调度器")
        logger.info("   查看状态: 访问调度器状态接口")
        logger.info("")

        # 显示当前配置
        status = training_scheduler.get_scheduler_status()
        logger.info("📊 调度器状态:")
        logger.info(f"   运行状态: {'运行中' if status['is_running'] else '已停止'}")
        logger.info(f"   触发次数: {status['trigger_count']}")
        logger.info("")

        # 保持运行
        logger.info("🔄 调度器正在运行，等待触发条件...")

        while True:
            time.sleep(60)  # 每分钟检查一次

            # 可以在这里添加状态监控
            if not training_scheduler.is_running:
                logger.warning("⚠️ 调度器意外停止")
                break

    except KeyboardInterrupt:
        logger.info("👋 用户中断，正在关闭...")
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        logger.error(f"❌ 调度器运行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        logger.info("🛑 训练调度器已退出")

if __name__ == "__main__":
    main()
