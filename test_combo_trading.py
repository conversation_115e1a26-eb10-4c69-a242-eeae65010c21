#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
组合交易管理系统测试
验证对冲、套利、分散建仓等功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'QuantumForex_Pro'))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def create_test_scenarios():
    """创建测试场景"""
    scenarios = [
        {
            'name': '集中多头信号场景',
            'description': '5个货币对都产生多头信号，测试分散建仓',
            'signals': [
                {'symbol': 'EURUSD', 'action': 'enter_long', 'confidence': 0.75, 'signal_strength': 0.6, 'position_size': 0.05},
                {'symbol': 'GBPUSD', 'action': 'enter_long', 'confidence': 0.70, 'signal_strength': 0.5, 'position_size': 0.05},
                {'symbol': 'AUDUSD', 'action': 'enter_long', 'confidence': 0.68, 'signal_strength': 0.4, 'position_size': 0.05},
                {'symbol': 'NZDUSD', 'action': 'enter_long', 'confidence': 0.72, 'signal_strength': 0.5, 'position_size': 0.05},
                {'symbol': 'USDCAD', 'action': 'enter_long', 'confidence': 0.66, 'signal_strength': 0.3, 'position_size': 0.05}
            ],
            'current_positions': {}
        },
        {
            'name': '强弱对比场景',
            'description': '有强势和弱势信号，测试套利组合',
            'signals': [
                {'symbol': 'EURUSD', 'action': 'enter_long', 'confidence': 0.80, 'signal_strength': 0.8, 'position_size': 0.05},
                {'symbol': 'USDJPY', 'action': 'enter_short', 'confidence': 0.75, 'signal_strength': -0.7, 'position_size': 0.05},
                {'symbol': 'GBPUSD', 'action': 'enter_long', 'confidence': 0.65, 'signal_strength': 0.4, 'position_size': 0.05}
            ],
            'current_positions': {}
        },
        {
            'name': '对冲需求场景',
            'description': '已有高风险持仓，需要对冲',
            'signals': [
                {'symbol': 'USDCHF', 'action': 'enter_long', 'confidence': 0.70, 'signal_strength': 0.5, 'position_size': 0.04},
                {'symbol': 'USDCAD', 'action': 'enter_short', 'confidence': 0.68, 'signal_strength': -0.4, 'position_size': 0.04}
            ],
            'current_positions': {
                'EURUSD': {'size': 0.10, 'direction': 'long'},
                'GBPUSD': {'size': 0.08, 'direction': 'long'},
                'AUDUSD': {'size': 0.06, 'direction': 'long'}
            }
        },
        {
            'name': '轮换交易场景',
            'description': '已有多个持仓，新信号需要轮换',
            'signals': [
                {'symbol': 'USDJPY', 'action': 'enter_long', 'confidence': 0.78, 'signal_strength': 0.6, 'position_size': 0.05}
            ],
            'current_positions': {
                'EURUSD': {'size': 0.05, 'direction': 'long'},
                'GBPUSD': {'size': 0.04, 'direction': 'short'}
            }
        },
        {
            'name': '相关性过滤场景',
            'description': '新信号与现有持仓高相关，测试过滤',
            'signals': [
                {'symbol': 'AUDUSD', 'action': 'enter_long', 'confidence': 0.70, 'signal_strength': 0.5, 'position_size': 0.05},
                {'symbol': 'NZDUSD', 'action': 'enter_long', 'confidence': 0.68, 'signal_strength': 0.4, 'position_size': 0.05}
            ],
            'current_positions': {
                'AUDUSD': {'size': 0.05, 'direction': 'long'}  # 已有澳元持仓
            }
        }
    ]
    
    return scenarios

def test_combo_trading():
    """测试组合交易管理"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🧪 开始测试组合交易管理系统...")
    
    try:
        # 导入组合交易管理器
        from core.portfolio_manager.combo_trading_manager import combo_trading_manager
        
        # 创建测试场景
        scenarios = create_test_scenarios()
        
        logger.info("🎯 测试不同场景下的组合交易决策:")
        logger.info("=" * 80)
        
        for i, scenario in enumerate(scenarios, 1):
            logger.info(f"📊 场景 {i}: {scenario['name']}")
            logger.info(f"   描述: {scenario['description']}")
            logger.info(f"   信号数量: {len(scenario['signals'])}")
            logger.info(f"   现有持仓: {len(scenario['current_positions'])}")
            
            # 显示信号详情
            logger.info("   📈 输入信号:")
            for signal in scenario['signals']:
                logger.info(f"      {signal['symbol']}: {signal['action']}, "
                          f"置信度{signal['confidence']:.1%}, "
                          f"强度{signal['signal_strength']:.2f}")
            
            # 显示现有持仓
            if scenario['current_positions']:
                logger.info("   📋 现有持仓:")
                for symbol, pos in scenario['current_positions'].items():
                    logger.info(f"      {symbol}: {pos['size']}手 {pos['direction']}")
            
            # 分析组合交易机会
            decision = combo_trading_manager.analyze_combo_opportunities(
                scenario['signals'], scenario['current_positions']
            )
            
            # 显示决策结果
            logger.info(f"🎯 组合决策:")
            logger.info(f"   动作: {decision.action}")
            logger.info(f"   理由: {decision.reason}")
            logger.info(f"   置信度: {decision.confidence:.1%}")
            logger.info(f"   预期收益: {decision.expected_return:.1%}")
            logger.info(f"   最大风险: {decision.max_risk:.1%}")
            
            if decision.combo_trades:
                logger.info(f"   📊 组合交易详情:")
                for combo in decision.combo_trades:
                    logger.info(f"      类型: {combo.combo_type.value}")
                    logger.info(f"      描述: {combo.description}")
                    logger.info(f"      货币对: {combo.symbols}")
                    logger.info(f"      方向: {combo.directions}")
                    logger.info(f"      仓位: {combo.position_sizes}")
                    logger.info(f"      风险级别: {combo.risk_level}")
            
            logger.info("")
        
        # 总结分析
        logger.info("📈 组合交易管理系统特点:")
        logger.info("=" * 50)
        logger.info("✅ 解决集中建仓问题:")
        logger.info("   - 分批建仓：避免同时开启多个相同方向仓位")
        logger.info("   - 轮换交易：在有持仓时延迟新交易")
        logger.info("   - 相关性过滤：避免高相关货币对重复开仓")
        logger.info("")
        logger.info("✅ 高级交易策略:")
        logger.info("   - 对冲组合：降低方向性风险")
        logger.info("   - 套利组合：利用强弱对比")
        logger.info("   - 时间分散：不同时间点建立仓位")
        logger.info("")
        logger.info("✅ 智能风险管理:")
        logger.info("   - 动态仓位调整：根据组合类型调整仓位大小")
        logger.info("   - 风险等级分类：低/中/高风险组合")
        logger.info("   - 相关性控制：基于货币对相关性矩阵")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_combo_trading()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
    input("按任意键退出...")
