"""
测试增强后的市场变化分析模板

这个脚本测试增强后的市场变化分析模板的渲染，验证13日均线右侧交易策略的增强是否正确包含
"""
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append('.')

# 导入需要测试的模块
from app.utils import prompt_template_manager

def print_header(message):
    """打印带有分隔线的标题"""
    print("\n" + "=" * 80)
    print(f" {message} ".center(80, "="))
    print("=" * 80)

def test_enhanced_market_analyzer_template():
    """测试增强后的市场变化分析模板"""
    print_header("测试增强后的市场变化分析模板")
    
    # 准备模板数据
    template_data = {
        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'current_price': '1.1320',
        'time_since_last_analysis': '30',
        'trading_session': 'London',
        'last_analysis_time': (datetime.now().replace(hour=datetime.now().hour-1)).strftime("%Y-%m-%d %H:%M:%S"),
        'last_analysis_conclusion': '上升趋势，价格回踩13日均线',
        'last_trade_instruction': 'BUY LIMIT 1.1305',
        'last_entry_price': '1.1305',
        'last_stop_loss': '1.1290',
        'last_take_profit': '1.1335',
        'price_change_short_term': '0.05%',
        'price_change_medium_term': '0.1%',
        'price_volatility': '0.15%',
        'rsi_value': '55',
        'ma13_15min_direction': 'UP',
        'ma13_1hour_direction': 'UP',
        'price_to_ma13_15min': '+10 pips',
        'price_to_ma13_1hour': '+15 pips',
        'ma13_15min_slope': '0.3 pips/15min',
        'ma13_1hour_slope': '0.5 pips/hour',
        'active_positions': '无持仓',
        'pending_orders': 'BUYLIMIT 1.1305',
        'focus_points': '价格接近13日均线，RSI中性',
        'news_events': '无重要新闻'
    }
    
    # 渲染模板
    try:
        rendered_template = prompt_template_manager.render_template('market_change_analyzer_template', template_data)
        
        # 检查增强的13日均线右侧交易策略是否包含在渲染结果中
        distance_threshold_present = "距离均线15点以内" in rendered_template
        approach_condition_present = "距离均线20-30点且正在接近" in rendered_template
        retracement_criteria_present = "距离小于30点" in rendered_template
        lead_time_present = "均线提前量计算指导" in rendered_template
        timeframe_resonance_present = "多时间框架共振判断" in rendered_template
        
        # 检查JSON响应格式是否包含新增字段
        slope_value_present = "slopeValue" in rendered_template
        approaching_speed_present = "approachingSpeed" in rendered_template
        ma_projection_present = "maProjection" in rendered_template
        timeframe_resonance_field_present = "timeframeResonance" in rendered_template
        ideal_entry_zone_present = "idealEntryZone" in rendered_template
        
        # 打印结果
        print(f"距离阈值标准存在: {distance_threshold_present}")
        print(f"接近条件存在: {approach_condition_present}")
        print(f"回踩标准存在: {retracement_criteria_present}")
        print(f"均线提前量指导存在: {lead_time_present}")
        print(f"多时间框架共振存在: {timeframe_resonance_present}")
        print(f"斜率值字段存在: {slope_value_present}")
        print(f"接近速度字段存在: {approaching_speed_present}")
        print(f"均线投影字段存在: {ma_projection_present}")
        print(f"时间框架共振字段存在: {timeframe_resonance_field_present}")
        print(f"理想入场区域字段存在: {ideal_entry_zone_present}")
        
        # 验证结果
        assert distance_threshold_present, "距离阈值标准应该存在于渲染结果中"
        assert approach_condition_present, "接近条件应该存在于渲染结果中"
        assert retracement_criteria_present, "回踩标准应该存在于渲染结果中"
        assert lead_time_present, "均线提前量指导应该存在于渲染结果中"
        assert timeframe_resonance_present, "多时间框架共振应该存在于渲染结果中"
        assert slope_value_present, "斜率值字段应该存在于渲染结果中"
        assert approaching_speed_present, "接近速度字段应该存在于渲染结果中"
        assert ma_projection_present, "均线投影字段应该存在于渲染结果中"
        assert timeframe_resonance_field_present, "时间框架共振字段应该存在于渲染结果中"
        assert ideal_entry_zone_present, "理想入场区域字段应该存在于渲染结果中"
        
        # 打印渲染结果的前500个字符
        print("\n渲染结果预览:")
        print(rendered_template[:500] + "...")
        
        print("\n测试通过: 增强后的市场变化分析模板包含正确的13日均线右侧交易策略增强")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试开始')
    test_enhanced_market_analyzer_template()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试结束')
