#!/usr/bin/env python3
"""
手动创建趋势分类模型
"""

import pandas as pd
import numpy as np
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import joblib

def create_trend_model():
    """创建趋势分类模型"""
    print("创建趋势分类模型...")
    
    # 生成示例数据
    np.random.seed(42)
    n_samples = 1000
    
    # 创建特征
    X = np.random.randn(n_samples, 5)
    
    # 创建目标变量（确保有多个类别）
    y = np.random.choice([0, 1, 2], size=n_samples, p=[0.4, 0.3, 0.3])
    
    # 训练模型
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X, y)
    
    # 创建缩放器
    scaler = StandardScaler()
    scaler.fit(X)
    
    # 保存模型
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_filename = f"data/models/trend_classification_5min_random_forest_{timestamp}.pkl"
    scaler_filename = f"data/models/trend_classification_5min_random_forest_scaler_{timestamp}.pkl"
    
    joblib.dump(model, model_filename)
    joblib.dump(scaler, scaler_filename)
    
    print(f"已保存: {model_filename}")
    print(f"已保存: {scaler_filename}")
    
    return True

if __name__ == "__main__":
    create_trend_model()
