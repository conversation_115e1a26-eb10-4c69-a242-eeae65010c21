"""
自动交易系统测试脚本
用于测试自动交易系统的完整流程
"""
import os
import sys
import time
from datetime import datetime

from app.utils.forex_scheduled_tasks import start_hourly_forex_analysis, stop_all_tasks
from app.utils.mt4_client import mt4_client

def test_auto_trading_system():
    """测试自动交易系统"""
    try:
        print('=' * 50)
        print(f'开始测试自动交易系统，时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        print('=' * 50)
        
        # 步骤1: 确保MT4连接
        print('\n步骤1: 确保MT4连接')
        if not mt4_client.is_connected:
            print('MT4客户端未连接，尝试连接')
            connected = mt4_client.connect()
            if not connected:
                print('无法连接到MT4客户端，测试失败')
                return
        print('MT4连接正常')
        
        # 步骤2: 获取当前持仓和挂单
        print('\n步骤2: 获取当前持仓和挂单')
        positions_response = mt4_client.get_active_orders()
        positions = positions_response.get('orders', [])
        print(f'当前持仓数量: {len(positions)}')
        for position in positions:
            print(f'  - 持仓: 订单ID: {position.get("order_id")}, 类型: {position.get("type")}, 货币对: {position.get("symbol")}, 手数: {position.get("lots")}, 盈亏: {position.get("profit")}')
        
        pending_orders_response = mt4_client.get_pending_orders()
        pending_orders = pending_orders_response.get('orders', [])
        print(f'当前挂单数量: {len(pending_orders)}')
        for order in pending_orders:
            print(f'  - 挂单: 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 货币对: {order.get("symbol")}, 手数: {order.get("lots")}, 价格: {order.get("open_price")}, 止损: {order.get("sl")}, 止盈: {order.get("tp")}')
        
        # 步骤3: 启动定时任务，立即执行一次，并启用自动交易
        print('\n步骤3: 启动定时任务，立即执行一次，并启用自动交易')
        start_hourly_forex_analysis(run_immediately=True, auto_trade=True)
        
        # 步骤4: 等待任务执行完成
        print('\n步骤4: 等待任务执行完成')
        print('等待120秒，让任务执行完成')
        for i in range(120):
            time.sleep(1)
            if i % 10 == 0:
                print(f'已等待 {i} 秒')
        
        # 步骤5: 再次获取持仓和挂单
        print('\n步骤5: 再次获取持仓和挂单')
        new_positions_response = mt4_client.get_active_orders()
        new_positions = new_positions_response.get('orders', [])
        print(f'新的持仓数量: {len(new_positions)}')
        for position in new_positions:
            print(f'  - 持仓: 订单ID: {position.get("order_id")}, 类型: {position.get("type")}, 货币对: {position.get("symbol")}, 手数: {position.get("lots")}, 盈亏: {position.get("profit")}')
        
        new_pending_orders_response = mt4_client.get_pending_orders()
        new_pending_orders = new_pending_orders_response.get('orders', [])
        print(f'新的挂单数量: {len(new_pending_orders)}')
        for order in new_pending_orders:
            print(f'  - 挂单: 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 货币对: {order.get("symbol")}, 手数: {order.get("lots")}, 价格: {order.get("open_price")}, 止损: {order.get("sl")}, 止盈: {order.get("tp")}')
        
        # 步骤6: 检查是否有新增持仓或挂单
        print('\n步骤6: 检查是否有新增持仓或挂单')
        if len(new_positions) > len(positions):
            print('持仓数量增加，自动交易成功')
            
            # 显示新增的持仓
            for position in new_positions:
                if not any(p.get('order_id') == position.get('order_id') for p in positions):
                    print(f'  - 新增持仓: 订单ID: {position.get("order_id")}, 类型: {position.get("type")}, 货币对: {position.get("symbol")}, 手数: {position.get("lots")}, 开仓价: {position.get("open_price")}, 止损: {position.get("sl")}, 止盈: {position.get("tp")}')
        elif len(new_pending_orders) > len(pending_orders):
            print('挂单数量增加，自动交易成功')
            
            # 显示新增的挂单
            for order in new_pending_orders:
                if not any(p.get('order_id') == order.get('order_id') for p in pending_orders):
                    print(f'  - 新增挂单: 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 货币对: {order.get("symbol")}, 手数: {order.get("lots")}, 价格: {order.get("open_price")}, 止损: {order.get("sl")}, 止盈: {order.get("tp")}')
        else:
            print('持仓和挂单数量未增加，可能交易指令为观望或交易未成功执行')
        
        # 步骤7: 停止所有任务
        print('\n步骤7: 停止所有任务')
        stop_all_tasks()
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_auto_trading_system()
