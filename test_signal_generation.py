#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试信号生成脚本 - 使用模拟数据
仅用于测试，不修改原代码
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'QuantumForex_Pro'))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def generate_mock_market_data():
    """生成模拟市场数据"""
    symbols = ['EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCHF', 'USDCAD', 'USDJPY']
    market_data = {}
    
    for symbol in symbols:
        # 生成500个数据点的OHLCV数据
        dates = pd.date_range(end=datetime.now(), periods=500, freq='5min')
        
        # 基础价格
        if symbol == 'USDJPY':
            base_price = 144.0
            price_range = 2.0
        elif symbol == 'EURUSD':
            base_price = 1.136
            price_range = 0.02
        elif symbol == 'GBPUSD':
            base_price = 1.348
            price_range = 0.02
        elif symbol == 'AUDUSD':
            base_price = 0.644
            price_range = 0.01
        elif symbol == 'NZDUSD':
            base_price = 0.598
            price_range = 0.01
        elif symbol == 'USDCHF':
            base_price = 0.823
            price_range = 0.01
        else:  # USDCAD
            base_price = 1.381
            price_range = 0.02
        
        # 生成趋势数据（模拟强趋势以产生信号）
        trend = np.linspace(0, price_range * 0.8, 500)  # 上升趋势
        noise = np.random.normal(0, price_range * 0.1, 500)  # 噪音
        
        close_prices = base_price + trend + noise
        
        # 生成OHLC
        high_prices = close_prices + np.random.uniform(0, price_range * 0.05, 500)
        low_prices = close_prices - np.random.uniform(0, price_range * 0.05, 500)
        open_prices = np.roll(close_prices, 1)
        open_prices[0] = close_prices[0]
        
        # 生成成交量
        volumes = np.random.uniform(1000, 5000, 500)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'timestamp': dates,
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'volume': volumes
        })
        
        market_data[symbol] = {
            'ohlcv': df,
            'current_price': close_prices[-1],
            'spread': 0.00002,
            'last_update': datetime.now()
        }
    
    return market_data

def generate_mock_positions():
    """生成模拟持仓数据"""
    return {}  # 空持仓，测试新信号生成

def generate_mock_risk_assessment():
    """生成模拟风险评估"""
    return {
        'risk_level': 'LOW',
        'position_multiplier': 1.0,
        'max_risk_per_trade': 0.02,
        'allow_trading': True
    }

def test_signal_generation():
    """测试信号生成"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🧪 开始测试信号生成...")
    
    try:
        # 导入策略类
        from strategies.professional_portfolio_strategy import ProfessionalPortfolioStrategy
        
        # 创建策略实例
        strategy = ProfessionalPortfolioStrategy()
        
        # 生成模拟数据
        logger.info("📊 生成模拟市场数据...")
        market_data = generate_mock_market_data()
        
        logger.info("📋 生成模拟持仓数据...")
        current_positions = generate_mock_positions()
        
        logger.info("🛡️ 生成模拟风险评估...")
        risk_assessment = generate_mock_risk_assessment()
        
        # 测试策略决策生成
        logger.info("🧠 测试策略决策生成...")
        decisions = strategy.generate_portfolio_decisions(
            market_data, 
            current_positions, 
            risk_assessment
        )
        
        # 输出结果
        logger.info("✅ 测试完成！")
        logger.info(f"📊 生成决策数量: {len(decisions)}")
        
        if decisions:
            logger.info("🎯 生成的交易决策:")
            for i, decision in enumerate(decisions, 1):
                logger.info(f"  决策 {i}:")
                logger.info(f"    货币对: {decision.symbol}")
                logger.info(f"    动作: {decision.action.value}")
                logger.info(f"    仓位大小: {decision.size:.4f}")
                logger.info(f"    入场价格: {decision.entry_price:.5f}")
                logger.info(f"    止损: {decision.stop_loss:.5f}")
                logger.info(f"    止盈: {decision.take_profit:.5f}")
                logger.info(f"    置信度: {decision.confidence:.2%}")
                logger.info(f"    风险回报比: {decision.risk_reward_ratio:.2f}")
                logger.info(f"    理由: {decision.reasoning}")
                logger.info("")
        else:
            logger.info("⚠️ 未生成任何交易决策")
            logger.info("💡 可能原因:")
            logger.info("  - 市场信号强度不足")
            logger.info("  - 置信度低于阈值")
            logger.info("  - 风险控制限制")
        
        return len(decisions) > 0
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_signal_generation()
    print(f"\n🎯 测试结果: {'成功生成信号' if success else '未生成信号'}")
    input("按任意键退出...")
