"""
测试外汇交易功能
"""
from app.services.forex_trading_service import execute_trade
from app.utils.forex_analysis_history import get_latest_analysis

if __name__ == "__main__":
    print("开始执行外汇交易测试...")
    
    # 获取最新分析结果
    analysis = get_latest_analysis()
    if not analysis:
        print("未找到最新分析结果")
        exit(1)
    
    # 获取交易指令
    trade_instructions = analysis.get('tradeInstructions', {})
    print("交易指令:", trade_instructions)
    print("是否最终决策:", trade_instructions.get('isFinalDecision', False))
    
    # 执行交易
    result = execute_trade(trade_instructions, check_duplicate=True)
    print("交易执行结果:", result)
