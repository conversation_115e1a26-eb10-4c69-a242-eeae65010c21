"""
测试系统功能
"""
import os
import sys
import time
from datetime import datetime

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
try:
    from app.utils.mt4_client import mt4_client
    from app.utils.performance_evaluator import get_virtual_account
    from app.utils.order_result_analyzer import get_order_result_statistics
    from app.utils.performance_feedback import generate_performance_feedback
    from app.utils.forex_llm_prompt import generate_forex_analysis_prompt
    from app.services.forex_trading_service import execute_trade
    
    print("模块导入成功")
except Exception as e:
    print(f"模块导入失败: {e}")
    sys.exit(1)

def test_mt4_connection():
    """测试MT4连接"""
    print("\n=== 测试MT4连接 ===")
    try:
        connected = mt4_client.connect()
        print(f"MT4连接状态: {connected}")
        return connected
    except Exception as e:
        print(f"MT4连接失败: {e}")
        return False

def test_virtual_account():
    """测试虚拟账户"""
    print("\n=== 测试虚拟账户 ===")
    try:
        account = get_virtual_account()
        print(f"虚拟账户余额: {account['current_balance']}")
        print(f"开仓交易数量: {len(account['open_trades'])}")
        print(f"平仓交易数量: {len(account['closed_trades'])}")
        return True
    except Exception as e:
        print(f"获取虚拟账户失败: {e}")
        return False

def test_order_result_statistics():
    """测试订单结果统计"""
    print("\n=== 测试订单结果统计 ===")
    try:
        stats = get_order_result_statistics()
        print(f"总订单数: {stats['total_count']}")
        print(f"止盈触发率: {stats['take_profit_rate']:.1f}%")
        print(f"止损触发率: {stats['stop_loss_rate']:.1f}%")
        print(f"平均持仓时间: {stats['avg_duration_hours']:.1f}小时")
        return True
    except Exception as e:
        print(f"获取订单结果统计失败: {e}")
        return False

def test_performance_feedback():
    """测试绩效反馈"""
    print("\n=== 测试绩效反馈 ===")
    try:
        feedback = generate_performance_feedback()
        print(f"绩效反馈长度: {len(feedback)}")
        print(f"绩效反馈前100个字符: {feedback[:100]}")
        return True
    except Exception as e:
        print(f"生成绩效反馈失败: {e}")
        return False

def test_forex_llm_prompt():
    """测试外汇LLM提示词"""
    print("\n=== 测试外汇LLM提示词 ===")
    try:
        prompt = generate_forex_analysis_prompt({})
        print(f"提示词长度: {len(prompt)}")
        print(f"提示词前100个字符: {prompt[:100]}")
        return True
    except Exception as e:
        print(f"生成外汇LLM提示词失败: {e}")
        return False

def test_trade_execution():
    """测试交易执行"""
    print("\n=== 测试交易执行 ===")
    try:
        # 创建一个观望的交易指令
        trade_instructions = {
            "action": "NONE",
            "reasoning": "测试交易执行"
        }
        
        # 执行交易
        result = execute_trade(trade_instructions)
        print(f"交易执行结果: {result}")
        return True
    except Exception as e:
        print(f"交易执行失败: {e}")
        return False

def run_tests():
    """运行所有测试"""
    print("=== 开始测试 ===")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试MT4连接
    mt4_connected = test_mt4_connection()
    
    # 测试虚拟账户
    virtual_account_ok = test_virtual_account()
    
    # 测试订单结果统计
    order_stats_ok = test_order_result_statistics()
    
    # 测试绩效反馈
    performance_feedback_ok = test_performance_feedback()
    
    # 测试外汇LLM提示词
    forex_llm_prompt_ok = test_forex_llm_prompt()
    
    # 如果MT4已连接，测试交易执行
    trade_execution_ok = False
    if mt4_connected:
        trade_execution_ok = test_trade_execution()
    else:
        print("MT4未连接，跳过交易执行测试")
    
    # 打印测试结果摘要
    print("\n=== 测试结果摘要 ===")
    print(f"MT4连接: {'✅' if mt4_connected else '❌'}")
    print(f"虚拟账户: {'✅' if virtual_account_ok else '❌'}")
    print(f"订单结果统计: {'✅' if order_stats_ok else '❌'}")
    print(f"绩效反馈: {'✅' if performance_feedback_ok else '❌'}")
    print(f"外汇LLM提示词: {'✅' if forex_llm_prompt_ok else '❌'}")
    print(f"交易执行: {'✅' if trade_execution_ok else '❌' if mt4_connected else '⏭️ (跳过)'}")
    
    print("\n=== 测试完成 ===")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    run_tests()
