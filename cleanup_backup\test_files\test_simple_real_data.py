#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的真实数据测试
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_real_data():
    """简单的真实数据测试"""
    print("📊 简单真实数据测试")
    print("=" * 50)
    
    try:
        # 1. 直接测试数据库连接
        print("🔌 测试数据库连接...")
        from app.utils.db_client import get_connection, get_eurusd_min_data
        
        # 测试连接
        connection = get_connection()
        print("   ✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) as count FROM min_quote_eurusd")
            result = cursor.fetchone()
            print(f"   EURUSD数据量: {result['count']:,} 条")
        
        connection.close()
        
        # 2. 测试获取EURUSD数据
        print("\n💱 测试获取EURUSD数据...")
        
        # 获取最近50条数据
        data = get_eurusd_min_data(limit=50)
        
        if data:
            print(f"   ✅ 成功获取 {len(data)} 条数据")
            
            # 显示最新的5条数据
            print("   最新5条数据:")
            for i, record in enumerate(data[:5]):
                print(f"     {i+1}. {record['time']}: {record['close']:.5f}")
            
            # 计算简单技术指标
            print("\n📊 计算简单技术指标...")
            
            prices = [float(d['close']) for d in data]
            
            # 当前价格
            current_price = prices[0]  # 最新价格
            print(f"   当前价格: {current_price:.5f}")
            
            # 简单移动平均
            if len(prices) >= 20:
                ma_20 = sum(prices[:20]) / 20
                print(f"   MA20: {ma_20:.5f}")
                
                if current_price > ma_20:
                    print("   趋势: 价格在MA20之上 (看涨)")
                else:
                    print("   趋势: 价格在MA20之下 (看跌)")
            
            # 价格变化
            if len(prices) >= 2:
                price_change = prices[0] - prices[1]
                price_change_pct = (price_change / prices[1]) * 100
                print(f"   最新变化: {price_change:+.5f} ({price_change_pct:+.3f}%)")
            
            # 波动率
            if len(prices) >= 10:
                import numpy as np
                volatility = np.std(prices[:10])
                print(f"   近10期波动率: {volatility:.5f}")
            
            return True
        else:
            print("   ❌ 未能获取数据")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_adapter():
    """测试数据适配器"""
    print("\n🔧 测试数据适配器...")
    
    try:
        from app.core.data_source_adapter import DataSourceAdapter
        
        adapter = DataSourceAdapter()
        print("   ✅ 数据适配器初始化成功")
        
        # 测试连接
        connection_status = adapter.test_connection()
        print(f"   数据库连接: {'✅' if connection_status['database'] else '❌'}")
        print(f"   MT4连接: {'✅' if connection_status['mt4'] else '❌'}")
        
        # 测试获取当前价格
        print("\n💰 测试获取当前价格...")
        try:
            current_prices = adapter.get_current_prices()
            if current_prices:
                print(f"   ✅ 成功获取{len(current_prices)}个品种的价格:")
                for symbol, price in current_prices.items():
                    print(f"     {symbol}: {price:.5f}")
            else:
                print("   ⚠️ 未获取到价格数据")
        except Exception as e:
            print(f"   ❌ 获取价格失败: {e}")
        
        # 测试获取历史数据
        print("\n📈 测试获取历史数据...")
        try:
            historical_data = adapter.get_historical_data('EURUSD', 1, 20)  # 1分钟，20条
            if historical_data:
                print(f"   ✅ 成功获取{len(historical_data)}条历史数据")
                if historical_data:
                    latest = historical_data[-1]
                    print(f"   最新数据: {latest['timestamp']} - 收盘价: {latest['close']:.5f}")
            else:
                print("   ⚠️ 未获取到历史数据")
        except Exception as e:
            print(f"   ❌ 获取历史数据失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 数据适配器测试失败: {e}")
        return False

def test_basic_technical_analysis():
    """测试基础技术分析"""
    print("\n📊 测试基础技术分析...")
    
    try:
        from app.utils.db_client import get_eurusd_min_data
        import numpy as np
        
        # 获取数据
        data = get_eurusd_min_data(limit=100)
        
        if not data:
            print("   ❌ 无法获取数据")
            return False
        
        # 提取价格数据
        prices = [float(d['close']) for d in data]
        highs = [float(d['high']) for d in data]
        lows = [float(d['low']) for d in data]
        
        print(f"   ✅ 获取{len(prices)}条价格数据")
        
        # 计算技术指标
        indicators = {}
        
        # 移动平均线
        if len(prices) >= 20:
            indicators['ma_5'] = np.mean(prices[:5])
            indicators['ma_10'] = np.mean(prices[:10])
            indicators['ma_20'] = np.mean(prices[:20])
        
        # 简单RSI
        if len(prices) >= 14:
            deltas = np.diff(prices[:15])  # 取15个价格计算14个变化
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            avg_gain = np.mean(gains)
            avg_loss = np.mean(losses)
            
            if avg_loss > 0:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
                indicators['rsi'] = rsi
        
        # 布林带
        if len(prices) >= 20:
            ma_20 = np.mean(prices[:20])
            std_20 = np.std(prices[:20])
            indicators['bb_upper'] = ma_20 + (2 * std_20)
            indicators['bb_lower'] = ma_20 - (2 * std_20)
            indicators['bb_position'] = (prices[0] - indicators['bb_lower']) / (indicators['bb_upper'] - indicators['bb_lower'])
        
        # 显示结果
        print("   技术指标计算结果:")
        for name, value in indicators.items():
            if isinstance(value, float):
                print(f"     {name}: {value:.5f}")
        
        # 简单信号判断
        signals = []
        
        if 'ma_5' in indicators and 'ma_20' in indicators:
            if indicators['ma_5'] > indicators['ma_20']:
                signals.append("短期均线上穿长期均线 (看涨)")
            else:
                signals.append("短期均线下穿长期均线 (看跌)")
        
        if 'rsi' in indicators:
            if indicators['rsi'] > 70:
                signals.append("RSI超买 (看跌)")
            elif indicators['rsi'] < 30:
                signals.append("RSI超卖 (看涨)")
            else:
                signals.append(f"RSI正常 ({indicators['rsi']:.1f})")
        
        if 'bb_position' in indicators:
            if indicators['bb_position'] > 0.8:
                signals.append("价格接近布林带上轨 (看跌)")
            elif indicators['bb_position'] < 0.2:
                signals.append("价格接近布林带下轨 (看涨)")
            else:
                signals.append("价格在布林带中间区域")
        
        print("   交易信号:")
        for signal in signals:
            print(f"     • {signal}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 技术分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始简单真实数据测试")
    
    # 执行测试
    success1 = test_simple_real_data()
    success2 = test_data_adapter()
    success3 = test_basic_technical_analysis()
    
    if success1 and success2 and success3:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🎉 简单真实数据测试完成！")
        print("✅ 数据库连接正常")
        print("✅ 数据获取正常") 
        print("✅ 基础技术分析正常")
        print("✅ 系统可以基于真实数据进行分析")
    else:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ 部分测试失败")
