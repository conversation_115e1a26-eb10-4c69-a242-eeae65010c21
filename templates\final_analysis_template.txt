# 外汇交易最终决策

## JSON格式要求
**必须使用```json代码块输出交易指令，严格遵循以下结构：**

```json
{
  "action": "BUY/SELL/NONE",
  "orderType": "MARKET/LIMIT/STOP",
  "entryPrice": 数值或null,
  "stopLoss": 数值,
  "takeProfit": 数值,
  "lotSize": 0.01-0.2,
  "riskLevel": "LOW/MEDIUM/HIGH",
  "reasoning": "交易理由",
  "ma13Strategy": {
    "timeframe15min": "UP/DOWN/FLAT",
    "timeframe1hour": "UP/DOWN/FLAT",
    "priceToMA": "ABOVE/BELOW/NEAR",
    "retracement": true/false
  },
  "signalConfidence": "HIGH/MEDIUM/LOW",
  "orderManagement": []
}
```

## 综合技术分析策略
**核心原则**：基于多重技术指标确认，寻找最优入场时机

## 基本信息
- 货币对: ${symbol}
- 当前价格: ${current_price}
- 分析时间: ${analysis_time}

## 分析要点
${initial_insights}
${detail_insights}

## 技术指标
- MA20: ${ma20}
- MA50: ${ma50}
- RSI: ${rsi}
- MACD: ${macd_line}/${signal_line}
- 布林带: ${bb_upper}/${bb_lower}
- ATR: ${atr}

## 市场信息
### 新闻: ${news_data}
### 日历: ${calendar_data}
### 持仓: ${positions_data}
### 挂单: ${pending_orders_data}
### 历史: ${history_analysis}

## 分析要求

**当前价格: ${current_price}**

### 综合技术分析
1. **趋势判断**: 基于多重技术指标确定主趋势方向
2. **价格行为**: 关键支撑阻力位和价格形态分析
3. **入场机会**: 技术指标共振的最佳入场时机
4. **交易计划**: 方向、入场、止损、止盈的完整方案

**必须提供JSON格式交易指令**

### 风险管理
- 仓位: 0.01-0.2，风险越大仓位越小
- 止损: 基于技术位置和ATR设置合理止损
- 止盈: 基于支撑阻力位，风险回报比至少1:1

### 订单管理
${orders_status}

**当前价格: ${current_price}**

```json
{
  "action": "BUY/SELL/NONE",
  "orderType": "MARKET/LIMIT/STOP",
  "entryPrice": 数值或null,
  "stopLoss": 数值,
  "takeProfit": 数值,
  "lotSize": 0.01-0.2,
  "riskLevel": "LOW/MEDIUM/HIGH",
  "reasoning": "基于综合技术分析的详细理由",
  "technicalAnalysis": {
    "trend": "BULLISH/BEARISH/NEUTRAL",
    "momentum": "STRONG/WEAK/NEUTRAL",
    "support": 数值,
    "resistance": 数值
  },
  "signalConfidence": "HIGH/MEDIUM/LOW",
  "orderManagement": []
}
```

## 检查要点
1. **止损止盈方向**: 买入止损<当前价<止盈，卖出止盈<当前价<止损
2. **风险回报比**: 至少1:1
3. **仓位大小**: 风险越大仓位越小(0.01-0.05)，风险越小仓位可大(0.1-0.2)
4. **JSON格式**: 必须使用```json，不要添加注释
