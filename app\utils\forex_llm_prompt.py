"""
外汇LLM提示词生成工具
用于生成发送给大模型的提示词
"""
from datetime import datetime
from app.utils.error_logger import get_recent_errors, get_recent_operations, ErrorType, OperationType
from app.utils.performance_feedback import format_performance_data_for_prompt, get_performance_reward_message


def format_error_records(limit=5):
    """
    格式化错误记录

    Args:
        limit (int): 限制条数

    Returns:
        str: 格式化后的错误记录
    """
    # 获取最近的错误记录
    errors = get_recent_errors(limit)

    if not errors:
        return "无最近错误记录"

    result = []
    for error in errors:
        timestamp = error.get('timestamp', '')
        if timestamp:
            try:
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass

        error_type = error.get('error_type', 'UNKNOWN_ERROR')
        message = error.get('message', '未知错误')
        operation = error.get('operation', 'OTHER')
        order_id = error.get('order_id', '')

        error_line = f"- {timestamp} [{error_type}] {message}"
        if order_id:
            error_line += f" (订单ID: {order_id})"
        if operation != 'OTHER':
            error_line += f" (操作: {operation})"

        result.append(error_line)

    return '\n'.join(result)


def format_operation_records(limit=5, success=None):
    """
    格式化操作记录

    Args:
        limit (int): 限制条数
        success (bool, optional): 是否成功

    Returns:
        str: 格式化后的操作记录
    """
    # 获取最近的操作记录
    operations = get_recent_operations(limit, success=success)

    if not operations:
        return "无最近操作记录"

    result = []
    for operation in operations:
        timestamp = operation.get('timestamp', '')
        if timestamp:
            try:
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass

        operation_type = operation.get('operation_type', 'OTHER')
        message = operation.get('message', '')
        success = operation.get('success', False)
        order_id = operation.get('order_id', '')

        status = "成功" if success else "失败"
        operation_line = f"- {timestamp} [{operation_type}] [{status}] {message}"
        if order_id:
            operation_line += f" (订单ID: {order_id})"

        result.append(operation_line)

    return '\n'.join(result)


def generate_forex_analysis_prompt(data):
    """
    生成外汇分析提示词

    Args:
        data (dict): 分析数据

    Returns:
        str: 提示词
    """
    # 导入模板管理器
    from app.utils import prompt_template_manager

    symbol = data.get('symbol', 'EURUSD')
    timeframe15m = data.get('timeframe15m', [])
    timeframe1h = data.get('timeframe1h', [])
    indicators = data.get('indicators', {})
    news = data.get('news', [])
    calendar = data.get('calendar', [])
    positions = data.get('positions', [])
    pending_orders = data.get('pendingOrders', [])
    history_analysis = data.get('historyAnalysis', [])

    # 生成历史分析部分
    history_prompt = format_history_analysis(history_analysis)

    # 生成错误记录部分
    error_records = format_error_records(5)

    # 生成操作记录部分
    failed_operations = format_operation_records(5, success=False)

    # 生成绩效反馈部分
    performance_data = format_performance_data_for_prompt()

    # 生成绩效奖励信息
    performance_reward = get_performance_reward_message()

    # 获取订单结果统计
    from app.utils.order_result_analyzer import get_order_result_statistics, OrderResultType
    order_stats = get_order_result_statistics()

    # 准备模板数据
    current_price = data.get('currentPrice', '未知')

    # 计算限价单和止损止盈的建议范围
    try:
        current_price_float = float(current_price)

        # 买入限价单范围
        buy_limit_low = f"{current_price_float-0.0015:.4f}"
        buy_limit_high = f"{current_price_float-0.0005:.4f}"

        # 卖出限价单范围
        sell_limit_low = f"{current_price_float+0.0005:.4f}"
        sell_limit_high = f"{current_price_float+0.0015:.4f}"

        # 买入止损范围
        buy_sl_low = f"{current_price_float-0.0040:.4f}"
        buy_sl_high = f"{current_price_float-0.0025:.4f}"

        # 买入止盈范围
        buy_tp_low = f"{current_price_float+0.0020:.4f}"
        buy_tp_high = f"{current_price_float+0.0060:.4f}"

        # 卖出止损范围
        sell_sl_low = f"{current_price_float+0.0025:.4f}"
        sell_sl_high = f"{current_price_float+0.0040:.4f}"

        # 卖出止盈范围
        sell_tp_low = f"{current_price_float-0.0060:.4f}"
        sell_tp_high = f"{current_price_float-0.0020:.4f}"
    except (ValueError, TypeError):
        # 如果价格不是有效的浮点数，使用默认值
        buy_limit_low = "1.0835"
        buy_limit_high = "1.0845"
        sell_limit_low = "1.0855"
        sell_limit_high = "1.0865"
        buy_sl_low = "1.0810"
        buy_sl_high = "1.0825"
        buy_tp_low = "1.0870"
        buy_tp_high = "1.0910"
        sell_sl_low = "1.0875"
        sell_sl_high = "1.0890"
        sell_tp_low = "1.0790"
        sell_tp_high = "1.0830"

    # 获取15分钟和1小时周期的13日均线数据
    ma13_15min_data = {}
    ma13_1h_data = {}

    # 从15分钟数据中提取13日均线信息
    if '15min' in indicators and 'ma' in indicators['15min']:
        ma_data = indicators['15min'].get('ma', {})
        ma13_15min_data = {
            'value': ma_data.get(13, 'N/A'),
            'direction': ma_data.get('13_direction', 'UNKNOWN'),
            'slope': ma_data.get('13_slope', 0),
            'distance': ma_data.get('13_distance', 0),
            'position': ma_data.get('13_price_position', 'UNKNOWN'),
            'projection': ma_data.get('13_projection_2h', 'N/A')
        }

    # 从1小时数据中提取13日均线信息
    if '1h' in indicators and 'ma' in indicators['1h']:
        ma_data = indicators['1h'].get('ma', {})
        ma13_1h_data = {
            'value': ma_data.get(13, 'N/A'),
            'direction': ma_data.get('13_direction', 'UNKNOWN'),
            'slope': ma_data.get('13_slope', 0),
            'distance': ma_data.get('13_distance', 0),
            'position': ma_data.get('13_price_position', 'UNKNOWN'),
            'projection': ma_data.get('13_projection_2h', 'N/A')
        }

    # 准备模板数据
    template_data = {
        'symbol': symbol,
        'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'current_price': current_price,
        'timeframe15m_data': format_kline_data(timeframe15m[-30:] if len(timeframe15m) > 30 else timeframe15m),
        'timeframe1h_data': format_kline_data(timeframe1h[-30:] if len(timeframe1h) > 30 else timeframe1h),
        'ma5': indicators.get('ma', {}).get(5, 'N/A'),
        'ma10': indicators.get('ma', {}).get(10, 'N/A'),
        'ma13': indicators.get('ma', {}).get(13, 'N/A'),  # 添加13日均线
        'ma20': indicators.get('ma', {}).get(20, 'N/A'),
        'ma50': indicators.get('ma', {}).get(50, 'N/A'),
        'rsi': indicators.get('rsi', 'N/A'),
        'macd_line': indicators.get('macd', {}).get('macdLine', [-1])[-1] if indicators.get('macd') else 'N/A',
        # 添加13日均线详细数据
        'ma13_15min': ma13_15min_data,
        'ma13_1h': ma13_1h_data,
        'signal_line': indicators.get('macd', {}).get('signalLine', [-1])[-1] if indicators.get('macd') else 'N/A',
        'histogram': indicators.get('macd', {}).get('histogram', [-1])[-1] if indicators.get('macd') else 'N/A',
        'bollinger_upper': indicators.get('bollinger', {}).get('upper', [-1])[-1] if indicators.get('bollinger') else 'N/A',
        'bollinger_middle': indicators.get('bollinger', {}).get('middle', [-1])[-1] if indicators.get('bollinger') else 'N/A',
        'bollinger_lower': indicators.get('bollinger', {}).get('lower', [-1])[-1] if indicators.get('bollinger') else 'N/A',
        'momentum': indicators.get('momentum') if indicators.get('momentum') is not None else 'N/A',
        'advanced_analysis_15m': indicators.get('advancedAnalysis', 'N/A'),
        'advanced_analysis_1h': data.get('indicators1h', {}).get('advancedAnalysis', 'N/A'),
        'news_data': format_news_data(news),
        'calendar_data': format_calendar_data(calendar),
        'positions_data': format_positions_data(positions),
        'pending_orders_data': format_pending_orders_data(pending_orders),
        'history_analysis': history_prompt,
        'error_records': error_records,
        'failed_operations': failed_operations,
        'performance_data': performance_data,
        'total_orders': order_stats.get('total_count', 0),
        'take_profit_rate': f"{order_stats.get('take_profit_rate', 0):.1f}",
        'stop_loss_rate': f"{order_stats.get('stop_loss_rate', 0):.1f}",
        'avg_duration_hours': f"{order_stats.get('avg_duration_hours', 0):.1f}",
        'avg_risk_reward_ratio': f"{order_stats.get('avg_risk_reward_ratio', 0):.2f}",
        'performance_reward': performance_reward,
        'buy_limit_low': buy_limit_low,
        'buy_limit_high': buy_limit_high,
        'sell_limit_low': sell_limit_low,
        'sell_limit_high': sell_limit_high,
        'buy_sl_low': buy_sl_low,
        'buy_sl_high': buy_sl_high,
        'buy_tp_low': buy_tp_low,
        'buy_tp_high': buy_tp_high,
        'sell_sl_low': sell_sl_low,
        'sell_sl_high': sell_sl_high,
        'sell_tp_low': sell_tp_low,
        'sell_tp_high': sell_tp_high
    }

    try:
        # 使用模板管理器渲染模板
        prompt = prompt_template_manager.render_template('forex_analysis_template', template_data)
    except Exception as e:
        print(f"使用模板渲染失败: {e}，回退到硬编码提示词")
        # 如果模板渲染失败，回退到硬编码提示词
        prompt = f"""
# 外汇短线交易分析请求

**重要提示：我们采用短线交易策略，需要当天能够成交的订单，入场价格必须非常接近当前市场价格（最多相差10-15个点），止损止盈范围合理，目标是每天执行5-10笔交易。远离当前价格的入场点将被拒绝执行！**

## 基本信息
- 货币对: {symbol}
- 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 当前价格: {current_price}
- 分析目的: 提供短线交易建议和市场分析
"""


    return prompt


def format_kline_data(klines):
    """
    格式化K线数据

    Args:
        klines (list): K线数据

    Returns:
        str: 格式化后的K线数据
    """
    if not klines:
        return "无数据"

    result = []
    for kline in klines:
        time_str = kline.get('time', '')
        if isinstance(time_str, str) and 'T' in time_str:
            time_str = time_str.replace('T', ' ').split('.')[0]

        # 只保留时间部分
        if ' ' in time_str:
            time_str = time_str.split(' ')[1]

        line = f"{time_str},{kline.get('open', 0)},{kline.get('high', 0)},{kline.get('low', 0)},{kline.get('close', 0)},{kline.get('volume', 0)}"
        result.append(line)

    return '\n'.join(result)


def format_news_data(news):
    """
    格式化新闻数据

    Args:
        news (list): 新闻数据

    Returns:
        str: 格式化后的新闻数据
    """
    if not news:
        return "- 无相关新闻"

    result = []
    for item in news:
        result.append(f"- {item.get('time', '')} [重要性: {item.get('importance', 'MEDIUM')}] {item.get('content', '')}")

    return '\n\n'.join(result)


def format_calendar_data(calendar):
    """
    格式化日历事件数据

    Args:
        calendar (list): 日历事件数据

    Returns:
        str: 格式化后的日历事件数据
    """
    if not calendar:
        return "- 无相关日历事件"

    result = []
    for item in calendar:
        result.append(f"- {item.get('time', '')} [{item.get('country', '')}] [重要性: {item.get('importance', 'MEDIUM')}] {item.get('title', '')}\n  实际值: {item.get('actual', '')}, 预期值: {item.get('forecast', '')}, 前值: {item.get('previous', '')}")

    return '\n'.join(result)


def format_positions_data(positions):
    """
    格式化持仓数据

    Args:
        positions (list): 持仓数据

    Returns:
        str: 格式化后的持仓数据
    """
    if not positions:
        return "当前无持仓"

    result = []
    for position in positions:
        result.append(f"- 订单号: {position.get('order_id', '')}, 类型: {position.get('type', '')}, 手数: {position.get('lots', '')}, 开仓价: {position.get('open_price', '')}, 止损: {position.get('sl', '')}, 止盈: {position.get('tp', '')}, 盈亏: {position.get('profit', '')}")

    return '\n'.join(result)


def format_pending_orders_data(pending_orders):
    """
    格式化挂单数据

    Args:
        pending_orders (list): 挂单数据

    Returns:
        str: 格式化后的挂单数据
    """
    if not pending_orders:
        return "当前无挂单"

    result = []
    for order in pending_orders:
        result.append(f"- 订单号: {order.get('order_id', '')}, 类型: {order.get('type', '')}, 手数: {order.get('lots', '')}, 价格: {order.get('open_price', '')}, 止损: {order.get('sl', '')}, 止盈: {order.get('tp', '')}")

    return '\n'.join(result)


def format_history_analysis(history_analysis):
    """
    格式化历史分析记录 - 增强版，提供更完整的历史信息

    Args:
        history_analysis (list): 历史分析记录

    Returns:
        str: 格式化后的历史分析记录
    """
    if not history_analysis:
        return "## 历史分析记录\n无历史分析记录"

    result = "## 历史分析记录\n\n**重要提示：请仔细分析历史记录，保持分析的连续性和一致性。除非市场出现重大变化，否则应避免频繁改变分析框架和交易策略。**\n\n"

    for i, record in enumerate(history_analysis):
        date = datetime.fromisoformat(record.get('timestamp', '').replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
        result += f"### {i + 1}. {date} 分析\n"
        result += f"- 价格: {record.get('currentPrice', 'N/A')}\n"

        # 添加交易方向和详细信息
        trade_instructions = record.get('tradeInstructions', {})
        if trade_instructions:
            action = trade_instructions.get('action', '')
            if action == 'BUY':
                direction = '买入'
                # 添加更多买入详情
                entry_price = trade_instructions.get('entryPrice')
                stop_loss = trade_instructions.get('stopLoss')
                take_profit = trade_instructions.get('takeProfit')
                lot_size = trade_instructions.get('lotSize')

                result += f"- 交易方向: {direction}\n"
                if entry_price:
                    result += f"- 入场价格: {entry_price}\n"
                if stop_loss:
                    result += f"- 止损价格: {stop_loss}\n"
                if take_profit:
                    result += f"- 止盈价格: {take_profit}\n"
                if lot_size:
                    result += f"- 仓位大小: {lot_size}\n"

            elif action == 'SELL':
                direction = '卖出'
                # 添加更多卖出详情
                entry_price = trade_instructions.get('entryPrice')
                stop_loss = trade_instructions.get('stopLoss')
                take_profit = trade_instructions.get('takeProfit')
                lot_size = trade_instructions.get('lotSize')

                result += f"- 交易方向: {direction}\n"
                if entry_price:
                    result += f"- 入场价格: {entry_price}\n"
                if stop_loss:
                    result += f"- 止损价格: {stop_loss}\n"
                if take_profit:
                    result += f"- 止盈价格: {take_profit}\n"
                if lot_size:
                    result += f"- 仓位大小: {lot_size}\n"
            else:
                direction = '观望'
                result += f"- 交易方向: {direction}\n"
        else:
            result += "- 交易方向: 未知\n"

        # 添加技术指标
        indicators = record.get('indicators', {})
        if indicators:
            ma5 = indicators.get('ma5', 0)
            ma20 = indicators.get('ma20', 0)
            rsi = indicators.get('rsi', 0)

            if ma5 and ma20 and rsi:
                result += f"- 技术指标: MA5={ma5:.5f}, MA20={ma20:.5f}, RSI={rsi:.2f}\n"

        # 添加订单管理指令
        if trade_instructions and 'orderManagement' in trade_instructions and trade_instructions['orderManagement']:
            result += "- 订单管理指令:\n"
            for order_action in trade_instructions['orderManagement']:
                action_type = order_action.get('action', '')
                order_id = order_action.get('orderId', '')
                reason = order_action.get('reason', '')

                if action_type == 'MODIFY':
                    new_sl = order_action.get('newStopLoss', '')
                    new_tp = order_action.get('newTakeProfit', '')
                    result += f"  * 修改订单 {order_id}: 新止损={new_sl}, 新止盈={new_tp}, 原因: {reason}\n"
                elif action_type == 'CLOSE':
                    result += f"  * 平仓订单 {order_id}, 原因: {reason}\n"
                elif action_type == 'DELETE':
                    result += f"  * 删除挂单 {order_id}, 原因: {reason}\n"

        # 添加预分析关注点
        pre_analysis_focus_points = record.get('preAnalysisFocusPoints', [])
        if pre_analysis_focus_points:
            result += "- 预分析关注点:\n"
            for point in pre_analysis_focus_points:
                indicator = point.get('indicator', '')
                current_value = point.get('currentValue', '')
                significance = point.get('significance', '')
                result += f"  * {indicator}: {current_value} - {significance}\n"

        # 添加主要结论和分析逻辑
        if trade_instructions and trade_instructions.get('reasoning'):
            result += f"- 主要结论: {trade_instructions.get('reasoning')}\n"

            # 添加连续性分析（如果有）
            if 'continuityAnalysis' in trade_instructions:
                continuity = trade_instructions.get('continuityAnalysis', {})
                if continuity:
                    result += "- 连续性分析:\n"
                    if 'previousAnalysisConsistency' in continuity:
                        result += f"  * 与前次分析一致性: {continuity.get('previousAnalysisConsistency', '')}\n"
                    if 'strategyChangeReason' in continuity:
                        result += f"  * 策略变更原因: {continuity.get('strategyChangeReason', '')}\n"
                    if 'marketChangeSignificance' in continuity:
                        result += f"  * 市场变化重要性: {continuity.get('marketChangeSignificance', '')}\n"

            result += "\n"
        elif record.get('analysis'):
            # 如果没有reasoning字段，尝试从分析文本中提取结论
            import re
            analysis = record.get('analysis', '')

            # 尝试提取结论部分
            conclusion_match = re.search(r'结论[：:](.*?)(?=\n\n|\n##|$)', analysis, re.IGNORECASE | re.DOTALL)
            if conclusion_match:
                conclusion = conclusion_match.group(1).strip()
            else:
                # 尝试提取交易建议部分
                recommendation_match = re.search(r'交易建议[：:](.*?)(?=\n\n|\n##|$)', analysis, re.IGNORECASE | re.DOTALL)
                if recommendation_match:
                    conclusion = recommendation_match.group(1).strip()
                else:
                    # 如果都没有找到，返回分析的前200个字符
                    conclusion = analysis[:200].replace('\n', ' ') + '...'

            result += f"- 主要结论: {conclusion}\n\n"
        else:
            result += "- 主要结论: 无\n\n"

        # 添加交易执行状态
        trade_executed = record.get('tradeExecuted', False)
        result += f"- 交易执行状态: {'已执行' if trade_executed else '未执行'}\n\n"

    # 添加历史分析总结
    result += "### 历史分析总结\n"
    result += "请基于以上历史分析记录，评估分析的连续性和一致性。如果当前分析与历史分析有显著差异，必须详细解释原因。\n"
    result += "除非市场出现重大变化，否则应保持分析框架和交易策略的一致性，避免频繁改变分析结论和交易决策。\n\n"

    return result
