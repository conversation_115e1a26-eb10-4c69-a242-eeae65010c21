2025-05-30 02:45:51,969 - __main__ - INFO - 🚀 启动 QuantumForex Pro 云端模型接收服务
2025-05-30 02:45:51,969 - __main__ - INFO - ============================================================
2025-05-30 02:45:51,969 - __main__ - INFO - 🌐 启动API服务器...
2025-05-30 02:45:51,969 - __main__ - INFO - 📍 监听地址: 0.0.0.0:8081
2025-05-30 02:45:51,970 - __main__ - INFO - 🔗 API端点:
2025-05-30 02:45:51,970 - __main__ - INFO -    - 健康检查: http://127.0.0.1:8081/api/health
2025-05-30 02:45:51,970 - __main__ - INFO -    - 模型状态: http://127.0.0.1:8081/api/models/status
2025-05-30 02:45:51,970 - __main__ - INFO -    - 模型上传: http://127.0.0.1:8081/api/models/upload
2025-05-30 02:45:51,971 - __main__ - INFO -    - 模型列表: http://127.0.0.1:8081/api/models/list
2025-05-30 02:45:51,971 - __main__ - INFO -    📊 数据导出端点:
2025-05-30 02:45:51,972 - __main__ - INFO -    - 交易记录: http://127.0.0.1:8081/api/trading/records
2025-05-30 02:45:51,972 - __main__ - INFO -    - 参数优化: http://127.0.0.1:8081/api/optimization/history
2025-05-30 02:45:51,972 - __main__ - INFO -    - LLM分析: http://127.0.0.1:8081/api/llm/analysis_history
2025-05-30 02:45:51,972 - utils.cloud_model_receiver - INFO - 🚀 启动云端模型接收服务器: 0.0.0.0:8081
2025-05-30 02:45:51,992 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://***************:8081
2025-05-30 02:45:51,992 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-30 02:46:19,810 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 02:46:19] "GET /api/health HTTP/1.1" 200 -
2025-05-30 02:46:43,917 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 02:46:43] "GET /api/health HTTP/1.1" 200 -
2025-05-30 02:47:13,942 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 02:47:13] "GET /api/health HTTP/1.1" 200 -
2025-05-30 02:47:43,968 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 02:47:43] "GET /api/health HTTP/1.1" 200 -
2025-05-30 02:48:13,974 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 02:48:13] "GET /api/health HTTP/1.1" 200 -
2025-05-30 02:48:43,984 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 02:48:43] "GET /api/health HTTP/1.1" 200 -
