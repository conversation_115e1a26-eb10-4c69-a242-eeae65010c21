"""
智能持仓管理引擎
结合算法规则和LLM分析的持仓管理系统
"""

import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class PositionAction(Enum):
    HOLD = "hold"
    CLOSE = "close"
    PARTIAL_CLOSE = "partial_close"
    MODIFY_SL = "modify_sl"
    MODIFY_TP = "modify_tp"
    TRAIL_STOP = "trail_stop"

@dataclass
class PositionAnalysis:
    order_id: str
    symbol: str
    action: PositionAction
    reason: str
    priority: int  # 1-5, 5最高优先级
    profit_loss: float
    risk_level: str
    recommendation: str

class PositionManager:
    def __init__(self, trade_executor=None):
        self.trade_executor = trade_executor
        self.config = {
            # 风险控制参数
            'max_loss_per_position': -8.0,  # 单个持仓最大亏损$8
            'max_total_loss': -40.0,  # 总亏损限制$40
            'profit_protection_threshold': 3.0,  # 盈利$3以上开始保护
            'trailing_stop_distance': 15,  # 追踪止损距离15点
            'max_position_age_hours': 48,  # 最大持仓时间48小时
            'risk_free_profit_ratio': 0.6,  # 盈利60%时移动止损到盈亏平衡

            # 持仓数量控制（防止过度开仓）
            'max_positions_per_symbol': 4,  # 每个货币对最多4个持仓
            'max_same_direction_per_symbol': 2,  # 同一方向最多2个持仓
            'max_total_positions': 20,  # 全局最多20个持仓
            'position_concentration_warning': 15,  # 持仓数量警告阈值
        }

    def analyze_all_positions(self) -> List[PositionAnalysis]:
        """分析所有持仓并生成处理建议"""
        try:
            print("🔍 开始分析所有持仓...")

            # 获取MT4真实持仓
            if not self.trade_executor:
                print("❌ 交易执行器未初始化")
                return []

            mt4_positions = self.trade_executor._get_mt4_real_positions()
            if not mt4_positions:
                print("📊 无持仓需要分析")
                return []

            print(f"📊 分析{len(mt4_positions)}个持仓...")

            analyses = []
            total_profit = sum(pos.get('profit', 0) for pos in mt4_positions)

            for position in mt4_positions:
                analysis = self._analyze_single_position(position, total_profit)
                if analysis:
                    analyses.append(analysis)

            # 按优先级排序
            analyses.sort(key=lambda x: x.priority, reverse=True)

            print(f"✅ 完成{len(analyses)}个持仓分析")
            return analyses

        except Exception as e:
            print(f"❌ 持仓分析失败: {e}")
            return []

    def _analyze_single_position(self, position: Dict, total_profit: float) -> Optional[PositionAnalysis]:
        """分析单个持仓"""
        try:
            order_id = position['order_id']
            symbol = position['symbol']
            profit = position.get('profit', 0)
            entry_price = position['entry_price']
            open_time = position.get('open_time', '')

            # 算法规则分析
            algo_analysis = self._algorithmic_analysis(position, total_profit)

            # 创建分析结果
            analysis = PositionAnalysis(
                order_id=order_id,
                symbol=symbol,
                action=algo_analysis['action'],
                reason=algo_analysis['reason'],
                priority=algo_analysis['priority'],
                profit_loss=profit,
                risk_level=algo_analysis['risk_level'],
                recommendation=algo_analysis['recommendation']
            )

            return analysis

        except Exception as e:
            print(f"❌ 分析持仓{position.get('order_id', 'unknown')}失败: {e}")
            return None

    def _algorithmic_analysis(self, position: Dict, total_profit: float) -> Dict:
        """算法规则分析（增强版，主动管理过多持仓）"""
        try:
            profit = position.get('profit', 0)
            symbol = position['symbol']
            entry_price = position['entry_price']
            open_time_str = position.get('open_time', '')

            # 计算持仓时间
            position_age_hours = self._calculate_position_age(open_time_str)

            # 获取当前总持仓数量
            if self.trade_executor:
                mt4_positions = self.trade_executor._get_mt4_real_positions()
                total_positions = len(mt4_positions) if mt4_positions else 0
            else:
                total_positions = 0

            # 规则1: 严重亏损立即平仓
            if profit <= self.config['max_loss_per_position']:
                return {
                    'action': PositionAction.CLOSE,
                    'reason': f"严重亏损${profit:.2f}，超过限制${self.config['max_loss_per_position']}",
                    'priority': 5,
                    'risk_level': 'VERY_HIGH',
                    'recommendation': '立即平仓止损'
                }

            # 规则2: 总亏损过大，平仓最亏损的订单
            if total_profit <= self.config['max_total_loss']:
                return {
                    'action': PositionAction.CLOSE,
                    'reason': f"总亏损${total_profit:.2f}过大，优先平仓亏损订单",
                    'priority': 4,
                    'risk_level': 'HIGH',
                    'recommendation': '控制总体风险，平仓亏损订单'
                }

            # 规则3: 持仓数量过多，主动清理
            if total_positions >= 15:  # 持仓数量过多时主动清理
                if profit < 0:  # 优先清理亏损订单
                    return {
                        'action': PositionAction.CLOSE,
                        'reason': f"持仓数量过多({total_positions}个)，清理亏损订单${profit:.2f}",
                        'priority': 4,
                        'risk_level': 'HIGH',
                        'recommendation': '减少持仓数量，控制风险'
                    }
                elif position_age_hours >= 12:  # 清理长期持仓
                    return {
                        'action': PositionAction.CLOSE,
                        'reason': f"持仓数量过多({total_positions}个)，清理长期持仓({position_age_hours:.1f}小时)",
                        'priority': 3,
                        'risk_level': 'MEDIUM',
                        'recommendation': '减少持仓数量，释放资金'
                    }

            # 规则4: 盈利保护
            if profit >= self.config['profit_protection_threshold']:
                return {
                    'action': PositionAction.MODIFY_SL,
                    'reason': f"盈利${profit:.2f}，建议移动止损保护利润",
                    'priority': 3,
                    'risk_level': 'LOW',
                    'recommendation': '移动止损到盈亏平衡点附近'
                }

            # 规则5: 持仓时间过长
            if position_age_hours >= self.config['max_position_age_hours']:
                return {
                    'action': PositionAction.CLOSE,
                    'reason': f"持仓时间{position_age_hours:.1f}小时过长",
                    'priority': 2,
                    'risk_level': 'MEDIUM',
                    'recommendation': '避免长期持仓风险'
                }

            # 规则6: 中等亏损，考虑止损
            if profit <= -3.0:
                return {
                    'action': PositionAction.CLOSE,
                    'reason': f"中等亏损${profit:.2f}，建议止损",
                    'priority': 3,
                    'risk_level': 'MEDIUM',
                    'recommendation': '及时止损，避免扩大亏损'
                }

            # 规则7: 小额亏损，继续持有但监控
            if -3.0 < profit < 0:
                return {
                    'action': PositionAction.HOLD,
                    'reason': f"小额亏损${profit:.2f}，继续监控",
                    'priority': 1,
                    'risk_level': 'LOW',
                    'recommendation': '密切监控，准备止损'
                }

            # 规则8: 正常持有
            return {
                'action': PositionAction.HOLD,
                'reason': f"持仓正常，盈亏${profit:.2f}",
                'priority': 1,
                'risk_level': 'LOW',
                'recommendation': '继续持有，监控市场'
            }

        except Exception as e:
            print(f"❌ 算法分析失败: {e}")
            return {
                'action': PositionAction.HOLD,
                'reason': '分析失败，保持现状',
                'priority': 1,
                'risk_level': 'UNKNOWN',
                'recommendation': '需要人工检查'
            }

    def _calculate_position_age(self, open_time_str: str) -> float:
        """计算持仓时间（小时）"""
        try:
            if not open_time_str:
                return 0

            # 解析MT4时间格式: "2025.05.26 18:49:38"
            open_time = datetime.strptime(open_time_str, "%Y.%m.%d %H:%M:%S")
            now = datetime.now()
            age = now - open_time
            return age.total_seconds() / 3600

        except Exception as e:
            print(f"❌ 计算持仓时间失败: {e}")
            return 0

    def execute_position_actions(self, analyses: List[PositionAnalysis]) -> Dict:
        """执行持仓管理动作"""
        try:
            print("⚡ 开始执行持仓管理动作...")

            results = {
                'executed': 0,
                'failed': 0,
                'skipped': 0,
                'actions': []
            }

            for analysis in analyses:
                if analysis.action == PositionAction.HOLD:
                    results['skipped'] += 1
                    continue

                print(f"🔄 执行动作: {analysis.symbol} {analysis.order_id} - {analysis.action.value}")
                print(f"   原因: {analysis.reason}")

                success = self._execute_single_action(analysis)

                if success:
                    results['executed'] += 1
                    print(f"✅ 动作执行成功")
                else:
                    results['failed'] += 1
                    print(f"❌ 动作执行失败")

                results['actions'].append({
                    'order_id': analysis.order_id,
                    'symbol': analysis.symbol,
                    'action': analysis.action.value,
                    'success': success,
                    'reason': analysis.reason
                })

            print(f"📊 执行结果: {results['executed']}成功, {results['failed']}失败, {results['skipped']}跳过")
            return results

        except Exception as e:
            print(f"❌ 执行持仓动作失败: {e}")
            return {'executed': 0, 'failed': 0, 'skipped': 0, 'actions': []}

    def _execute_single_action(self, analysis: PositionAnalysis) -> bool:
        """执行单个持仓动作"""
        try:
            if analysis.action == PositionAction.CLOSE:
                return self.trade_executor.close_position(analysis.order_id)

            elif analysis.action == PositionAction.MODIFY_SL:
                # TODO: 实现修改止损功能
                print("⚠️ 修改止损功能待实现")
                return False

            elif analysis.action == PositionAction.MODIFY_TP:
                # TODO: 实现修改止盈功能
                print("⚠️ 修改止盈功能待实现")
                return False

            else:
                print(f"⚠️ 不支持的动作: {analysis.action}")
                return False

        except Exception as e:
            print(f"❌ 执行动作失败: {e}")
            return False

    def print_position_analysis(self, analyses: List[PositionAnalysis]):
        """打印持仓分析结果"""
        if not analyses:
            print("📊 无持仓分析结果")
            return

        print("\n" + "="*80)
        print("📊 智能持仓管理分析报告")
        print("="*80)

        # 按动作分组
        actions_count = {}
        for analysis in analyses:
            action = analysis.action.value
            actions_count[action] = actions_count.get(action, 0) + 1

        print(f"📈 分析持仓数量: {len(analyses)}")
        print(f"📊 动作分布: {actions_count}")
        print("-"*80)

        for i, analysis in enumerate(analyses, 1):
            print(f"🔹 持仓 {i}: {analysis.symbol} (订单号: {analysis.order_id})")
            print(f"   💰 盈亏: ${analysis.profit_loss:.2f}")
            print(f"   🎯 建议动作: {analysis.action.value}")
            print(f"   📋 原因: {analysis.reason}")
            print(f"   🛡️ 风险等级: {analysis.risk_level}")
            print(f"   ⭐ 优先级: {analysis.priority}/5")
            print(f"   💡 建议: {analysis.recommendation}")
            print("-"*50)

        print("="*80 + "\n")
