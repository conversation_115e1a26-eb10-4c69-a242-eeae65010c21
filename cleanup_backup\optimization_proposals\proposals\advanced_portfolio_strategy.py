#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级组合交易策略
基于LLM深度分析的战略性交易方案
核心理念：LLM负责战略决策，算法负责战术执行和风险管理
"""

from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json

class PortfolioStrategy(Enum):
    """组合策略类型"""
    CURRENCY_STRENGTH_MOMENTUM = "货币强弱动量"
    CORRELATION_HEDGE = "相关性对冲"
    CARRY_TRADE_PORTFOLIO = "利差交易组合"
    VOLATILITY_ARBITRAGE = "波动率套利"
    MULTI_TIMEFRAME_CONVERGENCE = "多时间框架收敛"
    RISK_PARITY = "风险平价"
    MOMENTUM_MEAN_REVERSION_COMBO = "动量均值回归组合"

class ExecutionTiming(Enum):
    """执行时机"""
    IMMEDIATE = "立即执行"
    STAGED = "分批执行"
    CONDITIONAL = "条件执行"
    DELAYED = "延迟执行"

@dataclass
class PortfolioAllocation:
    """组合配置"""
    primary_positions: List[Dict]      # 主要持仓
    hedge_positions: List[Dict]        # 对冲持仓
    reserve_positions: List[Dict]      # 备用持仓
    total_risk_budget: float           # 总风险预算
    expected_sharpe_ratio: float       # 预期夏普比率
    max_drawdown_target: float         # 最大回撤目标
    rebalance_frequency: str           # 再平衡频率

@dataclass
class StrategicPlan:
    """战略计划"""
    strategy_name: str
    market_thesis: str                 # 市场观点
    time_horizon: str                  # 时间跨度
    portfolio_allocation: PortfolioAllocation
    execution_plan: Dict               # 执行计划
    risk_management_rules: Dict        # 风险管理规则
    exit_conditions: Dict              # 退出条件
    performance_targets: Dict          # 业绩目标
    confidence_level: float            # 置信水平

class AdvancedPortfolioManager:
    """高级组合管理器"""
    
    def __init__(self):
        # 货币对配置
        self.currency_pairs = {
            'majors': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'],
            'minors': ['EURGBP', 'EURJPY', 'GBPJPY', 'CHFJPY'],
            'commodities': ['AUDUSD', 'NZDUSD', 'USDCAD'],
            'emerging': ['USDZAR', 'USDMXN', 'USDTRY']
        }
        
        # 风险参数
        self.risk_parameters = {
            'max_portfolio_risk': 0.08,        # 最大组合风险8%
            'max_single_position_risk': 0.025, # 单笔最大风险2.5%
            'max_correlation_exposure': 0.6,   # 最大相关性暴露60%
            'min_diversification_ratio': 0.3,  # 最小分散化比率30%
            'volatility_target': 0.15          # 目标波动率15%
        }
        
        # 策略权重（根据市场状态动态调整）
        self.strategy_weights = {
            'trending_market': {
                'momentum': 0.6,
                'carry_trade': 0.3,
                'hedge': 0.1
            },
            'ranging_market': {
                'mean_reversion': 0.5,
                'volatility_arbitrage': 0.3,
                'hedge': 0.2
            },
            'volatile_market': {
                'hedge': 0.5,
                'volatility_arbitrage': 0.3,
                'risk_parity': 0.2
            }
        }
    
    def create_strategic_plan(self, market_analysis: Dict, current_positions: List = None) -> StrategicPlan:
        """创建战略计划"""
        
        # 1. 分析市场环境
        market_regime = self._analyze_market_regime(market_analysis)
        
        # 2. 识别最佳策略
        optimal_strategy = self._select_optimal_strategy(market_regime, market_analysis)
        
        # 3. 构建组合配置
        portfolio_allocation = self._build_portfolio_allocation(optimal_strategy, market_analysis)
        
        # 4. 制定执行计划
        execution_plan = self._create_execution_plan(portfolio_allocation, current_positions)
        
        # 5. 设定风险管理规则
        risk_rules = self._define_risk_management_rules(market_regime)
        
        # 6. 确定退出条件
        exit_conditions = self._define_exit_conditions(optimal_strategy, market_analysis)
        
        # 7. 设定业绩目标
        performance_targets = self._set_performance_targets(optimal_strategy, market_regime)
        
        return StrategicPlan(
            strategy_name=optimal_strategy.value,
            market_thesis=self._generate_market_thesis(market_regime, market_analysis),
            time_horizon=self._determine_time_horizon(optimal_strategy),
            portfolio_allocation=portfolio_allocation,
            execution_plan=execution_plan,
            risk_management_rules=risk_rules,
            exit_conditions=exit_conditions,
            performance_targets=performance_targets,
            confidence_level=self._calculate_confidence_level(market_analysis)
        )
    
    def _analyze_market_regime(self, market_analysis: Dict) -> str:
        """分析市场状态"""
        
        # 从多货币分析中提取关键信息
        pair_analyses = market_analysis.get('pair_analyses', {})
        market_risk = market_analysis.get('market_risk', {})
        
        # 计算趋势强度
        trend_strength = 0
        total_pairs = len(pair_analyses)
        
        for analysis in pair_analyses.values():
            if hasattr(analysis, 'trend_strength'):
                trend_strength += analysis.trend_strength
        
        avg_trend_strength = trend_strength / total_pairs if total_pairs > 0 else 0
        
        # 判断市场状态
        if avg_trend_strength > 0.7:
            return "strong_trending"
        elif avg_trend_strength > 0.4:
            return "weak_trending"
        elif market_risk.get('volatility_risk', 0) > 0.6:
            return "volatile_ranging"
        else:
            return "calm_ranging"
    
    def _select_optimal_strategy(self, market_regime: str, market_analysis: Dict) -> PortfolioStrategy:
        """选择最优策略"""
        
        correlation_opportunities = market_analysis.get('correlation_opportunities', [])
        currency_strength = market_analysis.get('currency_strength', {})
        
        if market_regime == "strong_trending":
            return PortfolioStrategy.CURRENCY_STRENGTH_MOMENTUM
        elif market_regime == "weak_trending":
            return PortfolioStrategy.MULTI_TIMEFRAME_CONVERGENCE
        elif market_regime == "volatile_ranging":
            if len(correlation_opportunities) > 0:
                return PortfolioStrategy.CORRELATION_HEDGE
            else:
                return PortfolioStrategy.VOLATILITY_ARBITRAGE
        else:  # calm_ranging
            return PortfolioStrategy.MOMENTUM_MEAN_REVERSION_COMBO
    
    def _build_portfolio_allocation(self, strategy: PortfolioStrategy, market_analysis: Dict) -> PortfolioAllocation:
        """构建组合配置"""
        
        if strategy == PortfolioStrategy.CURRENCY_STRENGTH_MOMENTUM:
            return self._build_momentum_portfolio(market_analysis)
        elif strategy == PortfolioStrategy.CORRELATION_HEDGE:
            return self._build_hedge_portfolio(market_analysis)
        elif strategy == PortfolioStrategy.VOLATILITY_ARBITRAGE:
            return self._build_volatility_portfolio(market_analysis)
        elif strategy == PortfolioStrategy.MULTI_TIMEFRAME_CONVERGENCE:
            return self._build_convergence_portfolio(market_analysis)
        else:
            return self._build_balanced_portfolio(market_analysis)
    
    def _build_momentum_portfolio(self, market_analysis: Dict) -> PortfolioAllocation:
        """构建动量组合"""
        
        currency_strength = market_analysis.get('currency_strength', {})
        
        # 排序货币强弱
        sorted_currencies = sorted(currency_strength.items(), key=lambda x: x[1], reverse=True)
        
        primary_positions = []
        
        # 构建强弱货币对组合
        if len(sorted_currencies) >= 4:
            strongest = sorted_currencies[0][0]
            second_strongest = sorted_currencies[1][0]
            weakest = sorted_currencies[-1][0]
            second_weakest = sorted_currencies[-2][0]
            
            # 主要持仓：强货币对弱货币
            primary_positions = [
                {
                    'pair': f'{strongest}{weakest}',
                    'direction': 'LONG',
                    'weight': 0.4,
                    'risk_allocation': 0.03,
                    'reasoning': f'做多最强货币{strongest}对最弱货币{weakest}'
                },
                {
                    'pair': f'{second_strongest}{second_weakest}',
                    'direction': 'LONG',
                    'weight': 0.3,
                    'risk_allocation': 0.025,
                    'reasoning': f'做多次强货币{second_strongest}对次弱货币{second_weakest}'
                }
            ]
        
        # 对冲持仓
        hedge_positions = [
            {
                'pair': 'EURUSD',  # 使用主要货币对作为对冲
                'direction': 'SHORT',
                'weight': 0.15,
                'risk_allocation': 0.015,
                'reasoning': '使用EURUSD作为组合对冲'
            }
        ]
        
        return PortfolioAllocation(
            primary_positions=primary_positions,
            hedge_positions=hedge_positions,
            reserve_positions=[],
            total_risk_budget=0.07,
            expected_sharpe_ratio=1.8,
            max_drawdown_target=0.05,
            rebalance_frequency="每周"
        )
    
    def _build_hedge_portfolio(self, market_analysis: Dict) -> PortfolioAllocation:
        """构建对冲组合"""
        
        correlation_opportunities = market_analysis.get('correlation_opportunities', [])
        
        primary_positions = []
        hedge_positions = []
        
        # 利用相关性机会构建对冲组合
        for i, opportunity in enumerate(correlation_opportunities[:2]):  # 最多2个对冲组合
            pair1 = opportunity['pair1']
            pair2 = opportunity['pair2']
            correlation = opportunity['correlation']
            
            # 主要持仓
            primary_positions.append({
                'pair': pair1,
                'direction': 'LONG',
                'weight': 0.25,
                'risk_allocation': 0.02,
                'reasoning': f'相关性套利主要持仓：{pair1}'
            })
            
            # 对冲持仓
            hedge_direction = 'SHORT' if correlation > 0 else 'LONG'
            hedge_weight = 0.25 * abs(correlation)
            
            hedge_positions.append({
                'pair': pair2,
                'direction': hedge_direction,
                'weight': hedge_weight,
                'risk_allocation': 0.015,
                'reasoning': f'相关性对冲：{pair2}，相关性{correlation:.2f}'
            })
        
        return PortfolioAllocation(
            primary_positions=primary_positions,
            hedge_positions=hedge_positions,
            reserve_positions=[],
            total_risk_budget=0.05,
            expected_sharpe_ratio=2.2,
            max_drawdown_target=0.03,
            rebalance_frequency="每日"
        )
    
    def _build_volatility_portfolio(self, market_analysis: Dict) -> PortfolioAllocation:
        """构建波动率套利组合"""
        
        pair_analyses = market_analysis.get('pair_analyses', {})
        
        # 寻找波动率差异
        high_vol_pairs = []
        low_vol_pairs = []
        
        for pair, analysis in pair_analyses.items():
            if hasattr(analysis, 'volatility_level'):
                if analysis.volatility_level == "HIGH":
                    high_vol_pairs.append(pair)
                elif analysis.volatility_level == "LOW":
                    low_vol_pairs.append(pair)
        
        primary_positions = []
        
        # 做空高波动率，做多低波动率
        for pair in high_vol_pairs[:2]:
            primary_positions.append({
                'pair': pair,
                'direction': 'SHORT',
                'weight': 0.2,
                'risk_allocation': 0.015,
                'reasoning': f'做空高波动率货币对{pair}'
            })
        
        for pair in low_vol_pairs[:2]:
            primary_positions.append({
                'pair': pair,
                'direction': 'LONG',
                'weight': 0.2,
                'risk_allocation': 0.01,
                'reasoning': f'做多低波动率货币对{pair}'
            })
        
        return PortfolioAllocation(
            primary_positions=primary_positions,
            hedge_positions=[],
            reserve_positions=[],
            total_risk_budget=0.05,
            expected_sharpe_ratio=1.5,
            max_drawdown_target=0.04,
            rebalance_frequency="每周"
        )
    
    def _build_convergence_portfolio(self, market_analysis: Dict) -> PortfolioAllocation:
        """构建多时间框架收敛组合"""
        
        # 这个策略需要多时间框架数据
        # 简化实现：寻找短期和长期趋势收敛的机会
        
        primary_positions = [
            {
                'pair': 'EURUSD',
                'direction': 'LONG',
                'weight': 0.3,
                'risk_allocation': 0.025,
                'reasoning': '多时间框架趋势收敛，看多EURUSD'
            },
            {
                'pair': 'GBPUSD',
                'direction': 'SHORT',
                'weight': 0.25,
                'risk_allocation': 0.02,
                'reasoning': '多时间框架趋势收敛，看空GBPUSD'
            }
        ]
        
        return PortfolioAllocation(
            primary_positions=primary_positions,
            hedge_positions=[],
            reserve_positions=[],
            total_risk_budget=0.045,
            expected_sharpe_ratio=1.6,
            max_drawdown_target=0.035,
            rebalance_frequency="每3天"
        )
    
    def _build_balanced_portfolio(self, market_analysis: Dict) -> PortfolioAllocation:
        """构建平衡组合"""
        
        primary_positions = [
            {
                'pair': 'EURUSD',
                'direction': 'LONG',
                'weight': 0.25,
                'risk_allocation': 0.02,
                'reasoning': '平衡组合主要持仓'
            },
            {
                'pair': 'USDJPY',
                'direction': 'SHORT',
                'weight': 0.2,
                'risk_allocation': 0.015,
                'reasoning': '平衡组合辅助持仓'
            }
        ]
        
        hedge_positions = [
            {
                'pair': 'GBPUSD',
                'direction': 'LONG',
                'weight': 0.15,
                'risk_allocation': 0.01,
                'reasoning': '组合对冲持仓'
            }
        ]
        
        return PortfolioAllocation(
            primary_positions=primary_positions,
            hedge_positions=hedge_positions,
            reserve_positions=[],
            total_risk_budget=0.045,
            expected_sharpe_ratio=1.4,
            max_drawdown_target=0.04,
            rebalance_frequency="每周"
        )
    
    def _create_execution_plan(self, allocation: PortfolioAllocation, current_positions: List) -> Dict:
        """创建执行计划"""
        
        return {
            'execution_timing': ExecutionTiming.STAGED.value,
            'execution_phases': [
                {
                    'phase': 1,
                    'description': '建立主要持仓',
                    'positions': allocation.primary_positions[:2],
                    'timing': '立即执行'
                },
                {
                    'phase': 2,
                    'description': '建立对冲持仓',
                    'positions': allocation.hedge_positions,
                    'timing': '主要持仓建立后30分钟'
                },
                {
                    'phase': 3,
                    'description': '完善组合配置',
                    'positions': allocation.primary_positions[2:],
                    'timing': '市场确认后执行'
                }
            ],
            'position_sizing_method': '基于波动率的动态仓位',
            'entry_method': '分批入场，降低市场冲击',
            'monitoring_frequency': '每15分钟检查一次'
        }
    
    def _define_risk_management_rules(self, market_regime: str) -> Dict:
        """定义风险管理规则"""
        
        base_rules = {
            'max_portfolio_loss': 0.05,  # 最大组合亏损5%
            'position_correlation_limit': 0.7,  # 持仓相关性限制
            'volatility_adjustment': True,  # 波动率调整
            'dynamic_hedging': True  # 动态对冲
        }
        
        # 根据市场状态调整规则
        if market_regime in ["volatile_ranging", "strong_trending"]:
            base_rules.update({
                'max_portfolio_loss': 0.03,  # 更严格的止损
                'increase_hedge_ratio': True,  # 增加对冲比例
                'reduce_position_size': True  # 减少仓位大小
            })
        
        return base_rules
    
    def _define_exit_conditions(self, strategy: PortfolioStrategy, market_analysis: Dict) -> Dict:
        """定义退出条件"""
        
        return {
            'profit_targets': {
                'partial_exit_1': 0.02,  # 2%部分获利
                'partial_exit_2': 0.04,  # 4%部分获利
                'full_exit': 0.06  # 6%完全获利
            },
            'stop_loss_levels': {
                'individual_position': 0.015,  # 单笔止损1.5%
                'portfolio_level': 0.03  # 组合止损3%
            },
            'time_based_exits': {
                'max_holding_period': '2周',
                'review_frequency': '每3天'
            },
            'market_condition_exits': {
                'regime_change': True,  # 市场状态改变时退出
                'correlation_breakdown': True,  # 相关性失效时退出
                'volatility_spike': True  # 波动率异常时退出
            }
        }
    
    def _set_performance_targets(self, strategy: PortfolioStrategy, market_regime: str) -> Dict:
        """设定业绩目标"""
        
        base_targets = {
            'monthly_return_target': 0.03,  # 月度收益目标3%
            'sharpe_ratio_target': 1.5,  # 夏普比率目标1.5
            'max_drawdown_limit': 0.05,  # 最大回撤限制5%
            'win_rate_target': 0.65  # 胜率目标65%
        }
        
        # 根据策略调整目标
        if strategy == PortfolioStrategy.CURRENCY_STRENGTH_MOMENTUM:
            base_targets.update({
                'monthly_return_target': 0.04,
                'sharpe_ratio_target': 1.8
            })
        elif strategy == PortfolioStrategy.CORRELATION_HEDGE:
            base_targets.update({
                'monthly_return_target': 0.025,
                'sharpe_ratio_target': 2.0,
                'max_drawdown_limit': 0.03
            })
        
        return base_targets
    
    def _generate_market_thesis(self, market_regime: str, market_analysis: Dict) -> str:
        """生成市场观点"""
        
        thesis_templates = {
            'strong_trending': "市场呈现强劲趋势，货币强弱分化明显，适合采用动量策略捕获趋势收益",
            'weak_trending': "市场趋势相对温和，需要多时间框架确认，采用收敛策略等待明确信号",
            'volatile_ranging': "市场波动加剧但缺乏明确方向，采用对冲策略控制风险并捕获波动率机会",
            'calm_ranging': "市场相对平静，采用均值回归和小幅动量结合的策略获取稳定收益"
        }
        
        return thesis_templates.get(market_regime, "市场状态不明确，采用保守策略")
    
    def _determine_time_horizon(self, strategy: PortfolioStrategy) -> str:
        """确定时间跨度"""
        
        time_horizons = {
            PortfolioStrategy.CURRENCY_STRENGTH_MOMENTUM: "1-2周",
            PortfolioStrategy.CORRELATION_HEDGE: "3-7天",
            PortfolioStrategy.VOLATILITY_ARBITRAGE: "1-2周",
            PortfolioStrategy.MULTI_TIMEFRAME_CONVERGENCE: "1-3周",
            PortfolioStrategy.MOMENTUM_MEAN_REVERSION_COMBO: "5-10天"
        }
        
        return time_horizons.get(strategy, "1周")
    
    def _calculate_confidence_level(self, market_analysis: Dict) -> float:
        """计算置信水平"""
        
        # 基于分析质量和市场清晰度计算置信度
        base_confidence = 0.6
        
        # 分析货币对数量
        pair_count = len(market_analysis.get('pair_analyses', {}))
        if pair_count >= 4:
            base_confidence += 0.1
        
        # 相关性机会
        correlation_count = len(market_analysis.get('correlation_opportunities', []))
        if correlation_count > 0:
            base_confidence += 0.1
        
        # 市场风险水平
        market_risk = market_analysis.get('market_risk', {})
        if market_risk.get('risk_level') == 'LOW':
            base_confidence += 0.1
        elif market_risk.get('risk_level') == 'HIGH':
            base_confidence -= 0.1
        
        return min(max(base_confidence, 0.3), 0.9)

# 测试函数
def test_portfolio_manager():
    """测试组合管理器"""
    print("🚀 高级组合管理器测试")
    print("=" * 60)
    
    # 创建管理器
    manager = AdvancedPortfolioManager()
    
    # 模拟市场分析数据
    market_analysis = {
        'pair_analyses': {
            'EURUSD': type('Analysis', (), {
                'trend_strength': 0.8,
                'volatility_level': 'NORMAL',
                'technical_score': 0.7
            })(),
            'GBPUSD': type('Analysis', (), {
                'trend_strength': 0.6,
                'volatility_level': 'HIGH',
                'technical_score': 0.6
            })(),
            'USDJPY': type('Analysis', (), {
                'trend_strength': 0.7,
                'volatility_level': 'LOW',
                'technical_score': 0.8
            })()
        },
        'currency_strength': {
            'USD': 0.8, 'EUR': 0.3, 'GBP': 0.1, 'JPY': -0.5
        },
        'correlation_opportunities': [
            {
                'pair1': 'EURUSD',
                'pair2': 'GBPUSD',
                'correlation': 0.7,
                'divergence_score': 0.5
            }
        ],
        'market_risk': {
            'risk_level': 'MEDIUM',
            'volatility_risk': 0.4
        }
    }
    
    # 创建战略计划
    print("📊 创建战略计划...")
    strategic_plan = manager.create_strategic_plan(market_analysis)
    
    print(f"   策略名称: {strategic_plan.strategy_name}")
    print(f"   市场观点: {strategic_plan.market_thesis}")
    print(f"   时间跨度: {strategic_plan.time_horizon}")
    print(f"   置信水平: {strategic_plan.confidence_level:.1%}")
    
    # 显示组合配置
    allocation = strategic_plan.portfolio_allocation
    print(f"\n📋 组合配置:")
    print(f"   总风险预算: {allocation.total_risk_budget:.1%}")
    print(f"   预期夏普比率: {allocation.expected_sharpe_ratio:.1f}")
    print(f"   最大回撤目标: {allocation.max_drawdown_target:.1%}")
    print(f"   再平衡频率: {allocation.rebalance_frequency}")
    
    # 显示主要持仓
    print(f"\n💼 主要持仓 ({len(allocation.primary_positions)}个):")
    for i, pos in enumerate(allocation.primary_positions, 1):
        print(f"   {i}. {pos['pair']} {pos['direction']} - 权重:{pos['weight']:.1%}, 风险:{pos['risk_allocation']:.1%}")
        print(f"      理由: {pos['reasoning']}")
    
    # 显示对冲持仓
    if allocation.hedge_positions:
        print(f"\n🛡️ 对冲持仓 ({len(allocation.hedge_positions)}个):")
        for i, pos in enumerate(allocation.hedge_positions, 1):
            print(f"   {i}. {pos['pair']} {pos['direction']} - 权重:{pos['weight']:.1%}")
            print(f"      理由: {pos['reasoning']}")
    
    # 显示执行计划
    execution = strategic_plan.execution_plan
    print(f"\n⚡ 执行计划:")
    print(f"   执行方式: {execution['execution_timing']}")
    print(f"   执行阶段: {len(execution['execution_phases'])}个阶段")
    for phase in execution['execution_phases']:
        print(f"   阶段{phase['phase']}: {phase['description']} - {phase['timing']}")
    
    # 显示业绩目标
    targets = strategic_plan.performance_targets
    print(f"\n🎯 业绩目标:")
    print(f"   月度收益目标: {targets['monthly_return_target']:.1%}")
    print(f"   夏普比率目标: {targets['sharpe_ratio_target']:.1f}")
    print(f"   胜率目标: {targets['win_rate_target']:.1%}")

if __name__ == "__main__":
    test_portfolio_manager()
