#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantumForex Pro - 内存优化器
解决内存使用过高导致系统停止运行的问题
"""

import gc
import sys
import os
import time
import psutil
import threading
from datetime import datetime
from typing import Dict, List, Optional

class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self, max_memory_percent=70):
        self.max_memory_percent = max_memory_percent
        self.running = False
        self.monitor_thread = None
        self.cleanup_count = 0
        self.last_cleanup = None
        
    def start_monitoring(self):
        """开始内存监控"""
        if self.running:
            return
            
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        print(f"🔍 内存监控已启动，最大使用率限制: {self.max_memory_percent}%")
    
    def stop_monitoring(self):
        """停止内存监控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        print("🛑 内存监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                memory_percent = psutil.virtual_memory().percent
                
                if memory_percent > self.max_memory_percent:
                    print(f"⚠️ 内存使用过高: {memory_percent:.1f}%，开始清理...")
                    self._emergency_cleanup()
                elif memory_percent > self.max_memory_percent - 10:
                    print(f"📊 内存使用警告: {memory_percent:.1f}%，执行优化...")
                    self._optimize_memory()
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                print(f"❌ 内存监控异常: {e}")
                time.sleep(60)
    
    def _emergency_cleanup(self):
        """紧急内存清理"""
        try:
            start_memory = psutil.virtual_memory().percent
            
            # 1. 强制垃圾回收
            collected = gc.collect()
            
            # 2. 清理Python内部缓存
            sys.intern.clear() if hasattr(sys.intern, 'clear') else None
            
            # 3. 清理模块缓存（谨慎操作）
            self._clear_module_cache()
            
            # 4. 再次垃圾回收
            gc.collect()
            
            end_memory = psutil.virtual_memory().percent
            freed_memory = start_memory - end_memory
            
            self.cleanup_count += 1
            self.last_cleanup = datetime.now()
            
            print(f"🧹 紧急清理完成: 释放{freed_memory:.1f}%内存，回收{collected}个对象")
            
        except Exception as e:
            print(f"❌ 紧急清理失败: {e}")
    
    def _optimize_memory(self):
        """优化内存使用"""
        try:
            # 轻量级清理
            collected = gc.collect()
            
            # 清理临时变量
            if 'locals' in dir():
                locals().clear()
            
            print(f"🔧 内存优化完成: 回收{collected}个对象")
            
        except Exception as e:
            print(f"❌ 内存优化失败: {e}")
    
    def _clear_module_cache(self):
        """清理模块缓存（谨慎操作）"""
        try:
            # 清理pandas缓存
            try:
                import pandas as pd
                if hasattr(pd, '_cache'):
                    pd._cache.clear()
            except:
                pass
            
            # 清理numpy缓存
            try:
                import numpy as np
                if hasattr(np, '_cache'):
                    np._cache.clear()
            except:
                pass
            
            # 清理matplotlib缓存
            try:
                import matplotlib
                matplotlib.pyplot.close('all')
            except:
                pass
                
        except Exception as e:
            print(f"⚠️ 清理模块缓存失败: {e}")
    
    def get_memory_status(self) -> Dict:
        """获取内存状态"""
        try:
            memory = psutil.virtual_memory()
            return {
                'total_gb': memory.total / (1024**3),
                'available_gb': memory.available / (1024**3),
                'used_gb': memory.used / (1024**3),
                'percent': memory.percent,
                'cleanup_count': self.cleanup_count,
                'last_cleanup': self.last_cleanup.strftime('%H:%M:%S') if self.last_cleanup else 'Never'
            }
        except Exception as e:
            return {'error': str(e)}

class DataCacheOptimizer:
    """数据缓存优化器"""
    
    def __init__(self, max_cache_size_mb=100):
        self.max_cache_size_mb = max_cache_size_mb
        self.cache = {}
        self.access_times = {}
        
    def set(self, key: str, data, ttl: int = 300):
        """设置缓存数据"""
        try:
            # 检查缓存大小
            if self._get_cache_size_mb() > self.max_cache_size_mb:
                self._evict_old_data()
            
            self.cache[key] = {
                'data': data,
                'expires': time.time() + ttl,
                'size': sys.getsizeof(data)
            }
            self.access_times[key] = time.time()
            
        except Exception as e:
            print(f"❌ 设置缓存失败: {e}")
    
    def get(self, key: str):
        """获取缓存数据"""
        try:
            if key in self.cache:
                entry = self.cache[key]
                
                # 检查是否过期
                if entry['expires'] > time.time():
                    self.access_times[key] = time.time()
                    return entry['data']
                else:
                    # 过期删除
                    del self.cache[key]
                    if key in self.access_times:
                        del self.access_times[key]
            
            return None
            
        except Exception as e:
            print(f"❌ 获取缓存失败: {e}")
            return None
    
    def _get_cache_size_mb(self) -> float:
        """获取缓存大小（MB）"""
        try:
            total_size = sum(entry['size'] for entry in self.cache.values())
            return total_size / (1024 * 1024)
        except:
            return 0
    
    def _evict_old_data(self):
        """清理旧数据"""
        try:
            current_time = time.time()
            
            # 清理过期数据
            expired_keys = [
                key for key, entry in self.cache.items()
                if entry['expires'] < current_time
            ]
            
            for key in expired_keys:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
            
            # 如果还是太大，清理最久未访问的数据
            if self._get_cache_size_mb() > self.max_cache_size_mb:
                sorted_keys = sorted(
                    self.access_times.keys(),
                    key=lambda k: self.access_times[k]
                )
                
                # 删除最久未访问的50%
                keys_to_remove = sorted_keys[:len(sorted_keys)//2]
                for key in keys_to_remove:
                    if key in self.cache:
                        del self.cache[key]
                    if key in self.access_times:
                        del self.access_times[key]
            
            print(f"🧹 缓存清理完成，当前大小: {self._get_cache_size_mb():.1f}MB")
            
        except Exception as e:
            print(f"❌ 缓存清理失败: {e}")
    
    def clear_all(self):
        """清空所有缓存"""
        try:
            self.cache.clear()
            self.access_times.clear()
            gc.collect()
            print("🧹 所有缓存已清空")
        except Exception as e:
            print(f"❌ 清空缓存失败: {e}")

def optimize_system_memory():
    """系统内存优化"""
    print("🔧 开始系统内存优化...")
    
    try:
        # 获取当前内存状态
        memory = psutil.virtual_memory()
        print(f"📊 当前内存使用: {memory.percent:.1f}% ({memory.used/(1024**3):.1f}GB/{memory.total/(1024**3):.1f}GB)")
        
        if memory.percent > 75:
            print("⚠️ 内存使用过高，执行紧急优化...")
            
            # 强制垃圾回收
            collected = gc.collect()
            print(f"🧹 垃圾回收: 清理{collected}个对象")
            
            # 清理系统缓存
            try:
                os.system('echo 3 > /proc/sys/vm/drop_caches 2>/dev/null')  # Linux
            except:
                pass
            
            # 再次检查内存
            memory_after = psutil.virtual_memory()
            freed = memory.percent - memory_after.percent
            print(f"✅ 优化完成: 释放{freed:.1f}%内存")
            
            return True
        else:
            print("✅ 内存使用正常，无需优化")
            return False
            
    except Exception as e:
        print(f"❌ 系统内存优化失败: {e}")
        return False

# 创建全局实例
memory_optimizer = MemoryOptimizer(max_memory_percent=70)
data_cache = DataCacheOptimizer(max_cache_size_mb=50)

def start_memory_monitoring():
    """启动内存监控"""
    memory_optimizer.start_monitoring()

def stop_memory_monitoring():
    """停止内存监控"""
    memory_optimizer.stop_monitoring()

def emergency_memory_cleanup():
    """紧急内存清理"""
    memory_optimizer._emergency_cleanup()
    data_cache.clear_all()

if __name__ == "__main__":
    print("🚀 QuantumForex Pro 内存优化器")
    print("=" * 50)
    
    # 显示当前内存状态
    memory = psutil.virtual_memory()
    print(f"📊 当前内存使用: {memory.percent:.1f}%")
    print(f"📊 总内存: {memory.total/(1024**3):.1f}GB")
    print(f"📊 已用内存: {memory.used/(1024**3):.1f}GB")
    print(f"📊 可用内存: {memory.available/(1024**3):.1f}GB")
    
    if memory.percent > 70:
        print("\n⚠️ 内存使用过高，建议立即优化！")
        optimize_system_memory()
    
    # 启动监控
    start_memory_monitoring()
    
    try:
        print("\n🔍 内存监控运行中... (按Ctrl+C停止)")
        while True:
            time.sleep(60)
            status = memory_optimizer.get_memory_status()
            print(f"📊 内存状态: {status['percent']:.1f}% | 清理次数: {status['cleanup_count']} | 上次清理: {status['last_cleanup']}")
    except KeyboardInterrupt:
        print("\n🛑 停止内存监控...")
        stop_memory_monitoring()
        print("✅ 内存优化器已退出")
