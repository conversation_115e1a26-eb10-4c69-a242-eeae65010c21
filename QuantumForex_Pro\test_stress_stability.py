#!/usr/bin/env python3
"""
QuantumForex Pro - 第三轮压力和稳定性测试
测试系统在压力环境下的稳定性和性能
"""

import sys
import os
import time
import threading
import psutil
from datetime import datetime, timedelta
import gc

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.stats = {
            'cpu_usage': [],
            'memory_usage': [],
            'peak_memory': 0,
            'start_time': None,
            'errors': []
        }
    
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.stats['start_time'] = datetime.now()
        
        def monitor_loop():
            while self.monitoring:
                try:
                    # CPU使用率
                    cpu_percent = psutil.cpu_percent(interval=1)
                    self.stats['cpu_usage'].append(cpu_percent)
                    
                    # 内存使用率
                    memory = psutil.virtual_memory()
                    memory_percent = memory.percent
                    self.stats['memory_usage'].append(memory_percent)
                    
                    # 峰值内存
                    if memory_percent > self.stats['peak_memory']:
                        self.stats['peak_memory'] = memory_percent
                    
                    time.sleep(2)
                except Exception as e:
                    self.stats['errors'].append(f"监控错误: {e}")
        
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=5)
    
    def get_stats(self):
        """获取统计信息"""
        if not self.stats['start_time']:
            return self.stats
        
        runtime = datetime.now() - self.stats['start_time']
        
        return {
            'runtime_seconds': runtime.total_seconds(),
            'avg_cpu_usage': sum(self.stats['cpu_usage']) / len(self.stats['cpu_usage']) if self.stats['cpu_usage'] else 0,
            'max_cpu_usage': max(self.stats['cpu_usage']) if self.stats['cpu_usage'] else 0,
            'avg_memory_usage': sum(self.stats['memory_usage']) / len(self.stats['memory_usage']) if self.stats['memory_usage'] else 0,
            'peak_memory_usage': self.stats['peak_memory'],
            'error_count': len(self.stats['errors']),
            'errors': self.stats['errors']
        }

def test_long_running_stability():
    """测试长时间运行稳定性"""
    print("🔍 开始长时间运行稳定性测试...")
    print("=" * 50)
    
    try:
        from main import QuantumForexPro
        
        # 创建系统监控器
        monitor = SystemMonitor()
        monitor.start_monitoring()
        
        # 创建系统实例
        print("🚀 创建系统实例...")
        system = QuantumForexPro()
        print("✅ 系统实例创建成功")
        
        # 设置运行时间（5分钟）
        run_duration = 300  # 5分钟
        start_time = time.time()
        cycle_count = 0
        error_count = 0
        
        print(f"⏱️ 开始{run_duration//60}分钟稳定性测试...")
        
        system.running = True
        
        while system.running and (time.time() - start_time) < run_duration:
            try:
                # 执行分析周期
                success = system._execute_analysis_cycle()
                cycle_count += 1
                
                if success:
                    print(f"✅ 分析周期 {cycle_count} 完成")
                else:
                    print(f"⚠️ 分析周期 {cycle_count} 失败")
                    error_count += 1
                
                # 更新系统统计
                system._update_system_stats()
                
                # 显示进度
                elapsed = time.time() - start_time
                progress = (elapsed / run_duration) * 100
                print(f"📊 进度: {progress:.1f}% ({elapsed:.0f}s/{run_duration}s)")
                
                # 内存清理
                if cycle_count % 10 == 0:
                    gc.collect()
                
                # 短暂休眠
                time.sleep(10)
                
            except Exception as e:
                error_count += 1
                print(f"❌ 分析周期异常: {e}")
                time.sleep(5)
        
        # 停止系统
        system.running = False
        
        # 停止监控
        monitor.stop_monitoring()
        
        # 获取统计信息
        stats = monitor.get_stats()
        system_stats = system.system_stats
        
        print("\n📊 长时间运行测试结果:")
        print(f"   运行时间: {stats['runtime_seconds']:.1f}秒")
        print(f"   分析周期数: {cycle_count}")
        print(f"   错误次数: {error_count}")
        print(f"   成功率: {((cycle_count - error_count) / cycle_count * 100):.1f}%" if cycle_count > 0 else "0%")
        print(f"   平均CPU使用: {stats['avg_cpu_usage']:.1f}%")
        print(f"   峰值CPU使用: {stats['max_cpu_usage']:.1f}%")
        print(f"   平均内存使用: {stats['avg_memory_usage']:.1f}%")
        print(f"   峰值内存使用: {stats['peak_memory_usage']:.1f}%")
        print(f"   系统成功分析: {system_stats.get('successful_analyses', 0)}")
        print(f"   系统失败分析: {system_stats.get('failed_analyses', 0)}")
        
        # 判断测试结果
        success_rate = ((cycle_count - error_count) / cycle_count * 100) if cycle_count > 0 else 0
        memory_ok = stats['peak_memory_usage'] < 85  # 内存使用不超过85%
        cpu_ok = stats['avg_cpu_usage'] < 70  # 平均CPU使用不超过70%
        
        test_passed = success_rate >= 80 and memory_ok and cpu_ok and error_count < 5
        
        print("=" * 50)
        if test_passed:
            print("✅ 长时间运行稳定性测试通过!")
        else:
            print("❌ 长时间运行稳定性测试失败!")
            if success_rate < 80:
                print(f"   - 成功率过低: {success_rate:.1f}%")
            if not memory_ok:
                print(f"   - 内存使用过高: {stats['peak_memory_usage']:.1f}%")
            if not cpu_ok:
                print(f"   - CPU使用过高: {stats['avg_cpu_usage']:.1f}%")
            if error_count >= 5:
                print(f"   - 错误次数过多: {error_count}")
        
        return test_passed
        
    except Exception as e:
        print(f"❌ 长时间运行稳定性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_concurrent_operations():
    """测试并发操作"""
    print("\n🔍 开始并发操作测试...")
    print("=" * 50)
    
    try:
        from main import QuantumForexPro
        from core.system_coordinator import SystemPriority
        
        # 创建系统实例
        system = QuantumForexPro()
        
        # 并发任务结果
        results = {'success': 0, 'failed': 0, 'errors': []}
        
        def submit_concurrent_tasks():
            """提交并发任务"""
            tasks = [
                ('risk_management', 'assess_risk', SystemPriority.HIGH),
                ('position_manager', 'analyze_positions', SystemPriority.HIGH),
                ('trading_system', 'execute_trade', SystemPriority.MEDIUM),
                ('llm_analyzer', 'analyze', SystemPriority.LOW),
                ('parameter_optimizer', 'optimize', SystemPriority.BACKGROUND)
            ]
            
            for i in range(10):  # 提交10轮任务
                for system_name, action, priority in tasks:
                    try:
                        task_id = system.system_coordinator.submit_task(
                            system_name=f"{system_name}_{i}",
                            action=action,
                            data={'test': f'concurrent_data_{i}'},
                            priority=priority
                        )
                        if task_id:
                            results['success'] += 1
                        else:
                            results['failed'] += 1
                    except Exception as e:
                        results['failed'] += 1
                        results['errors'].append(f"任务提交失败: {e}")
                
                time.sleep(0.1)  # 短暂间隔
        
        # 创建多个并发线程
        threads = []
        for i in range(3):  # 3个并发线程
            thread = threading.Thread(target=submit_concurrent_tasks, daemon=True)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=30)
        
        # 等待任务处理完成
        print("⏳ 等待任务处理完成...")
        time.sleep(10)
        
        # 检查系统状态
        status = system.system_coordinator.get_system_status()
        
        print(f"📊 并发操作测试结果:")
        print(f"   成功提交任务: {results['success']}")
        print(f"   失败提交任务: {results['failed']}")
        print(f"   错误数量: {len(results['errors'])}")
        print(f"   最终队列大小: {status['queue_size']}")
        print(f"   活跃任务数: {status['active_tasks']}")
        
        # 判断测试结果
        total_tasks = results['success'] + results['failed']
        success_rate = (results['success'] / total_tasks * 100) if total_tasks > 0 else 0
        test_passed = success_rate >= 90 and len(results['errors']) < 5
        
        print("=" * 50)
        if test_passed:
            print("✅ 并发操作测试通过!")
        else:
            print("❌ 并发操作测试失败!")
            if success_rate < 90:
                print(f"   - 成功率过低: {success_rate:.1f}%")
            if len(results['errors']) >= 5:
                print(f"   - 错误过多: {len(results['errors'])}")
        
        return test_passed
        
    except Exception as e:
        print(f"❌ 并发操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_leak():
    """测试内存泄漏"""
    print("\n🔍 开始内存泄漏测试...")
    print("=" * 50)
    
    try:
        import psutil
        
        # 记录初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"📊 初始内存使用: {initial_memory:.1f}MB")
        
        # 多次创建和销毁系统实例
        for i in range(5):
            print(f"🔄 第{i+1}轮创建/销毁测试...")
            
            # 创建系统实例
            from main import QuantumForexPro
            system = QuantumForexPro()
            
            # 执行一些操作
            system._execute_analysis_cycle()
            
            # 删除实例
            del system
            
            # 强制垃圾回收
            gc.collect()
            
            # 检查内存使用
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = current_memory - initial_memory
            
            print(f"   当前内存: {current_memory:.1f}MB (增长: {memory_increase:.1f}MB)")
            
            time.sleep(2)
        
        # 最终内存检查
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_increase = final_memory - initial_memory
        
        print(f"\n📊 内存泄漏测试结果:")
        print(f"   初始内存: {initial_memory:.1f}MB")
        print(f"   最终内存: {final_memory:.1f}MB")
        print(f"   总增长: {total_increase:.1f}MB")
        print(f"   增长率: {(total_increase/initial_memory*100):.1f}%")
        
        # 判断测试结果（内存增长不超过50%）
        test_passed = total_increase < initial_memory * 0.5
        
        print("=" * 50)
        if test_passed:
            print("✅ 内存泄漏测试通过!")
        else:
            print("❌ 内存泄漏测试失败!")
            print(f"   - 内存增长过大: {total_increase:.1f}MB")
        
        return test_passed
        
    except Exception as e:
        print(f"❌ 内存泄漏测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_recovery():
    """测试错误恢复能力"""
    print("\n🔍 开始错误恢复测试...")
    print("=" * 50)
    
    try:
        from main import QuantumForexPro
        
        # 创建系统实例
        system = QuantumForexPro()
        
        recovery_tests = []
        
        # 测试1: 模拟网络中断
        print("🔧 测试网络中断恢复...")
        try:
            # 暂时断开MT4连接
            original_connected = system.trade_executor.mt4_client.is_connected
            system.trade_executor.mt4_client.is_connected = False
            
            # 尝试执行分析
            result = system._execute_analysis_cycle()
            
            # 恢复连接
            system.trade_executor.mt4_client.is_connected = original_connected
            
            # 再次执行分析
            recovery_result = system._execute_analysis_cycle()
            
            recovery_tests.append(('网络中断恢复', recovery_result))
            print(f"   ✅ 网络中断恢复: {'成功' if recovery_result else '失败'}")
            
        except Exception as e:
            recovery_tests.append(('网络中断恢复', False))
            print(f"   ❌ 网络中断恢复测试异常: {e}")
        
        # 测试2: 模拟数据异常
        print("🔧 测试数据异常恢复...")
        try:
            # 执行正常分析
            result1 = system._execute_analysis_cycle()
            
            # 再次执行分析（测试连续执行）
            result2 = system._execute_analysis_cycle()
            
            recovery_tests.append(('数据异常恢复', result1 and result2))
            print(f"   ✅ 数据异常恢复: {'成功' if result1 and result2 else '失败'}")
            
        except Exception as e:
            recovery_tests.append(('数据异常恢复', False))
            print(f"   ❌ 数据异常恢复测试异常: {e}")
        
        # 测试3: 系统健康检查
        print("🔧 测试系统健康检查...")
        try:
            health_status = system._is_system_healthy()
            recovery_tests.append(('系统健康检查', health_status))
            print(f"   ✅ 系统健康检查: {'健康' if health_status else '异常'}")
            
        except Exception as e:
            recovery_tests.append(('系统健康检查', False))
            print(f"   ❌ 系统健康检查异常: {e}")
        
        # 统计结果
        passed_tests = sum(1 for _, result in recovery_tests if result)
        total_tests = len(recovery_tests)
        
        print(f"\n📊 错误恢复测试结果:")
        print(f"   通过测试: {passed_tests}/{total_tests}")
        print(f"   成功率: {(passed_tests/total_tests*100):.1f}%")
        
        # 判断测试结果
        test_passed = passed_tests >= total_tests * 0.8  # 80%通过率
        
        print("=" * 50)
        if test_passed:
            print("✅ 错误恢复测试通过!")
        else:
            print("❌ 错误恢复测试失败!")
            print(f"   - 通过率过低: {(passed_tests/total_tests*100):.1f}%")
        
        return test_passed
        
    except Exception as e:
        print(f"❌ 错误恢复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 QuantumForex Pro - 第三轮压力和稳定性测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行所有压力测试
    tests = [
        ("长时间运行稳定性", test_long_running_stability),
        ("并发操作", test_concurrent_operations),
        ("内存泄漏", test_memory_leak),
        ("错误恢复", test_error_recovery)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"🔄 开始执行: {test_name}")
            print(f"{'='*60}")
            
            results[test_name] = test_func()
            
            if results[test_name]:
                print(f"✅ {test_name} - 测试通过")
            else:
                print(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("📊 第三轮压力和稳定性测试结果汇总:")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 所有压力和稳定性测试通过! 系统稳定可靠!")
        print("🚀 准备进入第四轮: 业务逻辑验证测试")
        sys.exit(0)
    else:
        print("❌ 部分压力和稳定性测试失败，请优化系统性能")
        sys.exit(1)
