"""
QuantumForex MLTrainer 外汇数据收集器
从pizza_quotes数据库收集历史外汇数据用于模型训练
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pymysql
from sqlalchemy import create_engine

from config.training_config import training_config

class ForexDataCollector:
    """外汇数据收集器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 数据库配置
        self.db_config = training_config.DATABASE_CONFIG['pizza_quotes']
        
        # 数据配置
        self.data_config = training_config.DATA_CONFIG
        
        # 支持的货币对
        self.currency_pairs = self.data_config['currency_pairs']
        
        # 数据质量控制
        self.quality_config = self.data_config['quality_control']
        
        # 创建数据库连接
        self.engine = self._create_db_engine()
    
    def _create_db_engine(self):
        """创建数据库连接引擎"""
        try:
            connection_string = (
                f"mysql+pymysql://{self.db_config['user']}:{self.db_config['password']}"
                f"@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
                f"?charset={self.db_config['charset']}"
            )
            
            engine = create_engine(
                connection_string,
                pool_size=5,
                max_overflow=10,
                pool_timeout=30,
                pool_recycle=3600
            )
            
            self.logger.info("✅ 数据库连接引擎创建成功")
            return engine
            
        except Exception as e:
            self.logger.error(f"❌ 数据库连接引擎创建失败: {e}")
            raise
    
    def collect_historical_data(self, symbol: str, days: int = None) -> pd.DataFrame:
        """收集指定货币对的历史数据"""
        try:
            if days is None:
                days = self.data_config['data_range']['lookback_days']
            
            self.logger.info(f"📊 收集历史数据: {symbol}, {days}天")
            
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 构建SQL查询
            query = self._build_data_query(symbol, start_date, end_date)
            
            # 执行查询
            df = pd.read_sql(query, self.engine)
            
            if df.empty:
                self.logger.warning(f"⚠️ 没有找到数据: {symbol}")
                return pd.DataFrame()
            
            # 数据预处理
            df = self._preprocess_data(df, symbol)
            
            # 数据质量检查
            df = self._quality_check(df, symbol)
            
            self.logger.info(f"✅ 数据收集完成: {symbol}, {len(df)}条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 收集历史数据失败 {symbol}: {e}")
            return pd.DataFrame()
    
    def _build_data_query(self, symbol: str, start_date: datetime, end_date: datetime) -> str:
        """构建数据查询SQL"""
        # pizza_quotes数据库表结构查询
        query = f"""
        SELECT 
            time_stamp as timestamp,
            open_price as open,
            high_price as high,
            low_price as low,
            close_price as close,
            volume
        FROM forex_1m_data 
        WHERE symbol = '{symbol}'
        AND time_stamp >= '{start_date.strftime('%Y-%m-%d %H:%M:%S')}'
        AND time_stamp <= '{end_date.strftime('%Y-%m-%d %H:%M:%S')}'
        ORDER BY time_stamp ASC
        """
        
        return query
    
    def _preprocess_data(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """数据预处理"""
        try:
            # 转换时间戳
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            
            # 确保数据类型正确
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
            
            # 添加货币对标识
            df['symbol'] = symbol
            
            # 排序
            df.sort_index(inplace=True)
            
            self.logger.debug(f"数据预处理完成: {symbol}")
            return df
            
        except Exception as e:
            self.logger.error(f"数据预处理失败 {symbol}: {e}")
            return df
    
    def _quality_check(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """数据质量检查和清理"""
        try:
            original_count = len(df)
            
            # 1. 删除空值
            if self.quality_config['fill_missing_values']:
                # 前向填充价格数据
                price_columns = ['open', 'high', 'low', 'close']
                df[price_columns] = df[price_columns].fillna(method='ffill')
                
                # 删除仍然为空的行
                df.dropna(subset=price_columns, inplace=True)
            
            # 2. 删除异常值
            if self.quality_config['remove_outliers']:
                df = self._remove_outliers(df)
            
            # 3. 验证OHLC逻辑
            df = self._validate_ohlc_logic(df)
            
            # 4. 删除重复数据
            df = df[~df.index.duplicated(keep='first')]
            
            cleaned_count = len(df)
            removed_count = original_count - cleaned_count
            
            if removed_count > 0:
                self.logger.info(f"🧹 数据清理: {symbol}, 删除{removed_count}条异常数据")
            
            return df
            
        except Exception as e:
            self.logger.error(f"数据质量检查失败 {symbol}: {e}")
            return df
    
    def _remove_outliers(self, df: pd.DataFrame) -> pd.DataFrame:
        """删除异常值"""
        try:
            price_columns = ['open', 'high', 'low', 'close']
            threshold = self.quality_config['outlier_threshold']
            
            for col in price_columns:
                # 计算Z分数
                z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
                
                # 删除超过阈值的数据
                outlier_mask = z_scores > threshold
                df = df[~outlier_mask]
            
            return df
            
        except Exception as e:
            self.logger.error(f"删除异常值失败: {e}")
            return df
    
    def _validate_ohlc_logic(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证OHLC数据逻辑"""
        try:
            # 检查 high >= max(open, close) 和 low <= min(open, close)
            valid_high = df['high'] >= df[['open', 'close']].max(axis=1)
            valid_low = df['low'] <= df[['open', 'close']].min(axis=1)
            
            # 删除逻辑错误的数据
            valid_mask = valid_high & valid_low
            invalid_count = (~valid_mask).sum()
            
            if invalid_count > 0:
                self.logger.warning(f"⚠️ 删除{invalid_count}条OHLC逻辑错误的数据")
                df = df[valid_mask]
            
            return df
            
        except Exception as e:
            self.logger.error(f"OHLC逻辑验证失败: {e}")
            return df
    
    def collect_multiple_symbols(self, symbols: List[str] = None, days: int = None) -> Dict[str, pd.DataFrame]:
        """收集多个货币对的数据"""
        try:
            if symbols is None:
                symbols = self.currency_pairs
            
            self.logger.info(f"📊 批量收集数据: {len(symbols)}个货币对")
            
            data_dict = {}
            success_count = 0
            
            for symbol in symbols:
                try:
                    df = self.collect_historical_data(symbol, days)
                    if not df.empty:
                        data_dict[symbol] = df
                        success_count += 1
                    else:
                        self.logger.warning(f"⚠️ {symbol} 数据为空")
                        
                except Exception as e:
                    self.logger.error(f"❌ 收集数据失败 {symbol}: {e}")
            
            self.logger.info(f"✅ 批量收集完成: {success_count}/{len(symbols)} 成功")
            return data_dict
            
        except Exception as e:
            self.logger.error(f"❌ 批量收集数据失败: {e}")
            return {}
    
    def get_latest_data(self, symbol: str, hours: int = 24) -> pd.DataFrame:
        """获取最新数据"""
        try:
            self.logger.info(f"📊 获取最新数据: {symbol}, {hours}小时")
            
            end_date = datetime.now()
            start_date = end_date - timedelta(hours=hours)
            
            query = self._build_data_query(symbol, start_date, end_date)
            df = pd.read_sql(query, self.engine)
            
            if not df.empty:
                df = self._preprocess_data(df, symbol)
                df = self._quality_check(df, symbol)
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 获取最新数据失败 {symbol}: {e}")
            return pd.DataFrame()
    
    def get_data_summary(self, symbol: str = None) -> Dict:
        """获取数据摘要信息"""
        try:
            if symbol:
                symbols = [symbol]
            else:
                symbols = self.currency_pairs
            
            summary = {}
            
            for sym in symbols:
                try:
                    # 查询数据统计
                    query = f"""
                    SELECT 
                        COUNT(*) as total_records,
                        MIN(time_stamp) as earliest_date,
                        MAX(time_stamp) as latest_date,
                        AVG(close_price) as avg_price,
                        MIN(close_price) as min_price,
                        MAX(close_price) as max_price
                    FROM forex_1m_data 
                    WHERE symbol = '{sym}'
                    """
                    
                    result = pd.read_sql(query, self.engine)
                    
                    if not result.empty and result.iloc[0]['total_records'] > 0:
                        summary[sym] = {
                            'total_records': int(result.iloc[0]['total_records']),
                            'earliest_date': result.iloc[0]['earliest_date'],
                            'latest_date': result.iloc[0]['latest_date'],
                            'avg_price': float(result.iloc[0]['avg_price']),
                            'min_price': float(result.iloc[0]['min_price']),
                            'max_price': float(result.iloc[0]['max_price']),
                            'data_available': True
                        }
                    else:
                        summary[sym] = {'data_available': False}
                        
                except Exception as e:
                    self.logger.error(f"获取摘要失败 {sym}: {e}")
                    summary[sym] = {'error': str(e)}
            
            return summary
            
        except Exception as e:
            self.logger.error(f"❌ 获取数据摘要失败: {e}")
            return {}
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            query = "SELECT 1 as test"
            result = pd.read_sql(query, self.engine)
            
            if not result.empty:
                self.logger.info("✅ 数据库连接测试成功")
                return True
            else:
                self.logger.error("❌ 数据库连接测试失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 数据库连接测试异常: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        try:
            if self.engine:
                self.engine.dispose()
                self.logger.info("✅ 数据库连接已关闭")
        except Exception as e:
            self.logger.error(f"❌ 关闭数据库连接失败: {e}")

# 创建全局实例
forex_collector = ForexDataCollector()
