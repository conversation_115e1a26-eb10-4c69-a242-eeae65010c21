#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantumForex Pro - 内存优化启动脚本
解决内存使用过高导致系统停止运行的问题
"""

import sys
import os
import time
import psutil
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_and_optimize_memory():
    """检查并优化内存"""
    try:
        memory = psutil.virtual_memory()
        print(f"📊 当前内存使用: {memory.percent:.1f}% ({memory.used/(1024**3):.1f}GB/{memory.total/(1024**3):.1f}GB)")
        
        if memory.percent > 75:
            print("⚠️ 内存使用过高，执行预优化...")
            
            # 导入内存优化器
            from memory_optimizer import optimize_system_memory, emergency_memory_cleanup
            
            # 执行系统内存优化
            optimize_system_memory()
            
            # 再次检查
            memory_after = psutil.virtual_memory()
            print(f"📊 优化后内存使用: {memory_after.percent:.1f}%")
            
            if memory_after.percent > 70:
                print("⚠️ 内存仍然较高，建议关闭其他程序后再启动")
                return False
            else:
                print("✅ 内存优化成功，可以启动系统")
                return True
        else:
            print("✅ 内存使用正常")
            return True
            
    except Exception as e:
        print(f"❌ 内存检查失败: {e}")
        return True  # 如果检查失败，仍然尝试启动

def display_system_requirements():
    """显示系统要求"""
    print("=" * 60)
    print("🎯 QuantumForex Pro 系统要求")
    print("=" * 60)
    print("💾 推荐内存: 4GB以上")
    print("🔧 推荐CPU: 双核以上")
    print("💽 可用磁盘: 1GB以上")
    print("🌐 网络: 稳定的互联网连接")
    print("📊 内存使用限制: <70%")
    print("=" * 60)

def main():
    """主启动函数"""
    print("🚀 QuantumForex Pro 内存优化启动器")
    print("=" * 60)
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示系统要求
    display_system_requirements()
    
    # 检查系统资源
    print("\n🔍 检查系统资源...")
    
    try:
        # 检查内存
        if not check_and_optimize_memory():
            print("\n❌ 内存优化失败，建议:")
            print("   1. 关闭不必要的程序")
            print("   2. 重启计算机")
            print("   3. 增加虚拟内存")
            return False
        
        # 检查CPU
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"🖥️ CPU使用率: {cpu_percent:.1f}%")
        
        if cpu_percent > 80:
            print("⚠️ CPU使用率较高，可能影响系统性能")
        
        # 检查磁盘空间
        disk = psutil.disk_usage('.')
        free_gb = disk.free / (1024**3)
        print(f"💽 可用磁盘空间: {free_gb:.1f}GB")
        
        if free_gb < 1:
            print("⚠️ 磁盘空间不足，建议清理磁盘")
        
        print("\n✅ 系统资源检查完成")
        
        # 启动内存监控
        print("\n🔍 启动内存监控...")
        from memory_optimizer import memory_optimizer
        memory_optimizer.start_monitoring()
        
        # 等待一下确保监控启动
        time.sleep(2)
        
        print("✅ 内存监控已启动")
        
        # 启动主程序
        print("\n🎯 启动 QuantumForex Pro 主程序...")
        print("=" * 60)
        
        # 导入并启动主程序
        from main import main as main_program
        main_program()
        
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断启动")
        return False
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 确保停止内存监控
        try:
            from memory_optimizer import memory_optimizer
            memory_optimizer.stop_monitoring()
            print("🔍 内存监控已停止")
        except:
            pass

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ QuantumForex Pro 运行完成")
        else:
            print("\n❌ QuantumForex Pro 启动失败")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 启动器异常: {e}")
        sys.exit(1)
