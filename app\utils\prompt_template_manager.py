"""
提示词模板管理器
用于加载和管理提示词模板
"""
import os
import re
import json
from datetime import datetime
from string import Template

# 模板目录
TEMPLATE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'templates')

# 模板缓存
template_cache = {}

# 模板版本
DEFAULT_VERSION = 'default'
BETA_VERSION = 'beta'

# 当前使用的版本
current_version = DEFAULT_VERSION

def get_template_path(template_name, version=None):
    """
    获取模板文件路径

    Args:
        template_name (str): 模板名称
        version (str, optional): 模板版本，如果为None则使用当前版本

    Returns:
        str: 模板文件路径
    """
    if version is None:
        version = current_version

    # 如果是beta版本，先尝试加载beta版本的模板
    if version == BETA_VERSION:
        beta_path = os.path.join(TEMPLATE_DIR, f"{template_name}_{BETA_VERSION}.txt")
        if os.path.exists(beta_path):
            return beta_path

    # 默认版本或beta版本不存在时，加载默认版本
    default_path = os.path.join(TEMPLATE_DIR, f"{template_name}.txt")
    if os.path.exists(default_path):
        return default_path

    # 如果没有.txt后缀，尝试添加后缀
    if not template_name.endswith('.txt'):
        default_path = os.path.join(TEMPLATE_DIR, f"{template_name}.txt")
        if os.path.exists(default_path):
            return default_path

    # 如果都不存在，返回None
    return None

def load_template(template_name, version=None, force_reload=False):
    """
    加载模板

    Args:
        template_name (str): 模板名称
        version (str, optional): 模板版本，如果为None则使用当前版本
        force_reload (bool, optional): 是否强制重新加载

    Returns:
        str: 模板内容
    """
    if version is None:
        version = current_version

    # 缓存键
    cache_key = f"{template_name}_{version}"

    # 如果缓存中存在且不强制重新加载，则直接返回缓存
    if cache_key in template_cache and not force_reload:
        return template_cache[cache_key]

    # 获取模板文件路径
    template_path = get_template_path(template_name, version)
    if not template_path:
        raise FileNotFoundError(f"模板文件不存在: {template_name}")

    # 加载模板文件
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()

        # 缓存模板
        template_cache[cache_key] = template_content

        return template_content
    except Exception as e:
        raise Exception(f"加载模板文件失败: {e}")

def render_template(template_name, data, version=None):
    """
    渲染模板 - 增强版

    支持多种变量引用格式:
    1. ${variable} - Python string.Template格式
    2. {{variable}} - Jinja2/Mustache风格格式

    Args:
        template_name (str): 模板名称
        data (dict): 模板数据
        version (str, optional): 模板版本，如果为None则使用当前版本

    Returns:
        str: 渲染后的内容
    """
    # 加载模板
    template_content = load_template(template_name, version)

    # 处理数据中的None值
    processed_data = {}
    for key, value in data.items():
        if value is None:
            processed_data[key] = 'N/A'
        else:
            processed_data[key] = value

    # 渲染模板
    try:
        # 第一步：使用string.Template渲染${variable}格式
        template = Template(template_content)
        content_after_step1 = template.safe_substitute(processed_data)

        # 第二步：渲染{{variable}}格式
        content_after_step2 = content_after_step1
        for key, value in processed_data.items():
            # 使用正则表达式替换{{variable}}格式
            pattern = r'\{\{\s*' + re.escape(key) + r'\s*\}\}'
            content_after_step2 = re.sub(pattern, str(value), content_after_step2)

        # 记录日志，帮助调试
        if content_after_step1 != content_after_step2:
            print(f"模板渲染：检测到并替换了{{{{variable}}}}格式的变量")

        return content_after_step2
    except KeyError as e:
        # 如果缺少必要的数据，抛出异常
        raise KeyError(f"渲染模板时缺少必要的数据: {e}")
    except Exception as e:
        # 其他异常
        raise Exception(f"渲染模板失败: {e}")

def set_version(version):
    """
    设置当前使用的版本

    Args:
        version (str): 版本名称
    """
    global current_version
    if version in [DEFAULT_VERSION, BETA_VERSION]:
        current_version = version
    else:
        raise ValueError(f"无效的版本名称: {version}")

def get_version():
    """
    获取当前使用的版本

    Returns:
        str: 当前版本
    """
    return current_version

def list_templates():
    """
    列出所有可用的模板

    Returns:
        list: 模板列表
    """
    templates = []
    for filename in os.listdir(TEMPLATE_DIR):
        if filename.endswith('.txt'):
            # 去掉后缀和版本信息
            template_name = filename.replace('.txt', '')
            if '_beta' in template_name:
                template_name = template_name.replace('_beta', '')

            if template_name not in templates:
                templates.append(template_name)

    return templates

def create_template(template_name, content, version=None):
    """
    创建新模板

    Args:
        template_name (str): 模板名称
        content (str): 模板内容
        version (str, optional): 模板版本，如果为None则使用当前版本
    """
    if version is None:
        version = current_version

    # 确保模板目录存在
    os.makedirs(TEMPLATE_DIR, exist_ok=True)

    # 构建模板文件路径
    if version == DEFAULT_VERSION:
        template_path = os.path.join(TEMPLATE_DIR, f"{template_name}.txt")
    else:
        template_path = os.path.join(TEMPLATE_DIR, f"{template_name}_{version}.txt")

    # 写入模板文件
    try:
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)

        # 清除缓存
        cache_key = f"{template_name}_{version}"
        if cache_key in template_cache:
            del template_cache[cache_key]

        return True
    except Exception as e:
        raise Exception(f"创建模板文件失败: {e}")

def update_template(template_name, content, version=None):
    """
    更新模板

    Args:
        template_name (str): 模板名称
        content (str): 模板内容
        version (str, optional): 模板版本，如果为None则使用当前版本
    """
    if version is None:
        version = current_version

    # 获取模板文件路径
    template_path = get_template_path(template_name, version)
    if not template_path:
        # 如果模板不存在，则创建新模板
        return create_template(template_name, content, version)

    # 更新模板文件
    try:
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)

        # 清除缓存
        cache_key = f"{template_name}_{version}"
        if cache_key in template_cache:
            del template_cache[cache_key]

        return True
    except Exception as e:
        raise Exception(f"更新模板文件失败: {e}")

def delete_template(template_name, version=None):
    """
    删除模板

    Args:
        template_name (str): 模板名称
        version (str, optional): 模板版本，如果为None则使用当前版本
    """
    if version is None:
        version = current_version

    # 获取模板文件路径
    template_path = get_template_path(template_name, version)
    if not template_path:
        raise FileNotFoundError(f"模板文件不存在: {template_name}")

    # 删除模板文件
    try:
        os.remove(template_path)

        # 清除缓存
        cache_key = f"{template_name}_{version}"
        if cache_key in template_cache:
            del template_cache[cache_key]

        return True
    except Exception as e:
        raise Exception(f"删除模板文件失败: {e}")

def extract_template_variables(template_content):
    """
    提取模板中的变量

    支持多种变量引用格式:
    1. ${variable} - Python string.Template格式
    2. {{variable}} - Jinja2/Mustache风格格式

    Args:
        template_content (str): 模板内容

    Returns:
        list: 变量列表
    """
    variables = []

    # 提取{{variable}}格式的变量
    pattern1 = r'\{\{\s*([a-zA-Z0-9_]+)\s*\}\}'
    variables1 = re.findall(pattern1, template_content)
    variables.extend(variables1)

    # 提取${variable}格式的变量
    pattern2 = r'\$\{([a-zA-Z0-9_]+)\}'
    variables2 = re.findall(pattern2, template_content)
    variables.extend(variables2)

    # 去重
    variables = list(set(variables))

    print(f"从模板中提取到 {len(variables)} 个变量: {variables}")

    return variables

def get_template_variables(template_name, version=None):
    """
    获取模板中的变量

    Args:
        template_name (str): 模板名称
        version (str, optional): 模板版本，如果为None则使用当前版本

    Returns:
        list: 变量列表
    """
    # 加载模板
    template_content = load_template(template_name, version)

    # 提取变量
    variables = extract_template_variables(template_content)

    return variables

def save_template_metadata(template_name, metadata, version=None):
    """
    保存模板元数据

    Args:
        template_name (str): 模板名称
        metadata (dict): 元数据
        version (str, optional): 模板版本，如果为None则使用当前版本
    """
    if version is None:
        version = current_version

    # 构建元数据文件路径
    if version == DEFAULT_VERSION:
        metadata_path = os.path.join(TEMPLATE_DIR, f"{template_name}_metadata.json")
    else:
        metadata_path = os.path.join(TEMPLATE_DIR, f"{template_name}_{version}_metadata.json")

    # 添加时间戳
    metadata['updated_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 写入元数据文件
    try:
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

        return True
    except Exception as e:
        raise Exception(f"保存模板元数据失败: {e}")

def load_template_metadata(template_name, version=None):
    """
    加载模板元数据

    Args:
        template_name (str): 模板名称
        version (str, optional): 模板版本，如果为None则使用当前版本

    Returns:
        dict: 元数据
    """
    if version is None:
        version = current_version

    # 构建元数据文件路径
    if version == DEFAULT_VERSION:
        metadata_path = os.path.join(TEMPLATE_DIR, f"{template_name}_metadata.json")
    else:
        metadata_path = os.path.join(TEMPLATE_DIR, f"{template_name}_{version}_metadata.json")

    # 加载元数据文件
    try:
        if os.path.exists(metadata_path):
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)

            return metadata
        else:
            # 如果元数据文件不存在，返回空字典
            return {}
    except Exception as e:
        raise Exception(f"加载模板元数据失败: {e}")
