#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能日志分析器
实时监听和分析Pro系统的日志输出
智能识别问题、异常模式和系统状态
"""

import sys
import os
import logging
import time
import threading
import re
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import subprocess

@dataclass
class LogEvent:
    """日志事件"""
    timestamp: datetime
    level: str
    component: str
    message: str
    raw_line: str

@dataclass
class SystemIssue:
    """系统问题"""
    issue_type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    component: str
    description: str
    evidence: List[str]
    first_seen: datetime
    count: int

class IntelligentLogAnalyzer:
    """智能日志分析器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 监听状态
        self.is_monitoring = False
        self.monitor_thread = None

        # 日志分析
        self.log_buffer = []
        self.detected_issues = {}
        self.system_status = {}

        # 配置
        self.config = {
            'log_file_pattern': 'QuantumForex_Pro*.log',
            'buffer_size': 1000,
            'analysis_interval': 10,  # 10秒分析一次
            'issue_threshold': 3  # 同类问题出现3次才报告
        }

        # 问题模式定义
        self.issue_patterns = {
            'connection_failure': {
                'patterns': [
                    r'连接.*失败',
                    r'无法连接',
                    r'Connection.*failed',
                    r'MT4.*连接.*异常'
                ],
                'severity': 'high',
                'description': 'MT4连接问题'
            },
            'trading_rejection': {
                'patterns': [
                    r'交易执行被拒绝',
                    r'拒绝原因',
                    r'无有效交易决策',
                    r'安全限制'
                ],
                'severity': 'medium',
                'description': '交易决策被拒绝'
            },
            'data_error': {
                'patterns': [
                    r'获取.*失败',
                    r'数据.*异常',
                    r'解析.*错误',
                    r'JSON.*失败'
                ],
                'severity': 'medium',
                'description': '数据获取或解析错误'
            },
            'strategy_error': {
                'patterns': [
                    r'策略.*失败',
                    r'分析.*异常',
                    r'ML.*错误',
                    r'模型.*失败'
                ],
                'severity': 'high',
                'description': '策略分析错误'
            },
            'risk_warning': {
                'patterns': [
                    r'风险.*过高',
                    r'仓位.*超限',
                    r'止损.*异常',
                    r'风险管理.*警告'
                ],
                'severity': 'high',
                'description': '风险管理警告'
            },
            'performance_issue': {
                'patterns': [
                    r'内存.*不足',
                    r'CPU.*过高',
                    r'响应.*超时',
                    r'性能.*警告'
                ],
                'severity': 'medium',
                'description': '系统性能问题'
            }
        }

        # 系统状态关键词
        self.status_keywords = {
            'startup': ['启动', '初始化', 'startup', 'initialize'],
            'running': ['运行中', '分析', '执行', 'running', 'analyzing'],
            'trading': ['交易', '订单', '持仓', 'trading', 'order', 'position'],
            'error': ['错误', '失败', '异常', 'error', 'failed', 'exception'],
            'warning': ['警告', '注意', 'warning', 'caution'],
            'success': ['成功', '完成', 'success', 'completed']
        }

    def start_monitoring(self):
        """启动日志监听"""
        try:
            self.logger.info("[START] 启动智能日志分析器")
            self.logger.info("[TARGET] 实时分析Pro系统日志，智能识别问题")

            self.is_monitoring = True

            # 启动监听线程
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()

            self.logger.info("[OK] 智能日志分析器启动成功")
            self.logger.info("[DATA] 监听功能:")
            self.logger.info("   - 实时日志解析")
            self.logger.info("   - 智能问题识别")
            self.logger.info("   - 系统状态分析")
            self.logger.info("   - 异常模式检测")
            self.logger.info("   - 可读性报告生成")

        except Exception as e:
            self.logger.error(f"[ERROR] 启动智能日志分析器失败: {e}")

    def stop_monitoring(self):
        """停止监听"""
        try:
            self.logger.info("[STOP] 停止智能日志分析器")
            self.is_monitoring = False

            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=10)

            # 生成最终报告
            self._generate_final_report()

            self.logger.info("[OK] 智能日志分析器已停止")

        except Exception as e:
            self.logger.error(f"[ERROR] 停止分析器失败: {e}")

    def _monitor_loop(self):
        """监听主循环"""
        last_analysis = datetime.now()

        while self.is_monitoring:
            try:
                # 读取新的日志行
                new_logs = self._read_new_logs()

                if new_logs:
                    # 解析日志事件
                    for log_line in new_logs:
                        event = self._parse_log_line(log_line)
                        if event:
                            self.log_buffer.append(event)

                    # 限制缓冲区大小
                    if len(self.log_buffer) > self.config['buffer_size']:
                        self.log_buffer = self.log_buffer[-self.config['buffer_size']:]

                # 定期分析
                if (datetime.now() - last_analysis).seconds >= self.config['analysis_interval']:
                    self._analyze_logs()
                    self._update_system_status()
                    self._report_issues()
                    last_analysis = datetime.now()

                time.sleep(2)  # 2秒检查一次新日志

            except Exception as e:
                self.logger.error(f"[ERROR] 监听循环异常: {e}")
                time.sleep(10)

    def _read_new_logs(self) -> List[str]:
        """读取新的日志行"""
        try:
            # 查找Pro系统的日志文件
            log_files = []
            for file in os.listdir('.'):
                if 'QuantumForex_Pro' in file and file.endswith('.log'):
                    log_files.append(file)

            if not log_files:
                return []

            # 读取最新的日志文件
            latest_log = max(log_files, key=os.path.getmtime)

            # Windows环境下使用Python方式读取
            with open(latest_log, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                return [line.strip() for line in lines[-50:]]  # 最后50行

            return []

        except Exception as e:
            self.logger.debug(f"读取日志失败: {e}")
            return []

    def _parse_log_line(self, line: str) -> Optional[LogEvent]:
        """解析日志行"""
        try:
            if not line.strip():
                return None

            # 尝试解析时间戳
            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
            if timestamp_match:
                timestamp_str = timestamp_match.group(1)
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
            else:
                timestamp = datetime.now()

            # 识别日志级别
            level = 'INFO'
            if 'ERROR' in line or '错误' in line or '❌' in line:
                level = 'ERROR'
            elif 'WARNING' in line or '警告' in line or '⚠️' in line:
                level = 'WARNING'
            elif 'DEBUG' in line:
                level = 'DEBUG'

            # 识别组件
            component = 'UNKNOWN'
            if 'MT4' in line:
                component = 'MT4'
            elif '交易' in line or 'trading' in line:
                component = 'TRADING'
            elif '策略' in line or 'strategy' in line:
                component = 'STRATEGY'
            elif '风险' in line or 'risk' in line:
                component = 'RISK'
            elif '数据' in line or 'data' in line:
                component = 'DATA'

            return LogEvent(
                timestamp=timestamp,
                level=level,
                component=component,
                message=line.strip(),
                raw_line=line
            )

        except Exception as e:
            self.logger.debug(f"解析日志行失败: {e}")
            return None

    def _analyze_logs(self):
        """分析日志事件"""
        try:
            if not self.log_buffer:
                return

            # 分析最近的日志事件
            recent_events = [event for event in self.log_buffer
                           if (datetime.now() - event.timestamp).seconds < 300]  # 最近5分钟

            # 检测问题模式
            for issue_type, pattern_config in self.issue_patterns.items():
                self._detect_issue_pattern(issue_type, pattern_config, recent_events)

        except Exception as e:
            self.logger.error(f"[ERROR] 分析日志失败: {e}")

    def _detect_issue_pattern(self, issue_type: str, pattern_config: Dict, events: List[LogEvent]):
        """检测问题模式"""
        try:
            patterns = pattern_config['patterns']
            severity = pattern_config['severity']
            description = pattern_config['description']

            # 查找匹配的事件
            matching_events = []
            for event in events:
                for pattern in patterns:
                    if re.search(pattern, event.message, re.IGNORECASE):
                        matching_events.append(event)
                        break

            if matching_events:
                # 更新或创建问题记录
                if issue_type in self.detected_issues:
                    issue = self.detected_issues[issue_type]
                    issue.count += len(matching_events)
                    issue.evidence.extend([event.message for event in matching_events[-3:]])  # 最近3个证据
                    issue.evidence = issue.evidence[-10:]  # 保留最近10个证据
                else:
                    self.detected_issues[issue_type] = SystemIssue(
                        issue_type=issue_type,
                        severity=severity,
                        component=matching_events[0].component,
                        description=description,
                        evidence=[event.message for event in matching_events[-3:]],
                        first_seen=matching_events[0].timestamp,
                        count=len(matching_events)
                    )

        except Exception as e:
            self.logger.debug(f"检测问题模式失败: {e}")

    def _update_system_status(self):
        """更新系统状态"""
        try:
            if not self.log_buffer:
                return

            # 分析最近的系统状态
            recent_events = [event for event in self.log_buffer
                           if (datetime.now() - event.timestamp).seconds < 60]  # 最近1分钟

            status_counts = {}
            for category, keywords in self.status_keywords.items():
                count = 0
                for event in recent_events:
                    for keyword in keywords:
                        if keyword in event.message.lower():
                            count += 1
                            break
                status_counts[category] = count

            self.system_status = {
                'last_update': datetime.now(),
                'recent_activity': status_counts,
                'total_events': len(self.log_buffer),
                'error_rate': len([e for e in recent_events if e.level == 'ERROR']) / max(len(recent_events), 1),
                'warning_rate': len([e for e in recent_events if e.level == 'WARNING']) / max(len(recent_events), 1)
            }

        except Exception as e:
            self.logger.debug(f"更新系统状态失败: {e}")

    def _report_issues(self):
        """报告问题"""
        try:
            # 报告严重问题
            critical_issues = [issue for issue in self.detected_issues.values()
                             if issue.severity in ['high', 'critical'] and issue.count >= self.config['issue_threshold']]

            for issue in critical_issues:
                self.logger.warning(f"[ALERT] {issue.description}: 检测到{issue.count}次")
                self.logger.warning(f"   组件: {issue.component}")
                self.logger.warning(f"   最新证据: {issue.evidence[-1] if issue.evidence else 'N/A'}")

            # 定期状态报告
            if self.system_status:
                activity = self.system_status['recent_activity']
                error_rate = self.system_status['error_rate']

                if error_rate > 0.1:  # 错误率超过10%
                    self.logger.warning(f"[WARN] 系统错误率较高: {error_rate:.1%}")

                # 报告系统活动
                active_components = [k for k, v in activity.items() if v > 0]
                if active_components:
                    self.logger.info(f"[DATA] 系统活动: {', '.join(active_components)}")

        except Exception as e:
            self.logger.debug(f"报告问题失败: {e}")

    def _generate_final_report(self):
        """生成最终报告"""
        try:
            self.logger.info("=" * 80)
            self.logger.info("[DATA] 智能日志分析报告")
            self.logger.info("=" * 80)

            # 问题总结
            if self.detected_issues:
                self.logger.info("[ALERT] 检测到的问题:")
                for issue_type, issue in self.detected_issues.items():
                    severity_icon = {'low': '[INFO]', 'medium': '[WARN]', 'high': '[ALERT]', 'critical': '[CRITICAL]'}
                    icon = severity_icon.get(issue.severity, '[INFO]')
                    self.logger.info(f"   {icon} {issue.description}: {issue.count}次")
                    self.logger.info(f"      首次发现: {issue.first_seen.strftime('%H:%M:%S')}")
                    self.logger.info(f"      最新证据: {issue.evidence[-1] if issue.evidence else 'N/A'}")
            else:
                self.logger.info("[OK] 未检测到严重问题")

            # 系统状态总结
            if self.system_status:
                self.logger.info("")
                self.logger.info("[DATA] 系统状态总结:")
                activity = self.system_status['recent_activity']
                self.logger.info(f"   总日志事件: {self.system_status['total_events']}")
                self.logger.info(f"   错误率: {self.system_status['error_rate']:.1%}")
                self.logger.info(f"   警告率: {self.system_status['warning_rate']:.1%}")
                self.logger.info(f"   活跃组件: {[k for k, v in activity.items() if v > 0]}")

            # 建议
            self.logger.info("")
            self.logger.info("[TIP] 分析建议:")

            high_priority_issues = [issue for issue in self.detected_issues.values()
                                  if issue.severity in ['high', 'critical']]

            if high_priority_issues:
                self.logger.info("   1. 优先解决高优先级问题")
                for issue in high_priority_issues[:3]:  # 前3个
                    self.logger.info(f"      - {issue.description}")

            if self.system_status and self.system_status['error_rate'] > 0.05:
                self.logger.info("   2. 系统错误率较高，建议检查配置")

            if not self.detected_issues:
                self.logger.info("   1. 系统运行正常，继续监控")

            self.logger.info("=" * 80)

        except Exception as e:
            self.logger.error(f"[ERROR] 生成最终报告失败: {e}")

    def get_current_status(self) -> Dict:
        """获取当前状态"""
        return {
            'is_monitoring': self.is_monitoring,
            'total_events': len(self.log_buffer),
            'detected_issues': len(self.detected_issues),
            'system_status': self.system_status,
            'recent_issues': [
                {
                    'type': issue.issue_type,
                    'description': issue.description,
                    'severity': issue.severity,
                    'count': issue.count
                }
                for issue in list(self.detected_issues.values())[-5:]  # 最近5个问题
            ]
        }

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'intelligent_log_analyzer_{datetime.now().strftime("%Y%m%d")}.log', encoding='utf-8')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("[START] 智能日志分析器")
    logger.info("[INFO] 实时分析Pro系统日志，智能识别问题")
    logger.info("[SCAN] 自动检测异常模式，生成可读性报告")
    logger.info("=" * 80)

    # 创建分析器
    analyzer = IntelligentLogAnalyzer()

    try:
        # 启动分析
        analyzer.start_monitoring()

        logger.info("[SCAN] 智能日志分析进行中...")
        logger.info("[TIP] 按 Ctrl+C 停止分析")

        # 定期输出状态
        while analyzer.is_monitoring:
            time.sleep(30)  # 每30秒输出一次状态
            status = analyzer.get_current_status()
            logger.info(f"[DATA] 分析状态: 事件{status['total_events']}个, 问题{status['detected_issues']}个")

    except KeyboardInterrupt:
        logger.info("[STOP] 收到停止信号")
    except Exception as e:
        logger.error(f"[ERROR] 分析器运行异常: {e}")
    finally:
        # 停止分析
        analyzer.stop_monitoring()

if __name__ == "__main__":
    main()
