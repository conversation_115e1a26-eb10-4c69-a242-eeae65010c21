#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
远程更新客户端
用于远程检查和执行系统更新
"""

import os
import sys
import json
import requests
import argparse
from datetime import datetime

class RemoteUpdateClient:
    """远程更新客户端"""
    
    def __init__(self, server_url, auth_token):
        self.server_url = server_url.rstrip('/')
        self.auth_token = auth_token
        self.headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }
    
    def get_current_version(self):
        """获取当前版本"""
        try:
            response = requests.get(
                f'{self.server_url}/api/update/version',
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return data.get('data', {})
            
            return None
            
        except Exception as e:
            print(f"❌ 获取版本信息失败: {e}")
            return None
    
    def check_update(self, current_version):
        """检查更新"""
        try:
            payload = {'version': current_version}
            
            response = requests.post(
                f'{self.server_url}/api/update/check',
                headers=self.headers,
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return data.get('data', {})
            elif response.status_code == 401:
                print("❌ 认证失败，请检查更新令牌")
            else:
                print(f"❌ 检查更新失败，状态码: {response.status_code}")
            
            return None
            
        except Exception as e:
            print(f"❌ 检查更新失败: {e}")
            return None
    
    def apply_update(self, update_file=None):
        """应用更新"""
        try:
            payload = {'update_file': update_file or 'latest.zip'}
            
            response = requests.post(
                f'{self.server_url}/api/update/apply',
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return data
            elif response.status_code == 401:
                print("❌ 认证失败，请检查更新令牌")
            else:
                print(f"❌ 应用更新失败，状态码: {response.status_code}")
            
            return None
            
        except Exception as e:
            print(f"❌ 应用更新失败: {e}")
            return None
    
    def get_update_status(self):
        """获取更新状态"""
        try:
            response = requests.get(
                f'{self.server_url}/api/update/status',
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return data.get('data', {})
            
            return None
            
        except Exception as e:
            print(f"❌ 获取更新状态失败: {e}")
            return None
    
    def rollback(self, target_version):
        """回滚版本"""
        try:
            payload = {'target_version': target_version}
            
            response = requests.post(
                f'{self.server_url}/api/update/rollback',
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return data
            
            return None
            
        except Exception as e:
            print(f"❌ 回滚失败: {e}")
            return None

def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(description='外汇交易系统远程更新客户端')
    parser.add_argument('--server', required=True, help='服务器地址 (例: http://your-server.com:5000)')
    parser.add_argument('--token', required=True, help='更新认证令牌')
    parser.add_argument('--action', choices=['check', 'update', 'status', 'rollback'], 
                       default='check', help='执行的操作')
    parser.add_argument('--version', help='目标版本（用于回滚）')
    
    args = parser.parse_args()
    
    print("🚀 外汇交易系统远程更新客户端")
    print("=" * 50)
    print(f"📊 服务器: {args.server}")
    print(f"🔧 操作: {args.action}")
    print("=" * 50)
    
    # 创建客户端
    client = RemoteUpdateClient(args.server, args.token)
    
    if args.action == 'check':
        # 检查更新
        print("🔍 获取当前版本信息...")
        version_info = client.get_current_version()
        
        if version_info:
            current_version = version_info.get('version', '未知')
            print(f"📊 当前版本: {current_version}")
            print(f"📅 构建日期: {version_info.get('build_date', '未知')}")
            print(f"🏗️ 构建号: {version_info.get('build_number', '未知')}")
            
            print("\n🔍 检查可用更新...")
            update_info = client.check_update(current_version)
            
            if update_info:
                if update_info.get('has_update'):
                    latest_version = update_info.get('latest_version')
                    print(f"✅ 发现新版本: {latest_version}")
                    print("📝 更新说明:")
                    for note in update_info.get('update_notes', []):
                        print(f"   • {note}")
                    print(f"\n💾 下载地址: {update_info.get('download_url', '未提供')}")
                else:
                    print("✅ 当前已是最新版本")
            else:
                print("❌ 检查更新失败")
        else:
            print("❌ 获取版本信息失败")
    
    elif args.action == 'update':
        # 执行更新
        print("🔄 开始执行更新...")
        result = client.apply_update()
        
        if result:
            print(f"✅ {result.get('message')}")
            print(f"⏱️ 预计时间: {result.get('estimated_time', '未知')}")
        else:
            print("❌ 更新执行失败")
    
    elif args.action == 'status':
        # 获取状态
        print("📊 获取更新状态...")
        status = client.get_update_status()
        
        if status:
            print(f"🔄 更新中: {'是' if status.get('is_updating') else '否'}")
            print(f"📈 进度: {status.get('progress', 0)}%")
            print(f"💬 状态: {status.get('message', '未知')}")
            print(f"📅 最后更新: {status.get('last_update', '未知')}")
        else:
            print("❌ 获取状态失败")
    
    elif args.action == 'rollback':
        # 回滚版本
        if not args.version:
            print("❌ 回滚操作需要指定目标版本 (--version)")
            return
        
        print(f"🔄 回滚到版本 {args.version}...")
        result = client.rollback(args.version)
        
        if result:
            print(f"✅ {result.get('message')}")
            print(f"⏱️ 预计时间: {result.get('estimated_time', '未知')}")
        else:
            print("❌ 回滚失败")

if __name__ == "__main__":
    main()
