#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试训练调度器功能
验证各种触发机制是否正常工作
"""

import sys
import os
import logging
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_scheduler_import():
    """测试调度器导入"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 测试调度器导入...")
    
    try:
        # 测试schedule模块
        import schedule
        logger.info("✅ schedule模块导入成功")
        
        # 测试配置导入
        try:
            from config.training_config import TrainingConfig
            config = TrainingConfig()
            logger.info("✅ TrainingConfig导入成功")
            logger.info(f"   重训练频率: {config.TRAINING_FLOW['schedule']['retrain_frequency']}")
        except Exception as e:
            logger.warning(f"⚠️ TrainingConfig导入失败: {e}")
            logger.info("   将使用简化配置")
        
        # 测试调度器导入
        from core.training_scheduler import training_scheduler
        logger.info("✅ 训练调度器导入成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 调度器导入失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_scheduler_basic_functions():
    """测试调度器基本功能"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 测试调度器基本功能...")
    
    try:
        from core.training_scheduler import training_scheduler
        
        # 测试获取状态
        status = training_scheduler.get_scheduler_status()
        logger.info("✅ 获取调度器状态成功")
        logger.info(f"   运行状态: {status.get('is_running', False)}")
        logger.info(f"   触发次数: {status.get('trigger_count', 0)}")
        
        # 测试手动触发
        result = training_scheduler.force_trigger_training("测试触发")
        if result:
            logger.info("✅ 手动触发训练成功")
        else:
            logger.warning("⚠️ 手动触发训练失败")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 调度器基本功能测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_scheduler_start_stop():
    """测试调度器启动停止"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 测试调度器启动停止...")
    
    try:
        from core.training_scheduler import training_scheduler
        
        # 测试启动
        logger.info("🚀 启动调度器...")
        training_scheduler.start_scheduler()
        time.sleep(2)  # 等待2秒
        
        # 检查状态
        status = training_scheduler.get_scheduler_status()
        if status.get('is_running', False):
            logger.info("✅ 调度器启动成功")
        else:
            logger.warning("⚠️ 调度器启动失败")
        
        # 测试停止
        logger.info("🛑 停止调度器...")
        training_scheduler.stop_scheduler()
        time.sleep(1)  # 等待1秒
        
        # 检查状态
        status = training_scheduler.get_scheduler_status()
        if not status.get('is_running', True):
            logger.info("✅ 调度器停止成功")
        else:
            logger.warning("⚠️ 调度器停止失败")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 调度器启动停止测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_trigger_mechanisms():
    """测试触发机制"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 测试触发机制...")
    
    try:
        from core.training_scheduler import training_scheduler
        
        # 测试数据驱动触发检查
        logger.info("📊 测试数据驱动触发检查...")
        training_scheduler._check_data_trigger("测试数据检查")
        logger.info("✅ 数据驱动触发检查完成")
        
        # 测试性能驱动触发检查
        logger.info("📈 测试性能驱动触发检查...")
        training_scheduler._check_performance_trigger("测试性能检查")
        logger.info("✅ 性能驱动触发检查完成")
        
        # 检查触发历史
        status = training_scheduler.get_scheduler_status()
        recent_triggers = status.get('recent_triggers', [])
        logger.info(f"📋 最近触发记录: {len(recent_triggers)} 条")
        
        for trigger in recent_triggers[-3:]:  # 显示最近3条
            logger.info(f"   - {trigger['timestamp']}: {trigger['reason']} ({trigger['type']})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 触发机制测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_schedule_setup():
    """测试定时任务设置"""
    logger = logging.getLogger(__name__)
    logger.info("🧪 测试定时任务设置...")
    
    try:
        import schedule
        
        # 清除现有任务
        schedule.clear()
        
        # 设置测试任务
        def test_job():
            logger.info("⏰ 测试定时任务执行")
        
        # 设置每分钟执行的测试任务
        schedule.every().minute.do(test_job)
        
        # 检查任务数量
        jobs = schedule.get_jobs()
        logger.info(f"✅ 定时任务设置成功，当前任务数: {len(jobs)}")
        
        # 清除测试任务
        schedule.clear()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 定时任务设置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 开始训练调度器功能测试")
    logger.info("=" * 60)
    
    test_results = []
    
    # 1. 测试导入
    logger.info("1️⃣ 测试调度器导入")
    test_results.append(("调度器导入", test_scheduler_import()))
    
    # 2. 测试基本功能
    logger.info("2️⃣ 测试调度器基本功能")
    test_results.append(("基本功能", test_scheduler_basic_functions()))
    
    # 3. 测试启动停止
    logger.info("3️⃣ 测试调度器启动停止")
    test_results.append(("启动停止", test_scheduler_start_stop()))
    
    # 4. 测试触发机制
    logger.info("4️⃣ 测试触发机制")
    test_results.append(("触发机制", test_trigger_mechanisms()))
    
    # 5. 测试定时任务
    logger.info("5️⃣ 测试定时任务设置")
    test_results.append(("定时任务", test_schedule_setup()))
    
    # 总结测试结果
    logger.info("=" * 60)
    logger.info("📊 测试结果总结:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info("")
    logger.info(f"🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！训练调度器功能正常！")
        logger.info("")
        logger.info("🚀 调度器功能特性:")
        logger.info("   ✅ 定时触发 - 支持每日/每周/每小时训练")
        logger.info("   ✅ 数据驱动 - 检测新数据自动触发训练")
        logger.info("   ✅ 性能驱动 - 监控模型性能自动重训练")
        logger.info("   ✅ 手动触发 - 支持手动强制触发")
        logger.info("   ✅ 状态监控 - 完整的运行状态和历史记录")
    else:
        logger.warning(f"⚠️ {total - passed} 个测试失败，需要进一步调试")
        logger.info("")
        logger.info("🔧 可能的问题:")
        logger.info("   - 依赖模块未安装")
        logger.info("   - 配置文件路径问题")
        logger.info("   - 权限或环境问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
    input("按任意键退出...")
