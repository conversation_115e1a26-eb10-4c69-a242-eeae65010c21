# MT4连接指南（更新版）

本文档提供了如何将MT4客户端与外汇交易系统连接的详细步骤，以及最新的用户界面改进。

## 前提条件

- 已安装MT4客户端
- 已安装ZeroMQ库
- 已安装Python 3.8+

## 安装ZeroMQ库

### Windows

1. 下载ZeroMQ库：https://zeromq.org/download/
2. 安装Visual Studio C++构建工具
3. 在项目目录中运行：`pip install pyzmq`

### Linux

```bash
sudo apt-get install libzmq3-dev
pip install pyzmq
```

### macOS

```bash
brew install zeromq
pip install pyzmq
```

## 安装MT4 ZeroMQ插件

1. 下载MT4 ZeroMQ插件：https://github.com/dingmaotu/mql-zmq
2. 将插件文件复制到MT4的`MQL4/Libraries`目录
3. 将示例EA复制到MT4的`MQL4/Experts`目录

## 配置MT4 EA

1. 打开MT4客户端
2. 打开MetaEditor（按F4）
3. 创建一个新的EA或使用示例EA
4. 确保EA包含以下代码：

```cpp
#include <Zmq/Zmq.mqh>

// ZeroMQ Context
Context context;

// Socket to talk to clients
Socket socket(context, ZMQ_REP);

// 初始化
int OnInit()
{
    // 绑定到端口5555
    socket.bind("tcp://*:5555");
    Print("Server started, waiting for requests...");
    return(INIT_SUCCEEDED);
}

// 清理
void OnDeinit(const int reason)
{
    socket.unbind("tcp://*:5555");
    Print("Server stopped");
}

// 主循环
void OnTick()
{
    // 检查是否有新消息
    if(socket.poll(0))
    {
        // 接收消息
        ZmqMsg request;
        socket.recv(request);
        string requestString = request.getData();
        Print("Received request: ", requestString);

        // 处理请求
        string responseString = ProcessRequest(requestString);

        // 发送响应
        ZmqMsg response(responseString);
        socket.send(response);
    }
}

// 处理请求
string ProcessRequest(string requestString)
{
    // 解析JSON请求
    // 执行相应的操作
    // 返回JSON响应
    return "{\"status\":\"success\",\"message\":\"Request processed\"}";
}
```

5. 编译EA
6. 将EA添加到图表

## 配置MT4Client

1. 将`MT4Client.mq4`文件复制到MT4的`MQL4/Experts`目录
2. 在MetaEditor中打开并编译
3. 将EA添加到图表

### MT4Client功能

MT4Client提供以下功能：

- **用户注册和登录**：通过内置对话框进行用户注册和登录
- **信号接收**：接收来自服务器的交易信号
- **自动交易**：根据接收到的信号自动执行交易
- **账户信息**：显示账户余额、净值和可用保证金
- **手数调整**：根据账户余额自动调整交易手数

### 用户界面改进

最新版本的MT4Client提供了以下用户界面改进：

- **内置对话框**：使用MT4内置对话框进行用户注册和登录，无需使用记事本
- **黑色主题**：所有对话框和界面元素采用黑色背景，符合MT4的整体风格
- **直观的按钮**：清晰的按钮标签和颜色，方便用户操作
- **实时反馈**：操作过程中提供实时反馈，提高用户体验

## 配置外汇交易系统

1. 打开`.env`文件
2. 设置MT4服务器地址：

```
MT4_SERVER_ADDRESS=tcp://127.0.0.1:5555
```

3. 保存文件

## 测试连接

1. 确保MT4客户端正在运行，并且EA已添加到图表
2. 启动外汇交易系统：`python run.py`
3. 测试连接：

```
curl http://localhost:5000/api/forex-trading/positions
```

如果连接成功，应该会返回当前持仓信息。

## 使用MT4Client

1. 在MT4中添加MT4Client到图表
2. **注册新用户**：
   - 点击"注册"按钮，在弹出的对话框中输入用户ID和用户名
   - 点击"注册"按钮完成注册
   - 系统会自动生成授权码并登录
3. **登录已有账户**：
   - 点击"登录"按钮，在弹出的对话框中输入用户名和密码
   - 点击"登录"按钮完成登录
   - 系统会自动获取授权码并保存
4. 点击"获取信号"按钮，接收最新的交易信号
5. 点击"账户信息"按钮，查看账户详情和手数调整
6. 点击"自动交易"按钮，切换自动交易状态

### 登录界面改进

最新版本的MT4Client提供了以下登录界面改进：

1. **账号密码登录**：使用用户名和密码登录，无需记忆复杂的授权码
2. **增强的视觉层次**：登录和注册对话框使用黄色边框和黑色背景，在K线图上更加醒目
3. **密码保护**：密码输入框使用掩码显示，保护用户隐私
4. **直观的按钮布局**：登录和取消按钮位置合理，操作更加直观

## 故障排除

### 连接失败

- 确保MT4客户端正在运行
- 确保EA已添加到图表并且正在运行
- 检查防火墙设置，确保端口5555未被阻止
- 检查ZeroMQ库是否正确安装

### 无法安装ZeroMQ

- 确保已安装Visual Studio C++构建工具（Windows）
- 确保已安装libzmq3-dev（Linux）
- 尝试使用管理员权限运行安装命令

### 交易执行失败

- 检查MT4账户是否有足够的资金
- 检查MT4账户是否允许自动交易
- 检查EA是否有足够的权限执行交易
- 检查交易参数是否有效

### 对话框显示问题

- 确保MT4客户端有足够的权限创建对象
- 尝试重新启动MT4客户端
- 检查是否有其他EA占用了相同的对象名称

## 注意事项

- 确保MT4客户端始终运行，否则交易将无法执行
- 定期检查MT4日志，确保EA正常运行
- 考虑使用VPS运行MT4客户端，确保24/7可用性
- 在实盘交易前，先在模拟账户上测试系统
