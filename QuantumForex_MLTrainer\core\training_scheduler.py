#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
训练调度器
实现定时触发、数据驱动触发、性能驱动触发
"""

import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd
from pathlib import Path

# 尝试导入schedule，如果没有则安装
try:
    import schedule
except ImportError:
    print("正在安装schedule模块...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "schedule"])
    import schedule

# 导入配置
try:
    from config.training_config import TrainingConfig
except ImportError:
    # 如果导入失败，创建一个简化的配置类
    class TrainingConfig:
        TRAINING_FLOW = {
            'schedule': {
                'auto_retrain': True,
                'retrain_frequency': 'daily',
                'retrain_threshold': 0.05,
                'max_training_time': 7200
            }
        }

class TrainingScheduler:
    """训练调度器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = TrainingConfig()

        # 调度状态
        self.is_running = False
        self.scheduler_thread = None

        # 触发历史
        self.trigger_history = []
        self.last_data_check = None
        self.last_performance_check = None

        # 数据驱动配置
        self.data_trigger_config = {
            'min_new_records': 1000,      # 最少新增记录数
            'check_interval_hours': 6,    # 检查间隔
            'data_freshness_hours': 24    # 数据新鲜度要求
        }

        # 性能驱动配置
        self.performance_trigger_config = {
            'performance_threshold': 0.05,  # 性能下降阈值
            'check_interval_hours': 12,     # 检查间隔
            'min_evaluation_samples': 100   # 最少评估样本
        }

    def start_scheduler(self):
        """启动调度器"""
        try:
            if self.is_running:
                self.logger.warning("调度器已在运行")
                return

            self.logger.info("🚀 启动训练调度器...")

            # 设置定时任务
            self._setup_scheduled_tasks()

            # 启动调度线程
            self.is_running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()

            self.logger.info("✅ 训练调度器启动成功")

        except Exception as e:
            self.logger.error(f"❌ 启动调度器失败: {e}")

    def stop_scheduler(self):
        """停止调度器"""
        try:
            self.logger.info("🛑 停止训练调度器...")
            self.is_running = False

            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=5)

            schedule.clear()
            self.logger.info("✅ 训练调度器已停止")

        except Exception as e:
            self.logger.error(f"❌ 停止调度器失败: {e}")

    def _setup_scheduled_tasks(self):
        """设置定时任务"""
        try:
            # 获取配置
            flow_config = self.config.TRAINING_FLOW['schedule']
            frequency = flow_config.get('retrain_frequency', 'daily')

            # 定时重训练
            if frequency == 'daily':
                schedule.every().day.at("02:00").do(self._trigger_scheduled_training, "定时训练(每日)")
            elif frequency == 'weekly':
                schedule.every().monday.at("02:00").do(self._trigger_scheduled_training, "定时训练(每周)")
            elif frequency == 'hourly':
                schedule.every().hour.do(self._trigger_scheduled_training, "定时训练(每小时)")

            # 数据驱动检查
            schedule.every(self.data_trigger_config['check_interval_hours']).hours.do(
                self._check_data_trigger, "数据驱动检查"
            )

            # 性能驱动检查
            schedule.every(self.performance_trigger_config['check_interval_hours']).hours.do(
                self._check_performance_trigger, "性能驱动检查"
            )

            self.logger.info(f"📅 定时任务设置完成: {frequency}重训练")

        except Exception as e:
            self.logger.error(f"❌ 设置定时任务失败: {e}")

    def _run_scheduler(self):
        """运行调度器主循环"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                self.logger.error(f"❌ 调度器运行异常: {e}")
                time.sleep(60)

    def _trigger_scheduled_training(self, reason: str):
        """触发定时训练"""
        try:
            self.logger.info(f"⏰ 触发训练: {reason}")

            # 记录触发历史
            self._record_trigger(reason, 'scheduled')

            # 执行训练
            self._execute_training(reason)

        except Exception as e:
            self.logger.error(f"❌ 定时训练失败: {e}")

    def _check_data_trigger(self, reason: str):
        """检查数据驱动触发"""
        try:
            self.logger.info(f"📊 {reason}")

            # 检查新数据量
            new_data_count = self._count_new_data()

            if new_data_count >= self.data_trigger_config['min_new_records']:
                self.logger.info(f"🔄 发现{new_data_count}条新数据，触发训练")

                # 记录触发历史
                self._record_trigger(f"新增{new_data_count}条数据", 'data_driven')

                # 执行训练
                self._execute_training(f"数据驱动训练(新增{new_data_count}条)")
            else:
                self.logger.info(f"📊 新数据量不足: {new_data_count}/{self.data_trigger_config['min_new_records']}")

            self.last_data_check = datetime.now()

        except Exception as e:
            self.logger.error(f"❌ 数据驱动检查失败: {e}")

    def _check_performance_trigger(self, reason: str):
        """检查性能驱动触发"""
        try:
            self.logger.info(f"📈 {reason}")

            # 评估当前模型性能
            performance_decline = self._evaluate_model_performance()

            threshold = self.performance_trigger_config['performance_threshold']

            if performance_decline >= threshold:
                self.logger.info(f"📉 模型性能下降{performance_decline:.2%}，触发重训练")

                # 记录触发历史
                self._record_trigger(f"性能下降{performance_decline:.2%}", 'performance_driven')

                # 执行训练
                self._execute_training(f"性能驱动训练(下降{performance_decline:.2%})")
            else:
                self.logger.info(f"📈 模型性能正常: 下降{performance_decline:.2%}/{threshold:.2%}")

            self.last_performance_check = datetime.now()

        except Exception as e:
            self.logger.error(f"❌ 性能驱动检查失败: {e}")

    def _count_new_data(self) -> int:
        """统计新数据量"""
        try:
            # 这里需要根据实际数据源实现
            # 示例：检查数据库中的新记录

            if not self.last_data_check:
                # 首次检查，设置基准时间
                self.last_data_check = datetime.now() - timedelta(hours=24)

            # 模拟数据检查（实际应该连接数据库）
            # 这里返回模拟的新数据量
            import random
            return random.randint(500, 2000)

        except Exception as e:
            self.logger.error(f"❌ 统计新数据失败: {e}")
            return 0

    def _evaluate_model_performance(self) -> float:
        """评估模型性能下降程度"""
        try:
            # 这里需要实现实际的模型性能评估
            # 示例：比较最近预测结果与实际结果

            # 模拟性能评估（实际应该加载模型并评估）
            import random
            performance_decline = random.uniform(0.0, 0.1)  # 0-10%的性能下降

            return performance_decline

        except Exception as e:
            self.logger.error(f"❌ 评估模型性能失败: {e}")
            return 0.0

    def _execute_training(self, reason: str):
        """执行训练"""
        try:
            self.logger.info(f"🚀 开始执行训练: {reason}")

            # 这里调用实际的训练脚本
            # 可以导入并调用现有的训练模块

            # 示例：调用简单训练脚本
            import subprocess
            import sys

            # 获取当前脚本目录
            current_dir = Path(__file__).parent.parent
            training_script = current_dir / "run_simple_training.py"

            if training_script.exists():
                # 在后台执行训练
                process = subprocess.Popen(
                    [sys.executable, str(training_script)],
                    cwd=str(current_dir),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    encoding='utf-8'
                )

                self.logger.info(f"训练进程已启动: PID {process.pid}")
            else:
                self.logger.warning("训练脚本不存在，跳过执行")

        except Exception as e:
            self.logger.error(f"❌ 执行训练失败: {e}")

    def _record_trigger(self, reason: str, trigger_type: str):
        """记录触发历史"""
        try:
            trigger_record = {
                'timestamp': datetime.now(),
                'reason': reason,
                'type': trigger_type
            }

            self.trigger_history.append(trigger_record)

            # 保持最近100条记录
            if len(self.trigger_history) > 100:
                self.trigger_history = self.trigger_history[-100:]

        except Exception as e:
            self.logger.error(f"❌ 记录触发历史失败: {e}")

    def get_scheduler_status(self) -> Dict:
        """获取调度器状态"""
        try:
            return {
                'is_running': self.is_running,
                'last_data_check': self.last_data_check.isoformat() if self.last_data_check else None,
                'last_performance_check': self.last_performance_check.isoformat() if self.last_performance_check else None,
                'trigger_count': len(self.trigger_history),
                'recent_triggers': [
                    {
                        'timestamp': t['timestamp'].isoformat(),
                        'reason': t['reason'],
                        'type': t['type']
                    }
                    for t in self.trigger_history[-5:]  # 最近5次触发
                ]
            }
        except Exception as e:
            self.logger.error(f"❌ 获取调度器状态失败: {e}")
            return {'error': str(e)}

    def force_trigger_training(self, reason: str = "手动触发"):
        """手动强制触发训练"""
        try:
            self.logger.info(f"🔧 手动触发训练: {reason}")

            # 记录触发历史
            self._record_trigger(reason, 'manual')

            # 执行训练
            self._execute_training(reason)

            return True

        except Exception as e:
            self.logger.error(f"❌ 手动触发训练失败: {e}")
            return False

# 创建全局实例
training_scheduler = TrainingScheduler()
