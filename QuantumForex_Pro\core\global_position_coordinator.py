#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全局持仓协调器
解决多策略模块之间的持仓冲突和重复订单问题
确保所有交易决策都经过统一的持仓状态检查
"""

import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

@dataclass
class PendingOrder:
    """待执行订单"""
    symbol: str
    action: str
    volume: float
    strategy_source: str
    timestamp: datetime
    priority: int = 1

class GlobalPositionCoordinator:
    """全局持仓协调器"""
    
    def __init__(self, trade_executor=None):
        self.trade_executor = trade_executor
        self.logger = None
        
        # 线程锁，确保持仓检查的原子性
        self._position_lock = threading.RLock()
        self._order_lock = threading.RLock()
        
        # 待执行订单队列
        self.pending_orders: List[PendingOrder] = []
        
        # 最近执行的订单记录（防止短时间重复）
        self.recent_orders: Dict[str, datetime] = {}
        
        # 配置参数
        self.config = {
            # 持仓限制
            'max_positions_per_symbol': 2,
            'max_same_direction_per_symbol': 1,
            'max_total_positions': 10,
            
            # 重复订单防护
            'min_order_interval_seconds': 30,  # 同一货币对最小下单间隔30秒
            'min_price_difference_points': 20,  # 最小价格差异20点
            
            # 策略协调
            'max_pending_orders': 5,
            'order_execution_delay_seconds': 2,  # 订单执行延迟2秒，给其他策略检查时间
        }
        
        # 策略优先级
        self.strategy_priority = {
            'risk_management': 10,      # 风险管理最高优先级
            'position_management': 9,   # 持仓管理
            'combo_trading': 8,         # 组合交易
            'technical_analysis': 7,    # 技术分析
            'signal_fusion': 6,         # 信号融合
            'default': 5                # 默认优先级
        }
    
    def request_trade_execution(self, symbol: str, action: str, volume: float, 
                              strategy_source: str = 'default') -> Dict:
        """请求交易执行（全局协调入口）"""
        try:
            with self._position_lock:
                print(f"🔒 全局协调器收到交易请求: {symbol} {action} {volume}手 (来源: {strategy_source})")
                
                # 1. 立即检查是否允许执行
                immediate_check = self._immediate_execution_check(symbol, action, volume, strategy_source)
                
                if not immediate_check['allow']:
                    print(f"❌ 交易请求被拒绝: {immediate_check['reason']}")
                    return {
                        'success': False,
                        'reason': immediate_check['reason'],
                        'action': 'rejected',
                        'details': immediate_check
                    }
                
                # 2. 检查是否需要延迟执行
                if immediate_check.get('delay_execution', False):
                    print(f"⏳ 交易请求加入待执行队列")
                    self._add_to_pending_queue(symbol, action, volume, strategy_source)
                    return {
                        'success': True,
                        'reason': '已加入执行队列',
                        'action': 'queued',
                        'details': immediate_check
                    }
                
                # 3. 立即执行
                print(f"⚡ 立即执行交易请求")
                execution_result = self._execute_trade_immediately(symbol, action, volume, strategy_source)
                
                return execution_result
                
        except Exception as e:
            print(f"❌ 全局协调器异常: {e}")
            return {
                'success': False,
                'reason': f'协调器异常: {e}',
                'action': 'error',
                'details': {}
            }
    
    def _immediate_execution_check(self, symbol: str, action: str, volume: float, strategy_source: str) -> Dict:
        """立即执行检查"""
        try:
            # 1. 检查最近订单间隔
            recent_check = self._check_recent_order_interval(symbol)
            if not recent_check['allow']:
                return recent_check
            
            # 2. 检查当前持仓状态
            position_check = self._check_current_positions(symbol, action)
            if not position_check['allow']:
                return position_check
            
            # 3. 检查待执行队列中的冲突
            queue_check = self._check_pending_queue_conflicts(symbol, action)
            if not queue_check['allow']:
                return queue_check
            
            # 4. 检查价格相似性
            price_check = self._check_price_similarity(symbol, action)
            if not price_check['allow']:
                return price_check
            
            # 5. 检查策略优先级
            priority_check = self._check_strategy_priority(strategy_source)
            
            return {
                'allow': True,
                'reason': '通过所有检查',
                'delay_execution': priority_check.get('delay_execution', False),
                'details': {
                    'recent_check': recent_check,
                    'position_check': position_check,
                    'queue_check': queue_check,
                    'price_check': price_check,
                    'priority_check': priority_check
                }
            }
            
        except Exception as e:
            return {
                'allow': False,
                'reason': f'检查异常: {e}',
                'details': {}
            }
    
    def _check_recent_order_interval(self, symbol: str) -> Dict:
        """检查最近订单间隔"""
        try:
            if symbol in self.recent_orders:
                last_order_time = self.recent_orders[symbol]
                time_diff = (datetime.now() - last_order_time).total_seconds()
                
                if time_diff < self.config['min_order_interval_seconds']:
                    return {
                        'allow': False,
                        'reason': f'{symbol}距离上次下单仅{time_diff:.1f}秒，小于最小间隔{self.config["min_order_interval_seconds"]}秒',
                        'time_diff': time_diff
                    }
            
            return {'allow': True, 'reason': '时间间隔检查通过'}
            
        except Exception as e:
            return {'allow': False, 'reason': f'时间间隔检查异常: {e}'}
    
    def _check_current_positions(self, symbol: str, action: str) -> Dict:
        """检查当前持仓状态"""
        try:
            if not self.trade_executor:
                return {'allow': True, 'reason': '无交易执行器，跳过持仓检查'}
            
            # 获取真实持仓状态
            mt4_positions = self.trade_executor._get_mt4_real_positions(symbol)
            
            # 统计持仓
            total_positions = len(mt4_positions) if mt4_positions else 0
            same_direction_count = 0
            
            if mt4_positions:
                for pos in mt4_positions:
                    pos_action = pos.get('action', pos.get('type', ''))
                    # 处理MT4的订单类型格式
                    if pos_action in ['0', 'OP_BUY']:
                        pos_action = 'BUY'
                    elif pos_action in ['1', 'OP_SELL']:
                        pos_action = 'SELL'
                    
                    if pos_action == action:
                        same_direction_count += 1
            
            # 检查限制
            if total_positions >= self.config['max_positions_per_symbol']:
                return {
                    'allow': False,
                    'reason': f'{symbol}持仓数量{total_positions}已达上限{self.config["max_positions_per_symbol"]}',
                    'total_positions': total_positions
                }
            
            if same_direction_count >= self.config['max_same_direction_per_symbol']:
                return {
                    'allow': False,
                    'reason': f'{symbol} {action}方向持仓{same_direction_count}已达上限{self.config["max_same_direction_per_symbol"]}',
                    'same_direction_count': same_direction_count
                }
            
            return {
                'allow': True,
                'reason': '持仓状态检查通过',
                'total_positions': total_positions,
                'same_direction_count': same_direction_count
            }
            
        except Exception as e:
            return {'allow': False, 'reason': f'持仓检查异常: {e}'}
    
    def _check_pending_queue_conflicts(self, symbol: str, action: str) -> Dict:
        """检查待执行队列中的冲突"""
        try:
            with self._order_lock:
                # 统计队列中相同货币对和方向的订单
                same_symbol_count = 0
                same_direction_count = 0
                
                for pending_order in self.pending_orders:
                    if pending_order.symbol == symbol:
                        same_symbol_count += 1
                        if pending_order.action == action:
                            same_direction_count += 1
                
                # 检查是否会超过限制
                if same_direction_count > 0:
                    return {
                        'allow': False,
                        'reason': f'待执行队列中已有{same_direction_count}个{symbol} {action}订单',
                        'queue_conflicts': same_direction_count
                    }
                
                return {
                    'allow': True,
                    'reason': '队列冲突检查通过',
                    'queue_same_symbol': same_symbol_count
                }
                
        except Exception as e:
            return {'allow': False, 'reason': f'队列冲突检查异常: {e}'}
    
    def _check_price_similarity(self, symbol: str, action: str) -> Dict:
        """检查价格相似性"""
        try:
            if not self.trade_executor:
                return {'allow': True, 'reason': '无交易执行器，跳过价格检查'}
            
            # 获取当前价格
            current_price = self.trade_executor._get_current_market_price(symbol)
            if not current_price:
                return {'allow': False, 'reason': '无法获取当前价格'}
            
            # 获取现有持仓的价格
            mt4_positions = self.trade_executor._get_mt4_real_positions(symbol)
            if mt4_positions:
                for pos in mt4_positions:
                    pos_action = pos.get('action', pos.get('type', ''))
                    # 处理MT4的订单类型格式
                    if pos_action in ['0', 'OP_BUY']:
                        pos_action = 'BUY'
                    elif pos_action in ['1', 'OP_SELL']:
                        pos_action = 'SELL'
                    
                    if pos_action == action:
                        pos_price = pos.get('entry_price', pos.get('open_price', 0))
                        if pos_price > 0:
                            price_diff = abs(current_price - pos_price)
                            min_diff = self.config['min_price_difference_points'] * 0.0001  # 转换为价格差异
                            
                            if price_diff < min_diff:
                                return {
                                    'allow': False,
                                    'reason': f'{symbol}价格相似：当前{current_price:.5f} vs 已有{pos_price:.5f}，差异{price_diff:.5f}小于最小要求{min_diff:.5f}',
                                    'price_diff': price_diff,
                                    'min_required': min_diff
                                }
            
            return {'allow': True, 'reason': '价格相似性检查通过'}
            
        except Exception as e:
            return {'allow': False, 'reason': f'价格检查异常: {e}'}
    
    def _check_strategy_priority(self, strategy_source: str) -> Dict:
        """检查策略优先级"""
        try:
            priority = self.strategy_priority.get(strategy_source, self.strategy_priority['default'])
            
            # 低优先级策略需要延迟执行，给高优先级策略检查时间
            if priority < 8:
                return {
                    'allow': True,
                    'delay_execution': True,
                    'reason': f'策略{strategy_source}优先级{priority}，延迟执行',
                    'priority': priority
                }
            
            return {
                'allow': True,
                'delay_execution': False,
                'reason': f'策略{strategy_source}优先级{priority}，立即执行',
                'priority': priority
            }
            
        except Exception as e:
            return {'allow': True, 'delay_execution': False, 'reason': f'优先级检查异常: {e}'}
    
    def _add_to_pending_queue(self, symbol: str, action: str, volume: float, strategy_source: str):
        """添加到待执行队列"""
        try:
            with self._order_lock:
                priority = self.strategy_priority.get(strategy_source, self.strategy_priority['default'])
                
                pending_order = PendingOrder(
                    symbol=symbol,
                    action=action,
                    volume=volume,
                    strategy_source=strategy_source,
                    timestamp=datetime.now(),
                    priority=priority
                )
                
                self.pending_orders.append(pending_order)
                
                # 按优先级排序
                self.pending_orders.sort(key=lambda x: x.priority, reverse=True)
                
                # 限制队列长度
                if len(self.pending_orders) > self.config['max_pending_orders']:
                    removed = self.pending_orders.pop()
                    print(f"⚠️ 队列已满，移除低优先级订单: {removed.symbol} {removed.action}")
                
                print(f"📝 订单已加入队列: {symbol} {action} (优先级: {priority})")
                
        except Exception as e:
            print(f"❌ 添加到队列失败: {e}")
    
    def _execute_trade_immediately(self, symbol: str, action: str, volume: float, strategy_source: str) -> Dict:
        """立即执行交易"""
        try:
            if not self.trade_executor:
                return {
                    'success': False,
                    'reason': '无交易执行器',
                    'action': 'error'
                }
            
            # 记录执行时间
            self.recent_orders[symbol] = datetime.now()
            
            # 调用原有的交易执行逻辑
            result = self.trade_executor.execute_trade(symbol, action, volume)
            
            print(f"⚡ 交易执行完成: {symbol} {action} {volume}手 - {'成功' if result.get('success') else '失败'}")
            
            return result
            
        except Exception as e:
            print(f"❌ 立即执行失败: {e}")
            return {
                'success': False,
                'reason': f'执行异常: {e}',
                'action': 'error'
            }
    
    def process_pending_orders(self):
        """处理待执行订单队列"""
        try:
            with self._order_lock:
                if not self.pending_orders:
                    return
                
                print(f"🔄 处理{len(self.pending_orders)}个待执行订单...")
                
                # 按优先级处理
                processed_orders = []
                
                for pending_order in self.pending_orders[:]:  # 复制列表避免修改时出错
                    # 检查是否超时
                    age = (datetime.now() - pending_order.timestamp).total_seconds()
                    if age > 300:  # 5分钟超时
                        print(f"⏰ 订单超时移除: {pending_order.symbol} {pending_order.action}")
                        self.pending_orders.remove(pending_order)
                        continue
                    
                    # 重新检查是否可以执行
                    check_result = self._immediate_execution_check(
                        pending_order.symbol, 
                        pending_order.action, 
                        pending_order.volume, 
                        pending_order.strategy_source
                    )
                    
                    if check_result['allow'] and not check_result.get('delay_execution', False):
                        # 执行订单
                        execution_result = self._execute_trade_immediately(
                            pending_order.symbol,
                            pending_order.action,
                            pending_order.volume,
                            pending_order.strategy_source
                        )
                        
                        processed_orders.append(pending_order)
                        print(f"✅ 队列订单执行: {pending_order.symbol} {pending_order.action}")
                        
                        # 执行成功后等待一段时间，避免过快执行
                        time.sleep(self.config['order_execution_delay_seconds'])
                
                # 移除已处理的订单
                for order in processed_orders:
                    if order in self.pending_orders:
                        self.pending_orders.remove(order)
                
        except Exception as e:
            print(f"❌ 处理待执行订单失败: {e}")
    
    def get_status(self) -> Dict:
        """获取协调器状态"""
        try:
            with self._position_lock:
                return {
                    'pending_orders_count': len(self.pending_orders),
                    'recent_orders_count': len(self.recent_orders),
                    'pending_orders': [
                        {
                            'symbol': order.symbol,
                            'action': order.action,
                            'volume': order.volume,
                            'strategy': order.strategy_source,
                            'age_seconds': (datetime.now() - order.timestamp).total_seconds(),
                            'priority': order.priority
                        }
                        for order in self.pending_orders
                    ]
                }
        except Exception as e:
            return {'error': str(e)}
