#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantumForex Pro 云端模型接收服务启动脚本
在腾讯云服务器上启动模型接收API服务
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.cloud_model_receiver import cloud_receiver

def setup_logging():
    """设置日志"""
    log_dir = "logs/model_receiver"
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, f"receiver_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    return logging.getLogger(__name__)

def main():
    """主函数"""
    logger = setup_logging()

    logger.info("🚀 启动 QuantumForex Pro 云端模型接收服务")
    logger.info("=" * 60)

    try:
        # 清理临时文件
        cloud_receiver.cleanup_temp_files()

        # 启动API服务器
        logger.info("🌐 启动API服务器...")
        logger.info("📍 监听地址: 0.0.0.0:8081")
        logger.info("🔗 API端点:")
        logger.info("   - 健康检查: http://127.0.0.1:8081/api/health")
        logger.info("   - 模型状态: http://127.0.0.1:8081/api/models/status")
        logger.info("   - 模型上传: http://127.0.0.1:8081/api/models/upload")
        logger.info("   - 模型列表: http://127.0.0.1:8081/api/models/list")
        logger.info("   📊 数据导出端点:")
        logger.info("   - 交易记录: http://127.0.0.1:8081/api/trading/records")
        logger.info("   - 参数优化: http://127.0.0.1:8081/api/optimization/history")
        logger.info("   - LLM分析: http://127.0.0.1:8081/api/llm/analysis_history")

        # 启动服务器
        cloud_receiver.start_server(
            host='0.0.0.0',
            port=8081,
            debug=False
        )

    except KeyboardInterrupt:
        logger.info("⚠️ 收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

    logger.info("✅ 云端模型接收服务已关闭")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
