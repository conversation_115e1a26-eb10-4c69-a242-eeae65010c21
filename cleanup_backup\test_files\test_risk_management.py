#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试风险管理系统集成
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_risk_management_integration():
    """测试风险管理系统集成"""
    print("🚀 风险管理系统集成测试")
    print("=" * 60)
    
    try:
        # 1. 测试风险管理模块导入
        print("📦 测试模块导入...")
        from app.core.risk_management import AdvancedRiskManager, RiskLevel, TradingAction
        print("   ✅ 风险管理模块导入成功")
        
        # 2. 测试风险管理器初始化
        print("\n🔧 测试风险管理器初始化...")
        risk_manager = AdvancedRiskManager()
        print("   ✅ 风险管理器初始化成功")
        
        # 3. 测试风险评估
        print("\n📊 测试风险评估...")
        
        # 模拟账户信息
        account_info = {
            'balance': 10000,
            'equity': 9800,  # 2%回撤
            'status': 'success'
        }
        
        # 模拟持仓信息
        positions = [
            {
                'symbol': 'EURUSD',
                'lot_size': 0.1,
                'entry_price': 1.1300,
                'stop_loss': 1.1250,
                'type': 'BUY'
            }
        ]
        
        # 模拟市场数据
        market_data = {
            'current_price': 1.1280,
            'atr': 0.0015,
            'spread': 2
        }
        
        # 执行风险评估
        risk_metrics = risk_manager.assess_comprehensive_risk(
            account_info, positions, market_data
        )
        
        print(f"   风险等级: {risk_metrics.risk_level.value}")
        print(f"   账户回撤: {risk_metrics.account_drawdown:.2%}")
        print(f"   持仓风险: {risk_metrics.position_risk:.2%}")
        print(f"   组合风险: {risk_metrics.portfolio_risk:.2%}")
        print(f"   推荐行动: {risk_metrics.recommended_action.value}")
        print("   ✅ 风险评估完成")
        
        # 4. 测试交易权限检查
        print("\n🔒 测试交易权限检查...")
        can_trade, reason = risk_manager.should_allow_trading(risk_metrics)
        print(f"   允许交易: {'是' if can_trade else '否'}")
        print(f"   原因: {reason}")
        
        # 5. 测试仓位计算
        print("\n💰 测试仓位计算...")
        signal_confidence = 0.8
        market_volatility = 0.0015
        account_balance = 10000
        
        optimal_size = risk_manager.calculate_optimal_position_size(
            signal_confidence, market_volatility, risk_metrics, account_balance
        )
        
        print(f"   信号置信度: {signal_confidence:.2f}")
        print(f"   市场波动率: {market_volatility:.4f}")
        print(f"   最优仓位: {optimal_size:.3f}")
        print("   ✅ 仓位计算完成")
        
        # 6. 测试交易服务集成
        print("\n🔗 测试交易服务集成...")
        try:
            from app.services.forex_trading_service import execute_trade_with_risk_management
            print("   ✅ 风险管理交易函数导入成功")
            
            # 模拟交易指令
            trade_instructions = {
                'action': 'BUY',
                'orderType': 'MARKET',
                'entryPrice': 1.1300,
                'stopLoss': 1.1250,
                'takeProfit': 1.1400,
                'lotSize': 0.1,
                'reasoning': '技术指标显示上升趋势，确信看多',
                'signalConfidence': 'HIGH'
            }
            
            print("   模拟交易指令准备完成")
            print(f"   行动: {trade_instructions['action']}")
            print(f"   仓位: {trade_instructions['lotSize']}")
            print(f"   信号强度: {trade_instructions['signalConfidence']}")
            
        except ImportError as e:
            print(f"   ⚠️ 交易服务集成测试跳过: {e}")
        
        # 7. 测试紧急行动
        print("\n🚨 测试紧急行动...")
        emergency_actions = risk_manager.get_emergency_actions(risk_metrics)
        if emergency_actions:
            print("   紧急行动建议:")
            for action in emergency_actions:
                print(f"   - {action['action']}: {action['reason']}")
        else:
            print("   无需紧急行动")
        
        # 8. 测试统计信息
        print("\n📈 测试统计信息...")
        stats = risk_manager.get_risk_statistics()
        print(f"   当前风险等级: {stats['current_risk_level']}")
        print(f"   日交易次数: {stats['daily_stats']['trades_count']}")
        print(f"   连续亏损: {stats['daily_stats']['consecutive_losses']}")
        
        print("\n🎉 风险管理系统集成测试完成！")
        print("   ✅ 所有核心功能正常工作")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_risk_scenarios():
    """测试不同风险场景"""
    print("\n🎯 风险场景测试")
    print("=" * 40)
    
    try:
        from app.core.risk_management import AdvancedRiskManager
        risk_manager = AdvancedRiskManager()
        
        scenarios = [
            {
                'name': '正常市场',
                'account': {'balance': 10000, 'equity': 10050},
                'positions': [],
                'market': {'current_price': 1.1300, 'atr': 0.0015}
            },
            {
                'name': '小幅亏损',
                'account': {'balance': 10000, 'equity': 9700},
                'positions': [{'symbol': 'EURUSD', 'lot_size': 0.1, 'entry_price': 1.1300, 'stop_loss': 1.1250, 'type': 'BUY'}],
                'market': {'current_price': 1.1280, 'atr': 0.0015}
            },
            {
                'name': '中等风险',
                'account': {'balance': 10000, 'equity': 9400},
                'positions': [{'symbol': 'EURUSD', 'lot_size': 0.2, 'entry_price': 1.1300, 'stop_loss': 1.1200, 'type': 'BUY'}],
                'market': {'current_price': 1.1250, 'atr': 0.0025}
            },
            {
                'name': '高风险',
                'account': {'balance': 10000, 'equity': 9000},
                'positions': [
                    {'symbol': 'EURUSD', 'lot_size': 0.3, 'entry_price': 1.1300, 'stop_loss': 1.1200, 'type': 'BUY'},
                    {'symbol': 'GBPUSD', 'lot_size': 0.2, 'entry_price': 1.2500, 'stop_loss': 1.2400, 'type': 'BUY'}
                ],
                'market': {'current_price': 1.1200, 'atr': 0.0030}
            },
            {
                'name': '紧急情况',
                'account': {'balance': 10000, 'equity': 8500},
                'positions': [{'symbol': 'EURUSD', 'lot_size': 0.5, 'entry_price': 1.1300, 'stop_loss': 1.1100, 'type': 'BUY'}],
                'market': {'current_price': 1.1150, 'atr': 0.0040}
            }
        ]
        
        for scenario in scenarios:
            print(f"\n📊 场景: {scenario['name']}")
            
            risk_metrics = risk_manager.assess_comprehensive_risk(
                scenario['account'], scenario['positions'], scenario['market']
            )
            
            can_trade, reason = risk_manager.should_allow_trading(risk_metrics)
            
            print(f"   风险等级: {risk_metrics.risk_level.value}")
            print(f"   账户回撤: {risk_metrics.account_drawdown:.1%}")
            print(f"   允许交易: {'是' if can_trade else '否'}")
            print(f"   推荐行动: {risk_metrics.recommended_action.value}")
            
            if not can_trade:
                print(f"   限制原因: {reason}")
        
        print("\n✅ 风险场景测试完成")
        
    except Exception as e:
        print(f"❌ 风险场景测试失败: {e}")

if __name__ == "__main__":
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始风险管理系统测试")
    
    # 执行集成测试
    success = test_risk_management_integration()
    
    if success:
        # 执行场景测试
        test_risk_scenarios()
        
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🎉 所有测试完成！")
        print("风险管理系统已成功集成到交易系统中。")
    else:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ 测试失败，请检查系统配置。")
