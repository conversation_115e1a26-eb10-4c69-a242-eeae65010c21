#!/usr/bin/env python3
"""
QuantumForex MLTrainer - 训练所有模型类型
为Pro系统训练完整的4种模型类型：
1. price_prediction (价格预测)
2. trend_classification (趋势分类)
3. volatility_prediction (波动率预测)
4. risk_assessment (风险评估)
"""

import sys
import os
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入训练器
from model_training.price_prediction_trainer import PricePredictionTrainer
from model_training.trend_classification_trainer import TrendClassificationTrainer
from model_training.volatility_prediction_trainer import VolatilityPredictionTrainer
from model_training.risk_assessment_trainer import RiskAssessmentTrainer

# 导入数据收集器和特征工程
from data_collector.forex_data_collector import ForexDataCollector
from feature_engineering.market_features import MarketFeatureEngine
from feature_engineering.technical_features import TechnicalFeatureEngine

class ComprehensiveModelTrainer:
    """综合模型训练器"""

    def __init__(self):
        self.logger = self._setup_logging()

        # 初始化训练器
        self.price_trainer = PricePredictionTrainer()
        self.trend_trainer = TrendClassificationTrainer()
        self.volatility_trainer = VolatilityPredictionTrainer()
        self.risk_trainer = RiskAssessmentTrainer()

        # 初始化数据收集器和特征工程
        self.data_collector = ForexDataCollector()
        self.market_engineer = MarketFeatureEngine()
        self.technical_engineer = TechnicalFeatureEngine()

        # 训练结果
        self.training_results = {}

    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'logs/comprehensive_training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)

    def collect_and_prepare_data(self, symbol: str = 'EURUSD', limit: int = 10000) -> pd.DataFrame:
        """收集和准备训练数据"""
        try:
            self.logger.info(f"📊 收集{symbol}数据，数量限制: {limit}")

            # 收集市场数据
            market_data = self.data_collector.collect_historical_data(symbol, days=30)

            if market_data.empty:
                raise ValueError(f"无法获取{symbol}的市场数据")

            self.logger.info(f"✅ 成功收集{len(market_data)}条{symbol}数据")

            # 特征工程
            self.logger.info("🔧 开始特征工程...")

            # 市场特征
            market_features = self.market_engineer.generate_all_features(market_data.copy())

            # 技术特征
            technical_features = self.technical_engineer.generate_all_features(market_data.copy())

            # 合并特征（只取新增的特征列）
            market_feature_cols = self.market_engineer.get_feature_names()
            technical_feature_cols = self.technical_engineer.get_feature_names()

            feature_data = market_data.copy()

            # 添加市场特征
            for col in market_feature_cols:
                if col in market_features.columns:
                    feature_data[col] = market_features[col]

            # 添加技术特征
            for col in technical_feature_cols:
                if col in technical_features.columns:
                    feature_data[col] = technical_features[col]

            # 删除NaN值
            feature_data = feature_data.dropna()

            self.logger.info(f"✅ 特征工程完成，最终数据: {len(feature_data)}行 x {len(feature_data.columns)}列")

            return feature_data

        except Exception as e:
            self.logger.error(f"❌ 数据收集和准备失败: {e}")
            raise

    def get_feature_columns(self, df: pd.DataFrame) -> list:
        """获取特征列"""
        # 排除基础价格列
        exclude_columns = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
        feature_columns = [col for col in df.columns if col not in exclude_columns]

        self.logger.info(f"📊 特征列数量: {len(feature_columns)}")
        self.logger.info(f"特征列示例: {feature_columns[:10]}")

        return feature_columns

    def train_price_prediction_models(self, df: pd.DataFrame, feature_columns: list) -> Dict[str, Any]:
        """训练价格预测模型"""
        try:
            self.logger.info("🚀 开始训练价格预测模型...")

            # 训练价格方向预测模型（5分钟）
            results = self.price_trainer.train_price_direction_model(
                df, feature_columns, horizon=5, threshold=0.0001
            )

            self.logger.info("✅ 价格预测模型训练完成")
            return results

        except Exception as e:
            self.logger.error(f"❌ 价格预测模型训练失败: {e}")
            return {}

    def train_trend_classification_models(self, df: pd.DataFrame, feature_columns: list) -> Dict[str, Any]:
        """训练趋势分类模型"""
        try:
            self.logger.info("🚀 开始训练趋势分类模型...")

            # 训练趋势强度分类模型
            results = self.trend_trainer.train_trend_strength_model(
                df, feature_columns, window=20, threshold=0.002
            )

            self.logger.info("✅ 趋势分类模型训练完成")
            return results

        except Exception as e:
            self.logger.error(f"❌ 趋势分类模型训练失败: {e}")
            return {}

    def train_volatility_prediction_models(self, df: pd.DataFrame, feature_columns: list) -> Dict[str, Any]:
        """训练波动率预测模型"""
        try:
            self.logger.info("🚀 开始训练波动率预测模型...")

            # 训练波动率预测模型
            results = self.volatility_trainer.train_volatility_prediction_model(
                df, feature_columns, window=20, horizon=5
            )

            self.logger.info("✅ 波动率预测模型训练完成")
            return results

        except Exception as e:
            self.logger.error(f"❌ 波动率预测模型训练失败: {e}")
            return {}

    def train_risk_assessment_models(self, df: pd.DataFrame, feature_columns: list) -> Dict[str, Any]:
        """训练风险评估模型"""
        try:
            self.logger.info("🚀 开始训练风险评估模型...")

            # 训练综合风险评估模型
            results = self.risk_trainer.train_composite_risk_model(
                df, feature_columns, window=20, horizon=5
            )

            self.logger.info("✅ 风险评估模型训练完成")
            return results

        except Exception as e:
            self.logger.error(f"❌ 风险评估模型训练失败: {e}")
            return {}

    def copy_models_to_pro(self):
        """复制训练好的模型到Pro系统"""
        try:
            self.logger.info("📁 复制模型文件到Pro系统...")

            import shutil
            from pathlib import Path

            # 源目录和目标目录
            source_dir = Path("data/models")
            target_dir = Path("../QuantumForex_Pro/data/models")

            # 确保目标目录存在
            target_dir.mkdir(parents=True, exist_ok=True)

            # 复制所有模型文件
            copied_files = 0
            for model_file in source_dir.glob("*.pkl"):
                target_file = target_dir / model_file.name
                shutil.copy2(model_file, target_file)
                copied_files += 1
                self.logger.info(f"📄 复制: {model_file.name}")

            self.logger.info(f"✅ 成功复制{copied_files}个模型文件到Pro系统")

        except Exception as e:
            self.logger.error(f"❌ 复制模型文件失败: {e}")

    def train_all_models(self, symbol: str = 'EURUSD', data_limit: int = 10000):
        """训练所有模型类型"""
        try:
            self.logger.info("🚀 开始综合模型训练...")
            self.logger.info(f"训练时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 1. 收集和准备数据
            df = self.collect_and_prepare_data(symbol, data_limit)
            feature_columns = self.get_feature_columns(df)

            # 2. 训练各种模型类型
            model_types = [
                ("价格预测", self.train_price_prediction_models),
                ("趋势分类", self.train_trend_classification_models),
                ("波动率预测", self.train_volatility_prediction_models),
                ("风险评估", self.train_risk_assessment_models)
            ]

            for model_name, train_func in model_types:
                try:
                    self.logger.info(f"\n{'='*60}")
                    self.logger.info(f"🔄 训练{model_name}模型...")
                    self.logger.info(f"{'='*60}")

                    results = train_func(df, feature_columns)
                    self.training_results[model_name] = results

                    if results:
                        self.logger.info(f"✅ {model_name}模型训练成功")
                    else:
                        self.logger.warning(f"⚠️ {model_name}模型训练无结果")

                except Exception as e:
                    self.logger.error(f"❌ {model_name}模型训练失败: {e}")
                    self.training_results[model_name] = {}

            # 3. 复制模型到Pro系统
            self.copy_models_to_pro()

            # 4. 生成训练报告
            self.generate_training_report()

            self.logger.info("🎉 所有模型训练完成！")

        except Exception as e:
            self.logger.error(f"❌ 综合模型训练失败: {e}")
            raise

    def generate_training_report(self):
        """生成训练报告"""
        try:
            self.logger.info("\n" + "="*60)
            self.logger.info("📊 综合模型训练报告")
            self.logger.info("="*60)

            total_models = 0
            successful_models = 0

            for model_type, results in self.training_results.items():
                if results:
                    successful_models += 1
                    self.logger.info(f"✅ {model_type}: 训练成功")

                    # 显示最佳模型信息
                    if isinstance(results, dict) and results:
                        best_model = max(results.items(), key=lambda x: x[1].get('metrics', {}).get('f1_score', 0))
                        best_name, best_result = best_model
                        best_score = best_result.get('metrics', {}).get('f1_score', 0)
                        self.logger.info(f"   最佳模型: {best_name} (F1: {best_score:.3f})")
                else:
                    self.logger.info(f"❌ {model_type}: 训练失败")

                total_models += 1

            self.logger.info("="*60)
            self.logger.info(f"📈 训练统计: {successful_models}/{total_models} 成功")
            self.logger.info(f"🎯 成功率: {successful_models/total_models*100:.1f}%")

            if successful_models == total_models:
                self.logger.info("🎉 所有模型类型训练成功！Pro系统现在拥有完整的ML模型！")
            else:
                self.logger.warning(f"⚠️ {total_models - successful_models}个模型类型训练失败")

        except Exception as e:
            self.logger.error(f"❌ 生成训练报告失败: {e}")

def main():
    """主函数"""
    try:
        print("🚀 QuantumForex MLTrainer - 综合模型训练")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)

        # 创建训练器
        trainer = ComprehensiveModelTrainer()

        # 训练所有模型
        trainer.train_all_models(symbol='EURUSD', data_limit=10000)

        print("="*60)
        print("🎉 综合模型训练完成！")
        print("✅ Pro系统现在拥有完整的4种模型类型")
        print("🚀 可以享受高精度的ML预测！")

    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
