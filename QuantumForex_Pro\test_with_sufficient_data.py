#!/usr/bin/env python3
"""
用充足数据测试模型预测
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_with_sufficient_data():
    print("🧪 用充足数据测试最优模型选择")
    print("="*50)
    
    try:
        # 导入ML引擎
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine, ModelType
        
        print("🤖 初始化ML引擎...")
        ml_engine = LightweightMLEngine()
        
        # 检查模型状态
        print(f"\n📊 模型加载状态:")
        for model_type, model in ml_engine.models.items():
            if model is not None:
                performance = ml_engine.model_performance.get(model_type, 0.5)
                
                if performance >= 0.9:
                    status = "🎯 兼容模型 (最优)"
                elif performance >= 0.8:
                    status = "🤖 Trainer高级模型"
                else:
                    status = "📊 标准模型"
                
                print(f"   {model_type.value}: {status} (性能: {performance:.3f})")
        
        # 创建充足的测试数据 (50个数据点)
        print(f"\n📊 生成充足的测试数据...")
        
        periods = 50
        dates = pd.date_range('2024-01-01', periods=periods, freq='5min')
        
        # 生成逼真的价格数据
        np.random.seed(42)
        base_price = 1.0800
        
        # 生成价格序列
        returns = np.random.normal(0, 0.0001, periods)
        trend = np.sin(np.arange(periods) / 10) * 0.0001
        
        prices = [base_price]
        for i in range(periods - 1):
            new_price = prices[-1] * (1 + returns[i] + trend[i])
            prices.append(new_price)
        
        # 生成OHLC数据
        test_data = []
        for i in range(1, len(prices)):
            open_price = prices[i-1]
            close_price = prices[i]
            
            volatility = abs(returns[i-1]) * 5
            high_price = max(open_price, close_price) + volatility
            low_price = min(open_price, close_price) - volatility
            
            test_data.append({
                'timestamp': dates[i],
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': np.random.randint(1000, 5000)
            })
        
        market_data = pd.DataFrame(test_data)
        market_data.set_index('timestamp', inplace=True)
        
        print(f"✅ 生成测试数据: {len(market_data)}条记录")
        
        # 创建技术指标
        test_indicators = {
            'trend_analysis': {
                'trend_score': 0.6,
                'trend_strength': 0.7
            },
            'momentum_analysis': {
                'momentum_score': 0.5,
                'momentum_divergence': 0.2
            },
            'volatility_analysis': {
                'volatility_score': 0.4,
                'volatility_regime': 0.3
            }
        }
        
        # 获取预测
        print(f"\n🔮 测试最优模型预测能力...")
        predictions = ml_engine.generate_predictions(market_data, test_indicators)
        
        if predictions:
            print("✅ 最优模型预测成功！")
            
            # 显示预测结果
            valid_predictions = 0
            for model_type, prediction in predictions.items():
                print(f"\n📈 {model_type.value}:")
                print(f"   预测值: {prediction.prediction:.6f}")
                print(f"   置信度: {prediction.confidence:.3f}")
                print(f"   模型准确性: {prediction.model_accuracy:.3f}")
                
                # 检查预测质量
                if abs(prediction.prediction) > 0.000001 and prediction.confidence > 0.1:
                    print(f"   ✅ 预测有效")
                    valid_predictions += 1
                else:
                    print(f"   ❌ 预测无效")
            
            # 评估预测质量
            print(f"\n🎯 最优模型预测质量评估:")
            print(f"   有效预测: {valid_predictions}/{len(predictions)}")
            print(f"   预测质量: {valid_predictions/len(predictions):.1%}")
            
            # 检查模型是否真的是最优的
            print(f"\n🏆 最优模型验证:")
            optimal_count = 0
            for model_type, model in ml_engine.models.items():
                performance = ml_engine.model_performance.get(model_type, 0.5)
                if performance >= 0.9:
                    optimal_count += 1
                    print(f"   ✅ {model_type.value}: 最优模型 (性能: {performance:.3f})")
                else:
                    print(f"   ⚠️ {model_type.value}: 非最优模型 (性能: {performance:.3f})")
            
            print(f"\n📊 最优模型统计:")
            print(f"   最优模型数量: {optimal_count}/{len(ml_engine.models)}")
            print(f"   最优模型比例: {optimal_count/len(ml_engine.models):.1%}")
            
            if optimal_count == len(ml_engine.models):
                print("🌟 所有模型都是最优选择！")
                success = True
            elif optimal_count >= len(ml_engine.models) * 0.75:
                print("✅ 大部分模型是最优选择")
                success = True
            else:
                print("⚠️ 部分模型不是最优选择")
                success = False
            
            # 预测效果评估
            if valid_predictions >= len(predictions) * 0.5:
                print("✅ 最优模型预测效果良好")
                return success
            else:
                print("❌ 最优模型预测效果不佳")
                return False
        else:
            print("❌ 最优模型预测失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_with_sufficient_data()
    
    if success:
        print("\n🎉 最优模型选择和预测测试成功！")
        print("💡 系统正确选择并使用了最优模型")
    else:
        print("\n❌ 最优模型测试失败！")
        print("💡 需要进一步优化模型选择机制")
