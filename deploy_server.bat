@echo off
chcp 65001
echo ========================================
echo 外汇交易系统服务器部署脚本
echo ========================================

echo [1/6] 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo [2/6] 升级pip...
python -m pip install --upgrade pip

echo [3/6] 创建虚拟环境...
if not exist venv (
    python -m venv venv
)

echo [4/6] 激活虚拟环境...
call venv\Scripts\activate.bat

echo [5/6] 安装依赖包...
pip install -r requirements.txt

echo [6/6] 检查关键依赖...
python -c "import dotenv; print('python-dotenv: OK')"
python -c "import flask; print('flask: OK')"
python -c "import pandas; print('pandas: OK')"
python -c "import requests; print('requests: OK')"

echo ========================================
echo 部署完成！
echo ========================================
echo 启动命令: 
echo   call venv\Scripts\activate.bat
echo   python run.py
echo ========================================
pause
