#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场时间检测测试脚本
验证修复后的市场时间检测逻辑是否正确
"""

import pytz
from datetime import datetime, timedelta
from app.utils.market_time_checker import is_market_open, get_market_status, get_next_market_open_time, format_time_until_open

def test_market_time_detection():
    """测试市场时间检测功能"""
    print("=" * 60)
    print("🕒 外汇市场时间检测测试")
    print("=" * 60)
    
    # 当前时间
    utc_now = datetime.now(pytz.UTC)
    beijing_now = utc_now.astimezone(pytz.timezone('Asia/Shanghai'))
    
    print(f"📅 当前时间:")
    print(f"   UTC时间: {utc_now.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"   北京时间: {beijing_now.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"   星期几: {utc_now.weekday()} (0=周一, 6=周日)")
    print()
    
    # 当前市场状态
    print(f"🌍 当前市场状态:")
    print(f"   市场是否开放: {is_market_open()}")
    print(f"   市场状态: {get_market_status()}")
    print(f"   距离开市: {format_time_until_open()}")
    print()
    
    # 测试不同时间点
    test_times = [
        # 周日21:00 UTC (应该休市)
        utc_now.replace(weekday=6, hour=21, minute=0, second=0, microsecond=0),
        # 周日22:00 UTC (应该开市)
        utc_now.replace(weekday=6, hour=22, minute=0, second=0, microsecond=0),
        # 周一10:00 UTC (应该开市)
        utc_now.replace(weekday=0, hour=10, minute=0, second=0, microsecond=0),
        # 周三15:00 UTC (应该开市)
        utc_now.replace(weekday=2, hour=15, minute=0, second=0, microsecond=0),
        # 周五21:00 UTC (应该开市)
        utc_now.replace(weekday=4, hour=21, minute=0, second=0, microsecond=0),
        # 周五22:00 UTC (应该休市)
        utc_now.replace(weekday=4, hour=22, minute=0, second=0, microsecond=0),
        # 周六10:00 UTC (应该休市)
        utc_now.replace(weekday=5, hour=10, minute=0, second=0, microsecond=0),
    ]
    
    print("🧪 测试不同时间点的市场状态:")
    print("-" * 60)
    
    for i, test_time in enumerate(test_times, 1):
        # 临时修改时间进行测试（这里只是模拟，实际不会修改系统时间）
        weekday = test_time.weekday()
        hour = test_time.hour
        weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        
        # 模拟市场开放检测逻辑
        if weekday == 6:  # 周日
            market_open = hour >= 22
        elif weekday <= 3:  # 周一到周四
            market_open = True
        elif weekday == 4:  # 周五
            market_open = hour < 22
        elif weekday == 5:  # 周六
            market_open = False
        
        beijing_time = test_time.astimezone(pytz.timezone('Asia/Shanghai'))
        
        print(f"测试 {i}: {test_time.strftime('%Y-%m-%d %H:%M')} UTC ({weekday_names[weekday]})")
        print(f"       北京时间: {beijing_time.strftime('%Y-%m-%d %H:%M')}")
        print(f"       市场开放: {'✅ 是' if market_open else '❌ 否'}")
        print()

def test_market_transition_times():
    """测试市场开关时间的边界情况"""
    print("=" * 60)
    print("🔄 市场开关时间边界测试")
    print("=" * 60)
    
    # 测试关键时间点
    critical_times = [
        ("周日21:59 UTC", 6, 21, 59, False),  # 开市前1分钟
        ("周日22:00 UTC", 6, 22, 0, True),   # 开市时刻
        ("周日22:01 UTC", 6, 22, 1, True),   # 开市后1分钟
        ("周五21:59 UTC", 4, 21, 59, True),  # 收市前1分钟
        ("周五22:00 UTC", 4, 22, 0, False),  # 收市时刻
        ("周五22:01 UTC", 4, 22, 1, False),  # 收市后1分钟
    ]
    
    for desc, weekday, hour, minute, expected in critical_times:
        # 模拟市场开放检测逻辑
        if weekday == 6:  # 周日
            actual = hour >= 22 or (hour == 22 and minute >= 0)
        elif weekday == 4:  # 周五
            actual = hour < 22 or (hour == 21 and minute <= 59)
        else:
            actual = True
        
        status = "✅ 正确" if actual == expected else "❌ 错误"
        print(f"{desc}: 预期={expected}, 实际={actual} {status}")

if __name__ == "__main__":
    test_market_time_detection()
    print()
    test_market_transition_times()
    
    print()
    print("=" * 60)
    print("✅ 市场时间检测测试完成")
    print("=" * 60)
    print()
    print("📋 外汇市场开放时间规则:")
    print("   - 开市: 周日 22:00 UTC (北京时间周一 06:00)")
    print("   - 收市: 周五 22:00 UTC (北京时间周六 06:00)")
    print("   - 休市: 周五 22:00 - 周日 22:00")
    print("   - 开市时长: 120小时 (5天)")
    print("   - 休市时长: 48小时 (2天)")
