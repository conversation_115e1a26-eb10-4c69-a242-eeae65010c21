"""
测试LLM API调用
"""
import os
import sys
import json
import traceback
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
from app.utils import llm_client

def test_llm_api():
    """测试LLM API调用"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 开始测试LLM API调用...")
    
    try:
        # 构建简单的提示词
        prompt = "你好，这是一个测试。请简短回复。"
        
        # 调用LLM API
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 发送请求到LLM API...")
        response = llm_client.send_to_deepseek(prompt, max_tokens=100)
        
        # 打印响应
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> LLM API响应成功")
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 响应JSON结构:\n{json.dumps(response, indent=2)[:500]}...")
        
        # 提取内容
        if 'choices' in response and len(response['choices']) > 0 and 'message' in response['choices'][0] and 'content' in response['choices'][0]['message']:
            content = response['choices'][0]['message']['content']
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 响应内容:\n{content}")
        else:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 错误: 响应JSON格式不正确，缺少必要的字段")
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 完整响应:\n{json.dumps(response, indent=2)}")
        
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试成功!")
        return True
    except Exception as e:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试失败! 错误: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_llm_api()
