"""
QuantumForex Pro 云端模型接收器
接收来自本地训练端的模型文件并进行部署
"""

import os
import json
import gzip
import shutil
import hashlib
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from flask import Flask, request, jsonify

from config.config import config

class CloudModelReceiver:
    """云端模型接收器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 配置信息
        self.ml_config = config.ML_TRAINER_CONFIG

        # 路径配置
        self.models_folder = Path(self.ml_config['local_storage']['models_folder'])
        self.backup_folder = Path(self.ml_config['local_storage']['backup_folder'])
        self.temp_folder = Path(self.ml_config['local_storage']['temp_folder'])
        self.download_folder = Path(self.ml_config['local_storage']['download_folder'])

        # 创建必要文件夹
        self._create_folders()

        # 支持的模型类型（兼容Trainer端格式）
        self.supported_models = [
            'price_prediction',
            'risk_assessment',
            'trend_classification',
            'volatility_prediction',
            # 兼容旧格式
            'price_prediction_model',
            'risk_assessment_model',
            'trend_classification_model',
            'volatility_prediction_model'
        ]

        # Flask应用
        self.app = Flask(__name__)
        self._setup_routes()

    def _create_folders(self):
        """创建必要的文件夹"""
        folders = [
            self.models_folder,
            self.backup_folder,
            self.temp_folder,
            self.download_folder,
            Path('logs/model_receiver')
        ]

        for folder in folders:
            folder.mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"创建文件夹: {folder}")

    def _setup_routes(self):
        """设置API路由"""

        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'service': 'CloudModelReceiver'
            })

        @self.app.route('/api/models/status', methods=['GET'])
        def get_models_status():
            """获取模型状态"""
            try:
                status = self._get_models_status()
                return jsonify(status)
            except Exception as e:
                self.logger.error(f"获取模型状态失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        @self.app.route('/api/models/upload', methods=['POST'])
        def upload_model():
            """接收模型上传"""
            try:
                result = self._handle_model_upload(request)
                if result['success']:
                    return jsonify(result)
                else:
                    return jsonify(result), 400
            except Exception as e:
                self.logger.error(f"模型上传处理失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        @self.app.route('/api/models/list', methods=['GET'])
        def list_models():
            """列出所有模型"""
            try:
                models = self._list_available_models()
                return jsonify({
                    'success': True,
                    'models': models,
                    'count': len(models)
                })
            except Exception as e:
                self.logger.error(f"列出模型失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        # ==================== 新增：数据导出API ====================

        @self.app.route('/api/trading/records', methods=['GET'])
        def get_trading_records():
            """获取交易记录"""
            try:
                days = request.args.get('days', 7, type=int)
                result = self._get_trading_records(days)
                return jsonify({
                    'success': True,
                    'data': result,
                    'count': len(result),
                    'days': days,
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                self.logger.error(f"获取交易记录失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        @self.app.route('/api/optimization/history', methods=['GET'])
        def get_optimization_history():
            """获取参数优化历史"""
            try:
                days = request.args.get('days', 7, type=int)
                result = self._get_optimization_history(days)
                return jsonify({
                    'success': True,
                    'data': result,
                    'count': len(result),
                    'days': days,
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                self.logger.error(f"获取参数优化历史失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        @self.app.route('/api/llm/analysis_history', methods=['GET'])
        def get_llm_analysis_history():
            """获取LLM分析历史"""
            try:
                days = request.args.get('days', 7, type=int)
                result = self._get_llm_analysis_history(days)
                return jsonify({
                    'success': True,
                    'data': result,
                    'count': len(result),
                    'days': days,
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                self.logger.error(f"获取LLM分析历史失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

    def _handle_model_upload(self, request) -> Dict[str, Any]:
        """处理模型上传"""
        try:
            self.logger.info("📥 接收模型上传请求...")

            # 检查文件
            if 'model_file' not in request.files:
                raise ValueError("没有找到模型文件")

            file = request.files['model_file']
            if file.filename == '':
                raise ValueError("文件名为空")

            # 获取元数据
            metadata_str = request.form.get('metadata', '{}')
            metadata = json.loads(metadata_str)

            # 验证元数据
            if not self._validate_metadata(metadata):
                raise ValueError("元数据验证失败")

            # 保存临时文件
            temp_file = self._save_temp_file(file, metadata)

            # 验证文件完整性
            if not self._verify_file_integrity(temp_file, metadata):
                raise ValueError("文件完整性验证失败")

            # 处理文件（解压缩等）
            processed_file = self._process_uploaded_file(temp_file, metadata)

            # 备份现有模型
            backup_success = self._backup_existing_model(metadata['original_name'])

            # 部署新模型
            deploy_success = self._deploy_new_model(processed_file, metadata)

            if deploy_success:
                # 记录部署信息
                self._record_deployment(metadata, backup_success)

                self.logger.info(f"✅ 模型部署成功: {metadata['original_name']}")

                return {
                    'success': True,
                    'message': '模型上传和部署成功',
                    'model_name': metadata['original_name'],
                    'timestamp': datetime.now().isoformat()
                }
            else:
                raise Exception("模型部署失败")

        except Exception as e:
            self.logger.error(f"❌ 模型上传处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _validate_metadata(self, metadata: Dict[str, Any]) -> bool:
        """验证元数据"""
        required_fields = ['original_name', 'model_type', 'file_size', 'checksum']

        for field in required_fields:
            if field not in metadata:
                self.logger.error(f"缺少必要字段: {field}")
                return False

        # 验证模型类型
        if metadata['model_type'] not in self.supported_models:
            self.logger.error(f"不支持的模型类型: {metadata['model_type']}")
            return False

        return True

    def _save_temp_file(self, file, metadata: Dict[str, Any]) -> Path:
        """保存临时文件"""
        temp_filename = f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{metadata['original_name']}"
        temp_file = self.temp_folder / temp_filename

        file.save(str(temp_file))

        self.logger.debug(f"临时文件已保存: {temp_file}")
        return temp_file

    def _verify_file_integrity(self, file_path: Path, metadata: Dict[str, Any]) -> bool:
        """验证文件完整性"""
        try:
            # 计算文件校验和
            calculated_checksum = self._calculate_checksum(file_path)
            expected_checksum = metadata['checksum']

            if calculated_checksum == expected_checksum:
                self.logger.debug("✅ 文件完整性验证通过")
                return True
            else:
                self.logger.error(f"❌ 文件校验和不匹配: {calculated_checksum} != {expected_checksum}")
                return False

        except Exception as e:
            self.logger.error(f"文件完整性验证失败: {e}")
            return False

    def _calculate_checksum(self, file_path: Path) -> str:
        """计算文件校验和"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def _process_uploaded_file(self, temp_file: Path, metadata: Dict[str, Any]) -> Path:
        """处理上传的文件"""
        try:
            # 如果文件被压缩，需要解压
            if metadata.get('compressed', False):
                decompressed_file = self._decompress_file(temp_file, metadata)
                return decompressed_file
            else:
                return temp_file

        except Exception as e:
            self.logger.error(f"处理上传文件失败: {e}")
            raise

    def _decompress_file(self, compressed_file: Path, metadata: Dict[str, Any]) -> Path:
        """解压文件"""
        decompressed_file = self.temp_folder / metadata['original_name']

        with gzip.open(compressed_file, 'rb') as f_in:
            with open(decompressed_file, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)

        self.logger.debug(f"文件已解压: {compressed_file.name} → {decompressed_file.name}")
        return decompressed_file

    def _backup_existing_model(self, model_name: str) -> bool:
        """备份现有模型"""
        try:
            existing_model = self.models_folder / model_name

            if existing_model.exists():
                backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_name = f"{model_name}.backup_{backup_timestamp}"
                backup_file = self.backup_folder / backup_name

                shutil.copy2(existing_model, backup_file)
                self.logger.info(f"📦 现有模型已备份: {backup_name}")
                return True
            else:
                self.logger.info("📦 没有现有模型需要备份")
                return True

        except Exception as e:
            self.logger.error(f"❌ 备份现有模型失败: {e}")
            return False

    def _deploy_new_model(self, model_file: Path, metadata: Dict[str, Any]) -> bool:
        """部署新模型"""
        try:
            target_file = self.models_folder / metadata['original_name']

            # 复制模型文件
            shutil.copy2(model_file, target_file)

            # 验证部署
            if target_file.exists() and target_file.stat().st_size > 0:
                self.logger.info(f"✅ 模型部署成功: {target_file}")

                # 测试模型加载
                if self._test_model_loading(target_file):
                    return True
                else:
                    self.logger.error("❌ 模型加载测试失败")
                    return False
            else:
                self.logger.error("❌ 模型文件复制失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 部署新模型失败: {e}")
            return False

    def _test_model_loading(self, model_file: Path) -> bool:
        """测试模型加载"""
        try:
            import joblib
            model = joblib.load(model_file)

            # 根据文件名判断模型类型进行不同的验证
            filename = model_file.name.lower()

            if 'scaler' in filename:
                # 标准化器验证
                if hasattr(model, 'transform'):
                    self.logger.debug(f"✅ 标准化器加载测试通过: {model_file.name}")
                    return True
                else:
                    self.logger.error("❌ 标准化器缺少transform方法")
                    return False
            else:
                # 模型验证
                if hasattr(model, 'predict'):
                    self.logger.debug(f"✅ 模型加载测试通过: {model_file.name}")
                    return True
                else:
                    self.logger.error("❌ 模型缺少predict方法")
                    return False

        except Exception as e:
            self.logger.error(f"❌ 模型加载测试失败: {e}")
            return False

    def _record_deployment(self, metadata: Dict[str, Any], backup_success: bool):
        """记录部署信息"""
        try:
            deployment_record = {
                'model_name': metadata['original_name'],
                'model_type': metadata['model_type'],
                'deployment_time': datetime.now().isoformat(),
                'version': metadata.get('version', 'unknown'),
                'file_size': metadata['file_size'],
                'backup_created': backup_success,
                'metadata': metadata
            }

            records_file = Path('logs/model_receiver/deployment_records.json')
            records_file.parent.mkdir(parents=True, exist_ok=True)

            # 读取现有记录
            if records_file.exists():
                with open(records_file, 'r', encoding='utf-8') as f:
                    records = json.load(f)
            else:
                records = []

            # 添加新记录
            records.append(deployment_record)

            # 保持最近50条记录
            if len(records) > 50:
                records = records[-50:]

            # 保存记录
            with open(records_file, 'w', encoding='utf-8') as f:
                json.dump(records, f, indent=2, ensure_ascii=False)

            self.logger.debug(f"部署记录已保存: {records_file}")

        except Exception as e:
            self.logger.error(f"记录部署信息失败: {e}")

    def _get_models_status(self) -> Dict[str, Any]:
        """获取模型状态"""
        try:
            models_info = []

            for model_file in self.models_folder.glob('*.pkl'):
                model_info = {
                    'name': model_file.name,
                    'size': model_file.stat().st_size,
                    'modified_time': datetime.fromtimestamp(model_file.stat().st_mtime).isoformat(),
                    'type': self._detect_model_type(model_file.name)
                }
                models_info.append(model_info)

            return {
                'success': True,
                'models_count': len(models_info),
                'models': models_info,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"获取模型状态失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _list_available_models(self) -> List[Dict[str, Any]]:
        """列出可用模型"""
        models = []

        for model_file in self.models_folder.glob('*.pkl'):
            model_info = {
                'name': model_file.name,
                'path': str(model_file),
                'size': model_file.stat().st_size,
                'modified_time': datetime.fromtimestamp(model_file.stat().st_mtime).isoformat(),
                'type': self._detect_model_type(model_file.name)
            }
            models.append(model_info)

        return models

    def _detect_model_type(self, filename: str) -> str:
        """检测模型类型"""
        if 'price_prediction' in filename:
            return 'price_prediction'
        elif 'risk_assessment' in filename:
            return 'risk_assessment'
        elif 'trend_classification' in filename:
            return 'trend_classification'
        elif 'volatility_prediction' in filename:
            return 'volatility_prediction'
        else:
            return 'unknown'

    def start_server(self, host='0.0.0.0', port=8081, debug=False):
        """启动API服务器"""
        self.logger.info(f"🚀 启动云端模型接收服务器: {host}:{port}")
        self.app.run(host=host, port=port, debug=debug, threaded=True)

    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            temp_files = list(self.temp_folder.glob('*'))
            cleaned_count = 0

            for temp_file in temp_files:
                if temp_file.is_file():
                    # 删除超过1小时的临时文件
                    file_age = datetime.now().timestamp() - temp_file.stat().st_mtime
                    if file_age > 3600:  # 1小时
                        temp_file.unlink()
                        cleaned_count += 1

            if cleaned_count > 0:
                self.logger.info(f"🧹 清理临时文件: {cleaned_count}个")

        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")

    # ==================== 数据导出方法 ====================

    def _get_trading_records(self, days: int) -> List[Dict]:
        """获取交易记录"""
        try:
            # 导入交易记录器
            from core.learning_system.trade_result_recorder import TradeResultRecorder

            # 创建记录器实例
            recorder = TradeResultRecorder()

            # 获取最近的交易记录
            trades = recorder.get_recent_trades(days=days, limit=1000)

            # 转换为字典格式
            result = []
            for trade in trades:
                trade_dict = {
                    'trade_id': trade.trade_id,
                    'symbol': trade.symbol,
                    'action': trade.action,
                    'entry_time': trade.entry_time.isoformat() if trade.entry_time else None,
                    'entry_price': trade.entry_price,
                    'volume': trade.volume,
                    'strategy_used': trade.strategy_used,
                    'confidence': trade.confidence,
                    'market_condition': trade.market_condition,
                    'exit_time': trade.exit_time.isoformat() if trade.exit_time else None,
                    'exit_price': trade.exit_price,
                    'exit_reason': trade.exit_reason,
                    'profit_loss': trade.profit_loss,
                    'profit_loss_pct': trade.profit_loss_pct,
                    'holding_duration_minutes': trade.holding_duration_minutes,
                    'max_favorable_excursion': trade.max_favorable_excursion,
                    'max_adverse_excursion': trade.max_adverse_excursion
                }
                result.append(trade_dict)

            self.logger.info(f"获取交易记录: {len(result)}条 (最近{days}天)")
            return result

        except Exception as e:
            self.logger.error(f"获取交易记录失败: {e}")
            return []

    def _get_optimization_history(self, days: int) -> List[Dict]:
        """获取参数优化历史"""
        try:
            # 导入参数优化器
            from core.learning_system.parameter_optimizer import ParameterOptimizer

            # 创建优化器实例
            optimizer = ParameterOptimizer()

            # 获取优化历史
            history = optimizer.optimization_history

            # 过滤最近几天的记录
            cutoff_date = datetime.now() - timedelta(days=days)

            result = []
            for record in history:
                try:
                    record_time = datetime.fromisoformat(record['timestamp'])
                    if record_time >= cutoff_date:
                        # 展开优化结果
                        for opt_result in record.get('results', []):
                            result.append({
                                'parameter_name': opt_result.get('parameter_name'),
                                'old_value': opt_result.get('old_value'),
                                'new_value': opt_result.get('new_value'),
                                'improvement_score': opt_result.get('improvement_score'),
                                'confidence': opt_result.get('confidence'),
                                'reason': opt_result.get('reason'),
                                'optimization_time': record['timestamp']
                            })
                except Exception as e:
                    self.logger.warning(f"解析优化记录失败: {e}")
                    continue

            self.logger.info(f"获取参数优化历史: {len(result)}条 (最近{days}天)")
            return result

        except Exception as e:
            self.logger.error(f"获取参数优化历史失败: {e}")
            return []

    def _get_llm_analysis_history(self, days: int) -> List[Dict]:
        """获取LLM分析历史"""
        try:
            # 这里需要从LLM分析器获取历史记录
            # 由于LLM分析器可能没有持久化存储，我们先返回空列表
            # 后续可以添加LLM分析历史的存储机制

            self.logger.info(f"获取LLM分析历史: 0条 (功能待实现)")
            return []

        except Exception as e:
            self.logger.error(f"获取LLM分析历史失败: {e}")
            return []

# 创建全局实例
cloud_receiver = CloudModelReceiver()
