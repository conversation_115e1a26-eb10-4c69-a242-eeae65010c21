"""
高级分析格式化模块
用于将高级技术分析结果格式化为文本描述
"""
import numpy as np
import pandas as pd


def format_advanced_analysis(indicators, timeframe='15min'):
    """
    将高级技术分析结果格式化为文本描述

    Args:
        indicators (dict): 高级技术指标
        timeframe (str): 时间周期

    Returns:
        str: 格式化后的文本描述
    """
    if not indicators:
        return "无高级技术分析数据"

    sections = []

    # 1. 波动率分析
    volatility_section = format_volatility_analysis(indicators.get('volatility', {}))
    if volatility_section:
        sections.append(volatility_section)

    # 2. 趋势分析
    trend_section = format_trend_analysis(indicators.get('trend', {}))
    if trend_section:
        sections.append(trend_section)

    # 3. 市场微观结构分析
    microstructure_section = format_microstructure_analysis(indicators.get('microstructure', {}))
    if microstructure_section:
        sections.append(microstructure_section)

    # 4. 统计分析
    statistics_section = format_statistics_analysis(indicators.get('statistics', {}))
    if statistics_section:
        sections.append(statistics_section)

    # 5. 价格行为分析
    price_action_section = format_price_action_analysis(indicators.get('price_action', {}))
    if price_action_section:
        sections.append(price_action_section)

    # 6. 量化因子分析
    factors_section = format_factors_analysis(indicators.get('factors', {}), timeframe)
    if factors_section:
        sections.append(factors_section)

    # 7. 综合分析
    summary_section = generate_summary_analysis(indicators)
    if summary_section:
        sections.append(summary_section)

    return "\n\n".join(sections)


def format_volatility_analysis(volatility_indicators):
    """
    格式化波动率分析

    Args:
        volatility_indicators (dict): 波动率指标

    Returns:
        str: 格式化后的波动率分析
    """
    if not volatility_indicators:
        return ""

    lines = ["### 波动率分析"]

    # 历史波动率
    hv = volatility_indicators.get('historical_volatility')
    if hv is not None and not pd.isna(hv):
        hv_level = "高" if hv > 20 else "中等" if hv > 10 else "低"
        lines.append(f"- 历史波动率: {hv:.2f}%（{hv_level}）")

    # ATR
    atr = volatility_indicators.get('atr')
    if atr is not None and not pd.isna(atr):
        lines.append(f"- 真实波动幅度(ATR): {atr:.5f}")

    # 相对波动率
    rel_vol = volatility_indicators.get('relative_volatility')
    if rel_vol is not None and not pd.isna(rel_vol):
        vol_trend = "上升" if rel_vol > 1.1 else "下降" if rel_vol < 0.9 else "稳定"
        lines.append(f"- 相对波动率: {rel_vol:.2f}（波动率{vol_trend}）")

    # 波动率百分位
    vol_percentile = volatility_indicators.get('volatility_percentile')
    if vol_percentile is not None and not pd.isna(vol_percentile):
        percentile_desc = "极高" if vol_percentile > 0.9 else "高" if vol_percentile > 0.7 else "中等" if vol_percentile > 0.3 else "低" if vol_percentile > 0.1 else "极低"
        lines.append(f"- 波动率百分位: {vol_percentile:.2f}（{percentile_desc}）")

    if len(lines) <= 1:
        return ""

    return "\n".join(lines)


def format_trend_analysis(trend_indicators):
    """
    格式化趋势分析

    Args:
        trend_indicators (dict): 趋势指标

    Returns:
        str: 格式化后的趋势分析
    """
    if not trend_indicators:
        return ""

    lines = ["### 趋势分析"]

    # ADX
    adx = trend_indicators.get('adx')
    if adx is not None and not pd.isna(adx):
        trend_strength = "强" if adx > 25 else "中等" if adx > 15 else "弱"
        lines.append(f"- 平均趋势指数(ADX): {adx:.2f}（趋势强度{trend_strength}）")

    # 线性回归斜率
    slope = trend_indicators.get('normalized_slope')
    if slope is not None and not pd.isna(slope):
        slope_direction = "上升" if slope > 0 else "下降"
        slope_strength = "强" if abs(slope) > 0.01 else "中等" if abs(slope) > 0.005 else "弱"
        lines.append(f"- 价格趋势斜率: {slope:.6f}（{slope_direction}趋势，强度{slope_strength}）")

    # R平方值
    r_squared = trend_indicators.get('r_squared')
    if r_squared is not None and not pd.isna(r_squared):
        fit_quality = "高" if r_squared > 0.7 else "中等" if r_squared > 0.3 else "低"
        lines.append(f"- 趋势线性度: {r_squared:.2f}（{fit_quality}）")

    # 价格与移动平均线的关系
    price_vs_ma20 = trend_indicators.get('price_vs_ma20')
    if price_vs_ma20 is not None and not pd.isna(price_vs_ma20):
        relation = "大幅高于" if price_vs_ma20 > 1 else "高于" if price_vs_ma20 > 0 else "低于" if price_vs_ma20 > -1 else "大幅低于"
        lines.append(f"- 价格相对MA20: {price_vs_ma20:.2f}%（{relation}）")

    # MA20与MA50的关系
    ma20_vs_ma50 = trend_indicators.get('ma20_vs_ma50')
    if ma20_vs_ma50 is not None and not pd.isna(ma20_vs_ma50):
        ma_trend = "上升" if ma20_vs_ma50 > 0 else "下降"
        lines.append(f"- MA20相对MA50: {ma20_vs_ma50:.2f}%（趋势{ma_trend}）")

    # 连续上涨或下跌的天数
    streak = trend_indicators.get('current_streak')
    if streak is not None and not pd.isna(streak):
        streak_type = "上涨" if streak > 0 else "下跌" if streak < 0 else "持平"
        streak_count = abs(streak)
        if streak_count > 0:
            lines.append(f"- 连续{streak_type}天数: {streak_count}天")

    if len(lines) <= 1:
        return ""

    return "\n".join(lines)


def format_microstructure_analysis(microstructure_indicators):
    """
    格式化市场微观结构分析

    Args:
        microstructure_indicators (dict): 市场微观结构指标

    Returns:
        str: 格式化后的市场微观结构分析
    """
    if not microstructure_indicators:
        return ""

    lines = ["### 市场微观结构分析"]

    # 价格波动效率
    efficiency = microstructure_indicators.get('price_efficiency')
    if efficiency is not None and not pd.isna(efficiency):
        efficiency_level = "高" if efficiency > 0.7 else "中等" if efficiency > 0.3 else "低"
        lines.append(f"- 价格波动效率: {efficiency:.2f}（{efficiency_level}）")

    # 成交量加权平均价格
    vwap = microstructure_indicators.get('vwap')
    price_vs_vwap = microstructure_indicators.get('price_vs_vwap')
    if vwap is not None and not pd.isna(vwap) and price_vs_vwap is not None and not pd.isna(price_vs_vwap):
        relation = "高于" if price_vs_vwap > 0 else "低于"
        lines.append(f"- 成交量加权平均价格(VWAP): {vwap:.5f}")
        lines.append(f"- 价格相对VWAP: {price_vs_vwap:.2f}%（{relation}）")

    # 价格范围比率
    range_ratio = microstructure_indicators.get('range_ratio')
    if range_ratio is not None and not pd.isna(range_ratio):
        range_desc = "扩大" if range_ratio > 1.2 else "收窄" if range_ratio < 0.8 else "正常"
        lines.append(f"- 价格范围比率: {range_ratio:.2f}（{range_desc}）")

    # 价格加速度
    acceleration = microstructure_indicators.get('price_acceleration')
    if acceleration is not None and not pd.isna(acceleration):
        accel_desc = "加速上涨" if acceleration > 0.001 else "加速下跌" if acceleration < -0.001 else "稳定"
        lines.append(f"- 价格加速度: {acceleration:.6f}（{accel_desc}）")

    if len(lines) <= 1:
        return ""

    return "\n".join(lines)


def format_statistics_analysis(statistics_indicators):
    """
    格式化统计分析

    Args:
        statistics_indicators (dict): 统计指标

    Returns:
        str: 格式化后的统计分析
    """
    if not statistics_indicators:
        return ""

    lines = ["### 统计分析"]

    # 偏度
    skewness = statistics_indicators.get('skewness')
    if skewness is not None and not pd.isna(skewness):
        skew_desc = "正偏（大涨概率高）" if skewness > 0.5 else "负偏（大跌概率高）" if skewness < -0.5 else "对称（涨跌概率均衡）"
        lines.append(f"- 收益率偏度: {skewness:.2f}（{skew_desc}）")

    # 峰度
    kurtosis = statistics_indicators.get('kurtosis')
    if kurtosis is not None and not pd.isna(kurtosis):
        kurt_desc = "高（极端行情概率高）" if kurtosis > 3 else "低（极端行情概率低）"
        lines.append(f"- 收益率峰度: {kurtosis:.2f}（{kurt_desc}）")

    # 自相关系数
    autocorr = statistics_indicators.get('autocorrelation')
    if autocorr is not None and not pd.isna(autocorr):
        autocorr_desc = "强正相关（趋势延续性强）" if autocorr > 0.3 else "强负相关（趋势反转性强）" if autocorr < -0.3 else "弱相关（随机性强）"
        lines.append(f"- 收益率自相关: {autocorr:.2f}（{autocorr_desc}）")

    # 平稳性检验
    is_stationary = statistics_indicators.get('is_stationary')
    adf_pvalue = statistics_indicators.get('adf_pvalue')
    if is_stationary is not None and adf_pvalue is not None and not pd.isna(adf_pvalue):
        stationary_desc = "平稳（均值回归特性强）" if is_stationary else "非平稳（存在趋势）"
        lines.append(f"- 价格平稳性: {stationary_desc}（p值: {adf_pvalue:.4f}）")

    # Z-Score
    z_score = statistics_indicators.get('z_score')
    if z_score is not None and not pd.isna(z_score):
        zscore_desc = "严重超买" if z_score > 2 else "超买" if z_score > 1 else "超卖" if z_score < -1 else "严重超卖" if z_score < -2 else "中性"
        lines.append(f"- 价格Z-Score: {z_score:.2f}（{zscore_desc}）")

    if len(lines) <= 1:
        return ""

    return "\n".join(lines)


def format_price_action_analysis(price_action_indicators):
    """
    格式化价格行为分析

    Args:
        price_action_indicators (dict): 价格行为指标

    Returns:
        str: 格式化后的价格行为分析
    """
    if not price_action_indicators:
        return ""

    lines = ["### 价格行为分析"]

    # 实体比例
    body_ratio = price_action_indicators.get('body_ratio')
    if body_ratio is not None and not pd.isna(body_ratio):
        body_desc = "大（趋势强）" if body_ratio > 0.7 else "小（趋势弱）" if body_ratio < 0.3 else "中等"
        lines.append(f"- 蜡烛实体比例: {body_ratio:.2f}（{body_desc}）")

    # 蜡烛图形态
    is_doji = price_action_indicators.get('is_doji')
    is_hammer = price_action_indicators.get('is_hammer')
    is_shooting_star = price_action_indicators.get('is_shooting_star')
    is_engulfing = price_action_indicators.get('is_engulfing')

    patterns = []
    if is_doji:
        patterns.append("十字星（犹豫不决）")
    if is_hammer:
        patterns.append("锤子线（潜在反转向上）")
    if is_shooting_star:
        patterns.append("流星线（潜在反转向下）")
    if is_engulfing:
        patterns.append("吞没形态（强势反转信号）")

    if patterns:
        lines.append(f"- 蜡烛图形态: {', '.join(patterns)}")

    # 价格突破
    breakout_high = price_action_indicators.get('breakout_high')
    breakout_low = price_action_indicators.get('breakout_low')

    breakouts = []
    if breakout_high:
        breakouts.append("向上突破（看涨信号）")
    if breakout_low:
        breakouts.append("向下突破（看跌信号）")

    if breakouts:
        lines.append(f"- 价格突破: {', '.join(breakouts)}")

    if len(lines) <= 1:
        return ""

    return "\n".join(lines)


def generate_summary_analysis(indicators):
    """
    生成综合分析

    Args:
        indicators (dict): 所有技术指标

    Returns:
        str: 综合分析
    """
    if not indicators:
        return ""

    lines = ["### 综合分析"]

    # 趋势状态评估
    trend_status = assess_trend_status(indicators)
    if trend_status:
        lines.append(f"- 趋势状态: {trend_status}")

    # 波动率状态评估
    volatility_status = assess_volatility_status(indicators)
    if volatility_status:
        lines.append(f"- 波动率状态: {volatility_status}")

    # 动量状态评估
    momentum_status = assess_momentum_status(indicators)
    if momentum_status:
        lines.append(f"- 动量状态: {momentum_status}")

    # 均值回归可能性评估
    mean_reversion_status = assess_mean_reversion_status(indicators)
    if mean_reversion_status:
        lines.append(f"- 均值回归可能性: {mean_reversion_status}")

    # 市场结构评估
    market_structure = assess_market_structure(indicators)
    if market_structure:
        lines.append(f"- 市场结构: {market_structure}")

    # 潜在信号评估
    potential_signals = identify_potential_signals(indicators)
    if potential_signals:
        lines.append(f"- 潜在信号: {potential_signals}")

    if len(lines) <= 1:
        return ""

    return "\n".join(lines)


def assess_trend_status(indicators):
    """
    评估趋势状态

    Args:
        indicators (dict): 所有技术指标

    Returns:
        str: 趋势状态描述
    """
    trend_indicators = indicators.get('trend', {})
    price_action = indicators.get('price_action', {})
    factors = indicators.get('factors', {})

    # 获取关键指标
    adx = trend_indicators.get('adx')
    slope = trend_indicators.get('normalized_slope')
    price_vs_ma20 = trend_indicators.get('price_vs_ma20')
    ma20_vs_ma50 = trend_indicators.get('ma20_vs_ma50')
    current_streak = trend_indicators.get('current_streak')

    momentum_20 = factors.get('momentum_20')

    # 判断趋势方向和强度
    trend_direction = None
    trend_strength = None

    # 根据斜率判断方向
    if slope is not None and not pd.isna(slope):
        if slope > 0:
            trend_direction = "上升"
        elif slope < 0:
            trend_direction = "下降"

    # 如果斜率不可用，使用价格与MA20的关系判断
    if trend_direction is None and price_vs_ma20 is not None and not pd.isna(price_vs_ma20):
        if price_vs_ma20 > 0:
            trend_direction = "上升"
        elif price_vs_ma20 < 0:
            trend_direction = "下降"

    # 如果以上都不可用，使用MA20与MA50的关系判断
    if trend_direction is None and ma20_vs_ma50 is not None and not pd.isna(ma20_vs_ma50):
        if ma20_vs_ma50 > 0:
            trend_direction = "上升"
        elif ma20_vs_ma50 < 0:
            trend_direction = "下降"

    # 如果以上都不可用，使用动量判断
    if trend_direction is None and momentum_20 is not None and not pd.isna(momentum_20):
        if momentum_20 > 0:
            trend_direction = "上升"
        elif momentum_20 < 0:
            trend_direction = "下降"

    # 根据ADX判断强度
    if adx is not None and not pd.isna(adx):
        if adx > 25:
            trend_strength = "强"
        elif adx > 15:
            trend_strength = "中等"
        else:
            trend_strength = "弱"

    # 如果ADX不可用，使用连续天数判断
    if trend_strength is None and current_streak is not None and not pd.isna(current_streak):
        if abs(current_streak) >= 5:
            trend_strength = "强"
        elif abs(current_streak) >= 3:
            trend_strength = "中等"
        else:
            trend_strength = "弱"

    # 组合结果
    if trend_direction and trend_strength:
        return f"{trend_strength}{trend_direction}趋势"
    elif trend_direction:
        return f"{trend_direction}趋势"
    elif trend_strength:
        return f"趋势强度{trend_strength}"
    else:
        return "无明确趋势"


def assess_volatility_status(indicators):
    """
    评估波动率状态

    Args:
        indicators (dict): 所有技术指标

    Returns:
        str: 波动率状态描述
    """
    volatility = indicators.get('volatility', {})
    factors = indicators.get('factors', {})

    # 获取关键指标
    historical_volatility = volatility.get('historical_volatility')
    relative_volatility = volatility.get('relative_volatility')
    volatility_percentile = volatility.get('volatility_percentile')

    realized_vol = factors.get('realized_volatility')
    vol_change = factors.get('volatility_change')

    # 判断波动率水平
    vol_level = None
    vol_trend = None

    # 根据历史波动率判断水平
    if historical_volatility is not None and not pd.isna(historical_volatility):
        if historical_volatility > 20:
            vol_level = "高"
        elif historical_volatility > 10:
            vol_level = "中等"
        else:
            vol_level = "低"

    # 如果历史波动率不可用，使用实现波动率判断
    if vol_level is None and realized_vol is not None and not pd.isna(realized_vol):
        if realized_vol > 20:
            vol_level = "高"
        elif realized_vol > 10:
            vol_level = "中等"
        else:
            vol_level = "低"

    # 根据相对波动率判断趋势
    if relative_volatility is not None and not pd.isna(relative_volatility):
        if relative_volatility > 1.1:
            vol_trend = "上升"
        elif relative_volatility < 0.9:
            vol_trend = "下降"
        else:
            vol_trend = "稳定"

    # 如果相对波动率不可用，使用波动率变化判断
    if vol_trend is None and vol_change is not None and not pd.isna(vol_change):
        if vol_change > 10:
            vol_trend = "上升"
        elif vol_change < -10:
            vol_trend = "下降"
        else:
            vol_trend = "稳定"

    # 组合结果
    if vol_level and vol_trend:
        return f"{vol_level}波动率，{vol_trend}趋势"
    elif vol_level:
        return f"{vol_level}波动率"
    elif vol_trend:
        return f"波动率{vol_trend}"
    else:
        return "波动率状态不明确"


def assess_momentum_status(indicators):
    """
    评估动量状态

    Args:
        indicators (dict): 所有技术指标

    Returns:
        str: 动量状态描述
    """
    factors = indicators.get('factors', {})

    # 获取关键指标
    momentum_5 = factors.get('momentum_5')
    momentum_10 = factors.get('momentum_10')
    momentum_20 = factors.get('momentum_20')

    # 判断短期、中期和长期动量
    short_term = None
    mid_term = None
    long_term = None

    if momentum_5 is not None and not pd.isna(momentum_5):
        if momentum_5 > 0.01:
            short_term = "强上升"
        elif momentum_5 > 0:
            short_term = "弱上升"
        elif momentum_5 < -0.01:
            short_term = "强下降"
        else:
            short_term = "弱下降"

    if momentum_10 is not None and not pd.isna(momentum_10):
        if momentum_10 > 0.02:
            mid_term = "强上升"
        elif momentum_10 > 0:
            mid_term = "弱上升"
        elif momentum_10 < -0.02:
            mid_term = "强下降"
        else:
            mid_term = "弱下降"

    if momentum_20 is not None and not pd.isna(momentum_20):
        if momentum_20 > 0.03:
            long_term = "强上升"
        elif momentum_20 > 0:
            long_term = "弱上升"
        elif momentum_20 < -0.03:
            long_term = "强下降"
        else:
            long_term = "弱下降"

    # 判断动量分歧
    divergence = False
    if short_term and mid_term and (
        (short_term.endswith("上升") and mid_term.endswith("下降")) or
        (short_term.endswith("下降") and mid_term.endswith("上升"))
    ):
        divergence = True

    # 组合结果
    if short_term and mid_term and long_term:
        if divergence:
            return f"短期{short_term}，中期{mid_term}，长期{long_term}，存在动量分歧"
        else:
            return f"短期{short_term}，中期{mid_term}，长期{long_term}"
    elif short_term and mid_term:
        if divergence:
            return f"短期{short_term}，中期{mid_term}，存在动量分歧"
        else:
            return f"短期{short_term}，中期{mid_term}"
    elif short_term:
        return f"短期{short_term}"
    elif mid_term:
        return f"中期{mid_term}"
    elif long_term:
        return f"长期{long_term}"
    else:
        return "动量状态不明确"


def assess_mean_reversion_status(indicators):
    """
    评估均值回归可能性

    Args:
        indicators (dict): 所有技术指标

    Returns:
        str: 均值回归可能性描述
    """
    statistics = indicators.get('statistics', {})
    factors = indicators.get('factors', {})

    # 获取关键指标
    z_score = statistics.get('z_score')
    is_stationary = statistics.get('is_stationary')

    deviation_ma20 = factors.get('deviation_ma20')
    deviation_zscore = factors.get('deviation_zscore')

    # 判断均值回归可能性
    reversion_probability = None
    direction = None

    # 根据Z-Score判断
    if z_score is not None and not pd.isna(z_score):
        if z_score > 2:
            reversion_probability = "高"
            direction = "向下"
        elif z_score < -2:
            reversion_probability = "高"
            direction = "向上"
        elif z_score > 1:
            reversion_probability = "中等"
            direction = "向下"
        elif z_score < -1:
            reversion_probability = "中等"
            direction = "向上"
        else:
            reversion_probability = "低"

    # 如果Z-Score不可用，使用偏离度判断
    if reversion_probability is None and deviation_ma20 is not None and not pd.isna(deviation_ma20):
        if deviation_ma20 > 3:
            reversion_probability = "高"
            direction = "向下"
        elif deviation_ma20 < -3:
            reversion_probability = "高"
            direction = "向上"
        elif deviation_ma20 > 1.5:
            reversion_probability = "中等"
            direction = "向下"
        elif deviation_ma20 < -1.5:
            reversion_probability = "中等"
            direction = "向上"
        else:
            reversion_probability = "低"

    # 考虑平稳性
    if is_stationary is not None:
        if is_stationary and reversion_probability in ["中等", "高"]:
            reversion_probability = "高"  # 如果价格平稳，提高均值回归概率

    # 组合结果
    if reversion_probability and direction and reversion_probability != "低":
        return f"{reversion_probability}（{direction}）"
    elif reversion_probability:
        return f"{reversion_probability}"
    else:
        return "均值回归可能性不明确"


def assess_market_structure(indicators):
    """
    评估市场结构

    Args:
        indicators (dict): 所有技术指标

    Returns:
        str: 市场结构描述
    """
    microstructure = indicators.get('microstructure', {})
    price_action = indicators.get('price_action', {})

    # 获取关键指标
    efficiency = microstructure.get('price_efficiency')
    range_ratio = microstructure.get('range_ratio')

    breakout_high = price_action.get('breakout_high')
    breakout_low = price_action.get('breakout_low')

    # 判断市场结构
    structure_type = None

    # 根据价格效率判断
    if efficiency is not None and not pd.isna(efficiency):
        if efficiency > 0.7:
            structure_type = "趋势型"
        elif efficiency < 0.3:
            structure_type = "震荡型"
        else:
            structure_type = "混合型"

    # 考虑价格范围
    if range_ratio is not None and not pd.isna(range_ratio):
        if range_ratio > 1.2 and (breakout_high or breakout_low):
            structure_type = "突破型"
        elif range_ratio < 0.8:
            structure_type = "收缩型"

    # 返回结果
    if structure_type:
        return structure_type
    else:
        return "市场结构不明确"


def identify_potential_signals(indicators):
    """
    识别潜在信号

    Args:
        indicators (dict): 所有技术指标

    Returns:
        str: 潜在信号描述
    """
    trend = indicators.get('trend', {})
    volatility = indicators.get('volatility', {})
    statistics = indicators.get('statistics', {})
    price_action = indicators.get('price_action', {})
    factors = indicators.get('factors', {})

    signals = []

    # 趋势信号
    slope = trend.get('normalized_slope')
    price_vs_ma20 = trend.get('price_vs_ma20')
    ma20_vs_ma50 = trend.get('ma20_vs_ma50')

    if slope is not None and not pd.isna(slope) and price_vs_ma20 is not None and not pd.isna(price_vs_ma20):
        if slope > 0 and price_vs_ma20 > 0 and (ma20_vs_ma50 is None or ma20_vs_ma50 > 0):
            signals.append("趋势看涨")
        elif slope < 0 and price_vs_ma20 < 0 and (ma20_vs_ma50 is None or ma20_vs_ma50 < 0):
            signals.append("趋势看跌")

    # 均值回归信号
    z_score = statistics.get('z_score')

    if z_score is not None and not pd.isna(z_score):
        if z_score > 2:
            signals.append("超买（均值回归看跌）")
        elif z_score < -2:
            signals.append("超卖（均值回归看涨）")

    # 价格行为信号
    is_hammer = price_action.get('is_hammer')
    is_shooting_star = price_action.get('is_shooting_star')
    is_engulfing = price_action.get('is_engulfing')
    breakout_high = price_action.get('breakout_high')
    breakout_low = price_action.get('breakout_low')

    if is_hammer:
        signals.append("锤子线（潜在看涨）")
    if is_shooting_star:
        signals.append("流星线（潜在看跌）")
    if is_engulfing:
        signals.append("吞没形态（潜在反转）")
    if breakout_high:
        signals.append("向上突破（看涨）")
    if breakout_low:
        signals.append("向下突破（看跌）")

    # 动量信号
    momentum_5 = factors.get('momentum_5')
    momentum_20 = factors.get('momentum_20')

    if momentum_5 is not None and not pd.isna(momentum_5) and momentum_20 is not None and not pd.isna(momentum_20):
        if momentum_5 > 0.01 and momentum_20 > 0:
            signals.append("动量看涨")
        elif momentum_5 < -0.01 and momentum_20 < 0:
            signals.append("动量看跌")

    # 返回结果
    if signals:
        return "，".join(signals)
    else:
        return "无明确信号"


def format_factors_analysis(factors_indicators, timeframe):
    """
    格式化量化因子分析

    Args:
        factors_indicators (dict): 量化因子指标
        timeframe (str): 时间周期

    Returns:
        str: 格式化后的量化因子分析
    """
    if not factors_indicators:
        return ""

    lines = ["### 量化因子分析"]

    # 动量因子
    momentum_5 = factors_indicators.get('momentum_5')
    momentum_10 = factors_indicators.get('momentum_10')
    momentum_20 = factors_indicators.get('momentum_20')

    if momentum_5 is not None and not pd.isna(momentum_5):
        momentum_desc = "强上升" if momentum_5 > 0.01 else "弱上升" if momentum_5 > 0 else "强下降" if momentum_5 < -0.01 else "弱下降"
        lines.append(f"- 短期动量(5周期): {momentum_5:.2%}（{momentum_desc}）")

    if momentum_10 is not None and not pd.isna(momentum_10):
        momentum_desc = "强上升" if momentum_10 > 0.02 else "弱上升" if momentum_10 > 0 else "强下降" if momentum_10 < -0.02 else "弱下降"
        lines.append(f"- 中期动量(10周期): {momentum_10:.2%}（{momentum_desc}）")

    if momentum_20 is not None and not pd.isna(momentum_20):
        momentum_desc = "强上升" if momentum_20 > 0.03 else "弱上升" if momentum_20 > 0 else "强下降" if momentum_20 < -0.03 else "弱下降"
        lines.append(f"- 长期动量(20周期): {momentum_20:.2%}（{momentum_desc}）")

    # 均值回归因子
    deviation_ma20 = factors_indicators.get('deviation_ma20')
    deviation_zscore = factors_indicators.get('deviation_zscore')

    if deviation_ma20 is not None and not pd.isna(deviation_ma20):
        deviation_desc = "严重超买" if deviation_ma20 > 3 else "超买" if deviation_ma20 > 1.5 else "超卖" if deviation_ma20 < -1.5 else "严重超卖" if deviation_ma20 < -3 else "中性"
        lines.append(f"- MA20偏离度: {deviation_ma20:.2f}%（{deviation_desc}）")

    if deviation_zscore is not None and not pd.isna(deviation_zscore):
        zscore_desc = "极端（均值回归概率高）" if abs(deviation_zscore) > 2 else "正常范围内"
        lines.append(f"- 偏离度Z-Score: {deviation_zscore:.2f}（{zscore_desc}）")

    # 波动率因子
    realized_vol = factors_indicators.get('realized_volatility')
    vol_change = factors_indicators.get('volatility_change')

    if realized_vol is not None and not pd.isna(realized_vol):
        vol_desc = "高" if realized_vol > 20 else "中等" if realized_vol > 10 else "低"
        lines.append(f"- 实现波动率: {realized_vol:.2f}%（{vol_desc}）")

    if vol_change is not None and not pd.isna(vol_change):
        vol_trend = "上升（风险增加）" if vol_change > 10 else "下降（风险减少）" if vol_change < -10 else "稳定"
        lines.append(f"- 波动率变化: {vol_change:.2f}%（{vol_trend}）")

    # 流动性因子
    volume_change = factors_indicators.get('volume_change')
    volume_strength = factors_indicators.get('volume_strength')

    if volume_change is not None and not pd.isna(volume_change):
        volume_trend = "增加（流动性提高）" if volume_change > 10 else "减少（流动性降低）" if volume_change < -10 else "稳定"
        lines.append(f"- 成交量变化: {volume_change:.2f}%（{volume_trend}）")

    if volume_strength is not None and not pd.isna(volume_strength):
        strength_desc = "强（确认信号）" if volume_strength > 1.5 else "弱（信号可靠性低）" if volume_strength < 0.5 else "正常"
        lines.append(f"- 成交量强度: {volume_strength:.2f}（{strength_desc}）")

    # 时间因子
    if timeframe == '15min':
        is_overlap_session = factors_indicators.get('is_overlap_session')
        is_asian_session = factors_indicators.get('is_asian_session')
        is_european_session = factors_indicators.get('is_european_session')
        is_american_session = factors_indicators.get('is_american_session')

        sessions = []
        if is_overlap_session:
            sessions.append("欧美重叠时段（波动性通常较高）")
        if is_asian_session:
            sessions.append("亚洲时段（波动性通常较低）")
        if is_european_session:
            sessions.append("欧洲时段（波动性中等）")
        if is_american_session:
            sessions.append("美洲时段（波动性较高）")

        if sessions:
            lines.append(f"- 当前交易时段: {', '.join(sessions)}")

    if len(lines) <= 1:
        return ""

    return "\n".join(lines)
