"""
统一日志管理系统
用于优化和管理整个项目的日志输出
"""
import os
import logging
from datetime import datetime
from enum import Enum

# 智能检查是否跳过MT4连接
def should_skip_mt4_connection():
    """智能判断是否应该跳过MT4连接"""
    manual_skip = os.environ.get('SKIP_MT4_CONNECTION', '').lower()
    if manual_skip == 'true':
        return True
    elif manual_skip == 'false':
        return False

    try:
        from app.utils.market_time_checker import is_market_open
        return not is_market_open()
    except Exception:
        return False

class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class LogCategory(Enum):
    """日志分类枚举"""
    SYSTEM = "系统"
    MT4 = "MT4"
    ANALYSIS = "分析"
    TRADING = "交易"
    DATABASE = "数据库"
    API = "API"
    SCHEDULER = "调度器"
    ERROR = "错误"

class LoggerManager:
    """统一日志管理器"""

    def __init__(self):
        self.loggers = {}
        self.setup_loggers()

    def setup_loggers(self):
        """设置日志记录器"""
        # 创建主日志记录器
        self.main_logger = logging.getLogger('forex_system')
        self.main_logger.setLevel(logging.INFO)

        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.main_logger.addHandler(console_handler)

        # 创建文件处理器
        file_handler = logging.FileHandler('forex_system.log', encoding='utf-8')
        file_handler.setFormatter(formatter)
        self.main_logger.addHandler(file_handler)

    def get_logger(self, name):
        """获取指定名称的日志记录器"""
        if name not in self.loggers:
            self.loggers[name] = logging.getLogger(f'forex_system.{name}')
        return self.loggers[name]

    def log(self, category: LogCategory, level: LogLevel, message: str, details=None):
        """
        统一日志记录方法

        Args:
            category: 日志分类
            level: 日志级别
            message: 日志消息
            details: 详细信息（可选）
        """
        # 格式化时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 构建日志消息
        log_message = f"[{category.value}] {message}"
        if details:
            log_message += f" | 详情: {details}"

        # 根据级别记录日志
        if level == LogLevel.DEBUG:
            self.main_logger.debug(log_message)
        elif level == LogLevel.INFO:
            self.main_logger.info(log_message)
        elif level == LogLevel.WARNING:
            self.main_logger.warning(log_message)
        elif level == LogLevel.ERROR:
            self.main_logger.error(log_message)
        elif level == LogLevel.CRITICAL:
            self.main_logger.critical(log_message)

    def system_log(self, level: LogLevel, message: str, details=None):
        """系统日志"""
        self.log(LogCategory.SYSTEM, level, message, details)

    def mt4_log(self, level: LogLevel, message: str, details=None):
        """MT4相关日志"""
        # 如果跳过MT4连接，添加特殊标记
        if should_skip_mt4_connection() and level == LogLevel.INFO:
            # 判断是手动设置还是自动判断
            manual_skip = os.environ.get('SKIP_MT4_CONNECTION', '').lower()
            if manual_skip == 'true':
                message = f"⚠️ [手动测试模式] {message}"
            else:
                message = f"🕒 [市场关闭模式] {message}"
        self.log(LogCategory.MT4, level, message, details)

    def analysis_log(self, level: LogLevel, message: str, details=None):
        """分析相关日志"""
        self.log(LogCategory.ANALYSIS, level, message, details)

    def trading_log(self, level: LogLevel, message: str, details=None):
        """交易相关日志"""
        self.log(LogCategory.TRADING, level, message, details)

    def database_log(self, level: LogLevel, message: str, details=None):
        """数据库相关日志"""
        self.log(LogCategory.DATABASE, level, message, details)

    def api_log(self, level: LogLevel, message: str, details=None):
        """API相关日志"""
        self.log(LogCategory.API, level, message, details)

    def scheduler_log(self, level: LogLevel, message: str, details=None):
        """调度器相关日志"""
        self.log(LogCategory.SCHEDULER, level, message, details)

    def error_log(self, message: str, details=None):
        """错误日志"""
        self.log(LogCategory.ERROR, LogLevel.ERROR, message, details)

# 创建全局日志管理器实例
logger_manager = LoggerManager()

# 便捷函数
def log_system(level: LogLevel, message: str, details=None):
    """系统日志便捷函数"""
    logger_manager.system_log(level, message, details)

def log_mt4(level: LogLevel, message: str, details=None):
    """MT4日志便捷函数"""
    logger_manager.mt4_log(level, message, details)

def log_analysis(level: LogLevel, message: str, details=None):
    """分析日志便捷函数"""
    logger_manager.analysis_log(level, message, details)

def log_trading(level: LogLevel, message: str, details=None):
    """交易日志便捷函数"""
    logger_manager.trading_log(level, message, details)

def log_database(level: LogLevel, message: str, details=None):
    """数据库日志便捷函数"""
    logger_manager.database_log(level, message, details)

def log_api(level: LogLevel, message: str, details=None):
    """API日志便捷函数"""
    logger_manager.api_log(level, message, details)

def log_scheduler(level: LogLevel, message: str, details=None):
    """调度器日志便捷函数"""
    logger_manager.scheduler_log(level, message, details)

def log_error(message: str, details=None):
    """错误日志便捷函数"""
    logger_manager.error_log(message, details)

# 兼容性函数（保持向后兼容）
def print_log(category: str, message: str, level: str = "INFO"):
    """
    兼容性日志函数，用于替换项目中的print语句

    Args:
        category: 日志分类
        message: 日志消息
        level: 日志级别
    """
    try:
        log_level = LogLevel(level.upper())
        log_category = LogCategory(category)
        logger_manager.log(log_category, log_level, message)
    except ValueError:
        # 如果分类或级别不匹配，使用默认值
        logger_manager.system_log(LogLevel.INFO, f"[{category}] {message}")

# 简化的日志函数
def log_info(message: str, category: str = "系统"):
    """信息日志"""
    print_log(category, message, "INFO")

def log_warning(message: str, category: str = "系统"):
    """警告日志"""
    print_log(category, message, "WARNING")

def log_error_simple(message: str, category: str = "错误"):
    """简单错误日志"""
    print_log(category, message, "ERROR")
