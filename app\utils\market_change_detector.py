"""
市场变化检测模块
用于检测市场变化并触发分析
"""
import time
import threading
import numpy as np
import traceback
from datetime import datetime, timedelta
import pandas as pd

from app.utils import db_client
from app.utils import mt4_client
from app.utils import forex_data_processor
from app.utils.error_logger import log_error, ErrorType, OperationType

# 全局变量，用于存储最近的市场数据
last_price = None
last_indicators = None
last_check_time = None
last_analysis_time = None

# 从环境变量加载配置
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(encoding='utf-8')

# 配置参数
MIN_ANALYSIS_INTERVAL = int(os.environ.get('MIN_ANALYSIS_INTERVAL', '10'))  # 最小分析间隔（分钟）
PRICE_CHANGE_THRESHOLD = float(os.environ.get('PRICE_CHANGE_THRESHOLD', '10')) / 10000  # 价格变化阈值（点数转换为价格）
RSI_CHANGE_THRESHOLD = float(os.environ.get('RSI_CHANGE_THRESHOLD', '5.0'))  # RSI变化阈值
MACD_CHANGE_THRESHOLD = float(os.environ.get('MACD_CHANGE_THRESHOLD', '0.0002'))  # MACD变化阈值
VOLATILITY_THRESHOLD = float(os.environ.get('VOLATILITY_THRESHOLD', '5')) / 10000  # 波动率阈值（点数转换为价格）
VOLUME_SPIKE_THRESHOLD = float(os.environ.get('VOLUME_SPIKE_THRESHOLD', '2.0'))  # 交易量突增阈值（相对于平均值）

print(f'市场变化检测器配置:')
print(f'- 最小分析间隔: {MIN_ANALYSIS_INTERVAL}分钟')
print(f'- 价格变化阈值: {PRICE_CHANGE_THRESHOLD * 10000}点')
print(f'- RSI变化阈值: {RSI_CHANGE_THRESHOLD}')
print(f'- MACD变化阈值: {MACD_CHANGE_THRESHOLD}')
print(f'- 波动率阈值: {VOLATILITY_THRESHOLD * 10000}点')
print(f'- 交易量突增阈值: {VOLUME_SPIKE_THRESHOLD}倍')

# 检测线程
detector_thread = None
is_running = False
callback_function = None


def start_detector(callback=None, check_interval=60):
    """
    启动市场变化检测器

    Args:
        callback (function): 当检测到市场变化时调用的回调函数
        check_interval (int): 检查间隔（秒）
    """
    global detector_thread, is_running, callback_function

    if is_running and detector_thread and detector_thread.is_alive():
        print('市场变化检测器已经在运行')
        return

    callback_function = callback
    is_running = True

    # 创建并启动检测线程
    detector_thread = threading.Thread(target=_run_detector, args=(check_interval,), daemon=True)
    detector_thread.start()

    print(f'市场变化检测器已启动，检查间隔: {check_interval}秒')


def stop_detector():
    """停止市场变化检测器"""
    global is_running

    if not is_running:
        print('市场变化检测器未运行')
        return

    is_running = False
    print('市场变化检测器已停止')


def _run_detector(check_interval):
    """
    运行检测器主循环

    Args:
        check_interval (int): 检查间隔（秒）
    """
    global last_check_time

    print('市场变化检测器线程已启动')

    # 初始化错误计数器
    error_count = 0
    max_consecutive_errors = 5

    # 初始化重试延迟
    retry_delay = check_interval

    while is_running:
        try:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 开始检测市场变化')

            # 记录检查时间
            last_check_time = now

            # 检测市场变化
            change_detected, change_reason = detect_market_change()

            if change_detected:
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到市场变化: {change_reason}')

                # 检查是否可以执行分析（避免过于频繁）
                if can_perform_analysis():
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 触发分析')

                    # 调用回调函数
                    if callback_function:
                        try:
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 调用回调函数: {change_reason}')
                            callback_function(change_reason)
                            # 成功执行回调，重置错误计数
                            error_count = 0
                            retry_delay = check_interval
                        except Exception as callback_error:
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行回调函数失败: {callback_error}')
                            import traceback
                            traceback.print_exc()
                            # 增加错误计数
                            error_count += 1
                else:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 分析间隔过短，跳过本次分析')
            else:
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] {change_reason}')
                # 成功检测（即使没有变化），重置错误计数
                error_count = 0
                retry_delay = check_interval

            # 等待下一次检查
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {check_interval} 秒后进行下一次检测')
            time.sleep(check_interval)

        except KeyboardInterrupt:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 用户中断了市场变化检测器')
            break
        except Exception as error:
            error_count += 1
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 市场变化检测器运行出错 ({error_count}/{max_consecutive_errors}): {error}')
            import traceback
            traceback.print_exc()

            # 如果连续错误次数过多，增加等待时间
            if error_count >= max_consecutive_errors:
                retry_delay = min(300, retry_delay * 2)  # 最多等待5分钟
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 连续错误次数过多，增加等待时间到 {retry_delay} 秒')
                error_count = 0  # 重置错误计数

            # 等待一段时间后重试
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 等待 {retry_delay} 秒后重试')
            time.sleep(retry_delay)


def detect_market_change():
    """
    检测市场变化

    Returns:
        tuple: (是否检测到变化, 变化原因)
    """
    global last_price, last_indicators

    try:
        # 获取当前价格
        current_price = get_current_price()
        if current_price is None:
            print("无法获取当前价格，跳过本次检测")
            return False, "无法获取当前价格"

        # 获取最新的K线数据
        klines = get_recent_klines(15, 20)  # 获取15分钟周期的最近20根K线
        if not klines:
            print("无法获取K线数据，跳过本次检测")
            return False, "无法获取K线数据"

        # 确保有足够的K线数据
        if len(klines) < 5:  # 至少需要5根K线才能进行基本分析
            print(f"警告: K线数据不足，当前只有{len(klines)}根")
            # 如果数据不足，但至少有1根K线，仍然尝试进行有限的分析
            if len(klines) < 1:
                print("K线数据不足，无法进行分析，跳过本次检测")
                return False, "K线数据不足，无法进行分析"

        # 计算当前技术指标
        current_indicators = calculate_current_indicators(klines)
        if not current_indicators:
            print("无法计算当前技术指标，跳过本次检测")
            return False, "无法计算当前技术指标"

        # 如果是首次检查，保存数据并返回
        if last_price is None or last_indicators is None:
            print("首次检查，建立基准数据")
            last_price = current_price
            last_indicators = current_indicators
            return False, "首次检查，建立基准数据"

        # 检查价格变化
        try:
            price_change = abs(current_price - last_price)
            print(f"当前价格: {current_price}, 上次价格: {last_price}, 变化: {price_change:.5f}, 阈值: {PRICE_CHANGE_THRESHOLD:.5f}")
            if price_change >= PRICE_CHANGE_THRESHOLD:
                last_price = current_price
                last_indicators = current_indicators
                return True, f"价格变化超过阈值: {price_change:.5f}"
        except Exception as e:
            print(f"检查价格变化时出错: {e}")

        # 检查RSI变化（如果有RSI数据）
        try:
            if 'rsi' in current_indicators and 'rsi' in last_indicators and current_indicators['rsi'] is not None and last_indicators['rsi'] is not None:
                rsi_change = abs(current_indicators['rsi'] - last_indicators['rsi'])
                print(f"当前RSI: {current_indicators['rsi']}, 上次RSI: {last_indicators['rsi']}, 变化: {rsi_change:.2f}, 阈值: {RSI_CHANGE_THRESHOLD:.2f}")
                if rsi_change >= RSI_CHANGE_THRESHOLD:
                    last_price = current_price
                    last_indicators = current_indicators
                    return True, f"RSI变化超过阈值: {rsi_change:.2f}"
        except Exception as e:
            print(f"检查RSI变化时出错: {e}")

        # 检查MACD变化（如果有MACD数据）
        try:
            if ('macd' in current_indicators and 'macd' in last_indicators and
                current_indicators['macd'] is not None and last_indicators['macd'] is not None):

                if ('macdLine' in current_indicators['macd'] and 'macdLine' in last_indicators['macd'] and
                    current_indicators['macd']['macdLine'] is not None and last_indicators['macd']['macdLine'] is not None):

                    macd_change = abs(current_indicators['macd']['macdLine'] - last_indicators['macd']['macdLine'])
                    print(f"当前MACD: {current_indicators['macd']['macdLine']}, 上次MACD: {last_indicators['macd']['macdLine']}, 变化: {macd_change:.5f}, 阈值: {MACD_CHANGE_THRESHOLD:.5f}")

                    if macd_change >= MACD_CHANGE_THRESHOLD:
                        last_price = current_price
                        last_indicators = current_indicators
                        return True, f"MACD变化超过阈值: {macd_change:.5f}"

                # 检查MACD金叉/死叉（如果有完整的MACD数据）
                if ('macdLine' in current_indicators['macd'] and 'signalLine' in current_indicators['macd'] and
                    'macdLine' in last_indicators['macd'] and 'signalLine' in last_indicators['macd'] and
                    current_indicators['macd']['macdLine'] is not None and current_indicators['macd']['signalLine'] is not None and
                    last_indicators['macd']['macdLine'] is not None and last_indicators['macd']['signalLine'] is not None):

                    current_macd_above = current_indicators['macd']['macdLine'] > current_indicators['macd']['signalLine']
                    last_macd_above = last_indicators['macd']['macdLine'] > last_indicators['macd']['signalLine']

                    print(f"当前MACD位置: {'上方' if current_macd_above else '下方'}, 上次MACD位置: {'上方' if last_macd_above else '下方'}")

                    if current_macd_above != last_macd_above:
                        last_price = current_price
                        last_indicators = current_indicators
                        cross_type = "金叉" if current_macd_above else "死叉"
                        return True, f"MACD发生{cross_type}"
        except Exception as e:
            print(f"检查MACD变化时出错: {e}")

        # 检查波动率（如果K线数据足够）
        try:
            if len(klines) >= 5:
                volatility = calculate_volatility(klines)
                print(f"当前波动率: {volatility:.5f}, 阈值: {VOLATILITY_THRESHOLD:.5f}")
                if volatility >= VOLATILITY_THRESHOLD:
                    last_price = current_price
                    last_indicators = current_indicators
                    return True, f"市场波动率增加: {volatility:.5f}"
        except Exception as e:
            print(f"检查波动率时出错: {e}")

        # 检查交易量突增（如果K线数据足够）
        try:
            if len(klines) >= 10:
                volume_spike = detect_volume_spike(klines)
                if volume_spike:
                    print("检测到交易量突增")
                    last_price = current_price
                    last_indicators = current_indicators
                    return True, "交易量突增"
        except Exception as e:
            print(f"检查交易量突增时出错: {e}")

        # 检查价格突破（如果K线数据足够）
        try:
            if len(klines) >= 10:
                breakout = detect_price_breakout(klines)
                if breakout:
                    print(f"检测到价格突破: {breakout}")
                    last_price = current_price
                    last_indicators = current_indicators
                    return True, f"价格突破: {breakout}"
        except Exception as e:
            print(f"检查价格突破时出错: {e}")

        # 更新最后的价格和指标
        last_price = current_price
        last_indicators = current_indicators

        print("未检测到显著变化")
        return False, "未检测到显著变化"

    except Exception as error:
        print(f'检测市场变化失败: {error}')
        import traceback
        traceback.print_exc()
        return False, f"检测出错: {error}"


def get_current_price():
    """
    获取当前价格

    Returns:
        float: 当前价格
    """
    # 最大重试次数
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 尝试连接MT4服务器
            if not mt4_client.mt4_client.is_connected:
                print('MT4客户端未连接，尝试连接...')
                mt4_client.mt4_client.connect()
                # 等待连接建立
                time.sleep(1)

            # 获取市场信息
            market_info_response = mt4_client.mt4_client.get_market_info('EURUSD')
            if market_info_response and market_info_response.get('status') == 'success':
                bid = float(market_info_response['data']['bid'])
                ask = float(market_info_response['data']['ask'])
                return (bid + ask) / 2

            # 如果获取失败但没有抛出异常，增加重试计数
            retry_count += 1
            if retry_count < max_retries:
                print(f'获取市场信息失败，{retry_count}/{max_retries}次重试...')
                time.sleep(1)  # 等待1秒后重试
            else:
                print('达到最大重试次数，无法获取当前价格')
                return None
        except Exception as error:
            print(f'获取当前价格失败: {error}')
            retry_count += 1
            if retry_count < max_retries:
                print(f'发生错误，{retry_count}/{max_retries}次重试...')
                time.sleep(1)  # 等待1秒后重试
            else:
                print('达到最大重试次数，无法获取当前价格')
                return None

    return None


def get_recent_klines(period_minutes, count):
    """
    获取最近的K线数据

    Args:
        period_minutes (int): 周期（分钟）
        count (int): 数量

    Returns:
        list: K线数据
    """
    # 最大重试次数
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            from app.services.forex_trading_service import get_aggregated_klines
            klines = get_aggregated_klines(period_minutes, count)

            # 验证K线数据
            if klines and len(klines) > 0:
                print(f'成功获取{len(klines)}根{period_minutes}分钟K线数据')
                return klines
            else:
                print(f'获取到的K线数据为空')
                retry_count += 1
                if retry_count < max_retries:
                    print(f'尝试重新获取K线数据，{retry_count}/{max_retries}次重试...')
                    time.sleep(1)  # 等待1秒后重试
                else:
                    print('达到最大重试次数，无法获取K线数据')
                    return []
        except Exception as error:
            print(f'获取K线数据失败: {error}')
            import traceback
            traceback.print_exc()

            retry_count += 1
            if retry_count < max_retries:
                print(f'发生错误，{retry_count}/{max_retries}次重试...')
                time.sleep(1)  # 等待1秒后重试
            else:
                print('达到最大重试次数，无法获取K线数据')
                return []

    return []


def calculate_current_indicators(klines):
    """
    计算当前技术指标

    Args:
        klines (list): K线数据

    Returns:
        dict: 技术指标
    """
    try:
        # 检查K线数据是否有效
        if not klines or len(klines) < 5:
            print(f'K线数据不足，无法计算技术指标，当前只有 {len(klines) if klines else 0} 根K线')
            return {
                'ma': {'5': None, '10': None, '20': None, '50': None},
                'rsi': 50,  # 默认中性值
                'macd': {'macdLine': 0, 'signalLine': 0, 'histogram': 0},
                'bollinger': {'upper': None, 'middle': None, 'lower': None},
                'momentum': 0,
                'advanced': {},
                'advancedAnalysis': '数据不足，无法进行高级分析'
            }

        from app.services.forex_trading_service import calculate_indicators
        indicators = calculate_indicators(klines, '15min')

        # 验证指标数据
        if not indicators:
            print('计算指标返回空结果')
            return None

        # 确保关键指标存在
        if 'rsi' not in indicators or indicators['rsi'] is None:
            indicators['rsi'] = 50  # 默认中性值

        if 'macd' not in indicators or not indicators['macd']:
            indicators['macd'] = {'macdLine': 0, 'signalLine': 0, 'histogram': 0}
        elif 'macdLine' not in indicators['macd'] or indicators['macd']['macdLine'] is None:
            indicators['macd']['macdLine'] = 0
        elif 'signalLine' not in indicators['macd'] or indicators['macd']['signalLine'] is None:
            indicators['macd']['signalLine'] = 0

        print(f'成功计算技术指标: RSI={indicators.get("rsi")}, MACD={indicators.get("macd", {}).get("macdLine")}')
        return indicators
    except Exception as error:
        print(f'计算技术指标失败: {error}')
        import traceback
        traceback.print_exc()

        # 返回默认指标，避免空值
        return {
            'ma': {'5': None, '10': None, '20': None, '50': None},
            'rsi': 50,  # 默认中性值
            'macd': {'macdLine': 0, 'signalLine': 0, 'histogram': 0},
            'bollinger': {'upper': None, 'middle': None, 'lower': None},
            'momentum': 0,
            'advanced': {},
            'advancedAnalysis': '计算指标时出错'
        }


def calculate_volatility(klines):
    """
    计算波动率

    Args:
        klines (list): K线数据

    Returns:
        float: 波动率
    """
    try:
        # 使用最近5根K线计算波动率
        recent_klines = klines[-5:]
        highs = [k['high'] for k in recent_klines]
        lows = [k['low'] for k in recent_klines]

        # 计算真实波动范围的平均值
        true_ranges = []
        for i in range(1, len(recent_klines)):
            high = highs[i]
            low = lows[i]
            prev_close = recent_klines[i-1]['close']

            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)

            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)

        return np.mean(true_ranges) if true_ranges else 0
    except Exception as error:
        print(f'计算波动率失败: {error}')
        return 0


def detect_volume_spike(klines):
    """
    检测交易量突增

    Args:
        klines (list): K线数据

    Returns:
        bool: 是否检测到交易量突增
    """
    try:
        if len(klines) < 10:
            return False

        # 获取最近的交易量
        volumes = [k['volume'] for k in klines]

        # 计算平均交易量（不包括最新的）
        avg_volume = np.mean(volumes[:-1])

        # 检查最新的交易量是否显著高于平均值
        latest_volume = volumes[-1]

        return latest_volume > avg_volume * VOLUME_SPIKE_THRESHOLD
    except Exception as error:
        print(f'检测交易量突增失败: {error}')
        return False


def detect_price_breakout(klines):
    """
    检测价格突破

    Args:
        klines (list): K线数据

    Returns:
        str: 突破类型，如果没有突破则返回None
    """
    try:
        if len(klines) < 10:
            return None

        # 获取最近的价格数据（不包括最新的）
        prev_klines = klines[:-1]

        # 计算前期高点和低点
        prev_high = max([k['high'] for k in prev_klines])
        prev_low = min([k['low'] for k in prev_klines])

        # 获取最新价格
        latest_close = klines[-1]['close']

        # 检查是否突破
        if latest_close > prev_high:
            return "向上突破"
        elif latest_close < prev_low:
            return "向下突破"

        return None
    except Exception as error:
        print(f'检测价格突破失败: {error}')
        return None


def can_perform_analysis():
    """
    检查是否可以执行分析（避免过于频繁）

    Returns:
        bool: 是否可以执行分析
    """
    global last_analysis_time

    now = datetime.now()

    # 如果没有上次分析时间，允许执行
    if last_analysis_time is None:
        return True

    # 计算距离上次分析的时间间隔（分钟）
    time_diff = (now - last_analysis_time).total_seconds() / 60

    # 如果间隔大于最小分析间隔，允许执行
    return time_diff >= MIN_ANALYSIS_INTERVAL


def update_last_analysis_time():
    """更新最后分析时间"""
    global last_analysis_time
    last_analysis_time = datetime.now()
