#!/usr/bin/env python3
"""
QuantumForex Pro - 数据库连接测试
测试数据库连接和数据获取功能
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """测试数据库连接"""
    print("🔍 开始数据库连接测试...")
    print("=" * 50)
    
    try:
        from utils.db_client import get_connection, execute_query
        print("✅ 数据库客户端模块导入成功")
    except ImportError as e:
        print(f"❌ 数据库客户端模块导入失败: {e}")
        return False
    
    # 测试数据库连接
    try:
        connection = get_connection()
        if connection:
            print("✅ 数据库连接建立成功")
            connection.close()
        else:
            print("❌ 数据库连接失败")
            return False
    except Exception as e:
        print(f"❌ 数据库连接异常: {e}")
        return False
    
    return True

def test_eurusd_data_query():
    """测试EURUSD数据查询"""
    print("\n🔍 开始EURUSD数据查询测试...")
    print("=" * 50)
    
    try:
        from utils.db_client import execute_query
        
        # 检查数据表是否存在
        table_check_sql = "SHOW TABLES LIKE 'min_quote_eurusd'"
        table_result = execute_query(table_check_sql)
        
        if not table_result:
            print("❌ min_quote_eurusd表不存在")
            return False
        
        print("✅ min_quote_eurusd表存在")
        
        # 检查数据量
        count_sql = "SELECT COUNT(*) as total FROM min_quote_eurusd"
        count_result = execute_query(count_sql)
        
        if count_result:
            total_records = count_result[0]['total']
            print(f"✅ 数据表记录总数: {total_records}")
            
            if total_records == 0:
                print("⚠️ 数据表为空，无法进行数据测试")
                return False
        else:
            print("❌ 无法获取数据表记录数")
            return False
        
        # 获取最新数据样本
        sample_sql = """
        SELECT time_date_str, price, min as low_price, max as high_price, volume, create_time
        FROM min_quote_eurusd
        ORDER BY time_min_int DESC
        LIMIT 5
        """
        
        sample_data = execute_query(sample_sql)
        
        if sample_data:
            print(f"✅ 成功获取{len(sample_data)}条样本数据:")
            for i, row in enumerate(sample_data, 1):
                print(f"   {i}. 时间: {row['time_date_str']}, 价格: {row['price']}")
        else:
            print("❌ 无法获取样本数据")
            return False
        
        # 测试数据质量
        print("\n📊 数据质量检查:")
        
        # 检查价格范围
        price_range_sql = """
        SELECT MIN(price) as min_price, MAX(price) as max_price, AVG(price) as avg_price
        FROM min_quote_eurusd
        WHERE time_min_int >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY))
        """
        
        price_data = execute_query(price_range_sql)
        if price_data and price_data[0]['min_price']:
            price_info = price_data[0]
            print(f"   24小时价格范围: {price_info['min_price']:.5f} - {price_info['max_price']:.5f}")
            print(f"   24小时平均价格: {price_info['avg_price']:.5f}")
            
            # 检查价格是否在合理范围内 (EURUSD通常在0.9-1.3之间)
            if 0.9 <= float(price_info['min_price']) <= 1.3 and 0.9 <= float(price_info['max_price']) <= 1.3:
                print("   ✅ 价格数据在合理范围内")
            else:
                print("   ⚠️ 价格数据可能异常")
        
        # 检查数据时效性
        latest_time_sql = """
        SELECT time_date_str, UNIX_TIMESTAMP(NOW()) - time_min_int as age_seconds
        FROM min_quote_eurusd
        ORDER BY time_min_int DESC
        LIMIT 1
        """
        
        latest_data = execute_query(latest_time_sql)
        if latest_data:
            age_seconds = latest_data[0]['age_seconds']
            age_minutes = age_seconds / 60
            print(f"   最新数据时间: {latest_data[0]['time_date_str']}")
            print(f"   数据延迟: {age_minutes:.1f}分钟")
            
            if age_minutes <= 60:  # 1小时内
                print("   ✅ 数据时效性良好")
            elif age_minutes <= 1440:  # 24小时内
                print("   ⚠️ 数据有一定延迟")
            else:
                print("   ❌ 数据延迟过大")
        
        print("=" * 50)
        print("✅ EURUSD数据查询测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ EURUSD数据查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_processing():
    """测试数据处理功能"""
    print("\n🔍 开始数据处理测试...")
    print("=" * 50)
    
    try:
        from utils.db_client import execute_query
        import pandas as pd
        import numpy as np
        
        # 获取测试数据
        test_sql = """
        SELECT time_date_str, price, min as low_price, max as high_price, volume
        FROM min_quote_eurusd
        ORDER BY time_min_int DESC
        LIMIT 100
        """
        
        raw_data = execute_query(test_sql)
        
        if not raw_data:
            print("❌ 无法获取测试数据")
            return False
        
        print(f"✅ 获取{len(raw_data)}条测试数据")
        
        # 转换为DataFrame
        df_data = []
        for row in reversed(raw_data):  # 反转以获得时间顺序
            try:
                timestamp = pd.to_datetime(row['time_date_str'])
                close_price = float(row['price'])
                high_price = float(row['high_price']) if row['high_price'] else close_price
                low_price = float(row['low_price']) if row['low_price'] else close_price
                volume = int(row['volume']) if row['volume'] else 1000
                
                df_data.append({
                    'timestamp': timestamp,
                    'open': close_price,  # 简化处理
                    'high': max(high_price, close_price),
                    'low': min(low_price, close_price),
                    'close': close_price,
                    'volume': volume
                })
            except Exception as e:
                print(f"⚠️ 数据行处理失败: {e}")
                continue
        
        if not df_data:
            print("❌ 数据处理失败，无有效数据")
            return False
        
        # 创建DataFrame
        df = pd.DataFrame(df_data)
        df.set_index('timestamp', inplace=True)
        
        print(f"✅ 成功创建DataFrame，包含{len(df)}行数据")
        print(f"   时间范围: {df.index[0]} 到 {df.index[-1]}")
        print(f"   价格范围: {df['close'].min():.5f} - {df['close'].max():.5f}")
        
        # 计算基础统计
        current_price = df['close'].iloc[-1]
        price_changes = df['close'].pct_change().dropna()
        volatility = price_changes.std() if len(price_changes) > 1 else 0.001
        
        print(f"   当前价格: {current_price:.5f}")
        print(f"   波动率: {volatility:.6f}")
        print(f"   平均成交量: {df['volume'].mean():.0f}")
        
        # 检查数据完整性
        missing_data = df.isnull().sum().sum()
        if missing_data == 0:
            print("   ✅ 数据完整，无缺失值")
        else:
            print(f"   ⚠️ 发现{missing_data}个缺失值")
        
        print("=" * 50)
        print("✅ 数据处理测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mt4_client():
    """测试MT4客户端连接"""
    print("\n🔍 开始MT4客户端测试...")
    print("=" * 50)
    
    try:
        from utils.mt4_client import MT4Client
        print("✅ MT4客户端模块导入成功")
        
        # 创建MT4客户端实例
        mt4_client = MT4Client()
        print("✅ MT4客户端实例创建成功")
        
        # 尝试连接（可能失败，这是正常的）
        try:
            connected = mt4_client.connect()
            if connected:
                print("✅ MT4连接成功")
                
                # 测试获取市场信息
                market_info = mt4_client.get_market_info('EURUSD')
                if market_info and market_info.get('status') == 'success':
                    data = market_info.get('data', {})
                    print(f"   EURUSD买价: {data.get('bid', 'N/A')}")
                    print(f"   EURUSD卖价: {data.get('ask', 'N/A')}")
                else:
                    print("⚠️ 无法获取市场信息")
                
                mt4_client.disconnect()
            else:
                print("⚠️ MT4连接失败（这是正常的，如果MT4未运行）")
                print("   系统将使用数据库数据作为备用数据源")
        except Exception as e:
            print(f"⚠️ MT4连接异常: {e}")
            print("   系统将使用数据库数据作为备用数据源")
        
        print("=" * 50)
        print("✅ MT4客户端测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ MT4客户端测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 QuantumForex Pro - 数据库连接测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行所有测试
    db_ok = test_database_connection()
    data_ok = test_eurusd_data_query()
    process_ok = test_data_processing()
    mt4_ok = test_mt4_client()
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"数据库连接测试: {'✅ 通过' if db_ok else '❌ 失败'}")
    print(f"数据查询测试: {'✅ 通过' if data_ok else '❌ 失败'}")
    print(f"数据处理测试: {'✅ 通过' if process_ok else '❌ 失败'}")
    print(f"MT4客户端测试: {'✅ 通过' if mt4_ok else '❌ 失败'}")
    
    if db_ok and data_ok and process_ok and mt4_ok:
        print("🎉 所有数据库测试通过! 数据系统正常!")
        sys.exit(0)
    else:
        print("❌ 部分数据库测试失败，请检查数据库连接和配置")
        sys.exit(1)
