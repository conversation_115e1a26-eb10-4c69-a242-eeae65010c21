#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试完整的专业策略系统
包括有持仓情况下的决策测试
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_system_with_positions():
    """测试有持仓时的系统表现"""
    try:
        print("🧠 测试有持仓时的策略决策...")
        
        from strategies.master_strategy_engine import MasterStrategyEngine
        from strategies.professional_portfolio_strategy import PortfolioAction
        import pandas as pd
        import numpy as np
        from datetime import datetime
        
        # 创建主策略引擎
        master_strategy = MasterStrategyEngine()
        
        # 创建更真实的市场数据（有明显趋势）
        dates = pd.date_range(end=datetime.now(), periods=100, freq='1min')
        
        # EURUSD 上升趋势
        eurusd_trend = np.linspace(0, 0.01, 100)  # 1%上升趋势
        eurusd_prices = 1.0850 + eurusd_trend + np.random.normal(0, 0.0005, 100)
        
        # GBPUSD 下降趋势（与EURUSD相反）
        gbpusd_trend = np.linspace(0, -0.008, 100)  # 0.8%下降趋势
        gbpusd_prices = 1.2650 + gbpusd_trend + np.random.normal(0, 0.0005, 100)
        
        market_data = {
            'EURUSD': {
                'ohlcv': pd.DataFrame({
                    'open': eurusd_prices + np.random.normal(0, 0.0001, 100),
                    'high': eurusd_prices + np.abs(np.random.normal(0, 0.0003, 100)),
                    'low': eurusd_prices - np.abs(np.random.normal(0, 0.0003, 100)),
                    'close': eurusd_prices,
                    'volume': np.random.randint(1000, 5000, 100)
                }, index=dates),
                'current_price': eurusd_prices[-1],
                'volatility': 0.008  # 较高波动率
            },
            'GBPUSD': {
                'ohlcv': pd.DataFrame({
                    'open': gbpusd_prices + np.random.normal(0, 0.0001, 100),
                    'high': gbpusd_prices + np.abs(np.random.normal(0, 0.0003, 100)),
                    'low': gbpusd_prices - np.abs(np.random.normal(0, 0.0003, 100)),
                    'close': gbpusd_prices,
                    'volume': np.random.randint(1000, 5000, 100)
                }, index=dates),
                'current_price': gbpusd_prices[-1],
                'volatility': 0.009  # 较高波动率
            },
            'AUDUSD': {
                'ohlcv': pd.DataFrame({
                    'open': np.random.normal(0.6420, 0.001, 100),
                    'high': np.random.normal(0.6425, 0.001, 100),
                    'low': np.random.normal(0.6415, 0.001, 100),
                    'close': np.random.normal(0.6420, 0.001, 100),
                    'volume': np.random.randint(1000, 5000, 100)
                }, index=dates),
                'current_price': 0.6420,
                'volatility': 0.006
            }
        }
        
        print(f"📊 市场数据:")
        print(f"   EURUSD: {eurusd_prices[0]:.5f} → {eurusd_prices[-1]:.5f} (趋势: +{(eurusd_prices[-1]/eurusd_prices[0]-1)*100:.2f}%)")
        print(f"   GBPUSD: {gbpusd_prices[0]:.5f} → {gbpusd_prices[-1]:.5f} (趋势: {(gbpusd_prices[-1]/gbpusd_prices[0]-1)*100:.2f}%)")
        
        # 测试场景1：无持仓
        print("\n📋 场景1：无持仓情况")
        current_positions = {}
        
        master_decision = master_strategy.generate_master_decision(market_data, current_positions)
        print(f"   策略类型: {master_decision.strategy_used.value}")
        print(f"   市场条件: {master_decision.market_condition.value}")
        print(f"   置信度: {master_decision.confidence:.2%}")
        print(f"   决策数量: {len(master_decision.decisions)}")
        
        if master_decision.decisions:
            print("   具体决策:")
            for decision in master_decision.decisions:
                print(f"     {decision.symbol} - {decision.action.value} - {decision.size:.3f}手 (置信度: {decision.confidence:.2%})")
        
        # 测试场景2：有EURUSD多头持仓
        print("\n📋 场景2：已有EURUSD多头持仓")
        current_positions = {
            'EURUSD': {
                'orders': [{'order_id': '123', 'action': 'BUY', 'size': 0.02, 'entry_price': 1.0840}],
                'total_size': 0.02,
                'total_value': 0.02168,
                'avg_entry_price': 1.0840,
                'weight': 1.0
            }
        }
        
        master_decision = master_strategy.generate_master_decision(market_data, current_positions)
        print(f"   策略类型: {master_decision.strategy_used.value}")
        print(f"   市场条件: {master_decision.market_condition.value}")
        print(f"   置信度: {master_decision.confidence:.2%}")
        print(f"   决策数量: {len(master_decision.decisions)}")
        
        if master_decision.decisions:
            print("   具体决策:")
            for decision in master_decision.decisions:
                print(f"     {decision.symbol} - {decision.action.value} - {decision.size:.3f}手")
                print(f"     原因: {decision.reasoning}")
        
        # 测试场景3：有多个持仓（高相关性）
        print("\n📋 场景3：多个高相关性持仓")
        current_positions = {
            'EURUSD': {
                'orders': [{'order_id': '123', 'action': 'BUY', 'size': 0.02, 'entry_price': 1.0840}],
                'total_size': 0.02,
                'total_value': 0.02168,
                'avg_entry_price': 1.0840,
                'weight': 0.6
            },
            'GBPUSD': {
                'orders': [{'order_id': '124', 'action': 'BUY', 'size': 0.015, 'entry_price': 1.2640}],
                'total_size': 0.015,
                'total_value': 0.01896,
                'avg_entry_price': 1.2640,
                'weight': 0.4
            }
        }
        
        master_decision = master_strategy.generate_master_decision(market_data, current_positions)
        print(f"   策略类型: {master_decision.strategy_used.value}")
        print(f"   市场条件: {master_decision.market_condition.value}")
        print(f"   置信度: {master_decision.confidence:.2%}")
        print(f"   决策数量: {len(master_decision.decisions)}")
        
        if master_decision.decisions:
            print("   具体决策:")
            for decision in master_decision.decisions:
                action_desc = {
                    PortfolioAction.ENTER_LONG: "开多仓",
                    PortfolioAction.ENTER_SHORT: "开空仓", 
                    PortfolioAction.EXIT_POSITION: "平仓",
                    PortfolioAction.HEDGE_POSITION: "对冲",
                    PortfolioAction.REBALANCE: "重新平衡",
                    PortfolioAction.HOLD: "持有"
                }.get(decision.action, decision.action.value)
                
                print(f"     {decision.symbol} - {action_desc} - {decision.size:.3f}手")
                print(f"     原因: {decision.reasoning}")
        
        print("\n🎉 完整系统测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 完整系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_risk_assessment_integration():
    """测试风险评估集成"""
    try:
        print("\n🛡️ 测试风险评估集成...")
        
        # 导入主程序的风险评估方法
        import main
        from strategies.master_strategy_engine import MasterStrategyEngine, MarketCondition, StrategyType
        
        # 创建模拟的主决策
        class MockMasterDecision:
            def __init__(self, confidence, market_condition, strategy_used):
                self.confidence = confidence
                self.market_condition = market_condition
                self.strategy_used = strategy_used
                self.decisions = []
                self.reasoning = f"测试决策 - 置信度: {confidence:.2%}"
        
        # 创建QuantumForexPro实例
        quantum_forex = main.QuantumForexPro()
        
        # 测试不同置信度的风险评估
        test_cases = [
            (0.8, "高置信度"),
            (0.6, "中等置信度"), 
            (0.3, "低置信度")
        ]
        
        for confidence, desc in test_cases:
            mock_decision = MockMasterDecision(
                confidence=confidence,
                market_condition=MarketCondition.TRENDING,
                strategy_used=StrategyType.PORTFOLIO_STRATEGY
            )
            
            risk_assessment = quantum_forex._create_risk_assessment_from_master_decision(mock_decision)
            
            print(f"   {desc} ({confidence:.1%}):")
            print(f"     风险等级: {risk_assessment.get('overall_risk', 'UNKNOWN')}")
            print(f"     推荐行动: {risk_assessment['risk_metrics'].recommended_action.value}")
            print(f"     风险评分: {risk_assessment['risk_metrics'].risk_score:.3f}")
        
        print("✅ 风险评估集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 风险评估集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始完整系统测试")
    print("="*60)
    
    # 测试有持仓时的系统表现
    if not test_system_with_positions():
        return False
    
    # 测试风险评估集成
    if not test_risk_assessment_integration():
        return False
    
    print("\n🎉 所有完整系统测试通过！")
    print("✅ 专业策略系统完全正常运行！")
    print("✅ 风险管理集成正常！")
    print("✅ 多场景决策逻辑正确！")
    return True

if __name__ == "__main__":
    main()
