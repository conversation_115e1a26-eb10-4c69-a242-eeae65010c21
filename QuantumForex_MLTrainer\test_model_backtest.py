#!/usr/bin/env python3
"""
测试模型回测验证
验证训练的模型是否真的有效果
"""

import sys
import os
import pandas as pd
import numpy as np
import joblib
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model_evaluation.backtesting_engine import BacktestingEngine, BacktestResult
from data_collector.forex_data_collector import ForexDataCollector

def load_trained_model(model_path: str):
    """加载训练好的模型"""
    try:
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return None

        model = joblib.load(model_path)
        print(f"✅ 模型加载成功: {os.path.basename(model_path)}")
        return model

    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None

def prepare_backtest_data(days: int = 30) -> pd.DataFrame:
    """准备回测数据"""
    try:
        print(f"📊 准备{days}天的回测数据...")

        # 创建数据收集器
        collector = ForexDataCollector()

        # 获取历史数据
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        # 获取EURUSD数据作为示例
        data = collector.get_historical_data(
            symbol='EURUSD',
            timeframe='5min',
            start_time=start_time,
            end_time=end_time
        )

        if data is None or len(data) == 0:
            print("❌ 无法获取历史数据")
            return None

        print(f"✅ 获取到{len(data)}条历史数据")
        return data

    except Exception as e:
        print(f"❌ 准备回测数据失败: {e}")
        return None

def generate_model_predictions(model, data: pd.DataFrame) -> dict:
    """生成模型预测"""
    try:
        print("🤖 生成模型预测...")

        predictions = {}

        # 简化特征工程（实际应该使用完整的特征工程）
        for idx, row in data.iterrows():
            try:
                # 基础技术指标特征
                features = np.array([
                    row['close'],
                    row['high'] - row['low'],  # 波动范围
                    row['close'] - row['open'],  # 价格变化
                    row.get('volume', 1000),  # 成交量
                    idx / len(data)  # 时间特征
                ]).reshape(1, -1)

                # 模型预测
                if hasattr(model, 'predict_proba'):
                    # 分类模型
                    proba = model.predict_proba(features)[0]
                    signal = proba[1] - proba[0] if len(proba) > 1 else proba[0]
                    confidence = max(proba)
                else:
                    # 回归模型
                    prediction = model.predict(features)[0]
                    signal = prediction
                    confidence = min(abs(prediction), 1.0)

                predictions[idx] = {
                    'signal': signal,
                    'confidence': confidence,
                    'raw_prediction': prediction if 'prediction' in locals() else signal
                }

            except Exception as e:
                # 跳过有问题的数据点
                continue

        print(f"✅ 生成{len(predictions)}个预测")
        return predictions

    except Exception as e:
        print(f"❌ 生成预测失败: {e}")
        return {}

def run_model_backtest():
    """运行模型回测"""
    print("🚀 开始模型回测验证")
    print("="*60)

    try:
        # 1. 查找训练好的模型
        models_dir = Path("data/models")
        if not models_dir.exists():
            print("❌ 模型目录不存在")
            return False

        # 查找最新的价格预测模型
        model_files = list(models_dir.glob("*price_prediction*.pkl"))

        # 排除scaler文件
        model_files = [f for f in model_files if 'scaler' not in f.name]

        if not model_files:
            print("❌ 没有找到价格预测模型")
            print(f"📁 模型目录: {models_dir}")
            print(f"📄 可用文件: {list(models_dir.glob('*.pkl'))}")
            return False

        # 选择最新的模型
        latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
        print(f"📦 使用模型: {latest_model.name}")

        # 2. 加载模型
        model = load_trained_model(str(latest_model))
        if model is None:
            return False

        # 3. 准备回测数据
        backtest_data = prepare_backtest_data(days=7)  # 7天数据
        if backtest_data is None:
            return False

        # 4. 生成模型预测
        predictions = generate_model_predictions(model, backtest_data)
        if not predictions:
            print("❌ 无法生成预测")
            return False

        # 5. 运行回测
        print("\n🔄 运行回测...")
        backtest_engine = BacktestingEngine(initial_capital=10000.0)

        # 回测参数
        strategy_params = {
            'risk_per_trade': 0.02,      # 每笔交易风险2%
            'stop_loss_pips': 20,        # 止损20点
            'take_profit_pips': 40,      # 止盈40点
            'min_confidence': 0.6        # 最小置信度60%
        }

        result = backtest_engine.run_backtest(
            data=backtest_data,
            model_predictions=predictions,
            strategy_params=strategy_params
        )

        # 6. 显示回测结果
        display_backtest_results(result)

        # 7. 评估模型效果
        evaluate_model_effectiveness(result)

        return True

    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def display_backtest_results(result: BacktestResult):
    """显示回测结果"""
    print("\n📊 回测结果")
    print("="*60)

    # 基础统计
    print(f"📈 交易统计:")
    print(f"   总交易数: {result.total_trades}")
    print(f"   盈利交易: {result.winning_trades}")
    print(f"   亏损交易: {result.losing_trades}")
    print(f"   胜率: {result.win_rate:.1%}")

    # 盈亏统计
    print(f"\n💰 盈亏统计:")
    print(f"   总盈利: ${result.total_profit:.2f}")
    print(f"   总亏损: ${result.total_loss:.2f}")
    print(f"   净盈利: ${result.net_profit:.2f}")
    print(f"   盈亏比: {result.profit_factor:.2f}")

    # 风险指标
    print(f"\n🛡️ 风险指标:")
    print(f"   最大回撤: {result.max_drawdown:.1%}")
    print(f"   夏普比率: {result.sharpe_ratio:.2f}")
    print(f"   卡尔玛比率: {result.calmar_ratio:.2f}")

    # 交易质量
    print(f"\n📊 交易质量:")
    print(f"   平均盈利: ${result.average_win:.2f}")
    print(f"   平均亏损: ${result.average_loss:.2f}")
    print(f"   最大盈利: ${result.largest_win:.2f}")
    print(f"   最大亏损: ${result.largest_loss:.2f}")
    print(f"   最大连胜: {result.max_consecutive_wins}")
    print(f"   最大连亏: {result.max_consecutive_losses}")

def evaluate_model_effectiveness(result: BacktestResult):
    """评估模型效果"""
    print("\n🎯 模型效果评估")
    print("="*60)

    # 评估标准
    effectiveness_score = 0
    max_score = 100

    # 1. 盈利能力 (30分)
    if result.net_profit > 0:
        profit_score = min(30, (result.net_profit / 1000) * 30)  # 每1000美元得30分
        effectiveness_score += profit_score
        print(f"✅ 盈利能力: {profit_score:.1f}/30 (净盈利: ${result.net_profit:.2f})")
    else:
        print(f"❌ 盈利能力: 0/30 (净亏损: ${abs(result.net_profit):.2f})")

    # 2. 胜率 (20分)
    win_rate_score = result.win_rate * 20
    effectiveness_score += win_rate_score
    print(f"{'✅' if result.win_rate > 0.5 else '⚠️'} 胜率: {win_rate_score:.1f}/20 ({result.win_rate:.1%})")

    # 3. 盈亏比 (20分)
    if result.profit_factor > 1:
        profit_factor_score = min(20, (result.profit_factor - 1) * 10)
        effectiveness_score += profit_factor_score
        print(f"✅ 盈亏比: {profit_factor_score:.1f}/20 ({result.profit_factor:.2f})")
    else:
        print(f"❌ 盈亏比: 0/20 ({result.profit_factor:.2f})")

    # 4. 风险控制 (20分)
    if result.max_drawdown < 0.1:  # 回撤小于10%
        risk_score = 20 - (result.max_drawdown * 100)
        effectiveness_score += risk_score
        print(f"✅ 风险控制: {risk_score:.1f}/20 (最大回撤: {result.max_drawdown:.1%})")
    else:
        print(f"❌ 风险控制: 0/20 (最大回撤: {result.max_drawdown:.1%})")

    # 5. 交易频率 (10分)
    if result.total_trades > 0:
        frequency_score = min(10, result.total_trades)
        effectiveness_score += frequency_score
        print(f"✅ 交易频率: {frequency_score:.1f}/10 ({result.total_trades}笔交易)")
    else:
        print(f"❌ 交易频率: 0/10 (无交易)")

    # 总评分
    effectiveness_percentage = effectiveness_score / max_score

    print(f"\n🏆 模型效果总评:")
    print(f"   总得分: {effectiveness_score:.1f}/{max_score}")
    print(f"   效果评级: {effectiveness_percentage:.1%}")

    # 评级
    if effectiveness_percentage >= 0.8:
        grade = "🌟 优秀"
        recommendation = "模型效果优秀，建议投入实盘使用"
    elif effectiveness_percentage >= 0.6:
        grade = "✅ 良好"
        recommendation = "模型效果良好，可以考虑小资金测试"
    elif effectiveness_percentage >= 0.4:
        grade = "⚠️ 一般"
        recommendation = "模型效果一般，需要进一步优化"
    else:
        grade = "❌ 较差"
        recommendation = "模型效果较差，建议重新训练"

    print(f"   效果等级: {grade}")
    print(f"   建议: {recommendation}")

    return effectiveness_percentage

if __name__ == "__main__":
    success = run_model_backtest()

    if success:
        print("\n🎉 模型回测验证完成！")
        print("💡 现在您可以看到模型的真实交易效果了！")
    else:
        print("\n❌ 模型回测验证失败！")
        print("💡 请检查模型文件和数据连接")
