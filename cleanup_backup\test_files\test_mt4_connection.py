"""
MT4连接测试脚本
用于测试MT4服务器连接和基本功能
"""
import os
import sys
import time
from app.utils.mt4_client import mt4_client

def test_mt4_connection():
    """测试MT4连接"""
    try:
        print('开始测试MT4连接...')
        
        # 连接到MT4服务器
        print('连接到MT4服务器...')
        connected = mt4_client.connect()
        print(f'连接结果: {connected}')
        
        if not connected:
            print('连接MT4服务器失败，无法继续测试')
            return
        
        # 获取账户信息
        print('\n获取账户信息...')
        account_info = mt4_client.get_account_info()
        print(f'账户信息: {account_info}')
        
        # 获取市场信息
        print('\n获取EURUSD市场信息...')
        market_info = mt4_client.get_market_info('EURUSD')
        print(f'市场信息: {market_info}')
        
        # 获取活跃订单
        print('\n获取活跃订单...')
        active_orders = mt4_client.get_active_orders()
        print(f'活跃订单: {active_orders}')
        
        # 测试市价买入
        print('\n测试市价买入...')
        try:
            buy_response = mt4_client.buy('EURUSD', 0.01, 0, 0, '测试市价买入')
            print(f'市价买入响应: {buy_response}')
            
            if buy_response and buy_response.get('status') == 'success':
                print('✅ 市价买入成功!')
                
                # 如果成功下单，获取订单ID
                order_id = buy_response.get('order_id')
                if order_id:
                    print(f'订单ID: {order_id}')
                    
                    # 等待一段时间
                    print('等待3秒...')
                    time.sleep(3)
                    
                    # 关闭订单
                    print(f'关闭订单 {order_id}...')
                    close_response = mt4_client.close_order(order_id)
                    print(f'关闭订单响应: {close_response}')
            else:
                print(f'❌ 市价买入失败: {buy_response.get("message") if buy_response else "未知错误"}')
        except Exception as error:
            print(f'❌ 市价买入出错: {error}')
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_mt4_connection()
