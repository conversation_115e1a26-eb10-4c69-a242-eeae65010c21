"""
多轮分析模块
用于实现多轮分析模式，解决提示词长度限制问题
"""
import json
import time
import re
from datetime import datetime

from app.utils import llm_client
from app.utils import forex_llm_prompt
from app.utils.error_logger import log_error, ErrorType
from app.utils.error_collector import log_full_analysis_error


# 定义分析回退异常类
class AnalysisFallbackException(Exception):
    """
    当分析过程中API调用失败需要回退时抛出的异常

    Args:
        fallback_info (dict): 包含回退信息的字典，包括错误信息、重试时间等
    """
    def __init__(self, fallback_info):
        self.fallback_info = fallback_info
        super().__init__(fallback_info.get('message', '分析失败，需要回退'))


class AnalysisRound:
    """分析轮次类"""
    INITIAL = "INITIAL"  # 初始分析
    DETAIL = "DETAIL"    # 详细分析
    FINAL = "FINAL"      # 最终分析


class InformationCategory:
    """信息类别枚举"""
    CORE = "CORE"                # 核心信息（必须包含）
    TECHNICAL = "TECHNICAL"      # 技术分析信息
    FUNDAMENTAL = "FUNDAMENTAL"  # 基本面信息
    HISTORICAL = "HISTORICAL"    # 历史信息
    ERROR = "ERROR"              # 错误信息
    POSITION = "POSITION"        # 持仓信息


def generate_initial_prompt(data):
    """
    生成初始提示词

    Args:
        data (dict): 分析数据

    Returns:
        str: 初始提示词
    """
    symbol = data.get('symbol', 'EURUSD')

    # 获取15分钟K线数据，确保是最新的数据
    timeframe15m_all = data.get('timeframe15m', [])
    # 打印完整的15分钟K线数据时间范围，用于调试
    if timeframe15m_all and len(timeframe15m_all) > 0:
        print(f'15分钟K线数据时间范围：从 {timeframe15m_all[0]["time"]} 到 {timeframe15m_all[-1]["time"]}')
        print(f'15分钟K线数据总数：{len(timeframe15m_all)}')

    # 确保数据是按时间升序排列的，最新的数据在最后
    # 增加K线数据数量，从10根增加到20根，提供更多的历史数据
    timeframe15m = timeframe15m_all[-20:] if len(timeframe15m_all) >= 20 else timeframe15m_all

    # 获取1小时K线数据，确保是最新的数据
    timeframe1h_all = data.get('timeframe1h', [])
    # 打印完整的1小时K线数据时间范围，用于调试
    if timeframe1h_all and len(timeframe1h_all) > 0:
        print(f'1小时K线数据时间范围：从 {timeframe1h_all[0]["time"]} 到 {timeframe1h_all[-1]["time"]}')
        print(f'1小时K线数据总数：{len(timeframe1h_all)}')

    # 确保数据是按时间升序排列的，最新的数据在最后
    # 增加K线数据数量，从5根增加到10根，提供更多的历史数据
    timeframe1h = timeframe1h_all[-10:] if len(timeframe1h_all) >= 10 else timeframe1h_all

    # 打印LLM将使用的K线数据时间范围，用于调试
    if timeframe15m and len(timeframe15m) > 0:
        print(f'LLM将使用的15分钟K线数据时间范围：从 {timeframe15m[0]["time"]} 到 {timeframe15m[-1]["time"]}')
    if timeframe1h and len(timeframe1h) > 0:
        print(f'LLM将使用的1小时K线数据时间范围：从 {timeframe1h[0]["time"]} 到 {timeframe1h[-1]["time"]}')

    indicators = data.get('indicators', {})
    positions = data.get('positions', [])
    pending_orders = data.get('pendingOrders', [])

    # 导入模板管理器
    from app.utils import prompt_template_manager

    # 检查数据质量
    timeframe15m_formatted = forex_llm_prompt.format_kline_data(timeframe15m)
    timeframe1h_formatted = forex_llm_prompt.format_kline_data(timeframe1h)

    # 如果K线数据为空，提供详细的错误信息
    if not timeframe15m or len(timeframe15m) == 0:
        print(f'警告: 15分钟K线数据为空，无法进行有效分析')
        timeframe15m_formatted = "15分钟K线数据为空，无法获取价格数据。请检查数据库连接和数据更新状态。"
    elif timeframe15m_formatted == "无数据":
        print(f'警告: 15分钟K线数据格式化失败')
        timeframe15m_formatted = f"15分钟K线数据格式化失败，原始数据长度: {len(timeframe15m)}"

    if not timeframe1h or len(timeframe1h) == 0:
        print(f'警告: 1小时K线数据为空，将影响趋势分析')
        timeframe1h_formatted = "1小时K线数据为空，无法进行趋势确认。建议主要依赖15分钟数据进行分析。"
    elif timeframe1h_formatted == "无数据":
        print(f'警告: 1小时K线数据格式化失败')
        timeframe1h_formatted = f"1小时K线数据格式化失败，原始数据长度: {len(timeframe1h)}"

    # 准备模板数据
    template_data = {
        'symbol': symbol,
        'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'timeframe15m_data': timeframe15m_formatted,
        'timeframe1h_data': timeframe1h_formatted,
        'ma20': str(indicators.get('ma', {}).get(20, 'N/A')),
        'rsi': str(indicators.get('rsi', 'N/A')),
        'macd': str(indicators.get('macd', {}).get('macdLine', [-1])[-1] if indicators.get('macd') else 'N/A'),
        'positions_data': forex_llm_prompt.format_positions_data(positions),
        'pending_orders_data': forex_llm_prompt.format_pending_orders_data(pending_orders)
    }

    # 打印模板数据的长度，帮助诊断问题
    for key, value in template_data.items():
        if isinstance(value, str):
            print(f'初始分析模板数据 - {key}: {len(value)} 字符')
        elif isinstance(value, list) and value:
            print(f'初始分析模板数据 - {key}: 列表，包含 {len(value)} 个元素')
        else:
            print(f'初始分析模板数据 - {key}: 非字符串类型')

    try:
        # 使用模板管理器渲染模板
        prompt = prompt_template_manager.render_template('initial_analysis_template', template_data)
        print(f'使用模板渲染初始分析提示词，长度: {len(prompt)} 字符')

        # 如果提示词长度异常短，打印警告并检查数据问题
        if len(prompt) < 1000:
            print(f'警告: 初始分析提示词长度异常短 ({len(prompt)} 字符)，可能缺少重要信息')
            print(f'模板数据: {template_data.keys()}')

            # 检查是否是数据问题导致的
            if not timeframe15m or len(timeframe15m) == 0:
                print(f'错误: 15分钟K线数据为空，这是导致提示词过短的主要原因')
                print(f'建议: 检查数据库连接和数据更新状态')
                # 可以选择抛出异常或返回特殊的错误提示词
                return f"""
# 数据获取失败 - 无法进行分析

## 错误信息
- 15分钟K线数据为空
- 无法获取有效的市场数据进行分析
- 建议检查数据库连接和数据更新状态

## 建议操作
1. 检查数据库连接状态
2. 确认数据更新服务是否正常运行
3. 检查网络连接
4. 稍后重试分析

请在数据问题解决后重新进行分析。
"""
    except Exception as e:
        print(f'使用模板渲染初始分析提示词失败: {e}，回退到硬编码提示词')
        # 如果模板渲染失败，回退到硬编码提示词
        prompt = f"""
# 外汇交易多轮分析 - 第一轮（初始分析）

## 基本信息
- 货币对: {symbol}
- 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 分析目的: 提供交易建议和市场分析

## 核心市场数据

### 最新价格信息
```
【主要交易周期】最近20根15分钟K线:
{forex_llm_prompt.format_kline_data(timeframe15m)}

【参考周期】最近10根1小时K线:
{forex_llm_prompt.format_kline_data(timeframe1h)}
```

### 重要说明
- 请主要基于15分钟周期进行分析和交易决策
- 1小时周期仅作为参考，用于确认大趋势
- 交易信号应主要来自15分钟图表的技术指标和价格形态

### 核心技术指标
- MA20: {indicators.get('ma', {}).get(20, 'N/A')}
- RSI(14): {indicators.get('rsi', 'N/A')}
- MACD: {indicators.get('macd', {}).get('macdLine', [-1])[-1] if indicators.get('macd') else 'N/A'}

## 当前持仓和挂单
{forex_llm_prompt.format_positions_data(positions)}

{forex_llm_prompt.format_pending_orders_data(pending_orders)}

## 初步分析要求

请根据以上核心信息进行初步分析，并指出你需要哪些额外信息来完成分析。

可选的额外信息包括：
1. 详细技术指标（更多指标和更长时间周期的数据）
2. 基本面数据（新闻和经济日历事件）
3. 历史分析记录（之前的分析结果和交易决策）
4. 错误记录和系统状态（最近的错误和失败操作）
5. 特定时间段的价格数据（如特定日期范围的数据）

请提供初步市场分析，并明确列出你需要的额外信息类别（可多选）。
"""

    return prompt


def generate_detail_prompt(data, initial_analysis, requested_info):
    """
    生成详细提示词

    Args:
        data (dict): 分析数据
        initial_analysis (str): 初始分析结果
        requested_info (list): 请求的信息类别

    Returns:
        str: 详细提示词
    """
    symbol = data.get('symbol', 'EURUSD')

    # 获取15分钟K线数据，确保是最新的数据
    timeframe15m_all = data.get('timeframe15m', [])
    # 打印完整的15分钟K线数据时间范围，用于调试
    if timeframe15m_all and len(timeframe15m_all) > 0:
        print(f'详细分析 - 15分钟K线数据时间范围：从 {timeframe15m_all[0]["time"]} 到 {timeframe15m_all[-1]["time"]}')
        print(f'详细分析 - 15分钟K线数据总数：{len(timeframe15m_all)}')

    # 确保数据是按时间升序排列的
    timeframe15m = timeframe15m_all

    # 获取1小时K线数据，确保是最新的数据
    timeframe1h_all = data.get('timeframe1h', [])
    # 打印完整的1小时K线数据时间范围，用于调试
    if timeframe1h_all and len(timeframe1h_all) > 0:
        print(f'详细分析 - 1小时K线数据时间范围：从 {timeframe1h_all[0]["time"]} 到 {timeframe1h_all[-1]["time"]}')
        print(f'详细分析 - 1小时K线数据总数：{len(timeframe1h_all)}')

    # 确保数据是按时间升序排列的
    timeframe1h = timeframe1h_all

    indicators = data.get('indicators', {})
    news = data.get('news', [])
    calendar = data.get('calendar', [])
    positions = data.get('positions', [])
    pending_orders = data.get('pendingOrders', [])
    history_analysis = data.get('historyAnalysis', [])

    # 提取初始分析中的关键见解
    initial_insights = extract_key_insights(initial_analysis)

    # 导入模板管理器
    from app.utils import prompt_template_manager

    # 准备模板数据
    template_data = {
        'symbol': symbol,
        'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'initial_insights': initial_insights,
        'technical_indicators': '',
        'fundamental_data': '',
        'historical_data': '',
        'error_records': '',
        'position_data': ''
    }

    # 根据请求的信息类别添加相应内容
    for info_type in requested_info:
        if info_type == InformationCategory.TECHNICAL:
            technical_indicators = f"""
### 详细技术指标
#### 移动平均线
- MA5: {indicators.get('ma', {}).get(5, 'N/A')}
- MA10: {indicators.get('ma', {}).get(10, 'N/A')}
- MA20: {indicators.get('ma', {}).get(20, 'N/A')}
- MA50: {indicators.get('ma', {}).get(50, 'N/A')}

#### RSI指标
- RSI(14): {indicators.get('rsi', {}).get('value', 'N/A') if isinstance(indicators.get('rsi'), dict) else indicators.get('rsi', 'N/A')}
- RSI趋势: {indicators.get('rsi', {}).get('trend', 'N/A') if isinstance(indicators.get('rsi'), dict) else 'N/A'}

#### MACD指标
- MACD线: {indicators.get('macd', {}).get('value', 'N/A') if isinstance(indicators.get('macd'), dict) else indicators.get('macd', 'N/A')}
- 信号线: {indicators.get('macd', {}).get('signal', 'N/A') if isinstance(indicators.get('macd'), dict) else 'N/A'}
- 柱状图: {indicators.get('macd', {}).get('histogram', 'N/A') if isinstance(indicators.get('macd'), dict) else 'N/A'}

#### 布林带
- 上轨: {indicators.get('bollinger', {}).get('upper', 'N/A') if isinstance(indicators.get('bollinger'), dict) else 'N/A'}
- 中轨: {indicators.get('bollinger', {}).get('middle', 'N/A') if isinstance(indicators.get('bollinger'), dict) else 'N/A'}
- 下轨: {indicators.get('bollinger', {}).get('lower', 'N/A') if isinstance(indicators.get('bollinger'), dict) else 'N/A'}

#### 动量指标
- 动量(14): {indicators.get('momentum', 'N/A')}

#### 【主要交易周期】15分钟图表数据（最近30根）
```
{forex_llm_prompt.format_kline_data(timeframe15m[-30:] if len(timeframe15m) > 30 else timeframe15m)}
```

#### 【参考周期】1小时图表数据
```
{forex_llm_prompt.format_kline_data(timeframe1h[-30:] if len(timeframe1h) > 30 else timeframe1h) if timeframe1h and len(timeframe1h) > 0 else "1小时K线数据不足，请主要依赖15分钟周期数据进行分析"}
```

### 重要分析指导
- **请主要基于15分钟周期进行分析和交易决策**
- **15分钟周期的信号应该优先于1小时周期的信号**
- **1小时周期仅作为参考，用于确认大趋势和市场环境**
- **交易信号应主要来自15分钟图表的技术指标和价格形态**
- **入场点、止损点和目标点应主要基于15分钟图表设置**
{f"注意：1小时K线数据只有{len(timeframe1h)}根，可能不足以进行全面分析，请主要依赖15分钟周期数据" if timeframe1h and len(timeframe1h) < 20 else ""}
"""
            template_data['technical_indicators'] = technical_indicators

        elif info_type == InformationCategory.FUNDAMENTAL:
            fundamental_data = f"""
### 基本面数据

#### 最新相关新闻
{forex_llm_prompt.format_news_data(news)}

#### 经济日历事件
{forex_llm_prompt.format_calendar_data(calendar)}
"""
            template_data['fundamental_data'] = fundamental_data

        elif info_type == InformationCategory.HISTORICAL:
            historical_data = f"""
### 历史分析记录
{forex_llm_prompt.format_history_analysis(history_analysis)}
"""
            template_data['historical_data'] = historical_data

        elif info_type == InformationCategory.ERROR:
            error_records = f"""
### 系统状态与错误记录

#### 最近错误记录
{forex_llm_prompt.format_error_records(5)}

#### 最近失败操作
{forex_llm_prompt.format_operation_records(5, success=False)}
"""
            template_data['error_records'] = error_records

        elif info_type == InformationCategory.POSITION:
            position_data = f"""
### 详细持仓和挂单信息

#### 当前持仓
{forex_llm_prompt.format_positions_data(positions)}

#### 当前挂单
{forex_llm_prompt.format_pending_orders_data(pending_orders)}
"""
            template_data['position_data'] = position_data

    # 打印模板数据的长度，帮助诊断问题
    for key, value in template_data.items():
        if isinstance(value, str):
            print(f'详细分析模板数据 - {key}: {len(value)} 字符')
        else:
            print(f'详细分析模板数据 - {key}: 非字符串类型')

    try:
        # 使用模板管理器渲染模板
        prompt = prompt_template_manager.render_template('detail_analysis_template', template_data)
        print(f'使用模板渲染详细分析提示词，长度: {len(prompt)} 字符')

        # 如果提示词长度异常短，打印警告
        if len(prompt) < 1000:
            print(f'警告: 详细分析提示词长度异常短 ({len(prompt)} 字符)，可能缺少重要信息')
            print(f'模板数据: {template_data.keys()}')
    except Exception as e:
        print(f'使用模板渲染详细分析提示词失败: {e}，回退到硬编码提示词')
        # 如果模板渲染失败，回退到硬编码提示词
        prompt = f"""
# 外汇交易多轮分析 - 第二轮（详细分析）

## 基本信息
- 货币对: {symbol}
- 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 初始分析摘要
{initial_insights}

## 请求的额外信息
{template_data['technical_indicators']}
{template_data['fundamental_data']}
{template_data['historical_data']}
{template_data['error_records']}
{template_data['position_data']}

## 详细分析要求

请基于初始分析和这些额外信息，进行更深入的市场分析。重点关注：

1. 技术面和基本面的综合分析
2. 支撑位和阻力位的精确识别
3. 短期和中期趋势的判断
4. 潜在的交易机会和风险
5. 对现有持仓和挂单的评估

请提供详细分析，但不需要给出最终交易决策，这将在下一轮分析中完成。
"""

    return prompt


def generate_final_prompt(data, initial_analysis, detail_analysis):
    """
    生成最终提示词

    Args:
        data (dict): 分析数据
        initial_analysis (str): 初始分析结果
        detail_analysis (str): 详细分析结果

    Returns:
        str: 最终提示词
    """
    symbol = data.get('symbol', 'EURUSD')
    positions = data.get('positions', [])
    pending_orders = data.get('pendingOrders', [])

    # 提取前两轮分析的关键见解
    initial_insights = extract_key_insights(initial_analysis)
    detail_insights = extract_key_insights(detail_analysis)

    # 获取当前价格
    current_price = None

    # 首先尝试获取MT4实时价格，这是最准确的价格来源
    mt4_price = data.get('currentPrice')
    if mt4_price:
        current_price = mt4_price
        print(f'最终决策 - 使用MT4实时价格：{current_price}')
    else:
        # 如果MT4实时价格不可用，尝试从forex_trading_service获取最新价格
        try:
            from app.services.forex_trading_service import get_eurusd_min_data
            latest_data = get_eurusd_min_data(limit=1)
            if latest_data and len(latest_data) > 0:
                current_price = float(latest_data[-1]['close'])
                print(f'最终决策 - 使用数据库最新价格：{current_price}，来自时间：{latest_data[-1]["time"]}')
            else:
                print('最终决策 - 无法从数据库获取最新价格')
        except Exception as e:
            print(f'最终决策 - 获取数据库最新价格失败: {e}')

    # 如果上述方法都失败，才使用K线数据中的最后一个价格
    if current_price is None:
        timeframe15m = data.get('timeframe15m', [])
        if timeframe15m and len(timeframe15m) > 0:
            print(f'最终决策 - 15分钟K线数据时间范围：从 {timeframe15m[0]["time"]} 到 {timeframe15m[-1]["time"]}')
            print(f'最终决策 - 15分钟K线数据总数：{len(timeframe15m)}')
            current_price = float(timeframe15m[-1]['close'])
            print(f'最终决策 - 使用K线数据最后价格：{current_price}，来自时间：{timeframe15m[-1]["time"]}')

    # 如果所有方法都失败，打印警告
    if current_price is None:
        print('警告: 无法获取当前价格，这可能导致交易决策出错')

    # 导入模板管理器
    from app.utils import prompt_template_manager

    # 导入交易结果记录器
    try:
        from app.utils import trade_result_recorder
        has_trade_recorder = True
    except ImportError:
        print("警告: 交易结果记录器模块不可用，将不会包含历史交易结果")
        has_trade_recorder = False

    # 获取新闻、日历和历史分析数据
    news = data.get('news', [])
    calendar = data.get('calendar', [])
    history_analysis = data.get('historyAnalysis', [])

    # 获取历史交易结果
    trade_results = ""
    if has_trade_recorder:
        try:
            trade_results = trade_result_recorder.format_trade_results_for_prompt(5)
            print(f"成功获取历史交易结果，长度: {len(trade_results)} 字符")
        except Exception as e:
            print(f"获取历史交易结果失败: {e}")
            trade_results = "获取历史交易结果失败"

    # 准备模板数据
    # 检查是否有活跃订单和挂单
    has_positions = len(positions) > 0
    has_pending_orders = len(pending_orders) > 0

    # 为提示词添加明确的订单状态信息
    positions_status = "当前有 {} 个活跃持仓".format(len(positions)) if has_positions else "当前没有活跃持仓"
    pending_orders_status = "当前有 {} 个挂单".format(len(pending_orders)) if has_pending_orders else "当前没有挂单"
    orders_status = f"{positions_status}，{pending_orders_status}。"

    template_data = {
        'symbol': symbol,
        'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'current_price': current_price,
        'initial_insights': initial_insights,
        'detail_insights': detail_insights,
        'positions_data': forex_llm_prompt.format_positions_data(positions),
        'pending_orders_data': forex_llm_prompt.format_pending_orders_data(pending_orders),
        'orders_status': orders_status,  # 添加订单状态信息
        'news_data': forex_llm_prompt.format_news_data(news),
        'calendar_data': forex_llm_prompt.format_calendar_data(calendar),
        'history_analysis': forex_llm_prompt.format_history_analysis(history_analysis),
        'trade_results': trade_results
    }

    # 打印模板数据的长度，帮助诊断问题
    for key, value in template_data.items():
        if isinstance(value, str):
            print(f'最终分析模板数据 - {key}: {len(value)} 字符')
        elif isinstance(value, list) and value:
            print(f'最终分析模板数据 - {key}: 列表，包含 {len(value)} 个元素')
        else:
            print(f'最终分析模板数据 - {key}: 非字符串类型')

    try:
        # 使用模板管理器渲染模板
        prompt = prompt_template_manager.render_template('final_analysis_template', template_data)
        print(f'使用模板渲染最终分析提示词，长度: {len(prompt)} 字符')

        # 如果提示词长度异常短，打印警告
        if len(prompt) < 3000:
            print(f'警告: 最终分析提示词长度异常短 ({len(prompt)} 字符)，可能缺少重要信息')
            print(f'模板数据: {template_data.keys()}')
    except Exception as e:
        print(f'使用模板渲染最终分析提示词失败: {e}，回退到硬编码提示词')
        # 如果模板渲染失败，回退到硬编码提示词
        prompt = f"""
# 外汇交易多轮分析 - 第三轮（最终决策）

## 基本信息
- 货币对: {symbol}
- 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 当前价格: {current_price}

## 分析摘要

### 初始分析要点
{initial_insights}

### 详细分析要点
{detail_insights}

## 基本面数据

### 最新相关新闻
**注意：请特别关注新闻的时间戳，只参考最近24小时内的新闻进行短线交易决策。过期新闻可能已经被市场消化，不应作为短线交易的主要依据。**
{forex_llm_prompt.format_news_data(news)}

### 经济日历事件
**注意：请特别关注事件的时间戳，只参考最近24小时内已发布的经济数据和未来12小时内即将发布的重要数据。过期事件可能已经被市场消化，不应作为短线交易的主要依据。**
{forex_llm_prompt.format_calendar_data(calendar)}

## 当前持仓和挂单
{orders_status}

### 当前持仓
{forex_llm_prompt.format_positions_data(positions)}

### 当前挂单
{forex_llm_prompt.format_pending_orders_data(pending_orders)}

## 历史分析记录
{forex_llm_prompt.format_history_analysis(history_analysis)}

## 最终决策要求

请基于前两轮分析，给出最终交易决策。你的决策应该包含三部分：

### 第一部分：新交易指令（如果需要）
如果市场条件适合开新仓位，请提供以下信息：
1. 交易方向（买入/卖出）
2. 订单类型（市价单/限价单/止损单）
3. 入场价格、止损价格和止盈价格
4. 仓位大小（0.01-0.2之间）
5. 详细的交易理由

### 第二部分：订单管理指令（如果有现有订单）
{orders_status}

如果有现有订单需要管理，请为每个订单提供明确的管理指令：
1. 是否修改订单参数（止损/止盈/入场价）
2. 是否删除某些挂单
3. 是否平仓某些持仓
4. 每个操作的具体理由

**重要提示：**
- 只能管理当前实际存在的订单
- 如果当前没有活跃持仓或挂单，请将orderManagement设为空数组[]
- 不要尝试修改不存在的订单，这会导致操作失败
- 订单ID必须是系统中实际存在的订单ID，不要使用描述性文本作为订单ID

### 第三部分：下次预分析关注点
请提供3-5个具体的市场变化指标，预分析系统应该特别关注这些指标的变化：
1. 具体的价格水平（如支撑位、阻力位、突破点等）
2. 技术指标的关键值或交叉点（如RSI超买超卖区域、MACD交叉等）
3. 市场形态或趋势的变化点（如趋势线突破、形态完成等）
4. 持仓相关的风险点（如接近止损/止盈点等）

对于每个关注点，请说明：
- 为什么这个指标重要
- 这个指标的变化可能意味着什么
- 如果这个指标发生显著变化，是否应该触发新的完整分析

请使用以下JSON格式提供交易指令（确保JSON格式正确）：

```json
{{
  "action": "BUY或SELL或NONE",
  "orderType": "MARKET或LIMIT或STOP",
  "entryPrice": 数值或null,
  "stopLoss": 数值,
  "takeProfit": 数值,
  "lotSize": 数值,
  "riskLevel": "LOW或MEDIUM或HIGH",
  "reasoning": "详细的交易理由",
  "orderManagement": [
    {{
      "action": "MODIFY或DELETE或CLOSE",
      "orderId": "具体订单ID（如350362172）",
      "newStopLoss": 数值或null,
      "newTakeProfit": 数值或null,
      "newEntryPrice": 数值或null,
      "reason": "详细的操作原因"
    }}
  ],
  "preAnalysisFocusPoints": [
    {{
      "indicator": "具体的价格水平、技术指标或市场条件",
      "currentValue": "当前值",
      "significance": "为什么这个指标重要",
      "implication": "这个指标变化可能意味着什么",
      "triggerAnalysis": true或false
    }},
    {{
      "indicator": "另一个关注点",
      "currentValue": "当前值",
      "significance": "为什么这个指标重要",
      "implication": "这个指标变化可能意味着什么",
      "triggerAnalysis": true或false
    }}
  ]
}}
```

注意：请确保JSON格式完全正确，不要使用中文引号，不要在JSON中添加注释，不要使用不规范的格式。系统将直接解析这个JSON对象来执行交易操作。

### 重要说明：

#### 入场价格注意事项：
- **当前价格是 {current_price}，请根据你的市场分析设置合理的入场价格**
- **如果使用市价单(MARKET)，entryPrice应设为null，系统将使用当前市场价格**
- **如果使用限价单(LIMIT)或止损单(STOP)，请考虑市场趋势和关键价格水平**
- **请注意，入场价格离当前价格过远可能导致订单长时间不被触发**
- **作为专业交易者，请根据你的分析设置最合适的入场价格**

#### 交易空间和市场特性：
- EURUSD是一个波动性相对较低的货币对，日均波动范围通常在50-100点之间
- 当前市场环境下，支撑位和阻力位的有效性非常重要，请特别关注这些关键价格水平
- 交易时间段会影响波动性，欧美交易时段通常波动更大，亚洲时段相对平静
- 请考虑当前的市场趋势、波动性和重要的技术水平来设置合理的止损止盈
- **我们主要进行短线交易，持仓时间通常在几小时到几天之间，请据此设置合理的止损止盈**
- **系统需要在一天内有多次交易机会，请积极寻找短期交易信号**

#### 时间周期优先级：
- **请主要基于15分钟周期进行分析和交易决策**
- **15分钟周期的信号应该优先于1小时周期的信号**
- **1小时周期仅作为参考，用于确认大趋势和市场环境**
- **交易信号应主要来自15分钟图表的技术指标和价格形态**
- **入场点、止损点和目标点应主要基于15分钟图表设置**

#### 风险管理原则：
- **必须设置止损和止盈，这是硬性要求，系统会拒绝执行没有止损止盈的交易指令**
- **不要依赖系统的默认止损止盈值，它们只是在极少数情况下的应急措施**
- **你必须为每个交易设置明确的止损和止盈价格，这是最重要的要求**
- **止损应设置在合理的技术位置，如支撑位下方或阻力位上方**
- **止损不应太近（避免被市场噪音触发）也不应太远（避免过大风险）**
- **止盈应设置在合理的目标位置，如重要阻力位或支撑位**
- **风险回报比必须至少为1:1，即止损距离不应大于止盈距离，这是硬性要求**
- **理想的风险回报比应在1:1到1:3之间，这样可以在胜率不高的情况下仍然盈利**
- **止损点数通常应在10-50点之间，止盈点数应在10-150点之间，符合短线交易特性**
- **仓位大小(lotSize)必须在0.01到0.2之间，请根据风险评估合理设置**
- **止损止盈必须基于技术分析设置，而不是随机数值**
- **在分析逻辑中必须解释止损止盈设置的依据**

#### 交易指令格式：
- 如果建议观望（不开新仓位），将action设为"NONE"
- 如果是限价单或止损单，必须提供entryPrice
- 如果是市价单，entryPrice可以为null
- 订单管理时，orderId必须使用系统中实际存在的订单ID，不要使用描述性文本（如"BUY订单"）作为订单ID
- 如果当前没有活跃持仓或挂单，必须将orderManagement设为空数组[]
- 不要尝试管理不存在的订单，这会导致操作失败
- 修改订单时，只需提供要修改的参数，不需要修改的参数可以设为null
- 删除订单时，只需提供orderId，其他参数可以设为null
- 在生成订单管理指令前，请仔细检查当前是否有活跃持仓或挂单
"""

    return prompt


def extract_key_insights(analysis_text):
    """
    从分析文本中提取关键见解

    注意：不再进行摘要处理，直接返回完整的分析文本，确保LLM能够获取到所有信息

    Args:
        analysis_text (str): 分析文本

    Returns:
        str: 完整的分析文本
    """
    # 直接返回完整的分析文本，不进行任何摘要处理
    # 这确保了LLM能够获取到所有信息，包括最新的数据片段
    # 虽然这可能会增加token消耗，但对于交易决策来说，完整信息更为重要
    return analysis_text


def parse_requested_info(analysis_text):
    """
    从初始分析中解析请求的信息类别

    Args:
        analysis_text (str): 初始分析文本

    Returns:
        list: 请求的信息类别列表
    """
    requested_info = []
    print(f"开始解析LLM请求的额外信息，初始分析文本长度: {len(analysis_text)} 字符")

    # 检查是否请求了详细技术指标
    if re.search(r'(?:需要|请提供).*?(?:详细技术指标|更多指标|技术分析)', analysis_text, re.IGNORECASE):
        requested_info.append(InformationCategory.TECHNICAL)
        print("LLM请求了详细技术指标")

    # 检查是否请求了基本面数据
    if re.search(r'(?:需要|请提供).*?(?:基本面|新闻|经济日历|财经事件)', analysis_text, re.IGNORECASE):
        requested_info.append(InformationCategory.FUNDAMENTAL)
        print("LLM请求了基本面数据")

    # 检查是否请求了历史分析记录
    if re.search(r'(?:需要|请提供).*?(?:历史分析|历史记录|过去的分析|之前的决策)', analysis_text, re.IGNORECASE):
        requested_info.append(InformationCategory.HISTORICAL)
        print("LLM请求了历史分析记录")

    # 检查是否请求了错误记录
    if re.search(r'(?:需要|请提供).*?(?:错误记录|系统状态|失败操作)', analysis_text, re.IGNORECASE):
        requested_info.append(InformationCategory.ERROR)
        print("LLM请求了错误记录")

    # 检查是否请求了详细持仓信息
    if re.search(r'(?:需要|请提供).*?(?:详细持仓|挂单详情|订单信息)', analysis_text, re.IGNORECASE):
        requested_info.append(InformationCategory.POSITION)
        print("LLM请求了详细持仓信息")

    # 如果没有明确请求任何信息，默认提供技术和基本面数据
    if not requested_info:
        print("LLM没有明确请求任何额外信息，默认提供技术和基本面数据")
        requested_info = [InformationCategory.TECHNICAL, InformationCategory.FUNDAMENTAL]

    print(f"解析完成，LLM请求的额外信息类别: {requested_info}")
    return requested_info


# 预分析功能已移除 - 系统现在直接执行完整分析
def should_perform_analysis(data):
    """
    预分析功能已移除，现在直接返回需要执行完整分析

    Args:
        data (dict): 分析数据

    Returns:
        tuple: (是否需要分析, 原因)
    """
    return True, "预分析功能已移除，直接执行完整分析"

def perform_multi_round_analysis(data, force_analysis=False):
    """
    执行多轮分析

    Args:
        data (dict): 分析数据
        force_analysis (bool): 是否强制执行完整分析，完全跳过预分析

    Returns:
        dict: 分析结果
    """
    try:
        # 导入监控系统
        try:
            from app.utils.real_time_monitor import real_time_monitor
            monitor_available = True
        except ImportError:
            monitor_available = False
            print("监控系统不可用")

        # 如果强制分析，则完全跳过预分析
        if force_analysis:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2: 强制分析，完全跳过预分析')
            should_analyze = True
            reason = "强制执行完整分析"
        else:
            # 预分析：判断是否需要执行完整分析
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2: 执行预分析，判断是否需要完整分析')
            should_analyze, reason = should_perform_analysis(data)
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2: 预分析结果: {"需要" if should_analyze else "不需要"}分析，原因: {reason}')

        if not should_analyze:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2: 跳过完整分析: {reason}')

            # 返回一个简单的观望结果，但包含完整的字段结构
            result = {
                'timestamp': datetime.now().isoformat(),
                'symbol': 'EURUSD',
                'currentPrice': data.get('currentPrice'),
                'analysis': {
                    'preAnalysis': f"预分析结果: {reason}",
                    'recommendation': "观望",
                    'technicalAnalysis': "预分析决定跳过完整分析",
                    'fundamentalAnalysis': "预分析决定跳过完整分析",
                    'marketSentiment': "预分析决定跳过完整分析"
                },
                'tradeInstructions': {
                    'action': 'NONE',
                    'orderType': 'MARKET',
                    'entryPrice': None,
                    'stopLoss': None,
                    'takeProfit': None,
                    'lotSize': 0.01,
                    'riskLevel': 'LOW',
                    'reasoning': reason,
                    'orderManagement': [],
                    'isFinalDecision': True
                },
                'final': True  # 标记为最终结果，避免被视为错误
            }

            return result

        # 第一轮：初始分析
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: 第一轮分析')
        initial_prompt = generate_initial_prompt(data)

        # 使用异常处理和重试机制
        max_retries = 3
        retry_count = 0
        initial_analysis = None
        all_api_failed = False  # 标记所有API调用是否都失败

        while retry_count < max_retries and initial_analysis is None:
            try:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: 开始发送第一轮分析请求 (尝试 {retry_count + 1}/{max_retries})')
                start_time = datetime.now()

                # 设置更短的max_tokens，减少响应时间
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: 开始调用LLM API，max_tokens=3000')
                try:
                    initial_response = llm_client.send_to_deepseek(initial_prompt, max_tokens=3000)

                    end_time = datetime.now()
                    elapsed_time = (end_time - start_time).total_seconds()
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: 第一轮分析请求完成，耗时: {elapsed_time:.2f}秒')

                    # 记录分析事件到监控系统
                    if monitor_available:
                        try:
                            # 估算token消耗（基于提示词长度）
                            estimated_tokens = len(initial_prompt) // 4  # 粗略估算
                            estimated_cost = estimated_tokens * 0.000001  # 粗略估算成本
                            real_time_monitor.record_analysis_event(
                                event_type='full_analysis',
                                success=True,
                                response_time=elapsed_time,
                                tokens=estimated_tokens,
                                cost=estimated_cost
                            )
                        except Exception as monitor_error:
                            print(f"记录监控事件失败: {monitor_error}")

                    # 打印完整的响应JSON，用于调试
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: 第一轮分析响应JSON:\n{json.dumps(initial_response, indent=2)[:500]}...')

                    # 检查响应JSON是否包含必要的字段
                    if 'choices' in initial_response and len(initial_response['choices']) > 0:
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: 响应JSON包含choices字段')

                        if 'message' in initial_response['choices'][0]:
                            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: 响应JSON包含message字段')

                            if 'content' in initial_response['choices'][0]['message']:
                                initial_analysis = initial_response['choices'][0]['message']['content']
                                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: 成功获取第一轮分析结果，长度: {len(initial_analysis)} 字符')

                                # 打印分析结果的前500个字符，用于调试
                                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: 第一轮分析结果预览:\n{initial_analysis[:500]}...')
                            else:
                                error_msg = "响应JSON缺少content字段"
                                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: 错误: {error_msg}')
                                raise Exception(error_msg)
                        else:
                            error_msg = "响应JSON缺少message字段"
                            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: 错误: {error_msg}')
                            raise Exception(error_msg)
                    else:
                        error_msg = "响应JSON缺少choices字段或choices为空"
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: 错误: {error_msg}')
                        raise Exception(error_msg)
                except Exception as api_error:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1: LLM API调用失败: {api_error}')
                    traceback.print_exc()

                    # 记录失败事件到监控系统
                    if monitor_available:
                        try:
                            real_time_monitor.record_analysis_event(
                                event_type='full_analysis',
                                success=False,
                                response_time=0,
                                tokens=0,
                                cost=0
                            )
                            real_time_monitor.record_error_event('api_failure')
                            real_time_monitor.add_alert('error', 'analysis', f'第一轮分析API调用失败: {str(api_error)}')
                        except Exception as monitor_error:
                            print(f"记录监控事件失败: {monitor_error}")

                    # 检查是否是网络连接问题
                    if "连接" in str(api_error) or "网络" in str(api_error) or "timeout" in str(api_error).lower():
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 检测到网络连接问题，将触发回退机制')
                    raise
            except KeyboardInterrupt:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 用户中断了第一轮分析，尝试使用备用方案')
                # 如果是用户中断，使用简化的初始分析
                initial_analysis = """
                # 初始分析

                由于API请求中断，使用简化的初始分析。

                ## 市场趋势
                需要更多技术指标和基本面数据来确定当前趋势。

                ## 需要的额外信息
                1. 详细技术指标
                2. 基本面数据
                3. 当前持仓信息
                """
                break
            except Exception as e:
                retry_count += 1
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 第一轮分析失败 ({retry_count}/{max_retries}): {e}')
                if retry_count >= max_retries:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 达到最大重试次数，所有API调用失败')
                    all_api_failed = True  # 标记所有API调用都失败
                    # 不再使用备用方案，而是抛出异常，触发回退机制
                    raise Exception("第一轮分析所有API调用都失败，触发回退机制")
                else:
                    time.sleep(2)  # 等待2秒后重试

        # 解析请求的信息类别
        requested_info = parse_requested_info(initial_analysis)

        # 第二轮：详细分析
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 第二轮分析')
        detail_prompt = generate_detail_prompt(data, initial_analysis, requested_info)

        # 使用异常处理和重试机制
        retry_count = 0
        detail_analysis = None

        while retry_count < max_retries and detail_analysis is None:
            try:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 开始发送第二轮分析请求 (尝试 {retry_count + 1}/{max_retries})')
                start_time = datetime.now()

                # 设置更短的max_tokens，减少响应时间
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 开始调用LLM API，max_tokens=3000')
                try:
                    detail_response = llm_client.send_to_deepseek(detail_prompt, max_tokens=3000)

                    end_time = datetime.now()
                    elapsed_time = (end_time - start_time).total_seconds()
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 第二轮分析请求完成，耗时: {elapsed_time:.2f}秒')

                    # 打印完整的响应JSON，用于调试
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 第二轮分析响应JSON:\n{json.dumps(detail_response, indent=2)[:500]}...')

                    # 检查响应JSON是否包含必要的字段
                    if 'choices' in detail_response and len(detail_response['choices']) > 0:
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 响应JSON包含choices字段')

                        if 'message' in detail_response['choices'][0]:
                            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 响应JSON包含message字段')

                            if 'content' in detail_response['choices'][0]['message']:
                                detail_analysis = detail_response['choices'][0]['message']['content']
                                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 成功获取第二轮分析结果，长度: {len(detail_analysis)} 字符')

                                # 打印分析结果的前500个字符，用于调试
                                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 第二轮分析结果预览:\n{detail_analysis[:500]}...')
                            else:
                                error_msg = "响应JSON缺少content字段"
                                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 错误: {error_msg}')
                                raise Exception(error_msg)
                        else:
                            error_msg = "响应JSON缺少message字段"
                            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 错误: {error_msg}')
                            raise Exception(error_msg)
                    else:
                        error_msg = "响应JSON缺少choices字段或choices为空"
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 错误: {error_msg}')
                        raise Exception(error_msg)
                except Exception as api_error:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: LLM API调用失败: {api_error}')
                    traceback.print_exc()
                    raise
            except KeyboardInterrupt:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 用户中断了第二轮分析，尝试使用备用方案')
                # 如果是用户中断，使用简化的详细分析
                detail_analysis = """
                # 详细分析

                由于API请求中断，使用简化的详细分析。

                ## 技术分析
                基于初始分析和提供的技术指标，市场目前处于不确定状态。

                ## 基本面分析
                需要考虑最新的经济数据和新闻事件。

                ## 支撑与阻力
                需要进一步分析确定关键价格水平。
                """
                break
            except Exception as e:
                retry_count += 1
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 第二轮分析失败 ({retry_count}/{max_retries}): {e}')
                if retry_count >= max_retries:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 达到最大重试次数，所有API调用失败')
                    # 不再使用备用方案，而是抛出异常，触发回退机制
                    raise Exception("第二轮分析所有API调用都失败，触发回退机制")
                else:
                    time.sleep(2)  # 等待2秒后重试

        # 第三轮：最终决策
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 第三轮分析')
        final_prompt = generate_final_prompt(data, initial_analysis, detail_analysis)

        # 使用异常处理和重试机制
        retry_count = 0
        final_analysis = None

        while retry_count < max_retries and final_analysis is None:
            try:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 开始发送第三轮分析请求 (尝试 {retry_count + 1}/{max_retries})')
                start_time = datetime.now()

                # 设置更短的max_tokens，减少响应时间
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 开始调用LLM API，max_tokens=3000')
                try:
                    final_response = llm_client.send_to_deepseek(final_prompt, max_tokens=3000)

                    end_time = datetime.now()
                    elapsed_time = (end_time - start_time).total_seconds()
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 第三轮分析请求完成，耗时: {elapsed_time:.2f}秒')

                    # 打印完整的响应JSON，用于调试
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 第三轮分析响应JSON:\n{json.dumps(final_response, indent=2)[:500]}...')

                    # 检查响应JSON是否包含必要的字段
                    if 'choices' in final_response and len(final_response['choices']) > 0:
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 响应JSON包含choices字段')

                        if 'message' in final_response['choices'][0]:
                            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 响应JSON包含message字段')

                            if 'content' in final_response['choices'][0]['message']:
                                final_analysis = final_response['choices'][0]['message']['content']
                                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 成功获取第三轮分析结果，长度: {len(final_analysis)} 字符')

                                # 打印分析结果的前500个字符，用于调试
                                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 第三轮分析结果预览:\n{final_analysis[:500]}...')

                                # 输出完整的LLM最终分析结果
                                now = datetime.now()
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> LLM完整分析结果开始 >>>')
                                print(final_analysis)
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] <<< LLM完整分析结果结束 <<<')
                            else:
                                error_msg = "响应JSON缺少content字段"
                                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 错误: {error_msg}')
                                raise Exception(error_msg)
                        else:
                            error_msg = "响应JSON缺少message字段"
                            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 错误: {error_msg}')
                            raise Exception(error_msg)
                    else:
                        error_msg = "响应JSON缺少choices字段或choices为空"
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 错误: {error_msg}')
                        raise Exception(error_msg)
                except Exception as api_error:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: LLM API调用失败: {api_error}')
                    traceback.print_exc()
                    raise
            except KeyboardInterrupt:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 用户中断了第三轮分析，尝试使用备用方案')
                # 如果是用户中断，使用观望的最终决策
                final_analysis = """
                # 最终决策

                由于API请求中断，使用保守的观望决策。

                ## 交易指令

                ```json
                {
                  "action": "NONE",
                  "orderType": "MARKET",
                  "entryPrice": null,
                  "stopLoss": null,
                  "takeProfit": null,
                  "lotSize": 0.01,
                  "riskLevel": "LOW",
                  "reasoning": "由于分析过程中断，采取保守的观望策略，等待更明确的市场信号。",
                  "orderManagement": []
                }
                ```

                ## 分析总结
                由于无法完成完整的分析过程，建议暂时观望，等待更明确的市场信号。
                """
                break
            except Exception as e:
                retry_count += 1
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 第三轮分析失败 ({retry_count}/{max_retries}): {e}')
                if retry_count >= max_retries:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 达到最大重试次数，所有API调用失败')
                    # 不再使用备用方案，而是抛出异常，触发回退机制
                    raise Exception("第三轮分析所有API调用都失败，触发回退机制")
                else:
                    time.sleep(2)  # 等待2秒后重试

        # 解析交易指令
        trade_instructions = llm_client.parse_trade_instructions(final_analysis)

        # 明确标记这是最终决策的交易指令
        trade_instructions['isFinalDecision'] = True

        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 最终决策交易指令: {trade_instructions}')

        # 确保订单管理指令也被标记为最终决策
        if 'orderManagement' in trade_instructions and trade_instructions['orderManagement']:
            for order_action in trade_instructions['orderManagement']:
                if isinstance(order_action, dict):
                    order_action['isFinalDecision'] = True

            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 订单管理指令已标记为最终决策')

        # 构建分析结果
        result = {
            'timestamp': datetime.now().isoformat(),
            'symbol': data.get('symbol', 'EURUSD'),
            'currentPrice': data.get('currentPrice'),
            'analysis': {
                'initial': initial_analysis,
                'detail': detail_analysis,
                'final': final_analysis
            },
            'tradeInstructions': trade_instructions,
            'data': data,
            'prompts': {
                'initial': initial_prompt,
                'detail': detail_prompt,
                'final': final_prompt
            }
        }

        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 多轮分析完成')

        # 记录分析完成事件到监控系统
        if monitor_available:
            try:
                real_time_monitor.add_alert('info', 'analysis', '多轮分析成功完成')
            except Exception as monitor_error:
                print(f"记录监控事件失败: {monitor_error}")

        # 生成token统计报告
        try:
            from app.utils.token_statistics import get_hourly_token_stats, generate_token_report

            # 获取最近24小时的token统计
            hourly_stats = get_hourly_token_stats(24)

            # 打印最近一小时的token消耗
            current_hour = now.strftime('%Y-%m-%d %H:00')

            if current_hour in hourly_stats:
                hour_data = hourly_stats[current_hour]
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> Token统计: 当前小时消耗: {hour_data["total_tokens"]:,} tokens, 成本: {hour_data["cost"]:.4f} 元')

            # 打印完整分析的token消耗
            from app.utils.token_statistics import load_token_stats
            stats = load_token_stats()
            if 'analysis_type_stats' in stats and 'full_analysis' in stats['analysis_type_stats']:
                full_analysis_stats = stats['analysis_type_stats']['full_analysis']
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> Token统计: 完整分析总消耗: {full_analysis_stats["total_tokens"]:,} tokens, 成本: {full_analysis_stats["cost"]:.4f} 元')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> Token统计: 完整分析平均每次消耗: {full_analysis_stats["total_tokens"] / full_analysis_stats["count"]:.1f} tokens')

            # 生成完整的token统计报告并直接打印
            token_report = generate_token_report()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> Token统计报告:')
            print(token_report)
        except Exception as token_error:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> Token统计生成失败: {token_error}')

        return result

    except Exception as error:
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 多轮分析失败: {error}')
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 错误类型: {type(error)}')

        # 打印详细的错误堆栈
        import traceback
        error_traceback = traceback.format_exc()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 错误堆栈:\n{error_traceback}')

        # 记录错误
        log_error(
            error_type=ErrorType.LLM_ERROR,
            message=f'多轮分析失败: {error}',
            details={'exception': str(error), 'traceback': error_traceback},
            operation='ANALYSIS'
        )

        # 检查是否是API调用失败触发的回退机制
        if "API调用都失败，触发回退机制" in str(error):
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到API调用失败，触发回退机制')

            # 创建回退信息对象
            fallback_info = {
                'timestamp': datetime.now().isoformat(),
                'error': str(error),
                'message': '多轮分析API调用失败，需要回退并重试',
                'retry_after': 300,  # 5分钟后重试
                'needs_fallback': True
            }

            # 抛出特殊异常，包含回退信息
            raise AnalysisFallbackException(fallback_info)

        # 其他类型的错误，提供一个备用结果
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 多轮分析失败，提供备用结果')

        # 构建备用分析结果
        backup_result = {
            'timestamp': datetime.now().isoformat(),
            'symbol': data.get('symbol', 'EURUSD'),
            'currentPrice': data.get('currentPrice'),
            'analysis': {
                'initial': "多轮分析失败，使用备用分析",
                'detail': "多轮分析失败，使用备用分析",
                'final': """
                # 备用分析结果

                由于多轮分析过程失败，系统提供了这个备用分析结果。

                ## 交易指令

                ```json
                {
                  "action": "NONE",
                  "orderType": "MARKET",
                  "entryPrice": null,
                  "stopLoss": null,
                  "takeProfit": null,
                  "lotSize": 0.01,
                  "riskLevel": "LOW",
                  "reasoning": "由于分析过程失败，采取保守的观望策略，等待系统恢复正常。",
                  "orderManagement": []
                }
                ```

                ## 分析总结
                由于无法完成完整的分析过程，建议暂时观望，等待系统恢复正常。
                """
            },
            'tradeInstructions': {
                'action': 'NONE',
                'orderType': 'MARKET',
                'entryPrice': None,
                'stopLoss': None,
                'takeProfit': None,
                'lotSize': 0.01,
                'riskLevel': 'LOW',
                'reasoning': '由于分析过程失败，采取保守的观望策略，等待系统恢复正常。',
                'orderManagement': [],
                'isFinalDecision': True
            },
            'data': data,
            'prompts': {
                'initial': "多轮分析失败，使用备用分析",
                'detail': "多轮分析失败，使用备用分析",
                'final': "多轮分析失败，使用备用分析"
            },
            'error': str(error)
        }

        return backup_result
