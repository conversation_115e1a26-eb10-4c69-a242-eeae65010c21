#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目清理脚本 - 安全移动不需要的文件到备份文件夹
"""

import os
import shutil
import glob
from datetime import datetime

def create_backup_structure():
    """创建备份文件夹结构"""

    backup_dirs = [
        'cleanup_backup/test_files',
        'cleanup_backup/cache_files',
        'cleanup_backup/log_files',
        'cleanup_backup/draft_docs',
        'cleanup_backup/optimization_proposals',
        'cleanup_backup/temp_files',
        'cleanup_backup/gui_files'
    ]

    for dir_path in backup_dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"✅ 创建备份目录: {dir_path}")

def move_test_files():
    """移动测试文件"""

    print("\n📋 移动测试文件...")

    test_patterns = [
        'test_*.py',
        'simple_test.py',
        'simple_*.py',
        'analyze_database_data.py',
        'check_*.py',
        'final_*.py',
        'system_test_report.py'
    ]

    moved_count = 0
    for pattern in test_patterns:
        for file_path in glob.glob(pattern):
            if os.path.isfile(file_path):
                dest_path = f'cleanup_backup/test_files/{os.path.basename(file_path)}'
                shutil.move(file_path, dest_path)
                print(f"   📁 {file_path} → {dest_path}")
                moved_count += 1

    print(f"✅ 移动了 {moved_count} 个测试文件")

def move_cache_files():
    """移动缓存文件"""

    print("\n📋 移动缓存文件...")

    cache_patterns = [
        '__pycache__',
        'app/__pycache__',
        'app/*/__pycache__',
        'app/*/*/__pycache__',
        'optimization_proposals/__pycache__',
        '*.pyc'
    ]

    moved_count = 0
    for pattern in cache_patterns:
        for path in glob.glob(pattern, recursive=True):
            if os.path.exists(path):
                dest_name = path.replace('/', '_').replace('\\', '_')
                if os.path.isdir(path):
                    dest_path = f'cleanup_backup/cache_files/{dest_name}'
                    shutil.move(path, dest_path)
                else:
                    dest_path = f'cleanup_backup/cache_files/{os.path.basename(path)}'
                    shutil.move(path, dest_path)
                print(f"   📁 {path} → {dest_path}")
                moved_count += 1

    print(f"✅ 移动了 {moved_count} 个缓存文件/文件夹")

def move_log_files():
    """移动日志文件"""

    print("\n📋 移动开发日志文件...")

    log_files = [
        'forex_realtime.log',
        'forex_realtime_debug.log',
        'forex_system.log',
        'mt4_stability_test.log',
        'log.txt',
        'temp_output.txt',
        'mt4_data.json',
        'learning_data_*.json',
        'optimization_results_*.json',
        'portfolio_data_*.json'
    ]

    moved_count = 0
    for pattern in log_files:
        for file_path in glob.glob(pattern):
            if os.path.isfile(file_path):
                dest_path = f'cleanup_backup/log_files/{os.path.basename(file_path)}'
                try:
                    shutil.move(file_path, dest_path)
                    print(f"   📁 {file_path} → {dest_path}")
                    moved_count += 1
                except PermissionError:
                    print(f"   ⚠️ 跳过 {file_path} (文件被占用)")
                except Exception as e:
                    print(f"   ❌ 移动失败 {file_path}: {e}")

    print(f"✅ 移动了 {moved_count} 个日志文件")

def move_optimization_proposals():
    """移动优化提案文件夹"""

    print("\n📋 移动优化提案...")

    if os.path.exists('optimization_proposals'):
        shutil.move('optimization_proposals', 'cleanup_backup/optimization_proposals/proposals')
        print("   📁 optimization_proposals → cleanup_backup/optimization_proposals/proposals")
        print("✅ 移动了优化提案文件夹")
    else:
        print("⚠️ 优化提案文件夹不存在")

def move_gui_files():
    """移动GUI相关文件（保留一个主要的）"""

    print("\n📋 移动多余的GUI文件...")

    gui_files = [
        'visualization_dashboard.py',
        'web_dashboard.py',
        'real_data_dashboard.py',
        '可视化仪表板使用说明.md'
    ]

    moved_count = 0
    for file_path in gui_files:
        if os.path.isfile(file_path):
            dest_path = f'cleanup_backup/gui_files/{os.path.basename(file_path)}'
            shutil.move(file_path, dest_path)
            print(f"   📁 {file_path} → {dest_path}")
            moved_count += 1

    print(f"✅ 移动了 {moved_count} 个GUI文件（保留simple_dashboard.py）")

def move_draft_docs():
    """移动文档草稿"""

    print("\n📋 移动分析文档...")

    if os.path.exists('analysis'):
        shutil.move('analysis', 'cleanup_backup/draft_docs/analysis')
        print("   📁 analysis → cleanup_backup/draft_docs/analysis")
        print("✅ 移动了分析文档文件夹")
    else:
        print("⚠️ 分析文档文件夹不存在")

def move_temp_files():
    """移动临时文件"""

    print("\n📋 移动临时文件...")

    temp_items = [
        'tmp',
        'temp',
        'data/order_results.json',
        'data/virtual_account.json',
        'appdata'
    ]

    moved_count = 0
    for item in temp_items:
        if os.path.exists(item):
            dest_name = item.replace('/', '_').replace('\\', '_')
            dest_path = f'cleanup_backup/temp_files/{dest_name}'
            shutil.move(item, dest_path)
            print(f"   📁 {item} → {dest_path}")
            moved_count += 1

    print(f"✅ 移动了 {moved_count} 个临时文件/文件夹")

def create_cleanup_report():
    """创建清理报告"""

    print("\n📋 生成清理报告...")

    report_content = f"""# 项目清理报告

清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 清理概要

本次清理将以下类型的文件移动到 cleanup_backup 文件夹：

### 1. 测试文件
- 所有 test_*.py 文件
- 简单测试脚本
- 数据库检查脚本
- 系统测试报告

### 2. 缓存文件
- Python __pycache__ 文件夹
- 编译后的 .pyc 文件

### 3. 日志文件
- 开发期间的日志文件
- 调试输出文件
- 临时数据文件

### 4. 优化提案
- optimization_proposals 整个文件夹
- 各种实验性代码

### 5. GUI文件
- 多余的可视化文件（保留 simple_dashboard.py）

### 6. 文档草稿
- analysis 分析文档文件夹

### 7. 临时文件
- tmp, temp 文件夹
- 临时数据文件

## 保留的核心文件

以下文件保留在项目根目录，用于生产部署：

### 核心应用
- run.py (主启动文件)
- app/ (核心应用代码)
- requirements.txt (依赖列表)
- README.md (项目说明)

### 配置文件
- .env.local (环境配置)
- Dockerfile (容器配置)

### 文档
- docs/ (保留核心文档)

### 数据
- app/data/ (保留必要的数据文件)
- logs/ (保留日志结构，清空内容)

### GUI
- simple_dashboard.py (主要的GUI界面)

## 恢复说明

如果需要恢复任何文件，可以从 cleanup_backup 对应的子文件夹中找到。

## 下一步

1. 检查清理结果
2. 测试核心功能
3. 准备生产环境配置
"""

    with open('cleanup_backup/cleanup_report.md', 'w', encoding='utf-8') as f:
        f.write(report_content)

    print("✅ 清理报告已生成: cleanup_backup/cleanup_report.md")

def main():
    """主函数"""

    print("🚀 开始项目清理...")
    print("=" * 50)

    # 创建备份结构
    create_backup_structure()

    # 执行清理
    move_test_files()
    move_cache_files()
    move_log_files()
    move_optimization_proposals()
    move_gui_files()
    move_draft_docs()
    move_temp_files()

    # 生成报告
    create_cleanup_report()

    print("\n" + "=" * 50)
    print("🎉 项目清理完成！")
    print("📁 备份文件位置: cleanup_backup/")
    print("📄 清理报告: cleanup_backup/cleanup_report.md")
    print("\n💡 下一步: 检查清理结果并测试核心功能")

if __name__ == "__main__":
    main()
