"""
测试止损保护机制
"""
import os
import sys
import time
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 打印调试信息
print("Python版本:", sys.version)
print("当前工作目录:", os.getcwd())
print("系统路径:", sys.path)

try:
    from app.services.forex_trading_service import execute_trade
    from app.utils import mt4_client
    print("成功导入模块")
except Exception as e:
    print("导入模块失败:", e)


def test_no_stop_loss_order():
    """测试无止损订单的保护机制"""
    print("=" * 50)
    print("测试无止损订单的保护机制")
    print("=" * 50)

    # 创建一个无止损的交易指令
    trade_instructions = {
        'action': 'BUY',
        'orderType': 'MARKET',
        'entryPrice': None,
        'stopLoss': 0,  # 设置为0，测试保护机制
        'takeProfit': 1.13000,
        'lotSize': 0.01,
        'reasoning': '测试无止损保护机制',
        'isFinalDecision': True
    }

    print(f"交易指令: {trade_instructions}")

    # 执行交易
    result = execute_trade(trade_instructions, check_duplicate=True)

    print(f"交易结果: {result}")
    print("=" * 50)

    return result


def test_no_stop_loss_modify():
    """测试修改订单为无止损的保护机制"""
    print("=" * 50)
    print("测试修改订单为无止损的保护机制")
    print("=" * 50)

    # 获取当前持仓
    positions_response = mt4_client.mt4_client.get_active_orders()
    positions = positions_response.get('orders', [])

    # 过滤出EURUSD的持仓
    eurusd_positions = [p for p in positions if p.get('symbol') == 'EURUSD']

    if not eurusd_positions:
        print("没有找到EURUSD持仓，无法测试修改订单")
        return None

    # 使用第一个持仓进行测试
    position = eurusd_positions[0]
    order_id = position.get('order_id')

    print(f"找到持仓: ID: {order_id}, 类型: {position.get('type')}, 止损: {position.get('sl')}, 止盈: {position.get('tp')}")

    # 创建一个修改订单为无止损的交易指令
    trade_instructions = {
        'action': 'NONE',
        'orderType': 'MARKET',
        'entryPrice': None,
        'stopLoss': None,
        'takeProfit': None,
        'lotSize': 0.01,
        'reasoning': '测试修改订单为无止损的保护机制',
        'orderManagement': [
            {
                'action': 'MODIFY',
                'orderId': order_id,
                'newStopLoss': 0,  # 设置为0，测试保护机制
                'newTakeProfit': position.get('tp'),
                'reason': '测试修改订单为无止损的保护机制'
            }
        ],
        'isFinalDecision': True
    }

    print(f"交易指令: {trade_instructions}")

    # 执行交易
    result = execute_trade(trade_instructions, check_duplicate=True)

    print(f"交易结果: {result}")
    print("=" * 50)

    return result


def main():
    """主函数"""
    print("开始测试止损保护机制")

    # 确保MT4客户端已连接
    if not mt4_client.mt4_client.is_connected:
        print("MT4客户端未连接，尝试连接")
        connected = mt4_client.mt4_client.connect()
        if not connected:
            print("无法连接到MT4客户端，测试终止")
            return

    # 测试无止损订单的保护机制
    test_no_stop_loss_order()

    # 等待2秒
    time.sleep(2)

    # 测试修改订单为无止损的保护机制
    test_no_stop_loss_modify()

    print("测试完成")


if __name__ == "__main__":
    main()
