#!/usr/bin/env python3
"""
硅基流动API测试脚本
"""

import os
import openai
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_siliconflow_api():
    """测试硅基流动API"""
    print("🧪 测试硅基流动API...")
    
    # 获取API密钥
    api_key = os.getenv('SILICONFLOW_API_KEY')
    print(f"🔑 API密钥: {api_key[:10]}...{api_key[-4:] if api_key else 'None'}")
    
    if not api_key:
        print("❌ 未找到SILICONFLOW_API_KEY")
        return False
    
    try:
        # 创建客户端
        client = openai.OpenAI(
            api_key=api_key,
            base_url="https://api.siliconflow.cn/v1"
        )
        
        print("📡 发送测试请求...")
        
        # 发送测试请求
        response = client.chat.completions.create(
            model="Pro/deepseek-ai/DeepSeek-R1",
            messages=[
                {
                    "role": "system",
                    "content": "你是一个友好的AI助手，请用中文简洁回复。"
                },
                {
                    "role": "user",
                    "content": "你好，请简单介绍一下你自己"
                }
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        print("✅ API调用成功！")
        print(f"🤖 AI回复: {response.choices[0].message.content}")
        
        # 显示token使用情况
        if hasattr(response, 'usage'):
            usage = response.usage
            print(f"📊 Token使用: 输入={usage.prompt_tokens}, 输出={usage.completion_tokens}, 总计={usage.total_tokens}")
        
        return True
        
    except Exception as e:
        print(f"❌ API调用失败: {str(e)}")
        return False

def test_different_models():
    """测试不同模型"""
    print("\n🔄 测试不同模型...")
    
    models = [
        "Pro/deepseek-ai/DeepSeek-R1",
        "Pro/deepseek-ai/DeepSeek-V3",
        "Qwen/Qwen2.5-72B-Instruct"
    ]
    
    api_key = os.getenv('SILICONFLOW_API_KEY')
    if not api_key:
        print("❌ 未找到API密钥")
        return
    
    client = openai.OpenAI(
        api_key=api_key,
        base_url="https://api.siliconflow.cn/v1"
    )
    
    for model in models:
        print(f"\n🧪 测试模型: {model}")
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": "说一句话"}
                ],
                max_tokens=50
            )
            print(f"✅ {model}: {response.choices[0].message.content[:50]}...")
        except Exception as e:
            print(f"❌ {model}: {str(e)}")

if __name__ == "__main__":
    print("🚀 硅基流动API测试工具")
    print("=" * 50)
    
    # 基础测试
    if test_siliconflow_api():
        # 模型测试
        test_different_models()
    
    print("\n✨ 测试完成！")
