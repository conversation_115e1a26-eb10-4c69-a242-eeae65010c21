#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的表结构
"""

import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_database_tables():
    """检查数据库中的表"""

    try:
        from app.utils.db_client import get_connection

        conn = get_connection()
        if not conn:
            print("❌ 无法连接到数据库")
            return

        cursor = conn.cursor()

        # 显示所有表
        print("📊 检查数据库表结构...")
        print("=" * 50)

        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()

        print(f"📋 数据库中的表 ({len(tables)}个):")
        for table in tables:
            table_name = table[0]
            print(f"   📄 {table_name}")

            try:
                # 检查数据量
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]
                print(f"      数据量: {count} 条")

                # 如果表名包含eurusd，显示更多信息
                if 'eurusd' in table_name.lower():
                    print(f"      ⭐ EURUSD相关表!")

                    # 检查表结构
                    cursor.execute(f"DESCRIBE `{table_name}`")
                    columns = cursor.fetchall()

                    print(f"      列结构:")
                    for col in columns:
                        print(f"        - {col[0]} ({col[1]})")

                    # 检查最新数据
                    if count > 0:
                        cursor.execute(f"SELECT * FROM `{table_name}` ORDER BY timestamp DESC LIMIT 1")
                        latest = cursor.fetchone()
                        if latest:
                            print(f"      最新数据: {latest}")

            except Exception as e:
                print(f"      ❌ 查询表失败: {e}")

            print()

        conn.close()

    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")

def test_eurusd_data():
    """测试EURUSD数据获取"""

    try:
        from app.utils.db_client import get_connection

        conn = get_connection()
        if not conn:
            print("❌ 无法连接到数据库")
            return

        cursor = conn.cursor()

        print("\n🔍 测试EURUSD数据获取...")
        print("=" * 50)

        # 尝试不同的表名
        possible_tables = [
            'eurusd_m1',
            'eurusd_1m',
            'eurusd',
            'EURUSD_M1',
            'EURUSD',
            'm1_eurusd',
            'forex_eurusd_m1'
        ]

        for table_name in possible_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"✅ 表 {table_name} 存在，数据量: {count}")

                if count > 0:
                    # 获取最新5条数据
                    cursor.execute(f"SELECT timestamp, open, high, low, close, volume FROM {table_name} ORDER BY timestamp DESC LIMIT 5")
                    results = cursor.fetchall()

                    print(f"   最新5条数据:")
                    for i, row in enumerate(results):
                        print(f"   {i+1}. {row}")

                break

            except Exception as e:
                print(f"❌ 表 {table_name} 不存在或查询失败: {e}")

        conn.close()

    except Exception as e:
        print(f"❌ 测试EURUSD数据失败: {e}")

def main():
    """主函数"""

    print("🔍 开始检查数据库...")

    # 检查所有表
    check_database_tables()

    # 测试EURUSD数据
    test_eurusd_data()

if __name__ == "__main__":
    main()
