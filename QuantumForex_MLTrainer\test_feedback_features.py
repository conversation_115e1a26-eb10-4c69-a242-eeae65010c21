#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试反馈特征工程
"""

import sys
import os
import logging
import pandas as pd
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_feedback_features():
    """测试反馈特征工程"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🧪 开始测试反馈特征工程...")
    
    try:
        # 1. 收集Pro端数据
        logger.info("步骤1: 收集Pro端反馈数据...")
        from data_collector.pro_data_collector import pro_data_collector
        
        pro_feedback = pro_data_collector.collect_all_data(days=1)
        logger.info(f"✅ 收集到反馈数据: {len(pro_feedback.get('trade_records', []))}条交易记录")
        
        # 2. 创建测试市场数据
        logger.info("步骤2: 创建测试市场数据...")
        dates = pd.date_range('2024-01-01', periods=500, freq='1min')
        np.random.seed(42)
        
        price_changes = np.random.randn(500) * 0.0001
        prices = 1.1000 + np.cumsum(price_changes)
        
        market_data = pd.DataFrame({
            'close': prices,
            'open': prices + np.random.randn(500) * 0.00005,
            'high': prices + np.abs(np.random.randn(500)) * 0.0001,
            'low': prices - np.abs(np.random.randn(500)) * 0.0001,
            'volume': np.random.randint(1000, 10000, 500)
        }, index=dates)
        
        # 确保OHLC逻辑正确
        market_data['high'] = np.maximum(market_data[['open', 'close']].max(axis=1), market_data['high'])
        market_data['low'] = np.minimum(market_data[['open', 'close']].min(axis=1), market_data['low'])
        
        logger.info(f"✅ 创建市场数据: {len(market_data)}条记录")
        
        # 3. 测试反馈特征生成
        logger.info("步骤3: 生成反馈特征...")
        from feature_engineering.feedback_features import feedback_engine
        
        enhanced_data = feedback_engine.generate_feedback_features(
            market_data,
            pro_feedback.get('trade_records', []),
            pro_feedback.get('parameter_optimizations', []),
            pro_feedback.get('llm_analyses', [])
        )
        
        feedback_features = feedback_engine.get_feature_names()
        logger.info(f"✅ 生成反馈特征: {len(feedback_features)}个")
        
        # 4. 显示特征详情
        logger.info("📊 反馈特征详情:")
        for i, feature in enumerate(feedback_features):
            if i < 10:  # 只显示前10个
                sample_value = enhanced_data[feature].iloc[-1] if feature in enhanced_data.columns else "N/A"
                logger.info(f"  - {feature}: {sample_value}")
            elif i == 10:
                logger.info(f"  ... 还有{len(feedback_features) - 10}个特征")
                break
        
        # 5. 验证数据完整性
        logger.info("步骤4: 验证数据完整性...")
        
        original_columns = len(market_data.columns)
        enhanced_columns = len(enhanced_data.columns)
        added_features = enhanced_columns - original_columns
        
        logger.info(f"✅ 数据完整性验证:")
        logger.info(f"  - 原始列数: {original_columns}")
        logger.info(f"  - 增强后列数: {enhanced_columns}")
        logger.info(f"  - 新增特征: {added_features}")
        logger.info(f"  - 特征名称数量: {len(feedback_features)}")
        
        # 6. 检查特征值范围
        logger.info("步骤5: 检查特征值范围...")
        
        for feature in feedback_features[:5]:  # 检查前5个特征
            if feature in enhanced_data.columns:
                values = enhanced_data[feature]
                logger.info(f"  - {feature}: min={values.min():.4f}, max={values.max():.4f}, mean={values.mean():.4f}")
        
        logger.info("🎉 反馈特征工程测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_feedback_features()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    input("按任意键退出...")
