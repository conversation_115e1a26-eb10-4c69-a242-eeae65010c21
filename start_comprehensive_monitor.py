#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合监听系统
同时运行订单监听器和智能日志分析器
提供完整的Pro系统监控解决方案
"""

import sys
import os
import logging
import time
import threading
from datetime import datetime

# 导入监听器
from start_simple_monitor import SimpleTradingMonitor
from intelligent_log_analyzer import IntelligentLogAnalyzer

class ComprehensiveMonitor:
    """综合监听系统"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 创建监听器实例
        self.trading_monitor = SimpleTradingMonitor()
        self.log_analyzer = IntelligentLogAnalyzer()
        
        # 监听状态
        self.is_running = False
        self.status_thread = None
    
    def start_all_monitoring(self):
        """启动所有监听器"""
        try:
            self.logger.info("[START] 启动综合监听系统")
            self.logger.info("=" * 80)
            
            self.is_running = True
            
            # 启动交易订单监听器
            self.logger.info("[1] 启动交易订单监听器...")
            self.trading_monitor.start_monitoring()
            
            # 启动智能日志分析器
            self.logger.info("[2] 启动智能日志分析器...")
            self.log_analyzer.start_monitoring()
            
            # 启动状态汇总线程
            self._start_status_summary()
            
            self.logger.info("=" * 80)
            self.logger.info("[OK] 综合监听系统启动完成")
            self.logger.info("")
            self.logger.info("[DATA] 监听组件:")
            self.logger.info("   [TRADE] 交易订单监听器 - 实时订单、重复检测、规则检查")
            self.logger.info("   [LOG] 智能日志分析器 - 日志解析、问题识别、状态分析")
            self.logger.info("")
            self.logger.info("[SCAN] 综合监听进行中...")
            self.logger.info("[TIP] 按 Ctrl+C 停止所有监听")
            
        except Exception as e:
            self.logger.error(f"[ERROR] 启动综合监听系统失败: {e}")
    
    def stop_all_monitoring(self):
        """停止所有监听器"""
        try:
            self.logger.info("[STOP] 停止综合监听系统")
            self.is_running = False
            
            # 停止状态汇总线程
            if self.status_thread and self.status_thread.is_alive():
                self.status_thread.join(timeout=5)
            
            # 停止智能日志分析器
            self.logger.info("[1] 停止智能日志分析器...")
            self.log_analyzer.stop_monitoring()
            
            # 停止交易订单监听器
            self.logger.info("[2] 停止交易订单监听器...")
            self.trading_monitor.stop_monitoring()
            
            # 生成综合报告
            self._generate_comprehensive_report()
            
            self.logger.info("[OK] 综合监听系统已停止")
            
        except Exception as e:
            self.logger.error(f"[ERROR] 停止监听系统失败: {e}")
    
    def _start_status_summary(self):
        """启动状态汇总线程"""
        def status_summary_loop():
            while self.is_running:
                try:
                    time.sleep(60)  # 每分钟汇总一次
                    if self.is_running:
                        self._log_comprehensive_status()
                except Exception as e:
                    self.logger.debug(f"状态汇总异常: {e}")
        
        self.status_thread = threading.Thread(target=status_summary_loop, daemon=True)
        self.status_thread.start()
    
    def _log_comprehensive_status(self):
        """记录综合状态"""
        try:
            # 获取各监听器状态
            trading_status = self.trading_monitor.get_current_status()
            log_status = self.log_analyzer.get_current_status()
            
            # 汇总信息
            current_orders = trading_status['current_orders_count']
            trading_violations = trading_status['rule_violations']
            log_issues = log_status['detected_issues']
            total_events = log_status['total_events']
            
            self.logger.info("=" * 60)
            self.logger.info("[DATA] 综合监听状态汇总")
            self.logger.info("=" * 60)
            self.logger.info(f"[TRADE] 交易监听: {'运行中' if trading_status['is_monitoring'] else '停止'}")
            self.logger.info(f"[LOG] 日志分析: {'运行中' if log_status['is_monitoring'] else '停止'}")
            self.logger.info(f"[DATA] 当前订单: {current_orders}个")
            self.logger.info(f"[DATA] 日志事件: {total_events}个")
            self.logger.info(f"[WARN] 交易违规: {trading_violations}次")
            self.logger.info(f"[ALERT] 日志问题: {log_issues}个")
            
            # 显示最近的重要问题
            recent_log_issues = log_status.get('recent_issues', [])
            high_priority_issues = [issue for issue in recent_log_issues 
                                  if issue['severity'] in ['high', 'critical']]
            
            if high_priority_issues:
                self.logger.info("[ALERT] 最近重要问题:")
                for issue in high_priority_issues[-3:]:
                    self.logger.info(f"   [LOG] {issue['description']}: {issue['count']}次")
            
            if trading_violations > 0:
                self.logger.info(f"[WARN] 交易规则违反: {trading_violations}次")
            
            if not high_priority_issues and trading_violations == 0:
                self.logger.info("[OK] 无重要问题")
            
            self.logger.info("=" * 60)
            
        except Exception as e:
            self.logger.debug(f"记录综合状态失败: {e}")
    
    def _generate_comprehensive_report(self):
        """生成综合报告"""
        try:
            # 获取各监听器状态
            trading_status = self.trading_monitor.get_current_status()
            log_status = self.log_analyzer.get_current_status()
            
            self.logger.info("=" * 80)
            self.logger.info("[DATA] 综合监听系统报告")
            self.logger.info("=" * 80)
            
            # 交易监听总结
            self.logger.info("[TRADE] 交易监听统计:")
            self.logger.info(f"   检测订单: {trading_status['total_orders_detected']}个")
            self.logger.info(f"   重复订单: {trading_status['duplicate_orders_detected']}个")
            self.logger.info(f"   规则违反: {trading_status['rule_violations']}次")
            self.logger.info(f"   交易警告: {trading_status['total_alerts']}条")
            
            # 日志分析总结
            self.logger.info("")
            self.logger.info("[LOG] 日志分析统计:")
            self.logger.info(f"   日志事件: {log_status['total_events']}个")
            self.logger.info(f"   检测问题: {log_status['detected_issues']}个")
            
            # 显示具体问题
            recent_issues = log_status.get('recent_issues', [])
            if recent_issues:
                self.logger.info("   主要问题:")
                for issue in recent_issues:
                    severity_icon = {'low': '[INFO]', 'medium': '[WARN]', 'high': '[ALERT]', 'critical': '[CRITICAL]'}
                    icon = severity_icon.get(issue['severity'], '[INFO]')
                    self.logger.info(f"     {icon} {issue['description']}: {issue['count']}次")
            
            # 系统健康评估
            self.logger.info("")
            self.logger.info("[HEALTH] 系统健康评估:")
            
            health_score = 100
            issues = []
            
            # 交易健康检查
            if trading_status['rule_violations'] > 5:
                health_score -= 20
                issues.append("交易规则违反较多")
            
            if trading_status['duplicate_orders_detected'] > 3:
                health_score -= 15
                issues.append("重复订单问题")
            
            # 日志健康检查
            critical_issues = [issue for issue in recent_issues if issue['severity'] == 'critical']
            high_issues = [issue for issue in recent_issues if issue['severity'] == 'high']
            
            if critical_issues:
                health_score -= 30
                issues.append("严重系统问题")
            
            if len(high_issues) > 2:
                health_score -= 20
                issues.append("多个高优先级问题")
            
            # 健康评级
            if health_score >= 90:
                health_rating = "[OK] 优秀"
            elif health_score >= 70:
                health_rating = "[WARN] 良好"
            elif health_score >= 50:
                health_rating = "[WARN] 一般"
            else:
                health_rating = "[ALERT] 需要关注"
            
            self.logger.info(f"   健康评分: {health_score}/100")
            self.logger.info(f"   健康评级: {health_rating}")
            
            if issues:
                self.logger.info("   主要问题:")
                for issue in issues:
                    self.logger.info(f"     - {issue}")
            
            # 建议
            self.logger.info("")
            self.logger.info("[TIP] 监听建议:")
            
            if health_score < 70:
                self.logger.info("   1. 系统存在问题，建议立即检查")
                if critical_issues:
                    self.logger.info("   2. 优先解决严重问题")
                if trading_status['rule_violations'] > 0:
                    self.logger.info("   3. 检查交易规则配置")
            else:
                self.logger.info("   1. 系统运行良好，继续监控")
                self.logger.info("   2. 定期检查监听报告")
            
            self.logger.info("=" * 80)
            
        except Exception as e:
            self.logger.error(f"[ERROR] 生成综合报告失败: {e}")

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'comprehensive_monitor_{datetime.now().strftime("%Y%m%d")}.log', encoding='utf-8')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("[START] 综合监听系统")
    logger.info("[INFO] 同时监听订单和日志，提供完整的Pro系统监控")
    logger.info("[SCAN] 智能识别问题，生成健康评估报告")
    logger.info("=" * 80)
    
    # 创建综合监听系统
    comprehensive_monitor = ComprehensiveMonitor()
    
    try:
        # 启动所有监听
        comprehensive_monitor.start_all_monitoring()
        
        # 保持运行
        while comprehensive_monitor.is_running:
            time.sleep(1)
    
    except KeyboardInterrupt:
        logger.info("[STOP] 收到停止信号")
    except Exception as e:
        logger.error(f"[ERROR] 监听系统运行异常: {e}")
    finally:
        # 停止所有监听
        comprehensive_monitor.stop_all_monitoring()

if __name__ == "__main__":
    main()
