"""
MT4连接管理器
用于管理与MT4的连接，提供更可靠的连接方案
"""
import time
import traceback
from datetime import datetime
import threading
import queue
from typing import Dict, Any, Optional, List, Tuple, Callable

# 导入MT4客户端
from app.utils.mt4_client import mt4_client
from app.utils.error_logger import log_error, ErrorType, OperationType

class MT4ConnectionManager:
    """MT4连接管理器，提供更可靠的连接方案"""
    
    def __init__(self, 
                 max_retries: int = 3, 
                 retry_delay: float = 2.0,
                 connection_timeout: float = 10.0,
                 heartbeat_interval: float = 60.0,
                 auto_reconnect: bool = True):
        """
        初始化MT4连接管理器
        
        Args:
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            connection_timeout: 连接超时时间（秒）
            heartbeat_interval: 心跳检测间隔（秒）
            auto_reconnect: 是否自动重连
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.connection_timeout = connection_timeout
        self.heartbeat_interval = heartbeat_interval
        self.auto_reconnect = auto_reconnect
        
        # 连接状态
        self.is_connected = False
        self.last_connected_time = None
        self.connection_attempts = 0
        self.successful_connections = 0
        self.failed_connections = 0
        
        # 心跳检测线程
        self.heartbeat_thread = None
        self.heartbeat_running = False
        
        # 操作队列和结果队列
        self.operation_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # 操作线程
        self.operation_thread = None
        self.operation_running = False
        
        # 连接锁，防止并发连接
        self.connection_lock = threading.Lock()
        
        # 初始化连接
        if self.auto_reconnect:
            self.connect()
            
            # 启动心跳检测线程
            self.start_heartbeat()
            
            # 启动操作线程
            self.start_operation_thread()
    
    def log(self, message: str) -> None:
        """记录日志"""
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] [MT4连接管理器] {message}')
    
    def connect(self) -> bool:
        """
        连接到MT4服务器
        
        Returns:
            bool: 连接是否成功
        """
        # 使用连接锁，防止并发连接
        with self.connection_lock:
            # 如果已经连接，直接返回
            if self.is_connected and mt4_client.is_connected:
                self.log("已经连接到MT4服务器")
                return True
            
            self.log("尝试连接到MT4服务器...")
            self.connection_attempts += 1
            
            # 尝试连接
            try:
                # 设置连接超时
                start_time = time.time()
                connected = mt4_client.connect()
                end_time = time.time()
                
                # 检查连接是否成功
                if connected:
                    self.is_connected = True
                    self.last_connected_time = datetime.now()
                    self.successful_connections += 1
                    self.log(f"连接成功，耗时: {(end_time - start_time):.2f}秒")
                    return True
                else:
                    self.is_connected = False
                    self.failed_connections += 1
                    self.log(f"连接失败，耗时: {(end_time - start_time):.2f}秒")
                    return False
            except Exception as e:
                self.is_connected = False
                self.failed_connections += 1
                self.log(f"连接出错: {e}")
                traceback.print_exc()
                return False
    
    def disconnect(self) -> bool:
        """
        断开与MT4服务器的连接
        
        Returns:
            bool: 断开连接是否成功
        """
        with self.connection_lock:
            if not self.is_connected:
                self.log("已经断开连接")
                return True
            
            self.log("断开与MT4服务器的连接...")
            
            try:
                mt4_client.disconnect()
                self.is_connected = False
                self.log("断开连接成功")
                return True
            except Exception as e:
                self.log(f"断开连接出错: {e}")
                traceback.print_exc()
                return False
    
    def reconnect(self) -> bool:
        """
        重新连接到MT4服务器
        
        Returns:
            bool: 重连是否成功
        """
        self.log("尝试重新连接到MT4服务器...")
        
        # 先断开连接
        self.disconnect()
        
        # 等待一段时间
        time.sleep(self.retry_delay)
        
        # 重新连接
        return self.connect()
    
    def check_connection(self) -> bool:
        """
        检查与MT4服务器的连接状态
        
        Returns:
            bool: 连接是否正常
        """
        if not self.is_connected or not mt4_client.is_connected:
            self.log("连接已断开，尝试重新连接...")
            return self.reconnect()
        
        # 尝试获取市场信息，检查连接是否正常
        try:
            market_info = mt4_client.get_market_info('EURUSD')
            if market_info and market_info.get('status') == 'success':
                return True
            else:
                self.log("连接异常，尝试重新连接...")
                return self.reconnect()
        except Exception as e:
            self.log(f"检查连接出错: {e}")
            return self.reconnect()
    
    def start_heartbeat(self) -> None:
        """启动心跳检测线程"""
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            self.log("心跳检测线程已经在运行")
            return
        
        self.heartbeat_running = True
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
        self.heartbeat_thread.start()
        self.log("心跳检测线程已启动")
    
    def stop_heartbeat(self) -> None:
        """停止心跳检测线程"""
        if not self.heartbeat_thread or not self.heartbeat_thread.is_alive():
            self.log("心跳检测线程未运行")
            return
        
        self.heartbeat_running = False
        self.heartbeat_thread.join(timeout=5)
        self.log("心跳检测线程已停止")
    
    def _heartbeat_loop(self) -> None:
        """心跳检测循环"""
        self.log("心跳检测循环开始")
        
        while self.heartbeat_running:
            # 检查连接状态
            if not self.check_connection() and self.auto_reconnect:
                # 如果连接异常且启用了自动重连，尝试重连
                for attempt in range(self.max_retries):
                    self.log(f"自动重连尝试 {attempt+1}/{self.max_retries}...")
                    if self.reconnect():
                        self.log("自动重连成功")
                        break
                    else:
                        self.log("自动重连失败，等待后重试...")
                        time.sleep(self.retry_delay)
            
            # 等待下一次心跳检测
            time.sleep(self.heartbeat_interval)
        
        self.log("心跳检测循环结束")
    
    def start_operation_thread(self) -> None:
        """启动操作线程"""
        if self.operation_thread and self.operation_thread.is_alive():
            self.log("操作线程已经在运行")
            return
        
        self.operation_running = True
        self.operation_thread = threading.Thread(target=self._operation_loop, daemon=True)
        self.operation_thread.start()
        self.log("操作线程已启动")
    
    def stop_operation_thread(self) -> None:
        """停止操作线程"""
        if not self.operation_thread or not self.operation_thread.is_alive():
            self.log("操作线程未运行")
            return
        
        self.operation_running = False
        self.operation_thread.join(timeout=5)
        self.log("操作线程已停止")
    
    def _operation_loop(self) -> None:
        """操作线程循环"""
        self.log("操作线程循环开始")
        
        while self.operation_running:
            try:
                # 从操作队列中获取操作
                operation, args, kwargs, result_id = self.operation_queue.get(timeout=1)
                
                # 执行操作
                try:
                    # 检查连接状态
                    if not self.check_connection():
                        # 如果连接异常，将错误放入结果队列
                        self.result_queue.put((result_id, None, Exception("MT4连接异常")))
                        continue
                    
                    # 执行操作
                    result = operation(*args, **kwargs)
                    
                    # 将结果放入结果队列
                    self.result_queue.put((result_id, result, None))
                except Exception as e:
                    # 将异常放入结果队列
                    self.result_queue.put((result_id, None, e))
                
                # 标记任务完成
                self.operation_queue.task_done()
            except queue.Empty:
                # 队列为空，继续等待
                pass
            except Exception as e:
                self.log(f"操作线程出错: {e}")
                traceback.print_exc()
        
        self.log("操作线程循环结束")
    
    def execute_operation(self, operation: Callable, *args, **kwargs) -> Tuple[Any, Optional[Exception]]:
        """
        执行MT4操作，支持重试
        
        Args:
            operation: 要执行的操作函数
            *args: 操作函数的位置参数
            **kwargs: 操作函数的关键字参数
        
        Returns:
            Tuple[Any, Optional[Exception]]: (操作结果, 异常)，如果操作成功，异常为None
        """
        # 生成结果ID
        result_id = id(operation) + id(args) + id(time.time())
        
        # 将操作放入队列
        self.operation_queue.put((operation, args, kwargs, result_id))
        
        # 等待结果
        while True:
            try:
                # 从结果队列中获取结果
                queue_result_id, result, exception = self.result_queue.get(timeout=self.connection_timeout)
                
                # 检查结果ID是否匹配
                if queue_result_id == result_id:
                    # 标记任务完成
                    self.result_queue.task_done()
                    
                    # 返回结果
                    return result, exception
                else:
                    # 结果ID不匹配，放回队列
                    self.result_queue.put((queue_result_id, result, exception))
            except queue.Empty:
                # 超时，返回异常
                return None, Exception(f"操作超时: {operation.__name__}")
    
    def close(self) -> None:
        """关闭连接管理器"""
        self.log("关闭连接管理器...")
        
        # 停止心跳检测线程
        self.stop_heartbeat()
        
        # 停止操作线程
        self.stop_operation_thread()
        
        # 断开连接
        self.disconnect()
        
        self.log("连接管理器已关闭")

# 创建全局连接管理器实例
mt4_manager = MT4ConnectionManager(
    max_retries=3,
    retry_delay=2.0,
    connection_timeout=10.0,
    heartbeat_interval=60.0,
    auto_reconnect=True
)
