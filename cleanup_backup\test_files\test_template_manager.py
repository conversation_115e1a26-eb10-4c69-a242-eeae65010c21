"""
测试模板管理器
"""
import os
import sys
import traceback

# 添加当前目录到sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 打印当前工作目录和模板目录
print(f"当前工作目录: {os.getcwd()}")
template_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
print(f"模板目录: {template_dir}")
print(f"模板目录是否存在: {os.path.exists(template_dir)}")
if os.path.exists(template_dir):
    print(f"模板目录内容: {os.listdir(template_dir)}")

try:
    # 导入模板管理器
    from app.utils import prompt_template_manager

    # 列出所有模板
    print("所有模板:")
    templates = prompt_template_manager.list_templates()
    print(templates)

    # 测试渲染模板
    if 'final_analysis_template' in templates:
        print("\n测试渲染final_analysis_template:")
        template_data = {
            'symbol': 'EURUSD',
            'analysis_time': '2025-05-20 10:00:00',
            'current_price': '1.0850',
            'initial_insights': '初始分析结果...',
            'detail_insights': '详细分析结果...',
            'positions_data': '当前持仓数据...',
            'pending_orders_data': '当前挂单数据...'
        }

        try:
            prompt = prompt_template_manager.render_template('final_analysis_template', template_data)
            print(f"渲染成功，长度: {len(prompt)} 字符")
            print(prompt[:100] + "...")  # 只打印前100个字符
        except Exception as e:
            print(f"渲染失败: {e}")

    # 测试获取模板变量
    if 'final_analysis_template' in templates:
        print("\n测试获取模板变量:")
        variables = prompt_template_manager.get_template_variables('final_analysis_template')
        print(variables)

except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
