"""
最基本的ZeroMQ测试脚本
"""
import zmq
import time
import sys
from datetime import datetime

print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 脚本开始执行')
sys.stdout.flush()
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Python版本: {sys.version}')
sys.stdout.flush()
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ZeroMQ版本: {zmq.__version__}')
sys.stdout.flush()

# 测试ZeroMQ是否可以创建上下文和套接字
try:
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 创建ZeroMQ上下文')
    context = zmq.Context()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 上下文创建成功')

    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 创建REQ套接字')
    socket = context.socket(zmq.REQ)
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] REQ套接字创建成功')

    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 关闭套接字')
    socket.close()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 套接字关闭成功')

    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 终止上下文')
    context.term()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 上下文终止成功')

    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ZeroMQ基本功能测试成功')
except Exception as e:
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ZeroMQ测试失败: {e}')

print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 脚本结束执行')
