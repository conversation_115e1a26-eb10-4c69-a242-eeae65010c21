#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级交易系统 - 核心分析引擎
基于现代量化交易理念，融合多维度信号，实现自适应交易决策
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
# import talib  # 暂时注释，使用自定义实现

class MarketPhase(Enum):
    """市场阶段"""
    ACCUMULATION = "吸筹阶段"      # 底部整理，机构建仓
    MARKUP = "上升阶段"           # 明确上涨趋势
    DISTRIBUTION = "派发阶段"      # 顶部整理，机构出货
    MARKDOWN = "下跌阶段"         # 明确下跌趋势
    CONSOLIDATION = "整理阶段"     # 横盘震荡
    BREAKOUT = "突破阶段"         # 关键位置突破

class SignalStrength(Enum):
    """信号强度"""
    VERY_STRONG = 5
    STRONG = 4
    MODERATE = 3
    WEAK = 2
    VERY_WEAK = 1

@dataclass
class MarketStructure:
    """市场结构分析"""
    trend_direction: str           # UP, DOWN, SIDEWAYS
    trend_strength: float          # 0-1
    support_levels: List[float]    # 支撑位
    resistance_levels: List[float] # 阻力位
    key_level_strength: Dict       # 关键位强度
    market_phase: MarketPhase      # 市场阶段
    volatility_regime: str         # LOW, NORMAL, HIGH, EXTREME
    liquidity_profile: Dict        # 流动性分析

@dataclass
class TechnicalSignals:
    """技术信号集合"""
    trend_signals: Dict            # 趋势信号
    momentum_signals: Dict         # 动量信号
    volume_signals: Dict           # 成交量信号
    pattern_signals: Dict          # 形态信号
    oscillator_signals: Dict       # 震荡指标信号
    composite_score: float         # 综合技术评分

@dataclass
class FundamentalContext:
    """基本面背景"""
    economic_calendar: List[Dict]  # 经济日历
    news_sentiment: float          # 新闻情绪 -1到1
    central_bank_stance: str       # 央行立场
    risk_sentiment: str            # 风险情绪
    correlation_analysis: Dict     # 相关性分析

class AdvancedMarketAnalyzer:
    """高级市场分析器"""

    def __init__(self):
        self.lookback_periods = {
            'short': 20,    # 短期
            'medium': 50,   # 中期
            'long': 200     # 长期
        }

        # 技术指标参数
        self.indicator_params = {
            'rsi_period': 14,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'bb_period': 20,
            'bb_std': 2,
            'atr_period': 14,
            'adx_period': 14
        }

    def analyze_market_structure(self, price_data: pd.DataFrame) -> MarketStructure:
        """分析市场结构"""

        # 1. 趋势分析 - 多时间框架
        trend_analysis = self._analyze_trend_structure(price_data)

        # 2. 支撑阻力位识别
        sr_levels = self._identify_support_resistance(price_data)

        # 3. 市场阶段识别
        market_phase = self._identify_market_phase(price_data, trend_analysis)

        # 4. 波动率状态
        volatility_regime = self._analyze_volatility_regime(price_data)

        # 5. 流动性分析
        liquidity_profile = self._analyze_liquidity(price_data)

        return MarketStructure(
            trend_direction=trend_analysis['direction'],
            trend_strength=trend_analysis['strength'],
            support_levels=sr_levels['support'],
            resistance_levels=sr_levels['resistance'],
            key_level_strength=sr_levels['strength'],
            market_phase=market_phase,
            volatility_regime=volatility_regime,
            liquidity_profile=liquidity_profile
        )

    def _analyze_trend_structure(self, df: pd.DataFrame) -> Dict:
        """分析趋势结构"""
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values

        # 多重移动平均线分析
        ma_short = talib.SMA(close, timeperiod=20)
        ma_medium = talib.SMA(close, timeperiod=50)
        ma_long = talib.SMA(close, timeperiod=200)

        # ADX趋势强度
        adx = talib.ADX(high, low, close, timeperiod=self.indicator_params['adx_period'])

        # 价格相对于均线的位置
        current_price = close[-1]
        ma_alignment_score = 0

        # 均线排列评分
        if ma_short[-1] > ma_medium[-1] > ma_long[-1]:
            ma_alignment_score = 1  # 多头排列
        elif ma_short[-1] < ma_medium[-1] < ma_long[-1]:
            ma_alignment_score = -1  # 空头排列
        else:
            ma_alignment_score = 0  # 混乱排列

        # 价格突破分析
        price_vs_ma = 0
        if current_price > ma_long[-1]:
            price_vs_ma += 0.5
        if current_price > ma_medium[-1]:
            price_vs_ma += 0.3
        if current_price > ma_short[-1]:
            price_vs_ma += 0.2

        # 综合趋势强度
        trend_strength = (adx[-1] / 100 + abs(ma_alignment_score) + price_vs_ma) / 3

        # 趋势方向
        if ma_alignment_score > 0 and current_price > ma_long[-1]:
            direction = "UP"
        elif ma_alignment_score < 0 and current_price < ma_long[-1]:
            direction = "DOWN"
        else:
            direction = "SIDEWAYS"

        return {
            'direction': direction,
            'strength': min(trend_strength, 1.0),
            'adx': adx[-1],
            'ma_alignment': ma_alignment_score,
            'price_vs_ma': price_vs_ma
        }

    def _identify_support_resistance(self, df: pd.DataFrame) -> Dict:
        """识别支撑阻力位"""
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values

        # 使用局部极值识别关键位
        support_levels = []
        resistance_levels = []

        # 寻找局部最低点（支撑）
        for i in range(5, len(low) - 5):
            if all(low[i] <= low[i-j] for j in range(1, 6)) and \
               all(low[i] <= low[i+j] for j in range(1, 6)):
                support_levels.append(low[i])

        # 寻找局部最高点（阻力）
        for i in range(5, len(high) - 5):
            if all(high[i] >= high[i-j] for j in range(1, 6)) and \
               all(high[i] >= high[i+j] for j in range(1, 6)):
                resistance_levels.append(high[i])

        # 去重并排序
        current_price = close[-1]
        support_levels = sorted(list(set(support_levels)))
        resistance_levels = sorted(list(set(resistance_levels)))

        # 过滤：只保留当前价格附近的关键位
        nearby_supports = [s for s in support_levels if s < current_price and (current_price - s) / current_price < 0.02]
        nearby_resistances = [r for r in resistance_levels if r > current_price and (r - current_price) / current_price < 0.02]

        # 计算关键位强度
        strength_analysis = self._calculate_level_strength(df, nearby_supports + nearby_resistances)

        return {
            'support': nearby_supports[-3:] if len(nearby_supports) >= 3 else nearby_supports,
            'resistance': nearby_resistances[:3] if len(nearby_resistances) >= 3 else nearby_resistances,
            'strength': strength_analysis
        }

    def _calculate_level_strength(self, df: pd.DataFrame, levels: List[float]) -> Dict:
        """计算关键位强度"""
        strength_scores = {}

        for level in levels:
            # 计算价格在该水平附近的触及次数
            touches = 0
            bounces = 0

            for i in range(len(df)):
                price_range = (df.iloc[i]['high'] - df.iloc[i]['low']) / df.iloc[i]['close']
                tolerance = price_range * 2  # 动态容忍度

                if abs(df.iloc[i]['low'] - level) <= tolerance or abs(df.iloc[i]['high'] - level) <= tolerance:
                    touches += 1

                    # 检查是否有反弹/回调
                    if i < len(df) - 1:
                        next_close = df.iloc[i+1]['close']
                        current_close = df.iloc[i]['close']

                        if level < current_close and next_close > current_close:  # 支撑反弹
                            bounces += 1
                        elif level > current_close and next_close < current_close:  # 阻力回调
                            bounces += 1

            # 强度评分：触及次数 + 有效反弹率
            if touches > 0:
                bounce_rate = bounces / touches
                strength_scores[level] = min(touches * 0.3 + bounce_rate * 0.7, 1.0)
            else:
                strength_scores[level] = 0

        return strength_scores

    def _identify_market_phase(self, df: pd.DataFrame, trend_analysis: Dict) -> MarketPhase:
        """识别市场阶段"""
        close = df['close'].values
        volume = df.get('volume', pd.Series([1] * len(df))).values

        # 价格变化率
        price_change = (close[-1] - close[-20]) / close[-20]

        # 成交量趋势
        volume_ma_short = np.mean(volume[-10:])
        volume_ma_long = np.mean(volume[-30:])
        volume_trend = (volume_ma_short - volume_ma_long) / volume_ma_long

        trend_direction = trend_analysis['direction']
        trend_strength = trend_analysis['strength']

        # 阶段判断逻辑
        if trend_direction == "UP" and trend_strength > 0.6:
            if volume_trend > 0.1:
                return MarketPhase.MARKUP  # 上升阶段，成交量配合
            else:
                return MarketPhase.DISTRIBUTION  # 可能的派发阶段

        elif trend_direction == "DOWN" and trend_strength > 0.6:
            if volume_trend > 0.1:
                return MarketPhase.MARKDOWN  # 下跌阶段，成交量配合
            else:
                return MarketPhase.ACCUMULATION  # 可能的吸筹阶段

        elif abs(price_change) < 0.02:  # 价格变化小于2%
            if trend_strength < 0.3:
                return MarketPhase.CONSOLIDATION  # 整理阶段
            else:
                return MarketPhase.BREAKOUT  # 可能的突破前夕

        else:
            return MarketPhase.CONSOLIDATION  # 默认整理阶段

    def _analyze_volatility_regime(self, df: pd.DataFrame) -> str:
        """分析波动率状态"""
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values

        # 计算ATR
        atr = talib.ATR(high, low, close, timeperiod=self.indicator_params['atr_period'])
        current_atr = atr[-1]
        atr_ma = np.mean(atr[-50:])  # 50期ATR均值

        # 波动率比率
        volatility_ratio = current_atr / atr_ma if atr_ma > 0 else 1

        # 分类波动率状态
        if volatility_ratio > 2.0:
            return "EXTREME"
        elif volatility_ratio > 1.5:
            return "HIGH"
        elif volatility_ratio < 0.7:
            return "LOW"
        else:
            return "NORMAL"

    def _analyze_liquidity(self, df: pd.DataFrame) -> Dict:
        """分析流动性"""
        # 简化的流动性分析
        # 实际实现需要更多数据（订单簿、tick数据等）

        close = df['close'].values
        volume = df.get('volume', pd.Series([1] * len(df))).values

        # 价格影响分析（简化版）
        price_changes = np.diff(close) / close[:-1]
        volume_changes = np.diff(volume) / volume[:-1]

        # 流动性评分（价格变化与成交量变化的相关性）
        if len(price_changes) > 10:
            correlation = np.corrcoef(abs(price_changes[-20:]), volume_changes[-20:])[0, 1]
            liquidity_score = 1 - abs(correlation) if not np.isnan(correlation) else 0.5
        else:
            liquidity_score = 0.5

        return {
            'score': liquidity_score,
            'avg_volume': np.mean(volume[-20:]),
            'volume_volatility': np.std(volume[-20:]) / np.mean(volume[-20:]) if np.mean(volume[-20:]) > 0 else 0
        }

    def generate_technical_signals(self, df: pd.DataFrame) -> TechnicalSignals:
        """生成技术信号"""
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        volume = df.get('volume', pd.Series([1] * len(df))).values

        # 1. 趋势信号
        trend_signals = self._generate_trend_signals(close, high, low)

        # 2. 动量信号
        momentum_signals = self._generate_momentum_signals(close, high, low)

        # 3. 成交量信号
        volume_signals = self._generate_volume_signals(close, volume)

        # 4. 形态信号
        pattern_signals = self._generate_pattern_signals(df)

        # 5. 震荡指标信号
        oscillator_signals = self._generate_oscillator_signals(close, high, low)

        # 6. 综合评分
        composite_score = self._calculate_composite_score(
            trend_signals, momentum_signals, volume_signals,
            pattern_signals, oscillator_signals
        )

        return TechnicalSignals(
            trend_signals=trend_signals,
            momentum_signals=momentum_signals,
            volume_signals=volume_signals,
            pattern_signals=pattern_signals,
            oscillator_signals=oscillator_signals,
            composite_score=composite_score
        )

    def _generate_trend_signals(self, close: np.ndarray, high: np.ndarray, low: np.ndarray) -> Dict:
        """生成趋势信号"""
        signals = {}

        # EMA交叉系统
        ema_fast = talib.EMA(close, timeperiod=12)
        ema_slow = talib.EMA(close, timeperiod=26)

        # MACD
        macd, macd_signal, macd_hist = talib.MACD(close,
                                                  fastperiod=self.indicator_params['macd_fast'],
                                                  slowperiod=self.indicator_params['macd_slow'],
                                                  signalperiod=self.indicator_params['macd_signal'])

        # Parabolic SAR
        sar = talib.SAR(high, low, acceleration=0.02, maximum=0.2)

        # 信号评分
        signals['ema_cross'] = 1 if ema_fast[-1] > ema_slow[-1] else -1
        signals['macd_signal'] = 1 if macd[-1] > macd_signal[-1] else -1
        signals['sar_signal'] = 1 if close[-1] > sar[-1] else -1

        # 趋势强度
        signals['trend_strength'] = (signals['ema_cross'] + signals['macd_signal'] + signals['sar_signal']) / 3

        return signals

    def _generate_momentum_signals(self, close: np.ndarray, high: np.ndarray, low: np.ndarray) -> Dict:
        """生成动量信号"""
        signals = {}

        # RSI
        rsi = talib.RSI(close, timeperiod=self.indicator_params['rsi_period'])

        # Stochastic
        slowk, slowd = talib.STOCH(high, low, close, fastk_period=14, slowk_period=3, slowd_period=3)

        # Williams %R
        willr = talib.WILLR(high, low, close, timeperiod=14)

        # 动量评分
        signals['rsi'] = rsi[-1]
        signals['rsi_signal'] = 1 if 30 < rsi[-1] < 70 else 0  # 中性区间为好信号
        signals['stoch_signal'] = 1 if slowk[-1] > slowd[-1] else -1
        signals['willr_signal'] = 1 if willr[-1] > -50 else -1

        # 超买超卖状态
        signals['overbought'] = rsi[-1] > 70 or slowk[-1] > 80
        signals['oversold'] = rsi[-1] < 30 or slowk[-1] < 20

        return signals

    def _generate_volume_signals(self, close: np.ndarray, volume: np.ndarray) -> Dict:
        """生成成交量信号"""
        signals = {}

        # 成交量移动平均
        volume_ma = talib.SMA(volume.astype(float), timeperiod=20)

        # OBV (On Balance Volume)
        obv = talib.OBV(close, volume.astype(float))

        # 成交量信号
        signals['volume_trend'] = 1 if volume[-1] > volume_ma[-1] else -1
        signals['obv_trend'] = 1 if obv[-1] > obv[-5] else -1

        # 量价配合度
        price_change = (close[-1] - close[-2]) / close[-2]
        volume_change = (volume[-1] - volume[-2]) / volume[-2] if volume[-2] > 0 else 0

        signals['price_volume_sync'] = 1 if (price_change > 0 and volume_change > 0) or (price_change < 0 and volume_change > 0) else 0

        return signals

    def _generate_pattern_signals(self, df: pd.DataFrame) -> Dict:
        """生成形态信号"""
        signals = {}

        # 使用talib的形态识别
        open_prices = df['open'].values
        high_prices = df['high'].values
        low_prices = df['low'].values
        close_prices = df['close'].values

        # 常见形态
        hammer = talib.CDLHAMMER(open_prices, high_prices, low_prices, close_prices)
        doji = talib.CDLDOJI(open_prices, high_prices, low_prices, close_prices)
        engulfing = talib.CDLENGULFING(open_prices, high_prices, low_prices, close_prices)

        signals['hammer'] = 1 if hammer[-1] > 0 else 0
        signals['doji'] = 1 if doji[-1] != 0 else 0
        signals['engulfing'] = 1 if engulfing[-1] > 0 else (-1 if engulfing[-1] < 0 else 0)

        return signals

    def _generate_oscillator_signals(self, close: np.ndarray, high: np.ndarray, low: np.ndarray) -> Dict:
        """生成震荡指标信号"""
        signals = {}

        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = talib.BBANDS(close,
                                                     timeperiod=self.indicator_params['bb_period'],
                                                     nbdevup=self.indicator_params['bb_std'],
                                                     nbdevdn=self.indicator_params['bb_std'])

        # CCI (Commodity Channel Index)
        cci = talib.CCI(high, low, close, timeperiod=20)

        # Bollinger Bands信号
        bb_position = (close[-1] - bb_lower[-1]) / (bb_upper[-1] - bb_lower[-1])
        signals['bb_position'] = bb_position
        signals['bb_signal'] = 1 if bb_position > 0.8 else (-1 if bb_position < 0.2 else 0)

        # CCI信号
        signals['cci'] = cci[-1]
        signals['cci_signal'] = 1 if -100 < cci[-1] < 100 else 0

        return signals

    def _calculate_composite_score(self, trend_signals: Dict, momentum_signals: Dict,
                                 volume_signals: Dict, pattern_signals: Dict,
                                 oscillator_signals: Dict) -> float:
        """计算综合技术评分"""

        # 权重分配
        weights = {
            'trend': 0.35,
            'momentum': 0.25,
            'volume': 0.20,
            'pattern': 0.10,
            'oscillator': 0.10
        }

        # 趋势评分
        trend_score = trend_signals.get('trend_strength', 0)

        # 动量评分
        momentum_score = (momentum_signals.get('rsi_signal', 0) +
                         momentum_signals.get('stoch_signal', 0) +
                         momentum_signals.get('willr_signal', 0)) / 3

        # 成交量评分
        volume_score = (volume_signals.get('volume_trend', 0) +
                       volume_signals.get('obv_trend', 0) +
                       volume_signals.get('price_volume_sync', 0)) / 3

        # 形态评分
        pattern_score = (pattern_signals.get('hammer', 0) +
                        pattern_signals.get('engulfing', 0)) / 2

        # 震荡指标评分
        oscillator_score = (oscillator_signals.get('bb_signal', 0) +
                           oscillator_signals.get('cci_signal', 0)) / 2

        # 加权综合评分
        composite_score = (trend_score * weights['trend'] +
                          momentum_score * weights['momentum'] +
                          volume_score * weights['volume'] +
                          pattern_score * weights['pattern'] +
                          oscillator_score * weights['oscillator'])

        return composite_score

# 使用示例
def test_advanced_analyzer():
    """测试高级分析器"""
    # 创建模拟数据
    dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
    np.random.seed(42)

    # 生成模拟价格数据
    base_price = 1.1000
    price_changes = np.random.normal(0, 0.0005, 100)
    prices = [base_price]

    for change in price_changes:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)

    prices = prices[1:]  # 移除初始价格

    # 创建OHLC数据
    df = pd.DataFrame({
        'datetime': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.0002))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.0002))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000, 10000, 100)
    })

    # 分析
    analyzer = AdvancedMarketAnalyzer()

    print("🔍 高级市场分析测试")
    print("=" * 50)

    # 市场结构分析
    market_structure = analyzer.analyze_market_structure(df)
    print(f"趋势方向: {market_structure.trend_direction}")
    print(f"趋势强度: {market_structure.trend_strength:.2f}")
    print(f"市场阶段: {market_structure.market_phase.value}")
    print(f"波动率状态: {market_structure.volatility_regime}")
    print(f"支撑位数量: {len(market_structure.support_levels)}")
    print(f"阻力位数量: {len(market_structure.resistance_levels)}")

    # 技术信号分析
    technical_signals = analyzer.generate_technical_signals(df)
    print(f"\n📊 技术信号综合评分: {technical_signals.composite_score:.2f}")
    print(f"趋势信号强度: {technical_signals.trend_signals.get('trend_strength', 0):.2f}")
    print(f"当前RSI: {technical_signals.momentum_signals.get('rsi', 50):.1f}")

    return market_structure, technical_signals

if __name__ == "__main__":
    test_advanced_analyzer()
