"""
测试预分析功能
模拟真实的预分析过程，检查LLM返回结果和解析结果
"""
import os
import sys
import json
import traceback
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'

from app.utils.multi_round_analysis import should_perform_analysis
from app.utils import llm_client

def create_test_data():
    """创建测试数据"""
    # 模拟真实的市场数据
    data = {
        'symbol': 'EURUSD',
        'timeframe15m': [
            {
                'time': '2023-05-12 19:45:00',
                'open': 1.08500,
                'high': 1.08550,
                'low': 1.08480,
                'close': 1.08520,
                'volume': 1200
            },
            {
                'time': '2023-05-12 20:00:00',
                'open': 1.08520,
                'high': 1.08600,
                'low': 1.08510,
                'close': 1.08580,
                'volume': 1500
            }
        ],
        'indicators': {
            '15min': {
                'rsi': 55.2,
                'macd': {
                    'macdLine': [0.0002, 0.0003],
                    'signalLine': [0.0001, 0.0002],
                    'histogram': [0.0001, 0.0001]
                },
                'ma': {
                    5: 1.08500,
                    10: 1.08450,
                    13: 1.08420,
                    '13_direction': 'DOWN',
                    '13_slope': -0.0002,
                    '13_distance': 0.0016,
                    '13_price_position': 'ABOVE',
                    '13_speed': -0.0001,
                    '13_projection_2h': 1.08400,
                    20: 1.08400,
                    50: 1.08300
                },
                'bollinger': {
                    'upper': [1.08700],
                    'middle': [1.08500],
                    'lower': [1.08300]
                }
            },
            '1h': {
                'rsi': 52.1,
                'macd': {
                    'macdLine': [0.0001, 0.0002],
                    'signalLine': [0.0001, 0.0001],
                    'histogram': [0.0000, 0.0001]
                },
                'ma': {
                    5: 1.08550,
                    10: 1.08500,
                    13: 1.08480,
                    '13_direction': 'FLAT',
                    '13_slope': 0.0001,
                    '13_distance': 0.0010,
                    '13_price_position': 'ABOVE',
                    '13_speed': 0.0000,
                    '13_projection_2h': 1.08480,
                    20: 1.08450,
                    50: 1.08350
                },
                'bollinger': {
                    'upper': [1.08750],
                    'middle': [1.08550],
                    'lower': [1.08350]
                }
            },
            'rsi': 55.2,
            'macd': {
                'macdLine': [0.0002, 0.0003],
                'signalLine': [0.0001, 0.0002],
                'histogram': [0.0001, 0.0001]
            },
            'ma': {
                5: 1.08500,
                10: 1.08450,
                13: 1.08420,
                20: 1.08400,
                50: 1.08300
            },
            'bollinger': {
                'upper': [1.08700],
                'middle': [1.08500],
                'lower': [1.08300]
            }
        },
        'positions': []
    }
    return data

def test_preanalysis():
    """测试预分析功能"""
    try:
        print("=" * 50)
        print("测试预分析功能")
        print("=" * 50)
        print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 创建测试数据
        data = create_test_data()
        print(f"测试数据创建完成")

        # 打印13日均线数据
        print("\n13日均线数据:")
        print("15分钟图表:")
        ma13_15min = data['indicators']['15min']['ma']
        print(f"  均线值: {ma13_15min[13]}")
        print(f"  均线方向: {ma13_15min['13_direction']}")
        print(f"  均线斜率: {ma13_15min['13_slope']}")
        print(f"  价格位置: {ma13_15min['13_price_position']}")
        print(f"  价格距离: {ma13_15min['13_distance']}")
        print(f"  预测位置: {ma13_15min['13_projection_2h']}")

        print("\n1小时图表:")
        ma13_1h = data['indicators']['1h']['ma']
        print(f"  均线值: {ma13_1h[13]}")
        print(f"  均线方向: {ma13_1h['13_direction']}")
        print(f"  均线斜率: {ma13_1h['13_slope']}")
        print(f"  价格位置: {ma13_1h['13_price_position']}")
        print(f"  价格距离: {ma13_1h['13_distance']}")
        print(f"  预测位置: {ma13_1h['13_projection_2h']}")

        # 确保输出立即显示
        sys.stdout.flush()

        # 执行预分析
        print("\n开始执行预分析...")
        try:
            should_analyze, reason, _ = should_perform_analysis(data)  # 使用_忽略未使用的返回值

            # 输出结果
            print("\n预分析结果:")
            print(f"是否需要分析: {should_analyze}")
            print(f"原因: {reason}")
            # 移除下次分析间隔显示，因为该功能未实际实现
        except Exception as e:
            print(f"\n预分析执行失败: {e}")
            traceback.print_exc()

        print("\n测试完成")
    except Exception as e:
        print(f"测试过程出错: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_preanalysis()
