#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易逻辑实时监听器
专门监听交易决策、订单执行、策略逻辑是否符合设计
"""

import sys
import os
import logging
import time
import json
import threading
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum
import requests

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'QuantumForex_Pro'))

class TradingAlertLevel(Enum):
    """交易警告级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class TradingAlert:
    """交易警告"""
    timestamp: datetime
    alert_type: str
    level: TradingAlertLevel
    symbol: str
    message: str
    details: Dict = None

@dataclass
class OrderInfo:
    """订单信息"""
    order_id: str
    symbol: str
    action: str  # BUY/SELL
    volume: float
    entry_price: float
    stop_loss: float
    take_profit: float
    timestamp: datetime
    status: str

class TradingLogicMonitor:
    """交易逻辑监听器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 监听状态
        self.is_monitoring = False
        self.monitor_thread = None

        # 交易数据缓存
        self.current_orders: Dict[str, OrderInfo] = {}
        self.order_history: List[OrderInfo] = []
        self.alerts: List[TradingAlert] = []

        # 监听配置
        self.config = {
            'check_interval': 5,  # 5秒检查一次
            'mt4_api_url': 'http://127.0.0.1:8081/api',
            'max_alerts_history': 500,
            'duplicate_price_threshold': 0.0020,  # 20点价格差异阈值
            'max_orders_per_symbol': 2,
            'max_same_direction_orders': 1,
            'max_total_orders': 10
        }

        # 交易逻辑规则
        self.trading_rules = {
            'duplicate_order_check': True,
            'position_limit_check': True,
            'risk_management_check': True,
            'strategy_consistency_check': True,
            'execution_time_check': True
        }

        # 统计数据
        self.stats = {
            'total_orders_detected': 0,
            'duplicate_orders_detected': 0,
            'rule_violations': 0,
            'successful_trades': 0,
            'failed_trades': 0
        }

        # 上次检查的状态
        self.last_check_time = None
        self.last_order_count = 0

    def start_monitoring(self):
        """启动交易逻辑监听"""
        try:
            self.logger.info("🚀 启动交易逻辑实时监听器")
            self.logger.info("🎯 专门监听交易决策和订单执行逻辑")

            self.is_monitoring = True
            self.last_check_time = datetime.now()

            # 启动监听线程
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()

            self.logger.info("✅ 交易逻辑监听器启动成功")
            self.logger.info("📊 监听内容:")
            self.logger.info("   - 实时订单创建/修改/平仓")
            self.logger.info("   - 重复订单检测")
            self.logger.info("   - 持仓限制检查")
            self.logger.info("   - 策略执行一致性")
            self.logger.info("   - 风险管理合规性")
            self.logger.info("   - 异常交易行为检测")

        except Exception as e:
            self.logger.error(f"❌ 启动交易逻辑监听器失败: {e}")

    def stop_monitoring(self):
        """停止监听"""
        try:
            self.logger.info("🛑 停止交易逻辑监听器")
            self.is_monitoring = False

            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=10)

            # 生成监听报告
            self._generate_trading_report()

            self.logger.info("✅ 交易逻辑监听器已停止")

        except Exception as e:
            self.logger.error(f"❌ 停止监听器失败: {e}")

    def _monitor_loop(self):
        """监听主循环"""
        while self.is_monitoring:
            try:
                # 获取当前订单状态
                current_orders = self._get_current_orders()

                if current_orders is not None:
                    # 检测订单变化
                    self._detect_order_changes(current_orders)

                    # 执行各种检查
                    self._check_duplicate_orders(current_orders)
                    self._check_position_limits(current_orders)
                    self._check_risk_management(current_orders)
                    self._check_strategy_consistency(current_orders)

                    # 更新统计
                    self._update_statistics(current_orders)

                    # 输出监听状态
                    if len(current_orders) != self.last_order_count:
                        self._log_order_status(current_orders)
                        self.last_order_count = len(current_orders)

                time.sleep(self.config['check_interval'])

            except Exception as e:
                self._add_alert("监听异常", TradingAlertLevel.ERROR, "",
                              f"监听循环异常: {e}")
                time.sleep(30)  # 异常时等待更长时间

    def _get_current_orders(self) -> Optional[List[Dict]]:
        """获取当前订单"""
        try:
            # 尝试从MT4 API获取订单
            response = requests.get(f"{self.config['mt4_api_url']}/orders", timeout=5)
            if response.status_code == 200:
                return response.json().get('orders', [])

            # 如果API不可用，尝试直接连接MT4
            from utils.mt4_client import MT4Client
            mt4_client = MT4Client()

            if mt4_client.connect():
                orders_data = mt4_client.get_active_orders()
                if orders_data and orders_data.get('status') == 'success':
                    return orders_data.get('orders', [])

            return []

        except Exception as e:
            self.logger.debug(f"获取订单失败: {e}")
            return None

    def _detect_order_changes(self, current_orders: List[Dict]):
        """检测订单变化"""
        try:
            current_order_ids = {order.get('order_id', '') for order in current_orders}
            previous_order_ids = set(self.current_orders.keys())

            # 检测新订单
            new_orders = current_order_ids - previous_order_ids
            for order_id in new_orders:
                order_data = next((o for o in current_orders if o.get('order_id') == order_id), None)
                if order_data:
                    self._on_new_order(order_data)

            # 检测平仓订单
            closed_orders = previous_order_ids - current_order_ids
            for order_id in closed_orders:
                self._on_order_closed(order_id)

            # 更新当前订单缓存
            self.current_orders = {}
            for order in current_orders:
                order_id = order.get('order_id', '')
                if order_id:
                    self.current_orders[order_id] = OrderInfo(
                        order_id=order_id,
                        symbol=order.get('symbol', ''),
                        action=order.get('action', ''),
                        volume=order.get('volume', 0),
                        entry_price=order.get('entry_price', 0),
                        stop_loss=order.get('stop_loss', 0),
                        take_profit=order.get('take_profit', 0),
                        timestamp=datetime.now(),
                        status=order.get('status', 'active')
                    )

        except Exception as e:
            self._add_alert("订单变化检测", TradingAlertLevel.ERROR, "",
                          f"检测订单变化失败: {e}")

    def _on_new_order(self, order_data: Dict):
        """处理新订单"""
        try:
            symbol = order_data.get('symbol', '')
            action = order_data.get('action', '')
            volume = order_data.get('volume', 0)
            entry_price = order_data.get('entry_price', 0)
            order_id = order_data.get('order_id', '')

            self.logger.info(f"🆕 检测到新订单: {symbol} {action} {volume}手 @ {entry_price} (ID: {order_id})")

            self.stats['total_orders_detected'] += 1

            # 立即检查新订单是否违反规则
            self._check_new_order_rules(order_data)

        except Exception as e:
            self._add_alert("新订单处理", TradingAlertLevel.ERROR, "",
                          f"处理新订单失败: {e}")

    def _on_order_closed(self, order_id: str):
        """处理订单平仓"""
        try:
            if order_id in self.current_orders:
                order = self.current_orders[order_id]
                self.logger.info(f"🔚 检测到订单平仓: {order.symbol} {order.action} (ID: {order_id})")

                # 移动到历史记录
                self.order_history.append(order)

                # 限制历史记录数量
                if len(self.order_history) > 1000:
                    self.order_history = self.order_history[-1000:]

        except Exception as e:
            self._add_alert("订单平仓处理", TradingAlertLevel.ERROR, "",
                          f"处理订单平仓失败: {e}")

    def _check_new_order_rules(self, order_data: Dict):
        """检查新订单是否违反规则"""
        try:
            symbol = order_data.get('symbol', '')
            action = order_data.get('action', '')
            entry_price = order_data.get('entry_price', 0)

            # 检查是否有相似价格的订单
            similar_orders = []
            for existing_order in self.current_orders.values():
                if existing_order.symbol == symbol:
                    price_diff = abs(existing_order.entry_price - entry_price)
                    if price_diff <= self.config['duplicate_price_threshold']:
                        similar_orders.append(existing_order)

            if similar_orders:
                self._add_alert("重复订单检测", TradingAlertLevel.WARNING, symbol,
                              f"检测到相似价格订单: 新订单@{entry_price}, 已有{len(similar_orders)}个相似订单")
                self.stats['duplicate_orders_detected'] += 1

        except Exception as e:
            self._add_alert("新订单规则检查", TradingAlertLevel.ERROR, "",
                          f"检查新订单规则失败: {e}")

    def _check_duplicate_orders(self, current_orders: List[Dict]):
        """检查重复订单"""
        if not self.trading_rules['duplicate_order_check']:
            return

        try:
            # 按货币对分组检查
            symbol_orders = {}
            for order in current_orders:
                symbol = order.get('symbol', '')
                if symbol not in symbol_orders:
                    symbol_orders[symbol] = []
                symbol_orders[symbol].append(order)

            for symbol, orders in symbol_orders.items():
                if len(orders) > 1:
                    # 检查价格相似性
                    for i, order1 in enumerate(orders):
                        for order2 in orders[i+1:]:
                            price_diff = abs(order1.get('entry_price', 0) - order2.get('entry_price', 0))
                            if price_diff <= self.config['duplicate_price_threshold']:
                                self._add_alert("重复订单", TradingAlertLevel.WARNING, symbol,
                                              f"发现相似价格订单: {price_diff:.5f}点差异")

        except Exception as e:
            self._add_alert("重复订单检查", TradingAlertLevel.ERROR, "",
                          f"检查重复订单失败: {e}")

    def _check_position_limits(self, current_orders: List[Dict]):
        """检查持仓限制"""
        if not self.trading_rules['position_limit_check']:
            return

        try:
            # 检查总订单数量
            if len(current_orders) > self.config['max_total_orders']:
                self._add_alert("持仓限制", TradingAlertLevel.ERROR, "",
                              f"总订单数量超限: {len(current_orders)} > {self.config['max_total_orders']}")
                self.stats['rule_violations'] += 1

            # 按货币对检查
            symbol_counts = {}
            symbol_directions = {}

            for order in current_orders:
                symbol = order.get('symbol', '')
                action = order.get('action', '')

                # 统计每个货币对的订单数量
                symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1

                # 统计每个货币对每个方向的订单数量
                if symbol not in symbol_directions:
                    symbol_directions[symbol] = {'BUY': 0, 'SELL': 0}
                symbol_directions[symbol][action] = symbol_directions[symbol].get(action, 0) + 1

            # 检查每个货币对的限制
            for symbol, count in symbol_counts.items():
                if count > self.config['max_orders_per_symbol']:
                    self._add_alert("持仓限制", TradingAlertLevel.WARNING, symbol,
                                  f"货币对订单数量超限: {count} > {self.config['max_orders_per_symbol']}")
                    self.stats['rule_violations'] += 1

            # 检查同方向订单限制
            for symbol, directions in symbol_directions.items():
                for direction, count in directions.items():
                    if count > self.config['max_same_direction_orders']:
                        self._add_alert("持仓限制", TradingAlertLevel.WARNING, symbol,
                                      f"{direction}方向订单数量超限: {count} > {self.config['max_same_direction_orders']}")
                        self.stats['rule_violations'] += 1

        except Exception as e:
            self._add_alert("持仓限制检查", TradingAlertLevel.ERROR, "",
                          f"检查持仓限制失败: {e}")

    def _check_risk_management(self, current_orders: List[Dict]):
        """检查风险管理"""
        if not self.trading_rules['risk_management_check']:
            return

        try:
            for order in current_orders:
                symbol = order.get('symbol', '')
                entry_price = order.get('entry_price', 0)
                stop_loss = order.get('stop_loss', 0)
                take_profit = order.get('take_profit', 0)
                volume = order.get('volume', 0)

                # 检查是否设置了止损
                if stop_loss == 0:
                    self._add_alert("风险管理", TradingAlertLevel.WARNING, symbol,
                                  f"订单未设置止损: {order.get('order_id', '')}")

                # 检查是否设置了止盈
                if take_profit == 0:
                    self._add_alert("风险管理", TradingAlertLevel.INFO, symbol,
                                  f"订单未设置止盈: {order.get('order_id', '')}")

                # 检查仓位大小是否合理
                if volume > 0.1:  # 超过0.1手
                    self._add_alert("风险管理", TradingAlertLevel.WARNING, symbol,
                                  f"仓位较大: {volume}手")

        except Exception as e:
            self._add_alert("风险管理检查", TradingAlertLevel.ERROR, "",
                          f"检查风险管理失败: {e}")

    def _check_strategy_consistency(self, current_orders: List[Dict]):
        """检查策略一致性"""
        if not self.trading_rules['strategy_consistency_check']:
            return

        try:
            # 检查订单的备注信息是否一致
            comments = [order.get('comment', '') for order in current_orders]
            unique_comments = set(comments)

            # 如果有多种不同的备注，可能表示策略不一致
            if len(unique_comments) > 3:
                self._add_alert("策略一致性", TradingAlertLevel.INFO, "",
                              f"检测到多种订单备注: {len(unique_comments)}种")

        except Exception as e:
            self._add_alert("策略一致性检查", TradingAlertLevel.ERROR, "",
                          f"检查策略一致性失败: {e}")

    def _update_statistics(self, current_orders: List[Dict]):
        """更新统计数据"""
        try:
            # 更新统计信息
            # 这里可以添加更多的统计逻辑
            pass

        except Exception as e:
            self.logger.debug(f"更新统计失败: {e}")

    def _log_order_status(self, current_orders: List[Dict]):
        """记录订单状态"""
        try:
            self.logger.info(f"📊 当前订单状态: {len(current_orders)}个活跃订单")

            # 按货币对统计
            symbol_counts = {}
            for order in current_orders:
                symbol = order.get('symbol', '')
                symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1

            for symbol, count in symbol_counts.items():
                self.logger.info(f"   {symbol}: {count}个订单")

        except Exception as e:
            self.logger.debug(f"记录订单状态失败: {e}")

    def _add_alert(self, alert_type: str, level: TradingAlertLevel, symbol: str, message: str, details: Dict = None):
        """添加交易警告"""
        try:
            alert = TradingAlert(
                timestamp=datetime.now(),
                alert_type=alert_type,
                level=level,
                symbol=symbol,
                message=message,
                details=details
            )

            self.alerts.append(alert)

            # 限制警告历史数量
            if len(self.alerts) > self.config['max_alerts_history']:
                self.alerts = self.alerts[-self.config['max_alerts_history']:]

            # 实时输出警告
            level_icons = {
                TradingAlertLevel.INFO: "ℹ️",
                TradingAlertLevel.WARNING: "⚠️",
                TradingAlertLevel.ERROR: "❌",
                TradingAlertLevel.CRITICAL: "🚨"
            }

            icon = level_icons.get(level, "📢")
            symbol_info = f"[{symbol}] " if symbol else ""
            self.logger.warning(f"{icon} {symbol_info}{alert_type}: {message}")

        except Exception as e:
            self.logger.error(f"添加警告失败: {e}")

    def _generate_trading_report(self):
        """生成交易监听报告"""
        try:
            # 统计警告
            total_alerts = len(self.alerts)
            critical_alerts = len([a for a in self.alerts if a.level == TradingAlertLevel.CRITICAL])
            error_alerts = len([a for a in self.alerts if a.level == TradingAlertLevel.ERROR])
            warning_alerts = len([a for a in self.alerts if a.level == TradingAlertLevel.WARNING])

            # 生成报告
            report = {
                'trading_monitoring_summary': {
                    'monitoring_duration': str(datetime.now() - self.last_check_time) if self.last_check_time else None,
                    'total_orders_detected': self.stats['total_orders_detected'],
                    'duplicate_orders_detected': self.stats['duplicate_orders_detected'],
                    'rule_violations': self.stats['rule_violations'],
                    'total_alerts': total_alerts,
                    'critical_alerts': critical_alerts,
                    'error_alerts': error_alerts,
                    'warning_alerts': warning_alerts
                },
                'recent_alerts': [
                    {
                        'timestamp': alert.timestamp.isoformat(),
                        'type': alert.alert_type,
                        'level': alert.level.value,
                        'symbol': alert.symbol,
                        'message': alert.message
                    }
                    for alert in self.alerts[-50:]  # 最近50条警告
                ]
            }

            # 保存报告
            report_file = f"trading_logic_monitoring_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            # 输出总结
            self.logger.info("=" * 80)
            self.logger.info("📊 交易逻辑监听报告")
            self.logger.info("=" * 80)
            self.logger.info(f"📈 检测到订单: {self.stats['total_orders_detected']}")
            self.logger.info(f"🔄 重复订单: {self.stats['duplicate_orders_detected']}")
            self.logger.info(f"⚠️ 规则违反: {self.stats['rule_violations']}")
            self.logger.info(f"🚨 严重警告: {critical_alerts}")
            self.logger.info(f"❌ 错误警告: {error_alerts}")
            self.logger.info(f"⚠️ 一般警告: {warning_alerts}")
            self.logger.info(f"📄 详细报告: {report_file}")

        except Exception as e:
            self.logger.error(f"❌ 生成交易监听报告失败: {e}")

    def get_current_status(self) -> Dict:
        """获取当前监听状态"""
        recent_alerts = self.alerts[-5:] if self.alerts else []

        return {
            'is_monitoring': self.is_monitoring,
            'current_orders_count': len(self.current_orders),
            'total_orders_detected': self.stats['total_orders_detected'],
            'duplicate_orders_detected': self.stats['duplicate_orders_detected'],
            'rule_violations': self.stats['rule_violations'],
            'total_alerts': len(self.alerts),
            'recent_alerts': [
                {
                    'timestamp': alert.timestamp.isoformat(),
                    'type': alert.alert_type,
                    'level': alert.level.value,
                    'symbol': alert.symbol,
                    'message': alert.message
                }
                for alert in recent_alerts
            ]
        }

def setup_logging():
    """设置日志"""
    # 创建自定义格式器，避免emoji编码问题
    class SafeFormatter(logging.Formatter):
        def format(self, record):
            # 移除emoji字符，只保留文本
            msg = super().format(record)
            # 简单的emoji替换
            emoji_map = {
                '🚀': '[START]',
                '🎯': '[TARGET]',
                '✅': '[OK]',
                '❌': '[ERROR]',
                '⚠️': '[WARN]',
                '🚨': '[ALERT]',
                '📊': '[DATA]',
                '🔍': '[SCAN]',
                '💡': '[TIP]',
                '🛑': '[STOP]',
                '🆕': '[NEW]',
                '🔚': '[END]',
                'ℹ️': '[INFO]'
            }
            for emoji, text in emoji_map.items():
                msg = msg.replace(emoji, text)
            return msg

    # 设置日志配置
    formatter = SafeFormatter('%(asctime)s - %(levelname)s - %(message)s')

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # 文件处理器
    file_handler = logging.FileHandler(f'trading_logic_monitor_{datetime.now().strftime("%Y%m%d")}.log', encoding='utf-8')
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

    # 配置根日志器
    logging.basicConfig(
        level=logging.INFO,
        handlers=[console_handler, file_handler]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("🚀 交易逻辑实时监听器")
    logger.info("📋 专门监听交易决策和订单执行逻辑")
    logger.info("🔍 实时检测重复订单、规则违反、异常行为")
    logger.info("=" * 80)

    # 创建监听器
    monitor = TradingLogicMonitor()

    try:
        # 启动监听
        monitor.start_monitoring()

        logger.info("🔍 交易逻辑监听进行中...")
        logger.info("💡 按 Ctrl+C 停止监听")

        # 定期输出状态
        while monitor.is_monitoring:
            time.sleep(30)  # 每30秒输出一次状态
            status = monitor.get_current_status()
            logger.info(f"📊 监听状态: 订单{status['current_orders_count']}个, 警告{status['total_alerts']}条, 违规{status['rule_violations']}次")

    except KeyboardInterrupt:
        logger.info("🛑 收到停止信号")
    except Exception as e:
        logger.error(f"❌ 监听器运行异常: {e}")
    finally:
        # 停止监听
        monitor.stop_monitoring()

if __name__ == "__main__":
    main()
