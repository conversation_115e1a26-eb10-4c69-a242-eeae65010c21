"""
测试LLM JSON解析功能 - 特殊字符处理
用于验证llm_client.py中的parse_trade_instructions函数是否能正确处理中文字符和特殊字符
"""
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入要测试的函数
from app.utils.llm_client import parse_trade_instructions

# 测试用例
test_cases = [
    {
        "name": "中文字符和特殊符号",
        "input": """
```json
{
  "action": "SELL",
  "orderType": "LIMIT",
  "entryPrice": 1.11125,
  "stopLoss": 1.11190,
  "takeProfit": 1.10970,
  "lotSize": 0.12,
  "riskLevel": "MEDIUM",
  "reasoning": "价格在阻力区1.11130下方出现技术性反弹衰竭信号，MACD死叉确认且布林带开口向下。入场价设置于关键阻力位下方5点（1.11125），止损设于近期高点1.11185上方5点（1.11190），止盈目标为15分钟布林带下轨与1小时MA50共振区域（1.10970-1.10975）。风险回报比1:2.8（65点止损/182点止盈），符合短线交易策略要求。需注意14:30 GMT后欧美市场流动性增加可能加速趋势发展。",
  "orderManagement": []
}
```
        """,
        "expected": {
            "action": "SELL",
            "orderType": "LIMIT",
            "entryPrice": 1.11125,
            "stopLoss": 1.1119,
            "takeProfit": 1.1097,
            "lotSize": 0.12,
            "riskLevel": "MEDIUM",
            "reasoning": "价格在阻力区1.11130下方出现技术性反弹衰竭信号，MACD死叉确认且布林带开口向下。入场价设置于关键阻力位下方5点（1.11125），止损设于近期高点1.11185上方5点（1.11190），止盈目标为15分钟布林带下轨与1小时MA50共振区域（1.10970-1.10975）。风险回报比1:2.8（65点止损/182点止盈），符合短线交易策略要求。需注意14:30 GMT后欧美市场流动性增加可能加速趋势发展。",
            "orderManagement": []
        }
    },
    {
        "name": "带换行符的中文字符",
        "input": """
```json
{
  "action": "SELL",
  "orderType": "LIMIT",
  "entryPrice": 1.11125,
  "stopLoss": 1.11190,
  "takeProfit": 1.10970,
  "lotSize": 0.12,
  "riskLevel": "MEDIUM",
  "reasoning": "价格在阻力区1.11130下方出现技术性反弹衰竭信号，
MACD死叉确认且布林带开口向下。
入场价设置于关键阻力位下方5点（1.11125），
止损设于近期高点1.11185上方5点（1.11190），
止盈目标为15分钟布林带下轨与1小时MA50共振区域（1.10970-1.10975）。",
  "orderManagement": []
}
```
        """,
        "expected": {
            "action": "SELL",
            "orderType": "LIMIT",
            "entryPrice": 1.11125,
            "stopLoss": 1.1119,
            "takeProfit": 1.1097,
            "lotSize": 0.12,
            "riskLevel": "MEDIUM",
            "reasoning": "价格在阻力区1.11130下方出现技术性反弹衰竭信号， MACD死叉确认且布林带开口向下。 入场价设置于关键阻力位下方5点（1.11125）， 止损设于近期高点1.11185上方5点（1.11190）， 止盈目标为15分钟布林带下轨与1小时MA50共振区域（1.10970-1.10975）。",
            "orderManagement": []
        }
    },
    {
        "name": "带特殊符号的中文字符",
        "input": """
```json
{
  "action": "SELL",
  "orderType": "LIMIT",
  "entryPrice": 1.11125,
  "stopLoss": 1.11190,
  "takeProfit": 1.10970,
  "lotSize": 0.12,
  "riskLevel": "MEDIUM",
  "reasoning": "价格在阻力区1.11130下方出现技术性反弹衰竭信号，MACD死叉确认且布林带开口向下。入场价设置于关键阻力位下方5点（1.11125），止损设于近期高点1.11185上方5点（1.11190），止盈目标为15分钟布林带下轨与1小时MA50共振区域（1.10970-1.10975）。风险回报比1:2.8（65点止损/182点止盈），符合短线交易策略要求。需注意14:30 GMT后欧美市场流动性增加可能加速趋势发展。\\n",
  "orderManagement": []
}
```
        """,
        "expected": {
            "action": "SELL",
            "orderType": "LIMIT",
            "entryPrice": 1.11125,
            "stopLoss": 1.1119,
            "takeProfit": 1.1097,
            "lotSize": 0.12,
            "riskLevel": "MEDIUM",
            "reasoning": "价格在阻力区1.11130下方出现技术性反弹衰竭信号，MACD死叉确认且布林带开口向下。入场价设置于关键阻力位下方5点（1.11125），止损设于近期高点1.11185上方5点（1.11190），止盈目标为15分钟布林带下轨与1小时MA50共振区域（1.10970-1.10975）。风险回报比1:2.8（65点止损/182点止盈），符合短线交易策略要求。需注意14:30 GMT后欧美市场流动性增加可能加速趋势发展。\\n",
            "orderManagement": []
        }
    }
]

def run_tests():
    """运行所有测试用例"""
    print(f"开始测试LLM JSON解析功能 - 特殊字符处理，共{len(test_cases)}个测试用例")
    
    success_count = 0
    for i, test_case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {test_case['name']}")
        print("-" * 50)
        
        # 解析输入
        result = parse_trade_instructions(test_case['input'])
        
        # 验证结果
        expected = test_case['expected']
        success = True
        
        # 检查基本字段
        for key in ['action', 'orderType', 'entryPrice', 'stopLoss', 'takeProfit', 'lotSize', 'riskLevel']:
            if result.get(key) != expected.get(key):
                print(f"❌ {key}不匹配: 期望 {expected.get(key)}, 实际 {result.get(key)}")
                success = False
        
        # 检查reasoning字段（只检查前50个字符）
        expected_reasoning = expected.get('reasoning', '')[:50]
        actual_reasoning = result.get('reasoning', '')[:50]
        if expected_reasoning != actual_reasoning:
            print(f"❌ reasoning不匹配: \n期望: {expected_reasoning}...\n实际: {actual_reasoning}...")
            success = False
        
        if success:
            print(f"✅ 测试通过")
            success_count += 1
        else:
            print(f"❌ 测试失败")
            print(f"期望结果: {json.dumps(expected, ensure_ascii=False, indent=2)}")
            print(f"实际结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    print(f"\n测试完成: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)

if __name__ == "__main__":
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始测试LLM JSON解析功能 - 特殊字符处理")
    success = run_tests()
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 测试{'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
