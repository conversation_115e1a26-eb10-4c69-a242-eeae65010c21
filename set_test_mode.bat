@echo off
chcp 65001
echo ========================================
echo 设置外汇交易系统手动测试模式
echo ========================================

echo 正在设置环境变量...

REM 设置强制跳过MT4连接模式
set SKIP_MT4_CONNECTION=true

REM 设置其他测试相关环境变量
set FLASK_ENV=development
set LOG_LEVEL=INFO

echo ✅ 手动测试模式已启用
echo.
echo 📋 当前设置:
echo   - SKIP_MT4_CONNECTION=true (强制跳过MT4服务器连接)
echo   - FLASK_ENV=development (开发环境)
echo   - LOG_LEVEL=INFO (信息级别日志)
echo.
echo ⚠️  注意: 这是手动测试模式，无论市场是否开放都会跳过MT4连接
echo ⚠️  如需自动模式请运行 set_auto_mode.bat
echo ⚠️  如需生产模式请运行 set_production_mode.bat
echo.
echo 现在可以运行系统了:
echo   python run.py
echo.
echo ========================================
pause
