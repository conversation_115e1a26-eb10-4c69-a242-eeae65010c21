@echo off
chcp 65001
echo ========================================
echo 外汇交易系统 - 远程更新脚本
echo ========================================

:: 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo ❌ 需要管理员权限运行此脚本
    echo 请右键选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 设置变量
set SERVICE_NAME=ForexTradingSystem
set BACKUP_DIR=backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set UPDATE_URL=https://your-update-server.com/latest.zip
set CURRENT_DIR=%~dp0

echo 📊 当前目录: %CURRENT_DIR%
echo 📊 备份目录: %BACKUP_DIR%

:: 检查服务状态
echo 🔍 检查服务状态...
sc query "%SERVICE_NAME%" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 服务未安装，将进行全新安装
    set FRESH_INSTALL=1
) else (
    echo ✅ 服务已存在，将进行更新
    set FRESH_INSTALL=0
)

:: 停止服务
if %FRESH_INSTALL%==0 (
    echo 🔄 停止服务...
    sc stop "%SERVICE_NAME%"
    timeout /t 10 /nobreak >nul
)

:: 创建备份
echo 📦 创建备份...
if not exist "backups" mkdir backups
xcopy /E /I /Y "%CURRENT_DIR%" "backups\%BACKUP_DIR%\" /EXCLUDE:backup_exclude.txt

:: 下载更新包（模拟）
echo 🌐 检查更新...
echo [模拟] 从远程服务器下载更新包...
echo [模拟] 下载完成

:: 应用更新
echo 🔄 应用更新...

:: 更新Python依赖
echo 📦 更新依赖包...
call venv\Scripts\activate.bat
pip install -r requirements.txt --upgrade

:: 数据库迁移（如果需要）
echo 🗄️ 检查数据库更新...
python -c "print('数据库检查完成')"

:: 重启服务
if %FRESH_INSTALL%==0 (
    echo 🚀 重启服务...
    sc start "%SERVICE_NAME%"
    timeout /t 5 /nobreak >nul
) else (
    echo 📦 安装新服务...
    call install_service.bat
)

:: 验证更新
echo ✅ 验证更新...
timeout /t 10 /nobreak >nul

:: 检查服务状态
sc query "%SERVICE_NAME%" | findstr "RUNNING" >nul
if errorlevel 1 (
    echo ❌ 服务启动失败，正在回滚...
    goto :rollback
) else (
    echo ✅ 更新成功！服务正常运行
    goto :success
)

:rollback
echo 🔄 回滚到备份版本...
sc stop "%SERVICE_NAME%"
timeout /t 5 /nobreak >nul

:: 恢复备份
xcopy /E /I /Y "backups\%BACKUP_DIR%\*" "%CURRENT_DIR%"

:: 重启服务
sc start "%SERVICE_NAME%"
echo ⚠️ 已回滚到备份版本
goto :end

:success
echo 🎉 系统更新完成！
echo 📊 服务状态: 运行中
echo 🌐 访问地址: http://localhost:5000
echo 📄 备份位置: backups\%BACKUP_DIR%

:end
pause
