#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基本面分析引擎
整合新闻和经济事件，生成基本面分析信号
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .news_data_collector import news_collector, NewsItem, NewsImportance
from .economic_calendar_manager import calendar_manager, EconomicEvent, EventImportance

class FundamentalSignal(Enum):
    """基本面信号"""
    STRONG_BULLISH = "strong_bullish"
    BULLISH = "bullish"
    NEUTRAL = "neutral"
    BEARISH = "bearish"
    STRONG_BEARISH = "strong_bearish"

@dataclass
class FundamentalAnalysis:
    """基本面分析结果"""
    symbol: str
    signal: FundamentalSignal
    confidence: float  # 0.0 to 1.0
    sentiment_score: float  # -1.0 to 1.0
    event_impact: float  # 0.0 to 1.0
    news_impact: float  # 0.0 to 1.0
    time_horizon: str  # 'short', 'medium', 'long'
    key_factors: List[str]
    risk_events: List[str]
    analysis_time: datetime

class FundamentalAnalysisEngine:
    """基本面分析引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 分析缓存
        self.analysis_cache = {}
        self.last_analysis_time = {}
        
        # 信号权重配置
        self.signal_weights = {
            'news_sentiment': 0.4,
            'event_impact': 0.4,
            'time_decay': 0.2
        }
        
        # 时间范围配置
        self.time_horizons = {
            'short': {'hours': 6, 'weight': 1.0},
            'medium': {'hours': 24, 'weight': 0.8},
            'long': {'hours': 72, 'weight': 0.6}
        }
    
    def analyze_fundamentals(self, symbol: str, time_horizon: str = 'short') -> FundamentalAnalysis:
        """分析货币对的基本面
        
        Args:
            symbol: 货币对符号
            time_horizon: 时间范围 ('short', 'medium', 'long')
            
        Returns:
            FundamentalAnalysis: 基本面分析结果
        """
        try:
            # 检查缓存
            cache_key = f"{symbol}_{time_horizon}"
            if self._is_cache_valid(cache_key):
                return self.analysis_cache[cache_key]
            
            # 获取时间范围配置
            horizon_config = self.time_horizons.get(time_horizon, self.time_horizons['short'])
            hours_back = horizon_config['hours']
            
            # 1. 收集新闻数据
            relevant_news = self._get_relevant_news(symbol, hours_back)
            
            # 2. 收集经济事件
            relevant_events = self._get_relevant_events(symbol, hours_back)
            
            # 3. 分析新闻情感
            news_sentiment, news_impact = self._analyze_news_sentiment(relevant_news)
            
            # 4. 分析事件影响
            event_impact, event_factors = self._analyze_event_impact(relevant_events)
            
            # 5. 综合分析
            overall_sentiment = self._calculate_overall_sentiment(
                news_sentiment, news_impact, event_impact, horizon_config['weight']
            )
            
            # 6. 生成信号
            signal = self._generate_signal(overall_sentiment, news_impact, event_impact)
            
            # 7. 计算置信度
            confidence = self._calculate_confidence(
                relevant_news, relevant_events, news_impact, event_impact
            )
            
            # 8. 识别关键因素和风险
            key_factors = self._identify_key_factors(relevant_news, relevant_events)
            risk_events = self._identify_risk_events(relevant_events)
            
            # 9. 创建分析结果
            analysis = FundamentalAnalysis(
                symbol=symbol,
                signal=signal,
                confidence=confidence,
                sentiment_score=overall_sentiment,
                event_impact=event_impact,
                news_impact=news_impact,
                time_horizon=time_horizon,
                key_factors=key_factors,
                risk_events=risk_events,
                analysis_time=datetime.now()
            )
            
            # 10. 更新缓存
            self.analysis_cache[cache_key] = analysis
            self.last_analysis_time[cache_key] = datetime.now()
            
            self.logger.info(f"基本面分析完成 {symbol}: {signal.value}, 置信度: {confidence:.2f}")
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"基本面分析失败 {symbol}: {e}")
            # 返回中性分析
            return FundamentalAnalysis(
                symbol=symbol,
                signal=FundamentalSignal.NEUTRAL,
                confidence=0.0,
                sentiment_score=0.0,
                event_impact=0.0,
                news_impact=0.0,
                time_horizon=time_horizon,
                key_factors=[],
                risk_events=[],
                analysis_time=datetime.now()
            )
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.analysis_cache or cache_key not in self.last_analysis_time:
            return False
        
        # 缓存有效期：15分钟
        cache_age = (datetime.now() - self.last_analysis_time[cache_key]).total_seconds()
        return cache_age < 900
    
    def _get_relevant_news(self, symbol: str, hours_back: int) -> List[NewsItem]:
        """获取相关新闻"""
        try:
            # 获取最近新闻
            recent_news = news_collector.get_recent_news(hours_back)
            
            # 过滤与货币对相关的新闻
            relevant_news = []
            for news in recent_news:
                if symbol in news.currency_pairs or self._is_news_relevant(news, symbol):
                    relevant_news.append(news)
            
            return relevant_news
            
        except Exception as e:
            self.logger.error(f"获取相关新闻失败: {e}")
            return []
    
    def _get_relevant_events(self, symbol: str, hours_ahead: int) -> List[EconomicEvent]:
        """获取相关经济事件"""
        try:
            # 获取即将到来的事件
            upcoming_events = calendar_manager.get_events_for_currency_pair(symbol, hours_ahead)
            
            return upcoming_events
            
        except Exception as e:
            self.logger.error(f"获取相关事件失败: {e}")
            return []
    
    def _is_news_relevant(self, news: NewsItem, symbol: str) -> bool:
        """判断新闻是否与货币对相关"""
        # 提取货币代码
        base_currency = symbol[:3]
        quote_currency = symbol[3:]
        
        # 检查新闻内容是否包含相关货币
        content = (news.title + " " + news.content).upper()
        
        return base_currency in content or quote_currency in content
    
    def _analyze_news_sentiment(self, news_list: List[NewsItem]) -> Tuple[float, float]:
        """分析新闻情感"""
        if not news_list:
            return 0.0, 0.0
        
        total_sentiment = 0.0
        total_impact = 0.0
        total_weight = 0.0
        
        for news in news_list:
            # 计算权重（基于重要性和时效性）
            importance_weight = {
                NewsImportance.LOW: 0.2,
                NewsImportance.MEDIUM: 0.5,
                NewsImportance.HIGH: 0.8,
                NewsImportance.CRITICAL: 1.0
            }.get(news.importance, 0.5)
            
            # 时效性权重
            hours_old = (datetime.now() - news.publish_time).total_seconds() / 3600
            time_weight = max(0.1, 1.0 - hours_old / 24)
            
            weight = importance_weight * time_weight
            
            total_sentiment += news.sentiment_score * weight
            total_impact += news.impact_score * weight
            total_weight += weight
        
        if total_weight == 0:
            return 0.0, 0.0
        
        avg_sentiment = total_sentiment / total_weight
        avg_impact = total_impact / total_weight
        
        return avg_sentiment, avg_impact
    
    def _analyze_event_impact(self, events_list: List[EconomicEvent]) -> Tuple[float, List[str]]:
        """分析事件影响"""
        if not events_list:
            return 0.0, []
        
        total_impact = 0.0
        event_factors = []
        
        for event in events_list:
            # 计算事件影响
            impact = event.market_impact
            
            # 基于时间临近性调整
            hours_until = (event.scheduled_time - datetime.now()).total_seconds() / 3600
            if hours_until < 2:
                impact *= 1.5  # 2小时内事件影响加强
            elif hours_until < 24:
                impact *= 1.2  # 24小时内事件影响略微加强
            
            total_impact += impact
            
            # 记录重要事件
            if impact >= 0.6:
                event_factors.append(f"{event.title} ({event.scheduled_time.strftime('%m-%d %H:%M')})")
        
        # 归一化影响分数
        normalized_impact = min(1.0, total_impact / len(events_list))
        
        return normalized_impact, event_factors
    
    def _calculate_overall_sentiment(self, news_sentiment: float, news_impact: float, 
                                   event_impact: float, time_weight: float) -> float:
        """计算综合情感分数"""
        # 加权平均
        weighted_sentiment = (
            news_sentiment * news_impact * self.signal_weights['news_sentiment'] +
            event_impact * self.signal_weights['event_impact']
        ) * time_weight
        
        # 归一化到 -1.0 到 1.0
        return max(-1.0, min(1.0, weighted_sentiment))
    
    def _generate_signal(self, sentiment: float, news_impact: float, event_impact: float) -> FundamentalSignal:
        """生成基本面信号"""
        # 计算综合强度
        impact_strength = max(news_impact, event_impact)
        
        # 基于情感和强度生成信号
        if sentiment >= 0.6 and impact_strength >= 0.7:
            return FundamentalSignal.STRONG_BULLISH
        elif sentiment >= 0.3 and impact_strength >= 0.4:
            return FundamentalSignal.BULLISH
        elif sentiment <= -0.6 and impact_strength >= 0.7:
            return FundamentalSignal.STRONG_BEARISH
        elif sentiment <= -0.3 and impact_strength >= 0.4:
            return FundamentalSignal.BEARISH
        else:
            return FundamentalSignal.NEUTRAL
    
    def _calculate_confidence(self, news_list: List[NewsItem], events_list: List[EconomicEvent],
                            news_impact: float, event_impact: float) -> float:
        """计算置信度"""
        confidence = 0.0
        
        # 基于数据量
        data_volume = len(news_list) + len(events_list)
        volume_score = min(1.0, data_volume / 10)  # 10个数据点为满分
        confidence += volume_score * 0.3
        
        # 基于影响强度
        impact_score = max(news_impact, event_impact)
        confidence += impact_score * 0.4
        
        # 基于数据质量
        high_quality_news = sum(1 for news in news_list if news.importance in [NewsImportance.HIGH, NewsImportance.CRITICAL])
        high_quality_events = sum(1 for event in events_list if event.importance in [EventImportance.HIGH, EventImportance.CRITICAL])
        quality_score = min(1.0, (high_quality_news + high_quality_events) / 5)
        confidence += quality_score * 0.3
        
        return min(1.0, confidence)
    
    def _identify_key_factors(self, news_list: List[NewsItem], events_list: List[EconomicEvent]) -> List[str]:
        """识别关键因素"""
        key_factors = []
        
        # 高影响新闻
        for news in news_list:
            if news.impact_score >= 0.7:
                key_factors.append(f"新闻: {news.title[:30]}...")
        
        # 重要事件
        for event in events_list:
            if event.market_impact >= 0.7:
                key_factors.append(f"事件: {event.title}")
        
        return key_factors[:5]  # 最多5个关键因素
    
    def _identify_risk_events(self, events_list: List[EconomicEvent]) -> List[str]:
        """识别风险事件"""
        risk_events = []
        
        for event in events_list:
            # 2小时内的高影响事件
            hours_until = (event.scheduled_time - datetime.now()).total_seconds() / 3600
            if 0 <= hours_until <= 2 and event.market_impact >= 0.6:
                risk_events.append(f"{event.title} ({event.scheduled_time.strftime('%H:%M')})")
        
        return risk_events
    
    def get_market_sentiment_summary(self, symbols: List[str]) -> Dict[str, FundamentalAnalysis]:
        """获取市场情感总结"""
        summary = {}
        
        for symbol in symbols:
            try:
                analysis = self.analyze_fundamentals(symbol, 'short')
                summary[symbol] = analysis
            except Exception as e:
                self.logger.error(f"分析 {symbol} 基本面失败: {e}")
        
        return summary
    
    def get_risk_assessment(self, hours_ahead: int = 24) -> Dict[str, List[str]]:
        """获取风险评估"""
        try:
            # 获取高影响事件
            high_impact_events = calendar_manager.get_high_impact_events(hours_ahead)
            
            risk_assessment = {
                'critical_events': [],
                'high_impact_events': [],
                'currency_risks': {}
            }
            
            for event in high_impact_events:
                hours_until = (event.scheduled_time - datetime.now()).total_seconds() / 3600
                event_desc = f"{event.title} ({event.scheduled_time.strftime('%m-%d %H:%M')})"
                
                if event.importance == EventImportance.CRITICAL:
                    risk_assessment['critical_events'].append(event_desc)
                elif event.market_impact >= 0.8:
                    risk_assessment['high_impact_events'].append(event_desc)
                
                # 按货币分类风险
                if event.currency not in risk_assessment['currency_risks']:
                    risk_assessment['currency_risks'][event.currency] = []
                risk_assessment['currency_risks'][event.currency].append(event_desc)
            
            return risk_assessment
            
        except Exception as e:
            self.logger.error(f"获取风险评估失败: {e}")
            return {'critical_events': [], 'high_impact_events': [], 'currency_risks': {}}

# 创建全局实例
fundamental_engine = FundamentalAnalysisEngine()
