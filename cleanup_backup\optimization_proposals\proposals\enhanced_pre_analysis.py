#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强预分析系统
目标：提高预分析的准确性和敏感性，减少误判，提高交易时机把握
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class AnalysisUrgency(Enum):
    """分析紧急程度"""
    IMMEDIATE = "立即分析"
    HIGH = "高优先级"
    MEDIUM = "中等优先级"
    LOW = "低优先级"
    SKIP = "跳过分析"

@dataclass
class MarketSignal:
    """市场信号"""
    signal_type: str
    strength: float  # 0-1
    confidence: float  # 0-1
    time_sensitivity: float  # 0-1，时间敏感性
    description: str

class EnhancedPreAnalyzer:
    """增强预分析器"""
    
    def __init__(self):
        self.signal_weights = {
            'trend_change': 0.25,
            'ma13_interaction': 0.20,
            'volatility_spike': 0.15,
            'support_resistance': 0.15,
            'momentum_divergence': 0.10,
            'volume_anomaly': 0.10,
            'time_factor': 0.05
        }
        
        self.analysis_thresholds = {
            'immediate': 0.8,
            'high': 0.6,
            'medium': 0.4,
            'low': 0.2
        }
    
    def should_perform_analysis(self, market_data: Dict, last_analysis_time: Optional[datetime] = None) -> Tuple[bool, str, AnalysisUrgency, float]:
        """
        增强的预分析决策
        
        Returns:
            (should_analyze, reason, urgency, confidence)
        """
        
        # 1. 收集所有市场信号
        signals = self._collect_market_signals(market_data)
        
        # 2. 计算时间因子
        time_factor = self._calculate_time_factor(last_analysis_time)
        
        # 3. 评估交易成本和时机
        cost_timing_factor = self._evaluate_cost_and_timing(market_data)
        
        # 4. 计算综合分析需求分数
        analysis_score = self._calculate_analysis_score(signals, time_factor, cost_timing_factor)
        
        # 5. 确定分析决策
        should_analyze, urgency = self._determine_analysis_decision(analysis_score)
        
        # 6. 生成详细原因
        reason = self._generate_detailed_reason(signals, analysis_score, urgency)
        
        # 7. 计算决策置信度
        confidence = self._calculate_decision_confidence(signals, analysis_score)
        
        return should_analyze, reason, urgency, confidence
    
    def _collect_market_signals(self, market_data: Dict) -> List[MarketSignal]:
        """收集市场信号"""
        signals = []
        
        # 1. 趋势变化信号
        trend_signal = self._detect_trend_change_signal(market_data)
        if trend_signal:
            signals.append(trend_signal)
        
        # 2. 13日均线交互信号
        ma13_signal = self._detect_ma13_interaction_signal(market_data)
        if ma13_signal:
            signals.append(ma13_signal)
        
        # 3. 波动率异常信号
        volatility_signal = self._detect_volatility_spike_signal(market_data)
        if volatility_signal:
            signals.append(volatility_signal)
        
        # 4. 支撑阻力测试信号
        sr_signal = self._detect_support_resistance_signal(market_data)
        if sr_signal:
            signals.append(sr_signal)
        
        # 5. 动量背离信号
        momentum_signal = self._detect_momentum_divergence_signal(market_data)
        if momentum_signal:
            signals.append(momentum_signal)
        
        # 6. 成交量异常信号
        volume_signal = self._detect_volume_anomaly_signal(market_data)
        if volume_signal:
            signals.append(volume_signal)
        
        return signals
    
    def _detect_trend_change_signal(self, market_data: Dict) -> Optional[MarketSignal]:
        """检测趋势变化信号"""
        try:
            # 获取多时间框架趋势数据
            ma13_15m = market_data.get('ma13_15min', {})
            ma13_1h = market_data.get('ma13_1h', {})
            
            current_price = market_data.get('current_price', 0)
            
            # 检测均线方向变化
            ma15_direction = ma13_15m.get('direction', 'FLAT')
            ma1h_direction = ma13_1h.get('direction', 'FLAT')
            
            # 检测价格与均线关系变化
            ma15_position = ma13_15m.get('position', 'NEAR')
            ma1h_position = ma13_1h.get('position', 'NEAR')
            
            # 计算信号强度
            strength = 0
            confidence = 0
            time_sensitivity = 0
            description_parts = []
            
            # 均线方向一致性变化
            if ma15_direction != 'FLAT' and ma1h_direction != 'FLAT':
                if ma15_direction == ma1h_direction:
                    strength += 0.4
                    confidence += 0.3
                    description_parts.append(f"双时间框架均线方向一致({ma15_direction})")
                else:
                    strength += 0.6  # 方向分歧更需要分析
                    confidence += 0.4
                    time_sensitivity += 0.5
                    description_parts.append("双时间框架均线方向分歧")
            
            # 价格突破均线
            if ma15_position != 'NEAR' or ma1h_position != 'NEAR':
                strength += 0.3
                confidence += 0.2
                time_sensitivity += 0.3
                description_parts.append("价格偏离均线")
            
            # 均线角度变化（需要历史数据计算）
            ma15_slope = ma13_15m.get('slope', 0)
            ma1h_slope = ma13_1h.get('slope', 0)
            
            if abs(ma15_slope) > 0.0001 or abs(ma1h_slope) > 0.0001:
                strength += 0.2
                confidence += 0.2
                description_parts.append("均线斜率变化")
            
            if strength > 0.3:
                return MarketSignal(
                    signal_type='trend_change',
                    strength=min(strength, 1.0),
                    confidence=min(confidence, 1.0),
                    time_sensitivity=min(time_sensitivity, 1.0),
                    description='; '.join(description_parts)
                )
            
        except Exception as e:
            print(f"趋势变化信号检测失败: {e}")
        
        return None
    
    def _detect_ma13_interaction_signal(self, market_data: Dict) -> Optional[MarketSignal]:
        """检测13日均线交互信号"""
        try:
            current_price = market_data.get('current_price', 0)
            ma13_15m = market_data.get('ma13_15min', {})
            ma13_1h = market_data.get('ma13_1h', {})
            
            ma15_value = ma13_15m.get('value', 0)
            ma1h_value = ma13_1h.get('value', 0)
            
            if not all([current_price, ma15_value, ma1h_value]):
                return None
            
            # 计算价格与均线的距离
            distance_15m = abs(current_price - ma15_value) / ma15_value * 10000  # 点数
            distance_1h = abs(current_price - ma1h_value) / ma1h_value * 10000
            
            strength = 0
            confidence = 0
            time_sensitivity = 0
            description_parts = []
            
            # 价格接近均线（回踩机会）
            if distance_15m <= 10:  # 10点以内
                strength += 0.6
                confidence += 0.5
                time_sensitivity += 0.8  # 高时间敏感性
                description_parts.append(f"价格接近15分钟均线({distance_15m:.1f}点)")
            
            if distance_1h <= 15:  # 15点以内
                strength += 0.4
                confidence += 0.4
                time_sensitivity += 0.6
                description_parts.append(f"价格接近1小时均线({distance_1h:.1f}点)")
            
            # 均线交叉
            ma_distance = abs(ma15_value - ma1h_value) / ma1h_value * 10000
            if ma_distance <= 5:  # 均线接近
                strength += 0.3
                confidence += 0.3
                description_parts.append("双时间框架均线接近")
            
            # 预测回踩
            ma15_projection = ma13_15m.get('projection', ma15_value)
            if abs(current_price - ma15_projection) / ma15_projection * 10000 <= 8:
                strength += 0.4
                confidence += 0.4
                time_sensitivity += 0.7
                description_parts.append("预测回踩区域")
            
            if strength > 0.3:
                return MarketSignal(
                    signal_type='ma13_interaction',
                    strength=min(strength, 1.0),
                    confidence=min(confidence, 1.0),
                    time_sensitivity=min(time_sensitivity, 1.0),
                    description='; '.join(description_parts)
                )
            
        except Exception as e:
            print(f"MA13交互信号检测失败: {e}")
        
        return None
    
    def _detect_volatility_spike_signal(self, market_data: Dict) -> Optional[MarketSignal]:
        """检测波动率异常信号"""
        try:
            volatility_regime = market_data.get('volatility_regime', 'NORMAL')
            price_change_percent = market_data.get('price_change_percent', 0)
            
            strength = 0
            confidence = 0
            time_sensitivity = 0
            description_parts = []
            
            # 波动率状态变化
            if volatility_regime == 'HIGH':
                strength += 0.5
                confidence += 0.4
                time_sensitivity += 0.6
                description_parts.append("高波动率环境")
            elif volatility_regime == 'LOW':
                strength += 0.2
                confidence += 0.3
                description_parts.append("低波动率环境")
            
            # 价格变化幅度
            abs_change = abs(price_change_percent)
            if abs_change > 0.3:  # 0.3%以上变化
                strength += min(abs_change / 0.5, 0.6)  # 最大0.6
                confidence += 0.4
                time_sensitivity += 0.7
                description_parts.append(f"价格变化{abs_change:.2f}%")
            
            if strength > 0.2:
                return MarketSignal(
                    signal_type='volatility_spike',
                    strength=min(strength, 1.0),
                    confidence=min(confidence, 1.0),
                    time_sensitivity=min(time_sensitivity, 1.0),
                    description='; '.join(description_parts)
                )
            
        except Exception as e:
            print(f"波动率信号检测失败: {e}")
        
        return None
    
    def _detect_support_resistance_signal(self, market_data: Dict) -> Optional[MarketSignal]:
        """检测支撑阻力信号"""
        try:
            current_price = market_data.get('current_price', 0)
            support_resistance = market_data.get('support_resistance', {})
            
            support_strength = support_resistance.get('support_strength', 0)
            resistance_strength = support_resistance.get('resistance_strength', 0)
            
            strength = 0
            confidence = 0
            time_sensitivity = 0
            description_parts = []
            
            # 强支撑阻力位
            if support_strength > 0.6:
                strength += 0.4
                confidence += 0.5
                time_sensitivity += 0.5
                description_parts.append("接近强支撑位")
            
            if resistance_strength > 0.6:
                strength += 0.4
                confidence += 0.5
                time_sensitivity += 0.5
                description_parts.append("接近强阻力位")
            
            # 支撑阻力测试
            if support_strength > 0.3 or resistance_strength > 0.3:
                strength += 0.2
                confidence += 0.3
                description_parts.append("测试关键价位")
            
            if strength > 0.2:
                return MarketSignal(
                    signal_type='support_resistance',
                    strength=min(strength, 1.0),
                    confidence=min(confidence, 1.0),
                    time_sensitivity=min(time_sensitivity, 1.0),
                    description='; '.join(description_parts)
                )
            
        except Exception as e:
            print(f"支撑阻力信号检测失败: {e}")
        
        return None
    
    def _detect_momentum_divergence_signal(self, market_data: Dict) -> Optional[MarketSignal]:
        """检测动量背离信号"""
        try:
            rsi = market_data.get('rsi', 50)
            rsi_change = market_data.get('rsi_change', 0)
            price_change_percent = market_data.get('price_change_percent', 0)
            
            strength = 0
            confidence = 0
            time_sensitivity = 0
            description_parts = []
            
            # RSI极值
            if rsi > 70:
                strength += 0.3
                confidence += 0.4
                time_sensitivity += 0.4
                description_parts.append(f"RSI超买({rsi:.1f})")
            elif rsi < 30:
                strength += 0.3
                confidence += 0.4
                time_sensitivity += 0.4
                description_parts.append(f"RSI超卖({rsi:.1f})")
            
            # 价格与RSI背离
            if price_change_percent > 0.1 and rsi_change < -2:
                strength += 0.4
                confidence += 0.3
                time_sensitivity += 0.6
                description_parts.append("价格上涨RSI下降(看跌背离)")
            elif price_change_percent < -0.1 and rsi_change > 2:
                strength += 0.4
                confidence += 0.3
                time_sensitivity += 0.6
                description_parts.append("价格下跌RSI上升(看涨背离)")
            
            if strength > 0.2:
                return MarketSignal(
                    signal_type='momentum_divergence',
                    strength=min(strength, 1.0),
                    confidence=min(confidence, 1.0),
                    time_sensitivity=min(time_sensitivity, 1.0),
                    description='; '.join(description_parts)
                )
            
        except Exception as e:
            print(f"动量背离信号检测失败: {e}")
        
        return None
    
    def _detect_volume_anomaly_signal(self, market_data: Dict) -> Optional[MarketSignal]:
        """检测成交量异常信号"""
        # 简化实现，实际需要成交量数据
        return None
    
    def _calculate_time_factor(self, last_analysis_time: Optional[datetime]) -> float:
        """计算时间因子"""
        if not last_analysis_time:
            return 1.0  # 没有上次分析时间，需要分析
        
        now = datetime.now()
        time_diff = (now - last_analysis_time).total_seconds() / 3600  # 小时
        
        # 时间权重曲线
        if time_diff >= 2:
            return 1.0  # 超过2小时，强制分析
        elif time_diff >= 1:
            return 0.8  # 1-2小时，高权重
        elif time_diff >= 0.5:
            return 0.5  # 30分钟-1小时，中等权重
        else:
            return 0.2  # 30分钟内，低权重
    
    def _evaluate_cost_and_timing(self, market_data: Dict) -> float:
        """评估交易成本和时机"""
        cost_timing_score = 1.0
        
        # 点差成本评估
        spread = market_data.get('spread', 2)  # 默认2点点差
        if spread > 3:
            cost_timing_score *= 0.8  # 高点差降低分析需求
        
        # 市场时间评估
        current_hour = datetime.now().hour
        
        # 主要交易时段
        if 8 <= current_hour <= 17:  # 欧美盘
            cost_timing_score *= 1.2
        elif 21 <= current_hour <= 23 or 0 <= current_hour <= 2:  # 美盘
            cost_timing_score *= 1.1
        else:  # 亚洲盘
            cost_timing_score *= 0.9
        
        # 周末或节假日
        weekday = datetime.now().weekday()
        if weekday >= 5:  # 周末
            cost_timing_score *= 0.5
        
        return min(cost_timing_score, 1.5)
    
    def _calculate_analysis_score(self, signals: List[MarketSignal], 
                                 time_factor: float, cost_timing_factor: float) -> float:
        """计算综合分析需求分数"""
        if not signals:
            return time_factor * 0.3  # 仅基于时间因子
        
        # 加权信号强度
        weighted_signal_score = 0
        total_weight = 0
        
        for signal in signals:
            weight = self.signal_weights.get(signal.signal_type, 0.1)
            # 考虑信号强度、置信度和时间敏感性
            signal_score = (signal.strength * 0.5 + 
                          signal.confidence * 0.3 + 
                          signal.time_sensitivity * 0.2)
            
            weighted_signal_score += signal_score * weight
            total_weight += weight
        
        if total_weight > 0:
            avg_signal_score = weighted_signal_score / total_weight
        else:
            avg_signal_score = 0
        
        # 综合分数
        final_score = (avg_signal_score * 0.7 + 
                      time_factor * 0.2 + 
                      (cost_timing_factor - 1) * 0.1)
        
        return max(0, min(final_score, 1))
    
    def _determine_analysis_decision(self, analysis_score: float) -> Tuple[bool, AnalysisUrgency]:
        """确定分析决策"""
        if analysis_score >= self.analysis_thresholds['immediate']:
            return True, AnalysisUrgency.IMMEDIATE
        elif analysis_score >= self.analysis_thresholds['high']:
            return True, AnalysisUrgency.HIGH
        elif analysis_score >= self.analysis_thresholds['medium']:
            return True, AnalysisUrgency.MEDIUM
        elif analysis_score >= self.analysis_thresholds['low']:
            return True, AnalysisUrgency.LOW
        else:
            return False, AnalysisUrgency.SKIP
    
    def _generate_detailed_reason(self, signals: List[MarketSignal], 
                                 analysis_score: float, urgency: AnalysisUrgency) -> str:
        """生成详细原因"""
        if not signals:
            return f"基于时间因子的常规检查 (分数: {analysis_score:.2f})"
        
        # 按强度排序信号
        sorted_signals = sorted(signals, key=lambda x: x.strength * x.confidence, reverse=True)
        
        reason_parts = [f"分析需求分数: {analysis_score:.2f} ({urgency.value})"]
        
        # 添加主要信号
        for i, signal in enumerate(sorted_signals[:3]):  # 最多显示3个主要信号
            signal_strength = signal.strength * signal.confidence
            reason_parts.append(f"{i+1}. {signal.description} (强度: {signal_strength:.2f})")
        
        return '; '.join(reason_parts)
    
    def _calculate_decision_confidence(self, signals: List[MarketSignal], 
                                     analysis_score: float) -> float:
        """计算决策置信度"""
        if not signals:
            return 0.3  # 仅基于时间的决策置信度较低
        
        # 基于信号质量和一致性
        avg_confidence = sum(s.confidence for s in signals) / len(signals)
        signal_consistency = self._calculate_signal_consistency(signals)
        
        decision_confidence = (avg_confidence * 0.6 + 
                             signal_consistency * 0.3 + 
                             min(analysis_score, 1.0) * 0.1)
        
        return min(decision_confidence, 1.0)
    
    def _calculate_signal_consistency(self, signals: List[MarketSignal]) -> float:
        """计算信号一致性"""
        if len(signals) <= 1:
            return 1.0
        
        # 简化的一致性计算
        # 实际实现需要更复杂的信号方向性分析
        strengths = [s.strength for s in signals]
        avg_strength = sum(strengths) / len(strengths)
        variance = sum((s - avg_strength) ** 2 for s in strengths) / len(strengths)
        
        # 方差越小，一致性越高
        consistency = 1 / (1 + variance * 10)
        return consistency

# 使用示例
def enhanced_pre_analysis_decision(market_data: Dict, last_analysis_time: Optional[datetime] = None) -> Dict:
    """增强预分析决策"""
    analyzer = EnhancedPreAnalyzer()
    
    should_analyze, reason, urgency, confidence = analyzer.should_perform_analysis(
        market_data, last_analysis_time
    )
    
    return {
        'should_analyze': should_analyze,
        'reason': reason,
        'urgency': urgency.value,
        'confidence': confidence,
        'recommendation': {
            'immediate_action': urgency in [AnalysisUrgency.IMMEDIATE, AnalysisUrgency.HIGH],
            'can_delay': urgency in [AnalysisUrgency.MEDIUM, AnalysisUrgency.LOW],
            'skip_safe': urgency == AnalysisUrgency.SKIP
        }
    }
