"""
QuantumForex Pro 增强版启动脚本
实时监控版本，更频繁的分析和更敏感的信号生成
"""

import os
import sys
import time
from datetime import datetime

def display_banner():
    """显示启动横幅"""
    print("=" * 70)
    print("🚀 QuantumForex Pro - 增强实时版")
    print("🏆 世界顶级量化交易系统")
    print("⚡ 实时监控 | 智能分析 | 精准交易")
    print("=" * 70)
    print(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("💡 增强功能:")
    print("   • 1分钟分析间隔（原5分钟）")
    print("   • 降低信号阈值，更容易产生交易信号")
    print("   • 增强趋势数据，提高信号质量")
    print("   • 实时监控和状态更新")
    print("-" * 70)

def show_system_info():
    """显示系统信息"""
    try:
        import psutil
        
        print("🖥️ 系统资源状态:")
        memory = psutil.virtual_memory()
        print(f"   💾 内存: {memory.percent:.1f}% 使用中")
        print(f"   🖥️ CPU: {psutil.cpu_percent(interval=1):.1f}% 使用中")
        print(f"   💽 磁盘: {psutil.disk_usage('.').free / (1024**3):.1f} GB 可用")
        
    except ImportError:
        print("🖥️ 系统资源监控不可用")
    
    print("-" * 70)

def main():
    """主函数"""
    try:
        # 显示启动横幅
        display_banner()
        
        # 显示系统信息
        show_system_info()
        
        print("🔄 正在启动 QuantumForex Pro 增强版...")
        print("📊 预期功能:")
        print("   • 更频繁的市场分析")
        print("   • 更敏感的信号检测")
        print("   • 实时交易机会识别")
        print("   • 智能风险管理")
        print()
        print("⚠️ 注意: 按 Ctrl+C 可安全停止系统")
        print("=" * 70)
        
        # 等待用户确认
        input("按回车键开始运行...")
        
        # 启动主程序
        print("\n🎯 启动主程序...")
        from main import main as main_program
        main_program()
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户取消启动")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
