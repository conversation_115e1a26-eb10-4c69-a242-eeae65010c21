#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多策略融合交易框架
目标：突破单一13日均线右侧交易的局限，实现多策略自适应交易系统
"""

from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
import numpy as np
from datetime import datetime

class MarketRegime(Enum):
    """市场状态"""
    TRENDING_UP = "上升趋势"
    TRENDING_DOWN = "下降趋势"
    RANGING = "震荡整理"
    BREAKOUT_UP = "向上突破"
    BREAKOUT_DOWN = "向下突破"
    REVERSAL_BULLISH = "看涨反转"
    REVERSAL_BEARISH = "看跌反转"
    UNCERTAIN = "不确定"

class StrategyType(Enum):
    """策略类型"""
    TREND_FOLLOWING = "趋势跟随"
    MEAN_REVERSION = "均值回归"
    BREAKOUT = "突破交易"
    REVERSAL = "反转交易"
    SCALPING = "剥头皮"
    SWING = "波段交易"

@dataclass
class MarketContext:
    """市场上下文"""
    regime: MarketRegime
    volatility_level: str  # LOW, NORMAL, HIGH
    trend_strength: float  # 0-1
    support_resistance: Dict
    time_of_day: str
    economic_events: List[Dict]
    liquidity_score: float
    confidence: float

@dataclass
class TradingSignal:
    """交易信号"""
    strategy_type: StrategyType
    action: str  # BUY, SELL, NONE
    entry_price: Optional[float]
    stop_loss: float
    take_profit: float
    confidence: float
    strength: float
    reasoning: str
    timeframe: str
    risk_level: str

class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str, strategy_type: StrategyType):
        self.name = name
        self.strategy_type = strategy_type
        self.enabled = True
        self.weight = 1.0
        self.performance_history = []
    
    @abstractmethod
    def is_applicable(self, market_context: MarketContext) -> bool:
        """判断策略是否适用于当前市场环境"""
        pass
    
    @abstractmethod
    def generate_signal(self, market_data: Dict, market_context: MarketContext) -> Optional[TradingSignal]:
        """生成交易信号"""
        pass
    
    @abstractmethod
    def calculate_position_size(self, signal: TradingSignal, account_info: Dict) -> float:
        """计算仓位大小"""
        pass
    
    def update_performance(self, trade_result: Dict):
        """更新策略表现"""
        self.performance_history.append(trade_result)
        
        # 保持最近100笔交易记录
        if len(self.performance_history) > 100:
            self.performance_history = self.performance_history[-100:]
    
    def get_performance_metrics(self) -> Dict:
        """获取策略表现指标"""
        if not self.performance_history:
            return {'win_rate': 0, 'avg_profit': 0, 'total_trades': 0}
        
        profitable_trades = [t for t in self.performance_history if t.get('profit_loss', 0) > 0]
        win_rate = len(profitable_trades) / len(self.performance_history)
        avg_profit = sum(t.get('profit_loss', 0) for t in self.performance_history) / len(self.performance_history)
        
        return {
            'win_rate': win_rate,
            'avg_profit': avg_profit,
            'total_trades': len(self.performance_history),
            'recent_performance': self.performance_history[-10:] if len(self.performance_history) >= 10 else self.performance_history
        }

class MA13TrendFollowingStrategy(BaseStrategy):
    """13日均线趋势跟随策略（原有策略）"""
    
    def __init__(self):
        super().__init__("13日均线趋势跟随", StrategyType.TREND_FOLLOWING)
        self.ma_period = 13
        self.min_trend_strength = 0.6
    
    def is_applicable(self, market_context: MarketContext) -> bool:
        """适用于明确的趋势市场"""
        return (market_context.regime in [MarketRegime.TRENDING_UP, MarketRegime.TRENDING_DOWN] and
                market_context.trend_strength >= self.min_trend_strength and
                market_context.confidence >= 0.7)
    
    def generate_signal(self, market_data: Dict, market_context: MarketContext) -> Optional[TradingSignal]:
        """生成13日均线趋势跟随信号"""
        ma13_15m = market_data.get('ma13_15min', {})
        ma13_1h = market_data.get('ma13_1h', {})
        current_price = market_data.get('current_price', 0)
        
        if not all([ma13_15m, ma13_1h, current_price]):
            return None
        
        # 检查均线方向一致性
        direction_15m = ma13_15m.get('direction', 'FLAT')
        direction_1h = ma13_1h.get('direction', 'FLAT')
        
        if direction_15m != direction_1h or direction_15m == 'FLAT':
            return None
        
        # 检查价格与均线关系
        ma_value_15m = ma13_15m.get('value', 0)
        ma_value_1h = ma13_1h.get('value', 0)
        
        # 计算回踩机会
        distance_15m = abs(current_price - ma_value_15m) / ma_value_15m * 10000
        distance_1h = abs(current_price - ma_value_1h) / ma_value_1h * 10000
        
        # 判断是否有回踩入场机会
        if distance_15m <= 15 or distance_1h <= 20:  # 接近均线
            action = "BUY" if direction_15m == "UP" else "SELL"
            
            # 计算入场价格（考虑均线提前量）
            if action == "BUY":
                entry_price = min(ma_value_15m, ma_value_1h) + 0.0002  # 均线上方2点
                stop_loss = min(ma_value_15m, ma_value_1h) - 0.0015   # 均线下方15点
                take_profit = entry_price + (entry_price - stop_loss) * 2  # 1:2风险回报比
            else:
                entry_price = max(ma_value_15m, ma_value_1h) - 0.0002  # 均线下方2点
                stop_loss = max(ma_value_15m, ma_value_1h) + 0.0015   # 均线上方15点
                take_profit = entry_price - (stop_loss - entry_price) * 2  # 1:2风险回报比
            
            # 计算信号强度和置信度
            strength = min(market_context.trend_strength, 1.0)
            confidence = market_context.confidence * 0.9  # 稍微保守
            
            return TradingSignal(
                strategy_type=self.strategy_type,
                action=action,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                confidence=confidence,
                strength=strength,
                reasoning=f"13日均线{direction_15m}趋势，价格回踩入场机会",
                timeframe="15M+1H",
                risk_level="MEDIUM"
            )
        
        return None
    
    def calculate_position_size(self, signal: TradingSignal, account_info: Dict) -> float:
        """计算仓位大小"""
        base_size = 0.1
        
        # 根据信号强度调整
        strength_multiplier = 0.5 + (signal.strength * 0.5)
        
        # 根据置信度调整
        confidence_multiplier = 0.5 + (signal.confidence * 0.5)
        
        final_size = base_size * strength_multiplier * confidence_multiplier
        return max(min(final_size, 0.2), 0.01)

class SupportResistanceStrategy(BaseStrategy):
    """支撑阻力交易策略"""
    
    def __init__(self):
        super().__init__("支撑阻力交易", StrategyType.MEAN_REVERSION)
        self.min_sr_strength = 0.6
    
    def is_applicable(self, market_context: MarketContext) -> bool:
        """适用于震荡市场和明确的支撑阻力位"""
        return (market_context.regime == MarketRegime.RANGING or
                (market_context.support_resistance.get('support_strength', 0) >= self.min_sr_strength or
                 market_context.support_resistance.get('resistance_strength', 0) >= self.min_sr_strength))
    
    def generate_signal(self, market_data: Dict, market_context: MarketContext) -> Optional[TradingSignal]:
        """生成支撑阻力交易信号"""
        current_price = market_data.get('current_price', 0)
        sr_data = market_context.support_resistance
        
        support_levels = sr_data.get('support_levels', [])
        resistance_levels = sr_data.get('resistance_levels', [])
        
        # 寻找最近的支撑阻力位
        nearest_support = None
        nearest_resistance = None
        
        for level in support_levels:
            if level < current_price and (nearest_support is None or level > nearest_support):
                nearest_support = level
        
        for level in resistance_levels:
            if level > current_price and (nearest_resistance is None or level < nearest_resistance):
                nearest_resistance = level
        
        # 检查是否接近支撑位（做多机会）
        if nearest_support:
            distance_to_support = (current_price - nearest_support) / current_price * 10000
            if distance_to_support <= 10:  # 10点以内
                return TradingSignal(
                    strategy_type=self.strategy_type,
                    action="BUY",
                    entry_price=nearest_support + 0.0002,
                    stop_loss=nearest_support - 0.0008,
                    take_profit=nearest_resistance if nearest_resistance else current_price + 0.003,
                    confidence=0.7,
                    strength=sr_data.get('support_strength', 0.5),
                    reasoning=f"价格接近支撑位{nearest_support}，反弹机会",
                    timeframe="15M",
                    risk_level="MEDIUM"
                )
        
        # 检查是否接近阻力位（做空机会）
        if nearest_resistance:
            distance_to_resistance = (nearest_resistance - current_price) / current_price * 10000
            if distance_to_resistance <= 10:  # 10点以内
                return TradingSignal(
                    strategy_type=self.strategy_type,
                    action="SELL",
                    entry_price=nearest_resistance - 0.0002,
                    stop_loss=nearest_resistance + 0.0008,
                    take_profit=nearest_support if nearest_support else current_price - 0.003,
                    confidence=0.7,
                    strength=sr_data.get('resistance_strength', 0.5),
                    reasoning=f"价格接近阻力位{nearest_resistance}，回调机会",
                    timeframe="15M",
                    risk_level="MEDIUM"
                )
        
        return None
    
    def calculate_position_size(self, signal: TradingSignal, account_info: Dict) -> float:
        """计算仓位大小"""
        # 支撑阻力交易风险较高，使用较小仓位
        base_size = 0.08
        strength_multiplier = 0.5 + (signal.strength * 0.5)
        return max(min(base_size * strength_multiplier, 0.15), 0.02)

class BreakoutStrategy(BaseStrategy):
    """突破交易策略"""
    
    def __init__(self):
        super().__init__("突破交易", StrategyType.BREAKOUT)
        self.min_volatility_spike = 1.5
    
    def is_applicable(self, market_context: MarketContext) -> bool:
        """适用于突破行情"""
        return (market_context.regime in [MarketRegime.BREAKOUT_UP, MarketRegime.BREAKOUT_DOWN] or
                market_context.volatility_level == "HIGH")
    
    def generate_signal(self, market_data: Dict, market_context: MarketContext) -> Optional[TradingSignal]:
        """生成突破交易信号"""
        current_price = market_data.get('current_price', 0)
        sr_data = market_context.support_resistance
        
        resistance_levels = sr_data.get('resistance_levels', [])
        support_levels = sr_data.get('support_levels', [])
        
        # 检查向上突破
        for resistance in resistance_levels:
            if current_price > resistance and (current_price - resistance) / resistance * 10000 <= 5:
                return TradingSignal(
                    strategy_type=self.strategy_type,
                    action="BUY",
                    entry_price=current_price + 0.0002,
                    stop_loss=resistance - 0.0005,
                    take_profit=current_price + (current_price - resistance + 0.0005) * 2,
                    confidence=0.6,
                    strength=0.8,
                    reasoning=f"突破阻力位{resistance}，追涨机会",
                    timeframe="15M",
                    risk_level="HIGH"
                )
        
        # 检查向下突破
        for support in support_levels:
            if current_price < support and (support - current_price) / support * 10000 <= 5:
                return TradingSignal(
                    strategy_type=self.strategy_type,
                    action="SELL",
                    entry_price=current_price - 0.0002,
                    stop_loss=support + 0.0005,
                    take_profit=current_price - (support - current_price + 0.0005) * 2,
                    confidence=0.6,
                    strength=0.8,
                    reasoning=f"跌破支撑位{support}，追跌机会",
                    timeframe="15M",
                    risk_level="HIGH"
                )
        
        return None
    
    def calculate_position_size(self, signal: TradingSignal, account_info: Dict) -> float:
        """计算仓位大小"""
        # 突破交易风险很高，使用小仓位
        base_size = 0.06
        confidence_multiplier = 0.5 + (signal.confidence * 0.5)
        return max(min(base_size * confidence_multiplier, 0.12), 0.02)

class RSIDivergenceStrategy(BaseStrategy):
    """RSI背离反转策略"""
    
    def __init__(self):
        super().__init__("RSI背离反转", StrategyType.REVERSAL)
        self.rsi_overbought = 70
        self.rsi_oversold = 30
    
    def is_applicable(self, market_context: MarketContext) -> bool:
        """适用于超买超卖反转"""
        return market_context.regime in [MarketRegime.REVERSAL_BULLISH, MarketRegime.REVERSAL_BEARISH]
    
    def generate_signal(self, market_data: Dict, market_context: MarketContext) -> Optional[TradingSignal]:
        """生成RSI背离反转信号"""
        current_price = market_data.get('current_price', 0)
        rsi = market_data.get('rsi', 50)
        
        # 检查超卖反弹
        if rsi <= self.rsi_oversold:
            return TradingSignal(
                strategy_type=self.strategy_type,
                action="BUY",
                entry_price=current_price,
                stop_loss=current_price - 0.001,
                take_profit=current_price + 0.002,
                confidence=0.6,
                strength=0.7,
                reasoning=f"RSI超卖({rsi})，反弹机会",
                timeframe="15M",
                risk_level="HIGH"
            )
        
        # 检查超买回调
        if rsi >= self.rsi_overbought:
            return TradingSignal(
                strategy_type=self.strategy_type,
                action="SELL",
                entry_price=current_price,
                stop_loss=current_price + 0.001,
                take_profit=current_price - 0.002,
                confidence=0.6,
                strength=0.7,
                reasoning=f"RSI超买({rsi})，回调机会",
                timeframe="15M",
                risk_level="HIGH"
            )
        
        return None
    
    def calculate_position_size(self, signal: TradingSignal, account_info: Dict) -> float:
        """计算仓位大小"""
        # 反转交易风险较高，使用较小仓位
        return 0.05

class MultiStrategyFramework:
    """多策略融合框架"""
    
    def __init__(self):
        self.strategies = [
            MA13TrendFollowingStrategy(),
            SupportResistanceStrategy(),
            BreakoutStrategy(),
            RSIDivergenceStrategy()
        ]
        self.market_analyzer = None  # 将在后续实现
        self.signal_fusion_weights = {
            StrategyType.TREND_FOLLOWING: 0.4,
            StrategyType.MEAN_REVERSION: 0.25,
            StrategyType.BREAKOUT: 0.2,
            StrategyType.REVERSAL: 0.15
        }
    
    def analyze_market_context(self, market_data: Dict) -> MarketContext:
        """分析市场上下文（简化版，后续会详细实现）"""
        # 这里是简化的市场状态识别
        # 实际实现会更复杂
        
        current_price = market_data.get('current_price', 0)
        ma13_15m = market_data.get('ma13_15min', {})
        ma13_1h = market_data.get('ma13_1h', {})
        rsi = market_data.get('rsi', 50)
        
        # 简单的趋势判断
        direction_15m = ma13_15m.get('direction', 'FLAT')
        direction_1h = ma13_1h.get('direction', 'FLAT')
        
        if direction_15m == direction_1h == 'UP':
            regime = MarketRegime.TRENDING_UP
            trend_strength = 0.8
        elif direction_15m == direction_1h == 'DOWN':
            regime = MarketRegime.TRENDING_DOWN
            trend_strength = 0.8
        elif rsi >= 70:
            regime = MarketRegime.REVERSAL_BEARISH
            trend_strength = 0.3
        elif rsi <= 30:
            regime = MarketRegime.REVERSAL_BULLISH
            trend_strength = 0.3
        else:
            regime = MarketRegime.RANGING
            trend_strength = 0.4
        
        return MarketContext(
            regime=regime,
            volatility_level="NORMAL",
            trend_strength=trend_strength,
            support_resistance={'support_levels': [], 'resistance_levels': []},
            time_of_day="EUROPEAN",
            economic_events=[],
            liquidity_score=0.8,
            confidence=0.7
        )
    
    def generate_signals(self, market_data: Dict) -> List[TradingSignal]:
        """生成所有适用策略的信号"""
        market_context = self.analyze_market_context(market_data)
        signals = []
        
        for strategy in self.strategies:
            if strategy.enabled and strategy.is_applicable(market_context):
                signal = strategy.generate_signal(market_data, market_context)
                if signal:
                    signals.append(signal)
        
        return signals
    
    def fuse_signals(self, signals: List[TradingSignal]) -> Optional[TradingSignal]:
        """融合多个信号为最终决策"""
        if not signals:
            return None
        
        # 按策略类型分组
        signal_groups = {}
        for signal in signals:
            strategy_type = signal.strategy_type
            if strategy_type not in signal_groups:
                signal_groups[strategy_type] = []
            signal_groups[strategy_type].append(signal)
        
        # 计算加权信号
        weighted_signals = []
        for strategy_type, group_signals in signal_groups.items():
            weight = self.signal_fusion_weights.get(strategy_type, 0.1)
            
            # 选择该策略类型中最强的信号
            best_signal = max(group_signals, key=lambda s: s.confidence * s.strength)
            best_signal.confidence *= weight
            weighted_signals.append(best_signal)
        
        # 选择最终信号
        if weighted_signals:
            final_signal = max(weighted_signals, key=lambda s: s.confidence * s.strength)
            
            # 如果信号强度足够，返回信号
            if final_signal.confidence * final_signal.strength >= 0.4:
                return final_signal
        
        return None

# 使用示例
def test_multi_strategy_framework():
    """测试多策略框架"""
    framework = MultiStrategyFramework()
    
    # 模拟市场数据
    market_data = {
        'current_price': 1.1300,
        'ma13_15min': {'value': 1.1295, 'direction': 'UP'},
        'ma13_1h': {'value': 1.1290, 'direction': 'UP'},
        'rsi': 45,
        'support_resistance': {
            'support_levels': [1.1280, 1.1250],
            'resistance_levels': [1.1320, 1.1350]
        }
    }
    
    # 生成信号
    signals = framework.generate_signals(market_data)
    print(f"生成了 {len(signals)} 个信号")
    
    for signal in signals:
        print(f"策略: {signal.strategy_type.value}")
        print(f"行动: {signal.action}")
        print(f"置信度: {signal.confidence:.2f}")
        print(f"理由: {signal.reasoning}")
        print("-" * 40)
    
    # 融合信号
    final_signal = framework.fuse_signals(signals)
    if final_signal:
        print(f"最终决策: {final_signal.action}")
        print(f"策略: {final_signal.strategy_type.value}")
        print(f"置信度: {final_signal.confidence:.2f}")
    else:
        print("无明确交易信号")

if __name__ == "__main__":
    test_multi_strategy_framework()
