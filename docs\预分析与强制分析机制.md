# 预分析与强制分析机制

## 概述

外汇交易系统采用了两种分析模式：预分析和完整分析。预分析是一种轻量级的市场检查机制，用于决定是否需要进行完整分析；而完整分析则是深入的市场分析，可能会导致交易决策。

本文档详细说明了这两种分析模式的工作原理、互斥关系以及相关的代码实现。

## 最新优化

系统进行了以下优化：

1. **提高市场变化阈值**：
   - 短期价格变化阈值从0.05%提高到0.3%
   - 中期价格变化阈值从0.1%提高到0.5%
   - 价格波动性阈值从0.15%提高到1.0%
   - RSI指标超买超卖区域从70/30缩小到80/20

2. **降低信心度触发**：
   - LLM预分析信心度阈值从50提高到30，只有在信心度极低时才会覆盖LLM的"不需要分析"决策

3. **优化持仓评估频率**：
   - 持仓评估频率从每30分钟一次降低为每小时一次
   - 只在交易活跃时段（伦敦和纽约交易时段）进行持仓评估

4. **优化LLM提示词**：
   - 修改完整分析提示词，强调短线交易必须尊重趋势方向，避免逆势交易
   - 明确指出短线交易和趋势交易不是对立的，而是短线交易必须在趋势方向上进行
   - 强化奖惩机制在LLM提示词中的作用，使LLM更关注交易结果统计
   - 修改预分析提示词，增加无持仓时的灵活性，更积极地寻找交易机会

5. **预分析与完整分析交互循环**：
   - 完整分析现在会生成"下次预分析关注点"，指导预分析系统关注哪些市场变化
   - 预分析会检查这些关注点是否发生显著变化，如果是，则触发完整分析
   - 关注点包括价格水平、技术指标、市场形态和持仓风险等
   - 这种交互循环使系统更智能地决定何时需要进行完整分析

## 分析流程

### 预分析流程

1. 系统每隔固定的5分钟执行一次预分析（不再使用动态间隔）
2. 预分析通过详细的市场数据判断是否需要进行完整分析
3. 预分析使用JSON格式回答，确保解析准确性
4. 如果预分析决定需要进行完整分析，则触发完整分析（正常模式）
5. 如果预分析决定不需要进行完整分析，则等待5分钟后再次执行预分析

### 完整分析流程

1. 完整分析可以通过两种方式触发：
   - 预分析决定需要进行完整分析（正常模式）
   - 用户手动触发或系统调度触发（强制模式）
2. 在强制模式下，完整分析会完全跳过预分析步骤
3. 在正常模式下，完整分析会先执行预分析，然后根据预分析结果决定是否继续
4. 完整分析包括三轮分析：初始分析、详细分析和最终决策
5. 最终决策会生成交易指令，可能导致实际交易操作

## 互斥关系与步骤顺序保证

### 互斥关系

预分析和完整分析是互斥的，不能同时进行。具体规则如下：

1. 强制分析时，完全跳过预分析步骤
2. 预分析决定需要分析时，使用正常模式（不强制）进行完整分析
3. 任何时候只能有一个分析任务在运行（预分析或完整分析）
4. 如果已有分析任务在运行，新的分析请求会被跳过

### 步骤顺序保证

系统使用全局锁机制确保分析步骤按顺序执行，避免步骤混乱：

1. 使用`threading.Lock()`创建全局锁，确保同一时间只有一个分析任务在执行
2. 在开始分析前尝试获取锁，如果无法获取则跳过本次分析
3. 使用`try-finally`结构确保无论分析成功还是失败，都会释放锁
4. 这种设计避免了多线程执行导致的日志输出交错和步骤混乱问题
5. 每个分析步骤都有明确的日志标记，便于跟踪分析流程

## 数据不足与MT4交互失败处理

当数据不足或MT4交互失败时，系统会中断分析流程：

### 数据不足处理

1. 系统对K线数据有严格的要求：
   - 15分钟周期需要至少20根K线
   - 1小时周期需要至少20根K线
   - 1分钟原始数据需要足够多（根据聚合周期动态计算）
2. 如果数据不足，系统会记录错误并中断分析，而不是使用不完整的数据进行分析
3. 系统会在日志中明确指出数据不足的具体原因和数量
4. 这种设计确保了分析结果的可靠性，避免了基于不足数据的错误决策

### MT4交互失败处理

1. 在获取分析数据时，如果MT4交互失败（如无法获取价格、K线数据等），会返回`None`
2. 分析函数会检查数据是否为`None`，如果是，则中断分析
3. 系统会记录详细的错误信息，便于排查问题
4. 这样可以避免在数据不足或MT4连接异常的情况下进行无意义的分析

## 日志分类

系统使用了清晰的日志分类，便于区分不同类型的日志：

1. `[预分析]` - 预分析相关的日志
2. `[完整分析]` - 完整分析相关的日志
3. `[错误]` - 错误信息
4. `[警告]` - 警告信息

## 关键代码实现

### 全局锁机制实现

```python
# 全局锁定义
analysis_lock = threading.Lock()

# 在market_change_callback函数中使用锁
def market_change_callback(change_reason, force_analysis=True):
    # 尝试获取全局锁，如果无法获取，说明已有分析任务在执行
    if not analysis_lock.acquire(blocking=False):
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] [错误] 已有分析任务正在运行（锁被占用），跳过本次分析')
        return

    try:
        # 分析代码...
    except Exception as error:
        # 错误处理...
    finally:
        # 无论成功还是失败，都确保释放锁
        analysis_lock.release()
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] [完整分析] 已释放分析锁')
```

### 预分析与完整分析的互斥关系

```python
# 在perform_multi_round_analysis函数中
if force_analysis:
    now = datetime.now()
    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] DEBUG: 强制执行完整分析，完全跳过预分析')
    should_analyze = True
    reason = "强制执行完整分析"
    next_interval = 15  # 强制分析后默认15分钟再次检查
else:
    # 预分析：判断是否需要执行完整分析
    now = datetime.now()
    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] DEBUG: 调用should_perform_analysis')
    should_analyze, reason, next_interval = should_perform_analysis(data)
```

### 首次预分析跳过机制

为了避免在刚执行完强制分析后立即执行预分析，系统会在启动时设置一个延迟：

```python
# 如果刚刚执行过强制分析，则设置下次分析时间为5分钟后
# 这样可以避免在刚执行完强制分析后立即执行预分析
if run_immediately:
    next_analysis_time = datetime.now() + timedelta(minutes=5)
    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 已执行过强制分析，下次预分析时间设置为: {next_analysis_time.strftime("%Y-%m-%d %H:%M:%S")}')
    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 首次预分析将在5分钟后执行，避免与强制分析冲突')
else:
    # 否则立即执行预分析
    next_analysis_time = datetime.now()
    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 立即执行预分析')
```

### 防止多个任务同时运行

系统在启动新任务前会先停止现有任务，确保同一时间只有一个分析任务在运行：

```python
# 如果调度器已经初始化，先停止现有任务
if scheduler_initialized and tasks['realtime_analysis']['detector_running']:
    print('实时分析任务已经初始化，先停止现有任务')
    # 停止市场变化检测
    tasks['realtime_analysis']['detector_running'] = False
    print('已停止市场变化检测')

    # 等待任务完成
    if tasks['realtime_analysis']['running'] and tasks['realtime_analysis']['thread']:
        print('等待正在运行的实时分析任务完成...')
        tasks['realtime_analysis']['thread'].join(timeout=10)

    # 重置任务状态
    tasks['realtime_analysis']['running'] = False
    tasks['realtime_analysis']['detector_running'] = False
    print('现有任务已停止，准备启动新任务')
```

### 增强预分析数据采集和决策逻辑

系统增加了数据采集范围，从最近2根K线扩展到最近10根K线，并计算多种时间周期的价格变化：

```python
# 获取基本市场数据
currency_pair = data.get('symbol', 'EURUSD')
# 增加数据采集范围，取最近10根K线而不是2根
timeframe15m_all = data.get('timeframe15m', [])
timeframe15m = timeframe15m_all[-10:] if len(timeframe15m_all) >= 10 else timeframe15m_all
indicators = data.get('indicators', {})
positions = data.get('positions', [])

# 计算短期价格变化（最近两根K线）
short_term_price_change = abs(current_price - previous_price)
short_term_price_change_percent = (short_term_price_change / previous_price) * 100

# 计算中期价格变化（如果有足够的数据）
medium_term_price_change_percent = 0
if len(timeframe15m) >= 5:
    medium_term_price_change = abs(current_price - float(timeframe15m[-5]['close']))
    medium_term_price_change_percent = (medium_term_price_change / float(timeframe15m[-5]['close'])) * 100

# 计算长期价格变化（如果有足够的数据）
long_term_price_change_percent = 0
if len(timeframe15m) >= 10:
    long_term_price_change = abs(current_price - float(timeframe15m[0]['close']))
    long_term_price_change_percent = (long_term_price_change / float(timeframe15m[0]['close'])) * 100

# 计算价格波动性（最高价和最低价之间的差距）
price_volatility = 0
if len(timeframe15m) >= 5:
    highs = [float(k['high']) for k in timeframe15m[-5:]]
    lows = [float(k['low']) for k in timeframe15m[-5:]]
    price_volatility = (max(highs) - min(lows)) / min(lows) * 100
```

系统还增强了备用解析方案，使用多种市场指标来判断是否需要分析：

```python
# 如果解析结果是不需要分析，但市场有明显变化，则覆盖为需要分析
if not should_analyze:
    try:
        # 使用多种市场指标来判断是否需要分析
        analyze_needed = False
        analysis_reason = ""

        # 1. 短期价格变化（最敏感）- 大幅提高阈值，减少敏感度
        if short_term_price_change_percent > 0.3:  # 从0.05%提高到0.3%
            analyze_needed = True
            analysis_reason = f"短期价格变化{short_term_price_change_percent:.4f}%超过阈值"
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] DEBUG: 备用解析: {analysis_reason}，覆盖为需要分析')

        # 2. 中期价格变化 - 大幅提高阈值，减少敏感度
        elif medium_term_price_change_percent > 0.5:  # 从0.1%提高到0.5%
            analyze_needed = True
            analysis_reason = f"中期价格变化{medium_term_price_change_percent:.4f}%超过阈值"
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] DEBUG: 备用解析: {analysis_reason}，覆盖为需要分析')

        # 3. 价格波动性 - 极大幅提高阈值，减少敏感度
        elif price_volatility > 1.0:  # 从0.15%提高到1.0%
            analyze_needed = True
            analysis_reason = f"价格波动性{price_volatility:.4f}%超过阈值"
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] DEBUG: 备用解析: {analysis_reason}，覆盖为需要分析')

        # 4. RSI指标（如果可用）- 进一步缩小超买超卖区域范围
        elif indicators.get('rsi') is not None:
            rsi = float(indicators.get('rsi'))
            if rsi > 80 or rsi < 20:  # 从70/30改为80/20
                analyze_needed = True
                analysis_reason = f"RSI值{rsi}处于极端超买/超卖区域"
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] DEBUG: 备用解析: {analysis_reason}，覆盖为需要分析')

        # 如果需要分析，更新状态
        if analyze_needed:
            should_analyze = True
            reason = analysis_reason
            next_interval = 5  # 市场变化明显，设置较短的间隔
        else:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] DEBUG: 备用解析: 所有指标均未超过阈值，保持不分析')
    except Exception as e:
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] DEBUG: 备用解析失败: {e}')
        traceback.print_exc()

# 添加强制分析逻辑：如果有持仓，每隔一段时间强制分析一次，但频率降低
if not should_analyze and positions:
    # 获取当前时间的分钟数和小时数
    current_minute = datetime.now().minute
    current_hour = datetime.now().hour
    # 只在每小时的0分时强制分析持仓（从每30分钟一次改为每小时一次）
    # 并且只在交易活跃时段进行（伦敦和纽约交易时段）
    active_trading_hours = [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]  # 活跃交易时段（UTC+8）
    if current_minute == 0 and current_hour in active_trading_hours:
        should_analyze = True
        reason = "检测到有持仓，整点评估时间点"
        next_interval = 60  # 有持仓时，每60分钟分析一次
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤1.2: 持仓定期评估触发分析')
    else:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤1.2: 持仓检测未触发分析，非评估时间点')
```

### 数据不足处理

```python
# 在get_aggregated_klines函数中
# 检查数据是否足够
min_required = period_minutes * 20  # 确保至少有20根K线的数据
if len(min_data) < min_required:
    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告：1分钟K线数据不足，只有{len(min_data)}条，需要至少{min_required}条')

    # 记录错误
    log_error(
        error_type=ErrorType.DATA_ERROR,
        message=f'1分钟K线数据不足，只有{len(min_data)}条，需要至少{min_required}条',
        details={'timestamp': datetime.now().isoformat(), 'period_minutes': period_minutes, 'count': count, 'actual': len(min_data), 'required': min_required},
        operation=OperationType.DATA_FETCH
    )

    return []  # 数据不足时返回空列表，中断分析
```

### MT4交互失败处理

```python
# 在get_analysis_data函数中
# 获取当前价格 - 这是必须的
current_price = None
try:
    # MT4交互代码...
    if current_price <= 0:
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 获取到的价格无效: {current_price}')

        # 记录错误
        log_error(
            error_type=ErrorType.MT4_ERROR,
            message='获取到的价格无效',
            details={'timestamp': datetime.now().isoformat(), 'price': current_price},
            operation=OperationType.DATA_FETCH
        )

        return None
except Exception as price_error:
    # 错误处理...
    return None  # 价格是必须的，如果获取失败，中断分析
```

### 预分析决定是否进行完整分析

```python
# 在run_market_analyzer函数中
if should_analyze:
    # 触发分析（预分析决定需要分析，使用正常模式，不强制跳过预分析）
    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] [预分析] 步骤5: 触发完整分析')
    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] [预分析] 调用market_change_callback函数（正常模式）')
    market_change_callback(reason, force_analysis=False)  # 预分析决定需要分析，使用正常模式
    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] [预分析] 完整分析执行完毕')
else:
    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] [预分析] 步骤5: 跳过完整分析')
    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] [预分析] 原因: {reason}')
```

## 预分析决策标准

预分析使用以下标准来决定是否需要进行完整分析：

1. **市场变化阈值**：
   - 短期价格变化（最近2根15分钟K线）超过0.3%
   - 中期价格变化（最近5根15分钟K线）超过0.5%
   - 价格波动性（最近5根K线最高最低价差）超过1.0%
   - RSI指标超过80或低于20

2. **持仓状态**：
   - 如果有持仓，每小时的整点（在活跃交易时段）强制进行分析
   - 无持仓时，采用更灵活的标准，积极寻找交易机会：
     * 价格波动超过0.2%
     * 技术指标出现潜在交易信号
     * 价格接近关键支撑/阻力位
     * 市场形态开始变化
     * 出现潜在的趋势形成迹象

3. **LLM信心度**：
   - 如果LLM的信心度低于30且不建议分析，会覆盖为需要分析

4. **上次分析的关注点**：
   - 检查上次完整分析指定的关注点是否发生显著变化
   - 价格关注点：当前价格是否接近或突破关键价格水平（0.1%以内）
   - RSI关注点：RSI是否进入或离开超买超卖区域
   - MACD关注点：是否出现金叉或死叉信号
   - 趋势关注点：价格变化是否足够大，可能影响趋势或形态（>0.5%）

## 预分析与完整分析交互循环

系统实现了预分析与完整分析之间的智能交互循环，使分析过程更加高效和精准：

### 完整分析生成关注点

1. 完整分析在生成交易指令的同时，会生成3-5个预分析关注点
2. 这些关注点包括：
   - 具体的价格水平（如支撑位、阻力位、突破点等）
   - 技术指标的关键值或交叉点（如RSI超买超卖区域、MACD交叉等）
   - 市场形态或趋势的变化点（如趋势线突破、形态完成等）
   - 持仓相关的风险点（如接近止损/止盈点等）
3. 每个关注点都包含以下信息：
   - 指标名称和当前值
   - 为什么这个指标重要
   - 这个指标的变化可能意味着什么
   - 如果这个指标发生显著变化，是否应该触发新的完整分析

### 预分析检查关注点变化

1. 预分析会从上次完整分析中获取关注点列表
2. 对每个关注点进行检查，判断是否发生显著变化：
   - 价格关注点：检查当前价格是否接近或突破关键价格水平（0.1%以内）
   - RSI关注点：检查RSI是否进入或离开超买超卖区域
   - MACD关注点：检查是否出现金叉或死叉信号
   - 趋势关注点：检查价格变化是否足够大，可能影响趋势或形态
3. 如果任何关注点发生显著变化，预分析会触发完整分析

### 交互循环的优势

1. **更智能的分析触发**：系统不再依赖固定的阈值，而是根据市场具体情况动态调整关注点
2. **减少不必要的分析**：只有在关键市场变化时才触发完整分析，避免资源浪费
3. **持续学习和适应**：每次完整分析都会更新关注点，使系统能够适应不断变化的市场环境
4. **更精准的风险管理**：特别关注与当前持仓相关的风险点，及时发现潜在风险

### 实现方式

1. 完整分析提示词中添加了"下次预分析关注点"部分，引导LLM生成关注点
2. 预分析提示词中添加了上次分析关注点的信息，引导LLM评估这些关注点的变化
3. 系统代码中添加了关注点的存储、提取和评估逻辑
4. 使用JSON格式存储关注点，确保数据结构清晰和一致

## 最佳实践

1. **强制分析使用场景**：手动触发分析、定时任务触发分析等需要立即执行的场景
2. **预分析使用场景**：实时市场监控，根据市场变化决定是否需要进行完整分析
3. **MT4交互**：确保MT4客户端连接正常，否则分析将无法进行
4. **日志监控**：通过日志分类，可以快速定位问题所在

## 注意事项

1. 预分析和完整分析是互斥的，不能同时进行
2. 强制分析会完全跳过预分析步骤
3. MT4交互失败会导致分析中断
4. 预分析固定每5分钟执行一次，不再使用动态间隔
5. 预分析使用JSON格式回答，确保解析准确性
6. 系统对K线数据有严格的要求，数据不足时会中断分析
7. 全局锁机制确保分析步骤按顺序执行，避免步骤混乱
8. 系统会记录详细的错误信息，便于排查问题
9. 每个分析步骤都有明确的日志标记，便于跟踪分析流程
10. 移除了倒计时显示，简化系统逻辑
11. 预分析关注点是动态生成的，每次完整分析都会更新
12. 如果没有上次分析的关注点（如首次分析），系统会使用默认的市场变化阈值
13. 关注点检查逻辑会根据不同类型的指标使用不同的判断标准
14. 预分析关注点的存储和提取使用JSON格式，确保数据结构一致性
