"""
测试ZeroMQ服务器
"""
import zmq
import time
import json
import threading
import sys
from datetime import datetime

# 确保输出不会被缓存
# sys.stdout.reconfigure(line_buffering=True)  # 这行在某些Python版本中可能不支持
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 脚本开始执行')
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Python版本: {sys.version}')
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ZeroMQ版本: {zmq.__version__}')

# 服务器地址
SERVER_ADDRESS = "tcp://*:5555"
CLIENT_ADDRESS = "tcp://localhost:5555"

# 全局变量
server_running = False
server_thread = None

def start_server():
    """启动ZeroMQ服务器"""
    global server_running

    try:
        # 创建ZeroMQ上下文和套接字
        context = zmq.Context()
        socket = context.socket(zmq.REP)

        # 绑定地址
        socket.bind(SERVER_ADDRESS)

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ZeroMQ服务器已启动，监听地址: {SERVER_ADDRESS}')
        server_running = True

        while server_running:
            try:
                # 接收请求（设置超时，以便能够响应停止命令）
                try:
                    socket.setsockopt(zmq.RCVTIMEO, 1000)  # 1秒超时
                    message = socket.recv_string()
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 收到请求: {message}')

                    # 解析请求
                    request = json.loads(message)

                    # 处理请求
                    action = request.get('action', '')

                    if action == 'ping':
                        response = {
                            'status': 'success',
                            'message': 'pong',
                            'server_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
                    elif action == 'REGISTER':
                        response = {
                            'status': 'success',
                            'message': '注册成功',
                            'user_id': '123456',
                            'username': request.get('username', ''),
                            'auth_code': 'TEST-AUTH-CODE-123456'
                        }
                    elif action == 'LOGIN_WITH_CREDENTIALS':
                        response = {
                            'status': 'success',
                            'message': '登录成功',
                            'user_id': '123456',
                            'username': request.get('username', ''),
                            'auth_code': 'TEST-AUTH-CODE-123456'
                        }
                    elif action == 'LOGIN':
                        response = {
                            'status': 'success',
                            'message': '授权验证成功',
                            'user_id': '123456',
                            'username': 'testuser',
                            'account_type': '标准账户',
                            'expiry_date': '2025-05-13 15:00:00'
                        }
                    elif action == 'GET_SIGNALS':
                        response = {
                            'status': 'success',
                            'message': '获取交易信号成功',
                            'signals': [
                                {
                                    'id': '789012',
                                    'symbol': 'EURUSD',
                                    'direction': 'BUY',
                                    'price': 1.0850,
                                    'sl': 1.0800,
                                    'tp': 1.0900,
                                    'lot': 0.1,
                                    'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                }
                            ]
                        }
                    else:
                        response = {
                            'status': 'error',
                            'message': f'未知的操作: {action}'
                        }

                    # 发送响应
                    socket.send_string(json.dumps(response))
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 已发送响应: {response}')

                except zmq.error.Again:
                    # 超时，继续循环
                    continue

            except Exception as e:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 处理请求时出错: {e}')

                # 发送错误响应
                try:
                    socket.send_string(json.dumps({
                        'status': 'error',
                        'message': f'服务器内部错误: {str(e)}'
                    }))
                except:
                    pass
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 启动ZeroMQ服务器失败: {e}')
        server_running = False
    finally:
        # 清理资源
        try:
            socket.close()
            context.term()
        except:
            pass

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ZeroMQ服务器已停止')
        server_running = False

def start_server_thread():
    """在单独的线程中启动服务器"""
    global server_thread

    if server_thread and server_thread.is_alive():
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ZeroMQ服务器已经在运行中')
        return

    server_thread = threading.Thread(target=start_server)
    server_thread.daemon = True
    server_thread.start()

    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ZeroMQ服务器线程已启动')

def stop_server():
    """停止服务器"""
    global server_running

    server_running = False
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 正在停止ZeroMQ服务器...')

    # 等待服务器线程结束
    if server_thread and server_thread.is_alive():
        server_thread.join(timeout=5)
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ZeroMQ服务器线程已停止')

def test_client():
    """测试客户端"""
    try:
        # 创建ZeroMQ上下文和套接字
        context = zmq.Context()
        socket = context.socket(zmq.REQ)

        # 设置超时时间
        socket.setsockopt(zmq.RCVTIMEO, 5000)  # 5秒超时

        # 连接到服务器
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 正在连接到服务器: {CLIENT_ADDRESS}')
        socket.connect(CLIENT_ADDRESS)

        # 发送ping请求
        request = {
            'action': 'ping',
            'requestId': 'test-ping'
        }

        request_str = json.dumps(request)
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 发送请求: {request_str}')
        socket.send_string(request_str)

        # 接收响应
        response_str = socket.recv_string()
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 收到响应: {response_str}')

        # 解析响应
        response = json.loads(response_str)

        # 关闭套接字和上下文
        socket.close()
        context.term()

        return response
    except zmq.error.Again:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器响应超时')
        return {'status': 'error', 'message': '服务器响应超时'}
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试客户端失败: {e}')
        return {'status': 'error', 'message': f'测试客户端失败: {e}'}

def main():
    """主函数"""
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 开始测试ZeroMQ服务器')

    # 启动服务器
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 启动ZeroMQ服务器')
    start_server_thread()

    # 等待服务器启动
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 等待服务器启动...')
    time.sleep(2)

    # 测试客户端
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试客户端')
    response = test_client()

    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试结果: {response}')

    # 等待用户输入以保持服务器运行
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器正在运行，按Enter键停止...')
    input()

    # 停止服务器
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 停止ZeroMQ服务器')
    stop_server()

    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试完成')

if __name__ == '__main__':
    main()
