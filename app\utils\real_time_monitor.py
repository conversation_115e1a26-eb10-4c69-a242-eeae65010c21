#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控数据收集器
负责收集系统各项指标数据，为监控面板提供数据支持
"""

import os
import json
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque

@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_total_mb: float
    disk_percent: float
    disk_used_gb: float
    disk_total_gb: float
    network_sent_mb: float
    network_recv_mb: float
    process_count: int
    system_status: str
    market_status: str = 'unknown'
    beijing_time: str = ''
    next_open_time: str = ''  # healthy, warning, critical

@dataclass
class TradingMetrics:
    """交易指标数据类"""
    timestamp: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_profit: float
    total_loss: float
    net_profit: float
    profit_factor: float
    max_drawdown: float
    current_positions: int
    pending_orders: int
    risk_level: str
    account_balance: float
    account_equity: float

@dataclass
class AnalysisMetrics:
    """分析指标数据类"""
    timestamp: str
    total_analyses: int
    successful_analyses: int
    failed_analyses: int
    success_rate: float
    avg_response_time: float
    total_tokens: int
    total_cost: float
    pre_analysis_count: int
    full_analysis_count: int
    json_parse_errors: int
    api_failures: int

@dataclass
class AlertInfo:
    """告警信息数据类"""
    timestamp: str
    level: str  # info, warning, error, critical
    category: str  # system, trading, analysis, mt4
    message: str
    details: Optional[str] = None

class RealTimeMonitor:
    """实时监控数据收集器"""

    def __init__(self, data_dir: str = "app/data/monitoring"):
        self.data_dir = data_dir
        self.monitoring = False
        self.monitor_thread = None

        # 数据存储
        self.system_metrics_history = deque(maxlen=1440)  # 24小时数据（每分钟一个点）
        self.trading_metrics_history = deque(maxlen=288)   # 24小时数据（每5分钟一个点）
        self.analysis_metrics_history = deque(maxlen=288)  # 24小时数据（每5分钟一个点）
        self.alerts_history = deque(maxlen=1000)           # 最近1000条告警

        # 计数器
        self.counters = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'json_parse_errors': 0,
            'api_failures': 0,
            'total_tokens': 0,
            'total_cost': 0.0,
            'pre_analysis_count': 0,
            'full_analysis_count': 0
        }

        # 响应时间记录
        self.response_times = deque(maxlen=100)

        # 网络统计基线
        self.network_baseline = None

        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)

        # 加载历史数据
        self._load_historical_data()

    def start_monitoring(self, interval: int = 60):
        """开始监控"""
        if self.monitoring:
            return

        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 实时监控已启动，检查间隔: {interval}秒")

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self._save_historical_data()
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 实时监控已停止")

    def _monitoring_loop(self, interval: int):
        """监控循环"""
        while self.monitoring:
            try:
                # 收集系统指标
                system_metrics = self._collect_system_metrics()
                self.system_metrics_history.append(system_metrics)

                # 每5分钟收集一次交易和分析指标
                if len(self.system_metrics_history) % 5 == 0:
                    trading_metrics = self._collect_trading_metrics()
                    analysis_metrics = self._collect_analysis_metrics()

                    self.trading_metrics_history.append(trading_metrics)
                    self.analysis_metrics_history.append(analysis_metrics)

                # 检查告警条件
                self._check_alerts(system_metrics)

                # 定期保存数据
                if len(self.system_metrics_history) % 10 == 0:
                    self._save_historical_data()

                time.sleep(interval)

            except Exception as e:
                self.add_alert('error', 'system', f'监控循环出错: {str(e)}')
                time.sleep(interval)

    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / (1024 * 1024)
            memory_total_mb = memory.total / (1024 * 1024)

            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_used_gb = disk.used / (1024 * 1024 * 1024)
            disk_total_gb = disk.total / (1024 * 1024 * 1024)

            # 网络使用情况
            network = psutil.net_io_counters()
            if self.network_baseline is None:
                self.network_baseline = network
                network_sent_mb = 0
                network_recv_mb = 0
            else:
                network_sent_mb = (network.bytes_sent - self.network_baseline.bytes_sent) / (1024 * 1024)
                network_recv_mb = (network.bytes_recv - self.network_baseline.bytes_recv) / (1024 * 1024)

            # 进程数量
            process_count = len(psutil.pids())

            # 系统状态判断
            system_status = self._determine_system_status(cpu_percent, memory_percent, disk_percent)

            # 市场时间状态
            market_status, beijing_time, next_open_time = self._get_market_time_status()

            return SystemMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                memory_total_mb=memory_total_mb,
                disk_percent=disk_percent,
                disk_used_gb=disk_used_gb,
                disk_total_gb=disk_total_gb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                process_count=process_count,
                system_status=system_status,
                market_status=market_status,
                beijing_time=beijing_time,
                next_open_time=next_open_time
            )

        except Exception as e:
            self.add_alert('error', 'system', f'收集系统指标失败: {str(e)}')
            # 返回默认值
            return SystemMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_percent=0, memory_percent=0, memory_used_mb=0, memory_total_mb=0,
                disk_percent=0, disk_used_gb=0, disk_total_gb=0,
                network_sent_mb=0, network_recv_mb=0, process_count=0,
                system_status='unknown',
                market_status='unknown',
                beijing_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S (本地时间)'),
                next_open_time='时间计算失败'
            )

    def _determine_system_status(self, cpu_percent: float, memory_percent: float, disk_percent: float) -> str:
        """判断系统状态"""
        if cpu_percent > 90 or memory_percent > 90 or disk_percent > 95:
            return 'critical'
        elif cpu_percent > 70 or memory_percent > 70 or disk_percent > 85:
            return 'warning'
        else:
            return 'healthy'

    def _get_market_time_status(self) -> tuple:
        """获取市场时间状态"""
        try:
            import pytz

            # 北京时间
            beijing_tz = pytz.timezone('Asia/Shanghai')
            beijing_time = datetime.now(beijing_tz)
            beijing_time_str = beijing_time.strftime('%Y-%m-%d %H:%M:%S (北京时间)')

            # 获取当前是周几 (0=周一, 6=周日)
            weekday = beijing_time.weekday()
            hour = beijing_time.hour

            # MT4外汇市场开盘时间（北京时间）
            # 夏令时：周一早上5:00 - 周六早上5:00
            # 冬令时：周一早上6:00 - 周六早上6:00
            # 这里简化处理，使用夏令时时间

            if weekday == 0:  # 周一
                if hour >= 5:
                    market_status = 'open'
                    next_open_time = '市场已开盘'
                else:
                    market_status = 'closed'
                    next_open_time = f'今日 05:00 开盘'
            elif weekday in [1, 2, 3, 4]:  # 周二到周五
                market_status = 'open'
                next_open_time = '市场开盘中'
            elif weekday == 5:  # 周六
                if hour < 5:
                    market_status = 'open'
                    next_open_time = '今日 05:00 休市'
                else:
                    market_status = 'closed'
                    next_open_time = '下周一 05:00 开盘'
            else:  # 周日
                market_status = 'closed'
                next_open_time = '明日 05:00 开盘'

            return market_status, beijing_time_str, next_open_time

        except Exception as e:
            # 如果时区处理失败，使用简化版本
            now = datetime.now()
            beijing_time_str = now.strftime('%Y-%m-%d %H:%M:%S (本地时间)')
            return 'unknown', beijing_time_str, '时间计算失败'

    def _collect_trading_metrics(self) -> TradingMetrics:
        """收集交易指标"""
        try:
            # 从交易结果文件读取数据
            trade_results_file = "app/data/trade_results.json"
            trades_list = []

            if os.path.exists(trade_results_file):
                try:
                    with open(trade_results_file, 'r', encoding='utf-8') as f:
                        trades_list = json.load(f)
                        # 确保是列表格式
                        if not isinstance(trades_list, list):
                            trades_list = []
                except:
                    trades_list = []

            # 过滤掉测试数据，只保留真实交易数据
            # 排除order_id为"test_order_1"或"0"的测试数据
            real_trades = []
            for trade in trades_list:
                order_id = trade.get('order_id', '')
                # 排除明显的测试数据
                if order_id not in ['test_order_1', '0', 'test_order', '']:
                    # 检查是否是近期的交易（最近7天）
                    try:
                        open_time = datetime.fromisoformat(trade.get('open_time', ''))
                        if open_time > datetime.now() - timedelta(days=7):
                            real_trades.append(trade)
                    except:
                        # 如果时间解析失败，跳过这个交易
                        continue

            # 从真实交易列表计算指标
            total_trades = len(real_trades)
            winning_trades = 0
            losing_trades = 0
            total_profit = 0.0
            total_loss = 0.0
            current_positions = 0
            closed_trades = 0  # 只统计已平仓的交易

            for trade in real_trades:
                if trade.get('status') == 'CLOSED':
                    closed_trades += 1
                    profit = trade.get('profit_loss', 0.0)
                    if profit is not None:
                        if profit > 0:
                            winning_trades += 1
                            total_profit += profit
                        elif profit < 0:
                            losing_trades += 1
                            total_loss += abs(profit)
                elif trade.get('status') == 'OPEN':
                    current_positions += 1

            # 计算衍生指标（基于已平仓交易）
            win_rate = (winning_trades / closed_trades * 100) if closed_trades > 0 else 0
            net_profit = total_profit - total_loss
            profit_factor = (total_profit / total_loss) if total_loss > 0 else 0

            # 计算最大回撤
            max_drawdown = self._calculate_max_drawdown(trades_list)

            # 获取挂单信息
            pending_orders = total_trades - closed_trades - current_positions
            if pending_orders < 0:
                pending_orders = 0

            # 风险等级评估
            if max_drawdown > 20:
                risk_level = 'HIGH'
            elif max_drawdown > 10:
                risk_level = 'MEDIUM'
            else:
                risk_level = 'LOW'

            # 账户信息
            account_balance = 10000.0  # 初始余额
            account_equity = account_balance + net_profit

            return TradingMetrics(
                timestamp=datetime.now().isoformat(),
                total_trades=closed_trades,  # 只显示已平仓的交易数
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                total_profit=total_profit,
                total_loss=total_loss,
                net_profit=net_profit,
                profit_factor=profit_factor,
                max_drawdown=max_drawdown,
                current_positions=current_positions,
                pending_orders=pending_orders,
                risk_level=risk_level,
                account_balance=account_balance,
                account_equity=account_equity
            )

        except Exception as e:
            self.add_alert('error', 'trading', f'收集交易指标失败: {str(e)}')
            return TradingMetrics(
                timestamp=datetime.now().isoformat(),
                total_trades=0, winning_trades=0, losing_trades=0, win_rate=0,
                total_profit=0, total_loss=0, net_profit=0, profit_factor=0,
                max_drawdown=0, current_positions=0, pending_orders=0,
                risk_level='UNKNOWN', account_balance=0, account_equity=0
            )

    def _collect_analysis_metrics(self) -> AnalysisMetrics:
        """收集分析指标"""
        try:
            # 从专业token统计系统获取准确数据
            try:
                from app.utils.token_statistics import load_token_stats
                token_stats = load_token_stats()

                # 使用专业统计系统的数据
                total_tokens = token_stats.get('total_tokens', 0)
                total_cost = token_stats.get('total_cost', 0.0)

                # 获取分析类型统计
                analysis_type_stats = token_stats.get('analysis_type_stats', {})
                pre_analysis_count = analysis_type_stats.get('pre_analysis', {}).get('count', 0)
                full_analysis_count = analysis_type_stats.get('full_analysis', {}).get('count', 0)

                # 总分析次数 = 预分析次数 + 完整分析次数
                total_analyses = pre_analysis_count + full_analysis_count

                # 假设所有分析都成功（可以后续优化）
                successful_analyses = total_analyses
                failed_analyses = 0

            except Exception as e:
                print(f"获取专业token统计失败，使用内存计数器: {e}")
                # 回退到内存计数器，但优先使用专业统计的基础数据
                total_analyses = self.counters['total_analyses']
                successful_analyses = self.counters['successful_analyses']
                failed_analyses = self.counters['failed_analyses']

                # 尝试从专业统计获取基础token数据
                try:
                    import json
                    token_file = "app/data/token_statistics.json"
                    if os.path.exists(token_file):
                        with open(token_file, 'r', encoding='utf-8') as f:
                            token_data = json.load(f)
                            total_tokens = token_data.get('total_tokens', self.counters['total_tokens'])
                            total_cost = token_data.get('total_cost', self.counters['total_cost'])

                            # 获取分析类型统计
                            analysis_type_stats = token_data.get('analysis_type_stats', {})
                            pre_analysis_count = analysis_type_stats.get('pre_analysis', {}).get('count', self.counters['pre_analysis_count'])
                            full_analysis_count = analysis_type_stats.get('full_analysis', {}).get('count', self.counters['full_analysis_count'])

                            print(f"使用专业token文件数据: total_tokens={total_tokens}, total_cost={total_cost}")
                    else:
                        total_tokens = self.counters['total_tokens']
                        total_cost = self.counters['total_cost']
                        pre_analysis_count = self.counters['pre_analysis_count']
                        full_analysis_count = self.counters['full_analysis_count']
                        print(f"使用内存计数器数据: total_tokens={total_tokens}, total_cost={total_cost}")
                except:
                    total_tokens = self.counters['total_tokens']
                    total_cost = self.counters['total_cost']
                    pre_analysis_count = self.counters['pre_analysis_count']
                    full_analysis_count = self.counters['full_analysis_count']
                    print(f"使用内存计数器数据: total_tokens={total_tokens}, total_cost={total_cost}")

                print(f"分析次数: pre={pre_analysis_count}, full={full_analysis_count}, total={total_analyses}")

            # 计算成功率
            success_rate = (successful_analyses / total_analyses * 100) if total_analyses > 0 else 0

            # 计算平均响应时间
            avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0

            return AnalysisMetrics(
                timestamp=datetime.now().isoformat(),
                total_analyses=total_analyses,
                successful_analyses=successful_analyses,
                failed_analyses=failed_analyses,
                success_rate=success_rate,
                avg_response_time=avg_response_time,
                total_tokens=total_tokens,
                total_cost=total_cost,
                pre_analysis_count=pre_analysis_count,
                full_analysis_count=full_analysis_count,
                json_parse_errors=self.counters['json_parse_errors'],
                api_failures=self.counters['api_failures']
            )

        except Exception as e:
            self.add_alert('error', 'analysis', f'收集分析指标失败: {str(e)}')
            return AnalysisMetrics(
                timestamp=datetime.now().isoformat(),
                total_analyses=0, successful_analyses=0, failed_analyses=0,
                success_rate=0, avg_response_time=0, total_tokens=0,
                total_cost=0, pre_analysis_count=0, full_analysis_count=0,
                json_parse_errors=0, api_failures=0
            )

    def _calculate_max_drawdown(self, trades: List[Dict]) -> float:
        """计算最大回撤"""
        if not trades:
            return 0.0

        try:
            balance = 10000.0  # 初始余额
            peak = balance
            max_drawdown = 0.0

            for trade in trades:
                profit = trade.get('profit', 0.0)
                balance += profit

                if balance > peak:
                    peak = balance

                drawdown = (peak - balance) / peak * 100
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

            return max_drawdown

        except Exception:
            return 0.0

    def _check_alerts(self, system_metrics: SystemMetrics):
        """检查告警条件"""
        # 系统资源告警
        if system_metrics.system_status == 'critical':
            self.add_alert('critical', 'system',
                          f'系统资源严重不足: CPU {system_metrics.cpu_percent:.1f}%, '
                          f'内存 {system_metrics.memory_percent:.1f}%, '
                          f'磁盘 {system_metrics.disk_percent:.1f}%')
        elif system_metrics.system_status == 'warning':
            self.add_alert('warning', 'system',
                          f'系统资源紧张: CPU {system_metrics.cpu_percent:.1f}%, '
                          f'内存 {system_metrics.memory_percent:.1f}%')

        # 交易告警（基于最新的交易指标）
        if self.trading_metrics_history:
            latest_trading = self.trading_metrics_history[-1]
            if latest_trading.max_drawdown > 20:
                self.add_alert('critical', 'trading', f'最大回撤过大: {latest_trading.max_drawdown:.2f}%')
            elif latest_trading.win_rate < 30 and latest_trading.total_trades > 10:
                self.add_alert('warning', 'trading', f'胜率过低: {latest_trading.win_rate:.1f}%')

    def add_alert(self, level: str, category: str, message: str, details: str = None):
        """添加告警"""
        alert = AlertInfo(
            timestamp=datetime.now().isoformat(),
            level=level,
            category=category,
            message=message,
            details=details
        )
        self.alerts_history.append(alert)

        # 打印告警信息
        level_emoji = {'info': 'ℹ️', 'warning': '⚠️', 'error': '❌', 'critical': '🚨'}
        emoji = level_emoji.get(level, '📝')
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {emoji} [{level.upper()}] [{category}] {message}")

    def record_analysis_event(self, event_type: str, success: bool = True, response_time: float = 0,
                            tokens: int = 0, cost: float = 0):
        """记录分析事件"""
        self.counters['total_analyses'] += 1

        if success:
            self.counters['successful_analyses'] += 1
        else:
            self.counters['failed_analyses'] += 1

        if event_type == 'pre_analysis':
            self.counters['pre_analysis_count'] += 1
        elif event_type == 'full_analysis':
            self.counters['full_analysis_count'] += 1

        if response_time > 0:
            self.response_times.append(response_time)

        if tokens > 0:
            self.counters['total_tokens'] += tokens

        if cost > 0:
            self.counters['total_cost'] += cost

    def record_error_event(self, error_type: str):
        """记录错误事件"""
        if error_type == 'json_parse':
            self.counters['json_parse_errors'] += 1
        elif error_type == 'api_failure':
            self.counters['api_failures'] += 1

    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态摘要"""
        current_time = datetime.now().isoformat()

        # 最新指标
        latest_system = self.system_metrics_history[-1] if self.system_metrics_history else None
        latest_trading = self.trading_metrics_history[-1] if self.trading_metrics_history else None
        latest_analysis = self.analysis_metrics_history[-1] if self.analysis_metrics_history else None

        # 最近告警
        recent_alerts = [alert for alert in self.alerts_history
                        if datetime.fromisoformat(alert.timestamp) > datetime.now() - timedelta(hours=1)]

        return {
            'timestamp': current_time,
            'system': asdict(latest_system) if latest_system else None,
            'trading': asdict(latest_trading) if latest_trading else None,
            'analysis': asdict(latest_analysis) if latest_analysis else None,
            'recent_alerts': [asdict(alert) for alert in recent_alerts[-10:]],  # 最近10条告警
            'monitoring_status': 'active' if self.monitoring else 'inactive'
        }

    def get_historical_data(self, data_type: str, hours: int = 24) -> List[Dict]:
        """获取历史数据"""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        if data_type == 'system':
            return [asdict(metric) for metric in self.system_metrics_history
                   if datetime.fromisoformat(metric.timestamp) > cutoff_time]
        elif data_type == 'trading':
            return [asdict(metric) for metric in self.trading_metrics_history
                   if datetime.fromisoformat(metric.timestamp) > cutoff_time]
        elif data_type == 'analysis':
            return [asdict(metric) for metric in self.analysis_metrics_history
                   if datetime.fromisoformat(metric.timestamp) > cutoff_time]
        elif data_type == 'alerts':
            return [asdict(alert) for alert in self.alerts_history
                   if datetime.fromisoformat(alert.timestamp) > cutoff_time]
        else:
            return []

    def _load_historical_data(self):
        """加载历史数据"""
        try:
            # 加载系统指标历史
            system_file = os.path.join(self.data_dir, 'system_metrics.json')
            if os.path.exists(system_file):
                with open(system_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for item in data[-1440:]:  # 只保留最近24小时
                        self.system_metrics_history.append(SystemMetrics(**item))

            # 加载交易指标历史
            trading_file = os.path.join(self.data_dir, 'trading_metrics.json')
            if os.path.exists(trading_file):
                with open(trading_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for item in data[-288:]:  # 只保留最近24小时
                        self.trading_metrics_history.append(TradingMetrics(**item))

            # 加载分析指标历史
            analysis_file = os.path.join(self.data_dir, 'analysis_metrics.json')
            if os.path.exists(analysis_file):
                with open(analysis_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for item in data[-288:]:  # 只保留最近24小时
                        self.analysis_metrics_history.append(AnalysisMetrics(**item))

            # 加载告警历史
            alerts_file = os.path.join(self.data_dir, 'alerts.json')
            if os.path.exists(alerts_file):
                with open(alerts_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for item in data[-1000:]:  # 只保留最近1000条
                        self.alerts_history.append(AlertInfo(**item))

            # 加载计数器
            counters_file = os.path.join(self.data_dir, 'counters.json')
            if os.path.exists(counters_file):
                with open(counters_file, 'r', encoding='utf-8') as f:
                    saved_counters = json.load(f)
                    self.counters.update(saved_counters)

        except Exception as e:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 加载历史数据失败: {e}")

    def _save_historical_data(self):
        """保存历史数据"""
        try:
            # 保存系统指标历史
            system_file = os.path.join(self.data_dir, 'system_metrics.json')
            with open(system_file, 'w', encoding='utf-8') as f:
                data = [asdict(metric) for metric in self.system_metrics_history]
                json.dump(data, f, ensure_ascii=False, indent=2)

            # 保存交易指标历史
            trading_file = os.path.join(self.data_dir, 'trading_metrics.json')
            with open(trading_file, 'w', encoding='utf-8') as f:
                data = [asdict(metric) for metric in self.trading_metrics_history]
                json.dump(data, f, ensure_ascii=False, indent=2)

            # 保存分析指标历史
            analysis_file = os.path.join(self.data_dir, 'analysis_metrics.json')
            with open(analysis_file, 'w', encoding='utf-8') as f:
                data = [asdict(metric) for metric in self.analysis_metrics_history]
                json.dump(data, f, ensure_ascii=False, indent=2)

            # 保存告警历史
            alerts_file = os.path.join(self.data_dir, 'alerts.json')
            with open(alerts_file, 'w', encoding='utf-8') as f:
                data = [asdict(alert) for alert in self.alerts_history]
                json.dump(data, f, ensure_ascii=False, indent=2)

            # 保存计数器
            counters_file = os.path.join(self.data_dir, 'counters.json')
            with open(counters_file, 'w', encoding='utf-8') as f:
                json.dump(self.counters, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 保存历史数据失败: {e}")

    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        return {
            'current_status': self.get_current_status(),
            'system_metrics_24h': self.get_historical_data('system', 24),
            'trading_metrics_24h': self.get_historical_data('trading', 24),
            'analysis_metrics_24h': self.get_historical_data('analysis', 24),
            'recent_alerts': self.get_historical_data('alerts', 1),  # 最近1小时的告警
            'counters': self.counters.copy(),
            'risk_management': self.get_risk_management_status(),
            'strategy_optimization': self.get_strategy_optimization_status(),
            'performance_metrics': self.get_performance_metrics()
        }

    def get_risk_management_status(self):
        """获取风险管理状态"""
        try:
            # 导入风险管理系统
            from app.core.risk_management import AdvancedRiskManager
            risk_manager = AdvancedRiskManager()

            # 获取当前风险状态
            risk_status = {
                'risk_limits': risk_manager.risk_limits,
                'daily_stats': risk_manager.daily_stats,
                'last_check_time': datetime.now().isoformat(),
                'check_interval': '5分钟',
                'status': 'active',
                'checks_performed': {
                    'account_risk': '账户回撤、日亏损、周亏损监控',
                    'position_risk': '单笔风险、组合风险、相关性风险',
                    'trading_frequency': '日交易次数、交易间隔、连续亏损',
                    'market_risk': '波动率风险、流动性风险、市场压力'
                },
                'risk_levels': {
                    'LOW': '正常交易',
                    'MEDIUM': '减少仓位',
                    'HIGH': '停止新交易',
                    'CRITICAL': '减少持仓',
                    'EMERGENCY': '紧急平仓'
                }
            }

            return risk_status
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'message': '风险管理系统状态获取失败'
            }

    def get_strategy_optimization_status(self):
        """获取策略优化状态"""
        try:
            # 导入LLM优化策略
            from app.utils.llm_optimized_trading_strategy import LLMOptimizedTradingStrategy
            strategy_optimizer = LLMOptimizedTradingStrategy()

            # 获取策略状态
            strategy_status = {
                'current_strategy': strategy_optimizer.current_strategy,
                'available_strategies': list(strategy_optimizer.strategy_configs.keys()),
                'llm_constraints': strategy_optimizer.llm_constraints,
                'market_thresholds': strategy_optimizer.market_thresholds,
                'last_analysis_time': strategy_optimizer.last_analysis_time.isoformat() if strategy_optimizer.last_analysis_time else None,
                'strategy_details': {
                    'fast': {
                        'timeframe': '30分钟',
                        'min_interval': '15分钟',
                        'risk_reward': '2.0:1',
                        'scenario': '高波动率+强趋势',
                        'description': '快速捕捉短期机会，分析成本较高'
                    },
                    'standard': {
                        'timeframe': '1小时',
                        'min_interval': '30分钟',
                        'risk_reward': '2.5:1',
                        'scenario': '平衡市场条件',
                        'description': '平衡机会捕捉和成本控制，最常用'
                    },
                    'conservative': {
                        'timeframe': '4小时',
                        'min_interval': '60分钟',
                        'risk_reward': '3.0:1',
                        'scenario': '低波动率+弱趋势',
                        'description': '成本最低，适合长期趋势跟踪'
                    }
                }
            }

            return strategy_status
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'message': 'LLM策略优化系统状态获取失败'
            }

    def get_performance_metrics(self):
        """获取性能评估指标"""
        try:
            # 读取交易结果数据
            trade_results_file = os.path.join('app', 'data', 'trade_results.json')
            token_stats_file = os.path.join('app', 'data', 'token_statistics.json')
            analysis_history_file = os.path.join('app', 'data', 'forex_analysis_history.json')

            performance_data = {
                'trading_performance': {
                    'total_trades': 0,
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'max_drawdown': 0.0,
                    'sharpe_ratio': 0.0,
                    'monthly_return': 0.0,
                    'yearly_return': 0.0,
                    'last_trade_time': None
                },
                'llm_performance': {
                    'total_analyses': 0,
                    'avg_analysis_time': '3-8分钟',
                    'total_tokens': 0,
                    'total_cost': 0.0,
                    'success_rate': 0.0,
                    'efficiency_score': 0.0,
                    'last_analysis_time': None
                },
                'system_performance': {
                    'uptime': '系统运行时间',
                    'last_update': datetime.now().isoformat(),
                    'data_quality': 'good',
                    'system_health': 'healthy',
                    'monitoring_status': 'active' if self.monitoring else 'inactive'
                }
            }

            # 读取交易结果
            if os.path.exists(trade_results_file):
                with open(trade_results_file, 'r', encoding='utf-8') as f:
                    trade_results = json.load(f)
                    if trade_results:
                        # 计算交易性能指标
                        total_trades = len(trade_results)
                        winning_trades = len([t for t in trade_results if t.get('profit', 0) > 0])
                        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

                        performance_data['trading_performance'].update({
                            'total_trades': total_trades,
                            'win_rate': round(win_rate, 2),
                            'last_trade_time': trade_results[-1].get('timestamp') if trade_results else None
                        })

            # 读取Token统计
            if os.path.exists(token_stats_file):
                with open(token_stats_file, 'r', encoding='utf-8') as f:
                    token_stats = json.load(f)
                    performance_data['llm_performance'].update({
                        'total_tokens': token_stats.get('total_tokens', 0),
                        'total_cost': round(token_stats.get('total_cost', 0.0), 4),
                        'analysis_count': token_stats.get('analysis_count', 0)
                    })

            # 读取分析历史
            if os.path.exists(analysis_history_file):
                with open(analysis_history_file, 'r', encoding='utf-8') as f:
                    analysis_history = json.load(f)
                    if analysis_history:
                        total_analyses = len(analysis_history)
                        successful_analyses = len([a for a in analysis_history if a.get('success', False)])
                        success_rate = (successful_analyses / total_analyses * 100) if total_analyses > 0 else 0

                        performance_data['llm_performance'].update({
                            'total_analyses': total_analyses,
                            'success_rate': round(success_rate, 2),
                            'last_analysis_time': analysis_history[-1].get('timestamp') if analysis_history else None
                        })

            return performance_data
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'message': '性能指标获取失败'
            }

# 创建全局监控实例
real_time_monitor = RealTimeMonitor()
