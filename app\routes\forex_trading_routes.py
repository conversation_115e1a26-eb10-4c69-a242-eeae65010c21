"""
外汇交易路由
处理外汇交易相关的API请求
"""
from flask import Blueprint, request, jsonify

from app.services import forex_trading_service
from app.utils import mt4_client, db_client, forex_analysis_history

# 创建蓝图
forex_trading_bp = Blueprint('forex_trading', __name__, url_prefix='/api/forex-trading')


@forex_trading_bp.route('/test-db', methods=['GET'])
def test_db():
    """测试数据库连接"""
    try:
        connected = db_client.test_connection()

        if connected:
            return jsonify({
                'success': True,
                'message': 'pizza_quotes数据库连接成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'pizza_quotes数据库连接失败'
            }), 500
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'测试数据库连接失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/eurusd/min-data', methods=['GET'])
def get_eurusd_min_data():
    """获取EURUSD分钟数据"""
    try:
        start = request.args.get('start')
        end = request.args.get('end')
        limit = request.args.get('limit', 100)

        data = forex_trading_service.get_eurusd_min_data(
            start,
            end,
            int(limit)
        )

        return jsonify({
            'success': True,
            'count': len(data),
            'data': data
        })
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'获取EURUSD分钟数据失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/eurusd/klines', methods=['GET'])
def get_klines():
    """获取聚合K线数据"""
    try:
        period = request.args.get('period', 15)
        count = request.args.get('count', 100)

        period_minutes = int(period)
        data_count = int(count)

        print(f'处理获取K线数据请求，周期: {period_minutes}分钟，数量: {data_count}')

        data = forex_trading_service.get_aggregated_klines(
            period_minutes,
            data_count
        )

        print(f'成功获取到{len(data)}条K线数据')

        return jsonify({
            'success': True,
            'period': period_minutes,
            'count': len(data),
            'data': data
        })
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'获取聚合K线数据失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/eurusd/indicators', methods=['GET'])
def get_indicators():
    """获取技术指标"""
    try:
        period = request.args.get('period', 15)
        count = request.args.get('count', 100)

        period_minutes = int(period)
        data_count = int(count)

        # 获取K线数据
        klines = forex_trading_service.get_aggregated_klines(
            period_minutes,
            data_count
        )

        # 计算指标
        indicators = forex_trading_service.calculate_indicators(klines)

        return jsonify({
            'success': True,
            'period': period_minutes,
            'indicators': indicators
        })
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'获取技术指标失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/news', methods=['GET'])
def get_news():
    """获取相关新闻"""
    try:
        limit = request.args.get('limit', 10)

        news = forex_trading_service.get_relevant_news(int(limit))

        return jsonify({
            'success': True,
            'count': len(news),
            'news': news
        })
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'获取相关新闻失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/calendar', methods=['GET'])
def get_calendar():
    """获取相关日历事件"""
    try:
        limit = request.args.get('limit', 10)

        calendar = forex_trading_service.get_relevant_calendar(int(limit))

        return jsonify({
            'success': True,
            'count': len(calendar),
            'calendar': calendar
        })
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'获取相关日历事件失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/positions', methods=['GET'])
def get_positions():
    """获取当前持仓"""
    try:
        # 尝试连接MT4客户端
        if not mt4_client.mt4_client.is_connected:
            try:
                mt4_client.mt4_client.connect()
            except Exception as connect_error:
                print(f'MT4客户端连接失败: {connect_error}')

        # 从MT4服务器获取活跃订单
        try:
            print('开始从MT4服务器获取活跃订单...')
            response = mt4_client.mt4_client.get_active_orders()
            print('获取活跃订单响应:', response)

            # 检查orders是否存在
            orders = response.get('orders', [])
            print(f'获取到{len(orders)}个活跃订单')

            # 过滤EURUSD的订单
            eurusd_orders = [order for order in orders if order.get('symbol') == 'EURUSD']
            print(f'其中{len(eurusd_orders)}个是EURUSD订单')

            return jsonify({
                'success': True,
                'count': len(eurusd_orders),
                'positions': eurusd_orders,
                'message': response.get('message', '获取当前持仓成功')
            })
        except Exception as mt4_error:
            print(f'从MT4获取活跃订单失败: {mt4_error}')
            # 返回错误响应
            return jsonify({
                'success': False,
                'count': 0,
                'positions': [],
                'message': f'从MT4获取活跃订单失败: {str(mt4_error)}'
            }), 500
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'获取当前持仓失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/pending-orders', methods=['GET'])
def get_pending_orders():
    """获取挂单"""
    try:
        # 尝试连接MT4客户端
        if not mt4_client.mt4_client.is_connected:
            try:
                mt4_client.mt4_client.connect()
            except Exception as connect_error:
                print(f'MT4客户端连接失败: {connect_error}')

        # 从MT4服务器获取挂单
        try:
            print('开始从MT4服务器获取挂单...')
            response = mt4_client.mt4_client.get_pending_orders()
            print('获取挂单响应:', response)

            # 检查orders是否存在
            orders = response.get('orders', [])
            print(f'获取到{len(orders)}个挂单')

            # 过滤EURUSD的订单
            eurusd_orders = [order for order in orders if order.get('symbol') == 'EURUSD']
            print(f'其中{len(eurusd_orders)}个是EURUSD挂单')

            return jsonify({
                'success': True,
                'count': len(eurusd_orders),
                'pendingOrders': eurusd_orders,
                'message': response.get('message', '获取挂单成功')
            })
        except Exception as mt4_error:
            print(f'从MT4获取挂单失败: {mt4_error}')
            # 返回错误响应
            return jsonify({
                'success': False,
                'count': 0,
                'pendingOrders': [],
                'message': f'从MT4获取挂单失败: {str(mt4_error)}'
            }), 500
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'获取挂单失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/all-orders', methods=['GET'])
def get_all_orders():
    """获取所有订单，包括活跃订单和挂单"""
    try:
        # 尝试连接MT4客户端
        if not mt4_client.mt4_client.is_connected:
            try:
                mt4_client.mt4_client.connect()
            except Exception as connect_error:
                print(f'MT4客户端连接失败: {connect_error}')

        # 从MT4服务器获取所有订单
        try:
            print('开始从MT4服务器获取所有订单...')
            response = mt4_client.mt4_client.get_all_orders()
            print('获取所有订单响应:', response)

            # 检查active_orders和pending_orders是否存在
            active_orders = response.get('active_orders', [])
            pending_orders = response.get('pending_orders', [])
            all_orders = response.get('all_orders', [])

            # 过滤EURUSD的订单
            eurusd_active_orders = [order for order in active_orders if order.get('symbol') == 'EURUSD']
            eurusd_pending_orders = [order for order in pending_orders if order.get('symbol') == 'EURUSD']
            eurusd_all_orders = eurusd_active_orders + eurusd_pending_orders

            print(f'获取到{len(eurusd_active_orders)}个EURUSD活跃订单和{len(eurusd_pending_orders)}个EURUSD挂单')

            return jsonify({
                'success': True,
                'activeOrdersCount': len(eurusd_active_orders),
                'pendingOrdersCount': len(eurusd_pending_orders),
                'totalCount': len(eurusd_all_orders),
                'activeOrders': eurusd_active_orders,
                'pendingOrders': eurusd_pending_orders,
                'allOrders': eurusd_all_orders,
                'message': response.get('message', '获取所有订单成功')
            })
        except Exception as mt4_error:
            print(f'从MT4获取所有订单失败: {mt4_error}')
            # 返回错误响应
            return jsonify({
                'success': False,
                'activeOrdersCount': 0,
                'pendingOrdersCount': 0,
                'totalCount': 0,
                'activeOrders': [],
                'pendingOrders': [],
                'allOrders': [],
                'message': f'从MT4获取所有订单失败: {str(mt4_error)}'
            }), 500
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'获取所有订单失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/market-info/<symbol>', methods=['GET'])
def get_market_info(symbol):
    """获取市场信息"""
    try:
        if not symbol:
            return jsonify({
                'success': False,
                'message': '缺少symbol参数'
            }), 400

        # 尝试连接MT4客户端
        if not mt4_client.mt4_client.is_connected:
            try:
                mt4_client.mt4_client.connect()
            except Exception as connect_error:
                print(f'MT4客户端连接失败: {connect_error}')

        # 从MT4服务器获取市场信息
        try:
            response = mt4_client.mt4_client.get_market_info(symbol)

            if response.get('status') == 'success':
                return jsonify({
                    'success': True,
                    'marketInfo': response.get('data', {})
                })
            else:
                return jsonify({
                    'success': False,
                    'message': response.get('message', '获取市场信息失败')
                }), 500
        except Exception as mt4_error:
            return jsonify({
                'success': False,
                'message': f'从MT4获取市场信息失败: {str(mt4_error)}'
            }), 500
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'获取市场信息失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/analyze', methods=['POST'])
def analyze():
    """执行外汇分析（遵循1小时频率限制）"""
    try:
        print('收到外汇分析请求（遵循1小时频率限制）')

        # 尝试获取最新的分析记录
        recent_analysis = forex_analysis_history.get_latest_analysis_record()

        if recent_analysis:
            print('找到最近的分析结果，直接返回缓存数据')
            # 返回缓存的分析结果
            return jsonify({
                'success': True,
                'result': recent_analysis,
                'fromCache': True,
                'message': '使用缓存的分析结果'
            })

        # 如果没有缓存数据，执行分析
        print('未找到缓存数据，执行新的分析...')
        result = forex_trading_service.analyze_forex(False)  # 不强制执行LLM分析

        return jsonify({
            'success': True,
            'result': result,
            'fromCache': result.get('fromCache', False)
        })
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'执行外汇分析失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/analyze-force', methods=['POST'])
def analyze_force():
    """强制执行外汇分析（忽略频率限制）"""
    try:
        print('收到强制执行外汇分析请求')
        result = forex_trading_service.analyze_forex(True)  # 强制执行LLM分析

        return jsonify({
            'success': True,
            'result': result,
            'fromCache': False
        })
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'强制执行外汇分析失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/execute-trade', methods=['POST'])
def execute_trade():
    """执行交易"""
    try:
        print('收到执行交易请求:', request.json)

        data = request.json
        if not data:
            return jsonify({
                'success': False,
                'message': '缺少请求数据'
            }), 400

        action = data.get('action')
        order_type = data.get('orderType')
        symbol = data.get('symbol')
        lot = data.get('lot')
        price = data.get('price')
        sl = data.get('sl')
        tp = data.get('tp')

        if not action:
            return jsonify({
                'success': False,
                'message': '缺少交易动作参数'
            }), 400

        # 构建交易指令
        trade_instructions = {
            'action': action,
            'orderType': order_type or 'MARKET',
            'symbol': symbol or 'EURUSD',
            'lot': lot or 0.01,
            'entryPrice': price,  # 使用price作为entryPrice
            'stopLoss': sl,       # 使用sl作为stopLoss
            'takeProfit': tp      # 使用tp作为takeProfit
        }

        print('执行交易指令:', trade_instructions)

        result = forex_trading_service.execute_trade(trade_instructions)
        print('交易执行结果:', result)

        return jsonify({
            'success': result.get('success', False),
            'message': result.get('message', ''),
            'orderId': result.get('orderId'),
            'details': result.get('details', {})
        })
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'执行交易失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/analyze-and-trade', methods=['POST'])
def analyze_and_trade():
    """分析并执行交易（遵循1小时频率限制）"""
    try:
        print('收到分析并执行交易请求（遵循1小时频率限制）')

        # 执行分析
        print('步骤1: 执行外汇分析')
        analysis_result = forex_trading_service.analyze_forex(False)  # 不强制执行LLM分析
        print('外汇分析完成，交易指令:', analysis_result.get('tradeInstructions', {}))

        # 获取交易指令
        trade_instructions = analysis_result.get('tradeInstructions', {})

        # 检查交易指令是否有效
        if not trade_instructions or trade_instructions.get('action') == 'NONE':
            print('交易指令为观望或无效，不执行交易')
            return jsonify({
                'success': True,
                'message': '分析完成，交易指令为观望，不执行交易',
                'analysis': analysis_result,
                'trade': {
                    'success': True,
                    'message': '交易指令为观望，不执行交易',
                    'orderId': None
                },
                'fromCache': analysis_result.get('fromCache', False)
            })

        # 执行交易
        print('步骤2: 执行交易')
        trade_result = forex_trading_service.execute_trade(trade_instructions)
        print('交易执行完成:', trade_result)

        return jsonify({
            'success': True,
            'analysis': analysis_result,
            'trade': trade_result,
            'fromCache': analysis_result.get('fromCache', False)
        })
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'分析并执行交易失败: {str(error)}'
        }), 500


@forex_trading_bp.route('/analyze-and-trade-force', methods=['POST'])
def analyze_and_trade_force():
    """强制分析并执行交易（忽略频率限制）"""
    try:
        print('收到强制分析并执行交易请求')

        # 执行分析
        print('步骤1: 强制执行外汇分析')
        analysis_result = forex_trading_service.analyze_forex(True)  # 强制执行LLM分析
        print('外汇分析完成，交易指令:', analysis_result.get('tradeInstructions', {}))

        # 获取交易指令
        trade_instructions = analysis_result.get('tradeInstructions', {})

        # 检查交易指令是否有效
        if not trade_instructions or trade_instructions.get('action') == 'NONE':
            print('交易指令为观望或无效，不执行交易')
            return jsonify({
                'success': True,
                'message': '强制分析完成，交易指令为观望，不执行交易',
                'analysis': analysis_result,
                'trade': {
                    'success': True,
                    'message': '交易指令为观望，不执行交易',
                    'orderId': None
                },
                'fromCache': False
            })

        # 执行交易
        print('步骤2: 执行交易')
        trade_result = forex_trading_service.execute_trade(trade_instructions)
        print('交易执行完成:', trade_result)

        return jsonify({
            'success': True,
            'analysis': analysis_result,
            'trade': trade_result,
            'fromCache': False
        })
    except Exception as error:
        return jsonify({
            'success': False,
            'message': f'强制分析并执行交易失败: {str(error)}'
        }), 500
