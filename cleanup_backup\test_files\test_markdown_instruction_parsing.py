"""
测试Markdown格式交易指令解析

这个脚本测试系统对Markdown格式交易指令的解析能力，特别是"执行指令"部分
"""
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append('.')

# 导入需要测试的模块
from app.utils.llm_client import parse_trade_instructions

def print_header(message):
    """打印带有分隔线的标题"""
    print("\n" + "=" * 80)
    print(f" {message} ".center(80, "="))
    print("=" * 80)

def test_markdown_execution_instruction():
    """测试Markdown格式的执行指令解析"""
    print_header("测试Markdown格式的执行指令解析")
    
    # 测试用例：Markdown格式的执行指令
    test_response = """
### 最终交易决策（基于13日均线右侧交易策略）

#### 一、13日均线趋势验证
1. **15分钟图表**：
   - 当前MA13=1.1307（每小时上升3.2点）
   - 均线斜率0.00015/分钟，维持向上趋势
   - 价格偏离均线18点（1.13189 vs 1.1307）

2. **1小时图表**：
   - 当前MA13=1.1299（每小时上升4.1点）
   - 均线连续6小时抬升，但斜率趋缓

**结论**：双周期均线保持上行，符合右侧交易做多条件

---

#### 二、价格回踩机会评估
1. **动态支撑区**：
   - 15分钟MA13预测位置：14:00将达1.1310
   - 1小时MA13预测位置：14:00将达1.1303

2. **回踩触发条件**：
   - 理想入场区：1.1305-1.1310（含均线提前量）
   - 当前挂单价1.1309位于该区间中枢

**结论**：挂单设置符合策略要求，无需调整

---

#### 三、事件驱动风险控制
1. **美国新屋销售数据应对方案**：
   - 若数据>70万（利多美元）：
     - 立即撤销挂单
     - 在1.1295设置SELLSTOP 0.08手（突破MA50支撑）
   - 若数据<68万（利空美元）：
     - 挂单触发后扩大止盈至1.1345
     - 设置追踪止损（30点回撤保护）

2. **波动率缓冲**：
   - 数据公布前10分钟缩小挂单范围至1.1307-1.1312
   - 点差容忍度扩大至5点

---

#### 四、执行指令
```markdown
1. 维持BUYLIMIT 1.1309（0.12手）
   - 止损1.1299（MA50下方10点+数据缓冲）
   - 止盈1.1335（风险回报比1:2.3）

2. 设置对冲订单：
   - SELLSTOP 0.04手 @1.1288
   - 止损1.1303，止盈1.1260
   - 生效条件：美国数据公布后价格跌破1.1290

3. 监测警报：
   - 价格突破1.1322时启动追踪止损（基准价-15点）
   - 黄金突破3315美元/盎司时加仓0.04手
```

---

#### 五、决策依据矩阵

| 要素                | 多空权重 | 策略契合度       |
|---------------------|----------|------------------|
| 13日均线方向        | +0.8     | 完全符合（核心） |
| 价格/均线关系       | +0.6     | 偏离待回踩       |
| 多周期共振          | +0.7     | 双周期向上       |
| MACD动能            | -0.4     | 顶背离风险       |
| 事件波动率          | -0.5     | 数据前谨慎       |
| 历史模式验证        | +0.6     | 3/5相似案例成功  |

**综合评分**：+2.8/4.0（中等偏强信号）

---

#### 六、特殊情景预案
1. **假突破情景**：
   - 若价格触及1.1335后快速回落：
     - 平仓50%头寸
     - 剩余仓位止损上移至1.1320

2. **数据真空波动**：
   - 14:00前未触发挂单：
     - 撤销订单等待欧盘开盘
     - 重新评估MA13斜率变化

3. **黑天鹅冲击**：
   - 价格跳空突破1.1290：
     - 立即关闭所有多头头寸
     - 反手做空（0.08手）目标1.1260

---

### 最终建议
**严格执行BUYLIMIT 1.1309挂单**，同时做好以下准备：
1. 数据公布前15分钟检查流动性
2. 确保对冲订单处于激活状态
3. 准备手动干预突破行情

（当前价格1.13189距离理想入场区18点，耐心等待价格向MA13回归，避免在数据公布前追单）
"""
    
    # 解析交易指令
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 开始解析交易指令')
    result = parse_trade_instructions(test_response)
    
    # 打印结果
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 解析结果:')
    print(f"动作: {result['action']}")
    print(f"订单类型: {result['orderType']}")
    print(f"入场价格: {result['entryPrice']}")
    print(f"止损: {result['stopLoss']}")
    print(f"止盈: {result['takeProfit']}")
    print(f"手数: {result['lotSize']}")
    
    # 验证结果
    assert result['action'] == 'BUY', "动作应该是BUY"
    assert result['orderType'] == 'LIMIT', "订单类型应该是LIMIT"
    assert result['entryPrice'] == 1.1309, "入场价格应该是1.1309"
    assert result['stopLoss'] == 1.1299, "止损应该是1.1299"
    assert result['takeProfit'] == 1.1335, "止盈应该是1.1335"
    assert result['lotSize'] == 0.12, "手数应该是0.12"
    
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试通过: 系统成功解析了Markdown格式的执行指令')

if __name__ == "__main__":
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试开始')
    test_markdown_execution_instruction()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试结束')
