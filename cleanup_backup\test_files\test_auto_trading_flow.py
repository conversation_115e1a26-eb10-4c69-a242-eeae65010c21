"""
自动交易流程测试脚本
用于测试完整的自动交易流程，从分析到执行交易
"""
import os
import sys
import time
from datetime import datetime

from app.services.forex_trading_service import analyze_forex, execute_trade
from app.utils.mt4_client import mt4_client

def test_auto_trading_flow():
    """测试自动交易流程"""
    try:
        print('=' * 50)
        print(f'开始测试自动交易流程，时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        print('=' * 50)
        
        # 步骤1: 确保MT4连接
        print('\n步骤1: 确保MT4连接')
        if not mt4_client.is_connected:
            print('MT4客户端未连接，尝试连接')
            connected = mt4_client.connect()
            if not connected:
                print('无法连接到MT4客户端，测试失败')
                return
        print('MT4连接正常')
        
        # 步骤2: 获取当前持仓
        print('\n步骤2: 获取当前持仓')
        positions_response = mt4_client.get_active_orders()
        positions = positions_response.get('orders', [])
        print(f'当前持仓数量: {len(positions)}')
        for position in positions:
            print(f'  - 订单ID: {position.get("order_id")}, 类型: {position.get("type")}, 货币对: {position.get("symbol")}, 手数: {position.get("lots")}, 盈亏: {position.get("profit")}')
        
        # 步骤3: 执行外汇分析
        print('\n步骤3: 执行外汇分析')
        print('开始执行外汇分析，这可能需要几分钟时间...')
        analysis_result = analyze_forex(force=True)
        
        # 检查分析结果
        if not analysis_result:
            print('分析失败，未返回结果')
            return
        
        trade_instructions = analysis_result.get('tradeInstructions', {})
        print(f'分析完成，交易指令: {trade_instructions}')
        
        # 步骤4: 根据分析结果执行交易
        print('\n步骤4: 根据分析结果执行交易')
        
        # 检查是否有有效的交易指令
        if not trade_instructions or trade_instructions.get('action') == 'NONE':
            print('交易指令为观望或无效，不执行交易')
            return
        
        print(f'执行交易指令: {trade_instructions}')
        trade_result = execute_trade(trade_instructions)
        print(f'交易执行结果: {trade_result}')
        
        # 步骤5: 验证交易结果
        print('\n步骤5: 验证交易结果')
        if trade_result.get('success'):
            print('交易执行成功')
            
            # 获取新的持仓
            new_positions_response = mt4_client.get_active_orders()
            new_positions = new_positions_response.get('orders', [])
            print(f'新的持仓数量: {len(new_positions)}')
            
            # 检查是否有新增持仓
            if len(new_positions) > len(positions):
                print('持仓数量增加，交易确认成功')
            else:
                print('持仓数量未增加，可能交易未成功执行或已有相同方向的持仓')
        else:
            print(f'交易执行失败: {trade_result.get("message")}')
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_auto_trading_flow()
