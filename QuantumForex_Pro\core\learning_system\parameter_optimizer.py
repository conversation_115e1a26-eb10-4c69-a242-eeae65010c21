#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
参数优化器
基于交易结果自动优化策略参数
"""

import json
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
from scipy.optimize import minimize
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import cross_val_score

from .trade_result_recorder import TradeResultRecorder
from .pattern_analyzer import PatternAnalyzer

@dataclass
class ParameterSet:
    """参数集合"""
    name: str
    parameters: Dict[str, Any]
    performance_score: float
    win_rate: float
    avg_profit: float
    max_drawdown: float
    sample_size: int
    last_updated: datetime

@dataclass
class OptimizationResult:
    """优化结果"""
    parameter_name: str
    old_value: Any
    new_value: Any
    improvement_score: float
    confidence: float
    reason: str

class ParameterOptimizer:
    """参数优化器"""

    def __init__(self, trade_recorder: TradeResultRecorder, pattern_analyzer: PatternAnalyzer):
        self.trade_recorder = trade_recorder
        self.pattern_analyzer = pattern_analyzer
        self.logger = logging.getLogger(__name__)

        # 优化配置
        self.config_path = Path("data/optimization_config.json")
        self.results_path = Path("data/optimization_results.json")

        # 确保目录存在
        self.config_path.parent.mkdir(parents=True, exist_ok=True)

        # 加载配置
        self.optimization_config = self._load_optimization_config()

        # 历史优化结果
        self.optimization_history = self._load_optimization_history()

    def _load_optimization_config(self) -> Dict:
        """加载优化配置"""
        default_config = {
            "strategy_parameters": {
                "max_portfolio_risk": {
                    "current": 0.02,
                    "min": 0.005,
                    "max": 0.05,
                    "step": 0.005,
                    "type": "float"
                },
                "max_correlation_exposure": {
                    "current": 0.70,
                    "min": 0.50,
                    "max": 0.90,
                    "step": 0.05,
                    "type": "float"
                },
                "min_confidence_threshold": {
                    "current": 0.65,
                    "min": 0.50,
                    "max": 0.85,
                    "step": 0.05,
                    "type": "float"
                },
                "rebalance_threshold": {
                    "current": 0.05,
                    "min": 0.02,
                    "max": 0.10,
                    "step": 0.01,
                    "type": "float"
                }
            },
            "stop_loss_parameters": {
                "atr_multiplier": {
                    "current": 2.0,
                    "min": 1.0,
                    "max": 3.5,
                    "step": 0.25,
                    "type": "float"
                },
                "risk_reward_ratio": {
                    "current": 1.8,
                    "min": 1.2,
                    "max": 3.0,
                    "step": 0.2,
                    "type": "float"
                }
            },
            "currency_specific": {
                "EURUSD": {
                    "min_stop_pips": {"current": 15, "min": 10, "max": 25, "step": 2, "type": "int"},
                    "max_stop_pips": {"current": 30, "min": 20, "max": 40, "step": 2, "type": "int"}
                },
                "GBPUSD": {
                    "min_stop_pips": {"current": 18, "min": 12, "max": 30, "step": 2, "type": "int"},
                    "max_stop_pips": {"current": 35, "min": 25, "max": 45, "step": 2, "type": "int"}
                }
            },
            "optimization_settings": {
                "min_trades_for_optimization": 30,  # 降低最小交易数量要求，适应实际数据量
                "lookback_days": 60,
                "optimization_frequency_days": 7,
                "min_improvement_threshold": 0.05
            }
        }

        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            else:
                # 保存默认配置
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
                return default_config

        except Exception as e:
            self.logger.error(f"加载优化配置失败: {e}")
            return default_config

    def _load_optimization_history(self) -> List[Dict]:
        """加载优化历史"""
        try:
            if self.results_path.exists():
                with open(self.results_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            self.logger.error(f"加载优化历史失败: {e}")
            return []

    def _save_optimization_history(self):
        """保存优化历史"""
        try:
            with open(self.results_path, 'w', encoding='utf-8') as f:
                json.dump(self.optimization_history, f, indent=2, ensure_ascii=False, default=str)
        except Exception as e:
            self.logger.error(f"保存优化历史失败: {e}")

    def optimize_parameters(self, force: bool = False) -> List[OptimizationResult]:
        """优化参数"""
        try:
            # 检查是否需要优化
            if not force and not self._should_optimize():
                self.logger.info("暂时不需要参数优化")
                return []

            # 获取交易数据
            lookback_days = self.optimization_config["optimization_settings"]["lookback_days"]
            trades = self.trade_recorder.get_recent_trades(lookback_days)

            min_trades = self.optimization_config["optimization_settings"]["min_trades_for_optimization"]
            if len(trades) < min_trades:
                self.logger.warning(f"交易数据不足，需要至少 {min_trades} 笔交易，当前只有 {len(trades)} 笔")
                return []

            self.logger.info(f"开始参数优化，使用 {len(trades)} 笔交易数据")

            optimization_results = []

            # 1. 优化策略参数
            strategy_results = self._optimize_strategy_parameters(trades)
            optimization_results.extend(strategy_results)

            # 2. 优化止损止盈参数
            stop_loss_results = self._optimize_stop_loss_parameters(trades)
            optimization_results.extend(stop_loss_results)

            # 3. 优化货币对特定参数
            currency_results = self._optimize_currency_specific_parameters(trades)
            optimization_results.extend(currency_results)

            # 记录优化历史
            if optimization_results:
                self.optimization_history.append({
                    'timestamp': datetime.now().isoformat(),
                    'results': [asdict(result) for result in optimization_results],
                    'total_trades': len(trades),
                    'lookback_days': lookback_days
                })
                self._save_optimization_history()

            self.logger.info(f"参数优化完成，共优化 {len(optimization_results)} 个参数")
            return optimization_results

        except Exception as e:
            self.logger.error(f"参数优化失败: {e}")
            return []

    def _should_optimize(self) -> bool:
        """检查是否应该进行优化"""
        try:
            if not self.optimization_history:
                return True

            last_optimization = datetime.fromisoformat(self.optimization_history[-1]['timestamp'])
            days_since_last = (datetime.now() - last_optimization).days

            frequency_days = self.optimization_config["optimization_settings"]["optimization_frequency_days"]

            return days_since_last >= frequency_days

        except Exception:
            return True

    def _optimize_strategy_parameters(self, trades: List) -> List[OptimizationResult]:
        """优化策略参数"""
        results = []

        try:
            df = self._trades_to_dataframe(trades)

            strategy_params = self.optimization_config["strategy_parameters"]

            for param_name, param_config in strategy_params.items():
                result = self._optimize_single_parameter(
                    df, param_name, param_config, "strategy"
                )
                if result:
                    results.append(result)

        except Exception as e:
            self.logger.error(f"策略参数优化失败: {e}")

        return results

    def _optimize_stop_loss_parameters(self, trades: List) -> List[OptimizationResult]:
        """优化止损止盈参数"""
        results = []

        try:
            df = self._trades_to_dataframe(trades)

            stop_loss_params = self.optimization_config["stop_loss_parameters"]

            for param_name, param_config in stop_loss_params.items():
                result = self._optimize_single_parameter(
                    df, param_name, param_config, "stop_loss"
                )
                if result:
                    results.append(result)

        except Exception as e:
            self.logger.error(f"止损参数优化失败: {e}")

        return results

    def _optimize_currency_specific_parameters(self, trades: List) -> List[OptimizationResult]:
        """优化货币对特定参数"""
        results = []

        try:
            df = self._trades_to_dataframe(trades)

            currency_configs = self.optimization_config["currency_specific"]

            for symbol in df['symbol'].unique():
                if symbol not in currency_configs:
                    continue

                symbol_trades = df[df['symbol'] == symbol]
                if len(symbol_trades) < 20:  # 每个货币对至少20笔交易
                    continue

                symbol_params = currency_configs[symbol]

                for param_name, param_config in symbol_params.items():
                    result = self._optimize_single_parameter(
                        symbol_trades, f"{symbol}_{param_name}", param_config, "currency"
                    )
                    if result:
                        results.append(result)

        except Exception as e:
            self.logger.error(f"货币对参数优化失败: {e}")

        return results

    def _optimize_single_parameter(self, df: pd.DataFrame, param_name: str,
                                 param_config: Dict, param_type: str) -> Optional[OptimizationResult]:
        """优化单个参数"""
        try:
            current_value = param_config["current"]
            min_value = param_config["min"]
            max_value = param_config["max"]
            step = param_config["step"]

            # 生成候选值
            if param_config["type"] == "int":
                candidates = list(range(int(min_value), int(max_value) + 1, int(step)))
            else:
                candidates = np.arange(min_value, max_value + step, step).tolist()

            best_score = self._calculate_performance_score(df, param_name, current_value)
            best_value = current_value

            # 测试每个候选值
            for candidate in candidates:
                if candidate == current_value:
                    continue

                score = self._calculate_performance_score(df, param_name, candidate)

                if score > best_score:
                    best_score = score
                    best_value = candidate

            # 检查改进是否显著
            improvement = best_score - self._calculate_performance_score(df, param_name, current_value)
            min_improvement = self.optimization_config["optimization_settings"]["min_improvement_threshold"]

            if improvement > min_improvement:
                # 更新配置
                self._update_parameter_config(param_name, best_value, param_type)

                return OptimizationResult(
                    parameter_name=param_name,
                    old_value=current_value,
                    new_value=best_value,
                    improvement_score=improvement,
                    confidence=min(0.95, improvement * 2),  # 简单的置信度计算
                    reason=f"性能提升 {improvement:.3f}"
                )

        except Exception as e:
            self.logger.error(f"优化参数 {param_name} 失败: {e}")

        return None

    def _calculate_performance_score(self, df: pd.DataFrame, param_name: str, param_value: Any) -> float:
        """计算性能评分"""
        try:
            # 这里应该根据参数类型和值模拟交易结果
            # 为简化，我们使用基于历史数据的启发式方法

            if len(df) == 0:
                return 0.0

            win_rate = df['is_winning'].mean()
            avg_profit = df['profit_loss'].mean()
            profit_factor = df[df['profit_loss'] > 0]['profit_loss'].sum() / abs(df[df['profit_loss'] < 0]['profit_loss'].sum()) if len(df[df['profit_loss'] < 0]) > 0 else 1.0

            # 综合评分：胜率 * 平均利润 * 利润因子
            score = win_rate * avg_profit * profit_factor

            # 根据参数类型调整评分
            if 'confidence' in param_name.lower():
                # 置信度参数：更高的置信度应该对应更高的胜率
                confidence_bonus = (param_value - 0.5) * win_rate if param_value > 0.5 else 0
                score += confidence_bonus
            elif 'risk' in param_name.lower():
                # 风险参数：更低的风险更好，但不能太保守
                risk_penalty = param_value * 0.1 if param_value > 0.02 else 0
                score -= risk_penalty

            return score

        except Exception as e:
            self.logger.error(f"计算性能评分失败: {e}")
            return 0.0

    def _update_parameter_config(self, param_name: str, new_value: Any, param_type: str):
        """更新参数配置"""
        try:
            if param_type == "strategy":
                self.optimization_config["strategy_parameters"][param_name]["current"] = new_value
            elif param_type == "stop_loss":
                self.optimization_config["stop_loss_parameters"][param_name]["current"] = new_value
            elif param_type == "currency":
                # 解析货币对和参数名
                symbol, actual_param = param_name.split('_', 1)
                self.optimization_config["currency_specific"][symbol][actual_param]["current"] = new_value

            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.optimization_config, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.logger.error(f"更新参数配置失败: {e}")

    def _trades_to_dataframe(self, trades: List) -> pd.DataFrame:
        """将交易记录转换为DataFrame"""
        data = []
        for trade in trades:
            if trade.exit_time is None:
                continue

            data.append({
                'trade_id': trade.trade_id,
                'symbol': trade.symbol,
                'action': trade.action,
                'strategy_used': trade.strategy_used,
                'confidence': trade.confidence,
                'market_condition': trade.market_condition,
                'profit_loss': trade.profit_loss,
                'profit_loss_pct': trade.profit_loss_pct,
                'is_winning': trade.profit_loss > 0,
                'exit_reason': trade.exit_reason
            })

        return pd.DataFrame(data)

    def get_current_parameters(self) -> Dict:
        """获取当前参数"""
        try:
            current_params = {}

            # 策略参数
            for param_name, config in self.optimization_config["strategy_parameters"].items():
                current_params[param_name] = config["current"]

            # 止损参数
            for param_name, config in self.optimization_config["stop_loss_parameters"].items():
                current_params[param_name] = config["current"]

            # 货币对参数
            for symbol, symbol_config in self.optimization_config["currency_specific"].items():
                for param_name, config in symbol_config.items():
                    current_params[f"{symbol}_{param_name}"] = config["current"]

            return current_params

        except Exception as e:
            self.logger.error(f"获取当前参数失败: {e}")
            return {}

    def get_optimization_summary(self) -> Dict:
        """获取优化摘要"""
        try:
            if not self.optimization_history:
                return {'total_optimizations': 0}

            total_optimizations = len(self.optimization_history)
            last_optimization = self.optimization_history[-1]

            total_improvements = sum(len(opt['results']) for opt in self.optimization_history)

            return {
                'total_optimizations': total_optimizations,
                'total_improvements': total_improvements,
                'last_optimization_date': last_optimization['timestamp'],
                'last_optimization_results': len(last_optimization['results']),
                'current_parameters': self.get_current_parameters()
            }

        except Exception as e:
            self.logger.error(f"获取优化摘要失败: {e}")
            return {'error': str(e)}
