"""
测试新的风险回报比检查功能
"""
import os
import sys
import time
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.forex_trading_service import execute_trade
from app.utils import mt4_client


def test_risk_reward_ratio(risk_reward_ratio):
    """测试指定风险回报比的情况"""
    print("=" * 50)
    print(f"测试风险回报比为 1:{risk_reward_ratio} 的情况")
    print("=" * 50)
    
    # 获取当前价格
    market_info_response = mt4_client.mt4_client.get_market_info('EURUSD')
    if not market_info_response or market_info_response.get('status') != 'success':
        print("无法获取当前价格，测试终止")
        return None
    
    current_price = float(market_info_response['data']['ask'])
    print(f"当前价格: {current_price}")
    
    # 设置止损距离（100点）
    stop_loss_distance = 0.01
    stop_loss = current_price - stop_loss_distance
    
    # 根据指定的风险回报比设置止盈距离
    take_profit_distance = stop_loss_distance * risk_reward_ratio
    take_profit = current_price + take_profit_distance
    
    # 计算风险回报比
    risk = current_price - stop_loss
    reward = take_profit - current_price
    calculated_risk_reward_ratio = reward / risk
    
    print(f"止损: {stop_loss}")
    print(f"止盈: {take_profit}")
    print(f"风险: {risk:.5f}")
    print(f"回报: {reward:.5f}")
    print(f"风险回报比: {calculated_risk_reward_ratio:.2f}")
    
    # 创建交易指令
    trade_instructions = {
        'action': 'BUY',
        'orderType': 'MARKET',
        'entryPrice': None,
        'stopLoss': stop_loss,
        'takeProfit': take_profit,
        'lotSize': 0.01,
        'reasoning': f'测试风险回报比为 1:{risk_reward_ratio} 的情况',
        'isFinalDecision': True
    }
    
    print(f"交易指令: {trade_instructions}")
    
    # 执行交易
    result = execute_trade(trade_instructions, check_duplicate=True)
    
    print(f"交易结果: {result}")
    print("=" * 50)
    
    return result


def main():
    """主函数"""
    print("开始测试新的风险回报比检查功能")
    
    # 确保MT4客户端已连接
    if not mt4_client.mt4_client.is_connected:
        print("MT4客户端未连接，尝试连接")
        connected = mt4_client.mt4_client.connect()
        if not connected:
            print("无法连接到MT4客户端，测试终止")
            return
    
    # 测试风险回报比为0.5的情况（风险是回报的2倍）
    test_risk_reward_ratio(0.5)
    
    # 等待2秒
    time.sleep(2)
    
    # 测试风险回报比为0.8的情况（风险略大于回报）
    test_risk_reward_ratio(0.8)
    
    # 等待2秒
    time.sleep(2)
    
    # 测试风险回报比为1.0的情况（风险=回报）
    test_risk_reward_ratio(1.0)
    
    # 等待2秒
    time.sleep(2)
    
    # 测试风险回报比为1.5的情况（回报大于风险）
    test_risk_reward_ratio(1.5)
    
    # 等待2秒
    time.sleep(2)
    
    # 测试风险回报比为6.0的情况（回报远大于风险）
    test_risk_reward_ratio(6.0)
    
    print("测试完成")


if __name__ == "__main__":
    main()
