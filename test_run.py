"""
测试运行脚本 - 跳过MT4连接
用于在MT4服务器关闭时测试系统功能
"""
import os
import sys
from datetime import datetime

# 设置智能自动模式（不设置SKIP_MT4_CONNECTION，让系统自动判断）
# os.environ['SKIP_MT4_CONNECTION'] = 'true'  # 注释掉，使用智能模式
os.environ['FLASK_ENV'] = 'development'
os.environ['LOG_LEVEL'] = 'INFO'

print("========================================")
print("外汇交易系统 - 智能自动模式")
print("========================================")
print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("🧠 智能模式已启用")
print("🧠 系统将根据市场时间自动判断是否跳过MT4连接")
print("========================================")

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    import codecs
    try:
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
    except:
        pass

try:
    # 直接导入并运行主程序
    print("正在启动外汇交易系统...")

    # 导入应用
    from app import create_app

    # 创建应用
    app = create_app()

    print("系统启动成功！")
    print("按 Ctrl+C 退出系统")

    # 运行应用
    app.run(host='0.0.0.0', port=5000, debug=False)

except KeyboardInterrupt:
    print("\n用户中断，正在退出...")
except Exception as e:
    print(f"启动失败: {e}")
    import traceback
    traceback.print_exc()
finally:
    print("系统已退出")
