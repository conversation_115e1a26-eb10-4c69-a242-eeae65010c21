@echo off
echo ========================================
echo Forex Trading System - Package Script
echo ========================================

:: Set variables
set PACKAGE_NAME=ForexTradingSystem_v1.0.0
set PACKAGE_DIR=%PACKAGE_NAME%
set CURRENT_DIR=%~dp0

:: Clean old package
if exist "%PACKAGE_DIR%" (
    echo Cleaning old package...
    rmdir /s /q "%PACKAGE_DIR%"
)

if exist "%PACKAGE_NAME%.zip" (
    del "%PACKAGE_NAME%.zip"
)

:: Create package directory
echo Creating package directory...
mkdir "%PACKAGE_DIR%"

:: Copy core files
echo Copying core files...
xcopy /E /I /Y "app" "%PACKAGE_DIR%\app\"
copy "run.py" "%PACKAGE_DIR%\"
copy "config.py" "%PACKAGE_DIR%\"
copy "requirements.txt" "%PACKAGE_DIR%\"
copy ".env.example" "%PACKAGE_DIR%\"
copy "start_server.bat" "%PACKAGE_DIR%\"
copy "install_service.bat" "%PACKAGE_DIR%\"
copy "update_system.bat" "%PACKAGE_DIR%\"
copy "remote_update_client.py" "%PACKAGE_DIR%\"
copy "simple_dashboard.py" "%PACKAGE_DIR%\"

:: Copy documentation
if exist "Windows_部署指南.md" copy "Windows_部署指南.md" "%PACKAGE_DIR%\"
if exist "README.md" copy "README.md" "%PACKAGE_DIR%\"

:: Create directories
mkdir "%PACKAGE_DIR%\logs"
mkdir "%PACKAGE_DIR%\backups"

:: Create deployment info
echo Creating deployment info...
(
echo # Forex Trading System Deployment Package
echo.
echo Version: v1.0.0
echo Package Date: %date% %time%
echo Target OS: Windows Server 2012+
echo.
echo ## Quick Start
echo 1. Extract to target directory
echo 2. Install Python 3.9+
echo 3. Configure .env.local file
echo 4. Run start_server.bat to test
echo 5. Run install_service.bat to install as service
echo.
echo ## Important Files
echo - start_server.bat: Start script
echo - install_service.bat: Service installer
echo - update_system.bat: Update script
echo - .env.example: Configuration template
echo.
echo For detailed instructions, see documentation files.
) > "%PACKAGE_DIR%\DEPLOYMENT_README.txt"

:: Create version file
echo Creating version file...
(
echo {
echo   "version": "1.0.0",
echo   "build_date": "%date%",
echo   "build_time": "%time%",
echo   "description": "Forex Trading System Production Release",
echo   "features": [
echo     "Intelligent Forex Analysis",
echo     "Real-time Trading Monitor",
echo     "Risk Management System",
echo     "Multi-currency Support",
echo     "Remote Update Capability"
echo   ]
echo }
) > "%PACKAGE_DIR%\version.json"

:: Copy config template as local config
if not exist "%PACKAGE_DIR%\.env.local" (
    copy "%PACKAGE_DIR%\.env.example" "%PACKAGE_DIR%\.env.local"
)

:: Create package using PowerShell
echo Creating ZIP package...
powershell -command "Compress-Archive -Path '%PACKAGE_DIR%' -DestinationPath '%PACKAGE_NAME%.zip' -Force"

if exist "%PACKAGE_NAME%.zip" (
    echo.
    echo ========================================
    echo Package created successfully!
    echo ========================================
    echo Package file: %PACKAGE_NAME%.zip
    
    :: Show file size
    for %%I in ("%PACKAGE_NAME%.zip") do (
        echo Package size: %%~zI bytes
    )
    
    :: Clean up temp directory
    rmdir /s /q "%PACKAGE_DIR%"
    
    echo.
    echo Next steps:
    echo 1. Upload %PACKAGE_NAME%.zip to your server
    echo 2. Extract to target directory
    echo 3. Follow DEPLOYMENT_README.txt instructions
    echo.
) else (
    echo.
    echo Package creation failed!
    echo Please check if PowerShell is available.
)

echo.
echo Press any key to continue...
pause >nul
