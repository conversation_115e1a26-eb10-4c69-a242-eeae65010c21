#!/usr/bin/env python3
"""
调试模型加载问题
检查为什么系统显示加载了4/4个Trainer模型，但仍然初始化标准模型
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.ml_engine.lightweight_ml_engine import LightweightMLEngine, ModelType

def debug_model_loading():
    """调试模型加载过程"""
    print("🔍 调试模型加载过程")
    print("="*60)
    
    # 创建ML引擎实例（会触发模型加载）
    print("🤖 创建ML引擎实例...")
    ml_engine = LightweightMLEngine()
    
    print("\n📊 检查加载的模型:")
    print("-"*40)
    
    all_loaded = True
    trainer_models = 0
    standard_models = 0
    
    for model_type in ModelType:
        model = ml_engine.models.get(model_type)
        scaler = ml_engine.scalers.get(model_type)
        performance = ml_engine.model_performance.get(model_type, 0)
        
        print(f"\n🔹 {model_type.value}:")
        
        if model is not None:
            model_name = type(model).__name__
            print(f"   模型: {model_name}")
            print(f"   性能: {performance:.2f}")
            
            # 判断是否是Trainer模型（高性能指标0.85）
            if performance >= 0.8:
                print(f"   类型: ✅ Trainer高级模型")
                trainer_models += 1
            else:
                print(f"   类型: 📊 标准模型")
                standard_models += 1
        else:
            print(f"   模型: ❌ None")
            all_loaded = False
        
        if scaler is not None:
            scaler_name = type(scaler).__name__
            print(f"   缩放器: {scaler_name}")
        else:
            print(f"   缩放器: ❌ None")
    
    print("\n" + "="*60)
    print("📈 统计结果:")
    print(f"   Trainer高级模型: {trainer_models}个")
    print(f"   标准模型: {standard_models}个")
    print(f"   总模型数: {trainer_models + standard_models}/{len(ModelType)}")
    print(f"   所有模型已加载: {'✅' if all_loaded else '❌'}")
    
    # 检查模型文件
    print("\n📁 检查模型文件:")
    print("-"*40)
    
    model_dir = ml_engine.model_dir
    print(f"模型目录: {model_dir}")
    
    if os.path.exists(model_dir):
        model_files = [f for f in os.listdir(model_dir) if f.endswith('.pkl')]
        print(f"模型文件数量: {len(model_files)}")
        
        for file in sorted(model_files):
            file_path = os.path.join(model_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"   📄 {file} ({file_size:,}字节)")
    else:
        print("❌ 模型目录不存在")
    
    # 分析问题
    print("\n🔍 问题分析:")
    print("-"*40)
    
    if trainer_models == 4:
        print("✅ 所有4个模型都是Trainer高级模型")
        print("✅ 系统完全使用Trainer训练的模型")
    elif trainer_models > 0 and standard_models > 0:
        print(f"⚠️ 混合模式: {trainer_models}个Trainer模型 + {standard_models}个标准模型")
        print("💡 这是正常的，系统会优先使用Trainer模型，标准模型作为补充")
    elif standard_models == 4:
        print("❌ 所有模型都是标准模型")
        print("💡 可能Trainer模型加载失败，系统回退到标准模型")
    else:
        print("❓ 异常状态，需要进一步检查")
    
    return trainer_models, standard_models

if __name__ == "__main__":
    debug_model_loading()
