version: '3.8'

services:
  # 外汇交易系统主服务
  forex-trading-system:
    build: .
    container_name: forex_trading_system
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - PYTHONPATH=/app
      - PYTHONIOENCODING=utf-8
    volumes:
      - ./logs:/app/logs
      - ./app/data:/app/app/data
      - ./.env.local:/app/.env.local
    restart: unless-stopped
    networks:
      - forex-network
    depends_on:
      - redis

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: forex_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - forex-network

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: forex_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - forex-trading-system
    restart: unless-stopped
    networks:
      - forex-network

volumes:
  redis_data:

networks:
  forex-network:
    driver: bridge
