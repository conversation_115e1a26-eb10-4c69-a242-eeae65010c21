#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控系统集成工具
提供简单的接口来记录各种系统事件到监控系统
"""

from datetime import datetime
from typing import Optional

def get_monitor():
    """获取监控实例"""
    try:
        from app.utils.real_time_monitor import real_time_monitor
        return real_time_monitor
    except ImportError:
        return None

def record_analysis_start(analysis_type: str = 'unknown'):
    """记录分析开始事件"""
    monitor = get_monitor()
    if monitor:
        try:
            monitor.add_alert('info', 'analysis', f'{analysis_type}分析开始')
        except Exception as e:
            print(f"记录分析开始事件失败: {e}")

def record_analysis_success(analysis_type: str = 'unknown', response_time: float = 0, 
                          tokens: int = 0, cost: float = 0):
    """记录分析成功事件"""
    monitor = get_monitor()
    if monitor:
        try:
            monitor.record_analysis_event(
                event_type=analysis_type,
                success=True,
                response_time=response_time,
                tokens=tokens,
                cost=cost
            )
            monitor.add_alert('info', 'analysis', f'{analysis_type}分析成功完成')
        except Exception as e:
            print(f"记录分析成功事件失败: {e}")

def record_analysis_failure(analysis_type: str = 'unknown', error_message: str = ''):
    """记录分析失败事件"""
    monitor = get_monitor()
    if monitor:
        try:
            monitor.record_analysis_event(
                event_type=analysis_type,
                success=False,
                response_time=0,
                tokens=0,
                cost=0
            )
            monitor.record_error_event('api_failure')
            monitor.add_alert('error', 'analysis', f'{analysis_type}分析失败: {error_message}')
        except Exception as e:
            print(f"记录分析失败事件失败: {e}")

def record_trading_event(event_type: str, message: str, level: str = 'info'):
    """记录交易事件"""
    monitor = get_monitor()
    if monitor:
        try:
            monitor.add_alert(level, 'trading', f'{event_type}: {message}')
        except Exception as e:
            print(f"记录交易事件失败: {e}")

def record_mt4_event(event_type: str, message: str, level: str = 'info'):
    """记录MT4事件"""
    monitor = get_monitor()
    if monitor:
        try:
            monitor.add_alert(level, 'mt4', f'{event_type}: {message}')
        except Exception as e:
            print(f"记录MT4事件失败: {e}")

def record_system_event(event_type: str, message: str, level: str = 'info'):
    """记录系统事件"""
    monitor = get_monitor()
    if monitor:
        try:
            monitor.add_alert(level, 'system', f'{event_type}: {message}')
        except Exception as e:
            print(f"记录系统事件失败: {e}")

def record_json_parse_error(error_message: str = ''):
    """记录JSON解析错误"""
    monitor = get_monitor()
    if monitor:
        try:
            monitor.record_error_event('json_parse')
            monitor.add_alert('warning', 'analysis', f'JSON解析失败: {error_message}')
        except Exception as e:
            print(f"记录JSON解析错误失败: {e}")

def record_api_failure(api_name: str, error_message: str = ''):
    """记录API调用失败"""
    monitor = get_monitor()
    if monitor:
        try:
            monitor.record_error_event('api_failure')
            monitor.add_alert('error', 'system', f'{api_name} API调用失败: {error_message}')
        except Exception as e:
            print(f"记录API失败事件失败: {e}")

def record_critical_error(component: str, error_message: str):
    """记录严重错误"""
    monitor = get_monitor()
    if monitor:
        try:
            monitor.add_alert('critical', 'system', f'{component}严重错误: {error_message}')
        except Exception as e:
            print(f"记录严重错误失败: {e}")

def get_monitoring_status():
    """获取监控状态"""
    monitor = get_monitor()
    if monitor:
        try:
            return monitor.get_current_status()
        except Exception as e:
            print(f"获取监控状态失败: {e}")
            return None
    return None

def is_monitoring_active():
    """检查监控是否活跃"""
    monitor = get_monitor()
    if monitor:
        try:
            return monitor.monitoring
        except Exception:
            return False
    return False

# 装饰器：自动记录函数执行
def monitor_function(func_name: str = None, category: str = 'system'):
    """
    装饰器：自动记录函数的执行情况
    
    Args:
        func_name: 函数名称，如果不提供则使用实际函数名
        category: 事件类别
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            name = func_name or func.__name__
            start_time = datetime.now()
            
            try:
                # 记录开始
                monitor = get_monitor()
                if monitor:
                    monitor.add_alert('info', category, f'{name}开始执行')
                
                # 执行函数
                result = func(*args, **kwargs)
                
                # 记录成功
                end_time = datetime.now()
                elapsed_time = (end_time - start_time).total_seconds()
                if monitor:
                    monitor.add_alert('info', category, f'{name}执行成功，耗时: {elapsed_time:.2f}秒')
                
                return result
                
            except Exception as e:
                # 记录失败
                end_time = datetime.now()
                elapsed_time = (end_time - start_time).total_seconds()
                monitor = get_monitor()
                if monitor:
                    monitor.add_alert('error', category, f'{name}执行失败: {str(e)}，耗时: {elapsed_time:.2f}秒')
                raise
        
        return wrapper
    return decorator

# 上下文管理器：自动记录代码块执行
class MonitorContext:
    """监控上下文管理器"""
    
    def __init__(self, operation_name: str, category: str = 'system', level: str = 'info'):
        self.operation_name = operation_name
        self.category = category
        self.level = level
        self.start_time = None
        self.monitor = get_monitor()
    
    def __enter__(self):
        self.start_time = datetime.now()
        if self.monitor:
            try:
                self.monitor.add_alert(self.level, self.category, f'{self.operation_name}开始')
            except Exception as e:
                print(f"记录监控上下文开始失败: {e}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time and self.monitor:
            try:
                end_time = datetime.now()
                elapsed_time = (end_time - self.start_time).total_seconds()
                
                if exc_type is None:
                    # 成功完成
                    self.monitor.add_alert(self.level, self.category, 
                                         f'{self.operation_name}完成，耗时: {elapsed_time:.2f}秒')
                else:
                    # 发生异常
                    self.monitor.add_alert('error', self.category, 
                                         f'{self.operation_name}失败: {str(exc_val)}，耗时: {elapsed_time:.2f}秒')
            except Exception as e:
                print(f"记录监控上下文结束失败: {e}")

# 使用示例：
# 
# # 使用装饰器
# @monitor_function('数据获取', 'trading')
# def get_market_data():
#     pass
#
# # 使用上下文管理器
# with MonitorContext('执行交易', 'trading'):
#     execute_trade()
#
# # 直接调用函数
# record_analysis_start('预分析')
# record_trading_event('订单执行', '买入订单已提交')
