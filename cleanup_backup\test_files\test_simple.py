"""
简单测试脚本，用于验证JSON解析修复
"""
import sys
from datetime import datetime
sys.path.append('.')

print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试开始')

from app.utils.llm_client import parse_trade_instructions

# 测试用例：日期时间格式正确的JSON
test_json = """
```json
{
  "action": "BUY",
  "orderType": "LIMIT",
  "entryPrice": 1.1309,
  "stopLoss": 1.1294,
  "takeProfit": 1.1335,
  "lotSize": 0.12,
  "conditions": ["15分钟RSI<65时激活", "突破1.1318后移动止损至1.1309"],
  "expiration": "2025-05-23 14:30:00",
  "riskLevel": "MEDIUM_HIGH",
  "reasoning": "价格回踩13日均线，符合右侧交易策略"
}
```
"""

# 解析JSON
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 开始解析JSON')
result = parse_trade_instructions(test_json)

# 打印结果
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 解析结果:')
print(f"动作: {result['action']}")
print(f"订单类型: {result['orderType']}")
print(f"入场价格: {result['entryPrice']}")
print(f"止损: {result['stopLoss']}")
print(f"止盈: {result['takeProfit']}")
print(f"手数: {result['lotSize']}")
print(f"条件: {result['conditions']}")
print(f"过期时间: {result['expiration']}")
print(f"风险等级: {result['riskLevel']}")
print(f"理由: {result['reasoning']}")

# 验证结果
assert result['action'] == 'BUY', "动作应该是BUY"
assert result['orderType'] == 'LIMIT', "订单类型应该是LIMIT"
assert result['entryPrice'] == 1.1309, "入场价格应该是1.1309"
assert result['stopLoss'] == 1.1294, "止损应该是1.1294"
assert result['takeProfit'] == 1.1335, "止盈应该是1.1335"
assert result['lotSize'] == 0.12, "手数应该是0.12"
assert len(result['conditions']) == 2, "应该有2个条件"
assert "15分钟RSI<65时激活" in result['conditions'], "条件1应该是'15分钟RSI<65时激活'"
assert "突破1.1318后移动止损至1.1309" in result['conditions'], "条件2应该是'突破1.1318后移动止损至1.1309'"
assert result['expiration'] == "2025-05-23 14:30:00" or result['expiration'] == "2025-05-23 14:30", "过期时间应该是'2025-05-23 14:30:00'或'2025-05-23 14:30'"

print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试通过: 系统成功解析了JSON')
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试结束')
