#!/usr/bin/env python3
"""
测试模型有效性
验证训练的模型是否真的有交易效果
"""

import sys
import os
import pandas as pd
import numpy as np
import joblib
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_model_effectiveness():
    """测试模型有效性"""
    print("🚀 测试模型交易有效性")
    print("="*60)
    
    try:
        # 1. 检查模型文件
        models_dir = Path("data/models")
        if not models_dir.exists():
            print("❌ 模型目录不存在")
            return False
        
        model_files = list(models_dir.glob("*.pkl"))
        if not model_files:
            print("❌ 没有找到模型文件")
            return False
        
        print(f"📦 找到{len(model_files)}个模型文件")
        
        # 2. 加载ML引擎测试预测
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine, ModelType
        
        print("\n🤖 初始化ML引擎...")
        ml_engine = LightweightMLEngine()
        
        # 检查加载的模型
        trainer_models = 0
        for model_type, model in ml_engine.models.items():
            if model is not None:
                model_name = type(model).__name__
                performance = ml_engine.model_performance.get(model_type, 0.5)
                
                if 'LGBM' in model_name or 'LGB' in model_name or 'XGB' in model_name:
                    trainer_models += 1
                    status = "🤖 Trainer高级模型"
                else:
                    status = "📊 标准模型"
                
                print(f"   {model_type.value}: {model_name} - {status} (性能: {performance:.3f})")
        
        print(f"\n📊 模型统计: {trainer_models}个Trainer模型, {len(ml_engine.models)-trainer_models}个标准模型")
        
        # 3. 生成模拟市场数据进行测试
        print("\n📊 生成模拟市场数据...")
        test_data = generate_test_market_data()
        
        # 4. 测试模型预测
        print("\n🔮 测试模型预测...")
        predictions = test_model_predictions(ml_engine, test_data)
        
        # 5. 模拟交易回测
        print("\n💰 模拟交易回测...")
        backtest_result = simulate_trading(test_data, predictions)
        
        # 6. 评估结果
        print("\n📈 评估模型效果...")
        effectiveness = evaluate_effectiveness(backtest_result)
        
        return effectiveness > 0.5
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_test_market_data(days: int = 7) -> pd.DataFrame:
    """生成测试市场数据"""
    try:
        # 生成7天的5分钟数据
        periods = days * 24 * 12  # 7天 * 24小时 * 12个5分钟
        
        # 基础价格（EURUSD）
        base_price = 1.0800
        
        # 生成随机价格走势
        np.random.seed(42)  # 固定随机种子以便重现
        
        # 价格变化（随机游走 + 趋势）
        price_changes = np.random.normal(0, 0.0001, periods)  # 正态分布价格变化
        trend = np.linspace(0, 0.002, periods)  # 轻微上升趋势
        
        prices = [base_price]
        for i in range(periods - 1):
            new_price = prices[-1] + price_changes[i] + trend[i] / periods
            prices.append(max(0.5, new_price))  # 防止负价格
        
        # 生成OHLC数据
        data = []
        for i in range(periods):
            open_price = prices[i]
            close_price = prices[i] if i == 0 else prices[i]
            
            # 生成高低价
            volatility = np.random.uniform(0.0001, 0.0005)
            high_price = open_price + volatility
            low_price = open_price - volatility
            
            # 确保OHLC逻辑正确
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            data.append({
                'timestamp': datetime.now() - timedelta(minutes=5*(periods-i)),
                'symbol': 'EURUSD',
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': np.random.randint(1000, 5000)
            })
        
        df = pd.DataFrame(data)
        print(f"✅ 生成{len(df)}条测试数据")
        return df
        
    except Exception as e:
        print(f"❌ 生成测试数据失败: {e}")
        return pd.DataFrame()

def test_model_predictions(ml_engine, data: pd.DataFrame) -> dict:
    """测试模型预测"""
    try:
        predictions = {}
        
        for idx, row in data.iterrows():
            try:
                # 构造特征数据
                features = {
                    'close': row['close'],
                    'high': row['high'],
                    'low': row['low'],
                    'volume': row['volume'],
                    'price_change': row['close'] - row['open'],
                    'volatility': row['high'] - row['low'],
                    'timestamp': row['timestamp']
                }
                
                # 获取ML预测
                ml_predictions = ml_engine.predict(features)
                
                if ml_predictions:
                    # 提取价格预测信号
                    price_pred = ml_predictions.get('price_prediction')
                    if price_pred:
                        signal_strength = price_pred.prediction
                        confidence = price_pred.confidence
                        
                        predictions[idx] = {
                            'signal': signal_strength,
                            'confidence': confidence,
                            'direction': 'LONG' if signal_strength > 0 else 'SHORT',
                            'strength': abs(signal_strength)
                        }
                
            except Exception as e:
                # 跳过有问题的数据点
                continue
        
        print(f"✅ 生成{len(predictions)}个预测信号")
        return predictions
        
    except Exception as e:
        print(f"❌ 模型预测失败: {e}")
        return {}

def simulate_trading(data: pd.DataFrame, predictions: dict) -> dict:
    """模拟交易"""
    try:
        initial_capital = 10000.0
        current_capital = initial_capital
        
        trades = []
        open_trades = []
        
        for idx, row in data.iterrows():
            current_price = row['close']
            current_time = row['timestamp']
            
            # 检查平仓条件
            for trade in open_trades.copy():
                # 简单的止损止盈逻辑
                if trade['direction'] == 'LONG':
                    profit_pips = (current_price - trade['entry_price']) * 10000
                else:
                    profit_pips = (trade['entry_price'] - current_price) * 10000
                
                # 止损20点，止盈40点
                if profit_pips <= -20 or profit_pips >= 40:
                    trade['exit_price'] = current_price
                    trade['exit_time'] = current_time
                    trade['profit_pips'] = profit_pips
                    trade['profit_usd'] = profit_pips * 1.0  # 简化计算
                    
                    current_capital += trade['profit_usd']
                    trades.append(trade)
                    open_trades.remove(trade)
            
            # 检查开仓信号
            if idx in predictions:
                pred = predictions[idx]
                
                # 只有高置信度信号才交易
                if pred['confidence'] > 0.6 and pred['strength'] > 0.3:
                    # 限制同时开仓数量
                    if len(open_trades) < 3:
                        trade = {
                            'entry_time': current_time,
                            'entry_price': current_price,
                            'direction': pred['direction'],
                            'size': 0.1,  # 0.1手
                            'confidence': pred['confidence']
                        }
                        open_trades.append(trade)
        
        # 关闭剩余交易
        if open_trades:
            final_price = data.iloc[-1]['close']
            final_time = data.iloc[-1]['timestamp']
            
            for trade in open_trades:
                if trade['direction'] == 'LONG':
                    profit_pips = (final_price - trade['entry_price']) * 10000
                else:
                    profit_pips = (trade['entry_price'] - final_price) * 10000
                
                trade['exit_price'] = final_price
                trade['exit_time'] = final_time
                trade['profit_pips'] = profit_pips
                trade['profit_usd'] = profit_pips * 1.0
                
                current_capital += trade['profit_usd']
                trades.append(trade)
        
        # 计算结果
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['profit_usd'] > 0])
        total_profit = sum(t['profit_usd'] for t in trades)
        
        result = {
            'initial_capital': initial_capital,
            'final_capital': current_capital,
            'total_profit': total_profit,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'win_rate': winning_trades / total_trades if total_trades > 0 else 0,
            'trades': trades
        }
        
        print(f"✅ 模拟交易完成: {total_trades}笔交易")
        return result
        
    except Exception as e:
        print(f"❌ 模拟交易失败: {e}")
        return {}

def evaluate_effectiveness(result: dict) -> float:
    """评估模型效果"""
    try:
        print("\n📊 模型效果评估")
        print("-"*40)
        
        if not result or result['total_trades'] == 0:
            print("❌ 没有交易数据")
            return 0.0
        
        # 显示基础统计
        print(f"💰 资金变化:")
        print(f"   初始资金: ${result['initial_capital']:.2f}")
        print(f"   最终资金: ${result['final_capital']:.2f}")
        print(f"   总盈亏: ${result['total_profit']:.2f}")
        print(f"   收益率: {(result['total_profit']/result['initial_capital']):.1%}")
        
        print(f"\n📈 交易统计:")
        print(f"   总交易数: {result['total_trades']}")
        print(f"   盈利交易: {result['winning_trades']}")
        print(f"   胜率: {result['win_rate']:.1%}")
        
        # 计算效果评分
        effectiveness = 0.0
        
        # 1. 盈利能力 (50%)
        if result['total_profit'] > 0:
            profit_score = min(0.5, result['total_profit'] / 500)  # 每500美元得50%
            effectiveness += profit_score
            print(f"✅ 盈利能力: {profit_score:.1%}")
        else:
            print(f"❌ 盈利能力: 0%")
        
        # 2. 胜率 (30%)
        win_rate_score = result['win_rate'] * 0.3
        effectiveness += win_rate_score
        print(f"{'✅' if result['win_rate'] > 0.5 else '⚠️'} 胜率评分: {win_rate_score:.1%}")
        
        # 3. 交易频率 (20%)
        if result['total_trades'] > 5:
            frequency_score = 0.2
            effectiveness += frequency_score
            print(f"✅ 交易频率: {frequency_score:.1%}")
        else:
            print(f"⚠️ 交易频率: 0% (交易太少)")
        
        print(f"\n🏆 总体效果评分: {effectiveness:.1%}")
        
        # 评级
        if effectiveness >= 0.7:
            grade = "🌟 优秀"
        elif effectiveness >= 0.5:
            grade = "✅ 良好"
        elif effectiveness >= 0.3:
            grade = "⚠️ 一般"
        else:
            grade = "❌ 较差"
        
        print(f"🎯 效果等级: {grade}")
        
        return effectiveness
        
    except Exception as e:
        print(f"❌ 评估失败: {e}")
        return 0.0

if __name__ == "__main__":
    success = test_model_effectiveness()
    
    if success:
        print("\n🎉 模型效果测试完成！")
        print("✅ 模型具有一定的交易效果")
    else:
        print("\n⚠️ 模型效果有待改进")
        print("💡 建议重新训练或优化模型参数")
