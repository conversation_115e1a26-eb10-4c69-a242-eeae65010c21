"""
订单结果分析模块
用于分析交易订单的结果（止损/止盈），提供奖惩机制
"""
import os
import json
import math
from datetime import datetime, timedelta
from app.utils.error_logger import log_error, ErrorType
from app.utils.performance_evaluator import get_virtual_account, TradeResult


# 结果类型枚举
class OrderResultType:
    """订单结果类型枚举"""
    TAKE_PROFIT = "TAKE_PROFIT"  # 止盈
    STOP_LOSS = "STOP_LOSS"  # 止损
    MANUAL_CLOSE = "MANUAL_CLOSE"  # 手动平仓
    UNKNOWN = "UNKNOWN"  # 未知


# 订单结果数据文件路径
ORDER_RESULT_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
ORDER_RESULT_FILE = os.path.join(ORDER_RESULT_DIR, 'order_results.json')

# 确保数据目录存在
if not os.path.exists(ORDER_RESULT_DIR):
    os.makedirs(ORDER_RESULT_DIR)


def load_order_results():
    """
    加载订单结果数据
    
    Returns:
        list: 订单结果数据列表
    """
    try:
        if not os.path.exists(ORDER_RESULT_FILE):
            return []
        
        with open(ORDER_RESULT_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f'加载订单结果数据失败: {e}')
        return []


def save_order_results(results):
    """
    保存订单结果数据
    
    Args:
        results (list): 订单结果数据列表
    """
    try:
        with open(ORDER_RESULT_FILE, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f'保存订单结果数据失败: {e}')


def determine_result_type(trade):
    """
    确定订单结果类型
    
    Args:
        trade (dict): 交易数据
        
    Returns:
        str: 订单结果类型
    """
    # 如果交易没有平仓，返回未知
    if trade.get('status') != 'CLOSED':
        return OrderResultType.UNKNOWN
    
    # 获取入场价格、出场价格、止损价格和止盈价格
    entry_price = trade.get('entry_price')
    exit_price = trade.get('exit_price')
    stop_loss = trade.get('stop_loss')
    take_profit = trade.get('take_profit')
    
    # 如果缺少必要数据，返回未知
    if None in [entry_price, exit_price, stop_loss, take_profit]:
        return OrderResultType.UNKNOWN
    
    # 计算价格差异的绝对值
    tp_diff = abs(exit_price - take_profit)
    sl_diff = abs(exit_price - stop_loss)
    
    # 设置阈值，用于判断是否触及止损/止盈
    threshold = 0.0002  # 2个点的阈值
    
    # 判断是否触及止损/止盈
    if tp_diff <= threshold:
        return OrderResultType.TAKE_PROFIT
    elif sl_diff <= threshold:
        return OrderResultType.STOP_LOSS
    else:
        return OrderResultType.MANUAL_CLOSE


def analyze_closed_trade(trade):
    """
    分析已平仓交易
    
    Args:
        trade (dict): 交易数据
        
    Returns:
        dict: 分析结果
    """
    try:
        # 确定结果类型
        result_type = determine_result_type(trade)
        
        # 计算持仓时间
        entry_time = datetime.fromisoformat(trade.get('time', '').replace('Z', '+00:00'))
        exit_time = datetime.fromisoformat(trade.get('exit_time', '').replace('Z', '+00:00'))
        duration_hours = (exit_time - entry_time).total_seconds() / 3600
        
        # 计算盈亏比例
        profit_loss = trade.get('profit_loss', 0)
        profit_loss_pips = trade.get('profit_loss_pips', 0)
        
        # 计算风险回报比
        risk = 0
        if trade.get('direction') == 'BUY':
            risk = (trade.get('entry_price', 0) - trade.get('stop_loss', 0)) * 10000  # 转换为点数
        else:  # SELL
            risk = (trade.get('stop_loss', 0) - trade.get('entry_price', 0)) * 10000  # 转换为点数
        
        reward = abs(profit_loss_pips)
        risk_reward_ratio = reward / risk if risk > 0 else 0
        
        # 构建分析结果
        analysis = {
            'trade_id': trade.get('id'),
            'symbol': trade.get('symbol'),
            'direction': trade.get('direction'),
            'entry_price': trade.get('entry_price'),
            'exit_price': trade.get('exit_price'),
            'stop_loss': trade.get('stop_loss'),
            'take_profit': trade.get('take_profit'),
            'entry_time': trade.get('time'),
            'exit_time': trade.get('exit_time'),
            'duration_hours': duration_hours,
            'profit_loss': profit_loss,
            'profit_loss_pips': profit_loss_pips,
            'result': trade.get('result'),
            'result_type': result_type,
            'risk_reward_ratio': risk_reward_ratio,
            'analyzed_at': datetime.now().isoformat()
        }
        
        return analysis
    except Exception as e:
        print(f'分析交易失败: {e}')
        
        # 记录错误
        log_error(
            error_type=ErrorType.UNKNOWN_ERROR,
            message=f'分析交易失败: {e}',
            details={'exception': str(e), 'trade': trade},
            operation='ANALYZE_TRADE'
        )
        
        return None


def update_order_results():
    """
    更新订单结果数据
    
    Returns:
        bool: 是否成功更新
    """
    try:
        # 获取虚拟账户数据
        account = get_virtual_account()
        
        # 获取已平仓交易
        closed_trades = account.get('closed_trades', [])
        
        # 加载现有的订单结果数据
        results = load_order_results()
        
        # 获取已分析的交易ID列表
        analyzed_ids = [r.get('trade_id') for r in results]
        
        # 分析新的已平仓交易
        new_analyses = []
        for trade in closed_trades:
            trade_id = trade.get('id')
            if trade_id not in analyzed_ids:
                analysis = analyze_closed_trade(trade)
                if analysis:
                    new_analyses.append(analysis)
                    analyzed_ids.append(trade_id)
        
        # 如果有新的分析结果，更新数据文件
        if new_analyses:
            results.extend(new_analyses)
            save_order_results(results)
            print(f'更新了 {len(new_analyses)} 个新的订单结果分析')
        
        return True
    except Exception as e:
        print(f'更新订单结果数据失败: {e}')
        
        # 记录错误
        log_error(
            error_type=ErrorType.UNKNOWN_ERROR,
            message=f'更新订单结果数据失败: {e}',
            details={'exception': str(e)},
            operation='UPDATE_ORDER_RESULTS'
        )
        
        return False


def get_order_result_statistics(days=30):
    """
    获取订单结果统计
    
    Args:
        days (int): 统计天数
        
    Returns:
        dict: 订单结果统计
    """
    try:
        # 更新订单结果数据
        update_order_results()
        
        # 加载订单结果数据
        results = load_order_results()
        
        # 计算统计开始时间
        now = datetime.now()
        start_time = now - timedelta(days=days)
        start_time_str = start_time.isoformat()
        
        # 过滤最近的订单结果
        recent_results = [r for r in results if r.get('exit_time', '') >= start_time_str]
        
        # 统计各类型订单数量
        total_count = len(recent_results)
        take_profit_count = len([r for r in recent_results if r.get('result_type') == OrderResultType.TAKE_PROFIT])
        stop_loss_count = len([r for r in recent_results if r.get('result_type') == OrderResultType.STOP_LOSS])
        manual_close_count = len([r for r in recent_results if r.get('result_type') == OrderResultType.MANUAL_CLOSE])
        
        # 计算比例
        take_profit_rate = take_profit_count / total_count * 100 if total_count > 0 else 0
        stop_loss_rate = stop_loss_count / total_count * 100 if total_count > 0 else 0
        manual_close_rate = manual_close_count / total_count * 100 if total_count > 0 else 0
        
        # 计算平均持仓时间
        avg_duration = sum([r.get('duration_hours', 0) for r in recent_results]) / total_count if total_count > 0 else 0
        
        # 计算平均风险回报比
        avg_risk_reward = sum([r.get('risk_reward_ratio', 0) for r in recent_results]) / total_count if total_count > 0 else 0
        
        # 构建统计结果
        statistics = {
            'total_count': total_count,
            'take_profit_count': take_profit_count,
            'stop_loss_count': stop_loss_count,
            'manual_close_count': manual_close_count,
            'take_profit_rate': take_profit_rate,
            'stop_loss_rate': stop_loss_rate,
            'manual_close_rate': manual_close_rate,
            'avg_duration_hours': avg_duration,
            'avg_risk_reward_ratio': avg_risk_reward,
            'period_days': days,
            'generated_at': now.isoformat()
        }
        
        return statistics
    except Exception as e:
        print(f'获取订单结果统计失败: {e}')
        
        # 记录错误
        log_error(
            error_type=ErrorType.UNKNOWN_ERROR,
            message=f'获取订单结果统计失败: {e}',
            details={'exception': str(e)},
            operation='GET_ORDER_RESULT_STATISTICS'
        )
        
        return {
            'total_count': 0,
            'take_profit_count': 0,
            'stop_loss_count': 0,
            'manual_close_count': 0,
            'take_profit_rate': 0,
            'stop_loss_rate': 0,
            'manual_close_rate': 0,
            'avg_duration_hours': 0,
            'avg_risk_reward_ratio': 0,
            'period_days': days,
            'generated_at': now.isoformat(),
            'error': str(e)
        }
