#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantumForex Pro - 实际MT4仓位调整测试
测试符合MT4实际限制的仓位调整功能
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.execution_engine.trade_executor import TradeExecutor

# 手动创建风险枚举类（避免导入问题）
from enum import Enum

class RiskAction(Enum):
    """风险行动枚举"""
    ALLOW_TRADING = "allow_trading"
    REDUCE_POSITION = "reduce_position"
    STOP_NEW_TRADES = "stop_new_trades"
    CLOSE_ALL_POSITIONS = "close_all_positions"
    EMERGENCY_STOP = "emergency_stop"

class RiskLevel(Enum):
    """风险等级枚举"""
    VERY_LOW = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    VERY_HIGH = 5

def test_realistic_position_adjustment():
    """测试符合MT4实际限制的仓位调整"""
    print("🚀 QuantumForex Pro 实际MT4仓位调整测试")
    print("="*60)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("💡 测试目标: 验证仓位调整符合MT4实际限制（最小0.01手）")
    print()

    try:
        # 1. 创建交易执行器
        print("🔧 初始化交易执行器...")
        executor = TradeExecutor()
        
        # 2. 测试实际的仓位调整场景
        test_cases = [
            {
                'name': '标准仓位 - 无风险调整',
                'original_volume': 0.02,
                'risk_action': RiskAction.ALLOW_TRADING,
                'confidence': 0.7,
                'expected_result': 0.02,
                'description': '正常交易，无调整'
            },
            {
                'name': '标准仓位 - 风险减少',
                'original_volume': 0.02,
                'risk_action': RiskAction.REDUCE_POSITION,
                'confidence': 0.7,
                'expected_result': 0.01,
                'description': '0.02手减少50%到0.01手'
            },
            {
                'name': '较大仓位 - 风险减少',
                'original_volume': 0.04,
                'risk_action': RiskAction.REDUCE_POSITION,
                'confidence': 0.7,
                'expected_result': 0.02,
                'description': '0.04手减少50%到0.02手'
            },
            {
                'name': '最小仓位 - 风险减少',
                'original_volume': 0.01,
                'risk_action': RiskAction.REDUCE_POSITION,
                'confidence': 0.7,
                'expected_result': 0.01,
                'description': '0.01手无法再减少，保持最小值'
            },
            {
                'name': '任意仓位 - 停止交易',
                'original_volume': 0.02,
                'risk_action': RiskAction.STOP_NEW_TRADES,
                'confidence': 0.7,
                'expected_result': 0.0,
                'description': '风险过高，停止交易'
            },
            {
                'name': '低置信度调整',
                'original_volume': 0.03,
                'risk_action': RiskAction.ALLOW_TRADING,
                'confidence': 0.4,  # 40%置信度
                'expected_result': 0.02,  # 0.03 * (0.4/0.6) = 0.02
                'description': '低置信度按比例调整'
            },
            {
                'name': '组合调整 - 实际可行',
                'original_volume': 0.04,
                'risk_action': RiskAction.REDUCE_POSITION,
                'confidence': 0.6,  # 刚好60%置信度
                'expected_result': 0.02,  # 0.04 * 0.5 = 0.02
                'description': '风险减少50%，置信度刚好不调整'
            }
        ]

        passed_tests = 0
        total_tests = len(test_cases)

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试案例 {i}: {test_case['name']}")
            print("-" * 50)
            
            # 创建模拟风险评估
            risk_metrics = type('RiskMetrics', (), {
                'recommended_action': test_case['risk_action'],
                'risk_level': RiskLevel.MEDIUM,
                'risk_score': 0.5
            })()
            
            risk_assessment = {
                'risk_metrics': risk_metrics,
                'overall_risk': 'MEDIUM'
            }
            
            # 设置风险评估
            executor.set_risk_assessment(risk_assessment)
            
            # 创建测试交易决策
            test_decision = {
                'symbol': 'EURUSD',
                'action': 'enter_long',
                'volume': test_case['original_volume'],
                'confidence': test_case['confidence'],
                'reasoning': f'测试{test_case["name"]}'
            }
            
            print(f"📊 原始仓位: {test_case['original_volume']}手")
            print(f"🛡️ 风险建议: {test_case['risk_action'].value}")
            print(f"💪 信号置信度: {test_case['confidence']:.1%}")
            print(f"📝 说明: {test_case['description']}")
            
            # 测试仓位计算
            calculated_volume = executor._calculate_position_size(test_decision)
            expected_volume = test_case['expected_result']
            
            print(f"🎯 计算仓位: {calculated_volume}手")
            print(f"🎯 期望仓位: {expected_volume}手")
            
            # 验证结果
            if abs(calculated_volume - expected_volume) < 0.001:
                print("✅ 仓位调整正确")
                passed_tests += 1
            else:
                print("❌ 仓位调整错误")
                print(f"   期望: {expected_volume}手")
                print(f"   实际: {calculated_volume}手")
                print(f"   差异: {abs(calculated_volume - expected_volume):.3f}手")

        # 3. 测试边界情况
        print(f"\n📋 边界情况测试")
        print("-" * 50)
        
        # 测试极小仓位的处理
        risk_metrics = type('RiskMetrics', (), {
            'recommended_action': RiskAction.REDUCE_POSITION,
            'risk_level': RiskLevel.MEDIUM,
            'risk_score': 0.6
        })()
        
        risk_assessment = {
            'risk_metrics': risk_metrics,
            'overall_risk': 'MEDIUM'
        }
        
        executor.set_risk_assessment(risk_assessment)
        
        edge_case_decision = {
            'symbol': 'EURUSD',
            'action': 'enter_long',
            'volume': 0.015,  # 0.015手减少50%会变成0.0075手
            'confidence': 0.8,
            'reasoning': '测试边界情况'
        }
        
        print(f"📊 原始仓位: {edge_case_decision['volume']}手")
        print(f"🛡️ 风险建议: reduce_position (50%调整)")
        print(f"💡 理论调整: {edge_case_decision['volume']} * 0.5 = {edge_case_decision['volume'] * 0.5}手")
        print(f"⚠️ 但MT4最小仓位是0.01手")
        
        calculated_volume = executor._calculate_position_size(edge_case_decision)
        
        print(f"🎯 实际计算仓位: {calculated_volume}手")
        
        if calculated_volume >= 0.01:
            print("✅ 正确处理边界情况，符合MT4限制")
            passed_tests += 1
            total_tests += 1
        else:
            print("❌ 边界情况处理错误")
            total_tests += 1

        print("\n" + "="*60)
        print("📊 测试总结")
        print("="*60)
        print(f"✅ 通过测试: {passed_tests}/{total_tests}")
        print(f"📈 成功率: {passed_tests/total_tests:.1%}")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！风险管理仓位调整功能完全正确")
            print("💡 系统现在能够:")
            print("   • 根据风险管理建议自动调整仓位")
            print("   • 确保调整后的仓位符合MT4实际限制")
            print("   • 正确处理边界情况（最小0.01手）")
            print("   • 支持多种调整因素的组合")
        else:
            print("⚠️ 部分测试失败，需要进一步优化")
        
        return passed_tests == total_tests

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_realistic_position_adjustment()
