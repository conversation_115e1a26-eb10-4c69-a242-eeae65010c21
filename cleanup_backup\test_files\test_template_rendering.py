"""
测试提示词模板渲染

这个脚本测试最终分析提示词模板的渲染，验证格式警告是否正确包含
"""
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append('.')

# 导入需要测试的模块
from app.utils import prompt_template_manager

def print_header(message):
    """打印带有分隔线的标题"""
    print("\n" + "=" * 80)
    print(f" {message} ".center(80, "="))
    print("=" * 80)

def test_final_analysis_template():
    """测试最终分析提示词模板"""
    print_header("测试最终分析提示词模板")
    
    # 准备模板数据
    template_data = {
        'symbol': 'EURUSD',
        'analysis_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'current_price': '1.1320',
        'initial_insights': '初始分析要点...',
        'detail_insights': '详细分析要点...',
        'news_data': '最新相关新闻...',
        'calendar_data': '经济日历事件...',
        'positions_data': '当前持仓...',
        'pending_orders_data': '当前挂单...',
        'history_analysis': '历史分析记录...',
        'trade_results': '历史交易结果...'
    }
    
    # 渲染模板
    try:
        rendered_template = prompt_template_manager.render_template('final_analysis_template', template_data)
        
        # 检查格式警告是否包含在渲染结果中
        format_warning_present = "你必须使用标准JSON格式提供交易指令" in rendered_template
        markdown_warning_present = "不要使用Markdown格式的\"执行指令\"部分" in rendered_template
        json_block_warning_present = "必须使用```json代码块，而不是```markdown代码块" in rendered_template
        
        # 打印结果
        print(f"格式警告存在: {format_warning_present}")
        print(f"Markdown警告存在: {markdown_warning_present}")
        print(f"JSON代码块警告存在: {json_block_warning_present}")
        
        # 验证结果
        assert format_warning_present, "格式警告应该存在于渲染结果中"
        assert markdown_warning_present, "Markdown警告应该存在于渲染结果中"
        assert json_block_warning_present, "JSON代码块警告应该存在于渲染结果中"
        
        # 打印渲染结果的前500个字符
        print("\n渲染结果预览:")
        print(rendered_template[:500] + "...")
        
        print("\n测试通过: 最终分析提示词模板包含正确的格式警告")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试开始')
    test_final_analysis_template()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试结束')
