# QuantumForex MLTrainer 依赖包

# 核心机器学习库
scikit-learn>=1.3.0
xgboost>=1.7.0
lightgbm>=3.3.0
catboost>=1.2.0

# 数据处理
pandas>=1.5.0
numpy>=1.24.0
scipy>=1.10.0

# 技术指标
TA-Lib>=0.4.25
ta>=0.10.0

# 数据库连接
SQLAlchemy>=2.0.0
PyMySQL>=1.0.0
pymongo>=4.0.0

# 模型序列化和版本管理
joblib>=1.3.0
pickle5>=0.0.12
dill>=0.3.6

# 可视化
matplotlib>=3.6.0
seaborn>=0.12.0
plotly>=5.15.0

# 性能优化
numba>=0.57.0
cython>=0.29.0

# 配置管理
python-dotenv>=1.0.0
pyyaml>=6.0

# 日志和监控
loguru>=0.7.0
tqdm>=4.65.0

# 网络通信
requests>=2.31.0
flask>=2.3.0
fastapi>=0.100.0
uvicorn>=0.23.0

# 时间处理
python-dateutil>=2.8.0
pytz>=2023.3

# 文件处理
openpyxl>=3.1.0
xlsxwriter>=3.1.0

# 统计分析
statsmodels>=0.14.0
arch>=5.5.0

# 深度学习（可选，如果需要）
# tensorflow>=2.13.0
# torch>=2.0.0
# keras>=2.13.0

# 特征选择和降维
feature-engine>=1.6.0
imbalanced-learn>=0.11.0

# 超参数优化
optuna>=3.3.0
hyperopt>=0.2.7

# 模型解释性
shap>=0.42.0
lime>=0.2.0

# 时间序列分析
prophet>=1.1.0
pmdarima>=2.0.0

# 并行处理
multiprocessing-logging>=0.3.4
concurrent-futures>=3.1.1

# 开发工具
pytest>=7.4.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

# 系统监控
psutil>=5.9.0
memory-profiler>=0.61.0

# 加密和安全
cryptography>=41.0.0
bcrypt>=4.0.0
