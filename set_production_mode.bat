@echo off
chcp 65001
echo ========================================
echo 设置外汇交易系统强制生产模式
echo ========================================

echo 正在设置环境变量...

REM 设置强制生产模式
set SKIP_MT4_CONNECTION=false

REM 设置其他生产相关环境变量
set FLASK_ENV=production
set LOG_LEVEL=INFO

echo ✅ 强制生产模式已启用
echo.
echo 📋 当前设置:
echo   - SKIP_MT4_CONNECTION=false (强制启用MT4服务器连接)
echo   - FLASK_ENV=production (生产环境)
echo   - LOG_LEVEL=INFO (信息级别日志)
echo.
echo ⚠️  注意: 这是强制生产模式，无论市场是否开放都会尝试连接MT4服务器
echo ⚠️  如需自动模式请运行 set_auto_mode.bat
echo ⚠️  如需测试模式请运行 set_test_mode.bat
echo.
echo 现在可以运行系统了:
echo   python run.py
echo.
echo ========================================
pause
