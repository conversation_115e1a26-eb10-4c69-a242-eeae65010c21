# QuantumForex Pro 依赖包
# 世界顶级量化交易系统所需的Python包
# 更新时间: 2025-05-28
#
# 安装指南:
# 1. 核心依赖 (必须): pip install pandas numpy scikit-learn psutil scipy requests pymysql
# 2. 完整安装: pip install -r requirements.txt
# 3. 开发环境: pip install -r requirements.txt --upgrade
#
# 已验证兼容的Python版本: 3.8+ (推荐 3.11+)

# 核心数据处理
pandas>=1.3.0
numpy>=1.21.0

# 技术分析
TA-Lib>=0.4.24
pandas-ta>=0.3.14b

# 机器学习 (已验证兼容版本)
# scikit-learn 核心依赖:
# - numpy>=1.19.5 (数值计算基础)
# - scipy>=1.6.0 (科学计算)
# - joblib>=1.2.0 (并行计算和模型持久化)
# - threadpoolctl>=3.1.0 (线程池控制)
scikit-learn>=1.6.0
joblib>=1.5.0
threadpoolctl>=3.6.0

# 数据库连接
PyMySQL>=1.0.2
redis>=4.0.0

# HTTP请求
requests>=2.25.0
urllib3>=1.26.0

# 环境变量管理
python-dotenv>=0.19.0

# 系统监控 (已验证兼容版本)
psutil>=7.0.0

# 数学计算
scipy>=1.7.0

# 时间处理
python-dateutil>=2.8.0

# JSON处理
ujson>=4.0.0

# 日志处理
colorlog>=6.0.0

# 配置文件处理
PyYAML>=6.0

# 网络通信
websocket-client>=1.2.0
pyzmq>=24.0.0  # ZeroMQ for MT4 communication (修正包名)

# 数据可视化 (可选)
matplotlib>=3.5.0
plotly>=5.0.0

# 统计分析
statsmodels>=0.13.0

# 加密和安全
cryptography>=3.4.0

# 内存优化
memory-profiler>=0.60.0

# 性能分析
line-profiler>=3.5.0

# 测试框架
pytest>=6.2.0
pytest-cov>=3.0.0

# 代码质量
flake8>=4.0.0
black>=22.0.0

# 文档生成
sphinx>=4.0.0

# 开发工具
ipython>=7.30.0
jupyter>=1.0.0

# 额外的机器学习依赖
xgboost>=1.6.0
lightgbm>=3.3.0

# 时间序列分析
arch>=5.3.0

# 并发处理
concurrent-futures>=3.1.1
