#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学习反馈系统
目标：从历史交易中学习，持续优化策略，提高胜率
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict
import json

@dataclass
class TradingPattern:
    """交易模式"""
    pattern_id: str
    conditions: Dict
    success_rate: float
    avg_profit: float
    sample_size: int
    confidence: float
    last_updated: datetime

@dataclass
class LearningInsight:
    """学习洞察"""
    insight_type: str
    description: str
    impact_score: float
    actionable_advice: str
    supporting_data: Dict

class TradingPatternAnalyzer:
    """交易模式分析器"""
    
    def __init__(self):
        self.min_sample_size = 5
        self.confidence_threshold = 0.7
        self.pattern_categories = [
            'market_regime',
            'time_of_day',
            'volatility_level',
            'trend_direction',
            'ma13_position',
            'rsi_level',
            'risk_reward_ratio'
        ]
    
    def analyze_trading_patterns(self, trading_history: List[Dict]) -> List[TradingPattern]:
        """分析交易模式"""
        if len(trading_history) < self.min_sample_size:
            return []
        
        patterns = []
        
        # 按不同维度分析模式
        for category in self.pattern_categories:
            category_patterns = self._analyze_category_patterns(trading_history, category)
            patterns.extend(category_patterns)
        
        # 组合模式分析
        combo_patterns = self._analyze_combination_patterns(trading_history)
        patterns.extend(combo_patterns)
        
        # 过滤和排序模式
        filtered_patterns = self._filter_significant_patterns(patterns)
        
        return sorted(filtered_patterns, key=lambda x: x.confidence * x.impact_score, reverse=True)
    
    def _analyze_category_patterns(self, trading_history: List[Dict], category: str) -> List[TradingPattern]:
        """分析单一类别的模式"""
        patterns = []
        
        # 按类别分组交易
        grouped_trades = defaultdict(list)
        
        for trade in trading_history:
            category_value = self._extract_category_value(trade, category)
            if category_value:
                grouped_trades[category_value].append(trade)
        
        # 分析每个分组的表现
        for value, trades in grouped_trades.items():
            if len(trades) >= self.min_sample_size:
                pattern = self._calculate_pattern_metrics(
                    f"{category}_{value}", {category: value}, trades
                )
                if pattern:
                    patterns.append(pattern)
        
        return patterns
    
    def _analyze_combination_patterns(self, trading_history: List[Dict]) -> List[TradingPattern]:
        """分析组合模式"""
        patterns = []
        
        # 定义重要的组合
        important_combinations = [
            ['market_regime', 'volatility_level'],
            ['trend_direction', 'ma13_position'],
            ['time_of_day', 'market_regime'],
            ['rsi_level', 'trend_direction']
        ]
        
        for combo in important_combinations:
            combo_patterns = self._analyze_combo_patterns(trading_history, combo)
            patterns.extend(combo_patterns)
        
        return patterns
    
    def _analyze_combo_patterns(self, trading_history: List[Dict], categories: List[str]) -> List[TradingPattern]:
        """分析特定组合的模式"""
        patterns = []
        
        # 按组合分组
        grouped_trades = defaultdict(list)
        
        for trade in trading_history:
            combo_key = []
            valid_combo = True
            
            for category in categories:
                value = self._extract_category_value(trade, category)
                if value:
                    combo_key.append(f"{category}:{value}")
                else:
                    valid_combo = False
                    break
            
            if valid_combo:
                key = "_".join(combo_key)
                grouped_trades[key].append(trade)
        
        # 分析组合表现
        for combo_key, trades in grouped_trades.items():
            if len(trades) >= self.min_sample_size:
                conditions = {}
                for part in combo_key.split("_"):
                    category, value = part.split(":")
                    conditions[category] = value
                
                pattern = self._calculate_pattern_metrics(
                    f"combo_{combo_key}", conditions, trades
                )
                if pattern:
                    patterns.append(pattern)
        
        return patterns
    
    def _extract_category_value(self, trade: Dict, category: str) -> Optional[str]:
        """提取类别值"""
        extractors = {
            'market_regime': lambda t: t.get('market_conditions', {}).get('regime'),
            'time_of_day': lambda t: self._get_time_category(t.get('entry_time')),
            'volatility_level': lambda t: t.get('market_conditions', {}).get('volatility'),
            'trend_direction': lambda t: t.get('analysis_context', {}).get('trend_direction'),
            'ma13_position': lambda t: t.get('analysis_context', {}).get('ma13_position'),
            'rsi_level': lambda t: self._get_rsi_category(t.get('analysis_context', {}).get('rsi')),
            'risk_reward_ratio': lambda t: self._get_rr_category(t.get('risk_reward_ratio'))
        }
        
        extractor = extractors.get(category)
        if extractor:
            return extractor(trade)
        
        return None
    
    def _get_time_category(self, entry_time: str) -> Optional[str]:
        """获取时间类别"""
        if not entry_time:
            return None
        
        try:
            dt = datetime.fromisoformat(entry_time.replace('Z', '+00:00'))
            hour = dt.hour
            
            if 0 <= hour < 6:
                return 'LATE_US'
            elif 6 <= hour < 8:
                return 'ASIAN'
            elif 8 <= hour < 12:
                return 'EARLY_EUROPEAN'
            elif 12 <= hour < 17:
                return 'EUROPEAN'
            elif 17 <= hour < 22:
                return 'US'
            else:
                return 'LATE_US'
        except:
            return None
    
    def _get_rsi_category(self, rsi: Optional[float]) -> Optional[str]:
        """获取RSI类别"""
        if rsi is None:
            return None
        
        if rsi >= 70:
            return 'OVERBOUGHT'
        elif rsi <= 30:
            return 'OVERSOLD'
        elif rsi >= 55:
            return 'BULLISH'
        elif rsi <= 45:
            return 'BEARISH'
        else:
            return 'NEUTRAL'
    
    def _get_rr_category(self, rr_ratio: Optional[float]) -> Optional[str]:
        """获取风险回报比类别"""
        if rr_ratio is None:
            return None
        
        if rr_ratio >= 2.5:
            return 'HIGH_RR'
        elif rr_ratio >= 1.5:
            return 'MEDIUM_RR'
        else:
            return 'LOW_RR'
    
    def _calculate_pattern_metrics(self, pattern_id: str, conditions: Dict, trades: List[Dict]) -> Optional[TradingPattern]:
        """计算模式指标"""
        if len(trades) < self.min_sample_size:
            return None
        
        # 计算成功率
        profitable_trades = [t for t in trades if t.get('profit_loss', 0) > 0]
        success_rate = len(profitable_trades) / len(trades)
        
        # 计算平均盈利
        profits = [t.get('profit_loss', 0) for t in trades]
        avg_profit = sum(profits) / len(profits)
        
        # 计算置信度（基于样本大小和一致性）
        confidence = min(len(trades) / 20, 1.0)  # 样本大小因子
        
        # 一致性因子
        profit_std = np.std(profits) if len(profits) > 1 else 0
        consistency_factor = 1 / (1 + profit_std * 10) if profit_std > 0 else 1
        confidence *= consistency_factor
        
        # 计算影响分数
        impact_score = self._calculate_impact_score(success_rate, avg_profit, len(trades))
        
        return TradingPattern(
            pattern_id=pattern_id,
            conditions=conditions,
            success_rate=success_rate,
            avg_profit=avg_profit,
            sample_size=len(trades),
            confidence=confidence,
            last_updated=datetime.now()
        )
    
    def _calculate_impact_score(self, success_rate: float, avg_profit: float, sample_size: int) -> float:
        """计算影响分数"""
        # 综合考虑成功率、平均盈利和样本大小
        base_score = (success_rate - 0.5) * 2  # 将50%作为基准
        profit_factor = max(min(avg_profit / 10, 1), -1)  # 标准化盈利
        sample_factor = min(sample_size / 20, 1)  # 样本大小因子
        
        impact_score = (base_score * 0.5 + profit_factor * 0.3 + sample_factor * 0.2)
        return max(min(impact_score, 1), -1)
    
    def _filter_significant_patterns(self, patterns: List[TradingPattern]) -> List[TradingPattern]:
        """过滤显著模式"""
        significant_patterns = []
        
        for pattern in patterns:
            # 过滤条件
            if (pattern.confidence >= self.confidence_threshold and
                pattern.sample_size >= self.min_sample_size and
                abs(pattern.impact_score) >= 0.2):
                significant_patterns.append(pattern)
        
        return significant_patterns

class StrategyOptimizer:
    """策略优化器"""
    
    def __init__(self):
        self.optimization_history = []
        self.current_parameters = {}
    
    def optimize_strategy_parameters(self, patterns: List[TradingPattern], 
                                   current_performance: Dict) -> Dict:
        """优化策略参数"""
        
        optimizations = {}
        
        # 1. 仓位管理优化
        position_optimizations = self._optimize_position_sizing(patterns)
        optimizations.update(position_optimizations)
        
        # 2. 止损止盈优化
        sl_tp_optimizations = self._optimize_stop_loss_take_profit(patterns)
        optimizations.update(sl_tp_optimizations)
        
        # 3. 入场条件优化
        entry_optimizations = self._optimize_entry_conditions(patterns)
        optimizations.update(entry_optimizations)
        
        # 4. 时间过滤优化
        time_optimizations = self._optimize_time_filters(patterns)
        optimizations.update(time_optimizations)
        
        return optimizations
    
    def _optimize_position_sizing(self, patterns: List[TradingPattern]) -> Dict:
        """优化仓位管理"""
        optimizations = {}
        
        # 分析不同市场状态下的最优仓位
        regime_patterns = [p for p in patterns if 'market_regime' in p.conditions]
        
        for pattern in regime_patterns:
            regime = pattern.conditions['market_regime']
            
            if pattern.success_rate > 0.6 and pattern.avg_profit > 0:
                # 成功率高的市场状态可以增加仓位
                optimizations[f'position_multiplier_{regime}'] = 1.2
            elif pattern.success_rate < 0.4 or pattern.avg_profit < 0:
                # 表现差的市场状态减少仓位
                optimizations[f'position_multiplier_{regime}'] = 0.7
        
        return optimizations
    
    def _optimize_stop_loss_take_profit(self, patterns: List[TradingPattern]) -> Dict:
        """优化止损止盈"""
        optimizations = {}
        
        # 分析风险回报比模式
        rr_patterns = [p for p in patterns if 'risk_reward_ratio' in p.conditions]
        
        best_rr = None
        best_performance = -float('inf')
        
        for pattern in rr_patterns:
            performance_score = pattern.success_rate * pattern.avg_profit
            if performance_score > best_performance:
                best_performance = performance_score
                best_rr = pattern.conditions['risk_reward_ratio']
        
        if best_rr:
            optimizations['optimal_risk_reward_ratio'] = best_rr
        
        return optimizations
    
    def _optimize_entry_conditions(self, patterns: List[TradingPattern]) -> Dict:
        """优化入场条件"""
        optimizations = {}
        
        # 分析MA13位置模式
        ma_patterns = [p for p in patterns if 'ma13_position' in p.conditions]
        
        best_positions = []
        for pattern in ma_patterns:
            if pattern.success_rate > 0.6:
                best_positions.append(pattern.conditions['ma13_position'])
        
        if best_positions:
            optimizations['preferred_ma13_positions'] = best_positions
        
        # 分析RSI条件
        rsi_patterns = [p for p in patterns if 'rsi_level' in p.conditions]
        
        avoid_conditions = []
        for pattern in rsi_patterns:
            if pattern.success_rate < 0.4:
                avoid_conditions.append(pattern.conditions['rsi_level'])
        
        if avoid_conditions:
            optimizations['avoid_rsi_conditions'] = avoid_conditions
        
        return optimizations
    
    def _optimize_time_filters(self, patterns: List[TradingPattern]) -> Dict:
        """优化时间过滤"""
        optimizations = {}
        
        time_patterns = [p for p in patterns if 'time_of_day' in p.conditions]
        
        best_times = []
        worst_times = []
        
        for pattern in time_patterns:
            if pattern.success_rate > 0.65:
                best_times.append(pattern.conditions['time_of_day'])
            elif pattern.success_rate < 0.35:
                worst_times.append(pattern.conditions['time_of_day'])
        
        if best_times:
            optimizations['preferred_trading_times'] = best_times
        
        if worst_times:
            optimizations['avoid_trading_times'] = worst_times
        
        return optimizations

class LearningInsightGenerator:
    """学习洞察生成器"""
    
    def generate_insights(self, patterns: List[TradingPattern], 
                         current_performance: Dict) -> List[LearningInsight]:
        """生成学习洞察"""
        
        insights = []
        
        # 1. 成功模式洞察
        success_insights = self._generate_success_insights(patterns)
        insights.extend(success_insights)
        
        # 2. 失败模式洞察
        failure_insights = self._generate_failure_insights(patterns)
        insights.extend(failure_insights)
        
        # 3. 改进机会洞察
        improvement_insights = self._generate_improvement_insights(patterns, current_performance)
        insights.extend(improvement_insights)
        
        # 4. 风险警告洞察
        risk_insights = self._generate_risk_insights(patterns)
        insights.extend(risk_insights)
        
        return sorted(insights, key=lambda x: x.impact_score, reverse=True)
    
    def _generate_success_insights(self, patterns: List[TradingPattern]) -> List[LearningInsight]:
        """生成成功模式洞察"""
        insights = []
        
        # 找到最成功的模式
        successful_patterns = [p for p in patterns if p.success_rate > 0.7 and p.avg_profit > 0]
        
        for pattern in successful_patterns[:3]:  # 取前3个最成功的模式
            insight = LearningInsight(
                insight_type='SUCCESS_PATTERN',
                description=f"在{self._format_conditions(pattern.conditions)}条件下，交易成功率达到{pattern.success_rate:.1%}",
                impact_score=pattern.confidence * pattern.success_rate,
                actionable_advice=f"应该积极寻找符合这些条件的交易机会，可以考虑增加仓位",
                supporting_data={
                    'success_rate': pattern.success_rate,
                    'avg_profit': pattern.avg_profit,
                    'sample_size': pattern.sample_size,
                    'conditions': pattern.conditions
                }
            )
            insights.append(insight)
        
        return insights
    
    def _generate_failure_insights(self, patterns: List[TradingPattern]) -> List[LearningInsight]:
        """生成失败模式洞察"""
        insights = []
        
        # 找到最失败的模式
        failed_patterns = [p for p in patterns if p.success_rate < 0.4 or p.avg_profit < -5]
        
        for pattern in failed_patterns[:3]:
            insight = LearningInsight(
                insight_type='FAILURE_PATTERN',
                description=f"在{self._format_conditions(pattern.conditions)}条件下，交易表现较差（成功率{pattern.success_rate:.1%}）",
                impact_score=pattern.confidence * (1 - pattern.success_rate),
                actionable_advice=f"应该避免在这些条件下交易，或者使用更小的仓位",
                supporting_data={
                    'success_rate': pattern.success_rate,
                    'avg_profit': pattern.avg_profit,
                    'sample_size': pattern.sample_size,
                    'conditions': pattern.conditions
                }
            )
            insights.append(insight)
        
        return insights
    
    def _generate_improvement_insights(self, patterns: List[TradingPattern], 
                                     current_performance: Dict) -> List[LearningInsight]:
        """生成改进机会洞察"""
        insights = []
        
        current_win_rate = current_performance.get('win_rate', 0.5)
        
        # 寻找可以提高胜率的模式
        high_potential_patterns = [
            p for p in patterns 
            if p.success_rate > current_win_rate + 0.1 and p.sample_size >= 5
        ]
        
        if high_potential_patterns:
            best_pattern = max(high_potential_patterns, key=lambda x: x.success_rate)
            
            insight = LearningInsight(
                insight_type='IMPROVEMENT_OPPORTUNITY',
                description=f"通过专注于{self._format_conditions(best_pattern.conditions)}的交易，胜率可能从{current_win_rate:.1%}提升到{best_pattern.success_rate:.1%}",
                impact_score=0.8,
                actionable_advice="建议调整交易策略，更多关注这类高胜率的交易机会",
                supporting_data={
                    'current_win_rate': current_win_rate,
                    'potential_win_rate': best_pattern.success_rate,
                    'improvement': best_pattern.success_rate - current_win_rate
                }
            )
            insights.append(insight)
        
        return insights
    
    def _generate_risk_insights(self, patterns: List[TradingPattern]) -> List[LearningInsight]:
        """生成风险警告洞察"""
        insights = []
        
        # 检查高风险模式
        high_risk_patterns = [
            p for p in patterns 
            if p.avg_profit < -10 and p.sample_size >= 3
        ]
        
        for pattern in high_risk_patterns:
            insight = LearningInsight(
                insight_type='RISK_WARNING',
                description=f"在{self._format_conditions(pattern.conditions)}条件下，平均亏损{abs(pattern.avg_profit):.1f}点",
                impact_score=0.7,
                actionable_advice="强烈建议避免在这些条件下交易，或者设置更严格的止损",
                supporting_data={
                    'avg_loss': pattern.avg_profit,
                    'sample_size': pattern.sample_size,
                    'conditions': pattern.conditions
                }
            )
            insights.append(insight)
        
        return insights
    
    def _format_conditions(self, conditions: Dict) -> str:
        """格式化条件描述"""
        formatted_parts = []
        
        for key, value in conditions.items():
            if key == 'market_regime':
                formatted_parts.append(f"市场状态为{value}")
            elif key == 'time_of_day':
                formatted_parts.append(f"交易时段为{value}")
            elif key == 'volatility_level':
                formatted_parts.append(f"波动率{value}")
            elif key == 'trend_direction':
                formatted_parts.append(f"趋势方向{value}")
            elif key == 'ma13_position':
                formatted_parts.append(f"价格相对13日均线{value}")
            elif key == 'rsi_level':
                formatted_parts.append(f"RSI处于{value}状态")
            else:
                formatted_parts.append(f"{key}为{value}")
        
        return "、".join(formatted_parts)

class AdaptiveLearningSystem:
    """自适应学习系统"""
    
    def __init__(self):
        self.pattern_analyzer = TradingPatternAnalyzer()
        self.strategy_optimizer = StrategyOptimizer()
        self.insight_generator = LearningInsightGenerator()
        self.learning_history = []
    
    def learn_and_adapt(self, trading_history: List[Dict], 
                       current_performance: Dict) -> Dict:
        """学习和适应"""
        
        # 1. 分析交易模式
        patterns = self.pattern_analyzer.analyze_trading_patterns(trading_history)
        
        # 2. 优化策略参数
        optimizations = self.strategy_optimizer.optimize_strategy_parameters(
            patterns, current_performance
        )
        
        # 3. 生成学习洞察
        insights = self.insight_generator.generate_insights(patterns, current_performance)
        
        # 4. 记录学习历史
        learning_record = {
            'timestamp': datetime.now().isoformat(),
            'patterns_found': len(patterns),
            'optimizations_made': len(optimizations),
            'insights_generated': len(insights),
            'performance_snapshot': current_performance
        }
        self.learning_history.append(learning_record)
        
        return {
            'patterns': [p.__dict__ for p in patterns],
            'optimizations': optimizations,
            'insights': [i.__dict__ for i in insights],
            'learning_summary': self._generate_learning_summary(patterns, insights),
            'recommended_actions': self._generate_recommended_actions(optimizations, insights)
        }
    
    def _generate_learning_summary(self, patterns: List[TradingPattern], 
                                 insights: List[LearningInsight]) -> str:
        """生成学习总结"""
        summary_parts = [
            f"发现{len(patterns)}个交易模式",
            f"生成{len(insights)}个学习洞察"
        ]
        
        if patterns:
            best_pattern = max(patterns, key=lambda x: x.success_rate)
            summary_parts.append(f"最佳模式成功率: {best_pattern.success_rate:.1%}")
        
        high_impact_insights = [i for i in insights if i.impact_score > 0.7]
        if high_impact_insights:
            summary_parts.append(f"发现{len(high_impact_insights)}个高影响洞察")
        
        return "; ".join(summary_parts)
    
    def _generate_recommended_actions(self, optimizations: Dict, 
                                    insights: List[LearningInsight]) -> List[str]:
        """生成推荐行动"""
        actions = []
        
        # 基于优化建议
        for key, value in optimizations.items():
            if 'position_multiplier' in key:
                regime = key.split('_')[-1]
                if value > 1:
                    actions.append(f"在{regime}市场状态下可以适当增加仓位")
                else:
                    actions.append(f"在{regime}市场状态下应该减少仓位")
        
        # 基于洞察建议
        for insight in insights[:3]:  # 取前3个最重要的洞察
            actions.append(insight.actionable_advice)
        
        return actions

# 使用示例
def run_learning_system(trading_history: List[Dict], current_performance: Dict) -> Dict:
    """运行学习系统"""
    learning_system = AdaptiveLearningSystem()
    
    results = learning_system.learn_and_adapt(trading_history, current_performance)
    
    return results
