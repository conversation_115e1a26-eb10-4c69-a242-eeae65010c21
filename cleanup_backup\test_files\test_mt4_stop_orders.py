"""
MT4止损单测试脚本
用于测试止损单
"""
import os
import sys
import time
from app.utils.mt4_client import mt4_client

def test_mt4_stop_orders():
    """测试MT4止损单"""
    try:
        print('开始测试止损单...')
        
        # 连接到MT4服务器
        print('连接到MT4服务器...')
        connected = mt4_client.connect()
        print(f'连接结果: {connected}')
        
        if not connected:
            print('连接MT4服务器失败，无法继续测试')
            return
        
        # 获取市场信息
        print('\n获取EURUSD市场信息...')
        market_info = mt4_client.get_market_info('EURUSD')
        print(f'市场信息: {market_info}')
        
        if not market_info or market_info.get('status') != 'success':
            print('获取市场信息失败，无法继续测试')
            return
        
        # 获取当前价格
        current_bid = float(market_info['data']['bid'])
        current_ask = float(market_info['data']['ask'])
        print(f'当前EURUSD价格: Bid={current_bid}, Ask={current_ask}')
        
        # 设置测试参数
        symbol = 'EURUSD'
        lot = 0.01  # 最小手数，仅用于测试
        
        # 测试买入止损单
        print('\n测试买入止损单...')
        try:
            # 设置止损单价格（高于当前价格）
            buy_stop_price = round(current_ask + 0.0050, 5)
            stop_loss = round(buy_stop_price - 0.0050, 5)
            take_profit = round(buy_stop_price + 0.0050, 5)
            
            print(f'买入止损单参数: 价格={buy_stop_price}, 止损={stop_loss}, 止盈={take_profit}')
            
            buy_stop_response = mt4_client.buy_stop(symbol, lot, buy_stop_price, stop_loss, take_profit, '测试买入止损单')
            print(f'买入止损单响应: {buy_stop_response}')
            
            if buy_stop_response and buy_stop_response.get('status') == 'success':
                print('✅ 买入止损单成功!')
            else:
                print(f'❌ 买入止损单失败: {buy_stop_response.get("message") if buy_stop_response else "未知错误"}')
        except Exception as error:
            print(f'❌ 买入止损单出错: {error}')
        
        # 测试卖出止损单
        print('\n测试卖出止损单...')
        try:
            # 设置止损单价格（低于当前价格）
            sell_stop_price = round(current_bid - 0.0050, 5)
            stop_loss = round(sell_stop_price + 0.0050, 5)
            take_profit = round(sell_stop_price - 0.0050, 5)
            
            print(f'卖出止损单参数: 价格={sell_stop_price}, 止损={stop_loss}, 止盈={take_profit}')
            
            sell_stop_response = mt4_client.sell_stop(symbol, lot, sell_stop_price, stop_loss, take_profit, '测试卖出止损单')
            print(f'卖出止损单响应: {sell_stop_response}')
            
            if sell_stop_response and sell_stop_response.get('status') == 'success':
                print('✅ 卖出止损单成功!')
            else:
                print(f'❌ 卖出止损单失败: {sell_stop_response.get("message") if sell_stop_response else "未知错误"}')
        except Exception as error:
            print(f'❌ 卖出止损单出错: {error}')
        
        # 获取活跃订单
        print('\n获取活跃订单...')
        active_orders = mt4_client.get_active_orders()
        print(f'活跃订单: {active_orders}')
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_mt4_stop_orders()
