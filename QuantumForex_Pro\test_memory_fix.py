#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试内存修复效果
"""

import sys
import os
import psutil
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_memory_configuration():
    """测试内存配置"""
    print("🔍 测试内存配置修复...")
    print("=" * 50)
    
    try:
        # 导入配置
        from config.config import config
        
        # 显示新的内存配置
        memory_config = config.SYSTEM_CONFIG
        print("📊 新的内存配置:")
        print(f"   最大内存使用率: {memory_config['max_memory_usage']:.0%}")
        print(f"   警告内存使用率: {memory_config.get('warning_memory_usage', 0.75):.0%}")
        print(f"   危险内存使用率: {memory_config.get('critical_memory_usage', 0.90):.0%}")
        
        # 获取当前内存状态
        memory = psutil.virtual_memory()
        current_usage = memory.percent / 100
        
        print(f"\n📈 当前系统状态:")
        print(f"   内存使用率: {current_usage:.1%}")
        print(f"   总内存: {memory.total/(1024**3):.1f}GB")
        print(f"   已用内存: {memory.used/(1024**3):.1f}GB")
        print(f"   可用内存: {memory.available/(1024**3):.1f}GB")
        
        # 判断内存状态
        if current_usage > memory_config.get('critical_memory_usage', 0.90):
            status = "🚨 危险"
            action = "系统将停止运行"
        elif current_usage > memory_config['max_memory_usage']:
            status = "⚠️ 过高"
            action = "执行清理但继续运行"
        elif current_usage > memory_config.get('warning_memory_usage', 0.75):
            status = "📊 警告"
            action = "正常监控"
        else:
            status = "✅ 正常"
            action = "无需操作"
        
        print(f"\n🎯 内存状态评估:")
        print(f"   状态: {status}")
        print(f"   动作: {action}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_system_health_logic():
    """测试系统健康检查逻辑"""
    print("\n🔍 测试系统健康检查逻辑...")
    print("=" * 50)
    
    try:
        # 模拟系统健康检查
        from config.config import config
        
        memory = psutil.virtual_memory()
        memory_usage = memory.percent / 100
        
        print(f"📊 当前内存使用: {memory_usage:.1%}")
        
        # 模拟健康检查逻辑
        if memory_usage > config.SYSTEM_CONFIG.get('critical_memory_usage', 0.90):
            result = False
            reason = f"内存使用危险: {memory_usage:.1%} - 系统将停止运行"
        elif memory_usage > config.SYSTEM_CONFIG['max_memory_usage']:
            result = True
            reason = f"内存使用过高: {memory_usage:.1%} - 执行清理但继续运行"
        elif memory_usage > config.SYSTEM_CONFIG.get('warning_memory_usage', 0.75):
            result = True
            reason = f"内存使用警告: {memory_usage:.1%}"
        else:
            result = True
            reason = "内存使用正常"
        
        print(f"🎯 健康检查结果: {'✅ 通过' if result else '❌ 失败'}")
        print(f"📝 原因: {reason}")
        
        if result:
            print("✅ 系统将继续运行")
        else:
            print("❌ 系统将停止运行")
        
        return result
        
    except Exception as e:
        print(f"❌ 健康检查测试失败: {e}")
        return False

def test_memory_optimizer():
    """测试内存优化器"""
    print("\n🔍 测试内存优化器...")
    print("=" * 50)
    
    try:
        from memory_optimizer import memory_optimizer, emergency_memory_cleanup
        
        # 获取优化前内存状态
        memory_before = psutil.virtual_memory()
        print(f"📊 优化前内存: {memory_before.percent:.1f}%")
        
        # 执行内存清理
        print("🧹 执行内存清理...")
        emergency_memory_cleanup()
        
        # 获取优化后内存状态
        memory_after = psutil.virtual_memory()
        print(f"📊 优化后内存: {memory_after.percent:.1f}%")
        
        # 计算效果
        improvement = memory_before.percent - memory_after.percent
        print(f"📈 内存释放: {improvement:.1f}%")
        
        if improvement > 0:
            print("✅ 内存优化有效")
        else:
            print("⚠️ 内存优化效果有限")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存优化器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 QuantumForex Pro 内存修复测试")
    print("=" * 60)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试结果
    results = []
    
    # 1. 测试内存配置
    results.append(test_memory_configuration())
    
    # 2. 测试系统健康检查逻辑
    results.append(test_system_health_logic())
    
    # 3. 测试内存优化器
    results.append(test_memory_optimizer())
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通过测试: {passed}/{total}")
    print(f"❌ 失败测试: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 所有测试通过！内存问题已修复！")
        print("💡 现在系统应该能在76%内存使用率下正常运行")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
    
    print("\n📋 修复说明:")
    print("   1. 内存限制从70%提高到85%")
    print("   2. 添加了分级内存管理策略")
    print("   3. 只有在90%以上才会停止系统")
    print("   4. 76%内存使用率现在会继续运行")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
