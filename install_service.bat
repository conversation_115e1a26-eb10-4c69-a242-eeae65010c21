@echo off
chcp 65001
echo ========================================
echo 外汇交易系统 - Windows服务安装脚本
echo ========================================

:: 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo ❌ 需要管理员权限运行此脚本
    echo 请右键选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过

:: 设置服务参数
set SERVICE_NAME=ForexTradingSystem
set SERVICE_DISPLAY_NAME=外汇交易系统
set SERVICE_DESCRIPTION=智能外汇交易分析系统
set CURRENT_DIR=%~dp0
set PYTHON_EXE=%CURRENT_DIR%venv\Scripts\python.exe
set SCRIPT_PATH=%CURRENT_DIR%run.py

:: 检查Python环境
if not exist "%PYTHON_EXE%" (
    echo ❌ 虚拟环境不存在，请先运行 start_server.bat
    pause
    exit /b 1
)

:: 停止现有服务（如果存在）
echo 🔄 检查现有服务...
sc query "%SERVICE_NAME%" >nul 2>&1
if not errorlevel 1 (
    echo 停止现有服务...
    sc stop "%SERVICE_NAME%"
    timeout /t 5 /nobreak >nul
    echo 删除现有服务...
    sc delete "%SERVICE_NAME%"
    timeout /t 3 /nobreak >nul
)

:: 创建服务
echo 📦 创建Windows服务...
sc create "%SERVICE_NAME%" ^
    binPath= "\"%PYTHON_EXE%\" \"%SCRIPT_PATH%\"" ^
    DisplayName= "%SERVICE_DISPLAY_NAME%" ^
    start= auto ^
    obj= "LocalSystem"

if errorlevel 1 (
    echo ❌ 服务创建失败
    pause
    exit /b 1
)

:: 设置服务描述
sc description "%SERVICE_NAME%" "%SERVICE_DESCRIPTION%"

:: 设置服务恢复选项
echo 🔧 配置服务恢复选项...
sc failure "%SERVICE_NAME%" reset= 86400 actions= restart/5000/restart/10000/restart/20000

:: 启动服务
echo 🚀 启动服务...
sc start "%SERVICE_NAME%"

if errorlevel 1 (
    echo ❌ 服务启动失败
    echo 请检查日志文件: logs\forex_system.log
    pause
    exit /b 1
)

echo ✅ 服务安装并启动成功！
echo 📊 服务名称: %SERVICE_NAME%
echo 🌐 访问地址: http://localhost:5000
echo 📝 查看服务状态: services.msc
echo 📄 日志文件: logs\forex_system.log

pause
