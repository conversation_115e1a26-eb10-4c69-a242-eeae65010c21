#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的Pro端API接口
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_new_api():
    """测试新的API接口"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🧪 开始测试新的Pro端API接口...")
    
    try:
        from data_collector.pro_data_collector import pro_data_collector
        
        # 测试收集所有数据
        result = pro_data_collector.collect_all_data(days=1)
        
        logger.info("✅ API测试完成！")
        logger.info("📊 收集结果:")
        logger.info(f"  - 交易记录: {len(result.get('trade_records', []))}条")
        logger.info(f"  - 参数优化: {len(result.get('parameter_optimizations', []))}条")
        logger.info(f"  - LLM分析: {len(result.get('llm_analyses', []))}条")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_new_api()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    input("按任意键退出...")
