#!/usr/bin/env python3
"""
测试完整模型加载 - 验证混合模型系统
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.getcwd(), 'QuantumForex_Pro'))

def test_complete_model_loading():
    """测试完整模型加载"""
    print("🔍 测试完整模型加载...")
    print("=" * 60)
    
    try:
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine, ModelType
        
        # 创建ML引擎实例
        print("🚀 初始化ML引擎...")
        ml_engine = LightweightMLEngine()
        
        print("\n📊 模型加载状态检查:")
        print("=" * 60)
        
        trainer_models = 0
        standard_models = 0
        total_models = 0
        
        for model_type in ModelType:
            model = ml_engine.models.get(model_type)
            performance = ml_engine.model_performance.get(model_type, 0.0)
            
            if model is not None:
                total_models += 1
                model_name = type(model).__name__
                
                # 判断是否是Trainer模型
                if 'LGBM' in model_name or 'LGB' in model_name or 'XGB' in model_name:
                    trainer_models += 1
                    model_status = "🤖 Trainer高级模型"
                    status_icon = "✅"
                elif hasattr(model, 'n_estimators') and model.n_estimators > 50:
                    # 可能是Trainer的RandomForest模型
                    trainer_models += 1
                    model_status = "🤖 Trainer高级模型"
                    status_icon = "✅"
                else:
                    standard_models += 1
                    model_status = "📊 标准轻量模型"
                    status_icon = "🔧"
                
                print(f"{status_icon} {model_type.value}:")
                print(f"   模型: {model_name}")
                print(f"   类型: {model_status}")
                print(f"   性能: {performance:.3f}")
                
                if hasattr(model, 'n_estimators'):
                    print(f"   估计器: {model.n_estimators}")
                elif hasattr(model, 'max_iter'):
                    print(f"   迭代: {model.max_iter}")
                
                print()
            else:
                print(f"❌ {model_type.value}: 未加载")
        
        print("=" * 60)
        print("📈 模型统计汇总:")
        print(f"   🤖 Trainer高级模型: {trainer_models}")
        print(f"   📊 标准轻量模型: {standard_models}")
        print(f"   📊 总模型数量: {total_models}/{len(ModelType)}")
        
        # 计算完整性
        completeness = total_models / len(ModelType)
        trainer_ratio = trainer_models / len(ModelType)
        
        print(f"   📊 系统完整性: {completeness:.1%}")
        print(f"   🤖 Trainer集成度: {trainer_ratio:.1%}")
        
        # 评估系统状态
        if completeness == 1.0:
            if trainer_ratio >= 0.5:
                status = "🟢 优秀 - 高度集成"
                description = "所有模型已加载，大部分使用Trainer高级模型"
            elif trainer_ratio > 0:
                status = "🟡 良好 - 部分集成"
                description = "所有模型已加载，部分使用Trainer高级模型"
            else:
                status = "🟠 基础 - 标准模型"
                description = "所有模型已加载，使用标准轻量模型"
        else:
            status = "🔴 不完整"
            description = f"缺少{len(ModelType) - total_models}个模型类型"
        
        print(f"\n🎯 系统状态: {status}")
        print(f"📝 描述: {description}")
        
        return completeness == 1.0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_prediction():
    """测试模型预测功能"""
    print("\n🔍 测试模型预测功能...")
    print("=" * 60)
    
    try:
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine
        import pandas as pd
        
        # 创建ML引擎
        ml_engine = LightweightMLEngine()
        
        # 创建测试数据（足够的数据点）
        test_data = pd.DataFrame({
            'close': [1.1000 + i*0.0001 for i in range(35)],
            'high': [1.1005 + i*0.0001 for i in range(35)],
            'low': [0.9995 + i*0.0001 for i in range(35)],
            'volume': [1000 + i*10 for i in range(35)]
        })
        
        test_indicators = {
            'trend_analysis': {
                'trend_score': 0.6,
                'adx_analysis': {
                    'adx': 25.0,
                    'plus_di': 20.0,
                    'minus_di': 15.0
                }
            },
            'momentum_analysis': {
                'momentum_score': 0.7,
                'rsi_analysis': {
                    'rsi_data': {
                        'rsi_14': 55.0
                    }
                }
            },
            'volatility_analysis': {
                'volatility_score': 0.4
            }
        }
        
        print("🧪 生成ML预测...")
        predictions = ml_engine.generate_predictions(test_data, test_indicators)
        
        if predictions:
            print(f"✅ 成功生成{len(predictions)}个预测")
            
            trainer_predictions = 0
            standard_predictions = 0
            
            for model_type, prediction in predictions.items():
                model = ml_engine.models.get(model_type)
                if model:
                    model_name = type(model).__name__
                    
                    if 'LGBM' in model_name or 'LGB' in model_name or 'XGB' in model_name:
                        trainer_predictions += 1
                        model_status = "🤖 Trainer"
                    else:
                        standard_predictions += 1
                        model_status = "📊 标准"
                    
                    print(f"\n📊 {model_type.value} ({model_status}):")
                    print(f"   模型: {model_name}")
                    print(f"   预测值: {prediction.prediction:.6f}")
                    print(f"   置信度: {prediction.confidence:.3f}")
                    print(f"   准确率: {prediction.model_accuracy:.3f}")
            
            print(f"\n🎯 预测统计:")
            print(f"   🤖 Trainer模型预测: {trainer_predictions}")
            print(f"   📊 标准模型预测: {standard_predictions}")
            print(f"   📊 总预测数量: {len(predictions)}")
            
            return len(predictions) == 4  # 应该有4个预测
        else:
            print("❌ 未能生成预测")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 完整模型加载测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行测试
    tests = [
        ("完整模型加载", test_complete_model_loading),
        ("模型预测功能", test_model_prediction)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"🔄 开始测试: {test_name}")
            print(f"{'='*60}")
            
            results[test_name] = test_func()
            
            if results[test_name]:
                print(f"✅ {test_name} - 测试通过")
            else:
                print(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("📊 完整模型加载测试结果汇总:")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 完整模型加载测试全部通过！")
        print("✅ 系统现在是Trainer+标准混合模型系统")
        print("✅ 所有模型类型都已正确加载")
        print("✅ 系统功能完整，可以正常运行")
        print("🚀 享受高精度ML预测和完整功能！")
    else:
        print("❌ 部分测试失败，系统可能不完整")
        print("💡 建议检查失败的测试项目")
    
    sys.exit(0 if all_passed else 1)
