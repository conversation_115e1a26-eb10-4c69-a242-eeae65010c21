@echo off
echo ========================================
echo Creating Forex Trading System Package
echo ========================================

REM Create package directory
if exist "ForexTradingSystem_v2.0.0_ServerReady" rmdir /s /q "ForexTradingSystem_v2.0.0_ServerReady"
mkdir "ForexTradingSystem_v2.0.0_ServerReady"

echo Copying files...

REM Copy app directory
xcopy "app" "ForexTradingSystem_v2.0.0_ServerReady\app\" /E /I /Y /Q

REM Copy main files
copy "run.py" "ForexTradingSystem_v2.0.0_ServerReady\" >nul
copy "requirements.txt" "ForexTradingSystem_v2.0.0_ServerReady\" >nul
copy "README.md" "ForexTradingSystem_v2.0.0_ServerReady\" >nul

REM Copy config files
if exist ".env.local" copy ".env.local" "ForexTradingSystem_v2.0.0_ServerReady\" >nul
if exist "config.py" copy "config.py" "ForexTradingSystem_v2.0.0_ServerReady\" >nul

REM Copy deployment scripts
copy "start_server.bat" "ForexTradingSystem_v2.0.0_ServerReady\" >nul
copy "deploy_server.bat" "ForexTradingSystem_v2.0.0_ServerReady\" >nul

REM Copy documentation
if exist "docs" xcopy "docs" "ForexTradingSystem_v2.0.0_ServerReady\docs\" /E /I /Y /Q >nul
copy "SERVER_DEPLOYMENT_GUIDE.md" "ForexTradingSystem_v2.0.0_ServerReady\" >nul

REM Copy templates
if exist "templates" xcopy "templates" "ForexTradingSystem_v2.0.0_ServerReady\templates\" /E /I /Y /Q >nul

REM Create directory structure
mkdir "ForexTradingSystem_v2.0.0_ServerReady\logs" >nul 2>&1
mkdir "ForexTradingSystem_v2.0.0_ServerReady\logs\error_logs" >nul 2>&1
mkdir "ForexTradingSystem_v2.0.0_ServerReady\app\data" >nul 2>&1
mkdir "ForexTradingSystem_v2.0.0_ServerReady\app\data\errors" >nul 2>&1
mkdir "ForexTradingSystem_v2.0.0_ServerReady\app\data\charts" >nul 2>&1
mkdir "ForexTradingSystem_v2.0.0_ServerReady\backups" >nul 2>&1

REM Copy update functionality
copy "remote_update_client.py" "ForexTradingSystem_v2.0.0_ServerReady\" >nul 2>&1
copy "update_system.bat" "ForexTradingSystem_v2.0.0_ServerReady\" >nul 2>&1
if exist "install_service.bat" copy "install_service.bat" "ForexTradingSystem_v2.0.0_ServerReady\" >nul

REM Create version info
echo { > "ForexTradingSystem_v2.0.0_ServerReady\version.json"
echo   "version": "2.0.0", >> "ForexTradingSystem_v2.0.0_ServerReady\version.json"
echo   "description": "Forex Trading System - Server Ready Version", >> "ForexTradingSystem_v2.0.0_ServerReady\version.json"
echo   "features": [ >> "ForexTradingSystem_v2.0.0_ServerReady\version.json"
echo     "Fixed encoding issues", >> "ForexTradingSystem_v2.0.0_ServerReady\version.json"
echo     "Optimized dependency management", >> "ForexTradingSystem_v2.0.0_ServerReady\version.json"
echo     "Auto deployment scripts", >> "ForexTradingSystem_v2.0.0_ServerReady\version.json"
echo     "Enhanced server compatibility", >> "ForexTradingSystem_v2.0.0_ServerReady\version.json"
echo     "Improved error handling" >> "ForexTradingSystem_v2.0.0_ServerReady\version.json"
echo   ] >> "ForexTradingSystem_v2.0.0_ServerReady\version.json"
echo } >> "ForexTradingSystem_v2.0.0_ServerReady\version.json"

REM Create deployment readme
echo # Forex Trading System v2.0.0 - Server Ready > "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo. >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo ## Quick Deployment >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo. >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo 1. Run deploy_server.bat >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo 2. Run start_server.bat >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo. >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo ## Version Features >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo. >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo - Fixed Windows Server 2012 encoding issues >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo - Optimized dependency management >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo - Enhanced error handling >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo - Added auto deployment scripts >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"
echo - Improved server compatibility >> "ForexTradingSystem_v2.0.0_ServerReady\DEPLOYMENT_README.txt"

REM Clean unnecessary files
echo Cleaning unnecessary files...
if exist "ForexTradingSystem_v2.0.0_ServerReady\app\__pycache__" rmdir /s /q "ForexTradingSystem_v2.0.0_ServerReady\app\__pycache__"
for /d %%i in ("ForexTradingSystem_v2.0.0_ServerReady\app\*") do (
    if exist "%%i\__pycache__" rmdir /s /q "%%i\__pycache__"
)

echo Creating ZIP package...
powershell -command "Compress-Archive -Path 'ForexTradingSystem_v2.0.0_ServerReady' -DestinationPath 'ForexTradingSystem_v2.0.0_ServerReady.zip' -Force"

if exist "ForexTradingSystem_v2.0.0_ServerReady.zip" (
    echo SUCCESS: Package created!
    echo Package file: ForexTradingSystem_v2.0.0_ServerReady.zip
    echo Package directory: ForexTradingSystem_v2.0.0_ServerReady\
    echo.
    echo Deployment steps:
    echo 1. Upload ForexTradingSystem_v2.0.0_ServerReady.zip to server
    echo 2. Extract to target directory
    echo 3. Run deploy_server.bat
    echo 4. Run start_server.bat
    echo.
    echo See SERVER_DEPLOYMENT_GUIDE.md for detailed instructions
) else (
    echo ERROR: Package creation failed
)

echo.
echo ========================================
echo Packaging completed!
echo ========================================
pause
