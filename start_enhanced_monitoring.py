#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版监听系统启动器
同时启动系统监听器和交易逻辑监听器
"""

import sys
import os
import logging
import time
import threading
from datetime import datetime

# 导入监听器
from real_time_system_monitor import RealTimeSystemMonitor
from trading_logic_monitor import TradingLogicMonitor

class EnhancedMonitoringSystem:
    """增强版监听系统"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 创建监听器实例
        self.system_monitor = RealTimeSystemMonitor()
        self.trading_monitor = TradingLogicMonitor()

        # 监听状态
        self.is_running = False
        self.status_thread = None

    def start_all_monitoring(self):
        """启动所有监听器"""
        try:
            self.logger.info("🚀 启动增强版监听系统")
            self.logger.info("=" * 80)

            self.is_running = True

            # 启动系统监听器
            self.logger.info("1️⃣ 启动系统监听器...")
            self.system_monitor.start_monitoring()

            # 启动交易逻辑监听器
            self.logger.info("2️⃣ 启动交易逻辑监听器...")
            self.trading_monitor.start_monitoring()

            # 启动状态汇总线程
            self._start_status_summary()

            self.logger.info("=" * 80)
            self.logger.info("✅ 增强版监听系统启动完成")
            self.logger.info("")
            self.logger.info("📊 监听组件:")
            self.logger.info("   🖥️  系统监听器 - Pro/Trainer状态、资源使用、数据库")
            self.logger.info("   📈 交易逻辑监听器 - 订单监听、重复检测、规则检查")
            self.logger.info("")
            self.logger.info("🔍 实时监听进行中...")
            self.logger.info("💡 按 Ctrl+C 停止所有监听")

        except Exception as e:
            self.logger.error(f"❌ 启动增强版监听系统失败: {e}")

    def stop_all_monitoring(self):
        """停止所有监听器"""
        try:
            self.logger.info("🛑 停止增强版监听系统")
            self.is_running = False

            # 停止状态汇总线程
            if self.status_thread and self.status_thread.is_alive():
                self.status_thread.join(timeout=5)

            # 停止交易逻辑监听器
            self.logger.info("1️⃣ 停止交易逻辑监听器...")
            self.trading_monitor.stop_monitoring()

            # 停止系统监听器
            self.logger.info("2️⃣ 停止系统监听器...")
            self.system_monitor.stop_monitoring()

            # 生成综合报告
            self._generate_comprehensive_report()

            self.logger.info("✅ 增强版监听系统已停止")

        except Exception as e:
            self.logger.error(f"❌ 停止监听系统失败: {e}")

    def _start_status_summary(self):
        """启动状态汇总线程"""
        def status_summary_loop():
            while self.is_running:
                try:
                    time.sleep(60)  # 每分钟汇总一次
                    if self.is_running:
                        self._log_comprehensive_status()
                except Exception as e:
                    self.logger.debug(f"状态汇总异常: {e}")

        self.status_thread = threading.Thread(target=status_summary_loop, daemon=True)
        self.status_thread.start()

    def _log_comprehensive_status(self):
        """记录综合状态"""
        try:
            # 获取各监听器状态
            system_status = self.system_monitor.get_current_status()
            trading_status = self.trading_monitor.get_current_status()

            # 汇总信息
            total_alerts = system_status['total_alerts'] + trading_status['total_alerts']
            current_orders = trading_status['current_orders_count']
            rule_violations = trading_status['rule_violations']

            self.logger.info("=" * 60)
            self.logger.info("📊 监听系统状态汇总")
            self.logger.info("=" * 60)
            self.logger.info(f"🖥️  系统监听: {'✅ 运行中' if system_status['is_monitoring'] else '❌ 停止'}")
            self.logger.info(f"📈 交易监听: {'✅ 运行中' if trading_status['is_monitoring'] else '❌ 停止'}")
            self.logger.info(f"📊 当前订单: {current_orders}个")
            self.logger.info(f"⚠️ 总警告数: {total_alerts}条")
            self.logger.info(f"🚨 规则违反: {rule_violations}次")

            # 显示最近的重要警告
            recent_trading_alerts = [a for a in trading_status['recent_alerts']
                                   if a['level'] in ['error', 'critical']]
            recent_system_alerts = [a for a in system_status['recent_alerts']
                                  if a['level'] in ['error', 'critical']]

            if recent_trading_alerts or recent_system_alerts:
                self.logger.info("🚨 最近重要警告:")
                for alert in recent_trading_alerts[-3:]:
                    self.logger.info(f"   📈 {alert['type']}: {alert['message']}")
                for alert in recent_system_alerts[-3:]:
                    self.logger.info(f"   🖥️  {alert['component']}: {alert['message']}")
            else:
                self.logger.info("✅ 无重要警告")

            self.logger.info("=" * 60)

        except Exception as e:
            self.logger.debug(f"记录综合状态失败: {e}")

    def _generate_comprehensive_report(self):
        """生成综合报告"""
        try:
            # 获取各监听器状态
            system_status = self.system_monitor.get_current_status()
            trading_status = self.trading_monitor.get_current_status()

            # 生成综合报告
            report = {
                'comprehensive_monitoring_report': {
                    'timestamp': datetime.now().isoformat(),
                    'system_monitoring': {
                        'is_monitoring': system_status['is_monitoring'],
                        'total_alerts': system_status['total_alerts'],
                        'recent_alerts': system_status['recent_alerts']
                    },
                    'trading_monitoring': {
                        'is_monitoring': trading_status['is_monitoring'],
                        'current_orders_count': trading_status['current_orders_count'],
                        'total_orders_detected': trading_status['total_orders_detected'],
                        'duplicate_orders_detected': trading_status['duplicate_orders_detected'],
                        'rule_violations': trading_status['rule_violations'],
                        'total_alerts': trading_status['total_alerts'],
                        'recent_alerts': trading_status['recent_alerts']
                    },
                    'summary': {
                        'total_alerts': system_status['total_alerts'] + trading_status['total_alerts'],
                        'critical_issues': len([a for a in system_status['recent_alerts'] + trading_status['recent_alerts']
                                              if a['level'] == 'critical']),
                        'monitoring_effectiveness': 'high' if trading_status['rule_violations'] > 0 else 'normal'
                    }
                }
            }

            # 保存综合报告
            import json
            report_file = f"comprehensive_monitoring_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            # 输出总结
            self.logger.info("=" * 80)
            self.logger.info("📊 增强版监听系统综合报告")
            self.logger.info("=" * 80)
            self.logger.info(f"📈 交易监听统计:")
            self.logger.info(f"   检测订单: {trading_status['total_orders_detected']}个")
            self.logger.info(f"   重复订单: {trading_status['duplicate_orders_detected']}个")
            self.logger.info(f"   规则违反: {trading_status['rule_violations']}次")
            self.logger.info(f"   交易警告: {trading_status['total_alerts']}条")
            self.logger.info("")
            self.logger.info(f"🖥️  系统监听统计:")
            self.logger.info(f"   系统警告: {system_status['total_alerts']}条")
            self.logger.info("")
            self.logger.info(f"📄 详细报告: {report_file}")
            self.logger.info("=" * 80)

        except Exception as e:
            self.logger.error(f"❌ 生成综合报告失败: {e}")

def setup_logging():
    """设置日志"""
    # 创建自定义格式器，避免emoji编码问题
    class SafeFormatter(logging.Formatter):
        def format(self, record):
            # 移除emoji字符，只保留文本
            msg = super().format(record)
            # 简单的emoji替换
            emoji_map = {
                '🚀': '[START]',
                '📋': '[INFO]',
                '🔍': '[SCAN]',
                '✅': '[OK]',
                '❌': '[ERROR]',
                '⚠️': '[WARN]',
                '🚨': '[ALERT]',
                '📊': '[DATA]',
                '🖥️': '[SYS]',
                '📈': '[TRADE]',
                '💡': '[TIP]',
                '🛑': '[STOP]',
                '🎯': '[TARGET]',
                '1️⃣': '[1]',
                '2️⃣': '[2]',
                '🔄': '[PROC]',
                '🆕': '[NEW]',
                '🔚': '[END]'
            }
            for emoji, text in emoji_map.items():
                msg = msg.replace(emoji, text)
            return msg

    # 设置日志配置
    formatter = SafeFormatter('%(asctime)s - %(levelname)s - %(message)s')

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # 文件处理器
    file_handler = logging.FileHandler(f'enhanced_monitoring_{datetime.now().strftime("%Y%m%d")}.log', encoding='utf-8')
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

    # 配置根日志器
    logging.basicConfig(
        level=logging.INFO,
        handlers=[console_handler, file_handler]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("🚀 增强版监听系统")
    logger.info("📋 同时监听系统状态和交易逻辑")
    logger.info("🔍 实时检测异常、违规、重复订单")
    logger.info("=" * 80)

    # 创建增强版监听系统
    enhanced_monitor = EnhancedMonitoringSystem()

    try:
        # 启动所有监听
        enhanced_monitor.start_all_monitoring()

        # 保持运行
        while enhanced_monitor.is_running:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("🛑 收到停止信号")
    except Exception as e:
        logger.error(f"❌ 监听系统运行异常: {e}")
    finally:
        # 停止所有监听
        enhanced_monitor.stop_all_monitoring()

if __name__ == "__main__":
    main()
