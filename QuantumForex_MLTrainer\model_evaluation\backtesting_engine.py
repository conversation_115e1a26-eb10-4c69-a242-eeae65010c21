#!/usr/bin/env python3
"""
QuantumForex MLTrainer 回测引擎
验证模型的实际交易效果
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

class TradeDirection(Enum):
    LONG = "LONG"
    SHORT = "SHORT"

class TradeStatus(Enum):
    OPEN = "OPEN"
    CLOSED = "CLOSED"

@dataclass
class BacktestTrade:
    """回测交易记录"""
    id: int
    symbol: str
    direction: TradeDirection
    entry_time: datetime
    entry_price: float
    size: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    profit_loss: Optional[float] = None
    status: TradeStatus = TradeStatus.OPEN
    reason: str = ""

@dataclass
class BacktestResult:
    """回测结果"""
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_profit: float
    total_loss: float
    net_profit: float
    profit_factor: float
    max_drawdown: float
    sharpe_ratio: float
    calmar_ratio: float
    max_consecutive_wins: int
    max_consecutive_losses: int
    average_win: float
    average_loss: float
    largest_win: float
    largest_loss: float
    trades: List[BacktestTrade]

class BacktestingEngine:
    """回测引擎"""
    
    def __init__(self, initial_capital: float = 10000.0):
        self.logger = logging.getLogger(__name__)
        
        # 回测参数
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.transaction_cost = 0.0001  # 0.01%
        self.slippage = 0.0001  # 0.01%
        
        # 交易记录
        self.trades: List[BacktestTrade] = []
        self.open_trades: List[BacktestTrade] = []
        self.closed_trades: List[BacktestTrade] = []
        
        # 资金曲线
        self.equity_curve: List[Tuple[datetime, float]] = []
        self.drawdown_curve: List[Tuple[datetime, float]] = []
        
        # 统计数据
        self.trade_counter = 0
        
    def run_backtest(self, 
                    data: pd.DataFrame, 
                    model_predictions: Dict[str, Any],
                    strategy_params: Dict[str, Any] = None) -> BacktestResult:
        """运行回测"""
        try:
            self.logger.info("🔄 开始回测...")
            
            # 重置状态
            self._reset_backtest()
            
            # 默认策略参数
            if strategy_params is None:
                strategy_params = {
                    'risk_per_trade': 0.02,  # 每笔交易风险2%
                    'stop_loss_pips': 20,    # 止损20点
                    'take_profit_pips': 40,  # 止盈40点
                    'min_confidence': 0.6    # 最小置信度
                }
            
            # 逐行处理数据
            for idx, row in data.iterrows():
                current_time = row['timestamp'] if 'timestamp' in row else datetime.now()
                current_price = row['close']
                
                # 更新开仓交易
                self._update_open_trades(current_time, current_price)
                
                # 检查新的交易信号
                if idx in model_predictions:
                    prediction = model_predictions[idx]
                    self._process_trading_signal(
                        current_time, current_price, row, prediction, strategy_params
                    )
                
                # 记录资金曲线
                self._update_equity_curve(current_time)
            
            # 关闭所有开仓交易
            self._close_all_trades(data.iloc[-1]['timestamp'], data.iloc[-1]['close'])
            
            # 计算回测结果
            result = self._calculate_backtest_result()
            
            self.logger.info(f"✅ 回测完成: {result.total_trades}笔交易")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 回测失败: {e}")
            raise
    
    def _reset_backtest(self):
        """重置回测状态"""
        self.current_capital = self.initial_capital
        self.trades.clear()
        self.open_trades.clear()
        self.closed_trades.clear()
        self.equity_curve.clear()
        self.drawdown_curve.clear()
        self.trade_counter = 0
    
    def _process_trading_signal(self, 
                               current_time: datetime,
                               current_price: float,
                               market_data: pd.Series,
                               prediction: Dict[str, Any],
                               strategy_params: Dict[str, Any]):
        """处理交易信号"""
        try:
            # 检查置信度
            confidence = prediction.get('confidence', 0.5)
            if confidence < strategy_params['min_confidence']:
                return
            
            # 确定交易方向
            signal = prediction.get('signal', 0)
            if signal > 0.5:
                direction = TradeDirection.LONG
            elif signal < -0.5:
                direction = TradeDirection.SHORT
            else:
                return  # 无明确信号
            
            # 计算仓位大小
            position_size = self._calculate_position_size(
                current_price, strategy_params['risk_per_trade'], 
                strategy_params['stop_loss_pips']
            )
            
            if position_size <= 0:
                return
            
            # 计算止损止盈
            if direction == TradeDirection.LONG:
                stop_loss = current_price - (strategy_params['stop_loss_pips'] * 0.0001)
                take_profit = current_price + (strategy_params['take_profit_pips'] * 0.0001)
            else:
                stop_loss = current_price + (strategy_params['stop_loss_pips'] * 0.0001)
                take_profit = current_price - (strategy_params['take_profit_pips'] * 0.0001)
            
            # 创建交易
            trade = BacktestTrade(
                id=self.trade_counter,
                symbol=market_data.get('symbol', 'UNKNOWN'),
                direction=direction,
                entry_time=current_time,
                entry_price=current_price,
                size=position_size,
                stop_loss=stop_loss,
                take_profit=take_profit,
                reason=f"Model signal: {signal:.3f}, confidence: {confidence:.3f}"
            )
            
            self.trade_counter += 1
            self.trades.append(trade)
            self.open_trades.append(trade)
            
            self.logger.debug(f"开仓: {direction.value} {position_size:.3f}手 @ {current_price:.5f}")
            
        except Exception as e:
            self.logger.error(f"处理交易信号失败: {e}")
    
    def _calculate_position_size(self, price: float, risk_percent: float, stop_loss_pips: int) -> float:
        """计算仓位大小"""
        try:
            # 风险金额
            risk_amount = self.current_capital * risk_percent
            
            # 每点价值（假设标准手）
            pip_value = 10.0  # USD/pip for standard lot
            
            # 止损金额
            stop_loss_amount = stop_loss_pips * pip_value
            
            # 仓位大小（手数）
            position_size = risk_amount / stop_loss_amount
            
            # 限制最小和最大仓位
            position_size = max(0.01, min(position_size, 1.0))
            
            return round(position_size, 2)
            
        except Exception as e:
            self.logger.error(f"计算仓位大小失败: {e}")
            return 0.01
    
    def _update_open_trades(self, current_time: datetime, current_price: float):
        """更新开仓交易"""
        trades_to_close = []
        
        for trade in self.open_trades:
            # 检查止损
            if self._should_close_at_stop_loss(trade, current_price):
                trade.exit_time = current_time
                trade.exit_price = current_price
                trade.reason = "Stop Loss"
                trades_to_close.append(trade)
                continue
            
            # 检查止盈
            if self._should_close_at_take_profit(trade, current_price):
                trade.exit_time = current_time
                trade.exit_price = current_price
                trade.reason = "Take Profit"
                trades_to_close.append(trade)
                continue
        
        # 关闭交易
        for trade in trades_to_close:
            self._close_trade(trade)
    
    def _should_close_at_stop_loss(self, trade: BacktestTrade, current_price: float) -> bool:
        """检查是否触发止损"""
        if trade.stop_loss is None:
            return False
        
        if trade.direction == TradeDirection.LONG:
            return current_price <= trade.stop_loss
        else:
            return current_price >= trade.stop_loss
    
    def _should_close_at_take_profit(self, trade: BacktestTrade, current_price: float) -> bool:
        """检查是否触发止盈"""
        if trade.take_profit is None:
            return False
        
        if trade.direction == TradeDirection.LONG:
            return current_price >= trade.take_profit
        else:
            return current_price <= trade.take_profit
    
    def _close_trade(self, trade: BacktestTrade):
        """关闭交易"""
        try:
            # 计算盈亏
            if trade.direction == TradeDirection.LONG:
                price_diff = trade.exit_price - trade.entry_price
            else:
                price_diff = trade.entry_price - trade.exit_price
            
            # 计算盈亏（考虑手数和点值）
            pip_value = 10.0  # USD/pip for standard lot
            pips = price_diff / 0.0001
            gross_profit = pips * pip_value * trade.size
            
            # 扣除交易成本
            transaction_cost = trade.size * 100000 * self.transaction_cost * 2  # 开仓+平仓
            net_profit = gross_profit - transaction_cost
            
            trade.profit_loss = net_profit
            trade.status = TradeStatus.CLOSED
            
            # 更新资金
            self.current_capital += net_profit
            
            # 移动到已关闭交易
            self.open_trades.remove(trade)
            self.closed_trades.append(trade)
            
            self.logger.debug(f"平仓: {trade.direction.value} 盈亏: ${net_profit:.2f}")
            
        except Exception as e:
            self.logger.error(f"关闭交易失败: {e}")
    
    def _close_all_trades(self, final_time: datetime, final_price: float):
        """关闭所有开仓交易"""
        for trade in self.open_trades.copy():
            trade.exit_time = final_time
            trade.exit_price = final_price
            trade.reason = "Backtest End"
            self._close_trade(trade)
    
    def _update_equity_curve(self, current_time: datetime):
        """更新资金曲线"""
        # 计算当前权益
        current_equity = self.current_capital
        
        # 加上未平仓盈亏
        for trade in self.open_trades:
            # 这里需要当前价格，简化处理
            pass
        
        self.equity_curve.append((current_time, current_equity))
        
        # 计算回撤
        if self.equity_curve:
            peak_equity = max(eq[1] for eq in self.equity_curve)
            drawdown = (peak_equity - current_equity) / peak_equity
            self.drawdown_curve.append((current_time, drawdown))
    
    def _calculate_backtest_result(self) -> BacktestResult:
        """计算回测结果"""
        try:
            if not self.closed_trades:
                return BacktestResult(
                    total_trades=0, winning_trades=0, losing_trades=0,
                    win_rate=0, total_profit=0, total_loss=0, net_profit=0,
                    profit_factor=0, max_drawdown=0, sharpe_ratio=0,
                    calmar_ratio=0, max_consecutive_wins=0, max_consecutive_losses=0,
                    average_win=0, average_loss=0, largest_win=0, largest_loss=0,
                    trades=[]
                )
            
            # 基础统计
            total_trades = len(self.closed_trades)
            winning_trades = len([t for t in self.closed_trades if t.profit_loss > 0])
            losing_trades = len([t for t in self.closed_trades if t.profit_loss < 0])
            
            # 盈亏统计
            profits = [t.profit_loss for t in self.closed_trades if t.profit_loss > 0]
            losses = [t.profit_loss for t in self.closed_trades if t.profit_loss < 0]
            
            total_profit = sum(profits) if profits else 0
            total_loss = abs(sum(losses)) if losses else 0
            net_profit = total_profit - total_loss
            
            # 计算各项指标
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')
            
            # 回撤
            max_drawdown = max(dd[1] for dd in self.drawdown_curve) if self.drawdown_curve else 0
            
            # 夏普比率（简化计算）
            returns = [t.profit_loss / self.initial_capital for t in self.closed_trades]
            if returns:
                avg_return = np.mean(returns)
                std_return = np.std(returns)
                sharpe_ratio = avg_return / std_return if std_return > 0 else 0
            else:
                sharpe_ratio = 0
            
            # 卡尔玛比率
            annual_return = net_profit / self.initial_capital
            calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0
            
            # 连续盈亏
            consecutive_wins, consecutive_losses = self._calculate_consecutive_trades()
            
            # 平均盈亏
            average_win = np.mean(profits) if profits else 0
            average_loss = np.mean(losses) if losses else 0
            largest_win = max(profits) if profits else 0
            largest_loss = min(losses) if losses else 0
            
            return BacktestResult(
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                total_profit=total_profit,
                total_loss=total_loss,
                net_profit=net_profit,
                profit_factor=profit_factor,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                calmar_ratio=calmar_ratio,
                max_consecutive_wins=consecutive_wins,
                max_consecutive_losses=consecutive_losses,
                average_win=average_win,
                average_loss=average_loss,
                largest_win=largest_win,
                largest_loss=largest_loss,
                trades=self.closed_trades.copy()
            )
            
        except Exception as e:
            self.logger.error(f"计算回测结果失败: {e}")
            raise
    
    def _calculate_consecutive_trades(self) -> Tuple[int, int]:
        """计算最大连续盈亏"""
        if not self.closed_trades:
            return 0, 0
        
        max_wins = 0
        max_losses = 0
        current_wins = 0
        current_losses = 0
        
        for trade in self.closed_trades:
            if trade.profit_loss > 0:
                current_wins += 1
                current_losses = 0
                max_wins = max(max_wins, current_wins)
            else:
                current_losses += 1
                current_wins = 0
                max_losses = max(max_losses, current_losses)
        
        return max_wins, max_losses
