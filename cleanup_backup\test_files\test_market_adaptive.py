#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试市场状态自适应系统
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_market_adaptive_system():
    """测试市场自适应系统"""
    print("🎯 市场状态自适应系统测试")
    print("=" * 60)
    
    try:
        # 1. 测试模块导入
        print("📦 测试模块导入...")
        from app.core.market_adaptive_system import (
            MarketAdaptiveSystem, MarketRegime, TradingStrategy, TimeframeWeight
        )
        print("   ✅ 市场自适应系统模块导入成功")
        
        # 2. 测试系统初始化
        print("\n🔧 测试系统初始化...")
        adaptive_system = MarketAdaptiveSystem()
        print("   ✅ 市场自适应系统初始化成功")
        
        # 3. 测试不同市场状态的分析
        print("\n📊 测试市场状态分析...")
        
        test_scenarios = [
            {
                'name': '强牛市场景',
                'market_data': {
                    'current_price': 1.1400,
                    'ma_20': 1.1350,
                    'ma_50': 1.1300,
                    'ma_200': 1.1200,
                    'rsi': 75,
                    'macd': 0.0008,
                    'macd_signal': 0.0005,
                    'bb_upper': 1.1450,
                    'bb_lower': 1.1300,
                    'atr': 0.0020,
                    'volume': 2000,
                    'avg_volume': 1000
                }
            },
            {
                'name': '震荡市场景',
                'market_data': {
                    'current_price': 1.1300,
                    'ma_20': 1.1295,
                    'ma_50': 1.1305,
                    'ma_200': 1.1290,
                    'rsi': 52,
                    'macd': 0.0001,
                    'macd_signal': 0.0001,
                    'bb_upper': 1.1320,
                    'bb_lower': 1.1280,
                    'atr': 0.0012,
                    'volume': 800,
                    'avg_volume': 1000
                }
            },
            {
                'name': '高波动市场',
                'market_data': {
                    'current_price': 1.1300,
                    'ma_20': 1.1280,
                    'ma_50': 1.1320,
                    'ma_200': 1.1290,
                    'rsi': 45,
                    'macd': -0.0002,
                    'macd_signal': 0.0001,
                    'bb_upper': 1.1380,
                    'bb_lower': 1.1220,
                    'atr': 0.0040,
                    'volume': 3000,
                    'avg_volume': 1000
                }
            },
            {
                'name': '平静市场',
                'market_data': {
                    'current_price': 1.1300,
                    'ma_20': 1.1298,
                    'ma_50': 1.1302,
                    'ma_200': 1.1299,
                    'rsi': 50,
                    'macd': 0.0000,
                    'macd_signal': 0.0000,
                    'bb_upper': 1.1310,
                    'bb_lower': 1.1290,
                    'atr': 0.0005,
                    'volume': 500,
                    'avg_volume': 1000
                }
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n📈 {scenario['name']}")
            
            # 分析市场状态
            market_condition = adaptive_system.analyze_market_condition(scenario['market_data'])
            
            print(f"   市场制度: {market_condition.regime.value}")
            print(f"   趋势强度: {market_condition.trend_strength:.2f}")
            print(f"   波动率水平: {market_condition.volatility_level:.2f}")
            print(f"   动量评分: {market_condition.momentum_score:.2f}")
            print(f"   支撑阻力强度: {market_condition.support_resistance_strength:.2f}")
            print(f"   成交量特征: {market_condition.volume_profile:.2f}")
            print(f"   市场效率: {market_condition.market_efficiency:.2f}")
            print(f"   噪音水平: {market_condition.noise_level:.2f}")
            print(f"   识别置信度: {market_condition.confidence:.2f}")
            
            # 策略自适应
            adaptive_params = adaptive_system.adapt_strategy(market_condition)
            
            print(f"   推荐策略: {adaptive_params.strategy.value}")
            print(f"   时间框架权重: {adaptive_params.timeframe_weight.value}")
            print(f"   仓位倍数: {adaptive_params.position_size_multiplier:.2f}")
            print(f"   止损倍数: {adaptive_params.stop_loss_multiplier:.2f}")
            print(f"   止盈倍数: {adaptive_params.take_profit_multiplier:.2f}")
            print(f"   入场门槛: {adaptive_params.entry_threshold:.2f}")
            print(f"   风险容忍度: {adaptive_params.risk_tolerance:.2f}")
            print(f"   信号敏感度: {adaptive_params.signal_sensitivity:.2f}")
            print(f"   持仓偏好: {adaptive_params.holding_period_preference}")
        
        # 4. 测试策略表现更新
        print("\n📈 测试策略表现更新...")
        
        # 模拟一些交易结果
        trade_results = [
            {'profit_loss': 0.015, 'strategy': TradingStrategy.TREND_FOLLOWING, 'regime': MarketRegime.BULL},
            {'profit_loss': -0.008, 'strategy': TradingStrategy.TREND_FOLLOWING, 'regime': MarketRegime.BULL},
            {'profit_loss': 0.012, 'strategy': TradingStrategy.RANGE_TRADING, 'regime': MarketRegime.SIDEWAYS},
            {'profit_loss': -0.005, 'strategy': TradingStrategy.RANGE_TRADING, 'regime': MarketRegime.SIDEWAYS},
            {'profit_loss': 0.020, 'strategy': TradingStrategy.MOMENTUM, 'regime': MarketRegime.STRONG_BULL}
        ]
        
        for result in trade_results:
            adaptive_system.update_strategy_performance(
                result['strategy'], result['regime'], {'profit_loss': result['profit_loss']}
            )
        
        print("   ✅ 策略表现更新完成")
        
        # 5. 测试最佳策略推荐
        print("\n🏆 测试最佳策略推荐...")
        
        best_bull_strategy = adaptive_system.get_best_strategy_for_regime(MarketRegime.BULL)
        best_sideways_strategy = adaptive_system.get_best_strategy_for_regime(MarketRegime.SIDEWAYS)
        
        print(f"   牛市最佳策略: {best_bull_strategy.value}")
        print(f"   震荡市最佳策略: {best_sideways_strategy.value}")
        
        # 6. 测试统计功能
        print("\n📊 测试统计功能...")
        
        # 市场统计
        market_stats = adaptive_system.get_market_statistics()
        print(f"   市场观察次数: {market_stats['total_observations']}")
        print(f"   制度分布: {market_stats['regime_distribution']}")
        print(f"   平均波动率: {market_stats['avg_volatility']:.3f}")
        print(f"   平均趋势强度: {market_stats['avg_trend_strength']:.3f}")
        print(f"   平均置信度: {market_stats['avg_confidence']:.3f}")
        
        # 自适应统计
        adaptation_stats = adaptive_system.get_adaptation_statistics()
        print(f"   自适应次数: {adaptation_stats['total_adaptations']}")
        print(f"   策略分布: {adaptation_stats['strategy_distribution']}")
        print(f"   平均仓位倍数: {adaptation_stats['avg_position_multiplier']:.3f}")
        print(f"   平均风险容忍度: {adaptation_stats['avg_risk_tolerance']:.3f}")
        
        # 策略表现总结
        performance_summary = adaptive_system.get_strategy_performance_summary()
        print(f"   策略表现记录: {len(performance_summary)}项")
        for key, perf in performance_summary.items():
            print(f"     {key}: 胜率{perf['win_rate']:.1%}, 平均收益{perf['avg_return']:.2%}")
        
        # 7. 测试策略适应判断
        print("\n🔄 测试策略适应判断...")
        
        # 模拟不同表现情况
        performance_scenarios = [
            {'name': '表现良好', 'performance': {'win_rate': 0.7, 'max_drawdown': 0.05}},
            {'name': '胜率过低', 'performance': {'win_rate': 0.25, 'max_drawdown': 0.08}},
            {'name': '回撤过大', 'performance': {'win_rate': 0.6, 'max_drawdown': 0.25}}
        ]
        
        for scenario in performance_scenarios:
            should_adapt, reason = adaptive_system.should_adapt_strategy(scenario['performance'])
            print(f"   {scenario['name']}: {'需要调整' if should_adapt else '保持当前'} - {reason}")
        
        # 8. 测试当前状态
        print("\n📋 测试当前状态...")
        current_status = adaptive_system.get_current_status()
        print(f"   系统就绪: {current_status['system_ready']}")
        if current_status['market_condition']:
            print(f"   当前市场制度: {current_status['market_condition']['regime']}")
            print(f"   当前置信度: {current_status['market_condition']['confidence']:.2f}")
        if current_status['adaptive_params']:
            print(f"   当前策略: {current_status['adaptive_params']['strategy']}")
            print(f"   当前仓位倍数: {current_status['adaptive_params']['position_multiplier']:.2f}")
        
        print("\n🎉 市场状态自适应系统测试完成！")
        print("   ✅ 所有核心功能正常工作")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adaptive_scenarios():
    """测试自适应场景"""
    print("\n🔄 自适应场景测试")
    print("=" * 40)
    
    try:
        from app.core.market_adaptive_system import MarketAdaptiveSystem, MarketRegime
        adaptive_system = MarketAdaptiveSystem()
        
        # 模拟市场状态变化场景
        scenarios = [
            {
                'name': '从震荡转向牛市',
                'initial_regime': MarketRegime.SIDEWAYS,
                'target_regime': MarketRegime.BULL,
                'expected_strategy_change': True
            },
            {
                'name': '从牛市转向高波动',
                'initial_regime': MarketRegime.BULL,
                'target_regime': MarketRegime.VOLATILE,
                'expected_strategy_change': True
            },
            {
                'name': '牛市内部调整',
                'initial_regime': MarketRegime.BULL,
                'target_regime': MarketRegime.WEAK_BULL,
                'expected_strategy_change': False
            }
        ]
        
        print("市场状态变化适应测试:")
        for scenario in scenarios:
            print(f"\n📊 {scenario['name']}")
            
            # 获取初始策略
            initial_params = adaptive_system.strategy_params.get(scenario['initial_regime'], {})
            target_params = adaptive_system.strategy_params.get(scenario['target_regime'], {})
            
            initial_strategy = initial_params.get('preferred_strategy', 'UNKNOWN')
            target_strategy = target_params.get('preferred_strategy', 'UNKNOWN')
            
            print(f"   初始策略: {initial_strategy.value if hasattr(initial_strategy, 'value') else initial_strategy}")
            print(f"   目标策略: {target_strategy.value if hasattr(target_strategy, 'value') else target_strategy}")
            
            strategy_changed = initial_strategy != target_strategy
            print(f"   策略变化: {'是' if strategy_changed else '否'}")
            print(f"   预期变化: {'是' if scenario['expected_strategy_change'] else '否'}")
            
            if strategy_changed == scenario['expected_strategy_change']:
                print(f"   ✅ 适应行为符合预期")
            else:
                print(f"   ⚠️ 适应行为与预期不符")
        
        print("\n✅ 自适应场景测试完成")
        
    except Exception as e:
        print(f"❌ 自适应场景测试失败: {e}")

def show_adaptive_system_summary():
    """显示自适应系统总结"""
    print("\n📋 市场状态自适应系统总结")
    print("=" * 50)
    
    print("🎯 第三阶段完成：市场状态自适应机制")
    print("   ✅ 创建了市场状态自适应系统")
    print("   ✅ 实现了11种市场制度识别")
    print("   ✅ 集成了8种交易策略自适应")
    print("   ✅ 添加了5种时间框架权重调整")
    print("   ✅ 实现了策略表现跟踪和优化")
    print("   ✅ 集成了智能参数调整机制")
    
    print("\n🔄 系统改进效果：")
    print("   - 市场适应：从固定策略 → 动态自适应策略")
    print("   - 参数调整：从静态参数 → 基于市场状态动态调整")
    print("   - 策略选择：从单一策略 → 多策略智能切换")
    print("   - 表现优化：从经验调整 → 数据驱动优化")
    
    print("\n📈 预期收益提升：")
    print("   - 市场适应性：不同市场状态下的最优策略选择")
    print("   - 参数优化：基于市场状态的动态参数调整")
    print("   - 风险控制：根据市场波动率调整风险参数")
    print("   - 长期稳定：通过策略切换适应市场变化")
    
    print("\n🔧 技术实现亮点：")
    print("   - 多维度市场分析：趋势+波动+动量+效率+噪音")
    print("   - 智能制度识别：11种市场制度精确分类")
    print("   - 策略自适应：8种策略根据市场状态自动切换")
    print("   - 表现反馈：策略表现实时跟踪和优化")
    
    print("\n🚀 下一步优化方向：")
    print("   1. 交易结果反馈学习")
    print("   2. 多货币对组合管理")
    print("   3. 高级策略优化")
    print("   4. 机器学习模型集成")

if __name__ == "__main__":
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始市场状态自适应系统测试")
    
    # 执行自适应系统测试
    success = test_market_adaptive_system()
    
    if success:
        # 执行自适应场景测试
        test_adaptive_scenarios()
        
        # 显示系统总结
        show_adaptive_system_summary()
        
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🎉 第三阶段优化完成！")
        print("市场状态自适应系统已成功创建，系统智能化水平得到显著提升。")
    else:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ 测试失败，请检查系统配置。")
