"""
简单的ZeroMQ服务器
"""
import os
import sys
import time
import json
import zmq
from datetime import datetime

# 打印带时间戳的消息
def log(message):
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {message}")
    sys.stdout.flush()

# 服务器地址
SERVER_ADDRESS = "tcp://*:5559"

log("启动ZeroMQ服务器")
log(f"服务器地址: {SERVER_ADDRESS}")

try:
    # 创建ZeroMQ上下文和套接字
    log("创建ZeroMQ上下文")
    context = zmq.Context()

    log("创建REP套接字")
    socket = context.socket(zmq.REP)

    # 绑定地址
    log(f"绑定地址: {SERVER_ADDRESS}")
    socket.bind(SERVER_ADDRESS)

    log("服务器已启动，等待请求...")

    # 设置超时时间，以便能够响应Ctrl+C
    socket.setsockopt(zmq.RCVTIMEO, 1000)  # 1秒超时

    # 运行服务器
    running = True
    while running:
        try:
            # 接收请求
            message = socket.recv_string()
            log(f"收到请求: {message}")

            # 解析请求
            request = json.loads(message)

            # 处理请求
            action = request.get('action', '')

            if action == 'ping':
                response = {
                    'status': 'success',
                    'message': 'pong',
                    'server_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            else:
                response = {
                    'status': 'error',
                    'message': f'未知的操作: {action}'
                }

            # 发送响应
            response_str = json.dumps(response)
            log(f"发送响应: {response_str}")
            socket.send_string(response_str)
        except zmq.error.Again:
            # 超时，继续循环
            continue
        except KeyboardInterrupt:
            # 用户按下Ctrl+C
            log("收到停止信号，正在停止服务器...")
            running = False
        except Exception as e:
            log(f"处理请求时出错: {e}")

            # 尝试发送错误响应
            try:
                socket.send_string(json.dumps({
                    'status': 'error',
                    'message': f'服务器内部错误: {str(e)}'
                }))
            except:
                pass

    # 关闭套接字和上下文
    log("关闭套接字")
    socket.close()

    log("终止上下文")
    context.term()
except Exception as e:
    log(f"服务器启动失败: {e}")

log("服务器已停止")
