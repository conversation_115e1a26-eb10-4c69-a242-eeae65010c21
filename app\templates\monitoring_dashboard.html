<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外汇交易系统 - 实时监控面板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 300;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 20px;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-healthy { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-critical { background-color: #e74c3c; }
        .status-unknown { background-color: #95a5a6; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .metric-item {
            text-align: center;
            padding: 15px;
            background: rgba(52, 152, 219, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(52, 152, 219, 0.1);
        }

        .metric-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9em;
            color: #7f8c8d;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 15px;
        }

        .alerts-container {
            max-height: 300px;
            overflow-y: auto;
        }

        .alert-item {
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 8px;
            border-left: 4px solid;
            font-size: 0.9em;
        }

        .alert-critical {
            background: rgba(231, 76, 60, 0.1);
            border-left-color: #e74c3c;
        }

        .alert-warning {
            background: rgba(243, 156, 18, 0.1);
            border-left-color: #f39c12;
        }

        .alert-error {
            background: rgba(231, 76, 60, 0.1);
            border-left-color: #e74c3c;
        }

        .alert-info {
            background: rgba(52, 152, 219, 0.1);
            border-left-color: #3498db;
        }

        .alert-time {
            font-size: 0.8em;
            color: #7f8c8d;
            margin-bottom: 3px;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .error {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }

        .last-update {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .status-bar {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 外汇交易系统监控面板</h1>
            <div class="status-bar">
                <div class="status-item">
                    <div class="status-indicator status-unknown" id="systemStatus"></div>
                    <span>系统状态</span>
                </div>
                <div class="status-item">
                    <div class="status-indicator status-unknown" id="monitoringStatus"></div>
                    <span>监控状态</span>
                </div>
                <div class="status-item">
                    <span id="currentTime">--:--:--</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-success" onclick="startMonitoring()">启动监控</button>
            <button class="btn btn-danger" onclick="stopMonitoring()">停止监控</button>
            <button class="btn btn-primary" onclick="refreshData()">刷新数据</button>
            <button class="btn btn-primary" onclick="testAlert()">测试告警</button>
        </div>

        <div class="dashboard-grid">
            <!-- 系统指标卡片 -->
            <div class="card">
                <h3>💻 系统资源</h3>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="cpuUsage">--</div>
                        <div class="metric-label">CPU使用率</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="memoryUsage">--</div>
                        <div class="metric-label">内存使用率</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="diskUsage">--</div>
                        <div class="metric-label">磁盘使用率</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="systemChart"></canvas>
                </div>
            </div>

            <!-- 交易指标卡片 -->
            <div class="card">
                <h3>📈 交易表现</h3>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="winRate">--</div>
                        <div class="metric-label">胜率</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="netProfit">--</div>
                        <div class="metric-label">净利润</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="maxDrawdown">--</div>
                        <div class="metric-label">最大回撤</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="tradingChart"></canvas>
                </div>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- 分析指标卡片 -->
            <div class="card">
                <h3>🤖 LLM分析</h3>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="analysisCount">--</div>
                        <div class="metric-label">分析次数</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="successRate">--</div>
                        <div class="metric-label">成功率</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="totalCost">--</div>
                        <div class="metric-label">总成本</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="analysisChart"></canvas>
                </div>
            </div>

            <!-- 告警信息卡片 -->
            <div class="card">
                <h3>🚨 实时告警</h3>
                <div class="alerts-container" id="alertsContainer">
                    <div class="loading">正在加载告警信息...</div>
                </div>
            </div>
        </div>

        <div class="last-update" id="lastUpdate">
            最后更新: --
        </div>
    </div>

    <script>
        // 全局变量
        let systemChart, tradingChart, analysisChart;
        let refreshInterval;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            refreshData();

            // 自动刷新数据
            refreshInterval = setInterval(refreshData, 30000); // 30秒刷新一次
        });

        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleTimeString('zh-CN');
        }

        // 初始化图表
        function initCharts() {
            const chartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            };

            // 系统资源图表
            const systemCtx = document.getElementById('systemChart').getContext('2d');
            systemChart = new Chart(systemCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU使用率 (%)',
                        data: [],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.4
                    }, {
                        label: '内存使用率 (%)',
                        data: [],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4
                    }]
                },
                options: chartOptions
            });

            // 交易表现图表
            const tradingCtx = document.getElementById('tradingChart').getContext('2d');
            tradingChart = new Chart(tradingCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '净利润',
                        data: [],
                        borderColor: '#27ae60',
                        backgroundColor: 'rgba(39, 174, 96, 0.1)',
                        tension: 0.4
                    }, {
                        label: '胜率 (%)',
                        data: [],
                        borderColor: '#f39c12',
                        backgroundColor: 'rgba(243, 156, 18, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    ...chartOptions,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });

            // LLM分析图表
            const analysisCtx = document.getElementById('analysisChart').getContext('2d');
            analysisChart = new Chart(analysisCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '成功分析',
                        data: [],
                        backgroundColor: 'rgba(39, 174, 96, 0.8)',
                        borderColor: '#27ae60',
                        borderWidth: 1
                    }, {
                        label: '失败分析',
                        data: [],
                        backgroundColor: 'rgba(231, 76, 60, 0.8)',
                        borderColor: '#e74c3c',
                        borderWidth: 1
                    }]
                },
                options: chartOptions
            });
        }

        // 刷新数据
        async function refreshData() {
            try {
                const response = await fetch('/monitoring/api/dashboard');
                const result = await response.json();

                if (result.success) {
                    updateDashboard(result.data);
                    document.getElementById('lastUpdate').textContent =
                        `最后更新: ${new Date().toLocaleString('zh-CN')}`;
                } else {
                    showError('获取数据失败: ' + result.error);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        // 更新仪表板
        function updateDashboard(data) {
            // 更新状态指示器
            updateStatusIndicators(data.current_status);

            // 更新系统指标
            updateSystemMetrics(data.current_status.system);

            // 更新交易指标
            updateTradingMetrics(data.current_status.trading);

            // 更新分析指标
            updateAnalysisMetrics(data.current_status.analysis);

            // 更新图表
            updateCharts(data);

            // 更新告警
            updateAlerts(data.recent_alerts);
        }

        // 更新状态指示器
        function updateStatusIndicators(status) {
            const systemStatus = document.getElementById('systemStatus');
            const monitoringStatus = document.getElementById('monitoringStatus');

            // 系统状态
            if (status.system) {
                systemStatus.className = `status-indicator status-${status.system.system_status}`;
            }

            // 监控状态
            const isActive = status.monitoring_status === 'active';
            monitoringStatus.className = `status-indicator ${isActive ? 'status-healthy' : 'status-critical'}`;
        }

        // 更新系统指标
        function updateSystemMetrics(system) {
            if (!system) return;

            document.getElementById('cpuUsage').textContent = `${system.cpu_percent.toFixed(1)}%`;
            document.getElementById('memoryUsage').textContent = `${system.memory_percent.toFixed(1)}%`;
            document.getElementById('diskUsage').textContent = `${system.disk_percent.toFixed(1)}%`;
        }

        // 更新交易指标
        function updateTradingMetrics(trading) {
            if (!trading) return;

            document.getElementById('winRate').textContent = `${trading.win_rate.toFixed(1)}%`;
            document.getElementById('netProfit').textContent = `$${trading.net_profit.toFixed(2)}`;
            document.getElementById('maxDrawdown').textContent = `${trading.max_drawdown.toFixed(2)}%`;
        }

        // 更新分析指标
        function updateAnalysisMetrics(analysis) {
            if (!analysis) return;

            document.getElementById('analysisCount').textContent = analysis.total_analyses;
            document.getElementById('successRate').textContent = `${analysis.success_rate.toFixed(1)}%`;
            document.getElementById('totalCost').textContent = `¥${analysis.total_cost.toFixed(2)}`;
        }

        // 更新图表
        function updateCharts(data) {
            // 更新系统资源图表
            if (data.system_metrics_24h && data.system_metrics_24h.length > 0) {
                const systemData = data.system_metrics_24h.slice(-20); // 最近20个数据点
                const labels = systemData.map(d => new Date(d.timestamp).toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}));
                const cpuData = systemData.map(d => d.cpu_percent);
                const memoryData = systemData.map(d => d.memory_percent);

                systemChart.data.labels = labels;
                systemChart.data.datasets[0].data = cpuData;
                systemChart.data.datasets[1].data = memoryData;
                systemChart.update('none');
            }

            // 更新交易表现图表
            if (data.trading_metrics_24h && data.trading_metrics_24h.length > 0) {
                const tradingData = data.trading_metrics_24h.slice(-10); // 最近10个数据点
                const labels = tradingData.map(d => new Date(d.timestamp).toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}));
                const profitData = tradingData.map(d => d.net_profit);
                const winRateData = tradingData.map(d => d.win_rate);

                tradingChart.data.labels = labels;
                tradingChart.data.datasets[0].data = profitData;
                tradingChart.data.datasets[1].data = winRateData;
                tradingChart.update('none');
            }

            // 更新分析图表
            if (data.analysis_metrics_24h && data.analysis_metrics_24h.length > 0) {
                const analysisData = data.analysis_metrics_24h.slice(-10); // 最近10个数据点
                const labels = analysisData.map(d => new Date(d.timestamp).toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}));
                const successData = analysisData.map(d => d.successful_analyses);
                const failedData = analysisData.map(d => d.failed_analyses);

                analysisChart.data.labels = labels;
                analysisChart.data.datasets[0].data = successData;
                analysisChart.data.datasets[1].data = failedData;
                analysisChart.update('none');
            }
        }

        // 更新告警
        function updateAlerts(alerts) {
            const container = document.getElementById('alertsContainer');

            if (!alerts || alerts.length === 0) {
                container.innerHTML = '<div class="loading">暂无告警信息</div>';
                return;
            }

            const alertsHtml = alerts.map(alert => {
                const time = new Date(alert.timestamp).toLocaleString('zh-CN');
                return `
                    <div class="alert-item alert-${alert.level}">
                        <div class="alert-time">${time}</div>
                        <div><strong>[${alert.category.toUpperCase()}]</strong> ${alert.message}</div>
                        ${alert.details ? `<div style="margin-top: 5px; font-size: 0.8em;">${alert.details}</div>` : ''}
                    </div>
                `;
            }).join('');

            container.innerHTML = alertsHtml;
        }

        // 显示错误
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;

            const container = document.querySelector('.container');
            container.insertBefore(errorDiv, container.firstChild);

            // 5秒后自动移除错误信息
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        }

        // 控制函数
        async function startMonitoring() {
            try {
                const response = await fetch('/monitoring/api/control/start');
                const result = await response.json();

                if (result.success) {
                    alert('监控已启动');
                    refreshData();
                } else {
                    alert('启动监控失败: ' + result.error);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        async function stopMonitoring() {
            try {
                const response = await fetch('/monitoring/api/control/stop');
                const result = await response.json();

                if (result.success) {
                    alert('监控已停止');
                    refreshData();
                } else {
                    alert('停止监控失败: ' + result.error);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        async function testAlert() {
            try {
                const response = await fetch('/monitoring/api/test/alert?level=info&message=测试告警消息');
                const result = await response.json();

                if (result.success) {
                    alert('测试告警已发送');
                    setTimeout(refreshData, 1000); // 1秒后刷新数据
                } else {
                    alert('发送测试告警失败: ' + result.error);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
