"""
MT4关闭订单测试脚本
用于测试关闭活跃订单
"""
import os
import sys
import time
from app.utils.mt4_client import mt4_client

def test_mt4_close_order():
    """测试关闭MT4订单"""
    try:
        print('开始测试关闭订单...')
        
        # 连接到MT4服务器
        print('连接到MT4服务器...')
        connected = mt4_client.connect()
        print(f'连接结果: {connected}')
        
        if not connected:
            print('连接MT4服务器失败，无法继续测试')
            return
        
        # 获取活跃订单
        print('\n获取活跃订单...')
        active_orders = mt4_client.get_active_orders()
        print(f'活跃订单: {active_orders}')
        
        # 检查是否有活跃订单
        orders = active_orders.get('orders', [])
        if not orders:
            print('没有活跃订单，无法测试关闭订单')
            return
        
        # 关闭第一个订单
        order = orders[0]
        order_id = order.get('order_id')
        if not order_id:
            print('订单ID为空，无法关闭订单')
            return
        
        print(f'\n关闭订单 {order_id}...')
        close_response = mt4_client.close_order(order_id)
        print(f'关闭订单响应: {close_response}')
        
        if close_response and close_response.get('status') == 'success':
            print('✅ 关闭订单成功!')
        else:
            print(f'❌ 关闭订单失败: {close_response.get("message") if close_response else "未知错误"}')
        
        # 再次获取活跃订单，确认订单已关闭
        print('\n再次获取活跃订单...')
        active_orders = mt4_client.get_active_orders()
        print(f'活跃订单: {active_orders}')
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_mt4_close_order()
