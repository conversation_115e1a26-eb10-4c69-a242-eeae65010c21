#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源适配器
连接实际的pizza_quotes数据库，获取真实的市场数据
"""

import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from app.utils.db_client import get_connection
    from app.utils.mt4_client import mt4_client
except ImportError as e:
    print(f"导入模块失败: {e}")
    get_connection = None
    mt4_client = None

class DataSourceAdapter:
    """数据源适配器"""

    def __init__(self):
        # 实际支持的交易品种（基于pizza_quotes数据库）
        self.supported_symbols = [
            'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD',
            'USDCHF', 'USDCAD', 'USDJPY', 'GOLD'
        ]

        # 货币对映射（数据库中的名称可能不同）
        self.symbol_mapping = {
            'EURUSD': 'EURUSD',
            'GBPUSD': 'GBPUSD',
            'AUDUSD': 'AUDUSD',
            'NZDUSD': 'NZDUSD',
            'USDCHF': 'USDCHF',
            'USDCAD': 'USDCAD',
            'USDJPY': 'USDJPY',
            'GOLD': 'GOLD'
        }

        self.logger = logging.getLogger(__name__)

    def get_available_symbols(self) -> List[str]:
        """获取可用的交易品种"""
        return self.supported_symbols.copy()

    def get_current_prices(self) -> Dict[str, float]:
        """获取当前价格"""
        prices = {}

        for symbol in self.supported_symbols:
            try:
                price = self._get_symbol_current_price(symbol)
                if price:
                    prices[symbol] = price
            except Exception as e:
                self.logger.warning(f"获取{symbol}当前价格失败: {e}")

        return prices

    def get_historical_data(self, symbol: str, period_minutes: int = 15,
                          count: int = 100) -> List[Dict]:
        """获取历史数据"""

        if symbol not in self.supported_symbols:
            self.logger.warning(f"不支持的交易品种: {symbol}")
            return []

        try:
            # 尝试从数据库获取历史数据
            return self._get_db_historical_data(symbol, period_minutes, count)
        except Exception as e:
            self.logger.error(f"获取{symbol}历史数据失败: {e}")
            return []

    def get_market_info(self, symbol: str) -> Optional[Dict]:
        """获取市场信息"""

        if symbol not in self.supported_symbols:
            return None

        try:
            # 尝试从MT4获取市场信息
            if mt4_client and hasattr(mt4_client, 'get_market_info'):
                response = mt4_client.get_market_info(symbol)
                if response and response.get('status') == 'success':
                    return response.get('data', {})
        except Exception as e:
            self.logger.warning(f"从MT4获取{symbol}市场信息失败: {e}")

        # 返回基础市场信息
        return {
            'symbol': symbol,
            'bid': 0.0,
            'ask': 0.0,
            'spread': 0.0,
            'digits': 5 if symbol != 'USDJPY' and symbol != 'GOLD' else 3
        }

    def _get_symbol_current_price(self, symbol: str) -> Optional[float]:
        """获取单个品种的当前价格"""

        try:
            # 周末MT4服务器关闭，直接从数据库获取最新价格
            return self._get_db_latest_price(symbol)

        except Exception as e:
            self.logger.error(f"获取{symbol}当前价格失败: {e}")
            return None

    def _get_db_latest_price(self, symbol: str) -> Optional[float]:
        """从数据库获取最新价格"""

        if not get_connection:
            return None

        try:
            connection = get_connection()
            with connection.cursor() as cursor:
                # 根据实际的数据库表结构查询最新价格
                table_name = f"min_quote_{symbol.lower()}"

                query = f"""
                SELECT price, time_date_str
                FROM {table_name}
                ORDER BY time_min_int DESC
                LIMIT 1
                """

                cursor.execute(query)
                result = cursor.fetchone()

                if result:
                    return float(result['price'])

        except Exception as e:
            self.logger.error(f"从数据库获取{symbol}最新价格失败: {e}")
        finally:
            if 'connection' in locals():
                connection.close()

        return None

    def _get_db_historical_data(self, symbol: str, period_minutes: int,
                               count: int) -> List[Dict]:
        """从数据库获取历史数据"""

        if not get_connection:
            return []

        try:
            connection = get_connection()
            with connection.cursor() as cursor:
                # 根据实际的数据库表结构查询历史数据
                table_name = f"min_quote_{symbol.lower()}"

                if period_minutes == 1:
                    # 1分钟数据直接查询
                    query = f"""
                    SELECT
                        time_date_str as timestamp,
                        price as open,
                        max as high,
                        min as low,
                        price as close,
                        volume
                    FROM {table_name}
                    ORDER BY time_min_int DESC
                    LIMIT %s
                    """
                    cursor.execute(query, (count,))
                else:
                    # 聚合数据 - 简化版本，基于分钟数据聚合
                    query = f"""
                    SELECT
                        DATE_FORMAT(STR_TO_DATE(time_date_str, '%%Y.%%m.%%d %%H:%%i'), '%%Y-%%m-%%d %%H:%%i:00') as period_start,
                        SUBSTRING_INDEX(GROUP_CONCAT(price ORDER BY time_min_int), ',', 1) as open,
                        MAX(max) as high,
                        MIN(min) as low,
                        SUBSTRING_INDEX(GROUP_CONCAT(price ORDER BY time_min_int DESC), ',', 1) as close,
                        SUM(volume) as volume,
                        MAX(STR_TO_DATE(time_date_str, '%%Y.%%m.%%d %%H:%%i')) as timestamp
                    FROM {table_name}
                    WHERE time_date_str >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL %s MINUTE), '%%Y.%%m.%%d %%H:%%i')
                    GROUP BY FLOOR(time_min_int / {period_minutes})
                    ORDER BY timestamp DESC
                    LIMIT %s
                    """
                    cursor.execute(query, (period_minutes * count * 2, count))

                results = cursor.fetchall()

                # 转换为标准格式
                historical_data = []
                for row in results:
                    # 处理时间戳
                    if isinstance(row.get('timestamp'), str):
                        # 如果是字符串，需要解析
                        timestamp_str = row['timestamp']
                        if '.' in timestamp_str:
                            # 格式：2025.05.23 23:57
                            timestamp = datetime.strptime(timestamp_str, '%Y.%m.%d %H:%M')
                        else:
                            # 格式：2025-05-23 23:57:00
                            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                    else:
                        # 如果是datetime对象
                        timestamp = row['timestamp']

                    historical_data.append({
                        'timestamp': timestamp,
                        'open': float(row['open']),
                        'high': float(row['high']),
                        'low': float(row['low']),
                        'close': float(row['close']),
                        'volume': int(row.get('volume', 0))
                    })

                # 按时间正序排列
                historical_data.reverse()
                return historical_data

        except Exception as e:
            self.logger.error(f"从数据库获取{symbol}历史数据失败: {e}")
        finally:
            if 'connection' in locals():
                connection.close()

        return []

    def test_connection(self) -> Dict[str, bool]:
        """测试数据源连接"""

        results = {
            'database': False,
            'mt4': False  # 周末MT4服务器关闭，跳过测试
        }

        # 测试数据库连接
        if get_connection:
            try:
                connection = get_connection()
                with connection.cursor() as cursor:
                    cursor.execute('SELECT 1')
                    result = cursor.fetchone()
                    if result:
                        results['database'] = True
                        self.logger.info("数据库连接测试成功")
            except Exception as e:
                self.logger.error(f"数据库连接测试失败: {e}")
            finally:
                if 'connection' in locals():
                    connection.close()

        # 周末跳过MT4连接测试
        self.logger.info("周末MT4服务器关闭，跳过MT4连接测试")

        return results

    def get_data_quality_report(self) -> Dict:
        """获取数据质量报告"""

        report = {
            'timestamp': datetime.now().isoformat(),
            'supported_symbols': len(self.supported_symbols),
            'symbol_status': {},
            'data_sources': self.test_connection()
        }

        # 检查每个品种的数据可用性
        for symbol in self.supported_symbols:
            try:
                current_price = self._get_symbol_current_price(symbol)
                historical_data = self.get_historical_data(symbol, 15, 10)

                report['symbol_status'][symbol] = {
                    'current_price_available': current_price is not None,
                    'current_price': current_price,
                    'historical_data_points': len(historical_data),
                    'latest_data_time': historical_data[-1]['timestamp'].isoformat() if historical_data else None
                }
            except Exception as e:
                report['symbol_status'][symbol] = {
                    'current_price_available': False,
                    'current_price': None,
                    'historical_data_points': 0,
                    'latest_data_time': None,
                    'error': str(e)
                }

        return report

# 创建全局实例
data_source = DataSourceAdapter()
