"""
测试MT4客户端服务器
"""
import os
import sys
import json
import time
import zmq
from datetime import datetime

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'

# 服务器地址
SERVER_ADDRESS = "tcp://127.0.0.1:5555"

def send_request(request):
    """发送请求到服务器"""
    try:
        # 创建ZeroMQ上下文和套接字
        context = zmq.Context()
        socket = context.socket(zmq.REQ)
        
        # 设置超时时间
        socket.setsockopt(zmq.RCVTIMEO, 30000)  # 30秒超时
        
        # 连接到服务器
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 正在连接到服务器: {SERVER_ADDRESS}')
        socket.connect(SERVER_ADDRESS)
        
        # 发送请求
        request_str = json.dumps(request)
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 发送请求: {request_str}')
        socket.send_string(request_str)
        
        # 接收响应
        response_str = socket.recv_string()
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 收到响应: {response_str}')
        
        # 解析响应
        response = json.loads(response_str)
        
        return response
    except zmq.error.Again:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器响应超时')
        return {'status': 'error', 'message': '服务器响应超时'}
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 发送请求失败: {e}')
        return {'status': 'error', 'message': f'发送请求失败: {e}'}
    finally:
        # 关闭套接字和上下文
        try:
            socket.close()
            context.term()
        except:
            pass

def test_ping():
    """测试ping请求"""
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试ping请求')
    
    request = {
        'action': 'ping',
        'requestId': 'test-ping'
    }
    
    response = send_request(request)
    
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ping响应: {response}')
    
    return response

def test_register():
    """测试注册请求"""
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试注册请求')
    
    request = {
        'action': 'REGISTER',
        'username': 'testuser',
        'password': 'qwert12345@'
    }
    
    response = send_request(request)
    
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 注册响应: {response}')
    
    return response

def test_login_with_credentials():
    """测试凭证登录请求"""
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试凭证登录请求')
    
    request = {
        'action': 'LOGIN_WITH_CREDENTIALS',
        'username': 'testuser',
        'password': 'qwert12345@'
    }
    
    response = send_request(request)
    
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 凭证登录响应: {response}')
    
    return response

def test_login_with_auth_code(auth_code):
    """测试授权码登录请求"""
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试授权码登录请求')
    
    request = {
        'action': 'LOGIN',
        'auth_code': auth_code
    }
    
    response = send_request(request)
    
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 授权码登录响应: {response}')
    
    return response

def test_get_signals(auth_code):
    """测试获取信号请求"""
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试获取信号请求')
    
    request = {
        'action': 'GET_SIGNALS',
        'auth_code': auth_code
    }
    
    response = send_request(request)
    
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 获取信号响应: {response}')
    
    return response

def run_tests():
    """运行所有测试"""
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 开始测试MT4客户端服务器')
    
    # 测试ping
    ping_response = test_ping()
    
    # 测试注册
    register_response = test_register()
    
    # 获取授权码
    auth_code = register_response.get('auth_code')
    
    if not auth_code:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 注册失败，无法获取授权码')
        return
    
    # 测试凭证登录
    login_credentials_response = test_login_with_credentials()
    
    # 测试授权码登录
    login_auth_response = test_login_with_auth_code(auth_code)
    
    # 测试获取信号
    signals_response = test_get_signals(auth_code)
    
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试完成')

if __name__ == '__main__':
    run_tests()
