"""
测试LLM分析功能
"""
import os
import sys
import json
import traceback
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
from app.utils import llm_client
from app.utils.multi_round_analysis import perform_multi_round_analysis, generate_initial_prompt

def test_initial_prompt_generation():
    """测试初始提示词生成"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 开始测试初始提示词生成...")

    # 构建测试数据
    test_data = {
        'symbol': 'EURUSD',
        'currentPrice': 1.13574,
        'previousClose': 1.13520,
        'priceChange': 0.00054,
        'priceChangePercent': 0.048,
        'timeframe15m': [
            {'time': '2025-05-23 19:45:00', 'open': 1.13520, 'high': 1.13580, 'low': 1.13510, 'close': 1.13550, 'volume': 100},
            {'time': '2025-05-23 20:00:00', 'open': 1.13550, 'high': 1.13590, 'low': 1.13540, 'close': 1.13570, 'volume': 120},
            {'time': '2025-05-23 20:15:00', 'open': 1.13570, 'high': 1.13600, 'low': 1.13560, 'close': 1.13580, 'volume': 110},
            {'time': '2025-05-23 20:30:00', 'open': 1.13580, 'high': 1.13610, 'low': 1.13570, 'close': 1.13590, 'volume': 130}
        ],
        'timeframe1h': [
            {'time': '2025-05-23 17:00:00', 'open': 1.13400, 'high': 1.13450, 'low': 1.13380, 'close': 1.13420, 'volume': 500},
            {'time': '2025-05-23 18:00:00', 'open': 1.13420, 'high': 1.13480, 'low': 1.13410, 'close': 1.13460, 'volume': 520},
            {'time': '2025-05-23 19:00:00', 'open': 1.13460, 'high': 1.13520, 'low': 1.13450, 'close': 1.13500, 'volume': 510},
            {'time': '2025-05-23 20:00:00', 'open': 1.13500, 'high': 1.13590, 'low': 1.13490, 'close': 1.13570, 'volume': 530}
        ],
        'indicators': {
            'ma20': 1.13406,
            'rsi': {'value': 55.2, 'trend': 'NEUTRAL'},
            'macd': {'value': 0.0003, 'signal': 0.0001, 'histogram': 0.0002}
        },
        'positions': [
            {'ticket': '12345', 'type': 'BUY', 'openTime': '2025-05-23 15:30:00', 'openPrice': 1.13400, 'stopLoss': 1.13300, 'takeProfit': 1.13600, 'volume': 0.01}
        ],
        'pendingOrders': []
    }

    try:
        # 生成初始提示词
        initial_prompt = generate_initial_prompt(test_data)
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 初始提示词生成成功，长度: {len(initial_prompt)} 字符")
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 初始提示词预览:\n{initial_prompt[:500]}...")
        return True
    except Exception as e:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 初始提示词生成失败! 错误: {str(e)}")
        traceback.print_exc()
        return False

def test_direct_llm_call():
    """直接测试LLM API调用"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 开始直接测试LLM API调用...")

    # 构建测试数据
    test_data = {
        'symbol': 'EURUSD',
        'currentPrice': 1.13574
    }

    # 生成简单的提示词
    prompt = f"""
    请分析欧元/美元(EURUSD)的当前市场状况。

    当前价格: {test_data['currentPrice']}

    请提供简短的市场分析和交易建议。
    """

    try:
        # 直接调用LLM API
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 发送请求到LLM API...")
        response = llm_client.send_to_deepseek(prompt, max_tokens=500)

        # 打印响应
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> LLM API响应成功")
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 响应JSON结构:\n{json.dumps(response, indent=2)[:500]}...")

        # 提取内容
        if 'choices' in response and len(response['choices']) > 0 and 'message' in response['choices'][0] and 'content' in response['choices'][0]['message']:
            content = response['choices'][0]['message']['content']
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 响应内容:\n{content}")
        else:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 错误: 响应JSON格式不正确，缺少必要的字段")
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 完整响应:\n{json.dumps(response, indent=2)}")

        return True
    except Exception as e:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> LLM API调用失败! 错误: {str(e)}")
        traceback.print_exc()
        return False

def test_llm_analysis():
    """测试LLM分析功能"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 开始测试LLM分析功能...")

    # 首先测试初始提示词生成
    if not test_initial_prompt_generation():
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 初始提示词生成测试失败，跳过多轮分析测试")
        return False

    # 然后测试直接LLM API调用
    if not test_direct_llm_call():
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 直接LLM API调用测试失败，跳过多轮分析测试")
        return False

    # 构建测试数据
    test_data = {
        'symbol': 'EURUSD',
        'currentPrice': 1.13574,
        'previousClose': 1.13520,
        'priceChange': 0.00054,
        'priceChangePercent': 0.048,
        'timeframe15m': [
            {'time': '2025-05-23 19:45:00', 'open': 1.13520, 'high': 1.13580, 'low': 1.13510, 'close': 1.13550, 'volume': 100},
            {'time': '2025-05-23 20:00:00', 'open': 1.13550, 'high': 1.13590, 'low': 1.13540, 'close': 1.13570, 'volume': 120},
            {'time': '2025-05-23 20:15:00', 'open': 1.13570, 'high': 1.13600, 'low': 1.13560, 'close': 1.13580, 'volume': 110},
            {'time': '2025-05-23 20:30:00', 'open': 1.13580, 'high': 1.13610, 'low': 1.13570, 'close': 1.13590, 'volume': 130}
        ],
        'timeframe1h': [
            {'time': '2025-05-23 17:00:00', 'open': 1.13400, 'high': 1.13450, 'low': 1.13380, 'close': 1.13420, 'volume': 500},
            {'time': '2025-05-23 18:00:00', 'open': 1.13420, 'high': 1.13480, 'low': 1.13410, 'close': 1.13460, 'volume': 520},
            {'time': '2025-05-23 19:00:00', 'open': 1.13460, 'high': 1.13520, 'low': 1.13450, 'close': 1.13500, 'volume': 510},
            {'time': '2025-05-23 20:00:00', 'open': 1.13500, 'high': 1.13590, 'low': 1.13490, 'close': 1.13570, 'volume': 530}
        ],
        'indicators': {
            'ma20': 1.13406,
            'rsi': {'value': 55.2, 'trend': 'NEUTRAL'},
            'macd': {'value': 0.0003, 'signal': 0.0001, 'histogram': 0.0002}
        },
        'positions': [
            {'ticket': '12345', 'type': 'BUY', 'openTime': '2025-05-23 15:30:00', 'openPrice': 1.13400, 'stopLoss': 1.13300, 'takeProfit': 1.13600, 'volume': 0.01}
        ],
        'pendingOrders': []
    }

    # 执行多轮分析
    try:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 开始执行多轮分析...")
        result = perform_multi_round_analysis(test_data, force_analysis=True)

        # 打印分析结果
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 分析结果:")
        print(f"时间戳: {result['timestamp']}")
        print(f"交易品种: {result['symbol']}")
        print(f"当前价格: {result['currentPrice']}")

        # 打印交易指令
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 交易指令:")
        print(json.dumps(result['tradeInstructions'], indent=2, ensure_ascii=False))

        # 打印最终分析结果
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 最终分析结果:")
        print(result['analysis']['final'][:500] + "...")

        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试成功!")
        return True
    except Exception as e:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 多轮分析测试失败! 错误: {str(e)}")
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 错误类型: {type(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_llm_analysis()
