"""
止损止盈检查工具
用于检查和修复无止损或无止盈订单
"""
from datetime import datetime

from app.utils import mt4_client
from app.utils.error_logger import log_error, log_operation, ErrorType, OperationType


def check_and_fix_orders():
    """
    检查所有订单，修复无止损或无止盈的订单

    Returns:
        dict: 检查和修复结果
    """
    try:
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 开始检查订单止损情况')

        # 确保MT4客户端已连接
        if not mt4_client.mt4_client.is_connected:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端未连接，尝试连接')
            connected = mt4_client.mt4_client.connect()
            if not connected:
                error_msg = '无法连接到MT4客户端，无法检查订单止损情况'
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] {error_msg}')
                log_error(
                    error_type=ErrorType.MT4_ERROR,
                    message=error_msg,
                    details={},
                    operation=OperationType.OTHER
                )
                return {
                    'success': False,
                    'message': error_msg,
                    'fixed_orders': []
                }

        # 初始化结果
        result = {
            'success': True,
            'message': '订单止损止盈检查完成',
            'fixed_orders': [],
            'active_orders_count': 0,
            'pending_orders_count': 0,
            'no_sl_active_orders_count': 0,
            'no_sl_pending_orders_count': 0,
            'no_tp_active_orders_count': 0,
            'no_tp_pending_orders_count': 0
        }

        # 检查活跃订单
        try:
            positions_response = mt4_client.mt4_client.get_active_orders()
            positions = positions_response.get('orders', [])

            # 过滤出EURUSD的持仓
            eurusd_positions = [p for p in positions if p.get('symbol') == 'EURUSD']
            result['active_orders_count'] = len(eurusd_positions)

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 当前EURUSD持仓数量: {len(eurusd_positions)}')

            # 获取当前价格，用于后续修复
            market_info_response = mt4_client.mt4_client.get_market_info('EURUSD')
            current_price = None
            if market_info_response and market_info_response.get('status') == 'success':
                current_price = float(market_info_response['data']['ask'])
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 当前价格: {current_price}')
            else:
                error_msg = '无法获取当前价格，无法修复订单'
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] {error_msg}')
                log_error(
                    error_type=ErrorType.MT4_ERROR,
                    message=error_msg,
                    details={},
                    operation=OperationType.OTHER
                )
                return {
                    'success': False,
                    'message': error_msg,
                    'fixed_orders': []
                }

            # 检查每个持仓的止损和止盈
            no_sl_positions = []  # 无止损的持仓
            no_tp_positions = []  # 无止盈的持仓

            for position in eurusd_positions:
                order_id = position.get('order_id')
                order_type = position.get('type', '').upper()
                sl = position.get('sl')
                tp = position.get('tp')
                open_price = float(position.get('open_price', current_price))

                # 检查是否有止损
                if sl is None or float(sl) == 0:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 订单 {order_id} 没有设置止损，需要修复')
                    no_sl_positions.append(position)

                # 检查是否有止盈
                if tp is None or float(tp) == 0:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 订单 {order_id} 没有设置止盈，需要修复')
                    no_tp_positions.append(position)

            result['no_sl_active_orders_count'] = len(no_sl_positions)
            result['no_tp_active_orders_count'] = len(no_tp_positions)

            # 合并需要修复的持仓（去重）
            positions_to_fix = []
            for position in eurusd_positions:
                order_id = position.get('order_id')
                sl = position.get('sl')
                tp = position.get('tp')

                # 如果止损或止盈为0，需要修复
                if (sl is None or float(sl) == 0) or (tp is None or float(tp) == 0):
                    positions_to_fix.append(position)

            # 修复需要修复的持仓
            if positions_to_fix:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 开始修复 {len(positions_to_fix)} 个需要修复的持仓')

                # 修复每个需要修复的持仓
                for position in positions_to_fix:
                    order_id = position.get('order_id')
                    order_type = position.get('type', '').upper()
                    sl = position.get('sl')
                    tp = position.get('tp')
                    open_price = float(position.get('open_price', current_price))

                    # 确保订单类型和开仓价格有效
                    if not order_type or not open_price:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 无法获取订单 {order_id} 的类型或开仓价格，跳过修复')
                        continue

                    # 确保止损有效
                    new_sl = float(sl) if sl is not None and float(sl) != 0 else None
                    if new_sl is None:
                        # 根据订单类型设置合理的止损
                        if 'BUY' in order_type:
                            # 买单，止损设置在开仓价格下方100点
                            new_sl = open_price - 0.01
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为买单 {order_id} 设置默认止损: {new_sl}')
                        elif 'SELL' in order_type:
                            # 卖单，止损设置在开仓价格上方100点
                            new_sl = open_price + 0.01
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为卖单 {order_id} 设置默认止损: {new_sl}')
                        else:
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 未知订单类型: {order_type}，无法设置默认止损')
                            continue

                    # 确保止盈有效
                    new_tp = float(tp) if tp is not None and float(tp) != 0 else None
                    if new_tp is None:
                        # 根据订单类型和止损设置合理的默认止盈
                        if 'BUY' in order_type:
                            # 买单，止盈设置为风险的1.5倍（建议的风险回报比）
                            risk = open_price - new_sl
                            new_tp = open_price + (risk * 1.5)  # 使用1.5倍风险作为默认值
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为买单 {order_id} 设置默认止盈: {new_tp}')
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提示: 这是系统设置的默认止盈，建议LLM根据市场情况调整')
                        elif 'SELL' in order_type:
                            # 卖单，止盈设置为风险的1.5倍（建议的风险回报比）
                            risk = new_sl - open_price
                            new_tp = open_price - (risk * 1.5)  # 使用1.5倍风险作为默认值
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为卖单 {order_id} 设置默认止盈: {new_tp}')
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提示: 这是系统设置的默认止盈，建议LLM根据市场情况调整')

                    # 计算风险回报比
                    if 'BUY' in order_type:
                        risk = open_price - new_sl
                        reward = new_tp - open_price
                    elif 'SELL' in order_type:
                        risk = new_sl - open_price
                        reward = open_price - new_tp
                    else:
                        risk = 0
                        reward = 0

                    risk_reward_ratio = reward / risk if risk > 0 else 0
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 订单 {order_id} 的风险回报比: {risk_reward_ratio:.2f}')

                    # 修改持仓
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改持仓: {order_id}, 新止损: {new_sl}, 新止盈: {new_tp}')
                    modify_result = mt4_client.mt4_client.modify_position(
                        order_id,
                        new_sl,
                        new_tp
                    )

                    # 记录修改结果
                    if modify_result.get('status') == 'success':
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改成功: {order_id}')
                        result['fixed_orders'].append({
                            'order_id': order_id,
                            'type': order_type,
                            'old_sl': sl,
                            'new_sl': new_sl,
                            'old_tp': tp,
                            'new_tp': new_tp,
                            'risk_reward_ratio': risk_reward_ratio,
                            'success': True
                        })

                        # 记录操作
                        log_operation(
                            operation_type=OperationType.MODIFY_ORDER,
                            success=True,
                            message=f'自动修复订单: {order_id}',
                            parameters={'order_id': order_id, 'new_sl': new_sl, 'new_tp': new_tp},
                            result={'status': 'success'}
                        )
                    else:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改失败: {order_id}, 原因: {modify_result.get("message", "")}')
                        result['fixed_orders'].append({
                            'order_id': order_id,
                            'type': order_type,
                            'old_sl': sl,
                            'new_sl': new_sl,
                            'old_tp': tp,
                            'new_tp': new_tp,
                            'risk_reward_ratio': risk_reward_ratio,
                            'success': False,
                            'error': modify_result.get('message', '')
                        })

                        # 记录错误
                        log_error(
                            error_type=ErrorType.MT4_ERROR,
                            message=f'自动修复订单失败: {order_id}',
                            details={'order_id': order_id, 'error': modify_result.get('message', '')},
                            operation=OperationType.MODIFY_ORDER
                        )
        except Exception as e:
            error_msg = f'检查活跃订单时出错: {e}'
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] {error_msg}')
            log_error(
                error_type=ErrorType.MT4_ERROR,
                message=error_msg,
                details={'exception': str(e)},
                operation=OperationType.OTHER
            )
            return {
                'success': False,
                'message': error_msg,
                'fixed_orders': result.get('fixed_orders', [])
            }

        # 检查挂单
        try:
            pending_orders_response = mt4_client.mt4_client.get_pending_orders()
            pending_orders = pending_orders_response.get('orders', [])

            # 过滤出EURUSD的挂单
            eurusd_pending_orders = [p for p in pending_orders if p.get('symbol') == 'EURUSD']
            result['pending_orders_count'] = len(eurusd_pending_orders)

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 当前EURUSD挂单数量: {len(eurusd_pending_orders)}')

            # 检查每个挂单的止损和止盈
            no_sl_pending_orders = []  # 无止损的挂单
            no_tp_pending_orders = []  # 无止盈的挂单

            for order in eurusd_pending_orders:
                order_id = order.get('order_id')
                order_type = order.get('type', '').upper()
                sl = order.get('sl')
                tp = order.get('tp')

                # 检查是否有止损
                if sl is None or float(sl) == 0:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 挂单 {order_id} 没有设置止损，需要修复')
                    no_sl_pending_orders.append(order)

                # 检查是否有止盈
                if tp is None or float(tp) == 0:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 挂单 {order_id} 没有设置止盈，需要修复')
                    no_tp_pending_orders.append(order)

            result['no_sl_pending_orders_count'] = len(no_sl_pending_orders)
            result['no_tp_pending_orders_count'] = len(no_tp_pending_orders)

            # 合并需要修复的挂单（去重）
            orders_to_fix = []
            for order in eurusd_pending_orders:
                order_id = order.get('order_id')
                sl = order.get('sl')
                tp = order.get('tp')

                # 如果止损或止盈为0，需要修复
                if (sl is None or float(sl) == 0) or (tp is None or float(tp) == 0):
                    orders_to_fix.append(order)

            # 修复需要修复的挂单
            if orders_to_fix:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 开始修复 {len(orders_to_fix)} 个需要修复的挂单')

                # 修复每个需要修复的挂单
                for order in orders_to_fix:
                    order_id = order.get('order_id')
                    order_type = order.get('type', '').upper()
                    sl = order.get('sl')
                    tp = order.get('tp')
                    order_price = float(order.get('open_price', 0))

                    # 确保订单类型和挂单价格有效
                    if not order_type or not order_price:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 无法获取挂单 {order_id} 的类型或价格，跳过修复')
                        continue

                    # 确保止损有效
                    new_sl = float(sl) if sl is not None and float(sl) != 0 else None
                    if new_sl is None:
                        # 根据挂单类型设置合理的止损
                        if 'BUYLIMIT' in order_type or 'BUYSTOP' in order_type:
                            # 买单挂单，止损设置在挂单价格下方100点
                            new_sl = order_price - 0.01
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为买单挂单 {order_id} 设置默认止损: {new_sl}')
                        elif 'SELLLIMIT' in order_type or 'SELLSTOP' in order_type:
                            # 卖单挂单，止损设置在挂单价格上方100点
                            new_sl = order_price + 0.01
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为卖单挂单 {order_id} 设置默认止损: {new_sl}')
                        else:
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 未知挂单类型: {order_type}，无法设置默认止损')
                            continue

                    # 确保止盈有效
                    new_tp = float(tp) if tp is not None and float(tp) != 0 else None
                    if new_tp is None:
                        # 根据挂单类型和止损设置合理的默认止盈
                        if 'BUYLIMIT' in order_type or 'BUYSTOP' in order_type:
                            # 买单挂单，止盈设置为风险的1.5倍（建议的风险回报比）
                            risk = order_price - new_sl
                            new_tp = order_price + (risk * 1.5)  # 使用1.5倍风险作为默认值
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为买单挂单 {order_id} 设置默认止盈: {new_tp}')
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提示: 这是系统设置的默认止盈，建议LLM根据市场情况调整')
                        elif 'SELLLIMIT' in order_type or 'SELLSTOP' in order_type:
                            # 卖单挂单，止盈设置为风险的1.5倍（建议的风险回报比）
                            risk = new_sl - order_price
                            new_tp = order_price - (risk * 1.5)  # 使用1.5倍风险作为默认值
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为卖单挂单 {order_id} 设置默认止盈: {new_tp}')
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提示: 这是系统设置的默认止盈，建议LLM根据市场情况调整')

                    # 计算风险回报比
                    if 'BUY' in order_type:
                        risk = order_price - new_sl
                        reward = new_tp - order_price
                    elif 'SELL' in order_type:
                        risk = new_sl - order_price
                        reward = order_price - new_tp
                    else:
                        risk = 0
                        reward = 0

                    risk_reward_ratio = reward / risk if risk > 0 else 0
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 挂单 {order_id} 的风险回报比: {risk_reward_ratio:.2f}')

                    # 修改挂单
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改挂单: {order_id}, 新止损: {new_sl}, 新止盈: {new_tp}')
                    modify_result = mt4_client.mt4_client.modify_order(
                        order_id,
                        None,  # 不修改入场价格
                        new_sl,
                        new_tp
                    )

                    # 记录修改结果
                    if modify_result.get('status') == 'success':
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改成功: {order_id}')
                        result['fixed_orders'].append({
                            'order_id': order_id,
                            'type': order_type,
                            'old_sl': sl,
                            'new_sl': new_sl,
                            'old_tp': tp,
                            'new_tp': new_tp,
                            'risk_reward_ratio': risk_reward_ratio,
                            'success': True
                        })

                        # 记录操作
                        log_operation(
                            operation_type=OperationType.MODIFY_ORDER,
                            success=True,
                            message=f'自动修复挂单: {order_id}',
                            parameters={'order_id': order_id, 'new_sl': new_sl, 'new_tp': new_tp},
                            result={'status': 'success'}
                        )
                    else:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改失败: {order_id}, 原因: {modify_result.get("message", "")}')
                        result['fixed_orders'].append({
                            'order_id': order_id,
                            'type': order_type,
                            'old_sl': sl,
                            'new_sl': new_sl,
                            'old_tp': tp,
                            'new_tp': new_tp,
                            'risk_reward_ratio': risk_reward_ratio,
                            'success': False,
                            'error': modify_result.get('message', '')
                        })

                        # 记录错误
                        log_error(
                            error_type=ErrorType.MT4_ERROR,
                            message=f'自动修复挂单失败: {order_id}',
                            details={'order_id': order_id, 'error': modify_result.get('message', '')},
                            operation=OperationType.MODIFY_ORDER
                        )
        except Exception as e:
            error_msg = f'检查挂单时出错: {e}'
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] {error_msg}')
            log_error(
                error_type=ErrorType.MT4_ERROR,
                message=error_msg,
                details={'exception': str(e)},
                operation=OperationType.OTHER
            )
            return {
                'success': False,
                'message': error_msg,
                'fixed_orders': result.get('fixed_orders', [])
            }

        # 更新结果消息
        if result['fixed_orders']:
            result['message'] = f'订单止损止盈检查完成，已修复 {len(result["fixed_orders"])} 个订单'
        else:
            result['message'] = '订单止损止盈检查完成，所有订单都已设置合理的止损和止盈'

        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] {result["message"]}')

        # 打印详细统计信息
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 活跃订单数量: {result["active_orders_count"]}')
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 无止损活跃订单数量: {result["no_sl_active_orders_count"]}')
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 无止盈活跃订单数量: {result["no_tp_active_orders_count"]}')
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 挂单数量: {result["pending_orders_count"]}')
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 无止损挂单数量: {result["no_sl_pending_orders_count"]}')
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 无止盈挂单数量: {result["no_tp_pending_orders_count"]}')

        return result

    except Exception as e:
        error_msg = f'检查和修复订单止损时出错: {e}'
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] {error_msg}')
        log_error(
            error_type=ErrorType.MT4_ERROR,
            message=error_msg,
            details={'exception': str(e)},
            operation=OperationType.OTHER
        )
        return {
            'success': False,
            'message': error_msg,
            'fixed_orders': []
        }
