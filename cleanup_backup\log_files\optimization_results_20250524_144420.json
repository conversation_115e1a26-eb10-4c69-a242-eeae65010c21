{"export_timestamp": "2025-05-24T14:44:20.139985", "optimization_summary": {"total_optimizations": 14, "avg_improvement": 12.597397020639397, "best_optimization": {"strategy": "趋势跟随", "symbol": "EURUSD", "improvement": 176.36355828895157, "confidence": 1.0, "date": "2025-05-24T14:44:06.567157"}, "strategy_performance": {"趋势跟随": {"count": 9, "avg_improvement": 19.595950920994614, "best_improvement": 176.36355828895157, "symbols": ["AUDUSD", "EURUSD", "USDCHF", "USDJPY", "GOLD", "USDCAD", "GBPUSD", "NZDUSD"]}, "均值回归": {"count": 2, "avg_improvement": 0.0, "best_improvement": 0.0, "symbols": ["GBPUSD"]}, "突破策略": {"count": 3, "avg_improvement": 0.0, "best_improvement": 0.0, "symbols": ["GOLD"]}}, "recent_optimizations": [{"strategy": "突破策略", "symbol": "GOLD", "improvement": 0.0, "confidence": 0.3, "date": "2025-05-24T14:44:07.317102"}, {"strategy": "突破策略", "symbol": "GOLD", "improvement": 0.0, "confidence": 0.3, "date": "2025-05-24T14:44:07.523858"}, {"strategy": "趋势跟随", "symbol": "EURUSD", "improvement": 0.0, "confidence": 0.3, "date": "2025-05-24T14:44:09.153161"}, {"strategy": "趋势跟随", "symbol": "GBPUSD", "improvement": 0.0, "confidence": 0.3, "date": "2025-05-24T14:44:10.758513"}, {"strategy": "趋势跟随", "symbol": "AUDUSD", "improvement": 0.0, "confidence": 0.3, "date": "2025-05-24T14:44:12.272764"}, {"strategy": "趋势跟随", "symbol": "NZDUSD", "improvement": 0.0, "confidence": 0.3, "date": "2025-05-24T14:44:13.789037"}, {"strategy": "趋势跟随", "symbol": "USDCHF", "improvement": 0.0, "confidence": 0.3, "date": "2025-05-24T14:44:15.349208"}, {"strategy": "趋势跟随", "symbol": "USDCAD", "improvement": 0.0, "confidence": 0.3, "date": "2025-05-24T14:44:16.875384"}, {"strategy": "趋势跟随", "symbol": "USDJPY", "improvement": 0.0, "confidence": 0.3, "date": "2025-05-24T14:44:18.388934"}, {"strategy": "趋势跟随", "symbol": "GOLD", "improvement": 0.0, "confidence": 0.3, "date": "2025-05-24T14:44:20.134406"}]}, "strategy_configurations": {"趋势跟随_EURUSD": {"strategy_type": "趋势跟随", "symbol": "EURUSD", "parameters": {"ma_fast_period": {"current_value": 50, "min_value": 5, "max_value": 50, "description": "快速移动平均周期"}, "ma_slow_period": {"current_value": 27, "min_value": 20, "max_value": 100, "description": "慢速移动平均周期"}, "rsi_period": {"current_value": 21, "min_value": 7, "max_value": 30, "description": "RSI周期"}, "rsi_overbought": {"current_value": 78.48236837911932, "min_value": 60, "max_value": 85, "description": "RSI超买阈值"}, "rsi_oversold": {"current_value": 32.21249751913416, "min_value": 15, "max_value": 40, "description": "RSI超卖阈值"}, "stop_loss_pips": {"current_value": 22.18780544416694, "min_value": 20, "max_value": 200, "description": "止损点数"}, "take_profit_pips": {"current_value": 214.05076024843743, "min_value": 30, "max_value": 300, "description": "止盈点数"}, "position_size": {"current_value": 0.6399692920040364, "min_value": 0.01, "max_value": 1.0, "description": "仓位大小"}}, "performance_metrics": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "last_optimization": "2025-05-24T14:44:06.567152", "optimization_count": 1}, "均值回归_GBPUSD": {"strategy_type": "均值回归", "symbol": "GBPUSD", "parameters": {"bollinger_period": {"current_value": 20, "min_value": 10, "max_value": 50, "description": "布林带周期"}, "bollinger_std": {"current_value": 2.0, "min_value": 1.5, "max_value": 3.0, "description": "布林带标准差"}, "rsi_period": {"current_value": 14, "min_value": 7, "max_value": 30, "description": "RSI周期"}, "rsi_entry_high": {"current_value": 80, "min_value": 70, "max_value": 90, "description": "RSI入场高位"}, "rsi_entry_low": {"current_value": 20, "min_value": 10, "max_value": 30, "description": "RSI入场低位"}, "stop_loss_pips": {"current_value": 30, "min_value": 15, "max_value": 100, "description": "止损点数"}, "take_profit_pips": {"current_value": 60, "min_value": 20, "max_value": 150, "description": "止盈点数"}, "position_size": {"current_value": 0.1, "min_value": 0.01, "max_value": 1.0, "description": "仓位大小"}}, "performance_metrics": {}, "last_optimization": null, "optimization_count": 0}, "突破策略_GOLD": {"strategy_type": "突破策略", "symbol": "GOLD", "parameters": {"breakout_period": {"current_value": 20, "min_value": 10, "max_value": 50, "description": "突破周期"}, "atr_period": {"current_value": 14, "min_value": 7, "max_value": 30, "description": "ATR周期"}, "atr_multiplier": {"current_value": 2.0, "min_value": 1.0, "max_value": 4.0, "description": "ATR倍数"}, "volume_threshold": {"current_value": 1.5, "min_value": 1.0, "max_value": 3.0, "description": "成交量阈值"}, "stop_loss_atr": {"current_value": 2.0, "min_value": 1.0, "max_value": 4.0, "description": "止损ATR倍数"}, "take_profit_atr": {"current_value": 4.0, "min_value": 2.0, "max_value": 8.0, "description": "止盈ATR倍数"}, "position_size": {"current_value": 0.1, "min_value": 0.01, "max_value": 1.0, "description": "仓位大小"}}, "performance_metrics": {}, "last_optimization": null, "optimization_count": 0}, "剥头皮_USDJPY": {"strategy_type": "剥头皮", "symbol": "USDJPY", "parameters": {"ema_fast": {"current_value": 5, "min_value": 3, "max_value": 15, "description": "快速EMA"}, "ema_slow": {"current_value": 15, "min_value": 10, "max_value": 30, "description": "慢速EMA"}, "stoch_k": {"current_value": 5, "min_value": 3, "max_value": 10, "description": "随机指标K值"}, "stoch_d": {"current_value": 3, "min_value": 2, "max_value": 5, "description": "随机指标D值"}, "stoch_overbought": {"current_value": 80, "min_value": 70, "max_value": 90, "description": "随机指标超买"}, "stoch_oversold": {"current_value": 20, "min_value": 10, "max_value": 30, "description": "随机指标超卖"}, "stop_loss_pips": {"current_value": 10, "min_value": 5, "max_value": 30, "description": "止损点数"}, "take_profit_pips": {"current_value": 15, "min_value": 8, "max_value": 50, "description": "止盈点数"}, "position_size": {"current_value": 0.05, "min_value": 0.01, "max_value": 0.5, "description": "仓位大小"}}, "performance_metrics": {}, "last_optimization": null, "optimization_count": 0}, "趋势跟随_GBPUSD": {"strategy_type": "趋势跟随", "symbol": "GBPUSD", "parameters": {"ma_fast_period": {"current_value": 50, "min_value": 5, "max_value": 50, "description": "快速移动平均周期"}, "ma_slow_period": {"current_value": 27, "min_value": 20, "max_value": 100, "description": "慢速移动平均周期"}, "rsi_period": {"current_value": 21, "min_value": 7, "max_value": 30, "description": "RSI周期"}, "rsi_overbought": {"current_value": 78.48236837911932, "min_value": 60, "max_value": 85, "description": "RSI超买阈值"}, "rsi_oversold": {"current_value": 32.21249751913416, "min_value": 15, "max_value": 40, "description": "RSI超卖阈值"}, "stop_loss_pips": {"current_value": 22.18780544416694, "min_value": 20, "max_value": 200, "description": "止损点数"}, "take_profit_pips": {"current_value": 214.05076024843743, "min_value": 30, "max_value": 300, "description": "止盈点数"}, "position_size": {"current_value": 0.6399692920040364, "min_value": 0.01, "max_value": 1.0, "description": "仓位大小"}}, "performance_metrics": {}, "last_optimization": null, "optimization_count": 0}, "趋势跟随_AUDUSD": {"strategy_type": "趋势跟随", "symbol": "AUDUSD", "parameters": {"ma_fast_period": {"current_value": 50, "min_value": 5, "max_value": 50, "description": "快速移动平均周期"}, "ma_slow_period": {"current_value": 27, "min_value": 20, "max_value": 100, "description": "慢速移动平均周期"}, "rsi_period": {"current_value": 21, "min_value": 7, "max_value": 30, "description": "RSI周期"}, "rsi_overbought": {"current_value": 78.48236837911932, "min_value": 60, "max_value": 85, "description": "RSI超买阈值"}, "rsi_oversold": {"current_value": 32.21249751913416, "min_value": 15, "max_value": 40, "description": "RSI超卖阈值"}, "stop_loss_pips": {"current_value": 22.18780544416694, "min_value": 20, "max_value": 200, "description": "止损点数"}, "take_profit_pips": {"current_value": 214.05076024843743, "min_value": 30, "max_value": 300, "description": "止盈点数"}, "position_size": {"current_value": 0.6399692920040364, "min_value": 0.01, "max_value": 1.0, "description": "仓位大小"}}, "performance_metrics": {}, "last_optimization": null, "optimization_count": 0}, "趋势跟随_NZDUSD": {"strategy_type": "趋势跟随", "symbol": "NZDUSD", "parameters": {"ma_fast_period": {"current_value": 50, "min_value": 5, "max_value": 50, "description": "快速移动平均周期"}, "ma_slow_period": {"current_value": 27, "min_value": 20, "max_value": 100, "description": "慢速移动平均周期"}, "rsi_period": {"current_value": 21, "min_value": 7, "max_value": 30, "description": "RSI周期"}, "rsi_overbought": {"current_value": 78.48236837911932, "min_value": 60, "max_value": 85, "description": "RSI超买阈值"}, "rsi_oversold": {"current_value": 32.21249751913416, "min_value": 15, "max_value": 40, "description": "RSI超卖阈值"}, "stop_loss_pips": {"current_value": 22.18780544416694, "min_value": 20, "max_value": 200, "description": "止损点数"}, "take_profit_pips": {"current_value": 214.05076024843743, "min_value": 30, "max_value": 300, "description": "止盈点数"}, "position_size": {"current_value": 0.6399692920040364, "min_value": 0.01, "max_value": 1.0, "description": "仓位大小"}}, "performance_metrics": {}, "last_optimization": null, "optimization_count": 0}, "趋势跟随_USDCHF": {"strategy_type": "趋势跟随", "symbol": "USDCHF", "parameters": {"ma_fast_period": {"current_value": 50, "min_value": 5, "max_value": 50, "description": "快速移动平均周期"}, "ma_slow_period": {"current_value": 27, "min_value": 20, "max_value": 100, "description": "慢速移动平均周期"}, "rsi_period": {"current_value": 21, "min_value": 7, "max_value": 30, "description": "RSI周期"}, "rsi_overbought": {"current_value": 78.48236837911932, "min_value": 60, "max_value": 85, "description": "RSI超买阈值"}, "rsi_oversold": {"current_value": 32.21249751913416, "min_value": 15, "max_value": 40, "description": "RSI超卖阈值"}, "stop_loss_pips": {"current_value": 22.18780544416694, "min_value": 20, "max_value": 200, "description": "止损点数"}, "take_profit_pips": {"current_value": 214.05076024843743, "min_value": 30, "max_value": 300, "description": "止盈点数"}, "position_size": {"current_value": 0.6399692920040364, "min_value": 0.01, "max_value": 1.0, "description": "仓位大小"}}, "performance_metrics": {}, "last_optimization": null, "optimization_count": 0}, "趋势跟随_USDCAD": {"strategy_type": "趋势跟随", "symbol": "USDCAD", "parameters": {"ma_fast_period": {"current_value": 50, "min_value": 5, "max_value": 50, "description": "快速移动平均周期"}, "ma_slow_period": {"current_value": 27, "min_value": 20, "max_value": 100, "description": "慢速移动平均周期"}, "rsi_period": {"current_value": 21, "min_value": 7, "max_value": 30, "description": "RSI周期"}, "rsi_overbought": {"current_value": 78.48236837911932, "min_value": 60, "max_value": 85, "description": "RSI超买阈值"}, "rsi_oversold": {"current_value": 32.21249751913416, "min_value": 15, "max_value": 40, "description": "RSI超卖阈值"}, "stop_loss_pips": {"current_value": 22.18780544416694, "min_value": 20, "max_value": 200, "description": "止损点数"}, "take_profit_pips": {"current_value": 214.05076024843743, "min_value": 30, "max_value": 300, "description": "止盈点数"}, "position_size": {"current_value": 0.6399692920040364, "min_value": 0.01, "max_value": 1.0, "description": "仓位大小"}}, "performance_metrics": {}, "last_optimization": null, "optimization_count": 0}, "趋势跟随_USDJPY": {"strategy_type": "趋势跟随", "symbol": "USDJPY", "parameters": {"ma_fast_period": {"current_value": 50, "min_value": 5, "max_value": 50, "description": "快速移动平均周期"}, "ma_slow_period": {"current_value": 27, "min_value": 20, "max_value": 100, "description": "慢速移动平均周期"}, "rsi_period": {"current_value": 21, "min_value": 7, "max_value": 30, "description": "RSI周期"}, "rsi_overbought": {"current_value": 78.48236837911932, "min_value": 60, "max_value": 85, "description": "RSI超买阈值"}, "rsi_oversold": {"current_value": 32.21249751913416, "min_value": 15, "max_value": 40, "description": "RSI超卖阈值"}, "stop_loss_pips": {"current_value": 22.18780544416694, "min_value": 20, "max_value": 200, "description": "止损点数"}, "take_profit_pips": {"current_value": 214.05076024843743, "min_value": 30, "max_value": 300, "description": "止盈点数"}, "position_size": {"current_value": 0.6399692920040364, "min_value": 0.01, "max_value": 1.0, "description": "仓位大小"}}, "performance_metrics": {}, "last_optimization": null, "optimization_count": 0}, "趋势跟随_GOLD": {"strategy_type": "趋势跟随", "symbol": "GOLD", "parameters": {"ma_fast_period": {"current_value": 50, "min_value": 5, "max_value": 50, "description": "快速移动平均周期"}, "ma_slow_period": {"current_value": 27, "min_value": 20, "max_value": 100, "description": "慢速移动平均周期"}, "rsi_period": {"current_value": 21, "min_value": 7, "max_value": 30, "description": "RSI周期"}, "rsi_overbought": {"current_value": 78.48236837911932, "min_value": 60, "max_value": 85, "description": "RSI超买阈值"}, "rsi_oversold": {"current_value": 32.21249751913416, "min_value": 15, "max_value": 40, "description": "RSI超卖阈值"}, "stop_loss_pips": {"current_value": 22.18780544416694, "min_value": 20, "max_value": 200, "description": "止损点数"}, "take_profit_pips": {"current_value": 214.05076024843743, "min_value": 30, "max_value": 300, "description": "止盈点数"}, "position_size": {"current_value": 0.6399692920040364, "min_value": 0.01, "max_value": 1.0, "description": "仓位大小"}}, "performance_metrics": {}, "last_optimization": null, "optimization_count": 0}}, "optimization_history": [{"strategy_type": "趋势跟随", "symbol": "EURUSD", "optimization_method": "遗传算法", "target": "最大化夏普比率", "original_parameters": {"ma_fast_period": 10, "ma_slow_period": 30, "rsi_period": 14, "rsi_overbought": 70, "rsi_oversold": 30, "stop_loss_pips": 50, "take_profit_pips": 100, "position_size": 0.1}, "optimized_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "original_performance": {"total_return": 0.1347062933666056, "sharpe_ratio": 0.20485921332580134, "max_drawdown": 0.03551364042308421, "win_rate": 0.48, "profit_factor": 1.561276793562855, "volatility": 0.006575554556698075, "num_trades": 100}, "optimized_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "improvement_percentage": 176.36355828895157, "confidence_score": 1.0, "optimization_date": "2025-05-24T14:44:06.567157"}, {"strategy_type": "均值回归", "symbol": "GBPUSD", "optimization_method": "网格搜索", "target": "最大化胜率", "original_parameters": {"bollinger_period": 20, "bollinger_std": 2.0, "rsi_period": 14, "rsi_entry_high": 80, "rsi_entry_low": 20, "stop_loss_pips": 30, "take_profit_pips": 60, "position_size": 0.1}, "optimized_parameters": {"bollinger_period": 26, "bollinger_std": 1.5, "rsi_period": 7, "rsi_entry_high": 86, "rsi_entry_low": 10, "stop_loss_pips": 45, "take_profit_pips": 20, "position_size": 0.01}, "original_performance": {"total_return": 0.10373839404306141, "sharpe_ratio": 0.24490191262480612, "max_drawdown": 0.015152533317646402, "win_rate": 0.48, "profit_factor": 1.6890937849574668, "volatility": 0.004235916042109087, "num_trades": 100}, "optimized_performance": {"total_return": -0.12641043070439978, "sharpe_ratio": -0.40466445992903943, "max_drawdown": 0.13189236766289453, "win_rate": 0.48, "profit_factor": 0.4217419687059397, "volatility": 0.003123833279714426, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:06.830952"}, {"strategy_type": "均值回归", "symbol": "GBPUSD", "optimization_method": "随机搜索", "target": "最大化胜率", "original_parameters": {"bollinger_period": 20, "bollinger_std": 2.0, "rsi_period": 14, "rsi_entry_high": 80, "rsi_entry_low": 20, "stop_loss_pips": 30, "take_profit_pips": 60, "position_size": 0.1}, "optimized_parameters": {"bollinger_period": 18, "bollinger_std": 2.227244957038475, "rsi_period": 7, "rsi_entry_high": 72.39730734667366, "rsi_entry_low": 16.75230342807256, "stop_loss_pips": 95.14732483256412, "take_profit_pips": 62.01638116269818, "position_size": 0.5236027155259324}, "original_performance": {"total_return": 0.10373839404306141, "sharpe_ratio": 0.24490191262480612, "max_drawdown": 0.015152533317646402, "win_rate": 0.48, "profit_factor": 1.6890937849574668, "volatility": 0.004235916042109087, "num_trades": 100}, "optimized_performance": {"total_return": -0.14401252863858047, "sharpe_ratio": -0.2063576253853498, "max_drawdown": 0.17505081496234848, "win_rate": 0.48, "profit_factor": 0.6451950913555916, "volatility": 0.0069787839615644545, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:06.882598"}, {"strategy_type": "突破策略", "symbol": "GOLD", "optimization_method": "遗传算法", "target": "最大化收益", "original_parameters": {"breakout_period": 20, "atr_period": 14, "atr_multiplier": 2.0, "volume_threshold": 1.5, "stop_loss_atr": 2.0, "take_profit_atr": 4.0, "position_size": 0.1}, "optimized_parameters": {"breakout_period": 18, "atr_period": 11, "atr_multiplier": 3.077308098670811, "volume_threshold": 1.538824667597043, "stop_loss_atr": 1.7323765667433224, "take_profit_atr": 3.0097462530375836, "position_size": 0.22657657737733955}, "original_performance": {"total_return": 0.13167110463578482, "sharpe_ratio": 0.2014760702438064, "max_drawdown": 0.036142232667500857, "win_rate": 0.48, "profit_factor": 1.5508970750199227, "volatility": 0.0065353222581942105, "num_trades": 100}, "optimized_performance": {"total_return": 0.13167110463578482, "sharpe_ratio": 0.2014760702438064, "max_drawdown": 0.036142232667500857, "win_rate": 0.48, "profit_factor": 1.5508970750199227, "volatility": 0.0065353222581942105, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:07.101407"}, {"strategy_type": "突破策略", "symbol": "GOLD", "optimization_method": "遗传算法", "target": "最小化回撤", "original_parameters": {"breakout_period": 20, "atr_period": 14, "atr_multiplier": 2.0, "volume_threshold": 1.5, "stop_loss_atr": 2.0, "take_profit_atr": 4.0, "position_size": 0.1}, "optimized_parameters": {"breakout_period": 18, "atr_period": 11, "atr_multiplier": 3.077308098670811, "volume_threshold": 1.538824667597043, "stop_loss_atr": 1.7323765667433224, "take_profit_atr": 3.0097462530375836, "position_size": 0.22657657737733955}, "original_performance": {"total_return": 0.13167110463578482, "sharpe_ratio": 0.2014760702438064, "max_drawdown": 0.036142232667500857, "win_rate": 0.48, "profit_factor": 1.5508970750199227, "volatility": 0.0065353222581942105, "num_trades": 100}, "optimized_performance": {"total_return": 0.13167110463578482, "sharpe_ratio": 0.2014760702438064, "max_drawdown": 0.036142232667500857, "win_rate": 0.48, "profit_factor": 1.5508970750199227, "volatility": 0.0065353222581942105, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:07.317102"}, {"strategy_type": "突破策略", "symbol": "GOLD", "optimization_method": "遗传算法", "target": "最大化盈利因子", "original_parameters": {"breakout_period": 20, "atr_period": 14, "atr_multiplier": 2.0, "volume_threshold": 1.5, "stop_loss_atr": 2.0, "take_profit_atr": 4.0, "position_size": 0.1}, "optimized_parameters": {"breakout_period": 18, "atr_period": 11, "atr_multiplier": 3.077308098670811, "volume_threshold": 1.538824667597043, "stop_loss_atr": 1.7323765667433224, "take_profit_atr": 3.0097462530375836, "position_size": 0.22657657737733955}, "original_performance": {"total_return": 0.13167110463578482, "sharpe_ratio": 0.2014760702438064, "max_drawdown": 0.036142232667500857, "win_rate": 0.48, "profit_factor": 1.5508970750199227, "volatility": 0.0065353222581942105, "num_trades": 100}, "optimized_performance": {"total_return": 0.13167110463578482, "sharpe_ratio": 0.2014760702438064, "max_drawdown": 0.036142232667500857, "win_rate": 0.48, "profit_factor": 1.5508970750199227, "volatility": 0.0065353222581942105, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:07.523858"}, {"strategy_type": "趋势跟随", "symbol": "EURUSD", "optimization_method": "遗传算法", "target": "最大化夏普比率", "original_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "optimized_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "original_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "optimized_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:09.153161"}, {"strategy_type": "趋势跟随", "symbol": "GBPUSD", "optimization_method": "遗传算法", "target": "最大化夏普比率", "original_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "optimized_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "original_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "optimized_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:10.758513"}, {"strategy_type": "趋势跟随", "symbol": "AUDUSD", "optimization_method": "遗传算法", "target": "最大化夏普比率", "original_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "optimized_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "original_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "optimized_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:12.272764"}, {"strategy_type": "趋势跟随", "symbol": "NZDUSD", "optimization_method": "遗传算法", "target": "最大化夏普比率", "original_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "optimized_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "original_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "optimized_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:13.789037"}, {"strategy_type": "趋势跟随", "symbol": "USDCHF", "optimization_method": "遗传算法", "target": "最大化夏普比率", "original_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "optimized_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "original_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "optimized_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:15.349208"}, {"strategy_type": "趋势跟随", "symbol": "USDCAD", "optimization_method": "遗传算法", "target": "最大化夏普比率", "original_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "optimized_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "original_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "optimized_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:16.875384"}, {"strategy_type": "趋势跟随", "symbol": "USDJPY", "optimization_method": "遗传算法", "target": "最大化夏普比率", "original_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "optimized_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "original_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "optimized_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:18.388934"}, {"strategy_type": "趋势跟随", "symbol": "GOLD", "optimization_method": "遗传算法", "target": "最大化夏普比率", "original_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "optimized_parameters": {"ma_fast_period": 50, "ma_slow_period": 27, "rsi_period": 21, "rsi_overbought": 78.48236837911932, "rsi_oversold": 32.21249751913416, "stop_loss_pips": 22.18780544416694, "take_profit_pips": 214.05076024843743, "position_size": 0.6399692920040364}, "original_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "optimized_performance": {"total_return": 0.5236571551304772, "sharpe_ratio": 0.5661562114299387, "max_drawdown": 0.010431229612054643, "win_rate": 0.48, "profit_factor": 5.638638671374059, "volatility": 0.009249340456901078, "num_trades": 100}, "improvement_percentage": 0.0, "confidence_score": 0.3, "optimization_date": "2025-05-24T14:44:20.134406"}]}