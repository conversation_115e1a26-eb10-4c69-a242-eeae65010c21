# 外汇交易系统授权系统使用说明

## 概述

外汇交易系统支持多用户授权机制，授权验证完全由MT4 Server-V2处理。本文档介绍如何设置和使用授权系统。

## 授权机制

系统采用授权码机制进行用户验证，主要特点：

1. **授权验证**：完全由MT4 Server-V2验证授权码有效性
2. **多服务器支持**：可以连接到不同的MT4服务器
3. **授权期限**：支持设置授权有效期
4. **轻量级实现**：无需复杂的用户管理系统
5. **集中管理**：所有用户注册和登录功能都在MT4 Server-V2上实现

## 使用方法

### 1. 设置授权码

使用`set_auth.py`工具设置授权码：

```bash
# 设置授权码并测试
python set_auth.py --auth-code YOUR_AUTH_CODE

# 设置授权码并指定服务器地址
python set_auth.py --auth-code YOUR_AUTH_CODE --server tcp://192.168.1.100:5555

# 只测试授权码，不保存
python set_auth.py --auth-code YOUR_AUTH_CODE --test

# 只保存授权码，不测试
python set_auth.py --auth-code YOUR_AUTH_CODE --save
```

授权码由MT4 Server-V2管理员提供，用户无需自行注册。

### 2. 启动系统

设置授权码后，系统会自动使用授权码连接MT4服务器：

```bash
python run.py
```

系统启动时会：
1. 加载`.env.local`文件中的授权码
2. 使用授权码连接MT4服务器
3. 验证授权状态
4. 开始交易分析和执行

### 3. 查看授权状态

系统启动后，会在日志中显示授权状态：

```
[2025-05-01 12:00:00] 检测到授权码，将使用授权码连接MT4服务器
[2025-05-01 12:00:01] 授权验证成功，用户: 测试用户1，服务器: 本地服务器
```

## 服务器端配置

在MT4 Server-V2端需要进行以下配置：

1. **启用授权验证**：在MT4 Server-V2配置文件中启用授权验证功能
2. **添加授权码**：为每个用户生成唯一的授权码
3. **设置授权期限**：设置授权码的有效期
4. **设置服务器地址**：配置服务器监听地址和端口
5. **用户管理**：实现用户注册、登录和授权管理功能
6. **权限控制**：设置不同用户的权限级别

## 多用户支持

系统支持多用户使用，主要通过以下方式实现：

1. **授权码区分**：每个用户使用不同的授权码
2. **服务器识别**：MT4 Server-V2根据授权码识别用户
3. **信号分发**：服务器将交易信号分发给已授权的用户

## 安全考虑

1. **授权码保护**：授权码应妥善保管，避免泄露
2. **通信加密**：所有通信使用SSL/TLS加密
3. **授权验证**：每次连接都会验证授权状态
4. **日志记录**：记录所有授权操作，便于追踪问题

## 常见问题

### 授权验证失败

可能原因：
- 授权码无效或过期
- 服务器地址错误
- 网络连接问题
- MT4 Server-V2未启动

解决方法：
- 检查授权码是否正确
- 确认服务器地址和端口
- 检查网络连接
- 确认MT4 Server-V2已启动

### 无法连接到服务器

可能原因：
- 服务器未启动
- 防火墙阻止连接
- 网络问题

解决方法：
- 确认服务器已启动
- 检查防火墙设置
- 测试网络连接

### 授权过期

解决方法：
- 联系管理员续期授权
- 获取新的授权码

## 技术实现

系统授权机制的主要实现：

1. **MT4Client类**：添加授权相关属性和方法
2. **授权验证**：在连接时发送授权信息，由MT4 Server-V2验证
3. **请求处理**：在每个请求中添加授权信息
4. **配置管理**：使用`.env.local`文件存储授权信息
5. **错误处理**：添加授权错误类型和日志记录

## 未来扩展

1. **MT4 Server-V2一键安装**：开发MT4 Server-V2的一键安装包，简化用户部署
2. **授权级别**：支持不同级别的授权，提供不同的功能
3. **授权统计**：记录授权使用情况，生成统计报告
4. **自动续期**：支持授权自动续期功能
5. **多账户管理**：支持一个授权码管理多个MT4账户
