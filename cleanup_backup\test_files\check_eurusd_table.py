#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门检查EURUSD表结构
"""

import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_eurusd_table():
    """检查EURUSD表结构"""

    try:
        from app.utils.db_client import get_connection

        conn = get_connection()
        if not conn:
            print("❌ 无法连接到数据库")
            return

        cursor = conn.cursor()

        print("🔍 检查min_quote_eurusd表结构...")
        print("=" * 50)

        # 检查表结构
        print("📋 表结构:")
        cursor.execute("DESCRIBE min_quote_eurusd")
        columns = cursor.fetchall()

        print(f"原始列信息: {columns}")

        # 处理不同的返回格式
        column_names = []
        for i, col in enumerate(columns):
            if isinstance(col, dict):
                # 字典格式
                field_name = col.get('Field', list(col.values())[0] if col else 'unknown')
                column_names.append(field_name)
                print(f"   {i+1}. {field_name}")
            elif isinstance(col, (list, tuple)) and len(col) > 0:
                # 列表/元组格式
                field_name = col[0]
                column_names.append(field_name)
                print(f"   {i+1}. {field_name} ({col[1] if len(col) > 1 else 'unknown'})")
            else:
                print(f"   {i+1}. 未知格式: {col}")

        print(f"\n📝 列名列表: {column_names}")

        # 检查数据量
        cursor.execute("SELECT COUNT(*) FROM min_quote_eurusd")
        count_result = cursor.fetchone()
        count = count_result[0] if isinstance(count_result, (list, tuple)) else list(count_result.values())[0]
        print(f"\n📊 数据量: {count} 条")

        if count > 0:
            # 显示前5条数据
            print(f"\n📄 前5条数据:")
            cursor.execute("SELECT * FROM min_quote_eurusd LIMIT 5")
            results = cursor.fetchall()

            for i, row in enumerate(results):
                print(f"   {i+1}. {row}")

            # 显示最后5条数据
            print(f"\n📄 最后5条数据:")
            # 尝试不同的排序方式
            try:
                cursor.execute("SELECT * FROM min_quote_eurusd ORDER BY id DESC LIMIT 5")
                results = cursor.fetchall()
                print("   (按id倒序)")
                for i, row in enumerate(results):
                    print(f"   {i+1}. {row}")
            except:
                try:
                    cursor.execute("SELECT * FROM min_quote_eurusd ORDER BY time DESC LIMIT 5")
                    results = cursor.fetchall()
                    print("   (按time倒序)")
                    for i, row in enumerate(results):
                        print(f"   {i+1}. {row}")
                except:
                    print("   无法按时间排序，显示最后插入的数据")
                    cursor.execute("SELECT * FROM min_quote_eurusd ORDER BY ROWID DESC LIMIT 5")
                    results = cursor.fetchall()
                    for i, row in enumerate(results):
                        print(f"   {i+1}. {row}")

        conn.close()

    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_eurusd_table()
