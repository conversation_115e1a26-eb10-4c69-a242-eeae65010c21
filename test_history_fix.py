#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试历史记录修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_history_functions():
    """测试历史记录功能"""
    print("🧪 测试历史记录功能")
    
    try:
        from app.utils.forex_analysis_history import get_recent_analysis_records, get_latest_analysis_record
        
        # 测试获取历史记录
        print("  测试获取历史记录...")
        records = get_recent_analysis_records(3)
        print(f"  ✅ 获取历史记录成功: {len(records)} 条记录")
        
        # 测试获取最新记录
        print("  测试获取最新记录...")
        latest = get_latest_analysis_record()
        if latest:
            print(f"  ✅ 获取最新记录成功: {latest.get('timestamp', 'unknown')}")
        else:
            print("  ⚠️ 没有找到最新记录")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 历史记录功能测试失败: {e}")
        return False

def test_json_parsing():
    """测试JSON解析修复"""
    print("\n🔧 测试JSON解析修复")
    
    try:
        from app.utils.mt4_client import MT4Client
        mt4_client = MT4Client()
        
        if mt4_client.connect():
            print("  ✅ MT4连接成功")
            
            # 测试获取挂单（这个经常出现JSON解析错误）
            print("  测试获取挂单...")
            pending_orders = mt4_client.get_pending_orders()
            
            if pending_orders.get('status') == 'success':
                orders = pending_orders.get('orders', [])
                print(f"  ✅ 获取挂单成功: {len(orders)} 个挂单")
            else:
                print(f"  ⚠️ 获取挂单失败: {pending_orders.get('message', 'unknown')}")
            
            return True
        else:
            print("  ❌ MT4连接失败")
            return False
            
    except Exception as e:
        print(f"  ❌ JSON解析测试失败: {e}")
        return False

def test_system_startup():
    """测试系统启动组件"""
    print("\n🚀 测试系统启动组件")
    
    try:
        # 测试智能货币对选择
        print("  测试智能货币对选择...")
        from app.utils.intelligent_pair_selector import select_optimal_currency_pairs
        pairs = select_optimal_currency_pairs()
        print(f"  ✅ 智能选择结果: {pairs}")
        
        # 测试数据获取
        print("  测试数据获取...")
        from app.utils.multi_pair_data_manager import get_multi_pair_analysis_data
        data = get_multi_pair_analysis_data(pairs)
        print(f"  ✅ 数据获取成功: {len(data)} 个货币对")
        
        # 测试监控系统
        print("  测试监控系统...")
        from app.utils.real_time_monitor import RealTimeMonitor
        monitor = RealTimeMonitor()
        status = monitor.get_current_status()
        print(f"  ✅ 监控系统状态: {status.get('monitoring_status', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 系统启动组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 错误修复验证测试")
    print("=" * 50)
    
    # 执行各项测试
    tests = [
        ("历史记录功能", test_history_functions),
        ("JSON解析修复", test_json_parsing),
        ("系统启动组件", test_system_startup)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results[test_name] = success
        except Exception as e:
            print(f"  ❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {test_name}")
    
    success_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有错误修复验证通过！")
    elif success_count >= total_count * 0.8:
        print("✅ 大部分错误已修复")
    else:
        print("⚠️ 仍有错误需要进一步修复")

if __name__ == "__main__":
    main()
