"""
QuantumForex MLTrainer 技术指标特征工程
计算各种技术指标作为机器学习特征
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional

from config.training_config import training_config

# 如果TA-Lib不可用，使用pandas实现基础指标
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("⚠️ TA-Lib未安装，将使用pandas实现基础技术指标")

class TechnicalFeatureEngine:
    """技术指标特征工程器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 特征配置
        self.feature_config = training_config.FEATURE_CONFIG['technical_indicators']

        # 生成的特征列表
        self.feature_columns = []

    def generate_all_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成所有技术指标特征"""
        try:
            self.logger.info("🔧 开始生成技术指标特征...")

            # 确保数据格式正确
            df = self._prepare_data(df)

            # 生成各类技术指标
            df = self._add_moving_averages(df)
            df = self._add_ema_indicators(df)
            df = self._add_rsi_indicators(df)
            df = self._add_macd_indicators(df)
            df = self._add_bollinger_bands(df)
            df = self._add_stochastic_indicators(df)
            df = self._add_atr_indicators(df)
            df = self._add_adx_indicators(df)
            df = self._add_cci_indicators(df)
            df = self._add_williams_r_indicators(df)
            df = self._add_momentum_indicators(df)
            df = self._add_volume_indicators(df)

            # 删除包含NaN的行
            df = df.dropna()

            self.logger.info(f"✅ 技术指标特征生成完成: {len(self.feature_columns)}个特征")
            return df

        except Exception as e:
            self.logger.error(f"❌ 生成技术指标特征失败: {e}")
            return df

    def _prepare_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """准备数据"""
        try:
            # 确保必要的列存在
            required_columns = ['open', 'high', 'low', 'close']
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"缺少必要列: {col}")

            # 确保数据类型正确
            for col in required_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            if 'volume' in df.columns:
                df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
            else:
                df['volume'] = 1000  # 默认成交量

            return df

        except Exception as e:
            self.logger.error(f"数据准备失败: {e}")
            raise

    def _add_moving_averages(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加移动平均线指标"""
        try:
            periods = self.feature_config['moving_averages']

            for period in periods:
                # 简单移动平均
                col_name = f'SMA_{period}'
                if TALIB_AVAILABLE:
                    df[col_name] = talib.SMA(df['close'], timeperiod=period)
                else:
                    df[col_name] = df['close'].rolling(window=period).mean()
                self.feature_columns.append(col_name)

                # 价格相对于移动平均线的位置
                col_name = f'Price_SMA_{period}_Ratio'
                df[col_name] = df['close'] / df[f'SMA_{period}']
                self.feature_columns.append(col_name)

            # 移动平均线之间的关系
            if len(periods) >= 2:
                short_ma = f'SMA_{periods[0]}'
                long_ma = f'SMA_{periods[-1]}'
                col_name = f'{short_ma}_{long_ma}_Ratio'
                df[col_name] = df[short_ma] / df[long_ma]
                self.feature_columns.append(col_name)

            return df

        except Exception as e:
            self.logger.error(f"添加移动平均线失败: {e}")
            return df

    def _add_ema_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加指数移动平均线指标"""
        try:
            periods = self.feature_config['ema_periods']

            for period in periods:
                # 指数移动平均
                col_name = f'EMA_{period}'
                df[col_name] = talib.EMA(df['close'], timeperiod=period)
                self.feature_columns.append(col_name)

                # 价格相对于EMA的位置
                col_name = f'Price_EMA_{period}_Ratio'
                df[col_name] = df['close'] / df[f'EMA_{period}']
                self.feature_columns.append(col_name)

            return df

        except Exception as e:
            self.logger.error(f"添加EMA指标失败: {e}")
            return df

    def _add_rsi_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加RSI指标"""
        try:
            periods = self.feature_config['rsi_periods']

            for period in periods:
                # RSI
                col_name = f'RSI_{period}'
                df[col_name] = talib.RSI(df['close'], timeperiod=period)
                self.feature_columns.append(col_name)

                # RSI超买超卖信号
                col_name = f'RSI_{period}_Overbought'
                df[col_name] = (df[f'RSI_{period}'] > 70).astype(int)
                self.feature_columns.append(col_name)

                col_name = f'RSI_{period}_Oversold'
                df[col_name] = (df[f'RSI_{period}'] < 30).astype(int)
                self.feature_columns.append(col_name)

            return df

        except Exception as e:
            self.logger.error(f"添加RSI指标失败: {e}")
            return df

    def _add_macd_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加MACD指标"""
        try:
            macd_params = self.feature_config['macd_params']

            for fast, slow, signal in macd_params:
                # MACD
                macd, macd_signal, macd_hist = talib.MACD(
                    df['close'],
                    fastperiod=fast,
                    slowperiod=slow,
                    signalperiod=signal
                )

                prefix = f'MACD_{fast}_{slow}_{signal}'

                df[f'{prefix}_Line'] = macd
                df[f'{prefix}_Signal'] = macd_signal
                df[f'{prefix}_Histogram'] = macd_hist

                self.feature_columns.extend([
                    f'{prefix}_Line',
                    f'{prefix}_Signal',
                    f'{prefix}_Histogram'
                ])

                # MACD信号
                df[f'{prefix}_Bullish'] = (macd > macd_signal).astype(int)
                df[f'{prefix}_Bearish'] = (macd < macd_signal).astype(int)

                self.feature_columns.extend([
                    f'{prefix}_Bullish',
                    f'{prefix}_Bearish'
                ])

            return df

        except Exception as e:
            self.logger.error(f"添加MACD指标失败: {e}")
            return df

    def _add_bollinger_bands(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加布林带指标"""
        try:
            bb_params = self.feature_config['bollinger_bands']

            for period, std_dev in bb_params:
                # 布林带
                upper, middle, lower = talib.BBANDS(
                    df['close'],
                    timeperiod=period,
                    nbdevup=std_dev,
                    nbdevdn=std_dev
                )

                prefix = f'BB_{period}_{std_dev}'

                df[f'{prefix}_Upper'] = upper
                df[f'{prefix}_Middle'] = middle
                df[f'{prefix}_Lower'] = lower

                self.feature_columns.extend([
                    f'{prefix}_Upper',
                    f'{prefix}_Middle',
                    f'{prefix}_Lower'
                ])

                # 布林带位置
                df[f'{prefix}_Position'] = (df['close'] - lower) / (upper - lower)
                self.feature_columns.append(f'{prefix}_Position')

                # 布林带宽度
                df[f'{prefix}_Width'] = (upper - lower) / middle
                self.feature_columns.append(f'{prefix}_Width')

                # 布林带挤压
                df[f'{prefix}_Squeeze'] = (df[f'{prefix}_Width'] < df[f'{prefix}_Width'].rolling(20).mean()).astype(int)
                self.feature_columns.append(f'{prefix}_Squeeze')

            return df

        except Exception as e:
            self.logger.error(f"添加布林带指标失败: {e}")
            return df

    def _add_stochastic_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加随机指标"""
        try:
            stoch_params = self.feature_config['stochastic']

            for k_period, d_period, smooth_k in stoch_params:
                # 随机指标
                slowk, slowd = talib.STOCH(
                    df['high'],
                    df['low'],
                    df['close'],
                    fastk_period=k_period,
                    slowk_period=smooth_k,
                    slowd_period=d_period
                )

                prefix = f'STOCH_{k_period}_{d_period}_{smooth_k}'

                df[f'{prefix}_K'] = slowk
                df[f'{prefix}_D'] = slowd

                self.feature_columns.extend([
                    f'{prefix}_K',
                    f'{prefix}_D'
                ])

                # 随机指标信号
                df[f'{prefix}_Overbought'] = (slowk > 80).astype(int)
                df[f'{prefix}_Oversold'] = (slowk < 20).astype(int)
                df[f'{prefix}_Bullish_Cross'] = ((slowk > slowd) & (slowk.shift(1) <= slowd.shift(1))).astype(int)
                df[f'{prefix}_Bearish_Cross'] = ((slowk < slowd) & (slowk.shift(1) >= slowd.shift(1))).astype(int)

                self.feature_columns.extend([
                    f'{prefix}_Overbought',
                    f'{prefix}_Oversold',
                    f'{prefix}_Bullish_Cross',
                    f'{prefix}_Bearish_Cross'
                ])

            return df

        except Exception as e:
            self.logger.error(f"添加随机指标失败: {e}")
            return df

    def _add_atr_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加ATR指标"""
        try:
            periods = self.feature_config['atr_periods']

            for period in periods:
                # ATR
                col_name = f'ATR_{period}'
                df[col_name] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=period)
                self.feature_columns.append(col_name)

                # ATR相对值
                col_name = f'ATR_{period}_Ratio'
                df[col_name] = df[f'ATR_{period}'] / df['close']
                self.feature_columns.append(col_name)

            return df

        except Exception as e:
            self.logger.error(f"添加ATR指标失败: {e}")
            return df

    def _add_adx_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加ADX指标"""
        try:
            period = self.feature_config['adx_period']

            # ADX
            adx = talib.ADX(df['high'], df['low'], df['close'], timeperiod=period)
            df[f'ADX_{period}'] = adx
            self.feature_columns.append(f'ADX_{period}')

            # +DI和-DI
            plus_di = talib.PLUS_DI(df['high'], df['low'], df['close'], timeperiod=period)
            minus_di = talib.MINUS_DI(df['high'], df['low'], df['close'], timeperiod=period)

            df[f'PLUS_DI_{period}'] = plus_di
            df[f'MINUS_DI_{period}'] = minus_di

            self.feature_columns.extend([
                f'PLUS_DI_{period}',
                f'MINUS_DI_{period}'
            ])

            # 趋势强度
            df[f'ADX_{period}_Strong_Trend'] = (adx > 25).astype(int)
            df[f'ADX_{period}_Weak_Trend'] = (adx < 20).astype(int)

            self.feature_columns.extend([
                f'ADX_{period}_Strong_Trend',
                f'ADX_{period}_Weak_Trend'
            ])

            return df

        except Exception as e:
            self.logger.error(f"添加ADX指标失败: {e}")
            return df

    def _add_cci_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加CCI指标"""
        try:
            period = self.feature_config['cci_period']

            # CCI
            cci = talib.CCI(df['high'], df['low'], df['close'], timeperiod=period)
            df[f'CCI_{period}'] = cci
            self.feature_columns.append(f'CCI_{period}')

            # CCI信号
            df[f'CCI_{period}_Overbought'] = (cci > 100).astype(int)
            df[f'CCI_{period}_Oversold'] = (cci < -100).astype(int)

            self.feature_columns.extend([
                f'CCI_{period}_Overbought',
                f'CCI_{period}_Oversold'
            ])

            return df

        except Exception as e:
            self.logger.error(f"添加CCI指标失败: {e}")
            return df

    def _add_williams_r_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加Williams %R指标"""
        try:
            period = self.feature_config['williams_r_period']

            # Williams %R
            willr = talib.WILLR(df['high'], df['low'], df['close'], timeperiod=period)
            df[f'WILLR_{period}'] = willr
            self.feature_columns.append(f'WILLR_{period}')

            # Williams %R信号
            df[f'WILLR_{period}_Overbought'] = (willr > -20).astype(int)
            df[f'WILLR_{period}_Oversold'] = (willr < -80).astype(int)

            self.feature_columns.extend([
                f'WILLR_{period}_Overbought',
                f'WILLR_{period}_Oversold'
            ])

            return df

        except Exception as e:
            self.logger.error(f"添加Williams %R指标失败: {e}")
            return df

    def _add_momentum_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加动量指标"""
        try:
            # 动量
            periods = [10, 14, 20]
            for period in periods:
                df[f'MOM_{period}'] = talib.MOM(df['close'], timeperiod=period)
                self.feature_columns.append(f'MOM_{period}')

            # 变化率
            for period in periods:
                df[f'ROC_{period}'] = talib.ROC(df['close'], timeperiod=period)
                self.feature_columns.append(f'ROC_{period}')

            return df

        except Exception as e:
            self.logger.error(f"添加动量指标失败: {e}")
            return df

    def _add_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加成交量指标"""
        try:
            if 'volume' not in df.columns:
                return df

            # 成交量移动平均
            periods = [10, 20, 50]
            for period in periods:
                df[f'Volume_SMA_{period}'] = talib.SMA(df['volume'], timeperiod=period)
                self.feature_columns.append(f'Volume_SMA_{period}')

                # 成交量比率
                df[f'Volume_Ratio_{period}'] = df['volume'] / df[f'Volume_SMA_{period}']
                self.feature_columns.append(f'Volume_Ratio_{period}')

            # OBV
            df['OBV'] = talib.OBV(df['close'], df['volume'])
            self.feature_columns.append('OBV')

            # AD Line
            df['AD'] = talib.AD(df['high'], df['low'], df['close'], df['volume'])
            self.feature_columns.append('AD')

            return df

        except Exception as e:
            self.logger.error(f"添加成交量指标失败: {e}")
            return df

    def get_feature_names(self) -> List[str]:
        """获取所有特征名称"""
        return self.feature_columns.copy()

    def get_feature_importance_groups(self) -> Dict[str, List[str]]:
        """获取特征重要性分组"""
        groups = {
            'trend': [col for col in self.feature_columns if any(x in col for x in ['SMA', 'EMA', 'ADX'])],
            'momentum': [col for col in self.feature_columns if any(x in col for x in ['RSI', 'MOM', 'ROC', 'CCI'])],
            'volatility': [col for col in self.feature_columns if any(x in col for x in ['ATR', 'BB'])],
            'oscillators': [col for col in self.feature_columns if any(x in col for x in ['STOCH', 'WILLR'])],
            'volume': [col for col in self.feature_columns if any(x in col for x in ['Volume', 'OBV', 'AD'])],
            'signals': [col for col in self.feature_columns if any(x in col for x in ['Cross', 'Overbought', 'Oversold'])]
        }

        return groups

# 创建全局实例
technical_engine = TechnicalFeatureEngine()
