#!/usr/bin/env python3
"""
带回测验证的模型训练
训练完成后立即进行回测验证，确保模型有效
"""

import sys
import os
import logging
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 使用现有的训练脚本
import subprocess
from model_evaluation.real_backtest_engine import RealBacktestEngine

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def train_and_validate_models():
    """训练并验证模型"""
    logger = setup_logging()

    print("🚀 QuantumForex 带回测验证的模型训练")
    print("="*60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 1. 初始化训练器
        print("\n🤖 初始化高级模型训练器...")
        trainer = AdvancedModelTrainer()

        # 2. 训练所有模型
        print("\n📚 开始训练所有模型类型...")

        model_types = ['price_prediction', 'trend_classification', 'volatility_prediction', 'risk_assessment']
        trained_models = []

        for model_type in model_types:
            print(f"\n{'='*50}")
            print(f"🔧 训练 {model_type} 模型")
            print(f"{'='*50}")

            try:
                # 训练模型
                success = trainer.train_model(model_type)

                if success:
                    print(f"✅ {model_type} 模型训练成功")

                    # 查找训练好的模型文件
                    models_dir = Path("data/models")
                    model_files = list(models_dir.glob(f"*{model_type}*.pkl"))
                    model_files = [f for f in model_files if 'scaler' not in f.name]

                    if model_files:
                        latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
                        trained_models.append({
                            'type': model_type,
                            'path': latest_model,
                            'trained': True
                        })
                        print(f"📦 模型文件: {latest_model.name}")
                    else:
                        print(f"⚠️ 未找到 {model_type} 模型文件")
                else:
                    print(f"❌ {model_type} 模型训练失败")
                    trained_models.append({
                        'type': model_type,
                        'path': None,
                        'trained': False
                    })

            except Exception as e:
                print(f"❌ {model_type} 训练异常: {e}")
                logger.error(f"{model_type} 训练失败: {e}")
                trained_models.append({
                    'type': model_type,
                    'path': None,
                    'trained': False
                })

        # 3. 回测验证
        print(f"\n{'='*60}")
        print("🧪 开始回测验证")
        print(f"{'='*60}")

        backtest_engine = RealBacktestEngine()
        validated_models = []

        for model_info in trained_models:
            if not model_info['trained'] or model_info['path'] is None:
                print(f"⏭️ 跳过 {model_info['type']} (训练失败)")
                continue

            print(f"\n🔍 回测验证: {model_info['type']}")
            print("-" * 40)

            try:
                # 运行回测
                metrics = backtest_engine.run_comprehensive_backtest(
                    model_path=str(model_info['path']),
                    data_days=7  # 使用7天数据快速验证
                )

                # 评估效果
                effectiveness_score = evaluate_model_effectiveness(metrics)

                print(f"📊 回测结果:")
                print(f"   总交易: {metrics.total_trades}")
                print(f"   胜率: {metrics.win_rate:.1%}")
                print(f"   收益率: {metrics.total_return:.1%}")
                print(f"   最大回撤: {metrics.max_drawdown:.1%}")
                print(f"   预测准确率: {metrics.prediction_accuracy:.1%}")
                print(f"   效果评分: {effectiveness_score:.1%}")

                # 判断是否通过验证
                if effectiveness_score >= 0.4:  # 40%以上认为合格
                    print(f"✅ {model_info['type']} 通过回测验证")
                    model_info['validated'] = True
                    model_info['effectiveness'] = effectiveness_score
                    model_info['metrics'] = metrics
                else:
                    print(f"❌ {model_info['type']} 未通过回测验证")
                    model_info['validated'] = False
                    model_info['effectiveness'] = effectiveness_score

                validated_models.append(model_info)

            except Exception as e:
                print(f"❌ {model_info['type']} 回测失败: {e}")
                logger.error(f"{model_info['type']} 回测失败: {e}")
                model_info['validated'] = False
                validated_models.append(model_info)

        # 4. 生成训练报告
        print(f"\n{'='*60}")
        print("📋 训练和验证报告")
        print(f"{'='*60}")

        successful_models = [m for m in validated_models if m.get('validated', False)]
        failed_models = [m for m in validated_models if not m.get('validated', False)]

        print(f"📈 训练统计:")
        print(f"   总模型数: {len(model_types)}")
        print(f"   训练成功: {len([m for m in trained_models if m['trained']])}")
        print(f"   验证通过: {len(successful_models)}")
        print(f"   验证失败: {len(failed_models)}")

        if successful_models:
            print(f"\n✅ 通过验证的模型:")
            for model in successful_models:
                print(f"   🎯 {model['type']}: {model['effectiveness']:.1%}")

        if failed_models:
            print(f"\n❌ 未通过验证的模型:")
            for model in failed_models:
                reason = "训练失败" if not model['trained'] else "回测不合格"
                print(f"   ⚠️ {model['type']}: {reason}")

        # 5. 部署建议
        print(f"\n💡 部署建议:")
        if len(successful_models) >= 3:
            print("🌟 建议立即部署到Pro系统")
            print("✅ 有足够数量的高质量模型")
        elif len(successful_models) >= 1:
            print("⚠️ 可以谨慎部署部分模型")
            print("📝 建议继续优化其他模型")
        else:
            print("❌ 不建议部署，需要重新训练")
            print("📝 建议检查数据质量和特征工程")

        # 6. 保存报告
        save_training_report(validated_models)

        return len(successful_models) > 0

    except Exception as e:
        logger.error(f"训练验证系统失败: {e}")
        print(f"❌ 训练验证系统失败: {e}")
        return False

def evaluate_model_effectiveness(metrics) -> float:
    """评估模型效果"""
    score = 0.0

    # 盈利能力 (30%)
    if metrics.total_return > 0:
        profit_score = min(0.3, metrics.total_return * 3)  # 每1%收益得3%分数
        score += profit_score

    # 胜率 (25%)
    win_rate_score = metrics.win_rate * 0.25
    score += win_rate_score

    # 预测准确性 (25%)
    accuracy_score = metrics.prediction_accuracy * 0.25
    score += accuracy_score

    # 风险控制 (20%)
    if metrics.max_drawdown < 0.1:  # 回撤小于10%
        risk_score = 0.2 - (metrics.max_drawdown * 2)
        score += max(0, risk_score)

    return min(1.0, score)

def save_training_report(models: list):
    """保存训练报告"""
    try:
        import json

        # 创建报告目录
        reports_dir = Path("logs/training_reports")
        reports_dir.mkdir(parents=True, exist_ok=True)

        # 生成报告文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = reports_dir / f"training_report_{timestamp}.json"

        # 准备报告数据
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'total_models': len(models),
            'successful_models': len([m for m in models if m.get('validated', False)]),
            'models': []
        }

        for model in models:
            model_data = {
                'type': model['type'],
                'trained': model['trained'],
                'validated': model.get('validated', False),
                'effectiveness': model.get('effectiveness', 0.0)
            }

            if 'metrics' in model:
                metrics = model['metrics']
                model_data['metrics'] = {
                    'total_trades': metrics.total_trades,
                    'win_rate': metrics.win_rate,
                    'total_return': metrics.total_return,
                    'max_drawdown': metrics.max_drawdown,
                    'prediction_accuracy': metrics.prediction_accuracy
                }

            report_data['models'].append(model_data)

        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

        print(f"📄 训练报告已保存: {report_file}")

    except Exception as e:
        print(f"⚠️ 保存报告失败: {e}")

if __name__ == "__main__":
    print("🎯 QuantumForex 智能训练验证系统")
    print("🔄 训练 → 回测 → 验证 → 部署")
    print("="*60)

    success = train_and_validate_models()

    if success:
        print("\n🎉 训练验证完成！")
        print("✅ 有模型通过了回测验证")
        print("💡 现在您可以确信模型的真实效果了！")
    else:
        print("\n⚠️ 训练验证需要改进")
        print("💡 建议检查数据质量和训练参数")
