"""
测试错误收集工具

这个脚本测试错误收集工具的功能，验证是否能够正确记录LLM分析和预分析过程中的解析错误和结果
"""
import os
import sys
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append('.')

# 导入需要测试的模块
from app.utils.error_collector import log_pre_analysis_error, log_full_analysis_error, get_error_logs, get_error_log_detail

def print_header(message):
    """打印带有分隔线的标题"""
    print("\n" + "=" * 80)
    print(f" {message} ".center(80, "="))
    print("=" * 80)

def test_log_pre_analysis_error():
    """测试记录预分析错误"""
    print_header("测试记录预分析错误")
    
    # 准备测试数据
    raw_response = """
    ```json
    {
      "needAnalysis": false,
      "confidence": 80,
      "reason": "市场稳定，无显著变化",
      "urgency": "LOW",
      "nextInterval": 15
    }
    ```
    """
    
    parsed_result = {
        'needAnalysis': False,
        'reason': '市场稳定，无显著变化'
    }
    
    error_info = "JSON解析失败: Expecting ',' delimiter"
    
    prompt = "你是一个外汇交易分析助手。请根据以下简要市场数据，判断是否需要进行完整的市场分析..."
    
    market_data = {
        'symbol': 'EURUSD',
        'current_price': '1.1320',
        'price_change': '0.05%',
        'rsi': '55'
    }
    
    # 记录错误
    try:
        log_file_path = log_pre_analysis_error(
            raw_response=raw_response,
            parsed_result=parsed_result,
            error_info=error_info,
            prompt=prompt,
            market_data=market_data
        )
        
        print(f"预分析错误日志已保存到: {log_file_path}")
        
        # 验证日志文件是否存在
        assert os.path.exists(log_file_path), "日志文件应该存在"
        
        # 读取日志文件内容
        with open(log_file_path, 'r', encoding='utf-8') as f:
            log_data = json.load(f)
        
        # 验证日志内容
        assert log_data['analysis_type'] == 'pre_analysis', "分析类型应该是pre_analysis"
        assert log_data['raw_response'] == raw_response, "原始响应应该正确保存"
        assert log_data['error_info'] == error_info, "错误信息应该正确保存"
        assert log_data['additional_data']['prompt'] == prompt, "提示词应该正确保存"
        assert log_data['additional_data']['market_data'] == market_data, "市场数据应该正确保存"
        
        print("预分析错误日志内容验证通过")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_log_full_analysis_error():
    """测试记录完整分析错误"""
    print_header("测试记录完整分析错误")
    
    # 准备测试数据
    raw_response = """
    # 外汇交易分析

    ## 市场趋势
    当前EURUSD处于上升趋势，价格位于1.1320，高于13日均线。

    ## 交易指令
    ```json
    {
      "action": "BUY",
      "orderType": "MARKET",
      "entryPrice": null,
      "stopLoss": 1.1290,
      "takeProfit": 1.1350,
      "lotSize": 0.1,
      "riskLevel": "MEDIUM",
      "reasoning": "价格突破阻力位，MACD金叉，看涨信号明确"
    ```
    """
    
    parsed_result = {
        'action': 'NONE',
        'reason': 'JSON解析失败: 缺少右括号'
    }
    
    error_info = "JSON解析失败: Expecting '}' delimiter"
    
    prompt = "你是一个外汇交易分析师。请根据以下市场数据，进行全面分析并给出交易建议..."
    
    market_data = {
        'symbol': 'EURUSD',
        'current_price': '1.1320',
        'timeframe15m': [{'time': '2025-05-23 12:00:00', 'open': '1.1310', 'high': '1.1325', 'low': '1.1305', 'close': '1.1320'}],
        'indicators': {'rsi': 55, 'macd': {'macdLine': [0.0002], 'signalLine': [0.0001], 'histogram': [0.0001]}}
    }
    
    previous_analysis = {
        'action': 'NONE',
        'reason': '等待更明确的信号'
    }
    
    # 记录错误
    try:
        log_file_path = log_full_analysis_error(
            raw_response=raw_response,
            parsed_result=parsed_result,
            error_info=error_info,
            prompt=prompt,
            market_data=market_data,
            previous_analysis=previous_analysis
        )
        
        print(f"完整分析错误日志已保存到: {log_file_path}")
        
        # 验证日志文件是否存在
        assert os.path.exists(log_file_path), "日志文件应该存在"
        
        # 读取日志文件内容
        with open(log_file_path, 'r', encoding='utf-8') as f:
            log_data = json.load(f)
        
        # 验证日志内容
        assert log_data['analysis_type'] == 'full_analysis', "分析类型应该是full_analysis"
        assert log_data['raw_response'] == raw_response, "原始响应应该正确保存"
        assert log_data['error_info'] == error_info, "错误信息应该正确保存"
        assert log_data['additional_data']['prompt'] == prompt, "提示词应该正确保存"
        assert log_data['additional_data']['market_data'] == market_data, "市场数据应该正确保存"
        assert log_data['additional_data']['previous_analysis'] == previous_analysis, "上一次分析结果应该正确保存"
        
        print("完整分析错误日志内容验证通过")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_get_error_logs():
    """测试获取错误日志列表"""
    print_header("测试获取错误日志列表")
    
    try:
        # 获取所有错误日志
        all_logs = get_error_logs()
        print(f"获取到 {len(all_logs)} 条错误日志")
        
        # 获取预分析错误日志
        pre_analysis_logs = get_error_logs(analysis_type='pre_analysis')
        print(f"获取到 {len(pre_analysis_logs)} 条预分析错误日志")
        
        # 获取完整分析错误日志
        full_analysis_logs = get_error_logs(analysis_type='full_analysis')
        print(f"获取到 {len(full_analysis_logs)} 条完整分析错误日志")
        
        # 验证日志列表
        assert len(all_logs) >= len(pre_analysis_logs) + len(full_analysis_logs), "所有日志数量应该大于等于预分析和完整分析日志数量之和"
        
        print("错误日志列表获取验证通过")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_get_error_log_detail():
    """测试获取错误日志详情"""
    print_header("测试获取错误日志详情")
    
    try:
        # 获取所有错误日志
        all_logs = get_error_logs(limit=1)
        
        if not all_logs:
            print("没有找到错误日志，跳过测试")
            return
        
        # 获取第一条日志的详情
        log_id = all_logs[0]['log_id']
        log_detail = get_error_log_detail(log_id)
        
        print(f"获取到日志 {log_id} 的详情")
        
        # 验证日志详情
        assert log_detail is not None, "应该能够获取到日志详情"
        assert log_detail['log_id'] == log_id, "日志ID应该匹配"
        assert 'raw_response' in log_detail, "日志详情应该包含原始响应"
        assert 'parsed_result' in log_detail, "日志详情应该包含解析结果"
        
        print("错误日志详情获取验证通过")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试开始')
    test_log_pre_analysis_error()
    test_log_full_analysis_error()
    test_get_error_logs()
    test_get_error_log_detail()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试结束')
