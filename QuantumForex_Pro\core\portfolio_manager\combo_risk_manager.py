#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
组合交易风险管理器
专门处理组合交易的风险控制和持仓管理
将组合作为一个整体进行风险评估和控制
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .combo_trading_manager import ComboTrade, ComboType

class ComboRiskLevel(Enum):
    """组合风险等级"""
    SAFE = "safe"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class ComboAction(Enum):
    """组合操作建议"""
    HOLD = "hold"                    # 继续持有
    PARTIAL_CLOSE = "partial_close"  # 部分平仓
    FULL_CLOSE = "full_close"        # 全部平仓
    ADJUST_SL = "adjust_sl"          # 调整止损
    ADJUST_TP = "adjust_tp"          # 调整止盈
    ADD_HEDGE = "add_hedge"          # 增加对冲
    REDUCE_SIZE = "reduce_size"      # 减少仓位

@dataclass
class ComboPosition:
    """组合持仓信息"""
    combo_id: str
    combo_type: ComboType
    symbols: List[str]
    order_ids: List[str]
    directions: List[str]
    volumes: List[float]
    entry_prices: List[float]
    current_prices: List[float]
    profits: List[float]
    total_profit: float
    total_volume: float
    open_time: datetime
    duration_hours: float
    usd_exposure: float
    risk_exposure: float

@dataclass
class ComboRiskMetrics:
    """组合风险指标"""
    combo_id: str
    risk_level: ComboRiskLevel
    risk_score: float
    total_profit: float
    total_loss_potential: float
    usd_exposure: float
    correlation_risk: float
    time_risk: float
    volatility_risk: float
    recommended_action: ComboAction
    action_priority: int
    reason: str

class ComboRiskManager:
    """组合交易风险管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 风险阈值配置
        self.risk_thresholds = {
            'max_combo_loss': -30.0,        # 单个组合最大亏损$30
            'max_combo_exposure': 0.10,     # 单个组合最大敞口10%
            'max_total_exposure': 0.25,     # 总敞口25%
            'max_holding_hours': 24,        # 最大持有时间24小时
            'profit_protection_ratio': 0.5, # 利润保护比例50%
            'correlation_limit': 0.8,       # 相关性限制
            'volatility_multiplier': 2.0    # 波动率倍数
        }
        
        # 组合持仓跟踪
        self.active_combos: Dict[str, ComboPosition] = {}
        self.combo_history: List[ComboPosition] = []
        
    def register_combo(self, combo_trade: ComboTrade, order_ids: List[str], 
                      entry_prices: List[float]) -> bool:
        """注册新的组合交易"""
        try:
            combo_position = ComboPosition(
                combo_id=combo_trade.combo_id,
                combo_type=combo_trade.combo_type,
                symbols=combo_trade.symbols,
                order_ids=order_ids,
                directions=combo_trade.directions,
                volumes=combo_trade.position_sizes,
                entry_prices=entry_prices,
                current_prices=entry_prices.copy(),  # 初始为入场价
                profits=[0.0] * len(combo_trade.symbols),
                total_profit=0.0,
                total_volume=sum(combo_trade.position_sizes),
                open_time=datetime.now(),
                duration_hours=0.0,
                usd_exposure=self._calculate_combo_usd_exposure(combo_trade),
                risk_exposure=sum(combo_trade.position_sizes) * 0.02  # 假设2%风险
            )
            
            self.active_combos[combo_trade.combo_id] = combo_position
            self.logger.info(f"✅ 注册组合交易: {combo_trade.combo_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 注册组合交易失败: {e}")
            return False
    
    def update_combo_positions(self, mt4_positions: List[Dict]) -> None:
        """更新组合持仓信息"""
        try:
            # 创建订单ID到MT4持仓的映射
            mt4_position_map = {str(pos['order_id']): pos for pos in mt4_positions}
            
            for combo_id, combo_pos in self.active_combos.items():
                updated = False
                total_profit = 0.0
                
                for i, order_id in enumerate(combo_pos.order_ids):
                    if order_id in mt4_position_map:
                        mt4_pos = mt4_position_map[order_id]
                        combo_pos.current_prices[i] = mt4_pos.get('current_price', combo_pos.entry_prices[i])
                        combo_pos.profits[i] = mt4_pos.get('profit', 0.0)
                        total_profit += combo_pos.profits[i]
                        updated = True
                
                if updated:
                    combo_pos.total_profit = total_profit
                    combo_pos.duration_hours = (datetime.now() - combo_pos.open_time).total_seconds() / 3600
                    
        except Exception as e:
            self.logger.error(f"❌ 更新组合持仓失败: {e}")
    
    def assess_combo_risks(self) -> List[ComboRiskMetrics]:
        """评估所有组合的风险"""
        try:
            risk_assessments = []
            
            for combo_id, combo_pos in self.active_combos.items():
                risk_metrics = self._assess_single_combo_risk(combo_pos)
                risk_assessments.append(risk_metrics)
            
            # 按优先级排序
            risk_assessments.sort(key=lambda x: x.action_priority, reverse=True)
            
            return risk_assessments
            
        except Exception as e:
            self.logger.error(f"❌ 组合风险评估失败: {e}")
            return []
    
    def _assess_single_combo_risk(self, combo_pos: ComboPosition) -> ComboRiskMetrics:
        """评估单个组合的风险"""
        try:
            # 1. 亏损风险评估
            loss_risk = self._assess_loss_risk(combo_pos)
            
            # 2. 时间风险评估
            time_risk = self._assess_time_risk(combo_pos)
            
            # 3. 敞口风险评估
            exposure_risk = self._assess_exposure_risk(combo_pos)
            
            # 4. 波动率风险评估
            volatility_risk = self._assess_volatility_risk(combo_pos)
            
            # 5. 综合风险评分
            risk_score = (loss_risk * 0.4 + time_risk * 0.2 + 
                         exposure_risk * 0.3 + volatility_risk * 0.1)
            
            # 6. 确定风险等级
            risk_level = self._determine_risk_level(risk_score)
            
            # 7. 生成操作建议
            action, priority, reason = self._generate_combo_action(combo_pos, risk_score, risk_level)
            
            return ComboRiskMetrics(
                combo_id=combo_pos.combo_id,
                risk_level=risk_level,
                risk_score=risk_score,
                total_profit=combo_pos.total_profit,
                total_loss_potential=self._calculate_loss_potential(combo_pos),
                usd_exposure=combo_pos.usd_exposure,
                correlation_risk=0.0,  # 待实现
                time_risk=time_risk,
                volatility_risk=volatility_risk,
                recommended_action=action,
                action_priority=priority,
                reason=reason
            )
            
        except Exception as e:
            self.logger.error(f"❌ 单个组合风险评估失败: {e}")
            return ComboRiskMetrics(
                combo_id=combo_pos.combo_id,
                risk_level=ComboRiskLevel.MEDIUM,
                risk_score=0.5,
                total_profit=combo_pos.total_profit,
                total_loss_potential=0.0,
                usd_exposure=combo_pos.usd_exposure,
                correlation_risk=0.0,
                time_risk=0.0,
                volatility_risk=0.0,
                recommended_action=ComboAction.HOLD,
                action_priority=1,
                reason="风险评估异常"
            )
    
    def _assess_loss_risk(self, combo_pos: ComboPosition) -> float:
        """评估亏损风险"""
        if combo_pos.total_profit >= 0:
            return 0.0  # 盈利状态无亏损风险
        
        loss_ratio = abs(combo_pos.total_profit) / abs(self.risk_thresholds['max_combo_loss'])
        return min(loss_ratio, 1.0)
    
    def _assess_time_risk(self, combo_pos: ComboPosition) -> float:
        """评估时间风险"""
        time_ratio = combo_pos.duration_hours / self.risk_thresholds['max_holding_hours']
        return min(time_ratio, 1.0)
    
    def _assess_exposure_risk(self, combo_pos: ComboPosition) -> float:
        """评估敞口风险"""
        exposure_ratio = abs(combo_pos.usd_exposure) / self.risk_thresholds['max_combo_exposure']
        return min(exposure_ratio, 1.0)
    
    def _assess_volatility_risk(self, combo_pos: ComboPosition) -> float:
        """评估波动率风险"""
        # 简化实现，基于价格变动
        total_price_change = 0.0
        for i in range(len(combo_pos.symbols)):
            if combo_pos.entry_prices[i] > 0:
                price_change = abs(combo_pos.current_prices[i] - combo_pos.entry_prices[i]) / combo_pos.entry_prices[i]
                total_price_change += price_change
        
        avg_price_change = total_price_change / len(combo_pos.symbols) if combo_pos.symbols else 0.0
        return min(avg_price_change * 10, 1.0)  # 放大10倍作为风险指标
    
    def _determine_risk_level(self, risk_score: float) -> ComboRiskLevel:
        """确定风险等级"""
        if risk_score >= 0.9:
            return ComboRiskLevel.EMERGENCY
        elif risk_score >= 0.7:
            return ComboRiskLevel.CRITICAL
        elif risk_score >= 0.5:
            return ComboRiskLevel.HIGH
        elif risk_score >= 0.3:
            return ComboRiskLevel.MEDIUM
        elif risk_score >= 0.1:
            return ComboRiskLevel.LOW
        else:
            return ComboRiskLevel.SAFE
    
    def _generate_combo_action(self, combo_pos: ComboPosition, risk_score: float, 
                              risk_level: ComboRiskLevel) -> Tuple[ComboAction, int, str]:
        """生成组合操作建议"""
        
        # 紧急情况：立即平仓
        if risk_level == ComboRiskLevel.EMERGENCY:
            return ComboAction.FULL_CLOSE, 10, f"紧急风险，立即平仓 (风险评分: {risk_score:.2f})"
        
        # 严重亏损：全部平仓
        if combo_pos.total_profit <= self.risk_thresholds['max_combo_loss']:
            return ComboAction.FULL_CLOSE, 9, f"亏损超限${combo_pos.total_profit:.2f}，止损平仓"
        
        # 持有时间过长：部分平仓
        if combo_pos.duration_hours >= self.risk_thresholds['max_holding_hours']:
            return ComboAction.PARTIAL_CLOSE, 8, f"持有{combo_pos.duration_hours:.1f}小时过长，部分平仓"
        
        # 高风险：调整止损
        if risk_level == ComboRiskLevel.CRITICAL:
            return ComboAction.ADJUST_SL, 7, f"高风险状态，收紧止损"
        
        # 中等风险：监控
        if risk_level == ComboRiskLevel.HIGH:
            return ComboAction.HOLD, 5, f"中等风险，密切监控"
        
        # 盈利保护
        if combo_pos.total_profit > 10.0:  # 盈利超过$10
            protection_threshold = combo_pos.total_profit * self.risk_thresholds['profit_protection_ratio']
            return ComboAction.ADJUST_SL, 6, f"盈利${combo_pos.total_profit:.2f}，设置保护止损"
        
        # 正常状态
        return ComboAction.HOLD, 1, "组合状态正常"
    
    def _calculate_loss_potential(self, combo_pos: ComboPosition) -> float:
        """计算潜在亏损"""
        # 简化计算：假设最大亏损为当前风险敞口的2倍
        return combo_pos.risk_exposure * 2.0
    
    def _calculate_combo_usd_exposure(self, combo_trade: ComboTrade) -> float:
        """计算组合的USD敞口"""
        total_exposure = 0.0
        
        for i, symbol in enumerate(combo_trade.symbols):
            direction = combo_trade.directions[i]
            volume = combo_trade.position_sizes[i]
            
            if symbol.endswith('USD'):
                # USD是报价货币
                exposure = -volume if direction == 'long' else volume
            elif symbol.startswith('USD'):
                # USD是基础货币
                exposure = volume if direction == 'long' else -volume
            else:
                exposure = 0.0
            
            total_exposure += exposure
        
        return total_exposure
    
    def remove_combo(self, combo_id: str) -> bool:
        """移除已平仓的组合"""
        try:
            if combo_id in self.active_combos:
                combo_pos = self.active_combos.pop(combo_id)
                self.combo_history.append(combo_pos)
                self.logger.info(f"✅ 移除组合: {combo_id}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"❌ 移除组合失败: {e}")
            return False
    
    def get_combo_summary(self) -> Dict:
        """获取组合交易摘要"""
        try:
            total_combos = len(self.active_combos)
            total_profit = sum(combo.total_profit for combo in self.active_combos.values())
            total_exposure = sum(abs(combo.usd_exposure) for combo in self.active_combos.values())
            
            return {
                'active_combos': total_combos,
                'total_profit': total_profit,
                'total_usd_exposure': total_exposure,
                'avg_profit_per_combo': total_profit / total_combos if total_combos > 0 else 0.0,
                'risk_status': 'NORMAL' if total_exposure < self.risk_thresholds['max_total_exposure'] else 'HIGH'
            }
        except Exception as e:
            self.logger.error(f"❌ 获取组合摘要失败: {e}")
            return {}

# 创建全局实例
combo_risk_manager = ComboRiskManager()
