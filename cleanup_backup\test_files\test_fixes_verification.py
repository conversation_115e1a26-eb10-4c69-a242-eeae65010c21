#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复结果
测试所有修复的接口问题
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_all_fixes():
    """测试所有修复"""
    print("🔧 验证修复结果")
    print("=" * 60)

    test_results = []

    # 1. 测试市场自适应系统修复
    print("📊 测试1：市场自适应系统")
    try:
        from app.core.market_adaptive_system import MarketAdaptiveSystem
        market_adaptive = MarketAdaptiveSystem()

        market_data = {
            'current_price': 1.13550,
            'rsi': 65,
            'macd_line': 0.0012,
            'signal_line': 0.0008,
            'atr': 0.0015,
            'volume': 1500,
            'ma_20': 1.1340,
            'ma_50': 1.1320
        }

        # 测试新的兼容方法
        market_analysis = market_adaptive.analyze_market_regime(market_data)

        print(f"   ✅ 市场自适应系统修复成功")
        print(f"     市场制度: {market_analysis.market_regime.value}")
        print(f"     推荐策略: {market_analysis.recommended_strategy.value}")
        test_results.append(('市场自适应系统', 'PASS', '方法名兼容修复成功'))

    except Exception as e:
        print(f"   ❌ 市场自适应系统修复失败: {e}")
        test_results.append(('市场自适应系统', 'FAIL', str(e)))

    # 2. 测试反馈学习系统修复
    print("\n🧠 测试2：反馈学习系统")
    try:
        from app.core.feedback_learning_system import FeedbackLearningSystem, TradeRecord
        feedback_system = FeedbackLearningSystem()

        # 测试新的兼容类
        trade_record = TradeRecord(
            trade_id='TEST_001',
            timestamp=datetime.now(),
            symbol='EURUSD',
            action='BUY',
            entry_price=1.13550,
            exit_price=1.13650,
            lot_size=0.1,
            profit_loss=10.0,
            profit_loss_pct=0.088,
            duration_minutes=30,
            predicted_direction='BUY',
            actual_direction='BUY',
            market_regime='TRENDING_UP',
            signal_quality='B+',
            risk_level='MEDIUM'
        )

        feedback_system.add_trade_record(trade_record)
        analysis = feedback_system.analyze_prediction_accuracy()

        print(f"   ✅ 反馈学习系统修复成功")
        print(f"     方向准确率: {analysis.get('direction_accuracy', 0):.2%}")
        test_results.append(('反馈学习系统', 'PASS', '类名兼容修复成功'))

    except Exception as e:
        print(f"   ❌ 反馈学习系统修复失败: {e}")
        test_results.append(('反馈学习系统', 'FAIL', str(e)))

    # 3. 测试组合管理系统修复
    print("\n💼 测试3：组合管理系统")
    try:
        from app.core.portfolio_management_system import PortfolioManager
        portfolio_manager = PortfolioManager()

        # 模拟持仓数据 - 使用正确的PositionInfo格式
        from app.core.portfolio_management_system import PositionInfo

        positions = {
            'EURUSD': PositionInfo(
                symbol='EURUSD',
                action='BUY',
                lot_size=0.1,
                entry_price=1.13550,
                current_price=1.13650,
                unrealized_pnl=100.0,
                unrealized_pnl_pct=0.088,
                position_value=11365.0,
                margin_used=1136.5,
                days_held=1,
                stop_loss=1.13450,
                take_profit=1.13750
            ),
            'GBPUSD': PositionInfo(
                symbol='GBPUSD',
                action='BUY',
                lot_size=0.05,
                entry_price=1.27800,
                current_price=1.27850,
                unrealized_pnl=25.0,
                unrealized_pnl_pct=0.039,
                position_value=6392.5,
                margin_used=639.25,
                days_held=2,
                stop_loss=1.27700,
                take_profit=1.28000
            )
        }

        portfolio_manager.current_positions = positions
        portfolio_analysis = portfolio_manager.analyze_portfolio_risk()

        print(f"   ✅ 组合管理系统修复成功")
        print(f"     分散化比率: {portfolio_analysis.diversification_ratio:.2f}")
        test_results.append(('组合管理系统', 'PASS', '类名别名修复成功'))

    except Exception as e:
        print(f"   ❌ 组合管理系统修复失败: {e}")
        test_results.append(('组合管理系统', 'FAIL', str(e)))

    # 4. 测试数据处理模块修复
    print("\n📈 测试4：数据处理模块")
    try:
        from app.utils import forex_data_processor

        # 创建模拟K线数据
        mock_klines = []
        base_price = 1.13550
        for i in range(100):
            mock_klines.append({
                'timestamp': (datetime.now()).isoformat(),
                'open': base_price + (i % 10 - 5) * 0.0001,
                'high': base_price + (i % 10 - 3) * 0.0001,
                'low': base_price + (i % 10 - 7) * 0.0001,
                'close': base_price + (i % 10 - 4) * 0.0001,
                'volume': 1000 + i * 10
            })

        # 测试新的兼容方法
        indicators = forex_data_processor.calculate_technical_indicators(mock_klines, 'M15')

        print(f"   ✅ 数据处理模块修复成功")
        print(f"     计算指标数: {len(indicators)}")
        print(f"     RSI: {indicators.get('rsi', 'N/A')}")
        print(f"     MA20: {indicators.get('ma_20', 'N/A')}")
        test_results.append(('数据处理模块', 'PASS', '方法兼容修复成功'))

    except Exception as e:
        print(f"   ❌ 数据处理模块修复失败: {e}")
        test_results.append(('数据处理模块', 'FAIL', str(e)))

    # 5. 测试风险管理系统修复
    print("\n🛡️ 测试5：风险管理系统")
    try:
        from app.core.risk_management import AdvancedRiskManager
        risk_manager = AdvancedRiskManager()

        # 模拟账户信息和市场数据
        account_info = {'balance': 10000, 'equity': 9900}
        positions = []
        market_data = {
            'current_price': 1.13550,
            'atr': 0.0015,
            'spread': 2
        }

        risk_metrics = risk_manager.assess_comprehensive_risk(
            account_info, positions, market_data
        )

        print(f"   ✅ 风险管理系统修复成功")
        print(f"     风险等级: {risk_metrics.risk_level.value}")
        print(f"     风险评分: {risk_metrics.account_drawdown:.3f}")
        print(f"     日盈亏: {risk_metrics.daily_pnl:.2f}")
        print(f"     周盈亏: {risk_metrics.weekly_pnl:.2f}")
        test_results.append(('风险管理系统', 'PASS', '属性访问修复成功'))

    except Exception as e:
        print(f"   ❌ 风险管理系统修复失败: {e}")
        test_results.append(('风险管理系统', 'FAIL', str(e)))

    # 6. 综合测试
    print("\n🔄 测试6：综合功能测试")
    try:
        # 测试所有系统协同工作

        # 数据处理
        indicators = forex_data_processor.calculate_technical_indicators(mock_klines, 'M15')

        # 市场分析
        market_analysis = market_adaptive.analyze_market_regime(market_data)

        # 风险评估
        risk_metrics = risk_manager.assess_comprehensive_risk(
            account_info, positions, market_data
        )

        # 组合分析 - 确保使用正确的持仓数据
        portfolio_manager.current_positions = positions  # 使用之前定义的正确格式
        portfolio_analysis = portfolio_manager.analyze_portfolio_risk()

        print(f"   ✅ 综合功能测试成功")
        print(f"     所有系统协同工作正常")
        print(f"     数据流: 数据处理 → 市场分析 → 风险评估 → 组合管理")
        test_results.append(('综合功能测试', 'PASS', '所有系统协同工作'))

    except Exception as e:
        print(f"   ❌ 综合功能测试失败: {e}")
        test_results.append(('综合功能测试', 'FAIL', str(e)))

    # 统计结果
    total_tests = len(test_results)
    passed_tests = len([r for r in test_results if r[1] == 'PASS'])
    failed_tests = len([r for r in test_results if r[1] == 'FAIL'])

    print(f"\n📊 修复验证结果")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"修复成功: {passed_tests}")
    print(f"修复失败: {failed_tests}")
    print(f"修复成功率: {passed_tests/total_tests*100:.1f}%")

    print(f"\n📋 详细结果:")
    for test_name, status, details in test_results:
        status_icon = "✅" if status == "PASS" else "❌"
        print(f"   {status_icon} {test_name}: {status} - {details}")

    if passed_tests == total_tests:
        print(f"\n🎉 所有修复验证成功！")
        print("   ✅ 市场自适应系统 - 方法名兼容修复")
        print("   ✅ 反馈学习系统 - 类名兼容修复")
        print("   ✅ 组合管理系统 - 类名别名修复")
        print("   ✅ 数据处理模块 - 方法兼容修复")
        print("   ✅ 风险管理系统 - 属性访问修复")
        print("   ✅ 综合功能测试 - 系统协同工作")

        print(f"\n🚀 修复总结:")
        print("   这些问题都不是因为MT4连接问题导致的，")
        print("   而是代码接口不一致的技术问题。")
        print("   现在所有问题都已修复，系统功能完整！")

        return True
    else:
        print(f"\n⚠️ 部分修复需要进一步处理")
        return False

if __name__ == "__main__":
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始验证修复结果")

    success = test_all_fixes()

    if success:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🎉 所有修复验证成功！")
        print("系统现在完全正常工作，所有接口问题都已解决！")
    else:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ⚠️ 部分修复需要进一步处理")
