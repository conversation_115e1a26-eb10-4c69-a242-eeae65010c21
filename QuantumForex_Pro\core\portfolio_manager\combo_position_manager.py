#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
组合持仓管理器
专门管理组合交易的持仓，将组合作为一个整体进行操作
不再对单个订单进行独立的风控操作
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from .combo_risk_manager import ComboRiskManager, ComboRiskMetrics, ComboAction
from .combo_trading_manager import ComboTrade

@dataclass
class ComboExecutionResult:
    """组合执行结果"""
    combo_id: str
    action: str
    success: bool
    executed_orders: List[str]
    failed_orders: List[str]
    reason: str
    total_profit_before: float
    total_profit_after: float

class ComboPositionManager:
    """组合持仓管理器"""
    
    def __init__(self, trade_executor, combo_risk_manager: ComboRiskManager):
        self.logger = logging.getLogger(__name__)
        self.trade_executor = trade_executor
        self.combo_risk_manager = combo_risk_manager
        
        # 组合操作历史
        self.execution_history: List[ComboExecutionResult] = []
        
        # 组合操作配置
        self.execution_config = {
            'partial_close_ratio': 0.5,     # 部分平仓比例
            'sl_adjustment_pips': 10,        # 止损调整点数
            'tp_adjustment_pips': 15,        # 止盈调整点数
            'max_concurrent_actions': 3,    # 最大并发操作数
            'action_cooldown_minutes': 5    # 操作冷却时间
        }
    
    def analyze_combo_positions(self) -> List[ComboRiskMetrics]:
        """分析所有组合持仓"""
        try:
            self.logger.info("🔍 开始组合持仓分析...")
            
            # 获取MT4真实持仓
            mt4_positions = self.trade_executor._get_mt4_real_positions()
            
            # 更新组合持仓信息
            self.combo_risk_manager.update_combo_positions(mt4_positions)
            
            # 评估组合风险
            risk_assessments = self.combo_risk_manager.assess_combo_risks()
            
            self.logger.info(f"✅ 完成{len(risk_assessments)}个组合分析")
            return risk_assessments
            
        except Exception as e:
            self.logger.error(f"❌ 组合持仓分析失败: {e}")
            return []
    
    def execute_combo_actions(self, risk_assessments: List[ComboRiskMetrics]) -> Dict:
        """执行组合操作建议"""
        try:
            self.logger.info("🎯 开始执行组合操作...")
            
            executed_count = 0
            failed_count = 0
            results = []
            
            # 按优先级执行操作
            for assessment in risk_assessments:
                if executed_count >= self.execution_config['max_concurrent_actions']:
                    self.logger.info(f"⏸️ 达到最大并发操作数限制: {self.execution_config['max_concurrent_actions']}")
                    break
                
                if assessment.action_priority >= 7:  # 只执行高优先级操作
                    result = self._execute_single_combo_action(assessment)
                    results.append(result)
                    
                    if result.success:
                        executed_count += 1
                    else:
                        failed_count += 1
            
            summary = {
                'total_assessments': len(risk_assessments),
                'executed_actions': executed_count,
                'failed_actions': failed_count,
                'results': results
            }
            
            self.logger.info(f"✅ 组合操作完成: {executed_count}成功, {failed_count}失败")
            return summary
            
        except Exception as e:
            self.logger.error(f"❌ 组合操作执行失败: {e}")
            return {'error': str(e)}
    
    def _execute_single_combo_action(self, assessment: ComboRiskMetrics) -> ComboExecutionResult:
        """执行单个组合操作"""
        try:
            combo_id = assessment.combo_id
            action = assessment.recommended_action
            
            self.logger.info(f"🔄 执行组合操作: {combo_id} - {action.value}")
            
            # 获取组合信息
            combo_pos = self.combo_risk_manager.active_combos.get(combo_id)
            if not combo_pos:
                return ComboExecutionResult(
                    combo_id=combo_id,
                    action=action.value,
                    success=False,
                    executed_orders=[],
                    failed_orders=[],
                    reason="组合不存在",
                    total_profit_before=0.0,
                    total_profit_after=0.0
                )
            
            profit_before = combo_pos.total_profit
            executed_orders = []
            failed_orders = []
            
            # 根据操作类型执行
            if action == ComboAction.FULL_CLOSE:
                executed_orders, failed_orders = self._close_full_combo(combo_pos)
            elif action == ComboAction.PARTIAL_CLOSE:
                executed_orders, failed_orders = self._close_partial_combo(combo_pos)
            elif action == ComboAction.ADJUST_SL:
                executed_orders, failed_orders = self._adjust_combo_stop_loss(combo_pos)
            elif action == ComboAction.ADJUST_TP:
                executed_orders, failed_orders = self._adjust_combo_take_profit(combo_pos)
            else:
                # HOLD 或其他操作，不执行
                pass
            
            # 更新后的利润
            profit_after = combo_pos.total_profit if combo_pos.combo_id in self.combo_risk_manager.active_combos else 0.0
            
            success = len(failed_orders) == 0
            reason = assessment.reason if success else f"部分操作失败: {len(failed_orders)}个订单"
            
            result = ComboExecutionResult(
                combo_id=combo_id,
                action=action.value,
                success=success,
                executed_orders=executed_orders,
                failed_orders=failed_orders,
                reason=reason,
                total_profit_before=profit_before,
                total_profit_after=profit_after
            )
            
            self.execution_history.append(result)
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 单个组合操作失败: {e}")
            return ComboExecutionResult(
                combo_id=assessment.combo_id,
                action=assessment.recommended_action.value,
                success=False,
                executed_orders=[],
                failed_orders=[],
                reason=f"执行异常: {e}",
                total_profit_before=0.0,
                total_profit_after=0.0
            )
    
    def _close_full_combo(self, combo_pos) -> Tuple[List[str], List[str]]:
        """全部平仓组合"""
        executed = []
        failed = []
        
        try:
            self.logger.info(f"🔴 全部平仓组合: {combo_pos.combo_id}")
            
            for order_id in combo_pos.order_ids:
                try:
                    # 平仓单个订单
                    result = self.trade_executor.close_position(order_id)
                    if result and result.get('success', False):
                        executed.append(order_id)
                        self.logger.info(f"✅ 平仓成功: {order_id}")
                    else:
                        failed.append(order_id)
                        self.logger.warning(f"⚠️ 平仓失败: {order_id}")
                except Exception as e:
                    failed.append(order_id)
                    self.logger.error(f"❌ 平仓异常: {order_id} - {e}")
            
            # 如果全部平仓成功，移除组合
            if len(failed) == 0:
                self.combo_risk_manager.remove_combo(combo_pos.combo_id)
                self.logger.info(f"✅ 组合已移除: {combo_pos.combo_id}")
            
        except Exception as e:
            self.logger.error(f"❌ 全部平仓失败: {e}")
        
        return executed, failed
    
    def _close_partial_combo(self, combo_pos) -> Tuple[List[str], List[str]]:
        """部分平仓组合"""
        executed = []
        failed = []
        
        try:
            self.logger.info(f"🟡 部分平仓组合: {combo_pos.combo_id}")
            
            # 选择亏损最大的订单进行平仓
            order_profits = [(i, combo_pos.profits[i]) for i in range(len(combo_pos.order_ids))]
            order_profits.sort(key=lambda x: x[1])  # 按利润排序，亏损最大的在前
            
            # 平仓一半的订单
            close_count = max(1, len(combo_pos.order_ids) // 2)
            
            for i in range(close_count):
                order_index = order_profits[i][0]
                order_id = combo_pos.order_ids[order_index]
                
                try:
                    result = self.trade_executor.close_position(order_id)
                    if result and result.get('success', False):
                        executed.append(order_id)
                        self.logger.info(f"✅ 部分平仓成功: {order_id}")
                    else:
                        failed.append(order_id)
                        self.logger.warning(f"⚠️ 部分平仓失败: {order_id}")
                except Exception as e:
                    failed.append(order_id)
                    self.logger.error(f"❌ 部分平仓异常: {order_id} - {e}")
            
        except Exception as e:
            self.logger.error(f"❌ 部分平仓失败: {e}")
        
        return executed, failed
    
    def _adjust_combo_stop_loss(self, combo_pos) -> Tuple[List[str], List[str]]:
        """调整组合止损"""
        executed = []
        failed = []
        
        try:
            self.logger.info(f"🔧 调整组合止损: {combo_pos.combo_id}")
            
            for i, order_id in enumerate(combo_pos.order_ids):
                try:
                    # 计算新的止损价格（收紧止损）
                    current_price = combo_pos.current_prices[i]
                    direction = combo_pos.directions[i]
                    
                    if direction == 'long':
                        # 多头：提高止损价格
                        new_sl = current_price - (self.execution_config['sl_adjustment_pips'] * 0.0001)
                    else:
                        # 空头：降低止损价格
                        new_sl = current_price + (self.execution_config['sl_adjustment_pips'] * 0.0001)
                    
                    # 修改订单止损
                    result = self.trade_executor.modify_position(order_id, stop_loss=new_sl)
                    if result and result.get('success', False):
                        executed.append(order_id)
                        self.logger.info(f"✅ 止损调整成功: {order_id} -> {new_sl}")
                    else:
                        failed.append(order_id)
                        self.logger.warning(f"⚠️ 止损调整失败: {order_id}")
                        
                except Exception as e:
                    failed.append(order_id)
                    self.logger.error(f"❌ 止损调整异常: {order_id} - {e}")
            
        except Exception as e:
            self.logger.error(f"❌ 组合止损调整失败: {e}")
        
        return executed, failed
    
    def _adjust_combo_take_profit(self, combo_pos) -> Tuple[List[str], List[str]]:
        """调整组合止盈"""
        executed = []
        failed = []
        
        try:
            self.logger.info(f"🎯 调整组合止盈: {combo_pos.combo_id}")
            
            for i, order_id in enumerate(combo_pos.order_ids):
                try:
                    # 计算新的止盈价格
                    current_price = combo_pos.current_prices[i]
                    direction = combo_pos.directions[i]
                    
                    if direction == 'long':
                        # 多头：提高止盈价格
                        new_tp = current_price + (self.execution_config['tp_adjustment_pips'] * 0.0001)
                    else:
                        # 空头：降低止盈价格
                        new_tp = current_price - (self.execution_config['tp_adjustment_pips'] * 0.0001)
                    
                    # 修改订单止盈
                    result = self.trade_executor.modify_position(order_id, take_profit=new_tp)
                    if result and result.get('success', False):
                        executed.append(order_id)
                        self.logger.info(f"✅ 止盈调整成功: {order_id} -> {new_tp}")
                    else:
                        failed.append(order_id)
                        self.logger.warning(f"⚠️ 止盈调整失败: {order_id}")
                        
                except Exception as e:
                    failed.append(order_id)
                    self.logger.error(f"❌ 止盈调整异常: {order_id} - {e}")
            
        except Exception as e:
            self.logger.error(f"❌ 组合止盈调整失败: {e}")
        
        return executed, failed
    
    def get_combo_status_summary(self) -> Dict:
        """获取组合状态摘要"""
        try:
            combo_summary = self.combo_risk_manager.get_combo_summary()
            
            # 添加执行历史统计
            recent_executions = [ex for ex in self.execution_history 
                               if (datetime.now() - datetime.fromisoformat(ex.combo_id.split('_')[-1]) if '_' in ex.combo_id else datetime.now()).days < 1]
            
            combo_summary.update({
                'recent_executions': len(recent_executions),
                'successful_executions': sum(1 for ex in recent_executions if ex.success),
                'failed_executions': sum(1 for ex in recent_executions if not ex.success),
                'last_execution_time': recent_executions[-1].combo_id if recent_executions else None
            })
            
            return combo_summary
            
        except Exception as e:
            self.logger.error(f"❌ 获取组合状态摘要失败: {e}")
            return {}

# 工厂函数
def create_combo_position_manager(trade_executor, combo_risk_manager: ComboRiskManager) -> ComboPositionManager:
    """创建组合持仓管理器实例"""
    return ComboPositionManager(trade_executor, combo_risk_manager)
