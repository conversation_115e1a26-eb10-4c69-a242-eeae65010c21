"""
MT4下单测试脚本
用于测试下单操作
"""
import os
import sys
import time
from app.utils.mt4_client import mt4_client

def test_mt4_place_order():
    """测试MT4下单"""
    try:
        print('开始测试下单...')
        
        # 连接到MT4服务器
        print('连接到MT4服务器...')
        connected = mt4_client.connect()
        print(f'连接结果: {connected}')
        
        if not connected:
            print('连接MT4服务器失败，无法继续测试')
            return
        
        # 获取市场信息
        print('\n获取EURUSD市场信息...')
        market_info = mt4_client.get_market_info('EURUSD')
        print(f'市场信息: {market_info}')
        
        if not market_info or market_info.get('status') != 'success':
            print('获取市场信息失败，无法继续测试')
            return
        
        # 获取当前价格
        current_price = float(market_info['data']['ask'])
        print(f'当前EURUSD价格: {current_price}')
        
        # 设置测试参数
        symbol = 'EURUSD'
        lot = 0.01  # 最小手数，仅用于测试
        stop_loss = round(current_price - 0.0050, 5)  # 低于当前价格50点
        take_profit = round(current_price + 0.0050, 5)  # 高于当前价格50点
        comment = '测试下单'
        
        print(f'测试参数: 货币对={symbol}, 手数={lot}, 止损={stop_loss}, 止盈={take_profit}')
        
        # 测试市价买入
        print('\n测试市价买入...')
        try:
            buy_response = mt4_client.buy(symbol, lot, stop_loss, take_profit, comment)
            print(f'市价买入响应: {buy_response}')
            
            if buy_response and buy_response.get('status') == 'success':
                print('✅ 市价买入成功!')
                
                # 如果成功下单，获取订单ID
                order_id = buy_response.get('order_id')
                if order_id:
                    print(f'订单ID: {order_id}')
                    
                    # 等待一段时间
                    print('等待5秒...')
                    time.sleep(5)
                    
                    # 关闭订单
                    print(f'关闭订单 {order_id}...')
                    close_response = mt4_client.close_order(order_id)
                    print(f'关闭订单响应: {close_response}')
            else:
                print(f'❌ 市价买入失败: {buy_response.get("message") if buy_response else "未知错误"}')
        except Exception as error:
            print(f'❌ 市价买入出错: {error}')
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_mt4_place_order()
