"""
QuantumForex Pro - 交易数据迁移工具
从主项目导入历史交易数据到QuantumForex_Pro的学习系统
解决机器学习引擎数据不足的问题
"""

import sys
import os
import json
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
import uuid

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.learning_system.trade_result_recorder import TradeResultRecorder, TradeRecord

class TradeDataMigrator:
    """交易数据迁移器"""

    def __init__(self):
        # 获取项目根目录
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.main_project_data_path = os.path.join(current_dir, "app", "data", "trade_results.json")
        self.quantum_data_path = os.path.join(current_dir, "QuantumForex_Pro", "data", "trade_results.db")

    def check_data_sources(self):
        """检查数据源状态"""
        print("🔍 检查数据源状态...")

        # 检查主项目数据
        main_data_exists = os.path.exists(self.main_project_data_path)
        main_data_count = 0

        if main_data_exists:
            try:
                with open(self.main_project_data_path, 'r', encoding='utf-8') as f:
                    main_data = json.load(f)
                    main_data_count = len(main_data)
                print(f"✅ 主项目数据: {main_data_count}笔交易记录")
            except Exception as e:
                print(f"❌ 主项目数据读取失败: {e}")
                main_data_count = 0
        else:
            print("❌ 主项目数据文件不存在")

        # 检查QuantumForex_Pro数据
        quantum_data_exists = os.path.exists(self.quantum_data_path)
        quantum_data_count = 0

        if quantum_data_exists:
            try:
                with sqlite3.connect(self.quantum_data_path) as conn:
                    cursor = conn.execute("SELECT COUNT(*) FROM trade_records")
                    quantum_data_count = cursor.fetchone()[0]
                print(f"✅ QuantumForex_Pro数据: {quantum_data_count}笔交易记录")
            except Exception as e:
                print(f"❌ QuantumForex_Pro数据读取失败: {e}")
                quantum_data_count = 0
        else:
            print("⚠️ QuantumForex_Pro数据库不存在，将创建新数据库")

        return {
            'main_data_count': main_data_count,
            'quantum_data_count': quantum_data_count,
            'migration_needed': main_data_count > quantum_data_count
        }

    def migrate_trade_data(self):
        """迁移交易数据"""
        print("🔄 开始迁移交易数据...")

        try:
            # 读取主项目数据
            if not os.path.exists(self.main_project_data_path):
                print("❌ 主项目数据文件不存在")
                return False

            with open(self.main_project_data_path, 'r', encoding='utf-8') as f:
                main_trades = json.load(f)

            print(f"📊 读取到{len(main_trades)}笔主项目交易记录")

            # 初始化QuantumForex_Pro记录器
            recorder = TradeResultRecorder()

            migrated_count = 0
            skipped_count = 0

            for trade in main_trades:
                try:
                    # 转换数据格式
                    converted_trade = self._convert_trade_format(trade)

                    if converted_trade:
                        # 记录到QuantumForex_Pro系统
                        trade_id = recorder.record_trade_entry(converted_trade['entry_data'])

                        if trade_id and converted_trade['exit_data']:
                            # 如果有平仓数据，也记录平仓
                            recorder.record_trade_exit(trade_id, converted_trade['exit_data'])

                        migrated_count += 1
                        print(f"✅ 迁移交易 {migrated_count}: {trade.get('symbol', 'UNKNOWN')} {trade.get('action', 'UNKNOWN')}")
                    else:
                        skipped_count += 1

                except Exception as e:
                    print(f"⚠️ 迁移交易失败: {e}")
                    skipped_count += 1
                    continue

            print(f"\n📊 迁移完成:")
            print(f"   成功迁移: {migrated_count}笔")
            print(f"   跳过: {skipped_count}笔")

            return migrated_count > 0

        except Exception as e:
            print(f"❌ 数据迁移失败: {e}")
            return False

    def _convert_trade_format(self, main_trade):
        """转换交易数据格式"""
        try:
            # 基础数据验证
            if not all(key in main_trade for key in ['symbol', 'action', 'entry_price', 'lot_size']):
                return None

            # 解析开仓时间
            open_time_str = main_trade.get('open_time', '')
            try:
                open_time = datetime.fromisoformat(open_time_str.replace('Z', '+00:00'))
            except:
                open_time = datetime.now() - timedelta(days=1)  # 默认昨天

            # 构建开仓数据
            entry_data = {
                'symbol': main_trade['symbol'],
                'action': main_trade['action'],
                'entry_price': float(main_trade['entry_price']),
                'volume': float(main_trade['lot_size']),
                'stop_loss': float(main_trade.get('stop_loss', main_trade['entry_price'] * 0.99)),
                'take_profit': float(main_trade.get('take_profit', main_trade['entry_price'] * 1.01)),
                'confidence': 0.7,  # 默认置信度
                'strategy_used': 'migrated_from_main_project',
                'market_condition': 'unknown',
                'rsi': 50.0,  # 默认值
                'ma_20': float(main_trade['entry_price']),
                'ma_50': float(main_trade['entry_price']),
                'atr': 0.002,  # 默认ATR
                'volatility': 0.01  # 默认波动率
            }

            # 构建平仓数据（如果已平仓）
            exit_data = None
            if main_trade.get('status') == 'CLOSED' and main_trade.get('close_price'):
                exit_data = {
                    'exit_price': float(main_trade['close_price']),
                    'exit_reason': main_trade.get('close_reason', 'unknown'),
                    'max_favorable_excursion': 0.001,  # 默认值
                    'max_adverse_excursion': 0.001     # 默认值
                }

            return {
                'entry_data': entry_data,
                'exit_data': exit_data
            }

        except Exception as e:
            print(f"⚠️ 数据格式转换失败: {e}")
            return None

    def verify_migration(self):
        """验证迁移结果"""
        print("🔍 验证迁移结果...")

        try:
            # 检查迁移后的数据量
            status = self.check_data_sources()

            print(f"📊 迁移后数据统计:")
            print(f"   主项目: {status['main_data_count']}笔")
            print(f"   QuantumForex_Pro: {status['quantum_data_count']}笔")

            if status['quantum_data_count'] >= 10:
                print("✅ 数据量充足，机器学习引擎可以正常训练")
                return True
            elif status['quantum_data_count'] >= 5:
                print("⚠️ 数据量中等，机器学习引擎可以基础训练")
                return True
            else:
                print("❌ 数据量不足，需要更多交易数据")
                return False

        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 QuantumForex Pro 交易数据迁移工具")
    print("=" * 60)
    print("解决机器学习引擎数据不足问题")
    print("=" * 60)

    migrator = TradeDataMigrator()

    # 1. 检查数据源状态
    status = migrator.check_data_sources()

    print(f"\n📊 数据源状态:")
    print(f"   主项目交易数据: {status['main_data_count']}笔")
    print(f"   QuantumForex_Pro数据: {status['quantum_data_count']}笔")
    print(f"   需要迁移: {'是' if status['migration_needed'] else '否'}")

    if status['migration_needed']:
        print(f"\n🔄 开始数据迁移...")

        # 2. 执行迁移
        success = migrator.migrate_trade_data()

        if success:
            # 3. 验证迁移结果
            migrator.verify_migration()
            print(f"\n✅ 数据迁移完成！机器学习引擎现在有足够的数据进行训练")
        else:
            print(f"\n❌ 数据迁移失败")
    else:
        print(f"\n✅ 无需迁移，数据已经充足")

    print("=" * 60)

if __name__ == "__main__":
    main()
