2025-05-29 21:57:35,698 - __main__ - INFO - 🚀 启动 QuantumForex Pro 云端模型接收服务
2025-05-29 21:57:35,699 - __main__ - INFO - ============================================================
2025-05-29 21:57:35,699 - __main__ - INFO - 🌐 启动API服务器...
2025-05-29 21:57:35,699 - __main__ - INFO - 📍 监听地址: 0.0.0.0:8081
2025-05-29 21:57:35,700 - __main__ - INFO - 🔗 API端点:
2025-05-29 21:57:35,700 - __main__ - INFO -    - 健康检查: http://**************:8081/api/health
2025-05-29 21:57:35,700 - __main__ - INFO -    - 模型状态: http://**************:8081/api/models/status
2025-05-29 21:57:35,700 - __main__ - INFO -    - 模型上传: http://**************:8081/api/models/upload
2025-05-29 21:57:35,701 - __main__ - INFO -    - 模型列表: http://**************:8081/api/models/list
2025-05-29 21:57:35,702 - utils.cloud_model_receiver - INFO - 🚀 启动云端模型接收服务器: 0.0.0.0:8081
2025-05-29 21:57:35,720 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://***************:8081
2025-05-29 21:57:35,721 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 21:58:01,315 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:58:01] "GET /api/health HTTP/1.1" 200 -
2025-05-29 21:58:01,349 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 21:58:01,360 - utils.cloud_model_receiver - ERROR - ❌ 文件校验和不匹配: 1b9aa327f3644c24d0cb50c34c28e2a5 != 1a2e8ffa4047423d0ff3f4cc623ce820
2025-05-29 21:58:01,361 - utils.cloud_model_receiver - ERROR - ❌ 模型上传处理失败: 文件完整性验证失败
2025-05-29 21:58:01,361 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:58:01] "[31m[1mPOST /api/models/upload HTTP/1.1[0m" 400 -
2025-05-29 21:58:01,374 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 21:58:01,377 - utils.cloud_model_receiver - ERROR - ❌ 文件校验和不匹配: 1cf3f1da01c189164fe6f027a9e348e6 != fa1dd021725dc325fb6f273a0c8b035f
2025-05-29 21:58:01,378 - utils.cloud_model_receiver - ERROR - ❌ 模型上传处理失败: 文件完整性验证失败
2025-05-29 21:58:01,378 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:58:01] "[31m[1mPOST /api/models/upload HTTP/1.1[0m" 400 -
2025-05-29 21:59:34,325 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:59:34] "GET /api/health HTTP/1.1" 200 -
2025-05-29 21:59:34,330 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 21:59:34,344 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 21:59:34,346 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\test_price_prediction_model.pkl
2025-05-29 21:59:35,486 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: test_price_prediction_model.pkl
2025-05-29 21:59:35,486 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:59:35] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 21:59:35,513 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 21:59:35,518 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 21:59:35,520 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\test_price_prediction_scaler.pkl
2025-05-29 21:59:35,522 - utils.cloud_model_receiver - ERROR - ❌ 模型缺少predict方法
2025-05-29 21:59:35,523 - utils.cloud_model_receiver - ERROR - ❌ 模型加载测试失败
2025-05-29 21:59:35,523 - utils.cloud_model_receiver - ERROR - ❌ 模型上传处理失败: 模型部署失败
2025-05-29 21:59:35,523 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 21:59:35] "[31m[1mPOST /api/models/upload HTTP/1.1[0m" 400 -
