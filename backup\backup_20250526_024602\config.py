#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外汇交易系统配置管理
支持开发、测试、生产环境的配置
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('.env.local')

class Config:
    """基础配置类"""
    
    # 应用配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    FLASK_ENV = os.environ.get('FLASK_ENV') or 'development'
    
    # 数据库配置
    DB_HOST = os.environ.get('DB_HOST') or 'localhost'
    DB_PORT = int(os.environ.get('DB_PORT') or 3306)
    DB_NAME = os.environ.get('DB_NAME') or 'pizza_quotes'
    DB_USER = os.environ.get('DB_USER') or 'root'
    DB_PASSWORD = os.environ.get('DB_PASSWORD') or ''
    
    # LLM API配置
    LLM_API_URL = os.environ.get('LLM_API_URL') or 'https://api.siliconflow.cn/v1/chat/completions'
    LLM_API_KEY = os.environ.get('LLM_API_KEY') or ''
    LLM_MODEL = os.environ.get('LLM_MODEL') or 'Qwen/Qwen2.5-72B-Instruct'
    
    # MT4连接配置
    MT4_SERVER_HOST = os.environ.get('MT4_SERVER_HOST') or '127.0.0.1'
    MT4_SERVER_PORT = int(os.environ.get('MT4_SERVER_PORT') or 5555)
    MT4_CONNECTION_TIMEOUT = int(os.environ.get('MT4_CONNECTION_TIMEOUT') or 30)
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE_PATH = os.environ.get('LOG_FILE_PATH') or 'logs/forex_system.log'
    ERROR_LOG_PATH = os.environ.get('ERROR_LOG_PATH') or 'logs/error_log.json'
    
    # 安全配置
    ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', 'localhost,127.0.0.1').split(',')
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', 'http://localhost:3000,http://localhost:5000').split(',')
    
    # 性能配置
    MAX_WORKERS = int(os.environ.get('MAX_WORKERS') or 2)
    REQUEST_TIMEOUT = int(os.environ.get('REQUEST_TIMEOUT') or 120)
    ANALYSIS_CACHE_TTL = int(os.environ.get('ANALYSIS_CACHE_TTL') or 300)
    
    # 监控配置
    ENABLE_METRICS = os.environ.get('ENABLE_METRICS', 'true').lower() == 'true'
    METRICS_PORT = int(os.environ.get('METRICS_PORT') or 9090)
    
    # 部署配置
    DEPLOYMENT_ENV = os.environ.get('DEPLOYMENT_ENV') or 'development'
    SERVER_NAME = os.environ.get('SERVER_NAME') or 'forex-trading-system'

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    FLASK_ENV = 'development'
    LOG_LEVEL = 'DEBUG'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    FLASK_ENV = 'testing'
    DB_NAME = 'pizza_quotes_test'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    FLASK_ENV = 'production'
    LOG_LEVEL = 'INFO'
    
    # 生产环境安全检查
    @classmethod
    def validate_production_config(cls):
        """验证生产环境配置"""
        errors = []
        
        if not cls.SECRET_KEY or cls.SECRET_KEY == 'dev-secret-key-change-in-production':
            errors.append("SECRET_KEY must be set for production")
        
        if not cls.LLM_API_KEY:
            errors.append("LLM_API_KEY must be set for production")
        
        if not cls.DB_PASSWORD:
            errors.append("DB_PASSWORD should be set for production")
        
        if errors:
            raise ValueError("Production configuration errors: " + "; ".join(errors))

# 配置映射
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config():
    """获取当前环境配置"""
    env = os.environ.get('FLASK_ENV') or 'development'
    return config.get(env, config['default'])

def validate_config():
    """验证配置"""
    current_config = get_config()
    
    if current_config == ProductionConfig:
        ProductionConfig.validate_production_config()
    
    return current_config
