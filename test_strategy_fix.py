#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试策略修复效果
验证策略是否能正确生成货币对交易决策
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'QuantumForex_Pro'))

def test_strategy_decision_generation():
    """测试策略决策生成"""
    print("🧪 测试策略决策生成...")
    
    try:
        from strategies.professional_portfolio_strategy import ProfessionalPortfolioStrategy
        from strategies.portfolio_models import MarketCondition
        
        # 创建策略实例
        strategy = ProfessionalPortfolioStrategy()
        
        # 模拟市场数据
        market_data = {
            'EURUSD': {
                'ohlcv': None,  # 简化测试
                'current_price': 1.0850,
                'volatility': 0.015,
                'trend_strength': 0.6
            },
            'GBPUSD': {
                'ohlcv': None,
                'current_price': 1.2650,
                'volatility': 0.018,
                'trend_strength': 0.4
            },
            'AUDUSD': {
                'ohlcv': None,
                'current_price': 0.6450,
                'volatility': 0.020,
                'trend_strength': 0.7
            }
        }
        
        # 模拟当前持仓（修复后的格式）
        current_positions = {
            'positions_by_symbol': {
                'AUDUSD': [
                    {
                        'symbol': 'AUDUSD',
                        'volume': 0.03,
                        'entry_price': 0.6452,
                        'profit': -0.72
                    }
                ]
            },
            'total_positions': 1,
            'total_profit': -0.72,
            'raw_positions': [
                {
                    'symbol': 'AUDUSD',
                    'volume': 0.03,
                    'entry_price': 0.6452,
                    'profit': -0.72
                }
            ]
        }
        
        print("📊 测试数据:")
        print(f"   市场数据: {len(market_data)}个货币对")
        print(f"   当前持仓: {len(current_positions['positions_by_symbol'])}个货币对")
        print("")
        
        # 生成决策
        decisions = strategy.generate_decisions(
            market_data=market_data,
            current_positions=current_positions,
            market_condition=MarketCondition.TRENDING,
            position_multiplier=1.0
        )
        
        print(f"📋 生成决策数量: {len(decisions)}")
        print("")
        
        # 分析决策类型
        decision_types = {}
        currency_pairs = set()
        
        for decision in decisions:
            action = decision.action.value if hasattr(decision.action, 'value') else str(decision.action)
            symbol = decision.symbol
            
            if action not in decision_types:
                decision_types[action] = 0
            decision_types[action] += 1
            
            currency_pairs.add(symbol)
            
            print(f"🎯 决策: {symbol} - {action}")
            print(f"   仓位: {decision.size}手")
            print(f"   置信度: {decision.confidence:.2%}")
            print(f"   说明: {decision.reasoning}")
            print("")
        
        print("📊 决策分析:")
        print(f"   决策类型分布: {decision_types}")
        print(f"   涉及货币对: {currency_pairs}")
        print("")
        
        # 检查是否有错误的"货币对"
        invalid_symbols = []
        for symbol in currency_pairs:
            if symbol in ['total_positions', 'total_profit', 'raw_positions']:
                invalid_symbols.append(symbol)
        
        if invalid_symbols:
            print(f"❌ 发现无效的货币对: {invalid_symbols}")
            return False
        else:
            print("✅ 所有决策都使用了有效的货币对名称")
        
        # 检查是否有真实的交易决策
        trading_actions = ['ENTER_LONG', 'ENTER_SHORT', 'EXIT_POSITION']
        has_trading_decisions = any(action in trading_actions for action in decision_types.keys())
        
        if has_trading_decisions:
            print("✅ 生成了真实的交易决策")
        else:
            print("⚠️ 没有生成交易决策，只有rebalance决策")
        
        return len(invalid_symbols) == 0
        
    except Exception as e:
        print(f"❌ 测试策略决策生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rebalance_logic():
    """测试rebalance逻辑"""
    print("🧪 测试rebalance逻辑...")
    
    try:
        from strategies.professional_portfolio_strategy import ProfessionalPortfolioStrategy
        
        # 创建策略实例
        strategy = ProfessionalPortfolioStrategy()
        
        # 测试修复前的问题数据
        problematic_positions = {
            'total_positions': 3,
            'total_profit': -2.16,
            'raw_positions': []
        }
        
        print("📊 测试问题数据（修复前会出错的格式）:")
        print(f"   positions keys: {list(problematic_positions.keys())}")
        
        # 调用rebalance检查
        rebalance_decisions = strategy._check_rebalancing_needs(problematic_positions, {})
        
        print(f"📋 rebalance决策数量: {len(rebalance_decisions)}")
        
        for decision in rebalance_decisions:
            print(f"🎯 rebalance决策: {decision.symbol}")
            print(f"   仓位: {decision.size}")
            print(f"   说明: {decision.reasoning}")
        
        # 检查是否还有无效的货币对
        invalid_symbols = []
        for decision in rebalance_decisions:
            if decision.symbol in ['total_positions', 'total_profit', 'raw_positions']:
                invalid_symbols.append(decision.symbol)
        
        if invalid_symbols:
            print(f"❌ 仍然有无效的货币对: {invalid_symbols}")
            return False
        else:
            print("✅ rebalance逻辑修复成功，不再处理无效的货币对")
            return True
        
    except Exception as e:
        print(f"❌ 测试rebalance逻辑失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 策略修复效果测试")
    print("=" * 80)
    
    test_results = []
    
    # 1. 测试策略决策生成
    print("1️⃣ 测试策略决策生成")
    test_results.append(("策略决策生成", test_strategy_decision_generation()))
    
    print("=" * 80)
    
    # 2. 测试rebalance逻辑
    print("2️⃣ 测试rebalance逻辑修复")
    test_results.append(("rebalance逻辑", test_rebalance_logic()))
    
    # 总结测试结果
    print("=" * 80)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print("")
    print(f"🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！策略修复成功！")
        print("\n🛠️ 修复内容:")
        print("   ✅ 修复了rebalance逻辑中的无效货币对问题")
        print("   ✅ 正确处理positions_by_symbol数据结构")
        print("   ✅ 过滤掉统计数据，只处理真实货币对")
        print("   ✅ 增加了决策生成的日志输出")
        print("\n📈 预期效果:")
        print("   - 不再生成total_positions等无效决策")
        print("   - 正确生成真实货币对的交易决策")
        print("   - rebalance逻辑只处理真实持仓")
        print("   - 系统能正常生成交易机会")
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
    input("按任意键退出...")
