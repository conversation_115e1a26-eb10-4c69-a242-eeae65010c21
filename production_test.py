#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境部署前测试
验证核心功能是否正常工作
"""

import os
import sys
import time
import json
import traceback
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试核心模块导入"""
    
    print("🔍 测试核心模块导入...")
    print("=" * 50)
    
    test_results = []
    
    # 核心模块列表
    modules = [
        ('Flask应用', 'run'),
        ('数据库客户端', 'app.utils.db_client'),
        ('LLM客户端', 'app.utils.llm_client'),
        ('MT4客户端', 'app.utils.mt4_client'),
        ('风险管理', 'app.core.risk_management'),
        ('市场分析', 'app.core.market_adaptive_system'),
        ('组合管理', 'app.core.portfolio_management_system'),
        ('路由', 'app.routes.forex_trading_routes'),
        ('服务', 'app.services.forex_trading_service')
    ]
    
    for name, module_path in modules:
        try:
            __import__(module_path)
            print(f"   ✅ {name}: 导入成功")
            test_results.append((name, 'PASS'))
        except Exception as e:
            print(f"   ❌ {name}: 导入失败 - {e}")
            test_results.append((name, 'FAIL', str(e)))
    
    return test_results

def test_database_connection():
    """测试数据库连接"""
    
    print("\n🔍 测试数据库连接...")
    print("=" * 50)
    
    try:
        from app.utils.db_client import test_connection, get_eurusd_min_data
        
        # 测试连接
        if test_connection():
            print("   ✅ 数据库连接成功")
            
            # 测试数据获取
            data = get_eurusd_min_data(limit=1)
            if data and len(data) > 0:
                latest = data[0]
                print(f"   ✅ 数据获取成功: {latest['time']} - {latest['close']}")
                return ('数据库连接', 'PASS')
            else:
                print("   ❌ 数据获取失败: 无数据")
                return ('数据库连接', 'FAIL', '无数据')
        else:
            print("   ❌ 数据库连接失败")
            return ('数据库连接', 'FAIL', '连接失败')
            
    except Exception as e:
        print(f"   ❌ 数据库测试失败: {e}")
        return ('数据库连接', 'FAIL', str(e))

def test_llm_client():
    """测试LLM客户端"""
    
    print("\n🔍 测试LLM客户端...")
    print("=" * 50)
    
    try:
        from app.utils.llm_client import call_llm_api
        
        # 简单测试调用
        test_prompt = "请回答：1+1等于几？只回答数字。"
        
        response = call_llm_api(test_prompt)
        
        if response and 'choices' in response:
            content = response['choices'][0]['message']['content']
            print(f"   ✅ LLM响应成功: {content[:50]}...")
            return ('LLM客户端', 'PASS')
        else:
            print("   ❌ LLM响应格式错误")
            return ('LLM客户端', 'FAIL', '响应格式错误')
            
    except Exception as e:
        print(f"   ❌ LLM测试失败: {e}")
        return ('LLM客户端', 'FAIL', str(e))

def test_flask_app():
    """测试Flask应用启动"""
    
    print("\n🔍 测试Flask应用...")
    print("=" * 50)
    
    try:
        from run import app
        
        # 测试应用配置
        if app:
            print(f"   ✅ Flask应用创建成功")
            print(f"   📊 调试模式: {app.debug}")
            print(f"   📊 环境: {app.env}")
            
            # 测试路由
            with app.test_client() as client:
                response = client.get('/')
                if response.status_code == 200:
                    print("   ✅ 根路由响应正常")
                    return ('Flask应用', 'PASS')
                else:
                    print(f"   ❌ 根路由响应异常: {response.status_code}")
                    return ('Flask应用', 'FAIL', f'状态码: {response.status_code}')
        else:
            print("   ❌ Flask应用创建失败")
            return ('Flask应用', 'FAIL', '应用创建失败')
            
    except Exception as e:
        print(f"   ❌ Flask应用测试失败: {e}")
        return ('Flask应用', 'FAIL', str(e))

def test_core_analysis():
    """测试核心分析功能"""
    
    print("\n🔍 测试核心分析功能...")
    print("=" * 50)
    
    try:
        from app.services.forex_trading_service import ForexTradingService
        
        service = ForexTradingService()
        
        # 测试获取K线数据
        klines = service.get_klines('EURUSD', '15', 10)
        
        if klines and len(klines) > 0:
            print(f"   ✅ K线数据获取成功: {len(klines)} 条")
            
            # 测试技术指标计算
            from app.utils.forex_data_processor import calculate_technical_indicators
            
            indicators = calculate_technical_indicators(klines)
            
            if indicators:
                print(f"   ✅ 技术指标计算成功: {len(indicators)} 个指标")
                return ('核心分析', 'PASS')
            else:
                print("   ❌ 技术指标计算失败")
                return ('核心分析', 'FAIL', '技术指标计算失败')
        else:
            print("   ❌ K线数据获取失败")
            return ('核心分析', 'FAIL', 'K线数据获取失败')
            
    except Exception as e:
        print(f"   ❌ 核心分析测试失败: {e}")
        return ('核心分析', 'FAIL', str(e))

def test_file_permissions():
    """测试文件权限"""
    
    print("\n🔍 测试文件权限...")
    print("=" * 50)
    
    test_results = []
    
    # 需要写权限的目录
    write_dirs = [
        'logs',
        'app/data',
        'app/data/errors'
    ]
    
    for dir_path in write_dirs:
        try:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
            
            # 测试写权限
            test_file = os.path.join(dir_path, 'test_write.tmp')
            with open(test_file, 'w') as f:
                f.write('test')
            
            # 清理测试文件
            os.remove(test_file)
            
            print(f"   ✅ {dir_path}: 写权限正常")
            test_results.append((f'写权限-{dir_path}', 'PASS'))
            
        except Exception as e:
            print(f"   ❌ {dir_path}: 写权限失败 - {e}")
            test_results.append((f'写权限-{dir_path}', 'FAIL', str(e)))
    
    return test_results

def test_environment_config():
    """测试环境配置"""
    
    print("\n🔍 测试环境配置...")
    print("=" * 50)
    
    try:
        from config import get_config, validate_config
        
        config = get_config()
        
        print(f"   📊 当前环境: {config.FLASK_ENV}")
        print(f"   📊 调试模式: {config.DEBUG if hasattr(config, 'DEBUG') else 'N/A'}")
        print(f"   📊 数据库: {config.DB_HOST}:{config.DB_PORT}/{config.DB_NAME}")
        print(f"   📊 LLM API: {config.LLM_API_URL}")
        print(f"   📊 MT4服务器: {config.MT4_SERVER_HOST}:{config.MT4_SERVER_PORT}")
        
        # 验证配置
        validate_config()
        
        print("   ✅ 环境配置验证通过")
        return ('环境配置', 'PASS')
        
    except Exception as e:
        print(f"   ❌ 环境配置验证失败: {e}")
        return ('环境配置', 'FAIL', str(e))

def generate_test_report(all_results):
    """生成测试报告"""
    
    print("\n📋 生成测试报告...")
    print("=" * 50)
    
    total_tests = len(all_results)
    passed_tests = len([r for r in all_results if len(r) == 2 and r[1] == 'PASS'])
    failed_tests = total_tests - passed_tests
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    report = {
        'test_time': datetime.now().isoformat(),
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'failed_tests': failed_tests,
        'success_rate': success_rate,
        'results': []
    }
    
    for result in all_results:
        if len(result) == 2:
            name, status = result
            report['results'].append({
                'test_name': name,
                'status': status,
                'error': None
            })
        else:
            name, status, error = result
            report['results'].append({
                'test_name': name,
                'status': status,
                'error': error
            })
    
    # 保存报告
    report_file = f'production_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 测试报告已保存: {report_file}")
    
    return report, report_file

def main():
    """主函数"""
    
    print("🚀 开始生产环境部署前测试...")
    print("=" * 70)
    
    all_results = []
    
    # 执行所有测试
    try:
        # 模块导入测试
        import_results = test_imports()
        all_results.extend(import_results)
        
        # 数据库连接测试
        db_result = test_database_connection()
        all_results.append(db_result)
        
        # LLM客户端测试
        llm_result = test_llm_client()
        all_results.append(llm_result)
        
        # Flask应用测试
        flask_result = test_flask_app()
        all_results.append(flask_result)
        
        # 核心分析测试
        analysis_result = test_core_analysis()
        all_results.append(analysis_result)
        
        # 文件权限测试
        permission_results = test_file_permissions()
        all_results.extend(permission_results)
        
        # 环境配置测试
        config_result = test_environment_config()
        all_results.append(config_result)
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        traceback.print_exc()
    
    # 生成报告
    report, report_file = generate_test_report(all_results)
    
    # 显示总结
    print("\n" + "=" * 70)
    print("🎯 测试总结")
    print("=" * 70)
    print(f"📊 总测试数: {report['total_tests']}")
    print(f"✅ 通过测试: {report['passed_tests']}")
    print(f"❌ 失败测试: {report['failed_tests']}")
    print(f"📈 成功率: {report['success_rate']:.1f}%")
    
    if report['failed_tests'] > 0:
        print(f"\n❌ 失败的测试:")
        for result in report['results']:
            if result['status'] == 'FAIL':
                print(f"   - {result['test_name']}: {result['error']}")
    
    print(f"\n📄 详细报告: {report_file}")
    
    if report['success_rate'] >= 80:
        print("\n🎉 系统基本就绪，可以进行部署准备！")
        return True
    else:
        print("\n⚠️ 系统存在问题，建议修复后再部署！")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
