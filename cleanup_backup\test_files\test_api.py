"""
测试DeepSeek API连接
"""
import os
import json
import requests
from datetime import datetime

# DeepSeek API密钥
API_KEY = 'sk-dplvjslhezcjinavtmaporlyumqqwnowcbjwyvmetxychflk'

# 测试不同的API端点
API_ENDPOINTS = [
    'https://api.siliconflow.cn/v1/chat/completions',
    'https://api.deepseek.com/v1/chat/completions'
]

def test_api_endpoint(endpoint):
    """测试API端点连接"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试API端点: {endpoint}")
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {API_KEY}'
    }
    
    data = {
        'model': 'Pro/deepseek-ai/DeepSeek-R1',
        'messages': [
            {'role': 'system', 'content': '你是一个有用的AI助手。'},
            {'role': 'user', 'content': '你好，这是一个API测试。请简短回复。'}
        ],
        'max_tokens': 50,
        'temperature': 0.7
    }
    
    try:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 发送请求...")
        response = requests.post(endpoint, headers=headers, json=data, timeout=30)
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 收到响应，状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_json = response.json()
            content = response_json['choices'][0]['message']['content']
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 响应内容: {content}")
            
            if 'usage' in response_json:
                prompt_tokens = response_json['usage'].get('prompt_tokens', 0)
                completion_tokens = response_json['usage'].get('completion_tokens', 0)
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> Token使用: 提示词={prompt_tokens}, 生成={completion_tokens}, 总计={prompt_tokens + completion_tokens}")
            
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试成功!")
            return True
        else:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试失败! 状态码: {response.status_code}")
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试异常! 错误: {str(e)}")
        return False

def main():
    """主函数"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 开始测试DeepSeek API连接...")
    
    for endpoint in API_ENDPOINTS:
        success = test_api_endpoint(endpoint)
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 端点 {endpoint} 测试结果: {'成功' if success else '失败'}")
        print("-" * 50)
    
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试完成!")

if __name__ == "__main__":
    main()
