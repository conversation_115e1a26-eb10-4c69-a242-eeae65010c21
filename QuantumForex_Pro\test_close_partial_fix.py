#!/usr/bin/env python3
"""
测试CLOSE_PARTIAL修复
验证交易决策执行是否正常
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_close_partial_fix():
    """测试CLOSE_PARTIAL修复"""
    print("🧪 测试CLOSE_PARTIAL修复")
    print("="*50)
    
    try:
        from core.execution_engine.trade_executor import TradeExecutor
        
        # 创建交易执行器
        executor = TradeExecutor()
        print("✅ 交易执行器创建成功")
        
        # 模拟交易决策（类似您截图中的决策）
        test_decisions = [
            {
                'symbol': 'AUDUSD',
                'action': 'BUY',
                'volume': 0.03,
                'reasoning': '组合交易：对冲COMMODITY_HEDGE (USD做空对冲)',
                'confidence': 80.0
            },
            {
                'symbol': 'USDCAD',
                'action': 'BUY',
                'volume': 0.03,
                'reasoning': '组合交易：对冲COMMODITY_HEDGE (USD做空对冲)',
                'confidence': 80.0
            },
            {
                'symbol': 'GBPUSD',
                'action': 'CLOSE_PARTIAL',
                'volume': 0.01,
                'reasoning': '重新平衡：当前权重62.06%, 目标权重50.00%, 减仓0.01手',
                'confidence': 70.0
            }
        ]
        
        print(f"📋 测试交易决策: {len(test_decisions)}个")
        for i, decision in enumerate(test_decisions, 1):
            print(f"  {i}. {decision['symbol']} {decision['action']} {decision.get('volume', 0):.3f}手")
            print(f"     原因: {decision['reasoning']}")
        
        # 执行交易决策
        print(f"\n🚀 执行交易决策...")
        executed_orders = executor.execute_trading_decisions(test_decisions)
        
        print(f"\n📊 执行结果:")
        print(f"   执行成功: {len(executed_orders)}个")
        print(f"   总决策数: {len(test_decisions)}个")
        print(f"   成功率: {len(executed_orders)/len(test_decisions):.1%}")
        
        if executed_orders:
            print(f"\n✅ 执行成功的订单:")
            for i, order in enumerate(executed_orders, 1):
                print(f"  {i}. {order.symbol} {order.order_type.value if hasattr(order.order_type, 'value') else order.order_type}")
                print(f"     订单ID: {order.order_id}")
                print(f"     状态: {order.status.value if hasattr(order.status, 'value') else order.status}")
                print(f"     备注: {order.comment}")
        
        # 验证CLOSE_PARTIAL是否被正确处理
        close_partial_executed = any(
            'CLOSE_PARTIAL' in order.comment or 'CLOSE' in str(order.order_type)
            for order in executed_orders
        )
        
        if close_partial_executed:
            print(f"\n🎉 CLOSE_PARTIAL修复成功！")
            print(f"✅ 平仓操作被正确执行")
            return True
        else:
            print(f"\n⚠️ CLOSE_PARTIAL可能仍有问题")
            print(f"💡 检查是否有GBPUSD持仓可以平仓")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_handle_close_decision():
    """单独测试_handle_close_decision方法"""
    print("\n🧪 测试_handle_close_decision方法")
    print("="*50)
    
    try:
        from core.execution_engine.trade_executor import TradeExecutor
        
        executor = TradeExecutor()
        
        # 测试CLOSE_PARTIAL决策
        test_decision = {
            'symbol': 'GBPUSD',
            'action': 'CLOSE_PARTIAL',
            'volume': 0.01,
            'reasoning': '测试部分平仓'
        }
        
        print(f"📋 测试决策: {test_decision}")
        
        # 调用_handle_close_decision方法
        result = executor._handle_close_decision(test_decision)
        
        print(f"📊 方法返回结果: {result}")
        print(f"📊 返回类型: {type(result)}")
        
        if isinstance(result, bool):
            print(f"✅ 返回类型正确 (bool)")
            if result:
                print(f"✅ 平仓操作成功")
            else:
                print(f"⚠️ 平仓操作失败或无持仓")
            return True
        else:
            print(f"❌ 返回类型错误，期望bool，实际{type(result)}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 CLOSE_PARTIAL修复验证测试")
    print("="*60)
    
    # 测试1: _handle_close_decision方法
    test1_result = test_handle_close_decision()
    
    # 测试2: 完整的交易决策执行
    test2_result = test_close_partial_fix()
    
    print(f"\n📊 测试结果总结:")
    print(f"   _handle_close_decision测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   完整执行流程测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 CLOSE_PARTIAL修复验证成功！")
        print(f"💡 交易决策现在应该可以正常执行")
        return True
    else:
        print(f"\n❌ 修复验证失败")
        print(f"💡 需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 修复验证完成，系统应该可以正常执行交易决策")
    else:
        print("\n❌ 修复验证失败，需要进一步调试")
