"""
测试LLM响应和解析
直接调用LLM并检查返回结果
"""
import os
import sys
import traceback
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'

from app.utils import llm_client
import re

def test_llm_response():
    """测试LLM响应和解析"""
    try:
        print("=" * 50)
        print("测试LLM响应和解析")
        print("=" * 50)
        print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 构建简单的提示词
        prompt = """
你是一个外汇交易分析助手。请根据以下简要市场数据，判断是否需要进行完整的市场分析，并设置下次分析的时间间隔。

当前EURUSD价格: 1.08580
前一收盘价: 1.08520
价格变化: 0.00060 (0.06%)
RSI(14): 55.2
MACD: 0.0003
当前持仓数量: 0

请提供两项信息：
1. 是否需要进行完整分析（是/否）及简短理由
2. 下次分析的建议时间间隔（分钟）

重要说明：
- 如果市场波动较大或有明显的交易信号，请回答"是"
- 如果市场相对平静，没有明显的交易机会，请回答"否"
- 下次分析间隔必须是一个具体的分钟数，范围在5-120分钟之间
- 市场波动大时，设置较短的间隔（5-15分钟）
- 市场波动中等时，设置中等间隔（15-30分钟）
- 市场波动小时，设置较长间隔（30-120分钟）
- 系统会严格按照你设置的分钟数等待后再次分析，请确保设置合理的时间间隔

**必须严格按照以下格式回答，这对系统正常运行至关重要：**

分析决定: 是/否，简短理由
下次分析间隔: XX分钟

请注意：
1. "分析决定:"和"下次分析间隔:"这两个标签必须完全一致，包括冒号
2. "是/否"必须是单个汉字，不要使用"是的"或"不是"等变体
3. XX必须是一个5到120之间的整数，表示分钟数
4. 请不要添加额外的解释或分析，只需按照上述格式简洁回答
5. 你的回答将被自动解析，因此格式必须完全正确
6. 不要在回答中添加任何其他内容，只需提供上述两行信息

示例正确回答1:
分析决定: 是，价格波动较大
下次分析间隔: 10分钟

示例正确回答2:
分析决定: 否，市场平稳
下次分析间隔: 30分钟
"""
        
        # 确保输出立即显示
        sys.stdout.flush()
        
        # 调用LLM
        print("\n开始调用LLM...")
        try:
            response = llm_client.send_to_deepseek(prompt, temperature=0.1, max_tokens=100)
            
            # 输出原始响应
            print("\nLLM原始响应:")
            print(response)
            
            # 提取回答内容
            if 'choices' in response and response['choices'] and 'message' in response['choices'][0]:
                answer = response['choices'][0]['message']['content'].strip()
                print("\nLLM回答内容:")
                print(answer)
                
                # 解析分析决定
                print("\n开始解析分析决定...")
                should_analyze = False
                reason = "未能解析"
                
                # 尝试直接提取"是"或"否"
                yes_pattern = re.compile(r'分析决定\s*[:：]?\s*是', re.IGNORECASE)
                no_pattern = re.compile(r'分析决定\s*[:：]?\s*否', re.IGNORECASE)
                
                if yes_pattern.search(answer):
                    should_analyze = True
                    reason = "市场变化需要分析"
                    print("直接匹配到'是'")
                elif no_pattern.search(answer):
                    should_analyze = False
                    reason = "市场稳定"
                    print("直接匹配到'否'")
                else:
                    # 尝试更复杂的模式
                    analysis_match = re.search(r'分析决定\s*[:：]\s*(是|否)[,，]?\s*(.*?)(?:\n|$)', answer, re.IGNORECASE)
                    if analysis_match:
                        decision = analysis_match.group(1)
                        reason_text = analysis_match.group(2).strip()
                        
                        should_analyze = decision == '是'
                        if reason_text:
                            reason = reason_text
                        else:
                            reason = "市场变化需要分析" if should_analyze else "市场稳定"
                        print(f"成功匹配分析决定: {decision}, 原因: {reason}")
                    else:
                        print("未匹配到分析决定")
                
                # 解析下次分析间隔
                print("\n开始解析下次分析间隔...")
                next_interval = 30  # 默认30分钟
                
                # 尝试直接提取"下次分析间隔: XX分钟"格式
                direct_interval_match = re.search(r'下次分析间隔\s*[:：]\s*(\d+)\s*分钟', answer, re.IGNORECASE)
                
                if direct_interval_match:
                    try:
                        next_interval = int(direct_interval_match.group(1))
                        print(f"直接匹配到间隔值: {next_interval}分钟")
                    except ValueError:
                        print(f"直接匹配到间隔值但转换失败，使用默认值: {next_interval}分钟")
                else:
                    # 如果直接匹配失败，尝试提取所有数字
                    all_numbers = re.findall(r'\d+', answer)
                    if all_numbers:
                        # 如果找到了数字，尝试找到与"分钟"相关的数字
                        minute_related_numbers = []
                        for number in all_numbers:
                            # 检查数字前后是否有"分钟"相关的文本
                            number_pos = answer.find(number)
                            context = answer[max(0, number_pos-10):min(len(answer), number_pos+15)]
                            if '分钟' in context or '分' in context:
                                minute_related_numbers.append(int(number))
                        
                        if minute_related_numbers:
                            # 使用第一个与"分钟"相关的数字
                            next_interval = minute_related_numbers[0]
                            print(f"从上下文中提取到间隔值: {next_interval}分钟")
                        else:
                            print(f"未找到与'分钟'相关的数字，使用默认值: {next_interval}分钟")
                    else:
                        print(f"未找到任何数字，使用默认值: {next_interval}分钟")
                
                # 确保在合理范围内
                if next_interval < 5:
                    print(f"间隔值过小，调整为最小值: 5分钟")
                    next_interval = 5
                elif next_interval > 120:
                    print(f"间隔值过大，调整为最大值: 120分钟")
                    next_interval = 120
                
                # 输出最终解析结果
                print("\n最终解析结果:")
                print(f"是否需要分析: {should_analyze}")
                print(f"原因: {reason}")
                print(f"下次分析间隔: {next_interval}分钟")
            else:
                print("\nLLM响应格式异常，无法提取回答内容")
        except Exception as e:
            print(f"\nLLM调用或解析失败: {e}")
            traceback.print_exc()
        
        print("\n测试完成")
    except Exception as e:
        print(f"测试过程出错: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_llm_response()
