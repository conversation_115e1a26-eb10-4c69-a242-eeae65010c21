#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试本地连接脚本
"""

import requests
import json
import time

def test_local_connection():
    """测试本地连接"""
    print("🔍 测试本地API连接...")
    
    # 测试本地连接
    local_url = "http://127.0.0.1:8081/api/health"
    
    try:
        response = requests.get(local_url, timeout=5)
        if response.status_code == 200:
            print("✅ 本地API连接成功！")
            print(f"📊 响应: {response.json()}")
            return True
        else:
            print(f"❌ API响应错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_model_upload():
    """测试模型上传"""
    print("\n🔄 测试模型上传...")
    
    # 创建测试模型文件
    test_model_data = {
        "model_name": "test_model",
        "model_type": "price_prediction",
        "version": "1.0.0",
        "timestamp": time.time(),
        "data": "test_model_content"
    }
    
    upload_url = "http://127.0.0.1:8081/api/models/upload"
    
    try:
        # 模拟文件上传
        files = {
            'model_file': ('test_model.json', json.dumps(test_model_data), 'application/json')
        }
        
        data = {
            'model_name': 'test_model',
            'model_type': 'price_prediction'
        }
        
        response = requests.post(upload_url, files=files, data=data, timeout=10)
        
        if response.status_code == 200:
            print("✅ 模型上传测试成功！")
            print(f"📊 响应: {response.json()}")
            return True
        else:
            print(f"❌ 上传失败: {response.status_code}")
            print(f"📊 响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return False

def test_model_list():
    """测试模型列表"""
    print("\n📋 测试模型列表...")
    
    list_url = "http://127.0.0.1:8081/api/models/list"
    
    try:
        response = requests.get(list_url, timeout=5)
        if response.status_code == 200:
            print("✅ 模型列表获取成功！")
            models = response.json()
            print(f"📊 模型数量: {len(models.get('models', []))}")
            for model in models.get('models', []):
                print(f"   - {model}")
            return True
        else:
            print(f"❌ 获取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 获取异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 QuantumForex 本地连接测试")
    print("=" * 50)
    
    tests = [
        ("API连接", test_local_connection),
        ("模型上传", test_model_upload),
        ("模型列表", test_model_list)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔄 测试: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    print(f"✅ 通过测试: {passed}/{total}")
    print(f"📈 成功率: {passed/total:.1%}")
    
    if passed == total:
        print("🎉 所有本地连接测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按任意键退出...")
