"""
外汇交易服务
提供外汇数据获取、分析和交易功能
"""
import time
import traceback
import numpy as np
from datetime import datetime, timedelta

import os
from app.utils import db_client
from app.utils import mt4_client
from app.utils import llm_client

# 导入日志管理器
from app.utils.logger_manager import log_system, log_mt4, log_analysis, log_trading, log_database, log_error_simple, LogLevel

# 检查是否设置了授权码
AUTH_CODE = os.environ.get('AUTH_CODE')
if AUTH_CODE:
    log_mt4(LogLevel.INFO, "检测到授权码，将使用授权码连接MT4服务器")
    mt4_client.mt4_client.auth_code = AUTH_CODE
from app.utils import forex_data_processor
from app.utils import forex_llm_prompt
from app.utils import forex_analysis_history
from app.utils import multi_round_analysis
from app.utils import performance_evaluator
from app.utils.error_logger import log_error, log_operation, ErrorType, OperationType
from app.utils.error_collector import make_json_serializable
from app.utils.performance_evaluator import update_trade_status, TradeStatus
from app.utils.order_result_analyzer import update_order_results
from app.utils.intelligent_pair_selector import select_optimal_currency_pairs
from app.utils.multi_pair_data_manager import get_multi_pair_analysis_data

# 导入LLM优化技术分析器
try:
    from app.utils.llm_optimized_technical_analyzer import LLMOptimizedTechnicalAnalyzer
    from app.utils.llm_analysis_formatter import LLMAnalysisFormatter
    llm_technical_analyzer = LLMOptimizedTechnicalAnalyzer()
    llm_formatter = LLMAnalysisFormatter()
    has_llm_analyzer = True
    log_system(LogLevel.INFO, "LLM优化技术分析器已加载")
except ImportError as e:
    has_llm_analyzer = False
    llm_technical_analyzer = None
    llm_formatter = None
    log_system(LogLevel.WARNING, f"LLM优化技术分析器模块不可用: {e}")

# 导入LLM优化交易策略
try:
    from app.utils.llm_optimized_trading_strategy import LLMOptimizedTradingStrategy
    llm_trading_strategy = LLMOptimizedTradingStrategy()
    has_llm_strategy = True
    log_system(LogLevel.INFO, "LLM优化交易策略已加载")
except ImportError as e:
    has_llm_strategy = False
    llm_trading_strategy = None
    log_system(LogLevel.WARNING, f"LLM优化交易策略模块不可用: {e}")

# 导入风险管理系统
try:
    from app.core.risk_management import AdvancedRiskManager, RiskLevel, TradingAction
    risk_manager = AdvancedRiskManager()
    has_risk_manager = True
    log_system(LogLevel.INFO, "高级风险管理系统已加载")
except ImportError as e:
    has_risk_manager = False
    risk_manager = None
    log_system(LogLevel.WARNING, f"风险管理系统模块不可用: {e}")

# 导入信号质量分析系统
try:
    from app.core.signal_quality_analyzer import AdvancedSignalAnalyzer, SignalGrade
    signal_analyzer = AdvancedSignalAnalyzer()
    has_signal_analyzer = True
    log_system(LogLevel.INFO, "信号质量分析系统已加载")
except ImportError as e:
    has_signal_analyzer = False
    signal_analyzer = None
    log_system(LogLevel.WARNING, f"信号质量分析系统模块不可用: {e}")

# 导入市场自适应系统
try:
    from app.core.market_adaptive_system import MarketAdaptiveSystem, MarketRegime, TradingStrategy
    market_adaptive = MarketAdaptiveSystem()
    has_market_adaptive = True
    log_system(LogLevel.INFO, "市场状态自适应系统已加载")
except ImportError as e:
    has_market_adaptive = False
    market_adaptive = None
    log_system(LogLevel.WARNING, f"市场状态自适应系统模块不可用: {e}")

# 导入交易结果记录器
try:
    from app.utils import trade_result_recorder
    has_trade_recorder = True
    log_system(LogLevel.INFO, "交易结果记录器已加载")
except ImportError:
    has_trade_recorder = False
    log_system(LogLevel.WARNING, "交易结果记录器模块不可用，将不会记录交易结果")


def get_eurusd_min_data(start_time=None, end_time=None, limit=1000):
    """
    获取EURUSD分钟数据

    Args:
        start_time (str, optional): 开始时间
        end_time (str, optional): 结束时间
        limit (int, optional): 限制条数

    Returns:
        list: EURUSD分钟数据
    """
    try:
        return db_client.get_eurusd_min_data(start_time, end_time, limit)
    except Exception as error:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1.1: 错误: 获取EURUSD分钟数据失败')
        raise


def get_aggregated_klines(period_minutes=15, count=100):
    """
    获取聚合K线数据

    Args:
        period_minutes (int): 周期（分钟）
        count (int): 数量

    Returns:
        list: 聚合K线数据，如果数据不足则返回空列表
    """
    try:
        # 计算需要获取的1分钟K线数量
        # 增加获取的数据量，特别是对于较大的周期（如60分钟）
        if period_minutes <= 15:
            min_data_count = period_minutes * count * 2  # 15分钟及以下周期使用2倍系数
        elif period_minutes <= 30:
            min_data_count = period_minutes * count * 3  # 30分钟周期使用3倍系数
        else:
            min_data_count = period_minutes * count * 4  # 60分钟及以上周期使用4倍系数

        # 确保获取足够的数据
        min_data_count = max(min_data_count, 10000)  # 至少获取10000条1分钟数据

        # 获取1分钟K线数据
        min_data = get_eurusd_min_data(limit=min_data_count)

        if not min_data:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1.2: 错误: 1分钟K线数据为空')

            # 记录错误
            log_error(
                error_type=ErrorType.DATA_ERROR,
                message=f'获取1分钟K线数据失败，无法生成{period_minutes}分钟K线',
                details={'timestamp': datetime.now().isoformat(), 'period_minutes': period_minutes, 'count': count},
                operation=OperationType.DATA_FETCH
            )

            return []

        # 检查数据是否足够
        min_required = period_minutes * 20  # 确保至少有20根K线的数据
        if len(min_data) < min_required:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1.2: 警告: 1分钟K线数据不足 ({len(min_data)}/{min_required})')

            # 记录错误
            log_error(
                error_type=ErrorType.DATA_ERROR,
                message=f'1分钟K线数据不足，只有{len(min_data)}条，需要至少{min_required}条',
                details={'timestamp': datetime.now().isoformat(), 'period_minutes': period_minutes, 'count': count, 'actual': len(min_data), 'required': min_required},
                operation=OperationType.DATA_FETCH
            )

            return []

        # 数据是按时间倒序排列的，需要转为升序
        min_data.reverse()

        # 确保数据是最新的
        current_time = datetime.now()
        try:
            # 尝试解析时间，支持多种格式
            try:
                latest_data_time = datetime.strptime(min_data[-1]["time"], "%Y-%m-%d %H:%M:%S")
            except ValueError:
                try:
                    latest_data_time = datetime.strptime(min_data[-1]["time"], "%Y.%m.%d %H:%M")
                except ValueError:
                    # 如果都失败，尝试更通用的方法
                    from dateutil import parser
                    latest_data_time = parser.parse(min_data[-1]["time"])

            # 调整MT4时间到北京时间（GMT+8）
            # MT4时间通常是GMT+0，北京时间是GMT+8，差8小时
            mt4_to_beijing_hours = 8
            adjusted_data_time = latest_data_time + timedelta(hours=mt4_to_beijing_hours)

            # 计算调整后的时间差
            time_diff = current_time - adjusted_data_time
            # 只在时间差过大时输出警告
            if time_diff.total_seconds() / 60 > 30:  # 如果时间差超过30分钟
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1.2: 警告: 数据时间差较大: {time_diff.total_seconds() / 60:.0f}分钟')
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1.2: 警告: 无法解析数据时间')

        # 聚合为指定周期的K线
        aggregated_klines = forex_data_processor.aggregate_klines(min_data, period_minutes)

        # 检查聚合后的K线数量
        if not aggregated_klines:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1.3: 错误: 聚合后K线数据为空')

            # 记录错误
            log_error(
                error_type=ErrorType.DATA_ERROR,
                message=f'聚合后的{period_minutes}分钟K线数据为空',
                details={'timestamp': datetime.now().isoformat(), 'period_minutes': period_minutes, 'count': count, 'min_data_count': len(min_data)},
                operation=OperationType.OTHER
            )

            return []

        if len(aggregated_klines) < 20:  # 至少需要20根K线
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1.3: 警告: 聚合后K线数据不足 ({len(aggregated_klines)}/20)')

            # 记录错误
            log_error(
                error_type=ErrorType.DATA_ERROR,
                message=f'聚合后的{period_minutes}分钟K线数据不足，只有{len(aggregated_klines)}根，需要至少20根',
                details={'timestamp': datetime.now().isoformat(), 'period_minutes': period_minutes, 'count': count, 'actual': len(aggregated_klines), 'required': 20},
                operation=OperationType.OTHER
            )

            return []

        # 返回指定数量的K线，但确保是最新的数据
        if len(aggregated_klines) > count:
            # 如果聚合后的K线数量超过了请求的数量，取最新的count根K线
            result = aggregated_klines[-count:]
        else:
            # 否则返回所有可用的K线
            result = aggregated_klines

        # 确保数据是最新的
        if len(result) > 0:
            latest_data_time = datetime.strptime(result[-1]["time"], "%Y-%m-%d %H:%M:%S")

            # 调整MT4时间到北京时间（GMT+8）
            mt4_to_beijing_hours = 8
            adjusted_data_time = latest_data_time + timedelta(hours=mt4_to_beijing_hours)

            # 计算调整后的时间差
            time_diff = datetime.now() - adjusted_data_time
            # 只在时间差过大时输出警告
            if time_diff.total_seconds() / 60 > 30:  # 如果时间差超过30分钟
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1.3: 警告: 聚合后数据时间差较大: {time_diff.total_seconds() / 60:.0f}分钟')

        # 简化日志输出
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1.3: 返回{len(result)}根{period_minutes}分钟K线')

        # 确认数据是按时间升序排列的
        if len(result) > 1:
            first_time = datetime.strptime(result[0]["time"], "%Y-%m-%d %H:%M:%S")
            last_time = datetime.strptime(result[-1]["time"], "%Y-%m-%d %H:%M:%S")
            if first_time > last_time:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1.3: 警告: 数据时间顺序不正确，已修正')
                result.reverse()

        return result
    except Exception as error:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.1.3: 错误: 获取聚合K线数据失败')
        traceback.print_exc()

        # 记录错误
        log_error(
            error_type=ErrorType.SYSTEM_ERROR,
            message=f'获取聚合K线数据失败: {error}',
            details={'timestamp': datetime.now().isoformat(), 'period_minutes': period_minutes, 'count': count, 'traceback': traceback.format_exc()},
            operation=OperationType.DATA_FETCH
        )

        return []  # 出错时返回空列表，而不是抛出异常


def calculate_indicators(klines, timeframe='15min'):
    """
    计算技术指标

    Args:
        klines (list): K线数据
        timeframe (str): 时间周期，如 '15min', '1h' 等

    Returns:
        dict: 技术指标，如果数据不足则返回有限的指标
    """
    try:
        # 检查K线数据
        if not klines:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 错误: {timeframe}周期K线数据为空')
            return {
                'timeframe': timeframe,
                'dataAvailable': False,
                'message': 'K线数据为空'
            }

        # 记录可用的K线数量
        kline_count = len(klines)

        # 初始化结果字典
        indicators = {
            'timeframe': timeframe,
            'dataAvailable': True,
            'klineCount': kline_count
        }

        # 获取最新的K线数据
        latest_kline = klines[-1] if klines and len(klines) > 0 else None
        if latest_kline:
            indicators['latestClose'] = latest_kline['close']
            indicators['latestTime'] = latest_kline['time']

        # 如果K线数量太少，只计算基本指标
        if kline_count < 5:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 警告: {timeframe}周期K线数据不足5根')
            return indicators

        # 计算移动平均线 - 根据可用数据量逐步增加指标
        try:
            ma = {}
            # 添加13日均线，用于右侧交易策略
            for period in [5, 10, 13, 20, 50]:
                if kline_count >= period:
                    # 打印一些调试信息
                    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 步骤2.2: 计算{period}周期SMA，数据长度: {kline_count}")

                    # 确保使用正确的价格数据计算SMA
                    # MT4默认使用收盘价计算SMA
                    ma_values = forex_data_processor.calculate_sma(klines, period, field='close')

                    # 检查计算结果
                    if ma_values and len(ma_values) > 0 and ma_values[-1] is not None:
                        ma[period] = ma_values[-1]
                        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 步骤2.2: {period}周期SMA计算成功: {ma[period]}")
                    else:
                        ma[period] = None
                        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 步骤2.2: {period}周期SMA计算失败，结果为空或None")

                    # 如果是13日均线，计算额外的信息用于右侧交易策略
                    if period == 13 and ma_values and len(ma_values) >= 8 and ma_values[-1] is not None:
                        # 打印13日均线的详细信息，用于调试
                        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 步骤2.2: 13日均线详细信息:")
                        print(f"  最近8个值: {ma_values[-8:]}")

                        # 计算均线方向（使用最近8个值判断趋势）
                        recent_values = [v for v in ma_values[-8:] if v is not None]
                        if len(recent_values) >= 2:
                            # 检查是否全部上升
                            is_up = True
                            for i in range(len(recent_values)-1):
                                if recent_values[i] >= recent_values[i+1]:
                                    is_up = False
                                    break

                            # 检查是否全部下降
                            is_down = True
                            for i in range(len(recent_values)-1):
                                if recent_values[i] <= recent_values[i+1]:
                                    is_down = False
                                    break

                            if is_up:
                                ma['13_direction'] = 'UP'
                            elif is_down:
                                ma['13_direction'] = 'DOWN'
                            else:
                                ma['13_direction'] = 'FLAT'
                        else:
                            ma['13_direction'] = 'FLAT'

                        print(f"  均线方向: {ma['13_direction']}")

                        # 计算均线斜率（点/周期）
                        if len(recent_values) >= 2:
                            ma['13_slope'] = (recent_values[-1] - recent_values[0]) / (len(recent_values) - 1)
                            print(f"  均线斜率: {ma['13_slope']}")

                        # 计算价格与均线的关系
                        latest_close = float(klines[-1]['close'])
                        ma_value = float(ma_values[-1])
                        distance = abs(latest_close - ma_value)
                        ma['13_distance'] = distance
                        print(f"  价格与均线距离: {distance}")

                        if distance < 0.0015:  # 15点以内
                            ma['13_price_position'] = 'NEAR'
                        elif latest_close > ma_value:
                            ma['13_price_position'] = 'ABOVE'
                        else:
                            ma['13_price_position'] = 'BELOW'

                        print(f"  价格位置: {ma['13_price_position']}")

                        # 计算均线提前量（预测2小时后的位置）
                        if len(ma_values) >= 10:
                            valid_values = [v for v in ma_values[-10:] if v is not None]
                            if len(valid_values) >= 2:
                                speed = (valid_values[-1] - valid_values[0]) / len(valid_values)
                                ma['13_speed'] = speed
                                ma['13_projection_2h'] = ma_values[-1] + (speed * 8)  # 假设15分钟周期，2小时=8个周期
                                print(f"  均线速度: {speed}")
                                print(f"  2小时后预测位置: {ma['13_projection_2h']}")

            indicators['ma'] = ma
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 错误: 计算SMA指标失败: {str(e)}')
            import traceback
            traceback.print_exc()
            indicators['ma'] = {'error': str(e)}

        # 计算RSI - 需要至少14根K线
        try:
            if kline_count >= 14:
                rsi_values = forex_data_processor.calculate_rsi(klines)
                indicators['rsi'] = rsi_values[-1] if rsi_values and len(rsi_values) > 0 else None
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 错误: 计算RSI指标失败')
            indicators['rsi'] = None

        # 计算MACD - 需要至少26根K线
        try:
            if kline_count >= 26:
                macd = forex_data_processor.calculate_macd(klines)
                indicators['macd'] = macd
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 错误: 计算MACD指标失败')
            indicators['macd'] = {'error': str(e)}

        # 计算布林带 - 需要至少20根K线
        try:
            if kline_count >= 20:
                bollinger = forex_data_processor.calculate_bollinger_bands(klines)
                indicators['bollinger'] = bollinger
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 错误: 计算布林带指标失败')
            indicators['bollinger'] = {'error': str(e)}

        # 计算动量 - 需要至少14根K线
        try:
            if kline_count >= 14:
                momentum_values = forex_data_processor.calculate_momentum(klines)
                momentum = momentum_values[-1] if momentum_values and len(momentum_values) > 0 else None

                # 确保动量是一个单一的值，而不是数组
                if isinstance(momentum, (list, tuple, np.ndarray)):
                    momentum = momentum[-1] if len(momentum) > 0 else None

                indicators['momentum'] = momentum
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 错误: 计算动量指标失败')
            indicators['momentum'] = None

        # 执行高级技术分析 - 需要至少50根K线
        try:
            if kline_count >= 50:
                advanced_analysis = forex_data_processor.perform_advanced_analysis(klines, timeframe)
                indicators['advanced'] = advanced_analysis['indicators']
                indicators['advancedAnalysis'] = advanced_analysis['analysis']
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 错误: 执行高级技术分析失败')
            indicators['advanced'] = {'error': str(e)}
            indicators['advancedAnalysis'] = f'无法执行高级技术分析: {e}'

        # 执行LLM优化技术分析 - 需要至少20根K线
        try:
            if has_llm_analyzer and kline_count >= 20:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 执行LLM优化技术分析')
                llm_analysis = llm_technical_analyzer.analyze_for_llm(klines, timeframe)

                if llm_analysis and not llm_analysis.get('error', False):
                    indicators['llm_analysis'] = llm_analysis
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: LLM优化技术分析完成')
                else:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: LLM优化技术分析失败')
                    indicators['llm_analysis'] = {'error': 'LLM分析失败'}
            else:
                if not has_llm_analyzer:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: LLM优化技术分析器不可用')
                else:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 数据不足，跳过LLM优化技术分析')
                indicators['llm_analysis'] = {'available': False, 'reason': 'analyzer_unavailable' if not has_llm_analyzer else 'insufficient_data'}
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 错误: LLM优化技术分析失败: {e}')
            indicators['llm_analysis'] = {'error': str(e)}

        return indicators
    except Exception as error:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.2: 错误: 计算技术指标失败')
        # 返回一个基本的指标对象，而不是抛出异常
        return {
            'timeframe': timeframe,
            'dataAvailable': False,
            'error': str(error),
            'message': '计算技术指标时发生错误'
        }


def get_relevant_news(limit=10):
    """
    获取相关新闻

    Args:
        limit (int): 限制条数

    Returns:
        list: 相关新闻
    """
    try:
        # 获取最新新闻
        all_news = db_client.get_latest_news(limit * 2)  # 获取更多新闻，以便过滤

        # 过滤与外汇相关的新闻
        forex_keywords = ['欧元', '美元', 'EUR', 'USD', 'EURUSD', '欧美', '汇率', '外汇',
                         '欧洲央行', 'ECB', '美联储', 'Fed', '利率', '通胀', 'CPI',
                         'PMI', 'GDP', '就业', '非农', '经济数据']

        relevant_news = []
        for news in all_news:
            content = news.get('content', '').lower()
            if any(keyword.lower() in content for keyword in forex_keywords):
                relevant_news.append(news)

            if len(relevant_news) >= limit:
                break

        return relevant_news
    except Exception as error:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 错误: 获取相关新闻失败')
        raise


def get_relevant_calendar(limit=10):
    """
    获取相关日历事件

    Args:
        limit (int): 限制条数

    Returns:
        list: 相关日历事件
    """
    try:
        # 获取最新日历事件
        all_calendar = db_client.get_latest_calendar(limit * 2)  # 获取更多事件，以便过滤

        # 过滤与欧元和美元相关的事件
        forex_countries = ['欧元区', '美国', 'EUR', 'USD', 'EU', 'US', '德国', '法国', '意大利', '西班牙']

        relevant_calendar = []
        for event in all_calendar:
            country = event.get('country', '').lower()
            title = event.get('title', '').lower()

            if any(c.lower() in country for c in forex_countries):
                relevant_calendar.append(event)
            elif any(c.lower() in title for c in forex_countries):
                relevant_calendar.append(event)

            if len(relevant_calendar) >= limit:
                break

        return relevant_calendar
    except Exception as error:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 错误: 获取相关日历事件失败')
        raise


def get_analysis_data(target_symbols=None):
    """
    获取分析数据 - 支持智能多货币对分析

    Args:
        target_symbols (list): 目标货币对列表，如果为None则智能选择

    Returns:
        dict: 分析数据
        None: 如果MT4交互失败或数据不足
    """
    try:
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤2.1】智能货币对选择 - 开始获取智能多货币对分析数据')

        # 如果没有指定目标货币对，则智能选择
        if target_symbols is None:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 执行智能货币对选择算法')
            target_symbols = select_optimal_currency_pairs()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 智能选择结果: {target_symbols}')

        # 确保至少有一个货币对
        if not target_symbols:
            target_symbols = ['EURUSD']
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 回退到默认货币对: EURUSD')

        # 获取主要货币对数据（第一个为主要分析目标）
        primary_symbol = target_symbols[0]
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 主要分析货币对: {primary_symbol}')

        # 获取多货币对数据
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤2.2】市场数据获取 - 获取多货币对数据')

        # 使用新的多货币对数据管理器
        multi_pair_data = get_multi_pair_analysis_data(target_symbols, primary_symbol)

        if not multi_pair_data or not multi_pair_data.get('pairs_data'):
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 多货币对数据获取失败，回退到传统单货币对模式')

            # 回退到传统的EURUSD数据获取
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 回退: 获取EURUSD数据')
            timeframe15m = get_aggregated_klines(15, 100)

            if not timeframe15m:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 传统数据获取也失败，无法进行分析')

                # 记录错误
                log_error(
                    error_type=ErrorType.DATA_ERROR,
                    message='多货币对和传统数据获取都失败，无法进行分析',
                    details={'timestamp': datetime.now().isoformat(), 'target_symbols': target_symbols},
                    operation=OperationType.ANALYSIS
                )

                return None

            # 使用传统模式继续处理
            primary_symbol = 'EURUSD'
            target_symbols = ['EURUSD']
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 回退模式：使用EURUSD数据')
        else:
            # 使用多货币对数据
            primary_symbol = multi_pair_data['primary_symbol']
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 多货币对数据获取成功')
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 成功获取{len(multi_pair_data["pairs_data"])}个货币对数据')
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 主要货币对确认: {primary_symbol}')

            # 从多货币对数据中提取主要货币对的数据
            primary_data = multi_pair_data['pairs_data'][primary_symbol]
            timeframe15m = primary_data['timeframe_15m']

            # 检查主要货币对数据质量
            if not timeframe15m or len(timeframe15m) < 20:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 主要货币对{primary_symbol}数据不足，无法进行分析')

                # 记录错误
                log_error(
                    error_type=ErrorType.DATA_ERROR,
                    message=f'主要货币对{primary_symbol}数据不足，无法进行分析',
                    details={'timestamp': datetime.now().isoformat(), 'primary_symbol': primary_symbol, 'data_count': len(timeframe15m) if timeframe15m else 0},
                    operation=OperationType.ANALYSIS
                )

                return None

        # 检查数据质量（无论是多货币对模式还是传统模式）
        if len(timeframe15m) < 20:  # 至少需要20根K线
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 15分钟K线数据不足，只有{len(timeframe15m)}根，尝试获取更多数据')

            # 如果是传统模式，尝试获取更多数据
            if 'multi_pair_data' not in locals() or not multi_pair_data:
                timeframe15m = get_aggregated_klines(15, 200)  # 尝试获取200根K线
            if not timeframe15m or len(timeframe15m) < 20:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 15分钟K线数据仍然不足，只有{len(timeframe15m) if timeframe15m else 0}根，无法进行分析')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 系统需要至少20根15分钟K线才能进行有效分析')

                # 记录错误
                log_error(
                    error_type=ErrorType.DATA_ERROR,
                    message=f'15分钟K线数据不足，只有{len(timeframe15m) if timeframe15m else 0}根，无法进行分析',
                    details={'timestamp': datetime.now().isoformat(), 'required': 20, 'actual': len(timeframe15m) if timeframe15m else 0},
                    operation=OperationType.ANALYSIS
                )

                return None
            else:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 成功获取{len(timeframe15m)}根15分钟K线')

        # 获取1小时数据
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.3: 获取1小时K线数据')

        # 如果是多货币对模式，从已获取的数据中提取
        if 'multi_pair_data' in locals() and multi_pair_data and primary_symbol in multi_pair_data['pairs_data']:
            timeframe1h = multi_pair_data['pairs_data'][primary_symbol]['timeframe_1h']
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 从多货币对数据中获取1小时数据: {len(timeframe1h)}根')
        else:
            # 传统模式
            timeframe1h = get_aggregated_klines(60, 100)

        # 检查K线数据是否足够
        if not timeframe1h:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 1小时K线数据获取失败，尝试使用15分钟数据代替')
            # 如果1小时数据获取失败，尝试使用15分钟数据聚合
            try:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 尝试从15分钟数据聚合1小时数据')
                # 确保15分钟数据足够多
                if len(timeframe15m) >= 80:  # 至少需要80根15分钟K线才能生成20根1小时K线
                    # 将15分钟数据转换回1分钟数据的格式（近似）
                    pseudo_min_data = []
                    for kline in timeframe15m:
                        for i in range(15):
                            pseudo_min_data.append({
                                'time': kline['time'],  # 使用相同的时间
                                'open': kline['open'],
                                'high': kline['high'],
                                'low': kline['low'],
                                'close': kline['close'],
                                'volume': kline['volume'] / 15  # 平均分配成交量
                            })
                    # 聚合为1小时K线
                    timeframe1h = forex_data_processor.aggregate_klines(pseudo_min_data, 60)
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 成功从15分钟数据聚合出{len(timeframe1h)}根1小时K线')
                else:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 15分钟数据不足，无法聚合为1小时数据')
                    # 尝试直接获取更多1小时数据
                    timeframe1h = get_aggregated_klines(60, 200)  # 尝试获取200根K线
            except Exception as e:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 聚合1小时数据失败: {e}')
                # 最后尝试直接获取更多1小时数据
                timeframe1h = get_aggregated_klines(60, 200)  # 尝试获取200根K线

        # 最终检查1小时数据是否足够
        if not timeframe1h:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 1小时K线数据为空，无法进行分析')
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 可能原因：数据库连接失败或数据不足')

            # 记录错误
            log_error(
                error_type=ErrorType.DATA_ERROR,
                message='1小时K线数据为空，无法进行分析',
                details={'timestamp': datetime.now().isoformat()},
                operation=OperationType.ANALYSIS
            )

            return None
        elif len(timeframe1h) < 20:  # 标准要求是20根K线
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 1小时K线数据较少，只有{len(timeframe1h)}根，无法进行分析')
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 系统需要至少20根1小时K线才能进行有效分析')

            # 记录错误
            log_error(
                error_type=ErrorType.DATA_ERROR,
                message=f'1小时K线数据不足，只有{len(timeframe1h)}根，无法进行分析',
                details={'timestamp': datetime.now().isoformat(), 'required': 20, 'actual': len(timeframe1h)},
                operation=OperationType.ANALYSIS
            )

            return None

        # 计算技术指标
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.4: 计算技术指标')

        # 如果是多货币对模式，从已计算的指标中提取
        if 'multi_pair_data' in locals() and multi_pair_data and primary_symbol in multi_pair_data['pairs_data']:
            indicators15m = multi_pair_data['pairs_data'][primary_symbol]['indicators_15m']
            indicators1h = multi_pair_data['pairs_data'][primary_symbol]['indicators_1h']
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 从多货币对数据中获取技术指标')
        else:
            # 传统模式：计算技术指标
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 计算15分钟周期技术指标')
            indicators15m = calculate_indicators(timeframe15m, '15min')

            # 检查技术指标是否计算成功
            if not indicators15m:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 计算15分钟周期技术指标失败，无法进行分析')
                return None

            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 计算1小时周期技术指标')
            indicators1h = calculate_indicators(timeframe1h, '1h')

            # 检查技术指标是否计算成功
            if not indicators1h:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 计算1小时周期技术指标失败，但仍将继续分析')
                # 创建一个基本的指标对象，表示数据不可用
                indicators1h = {
                    'timeframe': '1h',
                    'dataAvailable': False,
                    'message': '1小时周期技术指标计算失败'
                }

        # 检查数据时间与当前时间的差距
        now = datetime.now()
        if len(timeframe15m) > 0:
            try:
                # 尝试解析时间，支持多种格式
                try:
                    latest_data_time = datetime.strptime(timeframe15m[-1]["time"], "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    try:
                        latest_data_time = datetime.strptime(timeframe15m[-1]["time"], "%Y.%m.%d %H:%M")
                    except ValueError:
                        # 如果都失败，尝试更通用的方法
                        from dateutil import parser
                        latest_data_time = parser.parse(timeframe15m[-1]["time"])

                # 调整MT4时间到北京时间（GMT+8）
                # MT4时间通常是GMT+0，北京时间是GMT+8，差8小时
                mt4_to_beijing_hours = 8
                adjusted_data_time = latest_data_time + timedelta(hours=mt4_to_beijing_hours)

                # 计算调整后的时间差
                time_diff = now - adjusted_data_time
                time_diff_minutes = time_diff.total_seconds() / 60
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 15分钟K线最新数据时间(MT4)：{timeframe15m[-1]["time"]}')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 调整后时间(北京)：{adjusted_data_time.strftime("%Y-%m-%d %H:%M:%S")}')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 当前时间(北京)：{now.strftime("%Y-%m-%d %H:%M:%S")}')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 调整后时间差：{time_diff_minutes:.2f}分钟')

                # 如果数据时间差超过24小时，发出警告
                if time_diff_minutes > 1440:  # 24小时 = 1440分钟
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 警告：15分钟K线数据过旧，最新数据时间与当前时间相差{time_diff_minutes:.2f}分钟（{time_diff_minutes/60:.2f}小时）')
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 建议检查数据库更新情况')

                    # 记录警告
                    log_error(
                        error_type=ErrorType.DATA_ERROR,
                        message=f'15分钟K线数据过旧，最新数据时间与当前时间相差{time_diff_minutes:.2f}分钟',
                        details={'timestamp': datetime.now().isoformat(), 'latest_data_time': timeframe15m[-1]["time"], 'adjusted_data_time': adjusted_data_time.isoformat(), 'time_diff_minutes': time_diff_minutes},
                        operation=OperationType.ANALYSIS
                    )
            except Exception as e:
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告：无法解析15分钟K线数据时间 {timeframe15m[-1]["time"]}: {e}')

        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤2.3】基本面信息收集 - 获取相关新闻')
        news = get_relevant_news(10)

        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 获取相关日历事件')
        calendar = get_relevant_calendar(10)

        # MT4交互部分 - 关键部分，失败将中断分析
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.8: 获取MT4数据')

        # 获取当前价格 - 这是必须的
        current_price = None
        mt4_bid_price = None
        mt4_ask_price = None
        try:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.8.1: 获取当前价格')

            # 首先从K线数据中获取最新价格
            if timeframe15m and len(timeframe15m) > 0:
                latest_kline_price = float(timeframe15m[-1]['close'])
                latest_kline_time = timeframe15m[-1]['time']
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 从K线数据获取的最新价格: {latest_kline_price}，时间: {latest_kline_time}')

            # 确保MT4客户端已连接
            if not mt4_client.mt4_client.is_connected:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! MT4客户端未连接，尝试连接')
                connect_result = mt4_client.mt4_client.connect()
                if not connect_result:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! MT4客户端连接失败，无法获取实时价格数据')
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 将使用K线数据中的最新价格')

                    if timeframe15m and len(timeframe15m) > 0:
                        current_price = latest_kline_price
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 使用K线数据中的最新价格: {current_price}')
                    else:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! K线数据也不可用，无法获取价格')

                        # 记录错误
                        log_error(
                            error_type=ErrorType.CONNECTION_ERROR,
                            message='MT4客户端连接失败且K线数据不可用，无法获取价格数据',
                            details={'timestamp': datetime.now().isoformat()},
                            operation=OperationType.CONNECTION
                        )

                        # 返回None，中断分析，让调用方处理重试逻辑
                        return None
                else:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> MT4客户端连接成功')

            # 尝试从MT4获取实时价格（使用主要货币对）
            market_info_response = mt4_client.mt4_client.get_market_info(primary_symbol)
            if not market_info_response or market_info_response.get('status') != 'success':
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 获取MT4市场信息失败: {market_info_response}')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 将使用K线数据中的最新价格')

                if timeframe15m and len(timeframe15m) > 0:
                    current_price = latest_kline_price
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 使用K线数据中的最新价格: {current_price}')
                else:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! K线数据也不可用，无法获取价格')

                    # 记录错误
                    log_error(
                        error_type=ErrorType.MT4_ERROR,
                        message='获取MT4市场信息失败且K线数据不可用',
                        details={'response': market_info_response},
                        operation=OperationType.OTHER
                    )

                    # 返回None，中断分析，让调用方处理重试逻辑
                    return None
            else:
                # 从MT4获取实时价格成功
                mt4_bid_price = float(market_info_response['data']['bid'])
                mt4_ask_price = float(market_info_response['data']['ask'])
                mt4_price = (mt4_bid_price + mt4_ask_price) / 2
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> MT4实时价格: {mt4_price} (Bid: {mt4_bid_price}, Ask: {mt4_ask_price})')

                # 比较MT4价格和K线数据价格
                if timeframe15m and len(timeframe15m) > 0:
                    price_diff = abs(mt4_price - latest_kline_price)
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> MT4价格与K线数据价格差异: {price_diff}')

                # 使用MT4实时价格
                current_price = mt4_price
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 使用MT4实时价格: {current_price}')

            # 检查价格是否有效
            if current_price <= 0:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 获取到的价格无效: {current_price}')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 将在下次分析时重试获取价格')

                # 返回None，中断分析，让调用方处理重试逻辑
                return None
        except Exception as price_error:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 获取当前价格失败: {price_error}')
            traceback.print_exc()
            log_error(
                error_type=ErrorType.MT4_ERROR,
                message=f'获取当前价格失败: {price_error}',
                details={'exception': str(price_error)},
                operation=OperationType.OTHER
            )
            # 返回None，中断分析，让调用方处理重试逻辑
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 将在下次分析时重试获取价格')
            return None

        # 获取持仓和挂单信息 - 这些可以为空，但获取过程不应该出错
        try:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.8.2: 获取当前持仓')
            positions_response = mt4_client.mt4_client.get_active_orders()
            if not positions_response or positions_response.get('status') != 'success':
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 获取持仓失败: {positions_response}')
                primary_positions = []
            else:
                positions = positions_response.get('orders', [])
                # 过滤出主要货币对的持仓
                primary_positions = [p for p in positions if p.get('symbol') == primary_symbol]
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 当前{primary_symbol}持仓数量: {len(primary_positions)}')

            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.8.3: 获取挂单信息')
            pending_orders_response = mt4_client.mt4_client.get_pending_orders()
            if not pending_orders_response or pending_orders_response.get('status') != 'success':
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 获取挂单失败: {pending_orders_response}')
                primary_pending_orders = []
            else:
                pending_orders = pending_orders_response.get('orders', [])
                # 过滤出主要货币对的挂单
                primary_pending_orders = [p for p in pending_orders if p.get('symbol') == primary_symbol]
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 当前{primary_symbol}挂单数量: {len(primary_pending_orders)}')
        except Exception as mt4_error:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 获取持仓或挂单失败: {mt4_error}')
            traceback.print_exc()
            log_error(
                error_type=ErrorType.MT4_ERROR,
                message=f'获取持仓或挂单失败: {mt4_error}',
                details={'exception': str(mt4_error)},
                operation=OperationType.OTHER
            )
            # 持仓和挂单可以为空，所以这里不中断分析
            primary_positions = []
            primary_pending_orders = []

        # 获取历史分析记录
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.9: 获取历史分析记录')
        history_analysis = forex_analysis_history.get_recent_analysis_records(3)

        # 阶段3：LLM策略优化分析
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ===== 【阶段3：LLM策略优化分析】开始策略评估 =====')
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤3.1】市场条件评估 - 执行LLM策略优化分析')
        strategy_optimization = None
        if has_llm_strategy:
            try:
                # 构建市场数据用于策略优化
                market_data_for_strategy = {
                    'currentPrice': current_price,
                    'indicators': indicators15m,
                    'symbol': primary_symbol
                }

                # 执行策略优化
                strategy_optimization = llm_trading_strategy.optimize_for_llm_constraints(
                    market_data_for_strategy,
                    {'trend': indicators15m, 'momentum': indicators15m, 'volatility': indicators15m}
                )

                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤3.2】策略选择优化 - 策略优化完成: {strategy_optimization["strategy_name"]}策略')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> LLM效率评分: {strategy_optimization["llm_efficiency_score"]:.2f}')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 建议时间框架: {strategy_optimization["optimized_parameters"]["timeframe"]}')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 建议风险回报比: {strategy_optimization["optimized_parameters"]["risk_reward_ratio"]:.1f}:1')

                # 检查是否允许分析
                if not strategy_optimization['analysis_allowed']:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤3.3】分析时机控制 - 策略建议: {strategy_optimization["trading_recommendation"]["reasoning"]}')
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 跳过本次分析，等待更合适的时机')

                    # 构建跳过分析的结果
                    skip_result = {
                        'timestamp': datetime.now().isoformat(),
                        'symbol': primary_symbol,
                        'currentPrice': current_price,
                        'analysis': {
                            'preAnalysis': f"策略优化建议: {strategy_optimization['trading_recommendation']['reasoning']}",
                            'recommendation': "观望",
                            'technicalAnalysis': "策略优化决定跳过完整分析",
                            'fundamentalAnalysis': "策略优化决定跳过完整分析",
                            'marketSentiment': "策略优化决定跳过完整分析"
                        },
                        'tradeInstructions': {
                            'action': 'NONE',
                            'orderType': 'MARKET',
                            'entryPrice': None,
                            'stopLoss': None,
                            'takeProfit': None,
                            'lotSize': 0.01,
                            'riskLevel': 'LOW',
                            'reasoning': strategy_optimization['trading_recommendation']['reasoning'],
                            'orderManagement': [],
                            'isFinalDecision': True
                        },
                        'final': True,  # 标记为最终结果，避免被视为错误
                        'strategy_optimization': strategy_optimization
                    }

                    # 保存分析记录（需要处理不可序列化的对象）
                    try:
                        # 处理strategy_optimization中的不可序列化对象
                        if 'strategy_optimization' in skip_result and skip_result['strategy_optimization']:
                            # 将MarketConditionAdaptation对象转换为字典
                            strategy_opt = skip_result['strategy_optimization']
                            if 'market_condition_adaptation' in strategy_opt:
                                adaptation = strategy_opt['market_condition_adaptation']
                                if hasattr(adaptation, '__dict__'):
                                    strategy_opt['market_condition_adaptation'] = adaptation.__dict__
                                elif hasattr(adaptation, '_asdict'):
                                    strategy_opt['market_condition_adaptation'] = adaptation._asdict()
                                else:
                                    strategy_opt['market_condition_adaptation'] = str(adaptation)

                        # 递归处理所有不可序列化的对象
                        skip_result = make_json_serializable(skip_result)

                        forex_analysis_history.save_analysis_record(skip_result)
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 跳过分析的记录已保存')
                    except Exception as save_error:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 保存跳过分析记录失败: {save_error}')
                        # 即使保存失败，也要继续返回结果
                        pass

                    return skip_result

            except Exception as e:
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 策略优化失败: {e}')
                strategy_optimization = None
        else:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> LLM策略优化器不可用，使用默认策略')

        # 准备分析数据
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤2.11: 构建智能多货币对分析数据')

        # 构建基础分析数据
        analysis_data = {
            'symbol': primary_symbol,  # 使用动态选择的主要货币对
            'currentPrice': current_price,  # 已经获取并验证过的价格
            'timeframe15m': timeframe15m,
            'timeframe1h': timeframe1h,
            'indicators': indicators15m,  # 使用15分钟周期的基础指标
            'indicators1h': indicators1h,  # 添加1小时周期的指标
            'news': news,
            'calendar': calendar,
            'positions': primary_positions,  # 使用主要货币对的持仓
            'pendingOrders': primary_pending_orders,  # 使用主要货币对的挂单
            'historyAnalysis': history_analysis,
            'strategyOptimization': strategy_optimization  # 添加策略优化结果
        }

        # 如果是多货币对模式，添加额外的多货币对信息
        if 'multi_pair_data' in locals() and multi_pair_data:
            analysis_data.update({
                'multiPairMode': True,
                'selectedSymbols': target_symbols,
                'multiPairData': multi_pair_data,
                'correlationMatrix': multi_pair_data.get('correlation_matrix', {}),
                'portfolioRisk': multi_pair_data.get('portfolio_risk', {}),
                'pairCount': len(multi_pair_data.get('pairs_data', {}))
            })
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 多货币对模式：包含{len(target_symbols)}个货币对的组合分析数据')
        else:
            analysis_data.update({
                'multiPairMode': False,
                'selectedSymbols': [primary_symbol],
                'pairCount': 1
            })
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 传统模式：单货币对{primary_symbol}分析数据')

        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 分析数据准备完成')
        return analysis_data
    except Exception as error:
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 获取分析数据失败: {error}')
        traceback.print_exc()
        log_error(
            error_type=ErrorType.DATA_ERROR,
            message=f'获取分析数据失败: {error}',
            details={'exception': str(error)},
            operation=OperationType.OTHER
        )
        return None  # 如果出现任何错误，返回None，中断分析

def analyze_forex(force=False):
    """
    分析外汇

    Args:
        force (bool): 是否强制执行分析

    Returns:
        dict: 分析结果
    """
    try:
        # 阶段1：系统初始化与准备
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ===== 【阶段1：系统初始化与准备】开始外汇分析 =====')
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤1.1】系统启动检查 - 初始化和参数验证')
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 参数: force={force}')

        # 检查是否有最近的分析记录（用于判断是否是首次分析）
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤1.2】市场时间检查 - 检查最近分析记录')
        recent_analysis = forex_analysis_history.get_latest_analysis_record()
        is_first_analysis = recent_analysis is None

        if recent_analysis:
            record_time = datetime.fromisoformat(recent_analysis['timestamp'].replace('Z', '+00:00'))
            now = datetime.now()
            time_diff = now - record_time
            minutes_diff = time_diff.total_seconds() / 60

            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 找到最近的分析记录，时间: {record_time.strftime("%Y-%m-%d %H:%M:%S")}')
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 距离上次分析已经过去 {minutes_diff:.1f} 分钟')

            # 如果不是强制分析，且距离上次分析不到5分钟，跳过本次分析
            if not force and minutes_diff < 5:
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 距离上次分析不到5分钟，跳过本次分析')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 返回最近的分析记录')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ===== 外汇分析结束（使用缓存记录） =====')
                return recent_analysis
        else:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 未找到最近的分析记录，这是首次分析')

        # 阶段2：数据收集与预处理
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ===== 【阶段2：数据收集与预处理】开始数据获取 =====')
        analysis_data = get_analysis_data()

        # 检查分析数据是否获取成功
        if analysis_data is None:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 分析数据获取失败，中断分析')
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 可能原因：数据库连接失败、MT4连接失败或数据不足')
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ===== 外汇分析结束（数据获取失败） =====')

            # 记录错误
            log_error(
                error_type=ErrorType.DATA_ERROR,
                message='分析数据获取失败，中断分析',
                details={'timestamp': datetime.now().isoformat()},
                operation=OperationType.ANALYSIS
            )

            return None

        # 检查是否是策略优化跳过分析的结果
        if isinstance(analysis_data, dict) and analysis_data.get('final') is True:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 检测到策略优化跳过分析结果')
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 策略建议: {analysis_data.get("tradeInstructions", {}).get("reasoning", "等待更合适的时机")}')
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ===== 外汇分析结束（策略优化跳过） =====')
            return analysis_data

        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 分析数据获取完成')

        # 如果是首次分析或强制分析，设置force_analysis=True
        force_analysis = force or is_first_analysis
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 执行条件: force_analysis={force_analysis} (force={force}, is_first_analysis={is_first_analysis})')

        # 阶段4：深度LLM分析
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ===== 【阶段4：深度LLM分析】开始多轮分析 =====')

        # 检查是否使用多轮分析模式
        use_multi_round = True  # 可以通过配置文件或参数控制
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤4.1】分析数据准备 - 使用多轮分析模式: {use_multi_round}')

        if use_multi_round:
            try:
                # 执行多轮分析，传递force_analysis参数
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤4.2】多轮LLM分析执行 - 调用perform_multi_round_analysis')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 参数: force_analysis={force_analysis}')

                analysis_start_time = datetime.now()
                analysis_result = multi_round_analysis.perform_multi_round_analysis(analysis_data, force_analysis=force_analysis)
                analysis_end_time = datetime.now()
                analysis_duration = (analysis_end_time - analysis_start_time).total_seconds()

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤4.3】分析结果解析 - 多轮分析完成，耗时: {analysis_duration:.2f}秒')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 返回结果类型: {type(analysis_result)}')

                # 步骤3.2: 检查是否是预分析结果（跳过完整分析的结果）
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤3.2: 检查分析结果')
                if analysis_result.get('final') is True:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 检测到预分析结果，final={analysis_result.get("final")}')
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 预分析决定跳过完整分析，返回观望结果')

                    # 步骤3.3: 保存分析记录
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 步骤3.3: 保存分析记录')
                    forex_analysis_history.save_analysis_record(analysis_result)
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 分析记录已保存')

                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ===== 外汇分析结束（预分析观望） =====')
                    return analysis_result

                # 提取分析结果
                analysis_text = analysis_result['analysis']['final']
                trade_instructions = analysis_result['tradeInstructions']

                # 记录操作
                log_operation(
                    operation_type=OperationType.ANALYSIS,
                    success=True,
                    message='多轮分析完成',
                    parameters={'symbol': 'EURUSD'},
                    result={'status': 'success'}
                )
            except multi_round_analysis.AnalysisFallbackException as fallback_error:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到API调用失败，触发回退机制: {fallback_error}')

                # 记录错误
                log_error(
                    error_type=ErrorType.LLM_ERROR,
                    message=f'API调用失败，触发回退机制: {fallback_error}',
                    details={'fallback_info': fallback_error.fallback_info},
                    operation=OperationType.ANALYSIS
                )

                # 返回回退信息，通知调用者需要等待并重试
                fallback_result = {
                    'timestamp': datetime.now().isoformat(),
                    'symbol': 'EURUSD',
                    'status': 'FALLBACK_REQUIRED',
                    'message': '分析API调用失败，需要等待并重试',
                    'retry_after': fallback_error.fallback_info.get('retry_after', 300),  # 默认5分钟后重试
                    'error': str(fallback_error)
                }

                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ===== 外汇分析结束（API调用失败，需要回退） =====')
                return fallback_result

            except Exception as multi_round_error:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 多轮分析失败，回退到单轮分析: {multi_round_error}')

                # 记录错误
                log_error(
                    error_type=ErrorType.LLM_ERROR,
                    message=f'多轮分析失败，回退到单轮分析: {multi_round_error}',
                    details={'exception': str(multi_round_error)},
                    operation=OperationType.ANALYSIS
                )

                # 回退到单轮分析
                use_multi_round = False

        if not use_multi_round:
            # 生成提示词
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 生成LLM提示词')
            prompt = forex_llm_prompt.generate_forex_analysis_prompt(analysis_data)

            # 发送到LLM进行分析
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 发送到LLM进行分析')
            llm_response = llm_client.send_to_deepseek(prompt)

            # 提取分析结果
            analysis_text = llm_response['choices'][0]['message']['content']
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 收到LLM分析结果')

            # 解析交易指令
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 解析交易指令')
            trade_instructions = llm_client.parse_trade_instructions(analysis_text)

            # 记录操作
            log_operation(
                operation_type=OperationType.ANALYSIS,
                success=True,
                message='单轮分析完成',
                parameters={'symbol': 'EURUSD'},
                result={'status': 'success'}
            )

        # 当前价格已经在analysis_data中获取

        # 构建分析结果
        if use_multi_round:
            # 多轮分析结果已经包含了所有需要的信息
            result = analysis_result

            # 添加一些额外信息
            result['positions'] = analysis_data.get('positions', [])
            result['pendingOrders'] = analysis_data.get('pendingOrders', [])
        else:
            # 单轮分析结果
            result = {
                'timestamp': datetime.now().isoformat(),
                'symbol': 'EURUSD',
                'currentPrice': analysis_data.get('currentPrice'),
                'analysis': analysis_text,
                'tradeInstructions': trade_instructions,
                'data': analysis_data,
                'prompt': prompt,
                'positions': analysis_data.get('positions', []),
                'pendingOrders': analysis_data.get('pendingOrders', [])
            }

        # 阶段6：监控与反馈
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ===== 【阶段6：监控与反馈】保存分析记录 =====')
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤6.1】实时监控 - 保存分析记录')
        forex_analysis_history.save_analysis_record(result)
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 分析记录已保存')

        # 更新策略的最后分析时间
        if has_llm_strategy and llm_trading_strategy:
            try:
                llm_trading_strategy.update_last_analysis_time()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤6.2】性能评估 - 已更新LLM策略分析时间')
            except Exception as e:
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 更新策略分析时间失败: {e}')

        # 完成分析
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] >>> 【步骤6.3】学习与优化 - 分析流程完成')
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ===== 外汇分析结束（完整分析） =====')
        return result
    except Exception as error:
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 分析外汇失败: {error}')
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] !!! 错误类型: {type(error)}')
        traceback.print_exc()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ===== 外汇分析结束（出错） =====')
        raise


# 风险管理辅助函数
def _extract_signal_confidence(trade_instructions):
    """从交易指令中提取信号置信度"""
    reasoning = trade_instructions.get('reasoning', '').lower()

    # 基于关键词判断置信度
    if '高度确信' in reasoning or '强烈' in reasoning:
        return 0.9
    elif '确信' in reasoning or '明确' in reasoning:
        return 0.8
    elif '可能' in reasoning or '倾向' in reasoning:
        return 0.6
    elif '谨慎' in reasoning or '不确定' in reasoning:
        return 0.4
    else:
        # 基于信号强度判断
        signal_confidence = trade_instructions.get('signalConfidence', 'MEDIUM')
        confidence_map = {
            'HIGH': 0.8,
            'MEDIUM': 0.6,
            'LOW': 0.4
        }
        return confidence_map.get(signal_confidence, 0.6)

def _emergency_close_all_positions(positions):
    """紧急平仓所有持仓"""
    close_results = []

    try:
        for position in positions:
            position_id = position.get('position_id') or position.get('order_id')
            symbol = position.get('symbol', 'UNKNOWN')

            if position_id:
                try:
                    # 执行平仓
                    close_result = mt4_client.mt4_client.close_position(position_id)
                    close_results.append({
                        'position_id': position_id,
                        'symbol': symbol,
                        'success': close_result.get('status') == 'success',
                        'message': close_result.get('message', ''),
                        'timestamp': datetime.now().isoformat()
                    })
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 紧急平仓: {symbol} - {position_id}')
                except Exception as e:
                    close_results.append({
                        'position_id': position_id,
                        'symbol': symbol,
                        'success': False,
                        'message': f'平仓失败: {e}',
                        'timestamp': datetime.now().isoformat()
                    })
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 紧急平仓失败: {symbol} - {e}')

    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 紧急平仓过程出错: {e}')

    return close_results

def execute_trade_with_risk_management(trade_instructions, check_duplicate=True):
    """
    带风险管理的交易执行函数

    Args:
        trade_instructions (dict): 交易指令
        check_duplicate (bool): 是否检查重复订单

    Returns:
        dict: 交易执行结果
    """
    try:
        if not has_risk_manager:
            # 如果没有风险管理器，使用原有函数
            return execute_trade(trade_instructions, check_duplicate)

        # 1. 获取账户信息
        account_info = mt4_client.mt4_client.get_account_info()
        if not account_info or account_info.get('status') != 'success':
            return {
                'success': False,
                'message': '无法获取账户信息',
                'orderId': None
            }

        # 2. 获取当前持仓
        positions_response = mt4_client.mt4_client.get_active_orders()
        current_positions = positions_response.get('orders', []) if positions_response else []

        # 3. 获取市场数据
        market_data = {
            'current_price': trade_instructions.get('entryPrice', 1.1000),
            'atr': 0.0015,  # 默认ATR
            'spread': 2     # 默认点差
        }

        # 4. 市场状态自适应分析
        market_condition = None
        adaptive_params = None
        if has_market_adaptive:
            try:
                # 分析市场状态
                market_condition = market_adaptive.analyze_market_condition(market_data)

                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 市场状态分析完成:')
                print(f'   市场制度: {market_condition.regime.value}')
                print(f'   趋势强度: {market_condition.trend_strength:.2f}')
                print(f'   波动率水平: {market_condition.volatility_level:.2f}')
                print(f'   识别置信度: {market_condition.confidence:.2f}')

                # 策略自适应
                adaptive_params = market_adaptive.adapt_strategy(market_condition)

                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 策略自适应完成:')
                print(f'   推荐策略: {adaptive_params.strategy.value}')
                print(f'   仓位倍数: {adaptive_params.position_size_multiplier:.2f}')
                print(f'   入场门槛: {adaptive_params.entry_threshold:.2f}')
                print(f'   风险容忍度: {adaptive_params.risk_tolerance:.2f}')

            except Exception as e:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 市场自适应分析失败: {e}')
                market_condition = None
                adaptive_params = None

        # 5. 信号质量分析
        signal_quality = None
        if has_signal_analyzer:
            try:
                # 构建LLM分析数据
                llm_analysis = {
                    'reasoning': trade_instructions.get('reasoning', ''),
                    'confidence': _extract_signal_confidence(trade_instructions)
                }

                signal_quality = signal_analyzer.analyze_signal_quality(
                    market_data, llm_analysis, trade_instructions
                )

                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 信号质量分析完成:')
                print(f'   信号等级: {signal_quality.signal_grade.value}')
                print(f'   置信度评分: {signal_quality.confidence_score:.2f}')
                print(f'   技术评分: {signal_quality.technical_score.overall_score:.2f}')
                print(f'   LLM评分: {signal_quality.llm_score.overall_score:.2f}')
                print(f'   市场状态: {signal_quality.market_condition.value}')
                print(f'   风险回报比: {signal_quality.risk_reward_ratio:.2f}')

                # 检查信号是否应该执行
                should_execute_signal, signal_reason = signal_analyzer.should_execute_signal(signal_quality)
                if not should_execute_signal:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 信号质量控制: {signal_reason}')
                    return {
                        'success': False,
                        'message': f'信号质量控制: {signal_reason}',
                        'signal_grade': signal_quality.signal_grade.value,
                        'signal_quality': {
                            'grade': signal_quality.signal_grade.value,
                            'confidence': signal_quality.confidence_score,
                            'recommendation': signal_quality.recommendation,
                            'warnings': signal_quality.warnings
                        },
                        'orderId': None
                    }

            except Exception as e:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 信号质量分析失败: {e}')
                signal_quality = None

        # 5. 风险评估
        risk_metrics = risk_manager.assess_comprehensive_risk(
            account_info, current_positions, market_data
        )

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 风险评估完成:')
        print(f'   风险等级: {risk_metrics.risk_level.value}')
        print(f'   账户回撤: {risk_metrics.account_drawdown:.2%}')
        print(f'   日盈亏: {risk_metrics.daily_pnl:.2%}')
        print(f'   组合风险: {risk_metrics.portfolio_risk:.2%}')

        # 5. 检查是否允许交易
        can_trade, risk_reason = risk_manager.should_allow_trading(risk_metrics)

        if not can_trade:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 风险控制: {risk_reason}')

            # 检查是否需要紧急行动
            emergency_actions = risk_manager.get_emergency_actions(risk_metrics)
            if emergency_actions:
                print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 执行紧急风险控制措施:')
                for action in emergency_actions:
                    print(f'   - {action["action"]}: {action["reason"]}')

                    if action['action'] == 'CLOSE_ALL_POSITIONS':
                        # 执行紧急平仓
                        close_results = _emergency_close_all_positions(current_positions)
                        return {
                            'success': False,
                            'message': f'风险控制: {risk_reason}',
                            'risk_level': risk_metrics.risk_level.value,
                            'emergency_actions': emergency_actions,
                            'close_results': close_results,
                            'orderId': None
                        }

            return {
                'success': False,
                'message': f'风险控制: {risk_reason}',
                'risk_level': risk_metrics.risk_level.value,
                'risk_metrics': {
                    'account_drawdown': risk_metrics.account_drawdown,
                    'daily_pnl': risk_metrics.daily_pnl,
                    'portfolio_risk': risk_metrics.portfolio_risk
                },
                'orderId': None
            }

        # 6. 计算安全仓位大小
        signal_confidence = _extract_signal_confidence(trade_instructions)
        market_volatility = market_data.get('atr', 0.0015)
        account_balance = account_info.get('balance', 10000)

        # 基础仓位计算
        safe_position_size = risk_manager.calculate_optimal_position_size(
            signal_confidence, market_volatility, risk_metrics, account_balance
        )

        # 基于市场自适应进一步调整仓位
        if adaptive_params and has_market_adaptive:
            market_multiplier = adaptive_params.position_size_multiplier
            safe_position_size = safe_position_size * market_multiplier

            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 市场自适应仓位调整:')
            print(f'   市场自适应倍数: {market_multiplier:.2f}')
            print(f'   调整后仓位: {safe_position_size:.3f}')

        # 基于信号质量进一步调整仓位
        if signal_quality and has_signal_analyzer:
            signal_multiplier = signal_analyzer.get_position_size_multiplier(signal_quality)
            safe_position_size = safe_position_size * signal_multiplier

            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 信号质量仓位调整:')
            print(f'   信号质量倍数: {signal_multiplier:.2f}')
            print(f'   最终调整仓位: {safe_position_size:.3f}')

        # 7. 调整交易指令
        adjusted_instructions = trade_instructions.copy()
        original_lot_size = adjusted_instructions.get('lotSize', 0.1)
        adjusted_instructions['lotSize'] = safe_position_size

        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 仓位调整:')
        print(f'   原始仓位: {original_lot_size:.3f}')
        print(f'   调整后仓位: {safe_position_size:.3f}')
        print(f'   信号置信度: {signal_confidence:.2f}')

        # 8. 执行交易
        result = execute_trade(adjusted_instructions, check_duplicate)

        # 9. 更新风险管理器状态
        if result.get('success'):
            trade_result = {
                'profit_loss': 0,  # 开仓时为0
                'order_id': result.get('orderId'),
                'action': adjusted_instructions.get('action'),
                'lot_size': safe_position_size
            }
            risk_manager.update_trade_result(trade_result)

            # 添加风险管理和信号质量信息到结果
            result['risk_management'] = {
                'risk_level': risk_metrics.risk_level.value,
                'position_adjusted': abs(safe_position_size - original_lot_size) > 0.001,
                'original_size': original_lot_size,
                'adjusted_size': safe_position_size,
                'risk_metrics': {
                    'account_drawdown': risk_metrics.account_drawdown,
                    'portfolio_risk': risk_metrics.portfolio_risk
                }
            }

            # 添加市场自适应信息
            if market_condition and adaptive_params:
                result['market_adaptive'] = {
                    'market_regime': market_condition.regime.value,
                    'trend_strength': market_condition.trend_strength,
                    'volatility_level': market_condition.volatility_level,
                    'confidence': market_condition.confidence,
                    'recommended_strategy': adaptive_params.strategy.value,
                    'timeframe_weight': adaptive_params.timeframe_weight.value,
                    'position_multiplier': adaptive_params.position_size_multiplier,
                    'entry_threshold': adaptive_params.entry_threshold,
                    'risk_tolerance': adaptive_params.risk_tolerance,
                    'holding_preference': adaptive_params.holding_period_preference
                }

            # 添加信号质量信息
            if signal_quality:
                result['signal_quality'] = {
                    'grade': signal_quality.signal_grade.value,
                    'confidence_score': signal_quality.confidence_score,
                    'technical_score': signal_quality.technical_score.overall_score,
                    'llm_score': signal_quality.llm_score.overall_score,
                    'market_condition': signal_quality.market_condition.value,
                    'signal_type': signal_quality.signal_type.value,
                    'risk_reward_ratio': signal_quality.risk_reward_ratio,
                    'recommendation': signal_quality.recommendation,
                    'warnings': signal_quality.warnings
                }

        return result

    except Exception as error:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 风险管理交易执行失败: {error}')
        # 如果风险管理失败，回退到原有函数
        return execute_trade(trade_instructions, check_duplicate)

def execute_trade(trade_instructions, check_duplicate=True):
    """
    执行交易

    Args:
        trade_instructions (dict): 交易指令
        check_duplicate (bool): 是否检查重复订单

    Returns:
        dict: 交易结果
    """
    try:
        now = datetime.now()
        # 使用更简洁的格式打印交易指令，避免日志过长被截断
        action = trade_instructions.get('action', 'NONE')
        order_type = trade_instructions.get('orderType', 'MARKET')
        entry_price = trade_instructions.get('entryPrice')
        stop_loss = trade_instructions.get('stopLoss')
        take_profit = trade_instructions.get('takeProfit')
        lot_size = trade_instructions.get('lotSize', 0.01)
        risk_level = trade_instructions.get('riskLevel', 'MEDIUM')
        order_management_count = len(trade_instructions.get('orderManagement', []))

        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行交易: {action} {order_type}, 入场={entry_price}, 止损={stop_loss}, 止盈={take_profit}, 手数={lot_size}, 风险={risk_level}, 订单管理操作数={order_management_count}')

        # 如果需要完整的交易指令，可以使用json格式化输出
        if order_management_count > 0:
            import json
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 订单管理详情: {json.dumps(trade_instructions.get("orderManagement", []), ensure_ascii=False)}')

        # 处理订单管理指令
        order_management = trade_instructions.get('orderManagement', [])
        management_results = []

        # 检查是否是最终决策的交易指令
        is_final_decision = trade_instructions.get('isFinalDecision', False)
        now = datetime.now()
        if not is_final_decision:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告：这不是最终决策的交易指令，但仍将执行')
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 交易指令详情: {trade_instructions}')
        else:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 确认这是最终决策的交易指令')

        if order_management:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行订单管理操作，共 {len(order_management)} 个操作')

            # 确保MT4客户端已连接
            if not mt4_client.mt4_client.is_connected:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端未连接，尝试连接')
                connected = mt4_client.mt4_client.connect()
                if not connected:
                    return {
                        'success': False,
                        'message': '无法连接到MT4客户端',
                        'orderId': None
                    }

            # 使用执行优化器
            use_optimizer = True
            try:
                from app.utils.execution_optimizer import ExecutionOptimizer
                optimizer = ExecutionOptimizer(mt4_client.mt4_client)
                management_results = optimizer.optimize_order_management_execution(order_management)
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ✅ 使用执行优化器完成订单管理')
            except ImportError:
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ⚠️ 执行优化器不可用，使用原有逻辑')
                use_optimizer = False
            except Exception as e:
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ❌ 执行优化器失败: {e}，回退到原有逻辑')
                use_optimizer = False

            # 如果优化器不可用，使用原有逻辑
            if not use_optimizer:
                # 获取当前所有订单ID（活跃订单和挂单）
                all_order_ids = []

            # 获取活跃订单
            positions_response = mt4_client.mt4_client.get_active_orders()
            positions = positions_response.get('orders', [])
            for position in positions:
                all_order_ids.append(str(position.get('order_id')))

            # 获取挂单
            pending_orders_response = mt4_client.mt4_client.get_pending_orders()
            pending_orders = pending_orders_response.get('orders', [])
            for order in pending_orders:
                all_order_ids.append(str(order.get('order_id')))

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 当前EURUSD持仓数量: {len(positions)}')

            # 执行每个订单管理操作
            management_results = []
            for order_action in order_management:
                action_type = order_action.get('action')
                order_id = order_action.get('orderId')
                reason = order_action.get('reason', '')

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行订单操作: {action_type}, 订单ID: {order_id}, 原因: {reason}')

                # 检查订单是否存在
                if str(order_id) not in all_order_ids:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 订单ID {order_id} 不存在，跳过操作')

                    # 记录错误
                    log_error(
                        error_type=ErrorType.MT4_ERROR,
                        message=f'订单ID {order_id} 不存在，跳过操作',
                        details={'order_id': order_id, 'action': action_type},
                        operation=OperationType.ORDER_MANAGEMENT
                    )

                    # 添加到结果中
                    management_results.append({
                        'action': action_type,
                        'orderId': order_id,
                        'success': False,
                        'message': f'订单ID {order_id} 不存在'
                    })

                    # 跳过此操作
                    continue

                if action_type == 'MODIFY':
                    # 修改订单
                    new_stop_loss = order_action.get('newStopLoss')
                    new_take_profit = order_action.get('newTakeProfit')
                    new_entry_price = order_action.get('newEntryPrice')

                    # 获取订单信息，确定是持仓还是挂单
                    is_pending = False
                    order_info = None

                    # 先检查活跃订单
                    positions_response = mt4_client.mt4_client.get_active_orders()
                    positions = positions_response.get('orders', [])
                    for position in positions:
                        if str(position.get('order_id')) == str(order_id):
                            order_info = position
                            is_pending = False
                            break

                    # 如果不是活跃订单，检查挂单
                    if not order_info:
                        pending_orders_response = mt4_client.mt4_client.get_pending_orders()
                        pending_orders = pending_orders_response.get('orders', [])
                        for order in pending_orders:
                            if str(order.get('order_id')) == str(order_id):
                                order_info = order
                                is_pending = True
                                break

                    if order_info:
                        if is_pending:
                            # 修改挂单
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改挂单: {order_id}, 新入场价: {new_entry_price}, 新止损: {new_stop_loss}, 新止盈: {new_take_profit}')

                            # 首先修改止损和止盈
                            modify_result = mt4_client.mt4_client.modify_order(
                                order_id,
                                new_stop_loss,
                                new_take_profit
                            )

                            # 如果需要修改入场价格，需要先删除原挂单，然后重新创建
                            if new_entry_price is not None:
                                # 获取挂单详细信息
                                order_type = order_info.get('type', '')
                                symbol = order_info.get('symbol', 'EURUSD')
                                lots = order_info.get('lots', 0.01)
                                comment = order_info.get('comment', '')

                                # 如果修改止损止盈成功，并且需要修改入场价格
                                if modify_result.get('status') == 'success':
                                    now = datetime.now()
                                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改入场价格需要重新创建挂单，先删除原挂单')

                                    # 删除原挂单
                                    delete_result = mt4_client.mt4_client.delete_order(order_id)

                                    if delete_result.get('status') == 'success':
                                        now = datetime.now()
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 原挂单删除成功，重新创建挂单')

                                        # 根据订单类型重新创建挂单
                                        if order_type == 'BUYLIMIT':
                                            new_order_result = mt4_client.mt4_client.buy_limit(
                                                symbol, lots, new_entry_price, new_stop_loss, new_take_profit, comment
                                            )
                                        elif order_type == 'SELLLIMIT':
                                            new_order_result = mt4_client.mt4_client.sell_limit(
                                                symbol, lots, new_entry_price, new_stop_loss, new_take_profit, comment
                                            )
                                        elif order_type == 'BUYSTOP':
                                            new_order_result = mt4_client.mt4_client.buy_stop(
                                                symbol, lots, new_entry_price, new_stop_loss, new_take_profit, comment
                                            )
                                        elif order_type == 'SELLSTOP':
                                            new_order_result = mt4_client.mt4_client.sell_stop(
                                                symbol, lots, new_entry_price, new_stop_loss, new_take_profit, comment
                                            )
                                        else:
                                            new_order_result = {
                                                'status': 'error',
                                                'message': f'未知的订单类型: {order_type}'
                                            }

                                        # 更新修改结果
                                        if new_order_result.get('status') == 'success':
                                            modify_result = {
                                                'status': 'success',
                                                'message': '修改挂单成功（删除并重新创建）',
                                                'old_order_id': order_id,
                                                'new_order_id': new_order_result.get('order_id')
                                            }
                                        else:
                                            modify_result = {
                                                'status': 'error',
                                                'message': f'重新创建挂单失败: {new_order_result.get("message")}',
                                                'details': new_order_result
                                            }
                                    else:
                                        modify_result = {
                                            'status': 'error',
                                            'message': f'删除原挂单失败: {delete_result.get("message")}',
                                            'details': delete_result
                                        }
                            management_results.append({
                                'action': 'MODIFY',
                                'orderId': order_id,
                                'success': modify_result.get('status') == 'success',
                                'message': modify_result.get('message', ''),
                                'details': modify_result
                            })
                        else:
                            # 修改持仓
                            now = datetime.now()

                            # 获取当前价格，用于后续计算
                            current_price = None
                            market_info_response = mt4_client.mt4_client.get_market_info('EURUSD')
                            if market_info_response and market_info_response.get('status') == 'success':
                                current_price = float(market_info_response['data']['ask'])

                            # 获取订单类型（买入或卖出）
                            order_type = order_info.get('type', '').upper()

                            # 检查止损是否为0，如果是，设置为安全的默认值
                            if new_stop_loss == 0 or new_stop_loss is None:
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 止损为0或未设置，这是非常危险的！尝试设置安全的默认止损')

                                if current_price is not None:
                                    # 根据订单类型设置合理的止损
                                    if 'BUY' in order_type:
                                        # 买单，止损设置在当前价格下方100点
                                        new_stop_loss = current_price - 0.01
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为买单设置默认止损: {new_stop_loss}')
                                    elif 'SELL' in order_type:
                                        # 卖单，止损设置在当前价格上方100点
                                        new_stop_loss = current_price + 0.01
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为卖单设置默认止损: {new_stop_loss}')
                                    else:
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 未知订单类型: {order_type}，无法设置默认止损')
                                        # 保持原有止损
                                        new_stop_loss = float(order_info.get('sl', 0))
                                        if new_stop_loss == 0:
                                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 原有止损也为0，这是非常危险的！')
                                else:
                                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 无法获取当前价格，无法设置默认止损')

                            # 检查风险回报比
                            if current_price is not None and new_stop_loss is not None and new_take_profit is not None:
                                # 对于已有订单，我们使用订单的开仓价作为入场价
                                entry_price = float(order_info.get('open_price', current_price))

                                if 'BUY' in order_type:
                                    risk = entry_price - new_stop_loss
                                    reward = new_take_profit - entry_price
                                    risk_reward_ratio = reward / risk if risk > 0 else 0
                                elif 'SELL' in order_type:
                                    risk = new_stop_loss - entry_price
                                    reward = entry_price - new_take_profit
                                    risk_reward_ratio = reward / risk if risk > 0 else 0
                                else:
                                    risk_reward_ratio = 0

                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 入场价: {entry_price}')
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 止损: {new_stop_loss}, 止盈: {new_take_profit}')
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 风险: {risk:.5f}, 回报: {reward:.5f}')
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 风险回报比: {risk_reward_ratio:.2f}')

                                # 记录风险回报比信息，但不强制调整
                                if risk_reward_ratio > 0:
                                    # 如果风险回报比小于0.5（即风险是回报的2倍以上），发出警告
                                    if risk_reward_ratio < 0.5:
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 风险回报比 ({risk_reward_ratio:.2f}) 过低，风险是回报的2倍以上')
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 建议: 考虑调整止损或止盈以改善风险回报比')

                                        # 记录到交易日志，但不调整
                                        log_operation(
                                            operation_type=OperationType.OTHER,
                                            success=True,
                                            message=f'风险回报比警告: {risk_reward_ratio:.2f}，风险是回报的2倍以上',
                                            parameters={'order_id': order_id, 'risk_reward_ratio': risk_reward_ratio},
                                            result={}
                                        )

                                    # 如果风险回报比在0.5-1.0之间，发出提示
                                    elif risk_reward_ratio < 1.0:
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提示: 风险回报比 ({risk_reward_ratio:.2f}) 较低，风险略大于回报')

                                    # 如果风险回报比在1.0-1.5之间，发出良好提示
                                    elif risk_reward_ratio < 1.5:
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提示: 风险回报比 ({risk_reward_ratio:.2f}) 良好')

                                    # 如果风险回报比在1.5-5.0之间，发出优秀提示
                                    elif risk_reward_ratio <= 5.0:
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提示: 风险回报比 ({risk_reward_ratio:.2f}) 优秀')

                                    # 如果风险回报比过高（大于5），可能表示止损设置过远，发出警告
                                    else:
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 风险回报比 ({risk_reward_ratio:.2f}) 过高，可能表示止损设置过远')
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 建议: 考虑将止损设置在更接近的技术位置')

                                        # 记录到交易日志，但不调整
                                        log_operation(
                                            operation_type=OperationType.OTHER,
                                            success=True,
                                            message=f'风险回报比警告: {risk_reward_ratio:.2f}，可能表示止损设置过远',
                                            parameters={'order_id': order_id, 'risk_reward_ratio': risk_reward_ratio},
                                            result={}
                                        )
                                else:
                                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 无法计算风险回报比，请检查止损和止盈设置')

                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改持仓: {order_id}, 新止损: {new_stop_loss}, 新止盈: {new_take_profit}')
                            modify_result = mt4_client.mt4_client.modify_position(
                                order_id,
                                new_stop_loss,
                                new_take_profit
                            )
                            management_results.append({
                                'action': 'MODIFY',
                                'orderId': order_id,
                                'success': modify_result.get('status') == 'success',
                                'message': modify_result.get('message', ''),
                                'details': modify_result
                            })
                    else:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 未找到订单: {order_id}')
                        management_results.append({
                            'action': 'MODIFY',
                            'orderId': order_id,
                            'success': False,
                            'message': '未找到订单'
                        })

                elif action_type == 'DELETE':
                    # 删除挂单
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 删除挂单: {order_id}')
                    delete_result = mt4_client.mt4_client.delete_order(order_id)
                    management_results.append({
                        'action': 'DELETE',
                        'orderId': order_id,
                        'success': delete_result.get('status') == 'success',
                        'message': delete_result.get('message', ''),
                        'details': delete_result
                    })

                elif action_type == 'CLOSE':
                    # 平仓
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 平仓: {order_id}')
                    close_result = mt4_client.mt4_client.close_order(order_id)
                    management_results.append({
                        'action': 'CLOSE',
                        'orderId': order_id,
                        'success': close_result.get('status') == 'success',
                        'message': close_result.get('message', ''),
                        'details': close_result
                    })

            # 如果只有订单管理操作，没有新的交易指令，则返回订单管理结果
            if not trade_instructions or trade_instructions.get('action') == 'NONE':
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 订单管理操作完成，无新交易指令')
                return {
                    'success': True,
                    'message': '订单管理操作完成',
                    'orderManagementResults': management_results
                }

        # 如果没有订单管理操作，且交易指令为观望，则直接返回
        if not order_management and (not trade_instructions or trade_instructions.get('action') == 'NONE'):
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 交易指令为观望或无效，不执行交易')
            return {
                'success': True,
                'message': '交易指令为观望，不执行交易',
                'orderId': None
            }

        # 获取当前持仓
        try:
            # 确保MT4客户端已连接
            if not mt4_client.mt4_client.is_connected:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端未连接，尝试连接')
                mt4_client.mt4_client.connect()

            positions_response = mt4_client.mt4_client.get_active_orders()
            positions = positions_response.get('orders', [])

            # 过滤出EURUSD的持仓
            eurusd_positions = [p for p in positions if p.get('symbol') == 'EURUSD']

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 当前EURUSD持仓数量: {len(eurusd_positions)}')

            # 检查持仓管理建议
            position_management = trade_instructions.get('positionManagement')
            if position_management and eurusd_positions:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 持仓管理建议: {position_management}')

                # 如果建议平仓，先平掉所有持仓
                if position_management == 'CLOSE':
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行平仓操作')
                    for position in eurusd_positions:
                        position_id = position.get('order_id')
                        position_type = position.get('type')

                        if 'BUY' in position_type.upper() or 'SELL' in position_type.upper():
                            close_result = mt4_client.mt4_client.close_order(position_id)
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 平仓结果: {close_result}')

                            # 如果平仓成功，更新虚拟账户
                            if close_result.get('status') == 'success':
                                now = datetime.now()
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 平仓成功，更新虚拟账户')

                                # 获取当前价格
                                market_info_response = mt4_client.mt4_client.get_market_info('EURUSD')
                                if market_info_response and market_info_response.get('status') == 'success':
                                    current_price = float(market_info_response['data']['bid'])  # 使用bid价格作为平仓价格

                                    # 更新虚拟账户中的交易状态
                                    update_result = update_trade_status(
                                        position_id,
                                        TradeStatus.CLOSED,
                                        current_price,
                                        datetime.now().isoformat()
                                    )

                                    if update_result:
                                        now = datetime.now()
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 虚拟账户更新成功')

                                        # 更新订单结果分析
                                        update_order_results()
                                        now = datetime.now()
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 订单结果分析更新成功')
                                    else:
                                        now = datetime.now()
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 虚拟账户更新失败')
                                else:
                                    now = datetime.now()
                                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取当前价格失败，无法更新虚拟账户')

                    # 如果只是平仓，不开新仓，则返回
                    if trade_instructions.get('action') == 'CLOSE':
                        return {
                            'success': True,
                            'message': '已平掉所有持仓',
                            'orderId': None
                        }

                # 如果建议减仓，平掉部分持仓
                elif position_management == 'REDUCE':
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行减仓操作')
                    # 简单起见，只平掉一个持仓
                    if eurusd_positions:
                        position = eurusd_positions[0]
                        position_id = position.get('order_id')
                        position_type = position.get('type')

                        if 'BUY' in position_type.upper() or 'SELL' in position_type.upper():
                            close_result = mt4_client.mt4_client.close_order(position_id)
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 减仓结果: {close_result}')

                            # 如果减仓成功，更新虚拟账户
                            if close_result.get('status') == 'success':
                                now = datetime.now()
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 减仓成功，更新虚拟账户')

                                # 获取当前价格
                                market_info_response = mt4_client.mt4_client.get_market_info('EURUSD')
                                if market_info_response and market_info_response.get('status') == 'success':
                                    current_price = float(market_info_response['data']['bid'])  # 使用bid价格作为平仓价格

                                    # 更新虚拟账户中的交易状态
                                    update_result = update_trade_status(
                                        position_id,
                                        TradeStatus.CLOSED,
                                        current_price,
                                        datetime.now().isoformat()
                                    )

                                    if update_result:
                                        now = datetime.now()
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 虚拟账户更新成功')

                                        # 更新订单结果分析
                                        update_order_results()
                                        now = datetime.now()
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 订单结果分析更新成功')
                                    else:
                                        now = datetime.now()
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 虚拟账户更新失败')
                                else:
                                    now = datetime.now()
                                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取当前价格失败，无法更新虚拟账户')

                # 如果建议保持，不执行新交易
                elif position_management == 'HOLD':
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 保持现有持仓，不执行新交易')
                    return {
                        'success': True,
                        'message': '保持现有持仓，不执行新交易',
                        'orderId': None
                    }

                # 如果建议加仓，继续执行新交易
                elif position_management == 'ADD':
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行加仓操作')
                    # 继续执行下面的交易逻辑

            # 检查是否有反向持仓
            if eurusd_positions:
                action = trade_instructions.get('action')
                has_opposite_position = False

                for position in eurusd_positions:
                    position_type = position.get('type', '').upper()

                    if action == 'BUY' and 'SELL' in position_type:
                        has_opposite_position = True
                    elif action == 'SELL' and 'BUY' in position_type:
                        has_opposite_position = True

                if has_opposite_position:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 存在反向持仓，但LLM仍建议执行{action}操作')

            # 检查是否有同向持仓过多
            if eurusd_positions:
                action = trade_instructions.get('action')
                same_direction_count = 0

                for position in eurusd_positions:
                    position_type = position.get('type', '').upper()

                    if action == 'BUY' and 'BUY' in position_type:
                        same_direction_count += 1
                    elif action == 'SELL' and 'SELL' in position_type:
                        same_direction_count += 1

                if same_direction_count >= 3:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 已有{same_direction_count}个同向持仓，但LLM仍建议继续{action}')

        except Exception as position_error:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取持仓信息出错: {position_error}')

        # 处理订单管理指令
        order_management = trade_instructions.get('orderManagement', [])
        if order_management:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行订单管理操作，共 {len(order_management)} 个操作')

            # 确保MT4客户端已连接
            if not mt4_client.mt4_client.is_connected:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] MT4客户端未连接，尝试连接')
                connected = mt4_client.mt4_client.connect()
                if not connected:
                    return {
                        'success': False,
                        'message': '无法连接到MT4客户端',
                        'orderId': None
                    }

            # 获取当前所有订单ID（活跃订单和挂单）
            all_order_ids = []

            # 获取活跃订单
            positions_response = mt4_client.mt4_client.get_active_orders()
            positions = positions_response.get('orders', [])
            for position in positions:
                all_order_ids.append(str(position.get('order_id')))

            # 获取挂单
            pending_orders_response = mt4_client.mt4_client.get_pending_orders()
            pending_orders = pending_orders_response.get('orders', [])
            for order in pending_orders:
                all_order_ids.append(str(order.get('order_id')))

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 当前系统中的订单ID: {all_order_ids}')

            # 执行每个订单管理操作
            management_results = []
            for order_action in order_management:
                action_type = order_action.get('action')
                order_id = order_action.get('orderId')
                reason = order_action.get('reason', '')

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行订单操作: {action_type}, 订单ID: {order_id}, 原因: {reason}')

                # 记录操作
                log_operation(
                    operation_type=OperationType.ORDER_MANAGEMENT,
                    message=f'执行订单操作: {action_type}, 订单ID: {order_id}',
                    details={'action': action_type, 'order_id': order_id, 'reason': reason}
                )

                # 检查订单ID是否包含描述性文本
                order_id_str = str(order_id)
                if '（' in order_id_str or '(' in order_id_str or 'BUY' in order_id_str or 'SELL' in order_id_str:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 订单ID "{order_id}" 包含描述性文本，不是有效的订单ID')

                    # 记录错误
                    log_error(
                        error_type=ErrorType.MT4_ERROR,
                        message=f'订单ID "{order_id}" 包含描述性文本，不是有效的订单ID',
                        details={'order_id': order_id, 'action': action_type},
                        operation=OperationType.ORDER_MANAGEMENT
                    )

                    # 添加到结果中
                    management_results.append({
                        'action': action_type,
                        'orderId': order_id,
                        'success': False,
                        'message': f'订单ID "{order_id}" 包含描述性文本，不是有效的订单ID'
                    })

                    # 跳过此操作
                    continue

                # 检查订单是否存在 - 改进版本，更宽松的匹配
                order_exists = False
                matched_order_id = None

                # 精确匹配
                if order_id_str in all_order_ids:
                    order_exists = True
                    matched_order_id = order_id_str
                else:
                    # 模糊匹配：检查是否有订单ID包含或被包含
                    for existing_id in all_order_ids:
                        if order_id_str in existing_id or existing_id in order_id_str:
                            order_exists = True
                            matched_order_id = existing_id
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 订单ID模糊匹配: {order_id} -> {existing_id}')
                            break

                if not order_exists:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 订单ID {order_id} 不存在于当前订单列表中')
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 当前可用订单ID: {all_order_ids}')

                    # 如果是DELETE操作，可能订单已经被删除，这是正常的
                    if action_type == 'DELETE':
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] DELETE操作：订单可能已被删除，标记为成功')
                        management_results.append({
                            'action': action_type,
                            'orderId': order_id,
                            'success': True,
                            'message': f'订单ID {order_id} 可能已被删除（正常情况）'
                        })
                        continue
                    else:
                        # 记录错误但不跳过，尝试执行
                        log_error(
                            error_type=ErrorType.MT4_ERROR,
                            message=f'订单ID {order_id} 不存在，但仍尝试执行操作',
                            details={'order_id': order_id, 'action': action_type, 'available_orders': all_order_ids},
                            operation=OperationType.ORDER_MANAGEMENT
                        )
                        matched_order_id = order_id_str  # 使用原始ID尝试执行
                else:
                    # 使用匹配到的订单ID
                    order_id = matched_order_id

                if action_type == 'MODIFY':
                    # 修改订单
                    new_stop_loss = order_action.get('newStopLoss')
                    new_take_profit = order_action.get('newTakeProfit')
                    new_entry_price = order_action.get('newEntryPrice')

                    # 获取订单信息，确定是持仓还是挂单
                    is_pending = False
                    order_info = None

                    # 先检查活跃订单
                    positions_response = mt4_client.mt4_client.get_active_orders()
                    positions = positions_response.get('orders', [])
                    for position in positions:
                        if str(position.get('order_id')) == str(order_id):
                            order_info = position
                            is_pending = False
                            break

                    # 如果不是活跃订单，检查挂单
                    if not order_info:
                        pending_orders_response = mt4_client.mt4_client.get_pending_orders()
                        pending_orders = pending_orders_response.get('orders', [])
                        for order in pending_orders:
                            if str(order.get('order_id')) == str(order_id):
                                order_info = order
                                is_pending = True
                                break

                    if order_info:
                        if is_pending:
                            # 修改挂单
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改挂单: {order_id}, 新入场价: {new_entry_price}, 新止损: {new_stop_loss}, 新止盈: {new_take_profit}')

                            # 如果需要修改入场价格，需要删除原挂单并重新创建
                            if new_entry_price is not None:
                                now = datetime.now()
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改入场价格需要重新创建挂单，先删除原挂单')

                                # 保存原挂单信息
                                order_symbol = order_info.get('symbol')
                                order_type = order_info.get('type')
                                order_lots = float(order_info.get('lots', 0.01))

                                # 删除原挂单
                                delete_result = mt4_client.mt4_client.delete_order(order_id)

                                # 检查删除结果
                                if delete_result.get('status') == 'success':
                                    now = datetime.now()
                                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 原挂单删除成功，重新创建挂单')

                                    # 重新创建挂单
                                    new_order_result = None
                                    if order_type == 'BUYLIMIT':
                                        print(f'执行买入限价单: {order_symbol}, 手数: {order_lots}, 价格: {new_entry_price}, 止损: {new_stop_loss}, 止盈: {new_take_profit}')
                                        new_order_result = mt4_client.mt4_client.buy_limit(order_symbol, order_lots, new_entry_price, new_stop_loss, new_take_profit)
                                    elif order_type == 'SELLLIMIT':
                                        print(f'执行卖出限价单: {order_symbol}, 手数: {order_lots}, 价格: {new_entry_price}, 止损: {new_stop_loss}, 止盈: {new_take_profit}')
                                        new_order_result = mt4_client.mt4_client.sell_limit(order_symbol, order_lots, new_entry_price, new_stop_loss, new_take_profit)
                                    elif order_type == 'BUYSTOP':
                                        print(f'执行买入止损单: {order_symbol}, 手数: {order_lots}, 价格: {new_entry_price}, 止损: {new_stop_loss}, 止盈: {new_take_profit}')
                                        new_order_result = mt4_client.mt4_client.buy_stop(order_symbol, order_lots, new_entry_price, new_stop_loss, new_take_profit)
                                    elif order_type == 'SELLSTOP':
                                        print(f'执行卖出止损单: {order_symbol}, 手数: {order_lots}, 价格: {new_entry_price}, 止损: {new_stop_loss}, 止盈: {new_take_profit}')
                                        new_order_result = mt4_client.mt4_client.sell_stop(order_symbol, order_lots, new_entry_price, new_stop_loss, new_take_profit)

                                    # 检查创建结果
                                    if new_order_result and new_order_result.get('status') == 'success':
                                        new_order_id = new_order_result.get('order_id', '')
                                        now = datetime.now()
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 新挂单创建成功，订单ID: {new_order_id}')

                                        # 添加结果
                                        management_results.append({
                                            'action': 'MODIFY',
                                            'orderId': order_id,
                                            'success': True,
                                            'message': '修改挂单成功（删除并重新创建）',
                                            'details': {
                                                'status': 'success',
                                                'message': '修改挂单成功（删除并重新创建）',
                                                'old_order_id': order_id,
                                                'new_order_id': new_order_id
                                            }
                                        })
                                    else:
                                        now = datetime.now()
                                        error_message = new_order_result.get('message', '未知错误') if new_order_result else '创建新挂单失败'
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 创建新挂单失败: {error_message}')

                                        # 添加结果
                                        management_results.append({
                                            'action': 'MODIFY',
                                            'orderId': order_id,
                                            'success': False,
                                            'message': f'删除原挂单成功，但创建新挂单失败: {error_message}',
                                            'details': new_order_result or {'status': 'error', 'message': '创建新挂单失败'}
                                        })
                                else:
                                    now = datetime.now()
                                    error_message = delete_result.get('message', '未知错误')
                                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 删除原挂单失败: {error_message}')

                                    # 添加结果
                                    management_results.append({
                                        'action': 'MODIFY',
                                        'orderId': order_id,
                                        'success': False,
                                        'message': f'删除原挂单失败: {error_message}',
                                        'details': delete_result
                                    })
                            else:
                                # 获取挂单类型和价格
                                order_type = order_info.get('type', '').upper()
                                order_price = float(order_info.get('open_price', 0))

                                # 检查止损是否为0，如果是，设置为安全的默认值
                                if new_stop_loss == 0 or new_stop_loss is None:
                                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 警告: 挂单止损为0或未设置，这是非常危险的！尝试设置安全的默认止损')

                                    # 根据挂单类型设置合理的止损
                                    if 'BUYLIMIT' in order_type or 'BUYSTOP' in order_type:
                                        # 买单挂单，止损设置在挂单价格下方100点
                                        new_stop_loss = order_price - 0.01
                                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 为买单挂单设置默认止损: {new_stop_loss}')
                                    elif 'SELLLIMIT' in order_type or 'SELLSTOP' in order_type:
                                        # 卖单挂单，止损设置在挂单价格上方100点
                                        new_stop_loss = order_price + 0.01
                                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 为卖单挂单设置默认止损: {new_stop_loss}')
                                    else:
                                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 未知挂单类型: {order_type}，无法设置默认止损')

                                # 检查风险回报比
                                if new_stop_loss is not None and new_take_profit is not None:
                                    # 对于挂单，我们使用挂单价格作为入场价
                                    entry_price = order_price

                                    if 'BUY' in order_type:
                                        risk = entry_price - new_stop_loss
                                        reward = new_take_profit - entry_price
                                        risk_reward_ratio = reward / risk if risk > 0 else 0
                                    elif 'SELL' in order_type:
                                        risk = new_stop_loss - entry_price
                                        reward = entry_price - new_take_profit
                                        risk_reward_ratio = reward / risk if risk > 0 else 0
                                    else:
                                        risk_reward_ratio = 0

                                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 挂单价格: {entry_price}')
                                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 止损: {new_stop_loss}, 止盈: {new_take_profit}')
                                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 风险: {risk:.5f}, 回报: {reward:.5f}')
                                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 风险回报比: {risk_reward_ratio:.2f}')

                                    # 如果风险回报比小于1.5，发出警告并调整止盈
                                    if risk_reward_ratio > 0 and risk_reward_ratio < 1.5:
                                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 警告: 风险回报比 ({risk_reward_ratio:.2f}) 小于建议的最小值 1.5，调整止盈')

                                        if 'BUY' in order_type:
                                            # 调整止盈，使风险回报比至少为1.5
                                            new_take_profit = entry_price + (risk * 1.5)
                                            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 调整后的止盈: {new_take_profit}')
                                        elif 'SELL' in order_type:
                                            # 调整止盈，使风险回报比至少为1.5
                                            new_take_profit = entry_price - (risk * 1.5)
                                            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 调整后的止盈: {new_take_profit}')

                                        # 重新计算风险回报比
                                        if 'BUY' in order_type:
                                            reward = new_take_profit - entry_price
                                        elif 'SELL' in order_type:
                                            reward = entry_price - new_take_profit

                                        risk_reward_ratio = reward / risk if risk > 0 else 0
                                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 调整后的风险回报比: {risk_reward_ratio:.2f}')

                                    # 如果风险回报比过高（大于5），可能表示止损设置过远，发出警告
                                    if risk_reward_ratio > 5:
                                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 警告: 风险回报比 ({risk_reward_ratio:.2f}) 过高，可能表示止损设置过远')

                                # 只修改止损止盈
                                modify_result = mt4_client.mt4_client.modify_order(
                                    order_id,
                                    None,  # 不修改入场价格
                                    new_stop_loss,
                                    new_take_profit
                                )

                                # 如果修改成功，记录到交易结果记录器
                                if modify_result.get('status') == 'success' and has_trade_recorder:
                                    try:
                                        # 记录修改订单操作
                                        trade_result_recorder.record_trade_modify(
                                            order_id=order_id,
                                            new_stop_loss=new_stop_loss,
                                            new_take_profit=new_take_profit
                                        )
                                        now = datetime.now()
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改挂单操作已记录到交易结果记录器')
                                    except Exception as e:
                                        now = datetime.now()
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 记录修改挂单操作到交易结果记录器失败: {e}')

                                management_results.append({
                                    'action': 'MODIFY',
                                    'orderId': order_id,
                                    'success': modify_result.get('status') == 'success',
                                    'message': modify_result.get('message', ''),
                                    'details': modify_result
                                })
                        else:
                            # 修改持仓
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改持仓: {order_id}')
                            modify_result = mt4_client.mt4_client.modify_position(
                                order_id,
                                new_stop_loss,
                                new_take_profit
                            )

                            # 如果修改成功，记录到交易结果记录器
                            if modify_result.get('status') == 'success' and has_trade_recorder:
                                try:
                                    # 记录修改持仓操作
                                    trade_result_recorder.record_trade_modify(
                                        order_id=order_id,
                                        new_stop_loss=new_stop_loss,
                                        new_take_profit=new_take_profit
                                    )
                                    now = datetime.now()
                                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修改持仓操作已记录到交易结果记录器')
                                except Exception as e:
                                    now = datetime.now()
                                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 记录修改持仓操作到交易结果记录器失败: {e}')

                            management_results.append({
                                'action': 'MODIFY',
                                'orderId': order_id,
                                'success': modify_result.get('status') == 'success',
                                'message': modify_result.get('message', ''),
                                'details': modify_result
                            })
                    else:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 未找到订单: {order_id}')
                        management_results.append({
                            'action': 'MODIFY',
                            'orderId': order_id,
                            'success': False,
                            'message': '未找到订单'
                        })

                elif action_type == 'DELETE':
                    # 删除挂单
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 删除挂单: {order_id}')

                    # 使用delete_pending_order方法删除挂单
                    delete_result = mt4_client.mt4_client.delete_pending_order(order_id)

                    # 如果删除成功，从虚拟账户中移除该挂单
                    if delete_result.get('status') == 'success':
                        # 从虚拟账户中移除挂单
                        from app.utils.performance_evaluator import remove_pending_order
                        remove_result = remove_pending_order(order_id)
                        if remove_result:
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已从虚拟账户中移除挂单: {order_id}')

                    management_results.append({
                        'action': 'DELETE',
                        'orderId': order_id,
                        'success': delete_result.get('status') == 'success',
                        'message': delete_result.get('message', ''),
                        'details': delete_result
                    })

                elif action_type == 'CLOSE':
                    # 平仓
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 平仓: {order_id}')
                    close_result = mt4_client.mt4_client.close_order(order_id)

                    # 如果平仓成功，更新虚拟账户
                    if close_result.get('status') == 'success':
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 平仓成功，更新虚拟账户')

                        # 获取当前价格
                        market_info_response = mt4_client.mt4_client.get_market_info('EURUSD')
                        if market_info_response and market_info_response.get('status') == 'success':
                            current_price = float(market_info_response['data']['bid'])  # 使用bid价格作为平仓价格

                            # 计算盈亏（简单估算）
                            profit_loss = 0
                            try:
                                # 获取订单信息
                                positions_response = mt4_client.mt4_client.get_active_orders()
                                positions = positions_response.get('orders', [])
                                for position in positions:
                                    if str(position.get('order_id')) == str(order_id):
                                        position_type = position.get('type', '').upper()
                                        open_price = float(position.get('open_price', 0))
                                        lots = float(position.get('lots', 0))

                                        # 简单估算盈亏
                                        if 'BUY' in position_type:
                                            profit_loss = (current_price - open_price) * lots * 100000  # 假设1手=100,000单位
                                        elif 'SELL' in position_type:
                                            profit_loss = (open_price - current_price) * lots * 100000

                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 估算盈亏: {profit_loss}')
                                        break
                            except Exception as e:
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 计算盈亏失败: {e}')

                            # 更新虚拟账户中的交易状态
                            update_result = update_trade_status(
                                order_id,
                                TradeStatus.CLOSED,
                                current_price,
                                datetime.now().isoformat()
                            )

                            if update_result:
                                now = datetime.now()
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 虚拟账户更新成功')

                                # 记录交易结果
                                if has_trade_recorder:
                                    try:
                                        # 记录平仓操作
                                        trade_result_recorder.record_trade_close(
                                            order_id=order_id,
                                            close_price=current_price,
                                            profit_loss=profit_loss,
                                            close_reason='MANUAL'
                                        )
                                        now = datetime.now()
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 平仓操作已记录到交易结果记录器')
                                    except Exception as e:
                                        now = datetime.now()
                                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 记录平仓操作到交易结果记录器失败: {e}')

                                # 更新订单结果分析（暂时注释掉）
                                # update_order_results()
                                now = datetime.now()
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 订单结果分析更新成功')
                            else:
                                now = datetime.now()
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 虚拟账户更新失败')
                        else:
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取当前价格失败，无法更新虚拟账户')

                    management_results.append({
                        'action': 'CLOSE',
                        'orderId': order_id,
                        'success': close_result.get('status') == 'success',
                        'message': close_result.get('message', ''),
                        'details': close_result
                    })

            # 如果只有订单管理操作，没有新的交易指令，则返回订单管理结果
            if trade_instructions.get('action') in ['NONE', 'DELETE', 'MODIFY', 'CLOSE']:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 订单管理操作完成，无新交易指令')

                # 记录操作
                log_operation(
                    operation_type=OperationType.ORDER_MANAGEMENT,
                    message=f'订单管理操作完成，共{len(management_results)}个操作',
                    details={'results': management_results}
                )

                return {
                    'success': True,
                    'message': '订单管理操作完成',
                    'orderManagementResults': management_results
                }

        # 获取交易参数
        action = trade_instructions.get('action', 'NONE')
        order_type = trade_instructions.get('orderType', 'MARKET')
        symbol = 'EURUSD'  # 固定为EURUSD
        lot = trade_instructions.get('lotSize', 0.01)  # 使用LLM指定的仓位大小，默认为0.01手
        # 限制仓位大小在0.01到0.2之间
        lot = min(0.2, max(0.01, lot))
        entry_price = trade_instructions.get('entryPrice')
        stop_loss = trade_instructions.get('stopLoss')
        take_profit = trade_instructions.get('takeProfit')

        # 确保止损止盈已设置
        if action not in ['NONE', 'CLOSE']:
            now = datetime.now()

            # 获取当前价格
            current_price = None
            market_info_response = mt4_client.mt4_client.get_market_info(symbol)
            if market_info_response and market_info_response.get('status') == 'success':
                current_price = float(market_info_response['data']['ask'])

            # 检查止损是否为0或未设置
            if stop_loss is None or stop_loss == 0:
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 止损为0或未设置，这是非常危险的！设置安全的默认止损')

                if current_price is not None:
                    # 如果是买入，止损设置在当前价格下方100点
                    if action == 'BUY':
                        stop_loss = current_price - 0.01
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为买单设置默认止损: {stop_loss}')
                    # 如果是卖出，止损设置在当前价格上方100点
                    elif action == 'SELL':
                        stop_loss = current_price + 0.01
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为卖单设置默认止损: {stop_loss}')
                else:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 无法获取当前价格，无法设置默认止损')
                    # 交易安全第一，如果无法设置合理的止损，不执行交易
                    return {
                        'success': False,
                        'message': '无法设置止损，为了交易安全，取消交易',
                        'orderId': None
                    }

            # 检查止盈是否为0或未设置
            if take_profit is None or take_profit == 0:
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 止盈为0或未设置，设置安全的默认止盈')

                if current_price is not None:
                    # 如果是买入，止盈设置在当前价格上方200点
                    if action == 'BUY':
                        take_profit = current_price + 0.02
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为买单设置默认止盈: {take_profit}')
                    # 如果是卖出，止盈设置在当前价格下方200点
                    elif action == 'SELL':
                        take_profit = current_price - 0.02
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 为卖单设置默认止盈: {take_profit}')
                else:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 无法获取当前价格，无法设置默认止盈')
                    # 交易安全第一，如果无法设置合理的止盈，不执行交易
                    return {
                        'success': False,
                        'message': '无法设置止盈，为了交易安全，取消交易',
                        'orderId': None
                    }

            # 检查风险回报比
            if current_price is not None and stop_loss is not None and take_profit is not None:
                # 对于市价单，我们使用当前价格作为预估入场价
                # 对于限价单和止损单，我们使用指定的入场价
                entry_price = entry_price if entry_price is not None else current_price

                # 检查止损止盈方向是否正确
                if action == 'BUY':
                    # 买入订单：止损必须低于入场价，止盈必须高于入场价
                    if stop_loss >= entry_price:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 错误: 买入订单的止损价格({stop_loss})必须低于入场价格({entry_price})')
                        return {
                            'success': False,
                            'message': f'买入订单的止损价格({stop_loss})必须低于入场价格({entry_price})',
                            'orderId': None
                        }

                    if take_profit <= entry_price:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 错误: 买入订单的止盈价格({take_profit})必须高于入场价格({entry_price})')
                        return {
                            'success': False,
                            'message': f'买入订单的止盈价格({take_profit})必须高于入场价格({entry_price})',
                            'orderId': None
                        }

                    risk = entry_price - stop_loss
                    reward = take_profit - entry_price

                elif action == 'SELL':
                    # 卖出订单：止损必须高于入场价，止盈必须低于入场价
                    if stop_loss <= entry_price:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 错误: 卖出订单的止损价格({stop_loss})必须高于入场价格({entry_price})')
                        return {
                            'success': False,
                            'message': f'卖出订单的止损价格({stop_loss})必须高于入场价格({entry_price})',
                            'orderId': None
                        }

                    if take_profit >= entry_price:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 错误: 卖出订单的止盈价格({take_profit})必须低于入场价格({entry_price})')
                        return {
                            'success': False,
                            'message': f'卖出订单的止盈价格({take_profit})必须低于入场价格({entry_price})',
                            'orderId': None
                        }

                    risk = stop_loss - entry_price
                    reward = entry_price - take_profit

                else:
                    risk = 0
                    reward = 0

                # 计算风险回报比
                risk_reward_ratio = reward / risk if risk > 0 else 0

                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 预估入场价: {entry_price}')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 止损: {stop_loss}, 止盈: {take_profit}')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 风险: {risk:.5f}, 回报: {reward:.5f}')
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 风险回报比: {risk_reward_ratio:.2f}')

                # 检查止损止盈距离是否合理
                if action in ['BUY', 'SELL']:
                    # 止损距离检查（10-100点）
                    stop_loss_distance = abs(entry_price - stop_loss) * 10000  # 转换为点数
                    if stop_loss_distance < 10:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 止损距离({stop_loss_distance:.1f}点)过小，可能被市场噪音触发')
                    elif stop_loss_distance > 100:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 止损距离({stop_loss_distance:.1f}点)过大，可能导致过大风险')

                    # 止盈距离检查（10-200点）
                    take_profit_distance = abs(entry_price - take_profit) * 10000  # 转换为点数
                    if take_profit_distance < 10:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 止盈距离({take_profit_distance:.1f}点)过小，可能无法覆盖交易成本')
                    elif take_profit_distance > 200:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 止盈距离({take_profit_distance:.1f}点)过大，可能难以达到')

                # 强制执行风险管理规则
                if risk_reward_ratio > 0:
                    # 如果风险回报比小于0.8（即风险大于回报），只发出警告，不自动调整
                    if risk_reward_ratio < 0.8:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 风险回报比 ({risk_reward_ratio:.2f}) 过低，风险大于回报')
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 建议: 考虑调整止损或止盈以改善风险回报比')

                        # 记录到交易日志，但不调整
                        log_operation(
                            operation_type=OperationType.OTHER,
                            success=True,
                            message=f'风险回报比警告: {risk_reward_ratio:.2f}，风险大于回报',
                            parameters={'action': action, 'risk_reward_ratio': risk_reward_ratio},
                            result={}
                        )

                    # 如果风险回报比在0.8-1.0之间，发出提示
                    elif risk_reward_ratio < 1.0:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提示: 风险回报比 ({risk_reward_ratio:.2f}) 较低，风险略大于回报')

                    # 如果风险回报比在1.0-1.5之间，发出良好提示
                    elif risk_reward_ratio < 1.5:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提示: 风险回报比 ({risk_reward_ratio:.2f}) 良好')

                    # 如果风险回报比在1.5-5.0之间，发出优秀提示
                    elif risk_reward_ratio <= 5.0:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 提示: 风险回报比 ({risk_reward_ratio:.2f}) 优秀')

                    # 如果风险回报比过高（大于5），可能表示止损设置过远，只发出警告，不自动调整
                    else:
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 风险回报比 ({risk_reward_ratio:.2f}) 过高，可能表示止损设置过远')
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 建议: 考虑将止损设置在更接近的技术位置')

                        # 记录到交易日志，但不调整
                        log_operation(
                            operation_type=OperationType.OTHER,
                            success=True,
                            message=f'风险回报比警告: {risk_reward_ratio:.2f}，可能表示止损设置过远',
                            parameters={'action': action, 'risk_reward_ratio': risk_reward_ratio},
                            result={}
                        )
                else:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 无法计算风险回报比，请检查止损和止盈设置')

        # 确保MT4客户端已连接
        if not mt4_client.mt4_client.is_connected:
            print('MT4客户端未连接，尝试连接')
            connected = mt4_client.mt4_client.connect()
            if not connected:
                return {
                    'success': False,
                    'message': '无法连接到MT4客户端',
                    'orderId': None
                }

        # 检查是否已经存在相同的订单（仅用于记录信息，不阻止交易）
        if check_duplicate and order_type in ['LIMIT', 'STOP']:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检查是否已经存在相同的订单...')
            try:
                pending_orders_response = mt4_client.mt4_client.get_pending_orders()
                pending_orders = pending_orders_response.get('orders', [])

                # 检查是否有相同的挂单
                for order in pending_orders:
                    order_price = float(order.get('open_price', 0))
                    order_type_str = order.get('type', '').upper()
                    order_symbol = order.get('symbol', '')

                    # 检查是否是相同类型的订单
                    is_same_type = False
                    if action == 'BUY' and order_type == 'LIMIT' and 'BUYLIMIT' in order_type_str:
                        is_same_type = True
                    elif action == 'BUY' and order_type == 'STOP' and 'BUYSTOP' in order_type_str:
                        is_same_type = True
                    elif action == 'SELL' and order_type == 'LIMIT' and 'SELLLIMIT' in order_type_str:
                        is_same_type = True
                    elif action == 'SELL' and order_type == 'STOP' and 'SELLSTOP' in order_type_str:
                        is_same_type = True

                    # 如果是相同类型的订单，检查价格是否接近
                    if is_same_type and order_symbol == symbol:
                        price_diff = abs(order_price - entry_price)
                        price_threshold = 0.0005  # 价格差异阈值，约5点

                        if price_diff < price_threshold:
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到可能的重复订单: {order_type_str}, 价格: {order_price}, 新订单价格: {entry_price}')

                            # 检查是否有条件字段或过期时间字段，如果有，可能是有意的重复订单
                            has_conditions = trade_instructions.get('conditions') and len(trade_instructions.get('conditions', [])) > 0
                            has_expiration = trade_instructions.get('expiration') is not None

                            if has_conditions or has_expiration:
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 订单包含条件或过期时间，可能是有意的重复订单，继续执行')
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 条件: {trade_instructions.get("conditions")}')
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 过期时间: {trade_instructions.get("expiration")}')
                            else:
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到重复订单，取消执行')
                                # 阻止交易，返回错误信息
                                return {
                                    'success': False,
                                    'message': f'检测到重复订单: {order_type_str}, 价格: {order_price}, 新订单价格: {entry_price}',
                                    'orderId': None,
                                    'duplicate': True
                                }
            except Exception as check_error:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检查重复订单时出错: {check_error}')

        # 确保MT4客户端已连接
        if not mt4_client.mt4_client.is_connected:
            print('MT4客户端未连接，尝试连接')
            connected = mt4_client.mt4_client.connect()
            if not connected:
                return {
                    'success': False,
                    'message': '无法连接到MT4客户端',
                    'orderId': None
                }

        # 根据交易指令执行交易
        response = None

        if action == 'BUY':
            if order_type == 'MARKET':
                print('执行市价买入')
                response = mt4_client.mt4_client.buy(symbol, lot, stop_loss, take_profit)
            elif order_type == 'LIMIT':
                if entry_price is None:
                    return {
                        'success': False,
                        'message': '限价单必须指定价格',
                        'orderId': None
                    }
                print('执行买入限价单')
                response = mt4_client.mt4_client.buy_limit(symbol, lot, entry_price, stop_loss, take_profit)
            elif order_type == 'STOP':
                if entry_price is None:
                    return {
                        'success': False,
                        'message': '止损单必须指定价格',
                        'orderId': None
                    }
                print('执行买入止损单')
                response = mt4_client.mt4_client.buy_stop(symbol, lot, entry_price, stop_loss, take_profit)
        elif action == 'SELL':
            if order_type == 'MARKET':
                print('执行市价卖出')
                response = mt4_client.mt4_client.sell(symbol, lot, stop_loss, take_profit)
            elif order_type == 'LIMIT':
                if entry_price is None:
                    return {
                        'success': False,
                        'message': '限价单必须指定价格',
                        'orderId': None
                    }
                print('执行卖出限价单')
                response = mt4_client.mt4_client.sell_limit(symbol, lot, entry_price, stop_loss, take_profit)
            elif order_type == 'STOP':
                if entry_price is None:
                    return {
                        'success': False,
                        'message': '止损单必须指定价格',
                        'orderId': None
                    }
                print('执行卖出止损单')
                response = mt4_client.mt4_client.sell_stop(symbol, lot, entry_price, stop_loss, take_profit)

        # 处理响应
        if response and response.get('status') == 'success':
            # 获取订单ID
            order_id = response.get('order_id', '')

            # 如果订单ID为空，尝试从响应中提取
            if not order_id:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 响应中没有订单ID，尝试从响应中提取')

                # 尝试从响应消息中提取订单ID
                message = response.get('message', '')
                if '成功' in message and '订单号' in message:
                    import re
                    id_match = re.search(r'订单号[：:]\s*(\d+)', message)
                    if id_match:
                        order_id = id_match.group(1)
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 从响应消息中提取到订单ID: {order_id}')

            # 如果订单ID仍然为空，尝试从挂单列表中查找最新的挂单
            if not order_id and order_type in ['LIMIT', 'STOP']:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 警告: 无法从响应中获取订单ID，尝试从挂单列表中查找最新的挂单')

                # 等待一秒，确保订单已经被处理
                time.sleep(1)

                try:
                    # 获取挂单列表
                    pending_orders_response = mt4_client.mt4_client.get_pending_orders()
                    pending_orders = pending_orders_response.get('orders', [])

                    # 按开仓时间排序，找出最新的挂单
                    if pending_orders:
                        # 转换时间字符串为datetime对象
                        for order in pending_orders:
                            try:
                                order['open_time_obj'] = datetime.strptime(order.get('open_time', ''), '%Y.%m.%d %H:%M:%S')
                            except:
                                order['open_time_obj'] = datetime.now() - timedelta(days=1)  # 默认为昨天

                        # 按开仓时间排序
                        pending_orders.sort(key=lambda x: x['open_time_obj'], reverse=True)

                        # 找出最新的符合条件的挂单
                        for order in pending_orders:
                            order_symbol = order.get('symbol', '')
                            order_type_str = order.get('type', '').upper()
                            order_lots = float(order.get('lots', 0))

                            # 检查是否是我们刚刚创建的挂单
                            is_match = False
                            if action == 'BUY' and order_type == 'LIMIT' and 'BUYLIMIT' in order_type_str:
                                is_match = True
                            elif action == 'BUY' and order_type == 'STOP' and 'BUYSTOP' in order_type_str:
                                is_match = True
                            elif action == 'SELL' and order_type == 'LIMIT' and 'SELLLIMIT' in order_type_str:
                                is_match = True
                            elif action == 'SELL' and order_type == 'STOP' and 'SELLSTOP' in order_type_str:
                                is_match = True

                            # 如果找到匹配的挂单，使用其ID
                            if is_match and order_symbol == symbol and abs(order_lots - lot) < 0.001:
                                order_id = order.get('order_id', '')
                                now = datetime.now()
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 从挂单列表中找到最新的匹配挂单，订单ID: {order_id}')
                                break
                except Exception as find_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 查找最新挂单时出错: {find_error}')

            # 记录交易到虚拟账户
            if action not in ['NONE', 'CLOSE'] and order_id:
                # 创建交易数据
                trade_data = {
                    'orderId': order_id,
                    'symbol': symbol,
                    'action': action,
                    'orderType': order_type,
                    'entryPrice': entry_price,
                    'stopLoss': stop_loss,
                    'takeProfit': take_profit,
                    'lotSize': lot,
                    'reasoning': trade_instructions.get('reasoning', '')
                }

                # 记录交易到虚拟账户
                performance_evaluator.record_trade(trade_data)

                # 记录交易到交易结果记录器
                if has_trade_recorder:
                    try:
                        # 获取分析ID
                        analysis_id = None
                        if isinstance(trade_instructions, dict) and 'analysisId' in trade_instructions:
                            analysis_id = trade_instructions.get('analysisId')

                        # 记录开仓操作
                        trade_result_recorder.record_trade_open(
                            order_id=order_id,
                            symbol=symbol,
                            action=action,
                            order_type=order_type,
                            entry_price=entry_price if entry_price is not None else float(response.get('price', 0)),
                            stop_loss=stop_loss,
                            take_profit=take_profit,
                            lot_size=lot,
                            analysis_id=analysis_id
                        )
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 交易已记录到交易结果记录器')
                    except Exception as e:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 记录交易到交易结果记录器失败: {e}')

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 交易已记录到虚拟账户')

            result = {
                'success': True,
                'message': '交易执行成功',
                'orderId': order_id,
                'details': response.get('details', {})
            }

            # 如果有订单管理操作，添加到结果中
            if 'management_results' in locals() and management_results:
                result['orderManagementResults'] = management_results

            # 如果是限价单或止损单，检查挂单列表
            if order_type in ['LIMIT', 'STOP']:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 交易执行成功，检查挂单列表...')
                time.sleep(1)  # 等待一秒，确保订单已经被处理

                try:
                    pending_orders_response = mt4_client.mt4_client.get_pending_orders()
                    pending_orders = pending_orders_response.get('orders', [])

                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 当前挂单数量: {len(pending_orders)}')
                    for order in pending_orders:
                        print(f'  - 挂单: 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 货币对: {order.get("symbol")}, 手数: {order.get("lots")}, 价格: {order.get("open_price")}, 止损: {order.get("sl")}, 止盈: {order.get("tp")}')
                except Exception as check_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检查挂单列表时出错: {check_error}')

            return result
        else:
            error_message = response.get("message", "未知错误") if response else "未知错误"
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 交易执行失败: {error_message}')

            result = {
                'success': False,
                'message': f'交易执行失败: {error_message}',
                'orderId': None
            }

            # 如果有订单管理操作，添加到结果中
            if 'management_results' in locals() and management_results:
                result['orderManagementResults'] = management_results

            return result
    except Exception as error:
        print(f'执行交易失败: {error}')
        return {
            'success': False,
            'message': f'执行交易失败: {str(error)}',
            'orderId': None
        }
