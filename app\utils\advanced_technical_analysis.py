"""
高级技术分析模块
提供高级技术分析工具，包括量化因子、市场微观结构分析和波动率分析等
"""
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from scipy import stats
from statsmodels.tsa.stattools import adfuller
from app.utils.error_logger import log_error, ErrorType


def calculate_advanced_indicators(df, timeframe='15min'):
    """
    计算高级技术指标

    Args:
        df (pandas.DataFrame): 价格数据，包含 open, high, low, close, volume 列
        timeframe (str): 时间周期，如 '15min', '1h' 等

    Returns:
        dict: 高级技术指标
    """
    try:
        # 确保数据按时间排序（从旧到新）
        df = df.sort_values('time')

        # 计算基本价格数据
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))

        # 计算高级指标
        indicators = {}

        # 1. 波动率指标
        indicators['volatility'] = calculate_volatility_indicators(df)

        # 2. 趋势强度指标
        indicators['trend'] = calculate_trend_indicators(df)

        # 3. 市场微观结构指标
        indicators['microstructure'] = calculate_microstructure_indicators(df)

        # 4. 统计指标
        indicators['statistics'] = calculate_statistical_indicators(df)

        # 5. 价格行为指标
        indicators['price_action'] = calculate_price_action_indicators(df)

        # 6. 量化因子
        indicators['factors'] = calculate_quantitative_factors(df, timeframe)

        return indicators
    except Exception as e:
        print(f'计算高级技术指标失败: {e}')

        # 记录错误
        log_error(
            error_type=ErrorType.CALCULATION_ERROR,
            message=f'计算高级技术指标失败: {e}',
            details={'exception': str(e)},
            operation='CALCULATE_ADVANCED_INDICATORS'
        )

        return {}


def calculate_volatility_indicators(df):
    """
    计算波动率指标

    Args:
        df (pandas.DataFrame): 价格数据

    Returns:
        dict: 波动率指标
    """
    try:
        # 计算历史波动率（过去20个周期的标准差，年化）
        returns = df['returns'].dropna()
        if len(returns) >= 20:
            historical_volatility = returns.rolling(window=20).std() * np.sqrt(252)
            current_hv = historical_volatility.iloc[-1]
        else:
            current_hv = np.nan

        # 计算真实波动幅度（ATR）
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())

        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = np.max(ranges, axis=1)
        atr = true_range.rolling(14).mean().iloc[-1]

        # 计算相对波动率（当前波动率与过去N周期平均波动率的比值）
        if len(returns) >= 50:
            current_vol = returns.iloc[-20:].std() * np.sqrt(252)
            past_vol = returns.iloc[-50:-20].std() * np.sqrt(252)
            relative_volatility = current_vol / past_vol if past_vol != 0 else np.nan
        else:
            relative_volatility = np.nan

        # 计算波动率范围（过去N周期的最高波动率与最低波动率之差）
        if len(returns) >= 50:
            rolling_vol = returns.rolling(20).std() * np.sqrt(252)
            vol_range = rolling_vol.iloc[-50:].max() - rolling_vol.iloc[-50:].min()
        else:
            vol_range = np.nan

        # 计算波动率趋势（当前波动率相对于过去N周期的百分位）
        if len(returns) >= 50:
            rolling_vol = returns.rolling(20).std() * np.sqrt(252)
            current_vol = rolling_vol.iloc[-1]
            vol_percentile = stats.percentileofscore(rolling_vol.iloc[-50:].dropna(), current_vol) / 100
        else:
            vol_percentile = np.nan

        return {
            'historical_volatility': current_hv,
            'atr': atr,
            'relative_volatility': relative_volatility,
            'volatility_range': vol_range,
            'volatility_percentile': vol_percentile
        }
    except Exception as e:
        print(f'计算波动率指标失败: {e}')
        return {}


def calculate_trend_indicators(df):
    """
    计算趋势强度指标

    Args:
        df (pandas.DataFrame): 价格数据

    Returns:
        dict: 趋势强度指标
    """
    try:
        # 计算ADX（平均趋势指数）
        # 简化版ADX计算
        high_diff = df['high'].diff()
        low_diff = -df['low'].diff()

        plus_dm = np.where((high_diff > low_diff) & (high_diff > 0), high_diff, 0)
        minus_dm = np.where((low_diff > high_diff) & (low_diff > 0), low_diff, 0)

        tr = np.maximum(df['high'] - df['low'],
                        np.maximum(np.abs(df['high'] - df['close'].shift(1)),
                                  np.abs(df['low'] - df['close'].shift(1))))

        plus_di = 100 * pd.Series(plus_dm).rolling(14).sum() / pd.Series(tr).rolling(14).sum()
        minus_di = 100 * pd.Series(minus_dm).rolling(14).sum() / pd.Series(tr).rolling(14).sum()

        dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(14).mean()

        current_adx = adx.iloc[-1] if not pd.isna(adx.iloc[-1]) else 0

        # 计算线性回归斜率
        if len(df) >= 20:
            y = df['close'].iloc[-20:].values
            x = np.arange(len(y))
            slope, _, r_value, _, _ = stats.linregress(x, y)

            # 标准化斜率（相对于价格）
            normalized_slope = slope / df['close'].iloc[-20:].mean()

            # R平方值（拟合优度）
            r_squared = r_value ** 2
        else:
            normalized_slope = np.nan
            r_squared = np.nan

        # 计算价格与移动平均线的关系
        ma20 = df['close'].rolling(20).mean()
        ma50 = df['close'].rolling(50).mean()

        price_vs_ma20 = (df['close'].iloc[-1] / ma20.iloc[-1] - 1) * 100 if not pd.isna(ma20.iloc[-1]) else np.nan
        ma20_vs_ma50 = (ma20.iloc[-1] / ma50.iloc[-1] - 1) * 100 if not pd.isna(ma50.iloc[-1]) else np.nan

        # 计算趋势持续性（连续上涨或下跌的天数）
        returns = df['returns'].dropna()
        if len(returns) > 0:
            positive_streak = 0
            negative_streak = 0

            for i in range(len(returns) - 1, -1, -1):
                if returns.iloc[i] > 0:
                    positive_streak += 1
                    negative_streak = 0
                elif returns.iloc[i] < 0:
                    negative_streak += 1
                    positive_streak = 0
                else:
                    break

            current_streak = positive_streak if positive_streak > 0 else -negative_streak
        else:
            current_streak = 0

        return {
            'adx': current_adx,
            'normalized_slope': normalized_slope,
            'r_squared': r_squared,
            'price_vs_ma20': price_vs_ma20,
            'ma20_vs_ma50': ma20_vs_ma50,
            'current_streak': current_streak
        }
    except Exception as e:
        print(f'计算趋势强度指标失败: {e}')
        return {}


def calculate_microstructure_indicators(df):
    """
    计算市场微观结构指标

    Args:
        df (pandas.DataFrame): 价格数据

    Returns:
        dict: 市场微观结构指标
    """
    try:
        # 计算价格波动效率（Price Efficiency）
        # 实际价格变化与路径长度的比值
        if len(df) >= 20:
            price_change = abs(df['close'].iloc[-1] - df['close'].iloc[-20])
            path_length = np.sum(abs(df['close'].diff().iloc[-19:]))
            efficiency = price_change / path_length if path_length != 0 else 0
        else:
            efficiency = np.nan

        # 计算成交量加权平均价格（VWAP）
        if 'volume' in df.columns and len(df) > 0:
            df['vwap'] = (df['close'] * df['volume']).cumsum() / df['volume'].cumsum()
            current_vwap = df['vwap'].iloc[-1]
            price_vs_vwap = (df['close'].iloc[-1] / current_vwap - 1) * 100
        else:
            current_vwap = np.nan
            price_vs_vwap = np.nan

        # 计算价格范围比率（当日价格范围与N日平均范围的比值）
        if len(df) >= 20:
            current_range = (df['high'].iloc[-1] - df['low'].iloc[-1]) / df['close'].iloc[-1]
            avg_range = ((df['high'] - df['low']) / df['close']).iloc[-20:-1].mean()
            range_ratio = current_range / avg_range if avg_range != 0 else np.nan
        else:
            range_ratio = np.nan

        # 计算价格加速度（价格变化率的变化率）
        if len(df) >= 3:
            returns = df['returns'].dropna()
            if len(returns) >= 2:
                acceleration = returns.iloc[-1] - returns.iloc[-2]
            else:
                acceleration = np.nan
        else:
            acceleration = np.nan

        return {
            'price_efficiency': efficiency,
            'vwap': current_vwap,
            'price_vs_vwap': price_vs_vwap,
            'range_ratio': range_ratio,
            'price_acceleration': acceleration
        }
    except Exception as e:
        print(f'计算市场微观结构指标失败: {e}')
        return {}


def calculate_statistical_indicators(df):
    """
    计算统计指标

    Args:
        df (pandas.DataFrame): 价格数据

    Returns:
        dict: 统计指标
    """
    try:
        returns = df['returns'].dropna()

        # 计算偏度（Skewness）
        if len(returns) >= 30:
            skewness = returns.iloc[-30:].skew()
        else:
            skewness = np.nan

        # 计算峰度（Kurtosis）
        if len(returns) >= 30:
            kurtosis = returns.iloc[-30:].kurt()
        else:
            kurtosis = np.nan

        # 计算自相关系数（Autocorrelation）
        if len(returns) >= 30:
            autocorr = returns.iloc[-30:].autocorr()
        else:
            autocorr = np.nan

        # 计算平稳性检验（ADF Test）
        if len(df['close']) >= 30:
            try:
                adf_result = adfuller(df['close'].iloc[-30:])
                adf_pvalue = adf_result[1]
                is_stationary = adf_pvalue < 0.05
            except:
                adf_pvalue = np.nan
                is_stationary = False
        else:
            adf_pvalue = np.nan
            is_stationary = False

        # 计算Z-Score（当前价格相对于N周期均值的标准差倍数）
        if len(df['close']) >= 20:
            mean = df['close'].iloc[-20:].mean()
            std = df['close'].iloc[-20:].std()
            z_score = (df['close'].iloc[-1] - mean) / std if std != 0 else 0
        else:
            z_score = np.nan

        return {
            'skewness': skewness,
            'kurtosis': kurtosis,
            'autocorrelation': autocorr,
            'adf_pvalue': adf_pvalue,
            'is_stationary': is_stationary,
            'z_score': z_score
        }
    except Exception as e:
        print(f'计算统计指标失败: {e}')
        return {}


def calculate_price_action_indicators(df):
    """
    计算价格行为指标

    Args:
        df (pandas.DataFrame): 价格数据

    Returns:
        dict: 价格行为指标
    """
    try:
        # 计算蜡烛图形态
        if len(df) >= 1:
            # 计算实体比例（实体与整体范围的比值）
            body = abs(df['close'].iloc[-1] - df['open'].iloc[-1])
            range_hl = df['high'].iloc[-1] - df['low'].iloc[-1]
            body_ratio = body / range_hl if range_hl != 0 else 0

            # 判断是否为十字星形态（实体很小）
            is_doji = body_ratio < 0.1

            # 判断是否为锤子线形态
            if df['close'].iloc[-1] > df['open'].iloc[-1]:  # 阳线
                upper_shadow = df['high'].iloc[-1] - df['close'].iloc[-1]
                lower_shadow = df['open'].iloc[-1] - df['low'].iloc[-1]
            else:  # 阴线
                upper_shadow = df['high'].iloc[-1] - df['open'].iloc[-1]
                lower_shadow = df['close'].iloc[-1] - df['low'].iloc[-1]

            is_hammer = (lower_shadow > 2 * body) and (upper_shadow < 0.2 * body)
            is_shooting_star = (upper_shadow > 2 * body) and (lower_shadow < 0.2 * body)

            # 判断是否为吞没形态
            if len(df) >= 2:
                prev_body = abs(df['close'].iloc[-2] - df['open'].iloc[-2])
                curr_body = body

                prev_bullish = df['close'].iloc[-2] > df['open'].iloc[-2]
                curr_bullish = df['close'].iloc[-1] > df['open'].iloc[-1]

                if prev_bullish and not curr_bullish:
                    is_engulfing = (curr_body > prev_body) and (df['open'].iloc[-1] > df['close'].iloc[-2]) and (df['close'].iloc[-1] < df['open'].iloc[-2])
                elif not prev_bullish and curr_bullish:
                    is_engulfing = (curr_body > prev_body) and (df['open'].iloc[-1] < df['close'].iloc[-2]) and (df['close'].iloc[-1] > df['open'].iloc[-2])
                else:
                    is_engulfing = False
            else:
                is_engulfing = False
        else:
            body_ratio = np.nan
            is_doji = False
            is_hammer = False
            is_shooting_star = False
            is_engulfing = False

        # 计算价格突破
        if len(df) >= 20:
            # 计算前19个周期的最高价和最低价
            prev_high = df['high'].iloc[-20:-1].max()
            prev_low = df['low'].iloc[-20:-1].min()

            # 判断当前价格是否突破前N个周期的最高价或最低价
            breakout_high = df['close'].iloc[-1] > prev_high
            breakout_low = df['close'].iloc[-1] < prev_low
        else:
            breakout_high = False
            breakout_low = False

        return {
            'body_ratio': body_ratio,
            'is_doji': is_doji,
            'is_hammer': is_hammer,
            'is_shooting_star': is_shooting_star,
            'is_engulfing': is_engulfing,
            'breakout_high': breakout_high,
            'breakout_low': breakout_low
        }
    except Exception as e:
        print(f'计算价格行为指标失败: {e}')
        return {}


def calculate_quantitative_factors(df, timeframe):
    """
    计算量化因子

    Args:
        df (pandas.DataFrame): 价格数据
        timeframe (str): 时间周期

    Returns:
        dict: 量化因子
    """
    try:
        # 计算动量因子
        # 确保数据足够计算各个周期的动量
        momentum_5 = np.nan
        momentum_10 = np.nan
        momentum_20 = np.nan

        if len(df) > 6:  # 需要至少7个数据点计算5周期动量
            # 短期动量（5周期）
            momentum_5 = df['close'].iloc[-1] / df['close'].iloc[-6] - 1

        if len(df) > 11:  # 需要至少12个数据点计算10周期动量
            # 中期动量（10周期）
            momentum_10 = df['close'].iloc[-1] / df['close'].iloc[-11] - 1

        if len(df) > 21:  # 需要至少22个数据点计算20周期动量
            # 长期动量（20周期）
            momentum_20 = df['close'].iloc[-1] / df['close'].iloc[-21] - 1

        # 计算均值回归因子
        current_deviation = np.nan
        deviation_zscore = np.nan

        # 确保有足够的数据计算20周期移动平均线
        if len(df) >= 20:
            # 价格相对于20周期移动平均线的偏离度
            ma20 = df['close'].rolling(20).mean()

            # 确保移动平均线有值
            if not pd.isna(ma20.iloc[-1]) and ma20.iloc[-1] != 0:
                deviation_ma20 = (df['close'] / ma20 - 1) * 100

                # 当前偏离度
                current_deviation = deviation_ma20.iloc[-1]

                # 如果有足够的历史数据，计算偏离度Z-Score
                if len(df) >= 50:
                    # 确保有足够的有效数据
                    valid_deviations = deviation_ma20.iloc[-50:].dropna()
                    if len(valid_deviations) >= 10:  # 至少需要10个有效数据点
                        deviation_mean = valid_deviations.mean()
                        deviation_std = valid_deviations.std()

                        if deviation_std != 0:
                            deviation_zscore = (current_deviation - deviation_mean) / deviation_std
                        else:
                            deviation_zscore = 0

        # 计算波动率因子
        realized_vol = np.nan
        vol_change = np.nan

        # 确保有足够的数据计算波动率
        returns = df['returns'].dropna()

        if len(returns) >= 20:
            # 实现波动率（已实现波动率）
            realized_vol = returns.iloc[-20:].std() * np.sqrt(252)

            # 波动率变化率
            if len(returns) >= 40:
                prev_vol = returns.iloc[-40:-20].std() * np.sqrt(252)
                if not pd.isna(prev_vol) and prev_vol != 0:
                    vol_change = (realized_vol / prev_vol - 1) * 100

        # 计算流动性因子
        volume_change = np.nan
        volume_strength = np.nan

        if 'volume' in df.columns:
            # 确保有足够的数据计算成交量变化率
            if len(df) >= 20:
                # 计算最近20个周期的平均成交量
                recent_volumes = df['volume'].iloc[-20:]
                if not recent_volumes.empty and not recent_volumes.isna().all():
                    avg_volume = recent_volumes.mean()

                    # 计算成交量相对强度
                    if not pd.isna(avg_volume) and avg_volume != 0 and not pd.isna(df['volume'].iloc[-1]):
                        volume_strength = df['volume'].iloc[-1] / avg_volume

                    # 如果有足够的数据，计算成交量变化率
                    if len(df) >= 40:
                        prev_volumes = df['volume'].iloc[-40:-20]
                        if not prev_volumes.empty and not prev_volumes.isna().all():
                            prev_avg_volume = prev_volumes.mean()

                            if not pd.isna(prev_avg_volume) and prev_avg_volume != 0 and not pd.isna(avg_volume):
                                volume_change = (avg_volume / prev_avg_volume - 1) * 100

        # 计算时间因子（根据不同时间周期的特性）
        is_overlap_session = False
        is_asian_session = False
        is_european_session = False
        is_american_session = False

        if timeframe == '15min' and 'time' in df.columns and len(df) > 0:
            # 对于15分钟周期，计算日内效应
            try:
                # 确保有时间数据
                if pd.isna(df['time'].iloc[-1]):
                    # 如果时间数据为空，使用默认值
                    pass
                elif isinstance(df['time'].iloc[-1], str):
                    # 如果时间是字符串格式
                    time_parts = df['time'].iloc[-1].split()
                    if len(time_parts) > 1:
                        time_parts = time_parts[1].split(':')
                        if len(time_parts) > 0:
                            hour = int(time_parts[0])

                            # 判断是否在欧美交易时段重叠期
                            is_overlap_session = 13 <= hour <= 16  # UTC+8时间

                            # 判断是否在亚洲交易时段
                            is_asian_session = 0 <= hour <= 8  # UTC+8时间

                            # 判断是否在欧洲交易时段
                            is_european_session = 9 <= hour <= 17  # UTC+8时间

                            # 判断是否在美洲交易时段
                            is_american_session = 15 <= hour <= 23  # UTC+8时间
                elif hasattr(df['time'].iloc[-1], 'hour'):
                    # 如果时间是datetime格式
                    hour = df['time'].iloc[-1].hour

                    # 判断是否在欧美交易时段重叠期
                    is_overlap_session = 13 <= hour <= 16  # UTC+8时间

                    # 判断是否在亚洲交易时段
                    is_asian_session = 0 <= hour <= 8  # UTC+8时间

                    # 判断是否在欧洲交易时段
                    is_european_session = 9 <= hour <= 17  # UTC+8时间

                    # 判断是否在美洲交易时段
                    is_american_session = 15 <= hour <= 23  # UTC+8时间
            except Exception as e:
                print(f'计算时间因子失败: {e}')

        return {
            'momentum_5': momentum_5,
            'momentum_10': momentum_10,
            'momentum_20': momentum_20,
            'deviation_ma20': current_deviation,
            'deviation_zscore': deviation_zscore,
            'realized_volatility': realized_vol,
            'volatility_change': vol_change,
            'volume_change': volume_change,
            'volume_strength': volume_strength,
            'is_overlap_session': is_overlap_session,
            'is_asian_session': is_asian_session,
            'is_european_session': is_european_session,
            'is_american_session': is_american_session
        }
    except Exception as e:
        print(f'计算量化因子失败: {e}')
        return {}
