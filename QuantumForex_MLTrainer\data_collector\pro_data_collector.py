#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Pro端实时数据收集器
收集Pro端的交易记录、参数优化历史、LLM分析数据等
用于改进Trainer端的深度训练
"""

import json
import requests
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

class ProDataCollector:
    """Pro端数据收集器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 配置
        from config.network_config import network_config
        self.network_config = network_config

        # API配置
        self.api_base = f"http://127.0.0.1:8081/api"  # 本地测试

        # 本地存储
        self.data_path = Path('data/pro_feedback')
        self.data_path.mkdir(parents=True, exist_ok=True)

        # 数据库
        self.db_path = self.data_path / 'pro_feedback.db'
        self._init_database()

    def _init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 交易记录表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS trade_records (
                        trade_id TEXT PRIMARY KEY,
                        symbol TEXT,
                        action TEXT,
                        entry_time TEXT,
                        entry_price REAL,
                        volume REAL,
                        strategy_used TEXT,
                        confidence REAL,
                        market_condition TEXT,
                        exit_time TEXT,
                        exit_price REAL,
                        exit_reason TEXT,
                        profit_loss REAL,
                        profit_loss_pct REAL,
                        holding_duration_minutes INTEGER,
                        max_favorable_excursion REAL,
                        max_adverse_excursion REAL,
                        collected_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 参数优化记录表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS parameter_optimizations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        parameter_name TEXT,
                        old_value REAL,
                        new_value REAL,
                        improvement_score REAL,
                        confidence REAL,
                        reason TEXT,
                        optimization_time TEXT,
                        collected_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # LLM分析记录表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS llm_analyses (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        analysis_type TEXT,
                        risk_score REAL,
                        recommendations TEXT,
                        portfolio_advice TEXT,
                        risk_management TEXT,
                        analysis_time TEXT,
                        collected_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                conn.commit()
                self.logger.info("Pro数据收集数据库初始化完成")

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")

    def collect_all_data(self, days: int = 7) -> Dict[str, Any]:
        """收集所有Pro端数据"""
        try:
            self.logger.info(f"🔄 开始收集Pro端数据 (最近{days}天)...")

            results = {
                'trade_records': self.collect_trade_records(days),
                'parameter_optimizations': self.collect_parameter_optimizations(days),
                'llm_analyses': self.collect_llm_analyses(days),
                'collection_time': datetime.now().isoformat()
            }

            # 保存收集摘要
            self._save_collection_summary(results)

            self.logger.info("✅ Pro端数据收集完成")
            return results

        except Exception as e:
            self.logger.error(f"❌ 收集Pro端数据失败: {e}")
            return {}

    def collect_trade_records(self, days: int = 7) -> List[Dict]:
        """收集交易记录"""
        try:
            self.logger.info(f"📊 收集交易记录 (最近{days}天)...")

            # 尝试从Pro端API获取
            api_data = self._fetch_from_api('/trading/records', {'days': days})

            if api_data:
                # 保存到本地数据库
                self._save_trade_records(api_data)
                self.logger.info(f"✅ 收集到{len(api_data)}条交易记录")
                return api_data
            else:
                # API失败，尝试从本地数据库获取
                local_data = self._load_trade_records_from_db(days)
                if local_data:
                    self.logger.info(f"✅ 从本地数据库获取{len(local_data)}条交易记录")
                    return local_data
                else:
                    # 没有数据时返回空列表
                    self.logger.warning("⚠️ 没有可用的交易记录数据")
                    return []

        except Exception as e:
            self.logger.error(f"❌ 收集交易记录失败: {e}")
            return []

    def collect_parameter_optimizations(self, days: int = 7) -> List[Dict]:
        """收集参数优化历史"""
        try:
            self.logger.info(f"📊 收集参数优化历史 (最近{days}天)...")

            api_data = self._fetch_from_api('/optimization/history', {'days': days})

            if api_data:
                self._save_parameter_optimizations(api_data)
                self.logger.info(f"✅ 收集到{len(api_data)}条参数优化记录")
                return api_data
            else:
                # API失败，尝试从本地数据库获取
                local_data = self._load_parameter_optimizations_from_db(days)
                if local_data:
                    self.logger.info(f"✅ 从本地数据库获取{len(local_data)}条参数优化记录")
                    return local_data
                else:
                    # 没有数据时返回空列表
                    self.logger.warning("⚠️ 没有可用的参数优化数据")
                    return []

        except Exception as e:
            self.logger.error(f"❌ 收集参数优化历史失败: {e}")
            return []

    def collect_llm_analyses(self, days: int = 7) -> List[Dict]:
        """收集LLM分析历史"""
        try:
            self.logger.info(f"📊 收集LLM分析历史 (最近{days}天)...")

            api_data = self._fetch_from_api('/llm/analysis_history', {'days': days})

            if api_data:
                self._save_llm_analyses(api_data)
                self.logger.info(f"✅ 收集到{len(api_data)}条LLM分析记录")
                return api_data
            else:
                # API失败，尝试从本地数据库获取
                local_data = self._load_llm_analyses_from_db(days)
                if local_data:
                    self.logger.info(f"✅ 从本地数据库获取{len(local_data)}条LLM分析记录")
                    return local_data
                else:
                    # 没有数据时返回空列表
                    self.logger.warning("⚠️ 没有可用的LLM分析数据")
                    return []

        except Exception as e:
            self.logger.error(f"❌ 收集LLM分析历史失败: {e}")
            return []

    def _fetch_from_api(self, endpoint: str, params: Dict = None) -> Optional[List[Dict]]:
        """从Pro端API获取数据"""
        try:
            url = f"{self.api_base}{endpoint}"

            response = requests.get(url, params=params, timeout=30)

            if response.status_code == 200:
                data = response.json()
                if isinstance(data, dict) and 'data' in data:
                    return data['data']
                elif isinstance(data, list):
                    return data
                else:
                    return []
            else:
                self.logger.warning(f"API请求失败: {response.status_code}")
                return None

        except requests.exceptions.RequestException as e:
            self.logger.warning(f"API连接失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"API请求异常: {e}")
            return None

    def _save_trade_records(self, records: List[Dict]):
        """保存交易记录到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                for record in records:
                    conn.execute("""
                        INSERT OR REPLACE INTO trade_records
                        (trade_id, symbol, action, entry_time, entry_price, volume,
                         strategy_used, confidence, market_condition, exit_time,
                         exit_price, exit_reason, profit_loss, profit_loss_pct,
                         holding_duration_minutes, max_favorable_excursion,
                         max_adverse_excursion)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        record.get('trade_id'),
                        record.get('symbol'),
                        record.get('action'),
                        record.get('entry_time'),
                        record.get('entry_price'),
                        record.get('volume'),
                        record.get('strategy_used'),
                        record.get('confidence'),
                        record.get('market_condition'),
                        record.get('exit_time'),
                        record.get('exit_price'),
                        record.get('exit_reason'),
                        record.get('profit_loss'),
                        record.get('profit_loss_pct'),
                        record.get('holding_duration_minutes'),
                        record.get('max_favorable_excursion'),
                        record.get('max_adverse_excursion')
                    ))
                conn.commit()

        except Exception as e:
            self.logger.error(f"保存交易记录失败: {e}")

    def _save_parameter_optimizations(self, optimizations: List[Dict]):
        """保存参数优化记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                for opt in optimizations:
                    conn.execute("""
                        INSERT INTO parameter_optimizations
                        (parameter_name, old_value, new_value, improvement_score,
                         confidence, reason, optimization_time)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        opt.get('parameter_name'),
                        opt.get('old_value'),
                        opt.get('new_value'),
                        opt.get('improvement_score'),
                        opt.get('confidence'),
                        opt.get('reason'),
                        opt.get('optimization_time')
                    ))
                conn.commit()

        except Exception as e:
            self.logger.error(f"保存参数优化记录失败: {e}")

    def _save_llm_analyses(self, analyses: List[Dict]):
        """保存LLM分析记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                for analysis in analyses:
                    conn.execute("""
                        INSERT INTO llm_analyses
                        (analysis_type, risk_score, recommendations, portfolio_advice,
                         risk_management, analysis_time)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        analysis.get('analysis_type'),
                        analysis.get('risk_score'),
                        json.dumps(analysis.get('recommendations', [])),
                        analysis.get('portfolio_advice'),
                        analysis.get('risk_management'),
                        analysis.get('analysis_time')
                    ))
                conn.commit()

        except Exception as e:
            self.logger.error(f"保存LLM分析记录失败: {e}")

    def _save_collection_summary(self, results: Dict):
        """保存收集摘要"""
        try:
            summary_file = self.data_path / f"collection_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)

            self.logger.info(f"收集摘要已保存: {summary_file}")

        except Exception as e:
            self.logger.error(f"保存收集摘要失败: {e}")

    def _load_trade_records_from_db(self, days: int) -> List[Dict]:
        """从本地数据库加载交易记录"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM trade_records
                    WHERE collected_at >= ?
                    ORDER BY entry_time DESC
                """, (cutoff_date.isoformat(),))

                records = []
                for row in cursor.fetchall():
                    records.append(dict(row))

                return records

        except Exception as e:
            self.logger.error(f"从数据库加载交易记录失败: {e}")
            return []

    def _load_parameter_optimizations_from_db(self, days: int) -> List[Dict]:
        """从本地数据库加载参数优化记录"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM parameter_optimizations
                    WHERE collected_at >= ?
                    ORDER BY optimization_time DESC
                """, (cutoff_date.isoformat(),))

                records = []
                for row in cursor.fetchall():
                    records.append(dict(row))

                return records

        except Exception as e:
            self.logger.error(f"从数据库加载参数优化记录失败: {e}")
            return []

    def _load_llm_analyses_from_db(self, days: int) -> List[Dict]:
        """从本地数据库加载LLM分析记录"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM llm_analyses
                    WHERE collected_at >= ?
                    ORDER BY analysis_time DESC
                """, (cutoff_date.isoformat(),))

                records = []
                for row in cursor.fetchall():
                    record = dict(row)
                    # 解析JSON字段
                    if record.get('recommendations'):
                        try:
                            record['recommendations'] = json.loads(record['recommendations'])
                        except:
                            record['recommendations'] = []
                    records.append(record)

                return records

        except Exception as e:
            self.logger.error(f"从数据库加载LLM分析记录失败: {e}")
            return []



# 创建全局实例
pro_data_collector = ProDataCollector()
