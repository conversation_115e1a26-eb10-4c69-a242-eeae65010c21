"""
QuantumForex MLTrainer 训练配置
专门用于机器学习模型训练的配置文件
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class TrainingConfig:
    """机器学习训练配置"""

    # ==================== 数据库配置 ====================
    DATABASE_CONFIG = {
        'host': os.getenv('PIZZA_QUOTES_DB_HOST', 'pizza-wnet-db1.mysql.rds.aliyuncs.com'),
        'port': int(os.getenv('PIZZA_QUOTES_DB_PORT', '6688')),
        'user': os.getenv('PIZZA_QUOTES_DB_USER', 'ea_quote_srv'),
        'password': os.getenv('PIZZA_QUOTES_DB_PASSWORD', 'pizza666!'),
        'database': os.getenv('PIZZA_QUOTES_DB_NAME', 'pizza_quotes'),
        'charset': 'utf8mb4'
    }

    # ==================== 数据收集配置 ====================
    DATA_CONFIG = {
        # 数据源配置
        'data_sources': {
            'pizza_quotes': True,      # 主要数据源
            'mt4_history': True,       # MT4历史数据
            'trade_results': True,     # 交易结果数据
        },

        # 数据范围
        'data_range': {
            'lookback_days': 365,      # 历史数据天数
            'min_samples': 1000,       # 最小样本数
            'max_samples': 50000,      # 最大样本数
            'update_frequency': 'daily' # 数据更新频率
        },

        # 支持的货币对
        'currency_pairs': [
            'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD',
            'USDCHF', 'USDCAD', 'USDJPY'
        ],

        # 数据质量控制
        'quality_control': {
            'remove_outliers': True,
            'fill_missing_values': True,
            'validate_data_integrity': True,
            'outlier_threshold': 3.0    # 标准差倍数
        }
    }

    # ==================== 特征工程配置 ====================
    FEATURE_CONFIG = {
        # 技术指标特征
        'technical_indicators': {
            'moving_averages': [5, 10, 20, 50, 100, 200],
            'ema_periods': [8, 13, 21, 34, 55, 89],
            'rsi_periods': [14, 21, 28],
            'macd_params': [(12, 26, 9), (5, 35, 5)],
            'bollinger_bands': [(20, 2), (50, 2.5)],
            'stochastic': [(14, 3, 3), (21, 5, 5)],
            'atr_periods': [14, 21, 28],
            'adx_period': 14,
            'cci_period': 20,
            'williams_r_period': 14
        },

        # 价格特征
        'price_features': {
            'price_changes': [1, 3, 5, 10, 20],    # N期价格变化
            'returns': [1, 3, 5, 10, 20],          # N期收益率
            'volatility_windows': [5, 10, 20, 50], # 波动率窗口
            'volume_features': True,                # 成交量特征
            'ohlc_ratios': True                     # OHLC比率特征
        },

        # 时间特征
        'time_features': {
            'hour_of_day': True,
            'day_of_week': True,
            'day_of_month': True,
            'month_of_year': True,
            'quarter': True,
            'is_weekend': True,
            'market_session': True  # 亚洲/欧洲/美洲时段
        },

        # 高级特征
        'advanced_features': {
            'fractal_dimension': True,      # 分形维度
            'hurst_exponent': True,         # 赫斯特指数
            'entropy_features': True,       # 熵特征
            'wavelet_features': True,       # 小波特征
            'fourier_features': True,       # 傅里叶特征
            'correlation_features': True,   # 相关性特征
            'regime_features': True         # 市场状态特征
        },

        # 特征选择
        'feature_selection': {
            'method': 'mutual_info',        # 特征选择方法
            'max_features': 200,            # 最大特征数
            'min_importance': 0.001,        # 最小重要性阈值
            'correlation_threshold': 0.95   # 相关性阈值
        }
    }

    # ==================== 模型训练配置 ====================
    MODEL_CONFIG = {
        # 数据分割
        'data_split': {
            'train_ratio': 0.7,
            'validation_ratio': 0.15,
            'test_ratio': 0.15,
            'time_series_split': True,      # 时间序列分割
            'walk_forward_validation': True  # 滚动验证
        },

        # 模型类型
        'model_types': {
            'random_forest': {
                'enabled': True,
                'n_estimators': [100, 200, 300],
                'max_depth': [10, 15, 20, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4],
                'max_features': ['sqrt', 'log2', None]
            },

            'xgboost': {
                'enabled': True,
                'n_estimators': [100, 200, 300],
                'max_depth': [6, 8, 10],
                'learning_rate': [0.01, 0.1, 0.2],
                'subsample': [0.8, 0.9, 1.0],
                'colsample_bytree': [0.8, 0.9, 1.0]
            },

            'lightgbm': {
                'enabled': True,
                'n_estimators': [100, 200, 300],
                'max_depth': [6, 8, 10],
                'learning_rate': [0.01, 0.1, 0.2],
                'num_leaves': [31, 50, 100],
                'feature_fraction': [0.8, 0.9, 1.0]
            },

            'catboost': {
                'enabled': True,
                'iterations': [100, 200, 300],
                'depth': [6, 8, 10],
                'learning_rate': [0.01, 0.1, 0.2],
                'l2_leaf_reg': [1, 3, 5]
            },

            'svm': {
                'enabled': True,
                'C': [0.1, 1, 10],
                'gamma': ['scale', 'auto', 0.001, 0.01],
                'kernel': ['rbf', 'poly', 'sigmoid']
            },

            'neural_network': {
                'enabled': False,  # 可选，需要更多计算资源
                'hidden_layer_sizes': [(100,), (100, 50), (200, 100, 50)],
                'activation': ['relu', 'tanh'],
                'alpha': [0.0001, 0.001, 0.01],
                'learning_rate': ['constant', 'adaptive']
            }
        },

        # 超参数优化
        'hyperparameter_optimization': {
            'method': 'optuna',             # 优化方法
            'n_trials': 100,                # 试验次数
            'timeout': 3600,                # 超时时间（秒）
            'n_jobs': -1,                   # 并行作业数
            'cv_folds': 5                   # 交叉验证折数
        },

        # 集成学习
        'ensemble': {
            'enabled': True,
            'methods': ['voting', 'stacking', 'blending'],
            'voting_weights': 'uniform',    # 投票权重
            'stacking_cv': 5               # 堆叠交叉验证
        }
    }

    # ==================== 训练流程配置 ====================
    TRAINING_FLOW = {
        # 训练调度
        'schedule': {
            'auto_retrain': True,
            'retrain_frequency': 'daily',   # 重训练频率
            'retrain_threshold': 0.05,      # 性能下降阈值
            'max_training_time': 7200       # 最大训练时间（秒）
        },

        # 模型验证
        'validation': {
            'cross_validation': True,
            'cv_folds': 5,
            'stratified': True,
            'shuffle': True,
            'random_state': 42
        },

        # 早停机制
        'early_stopping': {
            'enabled': True,
            'patience': 10,
            'min_delta': 0.001,
            'restore_best_weights': True
        },

        # 模型保存
        'model_saving': {
            'save_best_only': True,
            'save_format': 'joblib',        # 保存格式
            'compression': 3,               # 压缩级别
            'backup_models': True           # 备份旧模型
        }
    }

    # ==================== 性能评估配置 ====================
    EVALUATION_CONFIG = {
        # 评估指标
        'metrics': {
            'classification': [
                'accuracy', 'precision', 'recall', 'f1_score',
                'roc_auc', 'log_loss', 'matthews_corrcoef'
            ],
            'regression': [
                'mse', 'rmse', 'mae', 'r2_score',
                'mean_absolute_percentage_error'
            ],
            'trading_specific': [
                'sharpe_ratio', 'max_drawdown', 'win_rate',
                'profit_factor', 'calmar_ratio'
            ]
        },

        # 回测配置
        'backtesting': {
            'initial_capital': 10000,
            'transaction_cost': 0.0001,     # 交易成本
            'slippage': 0.0001,             # 滑点
            'position_sizing': 'fixed_risk', # 仓位大小方法
            'risk_per_trade': 0.02          # 每笔交易风险
        }
    }

    # ==================== 系统配置 ====================
    SYSTEM_CONFIG = {
        # 计算资源
        'computing': {
            'n_jobs': -1,                   # 并行作业数
            'memory_limit': '8GB',          # 内存限制
            'gpu_enabled': False,           # GPU加速
            'distributed': False            # 分布式计算
        },

        # 日志配置
        'logging': {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_rotation': '1 day',
            'retention': '30 days'
        },

        # 监控配置
        'monitoring': {
            'enable_progress_bar': True,
            'log_frequency': 100,           # 日志频率
            'save_checkpoints': True,       # 保存检查点
            'checkpoint_frequency': 1000    # 检查点频率
        }
    }

    # ==================== 数据库配置 ====================
    DATABASE_CONFIG = {
        'pizza_quotes': {
            'host': 'pizza-wnet-db1.mysql.rds.aliyuncs.com',
            'port': 6688,
            'user': 'ea_quote_srv',
            'password': 'pizza666!',
            'database': 'pizza_quotes',
            'charset': 'utf8mb4'
        }
    }

# 创建全局配置实例
training_config = TrainingConfig()
