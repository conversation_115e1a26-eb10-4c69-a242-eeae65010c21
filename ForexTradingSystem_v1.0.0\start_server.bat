@echo off
echo ========================================
echo Forex Trading System - Server Startup
echo ========================================

REM Set environment variables
set FLASK_ENV=production
set PYTHONPATH=%~dp0
set PYTHONIOENCODING=utf-8

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not installed or not in PATH
    echo Please install Python 3.9+ first
    pause
    exit /b 1
)

echo SUCCESS: Python environment check passed

REM Check virtual environment
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Virtual environment creation failed
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Virtual environment activation failed
    pause
    exit /b 1
)

REM Install dependencies
echo Installing/updating dependencies...
pip install -r requirements.txt

REM Check configuration file
if not exist ".env.local" (
    echo WARNING: Configuration file .env.local not found
    echo Please copy .env.example to .env.local and configure
    pause
    exit /b 1
)

REM Create necessary directories
if not exist "logs" mkdir logs
if not exist "app\data" mkdir app\data
if not exist "app\data\errors" mkdir app\data\errors

REM Start server
echo Starting Forex Trading System...
echo Access URL: http://localhost:5000
echo Press Ctrl+C to stop server
echo ========================================

python run.py

pause
