"""
智能货币对选择器
基于市场机会、风险控制、收益最大化的多货币对分析系统
"""

import time
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import numpy as np
from app.utils.data_source_adapter import DataSourceAdapter
from app.utils.technical_indicators import calculate_indicators
from app.utils.logger_manager import log_analysis, LogLevel

class IntelligentPairSelector:
    """智能货币对选择器 - 专注于收益最大化和风险控制"""

    def __init__(self):
        self.supported_symbols = [
            'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD',
            'USDCHF', 'USDCAD', 'USDJPY', 'GOLD'
        ]
        self.data_adapter = DataSourceAdapter()

        # 风险控制参数
        self.max_pairs_per_analysis = 2  # 每次最多分析2个货币对
        self.min_opportunity_score = 60  # 最低机会评分
        self.max_correlation = 0.7       # 最大相关性阈值

        # 收益优化参数
        self.volatility_weight = 0.3     # 波动率权重
        self.trend_weight = 0.4          # 趋势强度权重
        self.momentum_weight = 0.3       # 动量权重

    def select_optimal_currency_pairs(self) -> List[str]:
        """
        选择最优的货币对进行分析

        Returns:
            List[str]: 选择的货币对列表（1-2个）
        """
        try:
            log_analysis("开始智能货币对选择", LogLevel.INFO)

            # 第一步：快速技术扫描所有货币对
            opportunities = self._scan_all_pairs_technical()

            if not opportunities:
                log_analysis("技术扫描失败，回退到EURUSD", LogLevel.WARNING)
                return ['EURUSD']

            # 第二步：过滤低质量机会
            filtered_opportunities = [
                opp for opp in opportunities
                if opp['score'] >= self.min_opportunity_score
            ]

            if not filtered_opportunities:
                log_analysis("没有找到高质量机会，选择评分最高的货币对", LogLevel.INFO)
                return [opportunities[0]['symbol']]

            # 第三步：相关性过滤
            selected_pairs = self._filter_by_correlation(filtered_opportunities)

            # 第四步：风险平衡
            final_selection = self._balance_portfolio_risk(selected_pairs)

            selected_symbols = [pair['symbol'] for pair in final_selection]
            log_analysis(f"智能选择完成: {selected_symbols}", LogLevel.INFO)

            return selected_symbols

        except Exception as e:
            log_analysis(f"智能选择失败: {e}，回退到EURUSD", LogLevel.ERROR)
            return ['EURUSD']

    def _scan_all_pairs_technical(self) -> List[Dict]:
        """
        快速技术扫描所有货币对（无LLM成本）

        Returns:
            List[Dict]: 机会评分列表
        """
        opportunities = []

        for symbol in self.supported_symbols:
            try:
                # 获取技术数据
                data_15m = self.data_adapter.get_timeframe_data(symbol, '15min', 50)
                data_1h = self.data_adapter.get_timeframe_data(symbol, '60min', 24)

                if not data_15m or not data_1h:
                    continue

                # 计算技术指标
                indicators_15m = calculate_indicators(data_15m, '15min')
                indicators_1h = calculate_indicators(data_1h, '1h')

                if not indicators_15m or not indicators_1h:
                    continue

                # 计算机会评分
                score = self._calculate_opportunity_score(
                    symbol, data_15m, data_1h, indicators_15m, indicators_1h
                )

                opportunities.append({
                    'symbol': symbol,
                    'score': score,
                    'data_15m': data_15m,
                    'data_1h': data_1h,
                    'indicators_15m': indicators_15m,
                    'indicators_1h': indicators_1h,
                    'volatility': self._calculate_volatility(data_15m),
                    'trend_strength': self._calculate_trend_strength(indicators_15m, indicators_1h),
                    'momentum': self._calculate_momentum(indicators_15m)
                })

            except Exception as e:
                log_analysis(f"扫描{symbol}失败: {e}", LogLevel.WARNING)
                continue

        # 按评分排序
        opportunities.sort(key=lambda x: x['score'], reverse=True)

        log_analysis(f"技术扫描完成，发现{len(opportunities)}个有效机会", LogLevel.INFO)
        for opp in opportunities[:5]:  # 显示前5个
            log_analysis(f"{opp['symbol']}: {opp['score']:.1f}分", LogLevel.INFO)

        return opportunities

    def _calculate_opportunity_score(self, symbol: str, data_15m: List[Dict],
                                   data_1h: List[Dict], indicators_15m: Dict,
                                   indicators_1h: Dict) -> float:
        """
        计算机会评分（0-100分）

        Args:
            symbol: 货币对
            data_15m: 15分钟数据
            data_1h: 1小时数据
            indicators_15m: 15分钟技术指标
            indicators_1h: 1小时技术指标

        Returns:
            float: 机会评分
        """
        try:
            score = 0.0

            # 1. 趋势强度评分 (40分)
            trend_score = self._score_trend_strength(indicators_15m, indicators_1h)
            score += trend_score * 0.4

            # 2. 动量评分 (30分)
            momentum_score = self._score_momentum(indicators_15m)
            score += momentum_score * 0.3

            # 3. 波动率评分 (20分)
            volatility_score = self._score_volatility(data_15m)
            score += volatility_score * 0.2

            # 4. 技术信号评分 (10分)
            signal_score = self._score_technical_signals(indicators_15m, indicators_1h)
            score += signal_score * 0.1

            # 5. 特殊加分项
            # 移除EURUSD的固定加分，让所有货币对公平竞争
            # 只对GOLD进行风险调整
            if symbol == 'GOLD':
                score -= 10  # GOLD风险较高，减分

            return max(0, min(100, score))

        except Exception as e:
            log_analysis(f"计算{symbol}机会评分失败: {e}", LogLevel.WARNING)
            return 0.0

    def _score_trend_strength(self, indicators_15m: Dict, indicators_1h: Dict) -> float:
        """评分趋势强度 (0-100)"""
        try:
            score = 0.0

            # 15分钟趋势
            if 'ma20' in indicators_15m and 'currentPrice' in indicators_15m:
                ma20_15m = indicators_15m['ma20']
                current_price = indicators_15m['currentPrice']

                # 价格与MA20的关系
                price_ma_diff = abs(current_price - ma20_15m) / ma20_15m * 100
                if price_ma_diff > 0.1:  # 价格偏离MA20超过0.1%
                    score += 30

            # 1小时趋势确认
            if 'ma20' in indicators_1h and 'currentPrice' in indicators_1h:
                ma20_1h = indicators_1h['ma20']
                current_price = indicators_1h['currentPrice']

                # 多时间框架趋势一致性
                if (current_price > ma20_15m and current_price > ma20_1h) or \
                   (current_price < ma20_15m and current_price < ma20_1h):
                    score += 40

            # MACD趋势
            if 'macd' in indicators_15m:
                macd = indicators_15m['macd']
                if abs(macd) > 0.0001:  # MACD有明显信号
                    score += 30

            return score

        except Exception:
            return 0.0

    def _score_momentum(self, indicators_15m: Dict) -> float:
        """评分动量 (0-100)"""
        try:
            score = 0.0

            # RSI动量
            if 'rsi' in indicators_15m:
                rsi = indicators_15m['rsi']
                # RSI在超买超卖区域给高分
                if rsi < 30 or rsi > 70:
                    score += 50
                # RSI在中性区域给中等分
                elif 40 <= rsi <= 60:
                    score += 20

            # MACD动量
            if 'macd' in indicators_15m and 'macdSignal' in indicators_15m:
                macd = indicators_15m['macd']
                macd_signal = indicators_15m['macdSignal']

                # MACD金叉死叉
                if (macd > macd_signal and macd > 0) or (macd < macd_signal and macd < 0):
                    score += 50

            return score

        except Exception:
            return 0.0

    def _score_volatility(self, data_15m: List[Dict]) -> float:
        """评分波动率 (0-100)"""
        try:
            if len(data_15m) < 20:
                return 0.0

            # 计算ATR（平均真实波幅）
            atr_values = []
            for i in range(1, min(20, len(data_15m))):
                high = data_15m[i]['high']
                low = data_15m[i]['low']
                prev_close = data_15m[i-1]['close']

                tr = max(
                    high - low,
                    abs(high - prev_close),
                    abs(low - prev_close)
                )
                atr_values.append(tr)

            if not atr_values:
                return 0.0

            avg_atr = sum(atr_values) / len(atr_values)
            current_price = data_15m[-1]['close']

            # 波动率百分比
            volatility_pct = (avg_atr / current_price) * 100

            # 适中的波动率得高分
            if 0.05 <= volatility_pct <= 0.15:
                return 100
            elif 0.02 <= volatility_pct <= 0.25:
                return 70
            else:
                return 30

        except Exception:
            return 0.0

    def _score_technical_signals(self, indicators_15m: Dict, indicators_1h: Dict) -> float:
        """评分技术信号 (0-100)"""
        try:
            score = 0.0

            # 布林带信号
            if all(k in indicators_15m for k in ['bollinger_upper', 'bollinger_lower', 'currentPrice']):
                price = indicators_15m['currentPrice']
                upper = indicators_15m['bollinger_upper']
                lower = indicators_15m['bollinger_lower']

                # 价格接近布林带边界
                if price >= upper * 0.99 or price <= lower * 1.01:
                    score += 50

            # 支撑阻力信号
            # 这里可以添加更复杂的支撑阻力计算
            score += 50  # 基础分

            return score

        except Exception:
            return 0.0

    def _calculate_volatility(self, data_15m: List[Dict]) -> float:
        """计算波动率"""
        try:
            if len(data_15m) < 10:
                return 0.0

            prices = [float(d['close']) for d in data_15m[-10:]]
            returns = [prices[i]/prices[i-1] - 1 for i in range(1, len(prices))]

            if not returns:
                return 0.0

            return np.std(returns) * 100  # 转换为百分比

        except Exception:
            return 0.0

    def _calculate_trend_strength(self, indicators_15m: Dict, indicators_1h: Dict) -> float:
        """计算趋势强度"""
        try:
            strength = 0.0

            # 基于MA的趋势强度
            if 'ma20' in indicators_15m and 'currentPrice' in indicators_15m:
                price = indicators_15m['currentPrice']
                ma20 = indicators_15m['ma20']
                strength += abs(price - ma20) / ma20

            return min(1.0, strength)

        except Exception:
            return 0.0

    def _calculate_momentum(self, indicators_15m: Dict) -> float:
        """计算动量"""
        try:
            momentum = 0.0

            # 基于RSI的动量
            if 'rsi' in indicators_15m:
                rsi = indicators_15m['rsi']
                # RSI偏离50的程度
                momentum += abs(rsi - 50) / 50

            return min(1.0, momentum)

        except Exception:
            return 0.0

    def _filter_by_correlation(self, opportunities: List[Dict]) -> List[Dict]:
        """相关性过滤"""
        if len(opportunities) <= 1:
            return opportunities

        selected = [opportunities[0]]  # 选择评分最高的

        for opp in opportunities[1:]:
            # 检查与已选择货币对的相关性
            is_correlated = False
            for selected_opp in selected:
                correlation = self._calculate_correlation(opp, selected_opp)
                if correlation > self.max_correlation:
                    is_correlated = True
                    break

            if not is_correlated:
                selected.append(opp)
                if len(selected) >= self.max_pairs_per_analysis:
                    break

        return selected

    def _calculate_correlation(self, opp1: Dict, opp2: Dict) -> float:
        """计算两个货币对的相关性"""
        try:
            # 简化的相关性计算
            # 基于货币对名称的相关性估算
            symbol1 = opp1['symbol']
            symbol2 = opp2['symbol']

            # 相同基础货币或报价货币的相关性较高
            if symbol1[:3] == symbol2[:3] or symbol1[3:] == symbol2[3:]:
                return 0.8

            # 特殊相关性
            high_correlation_pairs = [
                ('EURUSD', 'GBPUSD'),
                ('AUDUSD', 'NZDUSD'),
                ('USDCHF', 'USDCAD')
            ]

            for pair in high_correlation_pairs:
                if (symbol1 in pair and symbol2 in pair):
                    return 0.9

            return 0.3  # 默认低相关性

        except Exception:
            return 0.5

    def _balance_portfolio_risk(self, opportunities: List[Dict]) -> List[Dict]:
        """组合风险平衡"""
        if len(opportunities) <= 1:
            return opportunities

        # 风险评估
        balanced = []
        total_risk = 0.0
        max_total_risk = 1.0  # 最大总风险

        for opp in opportunities:
            # 计算单个货币对风险
            risk = self._calculate_pair_risk(opp)

            if total_risk + risk <= max_total_risk:
                balanced.append(opp)
                total_risk += risk

            if len(balanced) >= self.max_pairs_per_analysis:
                break

        return balanced if balanced else opportunities[:1]

    def _calculate_pair_risk(self, opportunity: Dict) -> float:
        """计算单个货币对风险"""
        try:
            risk = 0.0

            # 波动率风险
            volatility = opportunity.get('volatility', 0)
            risk += volatility * 0.5

            # 特殊货币对风险
            if opportunity['symbol'] == 'GOLD':
                risk += 0.3  # GOLD风险较高

            return min(1.0, risk)

        except Exception:
            return 0.5


def select_optimal_currency_pairs() -> List[str]:
    """
    全局函数：选择最优货币对

    Returns:
        List[str]: 选择的货币对列表
    """
    selector = IntelligentPairSelector()
    return selector.select_optimal_currency_pairs()
