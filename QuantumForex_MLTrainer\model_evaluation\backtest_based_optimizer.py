#!/usr/bin/env python3
"""
基于回测的模型优化器
真正基于交易效果选择最优模型
"""

import os
import joblib
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

from real_backtest_engine import RealBacktestEngine, BacktestMetrics

@dataclass
class ModelPerformance:
    """模型性能评估"""
    model_path: str
    model_type: str
    
    # 回测指标
    total_return: float
    win_rate: float
    max_drawdown: float
    sharpe_ratio: float
    prediction_accuracy: float
    
    # 综合评分
    overall_score: float
    
    # 其他信息
    total_trades: int
    model_size: int
    training_time: datetime

class BacktestBasedOptimizer:
    """基于回测的模型优化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.backtest_engine = RealBacktestEngine()
        
        # 评分权重
        self.score_weights = {
            'total_return': 0.30,      # 总收益率 30%
            'win_rate': 0.20,          # 胜率 20%
            'prediction_accuracy': 0.20, # 预测准确率 20%
            'sharpe_ratio': 0.15,      # 夏普比率 15%
            'max_drawdown': 0.15       # 最大回撤 15% (负向)
        }
    
    def find_optimal_models(self, days_for_backtest: int = 7) -> Dict[str, ModelPerformance]:
        """
        找到每种类型的最优模型
        基于真实回测结果
        """
        try:
            self.logger.info("🔍 开始寻找最优模型...")
            
            # 1. 扫描所有可用模型
            all_models = self._scan_all_models()
            
            if not all_models:
                self.logger.error("❌ 没有找到任何模型文件")
                return {}
            
            self.logger.info(f"📦 找到{len(all_models)}个模型文件")
            
            # 2. 按类型分组
            models_by_type = self._group_models_by_type(all_models)
            
            # 3. 对每种类型的模型进行回测评估
            optimal_models = {}
            
            for model_type, models in models_by_type.items():
                self.logger.info(f"\n🧪 评估{model_type}模型 ({len(models)}个候选)")
                
                best_model = self._find_best_model_for_type(
                    model_type, models, days_for_backtest
                )
                
                if best_model:
                    optimal_models[model_type] = best_model
                    self.logger.info(f"✅ {model_type}最优模型: {best_model.overall_score:.3f}分")
                else:
                    self.logger.warning(f"❌ {model_type}没有找到有效模型")
            
            # 4. 保存优化结果
            self._save_optimization_results(optimal_models)
            
            return optimal_models
            
        except Exception as e:
            self.logger.error(f"❌ 最优模型搜索失败: {e}")
            return {}
    
    def _scan_all_models(self) -> List[str]:
        """扫描所有模型文件"""
        try:
            models_dir = Path("data/models")
            if not models_dir.exists():
                return []
            
            # 查找所有pkl文件，排除scaler
            model_files = []
            for pkl_file in models_dir.glob("*.pkl"):
                if 'scaler' not in pkl_file.name:
                    model_files.append(str(pkl_file))
            
            return model_files
            
        except Exception as e:
            self.logger.error(f"❌ 模型扫描失败: {e}")
            return []
    
    def _group_models_by_type(self, model_files: List[str]) -> Dict[str, List[str]]:
        """按模型类型分组"""
        groups = {
            'price_prediction': [],
            'trend_classification': [],
            'volatility_prediction': [],
            'risk_assessment': []
        }
        
        for model_file in model_files:
            filename = os.path.basename(model_file)
            
            for model_type in groups.keys():
                if model_type in filename:
                    groups[model_type].append(model_file)
                    break
        
        # 移除空组
        return {k: v for k, v in groups.items() if v}
    
    def _find_best_model_for_type(self, model_type: str, models: List[str], 
                                 days_for_backtest: int) -> Optional[ModelPerformance]:
        """为特定类型找到最佳模型"""
        try:
            model_performances = []
            
            for model_path in models:
                try:
                    self.logger.info(f"   🔍 测试: {os.path.basename(model_path)}")
                    
                    # 运行回测
                    metrics = self.backtest_engine.run_comprehensive_backtest(
                        model_path=model_path,
                        data_days=days_for_backtest
                    )
                    
                    # 计算综合评分
                    overall_score = self._calculate_overall_score(metrics)
                    
                    # 获取模型信息
                    model_size = os.path.getsize(model_path)
                    training_time = datetime.fromtimestamp(os.path.getmtime(model_path))
                    
                    # 创建性能记录
                    performance = ModelPerformance(
                        model_path=model_path,
                        model_type=model_type,
                        total_return=metrics.total_return,
                        win_rate=metrics.win_rate,
                        max_drawdown=metrics.max_drawdown,
                        sharpe_ratio=metrics.sharpe_ratio,
                        prediction_accuracy=metrics.prediction_accuracy,
                        overall_score=overall_score,
                        total_trades=metrics.total_trades,
                        model_size=model_size,
                        training_time=training_time
                    )
                    
                    model_performances.append(performance)
                    
                    self.logger.info(f"      评分: {overall_score:.3f}")
                    self.logger.info(f"      收益: {metrics.total_return:.1%}")
                    self.logger.info(f"      胜率: {metrics.win_rate:.1%}")
                    
                except Exception as e:
                    self.logger.error(f"      ❌ 回测失败: {e}")
                    continue
            
            # 选择评分最高的模型
            if model_performances:
                best_model = max(model_performances, key=lambda x: x.overall_score)
                
                # 只有评分达到最低标准才认为是有效的
                min_score = 0.3  # 最低30%评分
                if best_model.overall_score >= min_score:
                    return best_model
                else:
                    self.logger.warning(f"   ⚠️ 最佳模型评分过低: {best_model.overall_score:.3f} < {min_score}")
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ {model_type}最佳模型搜索失败: {e}")
            return None
    
    def _calculate_overall_score(self, metrics: BacktestMetrics) -> float:
        """计算综合评分"""
        try:
            score = 0.0
            
            # 1. 总收益率 (30%)
            return_score = max(0, min(1, metrics.total_return * 10))  # 10%收益=满分
            score += return_score * self.score_weights['total_return']
            
            # 2. 胜率 (20%)
            win_rate_score = metrics.win_rate
            score += win_rate_score * self.score_weights['win_rate']
            
            # 3. 预测准确率 (20%)
            accuracy_score = metrics.prediction_accuracy
            score += accuracy_score * self.score_weights['prediction_accuracy']
            
            # 4. 夏普比率 (15%)
            sharpe_score = max(0, min(1, metrics.sharpe_ratio / 2))  # 夏普比率2=满分
            score += sharpe_score * self.score_weights['sharpe_ratio']
            
            # 5. 最大回撤 (15%, 负向指标)
            drawdown_score = max(0, 1 - metrics.max_drawdown * 5)  # 20%回撤=0分
            score += drawdown_score * self.score_weights['max_drawdown']
            
            return min(1.0, score)
            
        except Exception as e:
            self.logger.error(f"❌ 评分计算失败: {e}")
            return 0.0
    
    def _save_optimization_results(self, optimal_models: Dict[str, ModelPerformance]):
        """保存优化结果"""
        try:
            # 创建结果目录
            results_dir = Path("logs/optimization_results")
            results_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成结果文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = results_dir / f"optimal_models_{timestamp}.json"
            
            # 准备结果数据
            results = {
                'timestamp': datetime.now().isoformat(),
                'optimization_method': 'backtest_based',
                'total_optimal_models': len(optimal_models),
                'models': {}
            }
            
            for model_type, performance in optimal_models.items():
                results['models'][model_type] = {
                    'model_path': performance.model_path,
                    'overall_score': performance.overall_score,
                    'total_return': performance.total_return,
                    'win_rate': performance.win_rate,
                    'max_drawdown': performance.max_drawdown,
                    'sharpe_ratio': performance.sharpe_ratio,
                    'prediction_accuracy': performance.prediction_accuracy,
                    'total_trades': performance.total_trades,
                    'model_size': performance.model_size,
                    'training_time': performance.training_time.isoformat()
                }
            
            # 保存结果
            import json
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📄 优化结果已保存: {result_file}")
            
            # 创建最优模型配置文件
            config_file = results_dir / "current_optimal_models.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📄 当前最优配置: {config_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 保存优化结果失败: {e}")

def run_model_optimization():
    """运行模型优化"""
    print("🚀 基于回测的模型优化系统")
    print("="*50)
    print("🎯 真正基于交易效果选择最优模型")
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        optimizer = BacktestBasedOptimizer()
        
        # 运行优化
        optimal_models = optimizer.find_optimal_models(days_for_backtest=7)
        
        if optimal_models:
            print(f"\n🏆 找到{len(optimal_models)}个最优模型:")
            
            for model_type, performance in optimal_models.items():
                print(f"\n📊 {model_type}:")
                print(f"   模型: {os.path.basename(performance.model_path)}")
                print(f"   综合评分: {performance.overall_score:.3f}")
                print(f"   总收益率: {performance.total_return:.1%}")
                print(f"   胜率: {performance.win_rate:.1%}")
                print(f"   最大回撤: {performance.max_drawdown:.1%}")
                print(f"   预测准确率: {performance.prediction_accuracy:.1%}")
                print(f"   交易次数: {performance.total_trades}")
            
            print(f"\n✅ 模型优化完成！")
            print(f"💡 这些是基于真实回测效果的最优模型")
            return True
        else:
            print(f"\n❌ 没有找到符合标准的最优模型")
            print(f"💡 建议重新训练或调整评估标准")
            return False
            
    except Exception as e:
        print(f"❌ 模型优化失败: {e}")
        return False

if __name__ == "__main__":
    run_model_optimization()
