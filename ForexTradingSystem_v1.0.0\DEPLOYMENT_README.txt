# Forex Trading System Deployment Package

Version: v1.0.0
Package Date: 2025-05-24
Target OS: Windows Server 2012+

## Quick Start - Ready to Use!

1. Extract to target directory (e.g., C:\ForexTradingSystem\)
2. Install Python 3.9+ (ensure "Add to PATH" is checked)
3. Run start_server.bat to test the system (NO configuration needed!)
4. Run install_service.bat (as Administrator) to install as Windows service

## Pre-configured Settings

This package comes with all necessary configurations pre-set:
- Database: Pizza Quotes database (pizza-wnet-db1.mysql.rds.aliyuncs.com)
- LLM API: DeepSeek API with valid key
- MT4 Server: localhost:5555
- All system parameters optimized for production

## Important Files

- start_server.bat: Start script for testing
- install_service.bat: Service installer (requires Administrator)
- update_system.bat: System update script
- .env.example: Configuration template
- .env.local: Your actual configuration (edit this file)
- simple_dashboard.py: GUI monitoring dashboard

## Configuration (Optional)

The system is pre-configured and ready to use. However, if you need to modify settings:

Edit .env.local file:
- Database settings are already configured for Pizza Quotes DB
- LLM API key is already set for DeepSeek
- MT4 connection is configured for localhost:5555
- All production parameters are optimized

No manual configuration required for normal deployment!

## System Requirements

- Windows Server 2012 or higher
- Python 3.9+
- MySQL 5.7+ or MariaDB 10.3+
- 4GB+ RAM
- 10GB+ disk space
- Internet connection

## Service Management

Start service: sc start ForexTradingSystem
Stop service: sc stop ForexTradingSystem
Check status: sc query ForexTradingSystem

## Access

Web Interface: http://localhost:5000
GUI Dashboard: Run simple_dashboard.py

## Support

Check logs in the logs/ directory for troubleshooting.
For detailed instructions, see documentation files.
