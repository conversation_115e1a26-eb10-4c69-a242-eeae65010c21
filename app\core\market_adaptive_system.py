#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场状态自适应系统
根据市场状态动态调整交易策略、参数和行为
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

class MarketRegime(Enum):
    """市场制度"""
    STRONG_BULL = "强牛市"
    BULL = "牛市"
    WEAK_BULL = "弱牛市"
    SIDEWAYS_HIGH = "高位震荡"
    SIDEWAYS = "震荡"
    SIDEWAYS_LOW = "低位震荡"
    WEAK_BEAR = "弱熊市"
    BEAR = "熊市"
    STRONG_BEAR = "强熊市"
    VOLATILE = "高波动"
    CALM = "平静"

class TradingStrategy(Enum):
    """交易策略"""
    TREND_FOLLOWING = "趋势跟随"
    MEAN_REVERSION = "均值回归"
    BREAKOUT = "突破策略"
    MOMENTUM = "动量策略"
    CONTRARIAN = "逆向策略"
    SCALPING = "剥头皮"
    RANGE_TRADING = "区间交易"
    CONSERVATIVE = "保守策略"

class TimeframeWeight(Enum):
    """时间框架权重"""
    M15_DOMINANT = "15分钟主导"
    H1_DOMINANT = "1小时主导"
    H4_DOMINANT = "4小时主导"
    D1_DOMINANT = "日线主导"
    BALANCED = "平衡"

@dataclass
class MarketCondition:
    """市场状态"""
    regime: MarketRegime
    volatility_level: float
    trend_strength: float
    momentum_score: float
    support_resistance_strength: float
    volume_profile: float
    market_efficiency: float
    noise_level: float
    confidence: float

    @property
    def market_regime(self):
        """兼容属性：market_regime"""
        return self.regime

    @property
    def recommended_strategy(self):
        """兼容属性：推荐策略"""
        # 根据市场制度返回推荐策略
        strategy_mapping = {
            MarketRegime.STRONG_BULL: TradingStrategy.TREND_FOLLOWING,
            MarketRegime.BULL: TradingStrategy.TREND_FOLLOWING,
            MarketRegime.WEAK_BULL: TradingStrategy.MOMENTUM,
            MarketRegime.SIDEWAYS: TradingStrategy.RANGE_TRADING,
            MarketRegime.SIDEWAYS_HIGH: TradingStrategy.MEAN_REVERSION,
            MarketRegime.SIDEWAYS_LOW: TradingStrategy.CONTRARIAN,
            MarketRegime.WEAK_BEAR: TradingStrategy.CONTRARIAN,
            MarketRegime.BEAR: TradingStrategy.TREND_FOLLOWING,
            MarketRegime.STRONG_BEAR: TradingStrategy.TREND_FOLLOWING,
            MarketRegime.VOLATILE: TradingStrategy.SCALPING,
            MarketRegime.CALM: TradingStrategy.CONSERVATIVE
        }
        return strategy_mapping.get(self.regime, TradingStrategy.RANGE_TRADING)

@dataclass
class AdaptiveParameters:
    """自适应参数"""
    strategy: TradingStrategy
    timeframe_weight: TimeframeWeight
    position_size_multiplier: float
    stop_loss_multiplier: float
    take_profit_multiplier: float
    entry_threshold: float
    exit_threshold: float
    risk_tolerance: float
    signal_sensitivity: float
    holding_period_preference: str

@dataclass
class StrategyPerformance:
    """策略表现"""
    strategy: TradingStrategy
    market_regime: MarketRegime
    win_rate: float
    avg_return: float
    max_drawdown: float
    sharpe_ratio: float
    total_trades: int
    last_updated: datetime

class MarketAdaptiveSystem:
    """市场自适应系统"""

    def __init__(self):
        # 市场状态检测参数
        self.detection_params = {
            'trend_lookback': 20,           # 趋势检测回看期
            'volatility_window': 14,        # 波动率窗口
            'momentum_period': 10,          # 动量周期
            'volume_ma_period': 20,         # 成交量均线周期
            'noise_threshold': 0.3,         # 噪音阈值
            'confidence_threshold': 0.7     # 置信度阈值
        }

        # 策略适应参数
        self.strategy_params = {
            MarketRegime.STRONG_BULL: {
                'preferred_strategy': TradingStrategy.TREND_FOLLOWING,
                'position_multiplier': 1.2,
                'stop_multiplier': 0.8,
                'take_profit_multiplier': 1.5,
                'entry_threshold': 0.6,
                'risk_tolerance': 0.8
            },
            MarketRegime.BULL: {
                'preferred_strategy': TradingStrategy.TREND_FOLLOWING,
                'position_multiplier': 1.0,
                'stop_multiplier': 1.0,
                'take_profit_multiplier': 1.3,
                'entry_threshold': 0.7,
                'risk_tolerance': 0.7
            },
            MarketRegime.WEAK_BULL: {
                'preferred_strategy': TradingStrategy.MOMENTUM,
                'position_multiplier': 0.8,
                'stop_multiplier': 1.1,
                'take_profit_multiplier': 1.2,
                'entry_threshold': 0.75,
                'risk_tolerance': 0.6
            },
            MarketRegime.SIDEWAYS: {
                'preferred_strategy': TradingStrategy.RANGE_TRADING,
                'position_multiplier': 0.7,
                'stop_multiplier': 1.2,
                'take_profit_multiplier': 1.0,
                'entry_threshold': 0.8,
                'risk_tolerance': 0.5
            },
            MarketRegime.SIDEWAYS_HIGH: {
                'preferred_strategy': TradingStrategy.MEAN_REVERSION,
                'position_multiplier': 0.6,
                'stop_multiplier': 1.3,
                'take_profit_multiplier': 0.9,
                'entry_threshold': 0.8,
                'risk_tolerance': 0.4
            },
            MarketRegime.SIDEWAYS_LOW: {
                'preferred_strategy': TradingStrategy.CONTRARIAN,
                'position_multiplier': 0.6,
                'stop_multiplier': 1.3,
                'take_profit_multiplier': 1.1,
                'entry_threshold': 0.8,
                'risk_tolerance': 0.4
            },
            MarketRegime.WEAK_BEAR: {
                'preferred_strategy': TradingStrategy.CONTRARIAN,
                'position_multiplier': 0.8,
                'stop_multiplier': 1.1,
                'take_profit_multiplier': 1.2,
                'entry_threshold': 0.75,
                'risk_tolerance': 0.6
            },
            MarketRegime.BEAR: {
                'preferred_strategy': TradingStrategy.TREND_FOLLOWING,
                'position_multiplier': 1.0,
                'stop_multiplier': 1.0,
                'take_profit_multiplier': 1.3,
                'entry_threshold': 0.7,
                'risk_tolerance': 0.7
            },
            MarketRegime.STRONG_BEAR: {
                'preferred_strategy': TradingStrategy.TREND_FOLLOWING,
                'position_multiplier': 1.2,
                'stop_multiplier': 0.8,
                'take_profit_multiplier': 1.5,
                'entry_threshold': 0.6,
                'risk_tolerance': 0.8
            },
            MarketRegime.VOLATILE: {
                'preferred_strategy': TradingStrategy.SCALPING,
                'position_multiplier': 0.5,
                'stop_multiplier': 1.5,
                'take_profit_multiplier': 0.8,
                'entry_threshold': 0.85,
                'risk_tolerance': 0.3
            },
            MarketRegime.CALM: {
                'preferred_strategy': TradingStrategy.CONSERVATIVE,
                'position_multiplier': 0.4,
                'stop_multiplier': 1.8,
                'take_profit_multiplier': 0.7,
                'entry_threshold': 0.9,
                'risk_tolerance': 0.2
            }
        }

        # 历史数据
        self.market_history = []
        self.strategy_performance = {}
        self.adaptation_history = []

        # 当前状态
        self.current_market_condition = None
        self.current_adaptive_params = None

        # 日志
        self.logger = logging.getLogger(__name__)

    def analyze_market_regime(self, market_data: Dict,
                             historical_data: List[Dict] = None) -> MarketCondition:
        """分析市场制度 - 兼容接口"""
        return self.analyze_market_condition(market_data, historical_data)

    def analyze_market_condition(self, market_data: Dict,
                                historical_data: List[Dict] = None) -> MarketCondition:
        """分析市场状态"""

        try:
            # 1. 趋势强度分析
            trend_strength = self._analyze_trend_strength(market_data, historical_data)

            # 2. 波动率分析
            volatility_level = self._analyze_volatility(market_data, historical_data)

            # 3. 动量分析
            momentum_score = self._analyze_momentum(market_data, historical_data)

            # 4. 支撑阻力强度
            sr_strength = self._analyze_support_resistance(market_data, historical_data)

            # 5. 成交量分析
            volume_profile = self._analyze_volume_profile(market_data, historical_data)

            # 6. 市场效率分析
            market_efficiency = self._analyze_market_efficiency(market_data, historical_data)

            # 7. 噪音水平分析
            noise_level = self._analyze_noise_level(market_data, historical_data)

            # 8. 确定市场制度
            regime = self._determine_market_regime(
                trend_strength, volatility_level, momentum_score,
                sr_strength, volume_profile, market_efficiency
            )

            # 9. 计算置信度
            confidence = self._calculate_confidence(
                trend_strength, volatility_level, momentum_score, noise_level
            )

            market_condition = MarketCondition(
                regime=regime,
                volatility_level=volatility_level,
                trend_strength=trend_strength,
                momentum_score=momentum_score,
                support_resistance_strength=sr_strength,
                volume_profile=volume_profile,
                market_efficiency=market_efficiency,
                noise_level=noise_level,
                confidence=confidence
            )

            # 记录市场状态历史
            self._record_market_condition(market_condition)

            return market_condition

        except Exception as e:
            self.logger.error(f"市场状态分析失败: {e}")
            return self._create_default_market_condition()

    def adapt_strategy(self, market_condition: MarketCondition,
                      current_performance: Dict = None) -> AdaptiveParameters:
        """根据市场状态自适应策略"""

        try:
            # 1. 获取基础策略参数
            base_params = self.strategy_params.get(market_condition.regime)

            if not base_params:
                base_params = self.strategy_params[MarketRegime.SIDEWAYS]

            # 2. 根据市场状态微调参数
            adjusted_params = self._adjust_parameters_by_market(base_params, market_condition)

            # 3. 根据历史表现优化参数
            if current_performance:
                adjusted_params = self._optimize_by_performance(adjusted_params, current_performance)

            # 4. 确定时间框架权重
            timeframe_weight = self._determine_timeframe_weight(market_condition)

            # 5. 计算信号敏感度
            signal_sensitivity = self._calculate_signal_sensitivity(market_condition)

            # 6. 确定持仓偏好
            holding_preference = self._determine_holding_preference(market_condition)

            adaptive_params = AdaptiveParameters(
                strategy=adjusted_params['preferred_strategy'],
                timeframe_weight=timeframe_weight,
                position_size_multiplier=adjusted_params['position_multiplier'],
                stop_loss_multiplier=adjusted_params['stop_multiplier'],
                take_profit_multiplier=adjusted_params['take_profit_multiplier'],
                entry_threshold=adjusted_params['entry_threshold'],
                exit_threshold=adjusted_params.get('exit_threshold', 0.3),
                risk_tolerance=adjusted_params['risk_tolerance'],
                signal_sensitivity=signal_sensitivity,
                holding_period_preference=holding_preference
            )

            # 记录自适应历史
            self._record_adaptation(market_condition, adaptive_params)

            # 更新当前状态
            self.current_market_condition = market_condition
            self.current_adaptive_params = adaptive_params

            return adaptive_params

        except Exception as e:
            self.logger.error(f"策略自适应失败: {e}")
            return self._create_default_adaptive_params()

    def _analyze_trend_strength(self, market_data: Dict, historical_data: List[Dict] = None) -> float:
        """分析趋势强度"""

        current_price = market_data.get('current_price', 0)
        ma_20 = market_data.get('ma_20', current_price)
        ma_50 = market_data.get('ma_50', current_price)
        ma_200 = market_data.get('ma_200', current_price)

        # 计算均线排列得分
        ma_alignment_score = 0.0

        # 上升趋势排列
        if current_price > ma_20 > ma_50 > ma_200:
            ma_alignment_score = 1.0
        elif current_price < ma_20 < ma_50 < ma_200:
            ma_alignment_score = 1.0  # 下降趋势也是强趋势
        elif current_price > ma_20 > ma_50:
            ma_alignment_score = 0.7
        elif current_price < ma_20 < ma_50:
            ma_alignment_score = 0.7
        elif current_price > ma_20:
            ma_alignment_score = 0.4
        elif current_price < ma_20:
            ma_alignment_score = 0.4
        else:
            ma_alignment_score = 0.1

        # 计算价格与均线的距离
        if current_price > 0:
            ma_distance = abs(current_price - ma_20) / current_price
            distance_score = min(ma_distance * 10, 1.0)  # 距离越大，趋势越强
        else:
            distance_score = 0.0

        # 综合趋势强度
        trend_strength = (ma_alignment_score * 0.7 + distance_score * 0.3)

        return min(trend_strength, 1.0)

    def _analyze_volatility(self, market_data: Dict, historical_data: List[Dict] = None) -> float:
        """分析波动率水平"""

        atr = market_data.get('atr', 0.001)
        current_price = market_data.get('current_price', 1.0)

        # 计算相对波动率
        if current_price > 0:
            relative_volatility = atr / current_price
        else:
            relative_volatility = 0.001

        # 标准化波动率（基于EURUSD的典型波动率）
        normal_volatility = 0.0015
        volatility_ratio = relative_volatility / normal_volatility

        # 将波动率映射到0-1范围
        volatility_level = min(volatility_ratio / 3.0, 1.0)  # 3倍正常波动率为最高

        return volatility_level

    def _analyze_momentum(self, market_data: Dict, historical_data: List[Dict] = None) -> float:
        """分析动量强度"""

        rsi = market_data.get('rsi', 50)
        macd = market_data.get('macd', 0)
        macd_signal = market_data.get('macd_signal', 0)

        # RSI动量评分
        if rsi > 70:
            rsi_score = (rsi - 70) / 30  # 超买区域
        elif rsi < 30:
            rsi_score = (30 - rsi) / 30  # 超卖区域
        else:
            rsi_score = abs(rsi - 50) / 20  # 中性区域

        # MACD动量评分
        macd_diff = macd - macd_signal
        macd_score = min(abs(macd_diff) * 10000, 1.0)  # 标准化MACD差值

        # 综合动量评分
        momentum_score = (rsi_score * 0.6 + macd_score * 0.4)

        return min(momentum_score, 1.0)

    def _analyze_support_resistance(self, market_data: Dict, historical_data: List[Dict] = None) -> float:
        """分析支撑阻力强度"""

        current_price = market_data.get('current_price', 0)
        bb_upper = market_data.get('bb_upper', current_price * 1.02)
        bb_lower = market_data.get('bb_lower', current_price * 0.98)
        ma_20 = market_data.get('ma_20', current_price)
        ma_50 = market_data.get('ma_50', current_price)

        # 布林带支撑阻力强度
        if bb_upper > bb_lower:
            bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
            if bb_position < 0.1 or bb_position > 0.9:
                bb_strength = 0.8  # 接近边界，支撑阻力强
            elif bb_position < 0.2 or bb_position > 0.8:
                bb_strength = 0.6
            else:
                bb_strength = 0.3  # 中间区域，支撑阻力弱
        else:
            bb_strength = 0.3

        # 均线支撑阻力强度
        ma_distances = [
            abs(current_price - ma_20) / current_price if current_price > 0 else 0,
            abs(current_price - ma_50) / current_price if current_price > 0 else 0
        ]

        # 距离均线越近，支撑阻力越强
        min_distance = min(ma_distances)
        if min_distance < 0.001:
            ma_strength = 0.9
        elif min_distance < 0.002:
            ma_strength = 0.7
        elif min_distance < 0.005:
            ma_strength = 0.5
        else:
            ma_strength = 0.2

        # 综合支撑阻力强度
        sr_strength = (bb_strength * 0.6 + ma_strength * 0.4)

        return sr_strength

    def _analyze_volume_profile(self, market_data: Dict, historical_data: List[Dict] = None) -> float:
        """分析成交量特征"""

        volume = market_data.get('volume', 1000)
        avg_volume = market_data.get('avg_volume', volume)

        # 计算成交量比率
        if avg_volume > 0:
            volume_ratio = volume / avg_volume
        else:
            volume_ratio = 1.0

        # 标准化成交量特征
        if volume_ratio > 2.0:
            volume_profile = 1.0  # 成交量爆发
        elif volume_ratio > 1.5:
            volume_profile = 0.8  # 成交量放大
        elif volume_ratio > 1.2:
            volume_profile = 0.6  # 成交量正常偏高
        elif volume_ratio > 0.8:
            volume_profile = 0.4  # 成交量正常
        else:
            volume_profile = 0.2  # 成交量萎缩

        return volume_profile

    def _analyze_market_efficiency(self, market_data: Dict, historical_data: List[Dict] = None) -> float:
        """分析市场效率"""

        # 简化的市场效率分析
        # 基于价格与技术指标的一致性

        current_price = market_data.get('current_price', 0)
        ma_20 = market_data.get('ma_20', current_price)
        rsi = market_data.get('rsi', 50)
        macd = market_data.get('macd', 0)

        efficiency_score = 0.5  # 基础效率

        # 价格与均线一致性
        if current_price > ma_20 and rsi > 50:
            efficiency_score += 0.2
        elif current_price < ma_20 and rsi < 50:
            efficiency_score += 0.2

        # MACD与价格趋势一致性
        if current_price > ma_20 and macd > 0:
            efficiency_score += 0.2
        elif current_price < ma_20 and macd < 0:
            efficiency_score += 0.2

        # RSI极值检查
        if 30 < rsi < 70:
            efficiency_score += 0.1

        return min(efficiency_score, 1.0)

    def _analyze_noise_level(self, market_data: Dict, historical_data: List[Dict] = None) -> float:
        """分析市场噪音水平"""

        # 基于波动率和趋势一致性计算噪音
        volatility = self._analyze_volatility(market_data, historical_data)
        trend_strength = self._analyze_trend_strength(market_data, historical_data)

        # 高波动率 + 低趋势强度 = 高噪音
        noise_level = volatility * (1 - trend_strength)

        return min(noise_level, 1.0)

    def _determine_market_regime(self, trend_strength: float, volatility_level: float,
                               momentum_score: float, sr_strength: float,
                               volume_profile: float, market_efficiency: float) -> MarketRegime:
        """确定市场制度"""

        # 高波动率市场
        if volatility_level > 0.8:
            return MarketRegime.VOLATILE

        # 低波动率市场
        if volatility_level < 0.2:
            return MarketRegime.CALM

        # 基于趋势强度和动量确定市场制度
        if trend_strength > 0.8:
            if momentum_score > 0.8:
                return MarketRegime.STRONG_BULL if volume_profile > 0.6 else MarketRegime.STRONG_BEAR
            elif momentum_score > 0.6:
                return MarketRegime.BULL if volume_profile > 0.5 else MarketRegime.BEAR
            else:
                return MarketRegime.WEAK_BULL if volume_profile > 0.4 else MarketRegime.WEAK_BEAR

        elif trend_strength > 0.5:
            if momentum_score > 0.6:
                return MarketRegime.BULL if volume_profile > 0.5 else MarketRegime.BEAR
            else:
                return MarketRegime.WEAK_BULL if volume_profile > 0.4 else MarketRegime.WEAK_BEAR

        else:
            # 震荡市场
            if sr_strength > 0.7:
                if volume_profile > 0.6:
                    return MarketRegime.SIDEWAYS_HIGH
                elif volume_profile < 0.3:
                    return MarketRegime.SIDEWAYS_LOW
                else:
                    return MarketRegime.SIDEWAYS
            else:
                return MarketRegime.SIDEWAYS

    def _calculate_confidence(self, trend_strength: float, volatility_level: float,
                            momentum_score: float, noise_level: float) -> float:
        """计算市场状态识别置信度"""

        # 基础置信度
        base_confidence = 0.5

        # 趋势强度贡献
        if trend_strength > 0.8:
            base_confidence += 0.3
        elif trend_strength > 0.6:
            base_confidence += 0.2
        elif trend_strength > 0.4:
            base_confidence += 0.1

        # 波动率稳定性贡献
        if 0.3 < volatility_level < 0.7:  # 适中波动率
            base_confidence += 0.1
        elif volatility_level > 0.9 or volatility_level < 0.1:  # 极端波动率
            base_confidence -= 0.1

        # 动量一致性贡献
        if momentum_score > 0.7:
            base_confidence += 0.1

        # 噪音水平影响
        base_confidence -= noise_level * 0.2

        return max(min(base_confidence, 1.0), 0.1)

    def _adjust_parameters_by_market(self, base_params: Dict, market_condition: MarketCondition) -> Dict:
        """根据市场状态微调参数"""

        adjusted = base_params.copy()

        # 根据置信度调整
        confidence_factor = market_condition.confidence
        adjusted['position_multiplier'] *= confidence_factor
        adjusted['entry_threshold'] = adjusted['entry_threshold'] + (1 - confidence_factor) * 0.1

        # 根据波动率调整
        volatility_factor = 1.0 - market_condition.volatility_level * 0.3
        adjusted['position_multiplier'] *= volatility_factor
        adjusted['stop_multiplier'] *= (1.0 + market_condition.volatility_level * 0.5)

        # 根据噪音水平调整
        noise_factor = 1.0 - market_condition.noise_level * 0.2
        adjusted['position_multiplier'] *= noise_factor
        adjusted['entry_threshold'] += market_condition.noise_level * 0.1

        # 根据市场效率调整
        efficiency_factor = market_condition.market_efficiency
        adjusted['take_profit_multiplier'] *= efficiency_factor

        return adjusted

    def _optimize_by_performance(self, params: Dict, performance: Dict) -> Dict:
        """根据历史表现优化参数"""

        optimized = params.copy()

        # 获取表现指标
        win_rate = performance.get('win_rate', 0.5)
        avg_return = performance.get('avg_return', 0.0)
        max_drawdown = performance.get('max_drawdown', 0.1)

        # 根据胜率调整
        if win_rate > 0.7:
            optimized['position_multiplier'] *= 1.1  # 提高仓位
            optimized['entry_threshold'] *= 0.95     # 降低入场门槛
        elif win_rate < 0.4:
            optimized['position_multiplier'] *= 0.8  # 降低仓位
            optimized['entry_threshold'] *= 1.1      # 提高入场门槛

        # 根据平均收益调整
        if avg_return > 0.02:
            optimized['take_profit_multiplier'] *= 1.1
        elif avg_return < -0.01:
            optimized['stop_multiplier'] *= 0.9

        # 根据最大回撤调整
        if max_drawdown > 0.15:
            optimized['position_multiplier'] *= 0.7
            optimized['stop_multiplier'] *= 0.8

        return optimized

    def _determine_timeframe_weight(self, market_condition: MarketCondition) -> TimeframeWeight:
        """确定时间框架权重"""

        if market_condition.regime in [MarketRegime.STRONG_BULL, MarketRegime.STRONG_BEAR]:
            return TimeframeWeight.H4_DOMINANT  # 强趋势用长时间框架
        elif market_condition.regime in [MarketRegime.BULL, MarketRegime.BEAR]:
            return TimeframeWeight.H1_DOMINANT  # 中等趋势用中时间框架
        elif market_condition.regime == MarketRegime.VOLATILE:
            return TimeframeWeight.M15_DOMINANT  # 高波动用短时间框架
        elif market_condition.regime == MarketRegime.CALM:
            return TimeframeWeight.D1_DOMINANT   # 平静市场用长时间框架
        else:
            return TimeframeWeight.BALANCED      # 震荡市场平衡使用

    def _calculate_signal_sensitivity(self, market_condition: MarketCondition) -> float:
        """计算信号敏感度"""

        base_sensitivity = 0.5

        # 根据市场制度调整
        regime_adjustments = {
            MarketRegime.STRONG_BULL: 0.3,
            MarketRegime.STRONG_BEAR: 0.3,
            MarketRegime.BULL: 0.2,
            MarketRegime.BEAR: 0.2,
            MarketRegime.VOLATILE: -0.3,
            MarketRegime.CALM: -0.2,
            MarketRegime.SIDEWAYS: -0.1
        }

        adjustment = regime_adjustments.get(market_condition.regime, 0.0)
        sensitivity = base_sensitivity + adjustment

        # 根据噪音水平调整
        sensitivity -= market_condition.noise_level * 0.2

        # 根据置信度调整
        sensitivity += (market_condition.confidence - 0.5) * 0.2

        return max(min(sensitivity, 1.0), 0.1)

    def _determine_holding_preference(self, market_condition: MarketCondition) -> str:
        """确定持仓偏好"""

        if market_condition.regime in [MarketRegime.STRONG_BULL, MarketRegime.STRONG_BEAR]:
            return "长期持有"
        elif market_condition.regime in [MarketRegime.BULL, MarketRegime.BEAR]:
            return "中期持有"
        elif market_condition.regime == MarketRegime.VOLATILE:
            return "快进快出"
        elif market_condition.regime == MarketRegime.CALM:
            return "耐心等待"
        else:
            return "短期交易"

    def _record_market_condition(self, market_condition: MarketCondition):
        """记录市场状态历史"""

        record = {
            'timestamp': datetime.now().isoformat(),
            'regime': market_condition.regime.value,
            'volatility_level': market_condition.volatility_level,
            'trend_strength': market_condition.trend_strength,
            'momentum_score': market_condition.momentum_score,
            'confidence': market_condition.confidence
        }

        self.market_history.append(record)

        # 保持最近200条记录
        if len(self.market_history) > 200:
            self.market_history = self.market_history[-200:]

    def _record_adaptation(self, market_condition: MarketCondition, adaptive_params: AdaptiveParameters):
        """记录自适应历史"""

        record = {
            'timestamp': datetime.now().isoformat(),
            'market_regime': market_condition.regime.value,
            'strategy': adaptive_params.strategy.value,
            'position_multiplier': adaptive_params.position_size_multiplier,
            'entry_threshold': adaptive_params.entry_threshold,
            'risk_tolerance': adaptive_params.risk_tolerance,
            'timeframe_weight': adaptive_params.timeframe_weight.value
        }

        self.adaptation_history.append(record)

        # 保持最近100条记录
        if len(self.adaptation_history) > 100:
            self.adaptation_history = self.adaptation_history[-100:]

    def _create_default_market_condition(self) -> MarketCondition:
        """创建默认市场状态"""

        return MarketCondition(
            regime=MarketRegime.SIDEWAYS,
            volatility_level=0.5,
            trend_strength=0.3,
            momentum_score=0.5,
            support_resistance_strength=0.5,
            volume_profile=0.5,
            market_efficiency=0.5,
            noise_level=0.5,
            confidence=0.3
        )

    def _create_default_adaptive_params(self) -> AdaptiveParameters:
        """创建默认自适应参数"""

        return AdaptiveParameters(
            strategy=TradingStrategy.CONSERVATIVE,
            timeframe_weight=TimeframeWeight.BALANCED,
            position_size_multiplier=0.5,
            stop_loss_multiplier=1.5,
            take_profit_multiplier=0.8,
            entry_threshold=0.8,
            exit_threshold=0.3,
            risk_tolerance=0.3,
            signal_sensitivity=0.3,
            holding_period_preference="保守交易"
        )

    def update_strategy_performance(self, strategy: TradingStrategy, market_regime: MarketRegime,
                                  trade_result: Dict):
        """更新策略表现"""

        key = f"{strategy.value}_{market_regime.value}"

        if key not in self.strategy_performance:
            self.strategy_performance[key] = StrategyPerformance(
                strategy=strategy,
                market_regime=market_regime,
                win_rate=0.0,
                avg_return=0.0,
                max_drawdown=0.0,
                sharpe_ratio=0.0,
                total_trades=0,
                last_updated=datetime.now()
            )

        perf = self.strategy_performance[key]

        # 更新统计
        profit_loss = trade_result.get('profit_loss', 0.0)
        is_win = profit_loss > 0

        # 更新胜率
        total_trades = perf.total_trades
        current_wins = perf.win_rate * total_trades
        new_wins = current_wins + (1 if is_win else 0)
        perf.total_trades += 1
        perf.win_rate = new_wins / perf.total_trades

        # 更新平均收益
        current_total_return = perf.avg_return * total_trades
        new_total_return = current_total_return + profit_loss
        perf.avg_return = new_total_return / perf.total_trades

        # 更新最大回撤（简化）
        if profit_loss < 0:
            perf.max_drawdown = max(perf.max_drawdown, abs(profit_loss))

        perf.last_updated = datetime.now()

    def get_best_strategy_for_regime(self, market_regime: MarketRegime) -> TradingStrategy:
        """获取特定市场制度下的最佳策略"""

        regime_performances = []

        for key, perf in self.strategy_performance.items():
            if perf.market_regime == market_regime and perf.total_trades >= 5:
                # 计算综合评分
                score = (perf.win_rate * 0.4 +
                        max(perf.avg_return, 0) * 0.3 +
                        (1 - perf.max_drawdown) * 0.3)
                regime_performances.append((perf.strategy, score))

        if regime_performances:
            # 返回评分最高的策略
            best_strategy = max(regime_performances, key=lambda x: x[1])[0]
            return best_strategy
        else:
            # 返回默认策略
            return self.strategy_params.get(market_regime, {}).get('preferred_strategy', TradingStrategy.CONSERVATIVE)

    def get_market_statistics(self) -> Dict:
        """获取市场统计信息"""

        if not self.market_history:
            return {
                'total_observations': 0,
                'regime_distribution': {},
                'avg_volatility': 0.0,
                'avg_trend_strength': 0.0,
                'avg_confidence': 0.0
            }

        # 统计市场制度分布
        regime_counts = {}
        total_volatility = 0.0
        total_trend_strength = 0.0
        total_confidence = 0.0

        for record in self.market_history:
            regime = record['regime']
            regime_counts[regime] = regime_counts.get(regime, 0) + 1
            total_volatility += record['volatility_level']
            total_trend_strength += record['trend_strength']
            total_confidence += record['confidence']

        total_records = len(self.market_history)

        return {
            'total_observations': total_records,
            'regime_distribution': regime_counts,
            'avg_volatility': total_volatility / total_records,
            'avg_trend_strength': total_trend_strength / total_records,
            'avg_confidence': total_confidence / total_records,
            'recent_observations': self.market_history[-10:] if self.market_history else []
        }

    def get_adaptation_statistics(self) -> Dict:
        """获取自适应统计信息"""

        if not self.adaptation_history:
            return {
                'total_adaptations': 0,
                'strategy_distribution': {},
                'avg_position_multiplier': 0.0,
                'avg_risk_tolerance': 0.0
            }

        # 统计策略分布
        strategy_counts = {}
        total_position_multiplier = 0.0
        total_risk_tolerance = 0.0

        for record in self.adaptation_history:
            strategy = record['strategy']
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
            total_position_multiplier += record['position_multiplier']
            total_risk_tolerance += record['risk_tolerance']

        total_records = len(self.adaptation_history)

        return {
            'total_adaptations': total_records,
            'strategy_distribution': strategy_counts,
            'avg_position_multiplier': total_position_multiplier / total_records,
            'avg_risk_tolerance': total_risk_tolerance / total_records,
            'recent_adaptations': self.adaptation_history[-10:] if self.adaptation_history else []
        }

    def get_strategy_performance_summary(self) -> Dict:
        """获取策略表现总结"""

        summary = {}

        for key, perf in self.strategy_performance.items():
            if perf.total_trades >= 3:  # 至少3笔交易才有统计意义
                summary[key] = {
                    'strategy': perf.strategy.value,
                    'market_regime': perf.market_regime.value,
                    'win_rate': perf.win_rate,
                    'avg_return': perf.avg_return,
                    'max_drawdown': perf.max_drawdown,
                    'total_trades': perf.total_trades,
                    'last_updated': perf.last_updated.isoformat()
                }

        return summary

    def should_adapt_strategy(self, current_performance: Dict = None) -> Tuple[bool, str]:
        """判断是否应该调整策略"""

        if not self.current_market_condition or not self.current_adaptive_params:
            return True, "初始化策略适应"

        # 检查市场状态变化
        if self.current_market_condition.confidence < 0.5:
            return True, "市场状态识别置信度过低"

        # 检查表现
        if current_performance:
            win_rate = current_performance.get('win_rate', 0.5)
            max_drawdown = current_performance.get('max_drawdown', 0.0)

            if win_rate < 0.3:
                return True, f"胜率过低({win_rate:.1%})，需要调整策略"

            if max_drawdown > 0.2:
                return True, f"回撤过大({max_drawdown:.1%})，需要降低风险"

        # 检查策略适用性
        current_regime = self.current_market_condition.regime
        current_strategy = self.current_adaptive_params.strategy

        # 获取当前市场制度的推荐策略
        recommended_strategy = self.strategy_params.get(current_regime, {}).get('preferred_strategy')

        if recommended_strategy and recommended_strategy != current_strategy:
            return True, f"市场制度变化，建议从{current_strategy.value}切换到{recommended_strategy.value}"

        return False, "当前策略适应良好"

    def get_current_status(self) -> Dict:
        """获取当前状态"""

        status = {
            'market_condition': None,
            'adaptive_params': None,
            'last_analysis_time': None,
            'system_ready': False
        }

        if self.current_market_condition:
            status['market_condition'] = {
                'regime': self.current_market_condition.regime.value,
                'volatility_level': self.current_market_condition.volatility_level,
                'trend_strength': self.current_market_condition.trend_strength,
                'confidence': self.current_market_condition.confidence
            }

        if self.current_adaptive_params:
            status['adaptive_params'] = {
                'strategy': self.current_adaptive_params.strategy.value,
                'position_multiplier': self.current_adaptive_params.position_size_multiplier,
                'entry_threshold': self.current_adaptive_params.entry_threshold,
                'risk_tolerance': self.current_adaptive_params.risk_tolerance,
                'holding_preference': self.current_adaptive_params.holding_period_preference
            }

        if self.market_history:
            status['last_analysis_time'] = self.market_history[-1]['timestamp']

        status['system_ready'] = bool(self.current_market_condition and self.current_adaptive_params)

        return status
