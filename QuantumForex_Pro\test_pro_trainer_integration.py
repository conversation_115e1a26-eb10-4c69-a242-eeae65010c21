#!/usr/bin/env python3
"""
QuantumForex Pro - Pro与Trainer集成状态检查
检查Pro系统是否真正使用了Trainer训练的模型
"""

import sys
import os
from datetime import datetime
from pathlib import Path
import joblib

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_ml_engine_status():
    """检查ML引擎状态"""
    print("🔍 检查ML引擎状态...")
    print("=" * 60)

    try:
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine, ModelType

        # 创建ML引擎实例
        ml_engine = LightweightMLEngine()

        print("📊 ML引擎基本信息:")
        print(f"   模型目录: {ml_engine.model_dir}")
        print(f"   支持的模型类型: {[mt.value for mt in ModelType]}")

        # 检查模型文件存在性
        print("\n📁 模型文件检查:")
        model_files_found = 0
        for model_type in ModelType:
            model_path = os.path.join(ml_engine.model_dir, f"{model_type.value}_model.pkl")
            scaler_path = os.path.join(ml_engine.model_dir, f"{model_type.value}_scaler.pkl")

            model_exists = os.path.exists(model_path)
            scaler_exists = os.path.exists(scaler_path)

            status = "✅" if (model_exists and scaler_exists) else "❌"
            print(f"   {status} {model_type.value}: 模型={model_exists}, 缩放器={scaler_exists}")

            if model_exists and scaler_exists:
                model_files_found += 1

                # 检查文件大小和修改时间
                model_size = os.path.getsize(model_path)
                model_mtime = datetime.fromtimestamp(os.path.getmtime(model_path))
                print(f"      模型大小: {model_size:,} bytes")
                print(f"      修改时间: {model_mtime.strftime('%Y-%m-%d %H:%M:%S')}")

        print(f"\n📈 模型文件统计: {model_files_found}/{len(ModelType)}个模型可用")

        # 检查模型是否实际加载
        print("\n🔄 模型加载测试:")
        loaded_models = 0
        for model_type in ModelType:
            if model_type in ml_engine.models and ml_engine.models[model_type] is not None:
                loaded_models += 1
                model = ml_engine.models[model_type]
                print(f"   ✅ {model_type.value}: {type(model).__name__}")
            else:
                print(f"   ❌ {model_type.value}: 未加载")

        print(f"\n📊 模型加载统计: {loaded_models}/{len(ModelType)}个模型已加载")

        return model_files_found > 0, loaded_models > 0

    except Exception as e:
        print(f"❌ ML引擎检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False, False

def check_trainer_models():
    """检查Trainer训练的模型"""
    print("\n🔍 检查Trainer训练的模型...")
    print("=" * 60)

    try:
        # 检查Pro的模型目录
        models_dir = Path("data/models")

        if not models_dir.exists():
            print("❌ 模型目录不存在")
            return False

        # 获取所有模型文件
        all_model_files = list(models_dir.glob("*.pkl"))

        print(f"📁 模型目录: {models_dir.absolute()}")
        print(f"📊 总模型文件数: {len(all_model_files)}")

        # 分类模型文件
        trainer_models = []
        standard_models = []

        for model_file in all_model_files:
            if any(keyword in model_file.name for keyword in ['lightgbm', 'xgboost', 'random_forest', 'direction']):
                trainer_models.append(model_file)
            elif model_file.name.endswith('_model.pkl') or model_file.name.endswith('_scaler.pkl'):
                standard_models.append(model_file)

        print(f"\n🤖 Trainer训练的模型 ({len(trainer_models)}个):")
        for model in sorted(trainer_models):
            size = model.stat().st_size
            mtime = datetime.fromtimestamp(model.stat().st_mtime)
            print(f"   📄 {model.name}")
            print(f"      大小: {size:,} bytes, 时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")

        print(f"\n📊 标准模型文件 ({len(standard_models)}个):")
        for model in sorted(standard_models):
            size = model.stat().st_size
            mtime = datetime.fromtimestamp(model.stat().st_mtime)
            print(f"   📄 {model.name}")
            print(f"      大小: {size:,} bytes, 时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")

        return len(trainer_models) > 0

    except Exception as e:
        print(f"❌ Trainer模型检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_model_sync_system():
    """检查模型同步系统"""
    print("\n🔍 检查模型同步系统...")
    print("=" * 60)

    try:
        from utils.ml_model_sync import MLModelSyncManager
        from utils.cloud_model_receiver import CloudModelReceiver

        # 检查ML模型同步器
        print("📡 ML模型同步器:")
        try:
            ml_sync = MLModelSyncManager()
            sync_status = ml_sync.get_sync_status()

            print(f"   训练端连接: {'✅' if sync_status['trainer_connection'] else '❌'}")
            print(f"   本地模型数量: {sync_status['local_models_count']}")
            print(f"   同步启用: {'✅' if sync_status['sync_enabled'] else '❌'}")
            print(f"   自动更新: {'✅' if sync_status['auto_update_enabled'] else '❌'}")

        except Exception as e:
            print(f"   ❌ ML模型同步器初始化失败: {e}")

        # 检查云模型接收器
        print("\n☁️ 云模型接收器:")
        try:
            cloud_receiver = CloudModelReceiver()
            print(f"   模型文件夹: {cloud_receiver.models_folder}")
            print(f"   支持的模型: {cloud_receiver.supported_models}")

        except Exception as e:
            print(f"   ❌ 云模型接收器初始化失败: {e}")

        return True

    except Exception as e:
        print(f"❌ 模型同步系统检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_actual_model_usage():
    """检查实际模型使用情况"""
    print("\n🔍 检查实际模型使用情况...")
    print("=" * 60)

    try:
        from main import QuantumForexPro
        import pandas as pd

        # 创建系统实例
        system = QuantumForexPro()

        # 检查ML引擎是否已初始化
        if hasattr(system, 'ml_engine') and system.ml_engine:
            print("✅ ML引擎已初始化")

            # 创建测试数据
            test_data = pd.DataFrame({
                'close': [1.1000, 1.1010, 1.1020, 1.1015, 1.1025],
                'high': [1.1005, 1.1015, 1.1025, 1.1020, 1.1030],
                'low': [0.9995, 1.1005, 1.1015, 1.1010, 1.1020],
                'volume': [1000, 1100, 1200, 1050, 1150]
            })

            test_indicators = {
                'rsi': 55.0,
                'macd': 0.001,
                'bb_upper': 1.1030,
                'bb_lower': 1.0970
            }

            print("\n🧪 测试ML预测生成:")
            predictions = system.ml_engine.generate_predictions(test_data, test_indicators)

            if predictions:
                print(f"   ✅ 成功生成{len(predictions)}个预测")
                for model_type, prediction in predictions.items():
                    print(f"   📊 {model_type.value}:")
                    print(f"      预测值: {prediction.prediction:.4f}")
                    print(f"      置信度: {prediction.confidence:.3f}")
                    print(f"      模型准确率: {prediction.model_accuracy:.3f}")
            else:
                print("   ❌ 未能生成预测")
                return False

            # 检查模型是否是从文件加载的
            print("\n🔍 检查模型来源:")
            for model_type in system.ml_engine.models:
                model = system.ml_engine.models[model_type]
                if model is not None:
                    # 检查模型是否有训练历史
                    if hasattr(model, 'n_estimators'):
                        print(f"   📊 {model_type.value}: {type(model).__name__} (n_estimators={model.n_estimators})")
                    else:
                        print(f"   📊 {model_type.value}: {type(model).__name__}")
                else:
                    print(f"   ❌ {model_type.value}: 模型为空")

            return True
        else:
            print("❌ ML引擎未初始化")
            return False

    except Exception as e:
        print(f"❌ 实际模型使用检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_integration_config():
    """检查集成配置"""
    print("\n🔍 检查集成配置...")
    print("=" * 60)

    try:
        from config.config import config

        # 检查ML训练端配置
        if hasattr(config, 'ML_TRAINER_CONFIG'):
            ml_config = config.ML_TRAINER_CONFIG

            print("📋 ML训练端集成配置:")
            print(f"   训练端IP: {ml_config.get('trainer_ip', 'N/A')}")
            print(f"   交易端IP: {ml_config.get('trading_ip', 'N/A')}")
            print(f"   模型同步: {'✅' if ml_config.get('model_sync_enabled', False) else '❌'}")
            print(f"   自动更新: {'✅' if ml_config.get('auto_update_models', False) else '❌'}")
            print(f"   连接类型: {ml_config.get('connection_type', 'N/A')}")

            # 检查API配置
            api_config = ml_config.get('api_config', {})
            print(f"\n🌐 API配置:")
            print(f"   基础URL: {api_config.get('base_url', 'N/A')}")
            print(f"   上传端点: {api_config.get('upload_endpoint', 'N/A')}")
            print(f"   超时设置: {api_config.get('timeout', 'N/A')}秒")

            # 检查本地存储配置
            storage_config = ml_config.get('local_storage', {})
            print(f"\n💾 本地存储配置:")
            print(f"   模型文件夹: {storage_config.get('models_folder', 'N/A')}")
            print(f"   备份文件夹: {storage_config.get('backup_folder', 'N/A')}")
            print(f"   下载文件夹: {storage_config.get('download_folder', 'N/A')}")

            return True
        else:
            print("❌ 未找到ML训练端配置")
            return False

    except Exception as e:
        print(f"❌ 集成配置检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_integration_status():
    """分析集成状态"""
    print("\n🔍 分析Pro-Trainer集成状态...")
    print("=" * 60)

    # 检查是否使用Trainer模型的证据
    evidence = {
        'trainer_models_exist': False,
        'standard_models_exist': False,
        'ml_engine_working': False,
        'sync_system_available': False,
        'config_complete': False
    }

    # 检查Trainer模型文件
    models_dir = Path("data/models")
    if models_dir.exists():
        trainer_files = list(models_dir.glob("*lightgbm*.pkl")) + \
                       list(models_dir.glob("*xgboost*.pkl")) + \
                       list(models_dir.glob("*random_forest*.pkl"))
        evidence['trainer_models_exist'] = len(trainer_files) > 0

        standard_files = list(models_dir.glob("*_model.pkl"))
        evidence['standard_models_exist'] = len(standard_files) > 0

    # 检查ML引擎
    try:
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine
        ml_engine = LightweightMLEngine()
        evidence['ml_engine_working'] = len(ml_engine.models) > 0
    except:
        pass

    # 检查同步系统
    try:
        from utils.ml_model_sync import MLModelSyncManager
        evidence['sync_system_available'] = True
    except:
        pass

    # 检查配置
    try:
        from config.config import config
        evidence['config_complete'] = hasattr(config, 'ML_TRAINER_CONFIG')
    except:
        pass

    print("📊 集成状态分析:")
    print(f"   Trainer模型存在: {'✅' if evidence['trainer_models_exist'] else '❌'}")
    print(f"   标准模型存在: {'✅' if evidence['standard_models_exist'] else '❌'}")
    print(f"   ML引擎工作: {'✅' if evidence['ml_engine_working'] else '❌'}")
    print(f"   同步系统可用: {'✅' if evidence['sync_system_available'] else '❌'}")
    print(f"   配置完整: {'✅' if evidence['config_complete'] else '❌'}")

    # 判断集成状态
    if evidence['trainer_models_exist'] and evidence['ml_engine_working']:
        status = "🟢 部分集成"
        explanation = "Pro系统有Trainer训练的模型文件，但可能未实际使用"
    elif evidence['standard_models_exist'] and evidence['ml_engine_working']:
        status = "🟡 独立运行"
        explanation = "Pro系统使用自己的轻量级模型，未使用Trainer模型"
    elif evidence['sync_system_available'] and evidence['config_complete']:
        status = "🔵 集成就绪"
        explanation = "集成系统已配置，但模型同步可能未激活"
    else:
        status = "🔴 未集成"
        explanation = "Pro和Trainer系统基本独立运行"

    print(f"\n🎯 集成状态: {status}")
    print(f"📝 说明: {explanation}")

    return evidence

if __name__ == "__main__":
    print("🚀 QuantumForex Pro - Pro与Trainer集成状态检查")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 执行所有检查
    tests = [
        ("ML引擎状态", check_ml_engine_status),
        ("Trainer模型", check_trainer_models),
        ("模型同步系统", check_model_sync_system),
        ("实际模型使用", check_actual_model_usage),
        ("集成配置", check_integration_config)
    ]

    results = {}
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"🔄 开始检查: {test_name}")
            print(f"{'='*60}")

            if test_name == "ML引擎状态":
                file_result, load_result = test_func()
                results[test_name] = file_result or load_result
            else:
                results[test_name] = test_func()

            if results[test_name]:
                print(f"✅ {test_name} - 检查通过")
            else:
                print(f"❌ {test_name} - 检查失败")

        except Exception as e:
            print(f"❌ {test_name}检查异常: {e}")
            results[test_name] = False

    # 最终分析
    print(f"\n{'='*60}")
    print("📊 最终集成状态分析")
    print(f"{'='*60}")

    evidence = analyze_integration_status()

    print(f"\n{'='*60}")
    print("📋 检查结果汇总:")
    print(f"{'='*60}")

    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")

    # 给出建议
    print(f"\n💡 建议:")
    if evidence['trainer_models_exist'] and not evidence['ml_engine_working']:
        print("   🔧 Trainer模型存在但未被使用，需要修改ML引擎加载逻辑")
    elif not evidence['trainer_models_exist']:
        print("   📤 需要从Trainer端同步最新训练的模型")
    elif evidence['ml_engine_working'] and evidence['standard_models_exist']:
        print("   🔄 系统正在使用标准模型，可考虑切换到Trainer训练的高级模型")

    sys.exit(0)
