#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信号质量分析系统
实现全面的交易信号质量评估、过滤和优化
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

class SignalGrade(Enum):
    """信号等级"""
    A_PLUS = "A+"    # 优秀信号，95%+置信度
    A = "A"          # 高质量信号，85%+置信度
    B_PLUS = "B+"    # 良好信号，75%+置信度
    B = "B"          # 中上信号，65%+置信度
    C_PLUS = "C+"    # 中等信号，55%+置信度
    C = "C"          # 一般信号，45%+置信度
    D = "D"          # 低质量信号，35%+置信度
    F = "F"          # 拒绝信号，<35%置信度

class MarketCondition(Enum):
    """市场状态"""
    STRONG_TREND_UP = "强上升趋势"
    TREND_UP = "上升趋势"
    WEAK_TREND_UP = "弱上升趋势"
    RANGING_HIGH = "高位震荡"
    RANGING = "震荡整理"
    RANGING_LOW = "低位震荡"
    WEAK_TREND_DOWN = "弱下降趋势"
    TREND_DOWN = "下降趋势"
    STRONG_TREND_DOWN = "强下降趋势"
    VOLATILE = "高波动"
    CALM = "平静"

class SignalType(Enum):
    """信号类型"""
    TREND_FOLLOWING = "趋势跟随"
    MEAN_REVERSION = "均值回归"
    BREAKOUT = "突破"
    MOMENTUM = "动量"
    CONTRARIAN = "逆向"
    SCALPING = "剥头皮"

@dataclass
class TechnicalScore:
    """技术指标评分"""
    trend_alignment: float      # 趋势一致性
    momentum_strength: float    # 动量强度
    support_resistance: float   # 支撑阻力
    volume_confirmation: float  # 成交量确认
    volatility_level: float     # 波动率水平
    pattern_recognition: float  # 形态识别
    overall_score: float        # 综合评分

@dataclass
class LLMAnalysisScore:
    """LLM分析评分"""
    reasoning_quality: float    # 推理质量
    confidence_level: float     # 置信度
    risk_assessment: float      # 风险评估
    entry_timing: float         # 入场时机
    exit_strategy: float        # 出场策略
    market_understanding: float # 市场理解
    overall_score: float        # 综合评分

@dataclass
class SignalQuality:
    """信号质量"""
    signal_grade: SignalGrade           # 信号等级
    confidence_score: float             # 置信度评分
    technical_score: TechnicalScore     # 技术评分
    llm_score: LLMAnalysisScore        # LLM评分
    market_condition: MarketCondition   # 市场状态
    signal_type: SignalType            # 信号类型
    risk_reward_ratio: float           # 风险回报比
    entry_quality: float               # 入场质量
    timing_score: float                # 时机评分
    overall_quality: float             # 综合质量
    recommendation: str                 # 建议
    warnings: List[str]                # 警告信息

class AdvancedSignalAnalyzer:
    """高级信号质量分析器"""

    def __init__(self):
        # 评分权重配置
        self.scoring_weights = {
            'technical_analysis': 0.35,    # 技术分析权重
            'llm_analysis': 0.30,          # LLM分析权重
            'market_condition': 0.20,      # 市场状态权重
            'timing': 0.15                 # 时机权重
        }

        # 技术指标权重
        self.technical_weights = {
            'trend_alignment': 0.25,
            'momentum_strength': 0.20,
            'support_resistance': 0.20,
            'volume_confirmation': 0.15,
            'volatility_level': 0.10,
            'pattern_recognition': 0.10
        }

        # LLM分析权重
        self.llm_weights = {
            'reasoning_quality': 0.25,
            'confidence_level': 0.20,
            'risk_assessment': 0.20,
            'entry_timing': 0.15,
            'exit_strategy': 0.10,
            'market_understanding': 0.10
        }

        # 信号质量历史
        self.signal_history = []

        # 日志
        self.logger = logging.getLogger(__name__)

    def analyze_signal_quality(self, market_data: Dict, llm_analysis: Dict,
                             trade_instructions: Dict) -> SignalQuality:
        """全面分析信号质量"""

        try:
            # 1. 技术指标评分
            technical_score = self._analyze_technical_indicators(market_data)

            # 2. LLM分析评分
            llm_score = self._analyze_llm_quality(llm_analysis, trade_instructions)

            # 3. 市场状态识别
            market_condition = self._identify_market_condition(market_data)

            # 4. 信号类型识别
            signal_type = self._identify_signal_type(trade_instructions, market_data)

            # 5. 风险回报比分析
            risk_reward_ratio = self._calculate_risk_reward_ratio(trade_instructions)

            # 6. 入场质量评估
            entry_quality = self._assess_entry_quality(market_data, trade_instructions)

            # 7. 时机评分
            timing_score = self._assess_timing_quality(market_data, market_condition)

            # 8. 综合质量计算
            overall_quality = self._calculate_overall_quality(
                technical_score, llm_score, market_condition, timing_score
            )

            # 9. 信号等级确定
            signal_grade = self._determine_signal_grade(overall_quality, risk_reward_ratio)

            # 10. 生成建议和警告
            recommendation, warnings = self._generate_recommendations(
                signal_grade, technical_score, llm_score, market_condition, risk_reward_ratio
            )

            signal_quality = SignalQuality(
                signal_grade=signal_grade,
                confidence_score=overall_quality,
                technical_score=technical_score,
                llm_score=llm_score,
                market_condition=market_condition,
                signal_type=signal_type,
                risk_reward_ratio=risk_reward_ratio,
                entry_quality=entry_quality,
                timing_score=timing_score,
                overall_quality=overall_quality,
                recommendation=recommendation,
                warnings=warnings
            )

            # 记录信号历史
            self._record_signal_quality(signal_quality, trade_instructions)

            return signal_quality

        except Exception as e:
            self.logger.error(f"信号质量分析失败: {e}")
            # 返回默认的低质量信号
            return self._create_default_signal_quality()

    def _analyze_technical_indicators(self, market_data: Dict) -> TechnicalScore:
        """分析技术指标质量"""

        # 获取技术指标数据
        current_price = market_data.get('current_price', 0)
        ma_20 = market_data.get('ma_20', current_price)
        ma_50 = market_data.get('ma_50', current_price)
        ma_200 = market_data.get('ma_200', current_price)
        rsi = market_data.get('rsi', 50)
        macd = market_data.get('macd', 0)
        macd_signal = market_data.get('macd_signal', 0)
        bb_upper = market_data.get('bb_upper', current_price * 1.02)
        bb_lower = market_data.get('bb_lower', current_price * 0.98)
        atr = market_data.get('atr', 0.001)
        volume = market_data.get('volume', 1000)

        # 1. 趋势一致性评分
        trend_alignment = self._score_trend_alignment(current_price, ma_20, ma_50, ma_200)

        # 2. 动量强度评分
        momentum_strength = self._score_momentum_strength(rsi, macd, macd_signal)

        # 3. 支撑阻力评分
        support_resistance = self._score_support_resistance(current_price, bb_upper, bb_lower, ma_20, ma_50)

        # 4. 成交量确认评分
        volume_confirmation = self._score_volume_confirmation(volume, market_data)

        # 5. 波动率水平评分
        volatility_level = self._score_volatility_level(atr, current_price)

        # 6. 形态识别评分
        pattern_recognition = self._score_pattern_recognition(market_data)

        # 计算综合技术评分
        overall_score = (
            trend_alignment * self.technical_weights['trend_alignment'] +
            momentum_strength * self.technical_weights['momentum_strength'] +
            support_resistance * self.technical_weights['support_resistance'] +
            volume_confirmation * self.technical_weights['volume_confirmation'] +
            volatility_level * self.technical_weights['volatility_level'] +
            pattern_recognition * self.technical_weights['pattern_recognition']
        )

        return TechnicalScore(
            trend_alignment=trend_alignment,
            momentum_strength=momentum_strength,
            support_resistance=support_resistance,
            volume_confirmation=volume_confirmation,
            volatility_level=volatility_level,
            pattern_recognition=pattern_recognition,
            overall_score=overall_score
        )

    def _analyze_llm_quality(self, llm_analysis: Dict, trade_instructions: Dict) -> LLMAnalysisScore:
        """分析LLM分析质量"""

        reasoning = llm_analysis.get('reasoning', '')
        action = trade_instructions.get('action', 'NONE')

        # 1. 推理质量评分
        reasoning_quality = self._score_reasoning_quality(reasoning)

        # 2. 置信度评分
        confidence_level = self._score_confidence_level(reasoning, llm_analysis)

        # 3. 风险评估评分
        risk_assessment = self._score_risk_assessment(trade_instructions, reasoning)

        # 4. 入场时机评分
        entry_timing = self._score_entry_timing(trade_instructions, reasoning)

        # 5. 出场策略评分
        exit_strategy = self._score_exit_strategy(trade_instructions, reasoning)

        # 6. 市场理解评分
        market_understanding = self._score_market_understanding(reasoning, llm_analysis)

        # 计算综合LLM评分
        overall_score = (
            reasoning_quality * self.llm_weights['reasoning_quality'] +
            confidence_level * self.llm_weights['confidence_level'] +
            risk_assessment * self.llm_weights['risk_assessment'] +
            entry_timing * self.llm_weights['entry_timing'] +
            exit_strategy * self.llm_weights['exit_strategy'] +
            market_understanding * self.llm_weights['market_understanding']
        )

        return LLMAnalysisScore(
            reasoning_quality=reasoning_quality,
            confidence_level=confidence_level,
            risk_assessment=risk_assessment,
            entry_timing=entry_timing,
            exit_strategy=exit_strategy,
            market_understanding=market_understanding,
            overall_score=overall_score
        )

    def _score_trend_alignment(self, current_price: float, ma_20: float,
                              ma_50: float, ma_200: float) -> float:
        """评分趋势一致性"""

        score = 0.0

        # 短期趋势（当前价格 vs MA20）
        if current_price > ma_20:
            score += 0.3
        elif current_price < ma_20:
            score += 0.3  # 下降趋势也是趋势

        # 中期趋势（MA20 vs MA50）
        if ma_20 > ma_50:
            score += 0.3
        elif ma_20 < ma_50:
            score += 0.3

        # 长期趋势（MA50 vs MA200）
        if ma_50 > ma_200:
            score += 0.2
        elif ma_50 < ma_200:
            score += 0.2

        # 趋势一致性奖励
        if ((current_price > ma_20 > ma_50 > ma_200) or
            (current_price < ma_20 < ma_50 < ma_200)):
            score += 0.2  # 完全一致的趋势

        return min(score, 1.0)

    def _score_momentum_strength(self, rsi: float, macd: float, macd_signal: float) -> float:
        """评分动量强度"""

        score = 0.0

        # RSI动量评分
        if 30 < rsi < 70:
            score += 0.4  # 正常范围
        elif 20 < rsi < 80:
            score += 0.3  # 可接受范围
        else:
            score += 0.1  # 极端值

        # MACD动量评分
        macd_diff = macd - macd_signal
        if abs(macd_diff) > 0.0001:  # 有明确的MACD信号
            score += 0.3
            if macd_diff > 0 and macd > 0:  # 强势上升
                score += 0.2
            elif macd_diff < 0 and macd < 0:  # 强势下降
                score += 0.2

        # 动量一致性
        if ((rsi > 50 and macd_diff > 0) or (rsi < 50 and macd_diff < 0)):
            score += 0.1

        return min(score, 1.0)

    def _score_support_resistance(self, current_price: float, bb_upper: float,
                                 bb_lower: float, ma_20: float, ma_50: float) -> float:
        """评分支撑阻力位质量"""

        score = 0.0

        # 布林带位置评分
        bb_range = bb_upper - bb_lower
        if bb_range > 0:
            bb_position = (current_price - bb_lower) / bb_range

            if 0.2 < bb_position < 0.8:  # 在布林带中间区域
                score += 0.4
            elif 0.1 < bb_position < 0.9:  # 在布林带内
                score += 0.3
            else:  # 接近或超出布林带边界
                score += 0.1

        # 均线支撑阻力评分
        ma_distance_20 = abs(current_price - ma_20) / current_price
        ma_distance_50 = abs(current_price - ma_50) / current_price

        if ma_distance_20 < 0.002:  # 接近MA20
            score += 0.3
        elif ma_distance_20 < 0.005:
            score += 0.2

        if ma_distance_50 < 0.003:  # 接近MA50
            score += 0.3
        elif ma_distance_50 < 0.008:
            score += 0.2

        return min(score, 1.0)

    def _score_volume_confirmation(self, volume: float, market_data: Dict) -> float:
        """评分成交量确认"""

        # 简化的成交量评分
        # 在实际应用中，这里应该比较当前成交量与历史平均成交量

        avg_volume = market_data.get('avg_volume', volume)

        if volume > avg_volume * 1.5:  # 成交量放大
            return 0.8
        elif volume > avg_volume * 1.2:
            return 0.6
        elif volume > avg_volume * 0.8:
            return 0.5
        else:  # 成交量萎缩
            return 0.3

    def _score_volatility_level(self, atr: float, current_price: float) -> float:
        """评分波动率水平"""

        if current_price <= 0:
            return 0.5

        volatility_pct = atr / current_price

        # 适中的波动率最好
        if 0.0008 < volatility_pct < 0.002:  # 正常波动率
            return 1.0
        elif 0.0005 < volatility_pct < 0.003:  # 可接受波动率
            return 0.7
        elif volatility_pct < 0.0005:  # 波动率过低
            return 0.4
        else:  # 波动率过高
            return 0.3

    def _score_pattern_recognition(self, market_data: Dict) -> float:
        """评分形态识别"""

        # 简化的形态识别评分
        # 在实际应用中，这里应该实现更复杂的形态识别算法

        # 基于K线数据进行简单的形态识别
        timeframe15m = market_data.get('timeframe15m', [])

        if len(timeframe15m) < 3:
            return 0.5

        # 检查最近几根K线的形态
        recent_candles = timeframe15m[-3:]

        # 简单的上升/下降形态识别
        closes = [candle.get('close', 0) for candle in recent_candles]

        if len(closes) >= 3:
            if closes[-1] > closes[-2] > closes[-3]:  # 连续上升
                return 0.8
            elif closes[-1] < closes[-2] < closes[-3]:  # 连续下降
                return 0.8
            else:
                return 0.5

        return 0.5

    def _score_reasoning_quality(self, reasoning: str) -> float:
        """评分推理质量"""

        if not reasoning:
            return 0.1

        reasoning_lower = reasoning.lower()
        score = 0.0

        # 检查关键分析要素
        analysis_keywords = {
            '技术指标': 0.15,
            '支撑': 0.1, '阻力': 0.1,
            '趋势': 0.15,
            '成交量': 0.1,
            '风险': 0.1,
            '止损': 0.1, '止盈': 0.1,
            '时间框架': 0.05,
            '市场': 0.05
        }

        for keyword, weight in analysis_keywords.items():
            if keyword in reasoning_lower:
                score += weight

        # 检查分析深度
        if len(reasoning) > 200:  # 详细分析
            score += 0.1
        elif len(reasoning) > 100:  # 中等分析
            score += 0.05

        # 检查逻辑连贯性（简化）
        logical_connectors = ['因为', '所以', '由于', '因此', '但是', '然而', '同时']
        connector_count = sum(1 for connector in logical_connectors if connector in reasoning)
        score += min(connector_count * 0.02, 0.1)

        return min(score, 1.0)

    def _score_confidence_level(self, reasoning: str, llm_analysis: Dict) -> float:
        """评分置信度水平"""

        reasoning_lower = reasoning.lower()

        # 高置信度关键词
        high_confidence = ['确信', '明确', '强烈', '显著', '清晰']
        medium_confidence = ['可能', '倾向', '预期', '认为']
        low_confidence = ['不确定', '谨慎', '或许', '可能性']

        score = 0.5  # 基础分

        for keyword in high_confidence:
            if keyword in reasoning_lower:
                score += 0.15

        for keyword in medium_confidence:
            if keyword in reasoning_lower:
                score += 0.05

        for keyword in low_confidence:
            if keyword in reasoning_lower:
                score -= 0.1

        # 从LLM分析中提取置信度
        confidence = llm_analysis.get('confidence', 0.5)
        if isinstance(confidence, (int, float)):
            score = (score + confidence) / 2

        return max(min(score, 1.0), 0.0)

    def _score_risk_assessment(self, trade_instructions: Dict, reasoning: str) -> float:
        """评分风险评估质量"""

        score = 0.0

        # 检查止损止盈设置
        stop_loss = trade_instructions.get('stopLoss')
        take_profit = trade_instructions.get('takeProfit')
        entry_price = trade_instructions.get('entryPrice', 0)

        if stop_loss and take_profit and entry_price:
            # 计算风险回报比
            if trade_instructions.get('action') == 'BUY':
                risk = abs(entry_price - stop_loss)
                reward = abs(take_profit - entry_price)
            else:
                risk = abs(stop_loss - entry_price)
                reward = abs(entry_price - take_profit)

            if risk > 0:
                rr_ratio = reward / risk
                if rr_ratio >= 2.0:
                    score += 0.4
                elif rr_ratio >= 1.5:
                    score += 0.3
                elif rr_ratio >= 1.0:
                    score += 0.2
                else:
                    score += 0.1

        # 检查风险相关关键词
        risk_keywords = ['风险', '止损', '止盈', '回撤', '保护']
        reasoning_lower = reasoning.lower()

        for keyword in risk_keywords:
            if keyword in reasoning_lower:
                score += 0.1

        # 检查仓位大小合理性
        lot_size = trade_instructions.get('lotSize', 0)
        if 0.01 <= lot_size <= 0.1:  # 合理的仓位大小
            score += 0.2
        elif lot_size <= 0.2:
            score += 0.1

        return min(score, 1.0)

    def _score_entry_timing(self, trade_instructions: Dict, reasoning: str) -> float:
        """评分入场时机"""

        score = 0.5  # 基础分
        reasoning_lower = reasoning.lower()

        # 时机相关关键词
        timing_keywords = {
            '突破': 0.15,
            '回调': 0.15,
            '反弹': 0.15,
            '确认': 0.1,
            '信号': 0.1,
            '时机': 0.1,
            '入场': 0.1
        }

        for keyword, weight in timing_keywords.items():
            if keyword in reasoning_lower:
                score += weight

        # 订单类型评分
        order_type = trade_instructions.get('orderType', 'MARKET')
        if order_type == 'LIMIT':
            score += 0.1  # 限价单显示更好的时机把握
        elif order_type == 'STOP':
            score += 0.05  # 止损单显示趋势跟随

        return min(score, 1.0)

    def _score_exit_strategy(self, trade_instructions: Dict, reasoning: str) -> float:
        """评分出场策略"""

        score = 0.0

        # 检查是否有明确的出场计划
        stop_loss = trade_instructions.get('stopLoss')
        take_profit = trade_instructions.get('takeProfit')

        if stop_loss:
            score += 0.4
        if take_profit:
            score += 0.4

        # 检查出场相关关键词
        exit_keywords = ['止损', '止盈', '出场', '平仓', '目标']
        reasoning_lower = reasoning.lower()

        for keyword in exit_keywords:
            if keyword in reasoning_lower:
                score += 0.05

        # 检查是否有分批出场或移动止损的考虑
        advanced_exit = ['分批', '移动', '跟踪', '调整']
        for keyword in advanced_exit:
            if keyword in reasoning_lower:
                score += 0.1

        return min(score, 1.0)

    def _score_market_understanding(self, reasoning: str, llm_analysis: Dict) -> float:
        """评分市场理解程度"""

        score = 0.0
        reasoning_lower = reasoning.lower()

        # 市场理解关键词
        market_keywords = {
            '市场': 0.1,
            '趋势': 0.1,
            '波动': 0.1,
            '流动性': 0.1,
            '情绪': 0.1,
            '基本面': 0.1,
            '技术面': 0.1,
            '时段': 0.05,
            '周期': 0.05
        }

        for keyword, weight in market_keywords.items():
            if keyword in reasoning_lower:
                score += weight

        # 检查多时间框架分析
        timeframe_keywords = ['15分钟', '1小时', '日线', '周线', '短期', '中期', '长期']
        timeframe_count = sum(1 for keyword in timeframe_keywords if keyword in reasoning_lower)
        score += min(timeframe_count * 0.1, 0.3)

        # 检查市场环境描述
        environment_keywords = ['震荡', '趋势', '突破', '整理', '反转']
        for keyword in environment_keywords:
            if keyword in reasoning_lower:
                score += 0.05

        return min(score, 1.0)

    def _identify_market_condition(self, market_data: Dict) -> MarketCondition:
        """识别市场状态"""

        current_price = market_data.get('current_price', 0)
        ma_20 = market_data.get('ma_20', current_price)
        ma_50 = market_data.get('ma_50', current_price)
        ma_200 = market_data.get('ma_200', current_price)
        atr = market_data.get('atr', 0.001)
        rsi = market_data.get('rsi', 50)

        # 计算趋势强度
        if current_price > ma_20 > ma_50 > ma_200:
            if rsi > 70:
                return MarketCondition.STRONG_TREND_UP
            elif rsi > 55:
                return MarketCondition.TREND_UP
            else:
                return MarketCondition.WEAK_TREND_UP
        elif current_price < ma_20 < ma_50 < ma_200:
            if rsi < 30:
                return MarketCondition.STRONG_TREND_DOWN
            elif rsi < 45:
                return MarketCondition.TREND_DOWN
            else:
                return MarketCondition.WEAK_TREND_DOWN
        else:
            # 震荡市场
            volatility_pct = atr / current_price if current_price > 0 else 0

            if volatility_pct > 0.003:
                return MarketCondition.VOLATILE
            elif volatility_pct < 0.0008:
                return MarketCondition.CALM
            elif current_price > ma_50:
                return MarketCondition.RANGING_HIGH
            elif current_price < ma_50:
                return MarketCondition.RANGING_LOW
            else:
                return MarketCondition.RANGING

    def _identify_signal_type(self, trade_instructions: Dict, market_data: Dict) -> SignalType:
        """识别信号类型"""

        reasoning = trade_instructions.get('reasoning', '').lower()
        order_type = trade_instructions.get('orderType', 'MARKET')

        # 基于关键词识别信号类型
        if '突破' in reasoning:
            return SignalType.BREAKOUT
        elif '回调' in reasoning or '回撤' in reasoning:
            return SignalType.MEAN_REVERSION
        elif '趋势' in reasoning:
            return SignalType.TREND_FOLLOWING
        elif '动量' in reasoning or '加速' in reasoning:
            return SignalType.MOMENTUM
        elif '反转' in reasoning or '逆向' in reasoning:
            return SignalType.CONTRARIAN
        elif order_type == 'MARKET' and '快速' in reasoning:
            return SignalType.SCALPING
        else:
            return SignalType.TREND_FOLLOWING  # 默认

    def _calculate_risk_reward_ratio(self, trade_instructions: Dict) -> float:
        """计算风险回报比"""

        entry_price = trade_instructions.get('entryPrice', 0)
        stop_loss = trade_instructions.get('stopLoss', 0)
        take_profit = trade_instructions.get('takeProfit', 0)
        action = trade_instructions.get('action', 'BUY')

        if not all([entry_price, stop_loss, take_profit]):
            return 0.0

        if action == 'BUY':
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
        else:  # SELL
            risk = abs(stop_loss - entry_price)
            reward = abs(entry_price - take_profit)

        if risk > 0:
            return reward / risk
        else:
            return 0.0

    def _assess_entry_quality(self, market_data: Dict, trade_instructions: Dict) -> float:
        """评估入场质量"""

        current_price = market_data.get('current_price', 0)
        entry_price = trade_instructions.get('entryPrice', current_price)
        order_type = trade_instructions.get('orderType', 'MARKET')

        score = 0.5  # 基础分

        # 市价单 vs 限价单
        if order_type == 'MARKET':
            score += 0.2  # 市价单执行确定性高
        elif order_type == 'LIMIT':
            # 限价单价格合理性
            price_diff = abs(entry_price - current_price) / current_price
            if price_diff < 0.001:  # 接近当前价格
                score += 0.3
            elif price_diff < 0.002:
                score += 0.2
            else:
                score += 0.1

        # 基于技术位置评估入场质量
        ma_20 = market_data.get('ma_20', current_price)
        ma_50 = market_data.get('ma_50', current_price)

        # 在关键技术位附近入场加分
        ma_distance = min(abs(entry_price - ma_20), abs(entry_price - ma_50)) / current_price
        if ma_distance < 0.001:
            score += 0.2
        elif ma_distance < 0.002:
            score += 0.1

        return min(score, 1.0)

    def _assess_timing_quality(self, market_data: Dict, market_condition: MarketCondition) -> float:
        """评估时机质量"""

        score = 0.5  # 基础分

        # 基于市场状态调整时机评分
        condition_scores = {
            MarketCondition.STRONG_TREND_UP: 0.9,
            MarketCondition.TREND_UP: 0.8,
            MarketCondition.WEAK_TREND_UP: 0.6,
            MarketCondition.STRONG_TREND_DOWN: 0.9,
            MarketCondition.TREND_DOWN: 0.8,
            MarketCondition.WEAK_TREND_DOWN: 0.6,
            MarketCondition.RANGING: 0.4,
            MarketCondition.RANGING_HIGH: 0.5,
            MarketCondition.RANGING_LOW: 0.5,
            MarketCondition.VOLATILE: 0.3,
            MarketCondition.CALM: 0.4
        }

        return condition_scores.get(market_condition, 0.5)

    def _calculate_overall_quality(self, technical_score: TechnicalScore,
                                 llm_score: LLMAnalysisScore,
                                 market_condition: MarketCondition,
                                 timing_score: float) -> float:
        """计算综合质量评分"""

        # 基于市场状态调整权重
        market_adjustment = self._get_market_condition_adjustment(market_condition)

        overall_score = (
            technical_score.overall_score * self.scoring_weights['technical_analysis'] +
            llm_score.overall_score * self.scoring_weights['llm_analysis'] +
            market_adjustment * self.scoring_weights['market_condition'] +
            timing_score * self.scoring_weights['timing']
        )

        return min(overall_score, 1.0)

    def _get_market_condition_adjustment(self, market_condition: MarketCondition) -> float:
        """获取市场状态调整系数"""

        adjustments = {
            MarketCondition.STRONG_TREND_UP: 1.0,
            MarketCondition.TREND_UP: 0.9,
            MarketCondition.WEAK_TREND_UP: 0.7,
            MarketCondition.STRONG_TREND_DOWN: 1.0,
            MarketCondition.TREND_DOWN: 0.9,
            MarketCondition.WEAK_TREND_DOWN: 0.7,
            MarketCondition.RANGING: 0.5,
            MarketCondition.RANGING_HIGH: 0.6,
            MarketCondition.RANGING_LOW: 0.6,
            MarketCondition.VOLATILE: 0.3,
            MarketCondition.CALM: 0.4
        }

        return adjustments.get(market_condition, 0.5)

    def _determine_signal_grade(self, overall_quality: float, risk_reward_ratio: float) -> SignalGrade:
        """确定信号等级"""

        # 基础等级
        if overall_quality >= 0.95:
            base_grade = SignalGrade.A_PLUS
        elif overall_quality >= 0.85:
            base_grade = SignalGrade.A
        elif overall_quality >= 0.75:
            base_grade = SignalGrade.B_PLUS
        elif overall_quality >= 0.65:
            base_grade = SignalGrade.B
        elif overall_quality >= 0.55:
            base_grade = SignalGrade.C_PLUS
        elif overall_quality >= 0.45:
            base_grade = SignalGrade.C
        elif overall_quality >= 0.35:
            base_grade = SignalGrade.D
        else:
            base_grade = SignalGrade.F

        # 风险回报比调整
        if risk_reward_ratio >= 3.0:
            # 优秀的风险回报比，提升等级
            grade_upgrades = {
                SignalGrade.A: SignalGrade.A_PLUS,
                SignalGrade.B_PLUS: SignalGrade.A,
                SignalGrade.B: SignalGrade.B_PLUS,
                SignalGrade.C_PLUS: SignalGrade.B,
                SignalGrade.C: SignalGrade.C_PLUS,
                SignalGrade.D: SignalGrade.C
            }
            base_grade = grade_upgrades.get(base_grade, base_grade)
        elif risk_reward_ratio < 1.0:
            # 较差的风险回报比，降低等级
            grade_downgrades = {
                SignalGrade.A_PLUS: SignalGrade.A,
                SignalGrade.A: SignalGrade.B_PLUS,
                SignalGrade.B_PLUS: SignalGrade.B,
                SignalGrade.B: SignalGrade.C_PLUS,
                SignalGrade.C_PLUS: SignalGrade.C,
                SignalGrade.C: SignalGrade.D,
                SignalGrade.D: SignalGrade.F
            }
            base_grade = grade_downgrades.get(base_grade, base_grade)

        return base_grade

    def _generate_recommendations(self, signal_grade: SignalGrade,
                                technical_score: TechnicalScore,
                                llm_score: LLMAnalysisScore,
                                market_condition: MarketCondition,
                                risk_reward_ratio: float) -> Tuple[str, List[str]]:
        """生成建议和警告"""

        recommendations = []
        warnings = []

        # 基于信号等级的建议
        if signal_grade in [SignalGrade.A_PLUS, SignalGrade.A]:
            recommendations.append("优秀信号，建议正常仓位执行")
        elif signal_grade in [SignalGrade.B_PLUS, SignalGrade.B]:
            recommendations.append("良好信号，建议适当减少仓位")
        elif signal_grade in [SignalGrade.C_PLUS, SignalGrade.C]:
            recommendations.append("一般信号，建议小仓位试探")
        elif signal_grade == SignalGrade.D:
            recommendations.append("低质量信号，建议谨慎考虑")
            warnings.append("信号质量较低，建议等待更好机会")
        else:  # F
            recommendations.append("拒绝执行，信号质量不足")
            warnings.append("信号质量过低，强烈建议放弃此次交易")

        # 技术分析相关建议
        if technical_score.trend_alignment < 0.5:
            warnings.append("趋势一致性不足，注意市场方向不明确")

        if technical_score.momentum_strength < 0.4:
            warnings.append("动量不足，可能缺乏持续性")

        if technical_score.volatility_level < 0.4:
            warnings.append("波动率异常，注意市场流动性风险")

        # LLM分析相关建议
        if llm_score.reasoning_quality < 0.5:
            warnings.append("分析推理质量不足，建议重新分析")

        if llm_score.risk_assessment < 0.5:
            warnings.append("风险评估不充分，注意风险控制")

        # 市场状态相关建议
        if market_condition in [MarketCondition.VOLATILE]:
            warnings.append("市场高波动，建议减少仓位或等待稳定")
        elif market_condition in [MarketCondition.RANGING]:
            recommendations.append("震荡市场，适合短线操作")

        # 风险回报比建议
        if risk_reward_ratio < 1.0:
            warnings.append(f"风险回报比过低({risk_reward_ratio:.2f})，建议调整止损止盈")
        elif risk_reward_ratio >= 2.0:
            recommendations.append(f"优秀的风险回报比({risk_reward_ratio:.2f})")

        # 综合建议
        main_recommendation = "; ".join(recommendations) if recommendations else "无明确建议"

        return main_recommendation, warnings

    def _record_signal_quality(self, signal_quality: SignalQuality, trade_instructions: Dict):
        """记录信号质量历史"""

        record = {
            'timestamp': datetime.now().isoformat(),
            'signal_grade': signal_quality.signal_grade.value,
            'confidence_score': signal_quality.confidence_score,
            'market_condition': signal_quality.market_condition.value,
            'signal_type': signal_quality.signal_type.value,
            'risk_reward_ratio': signal_quality.risk_reward_ratio,
            'action': trade_instructions.get('action', 'NONE'),
            'technical_score': signal_quality.technical_score.overall_score,
            'llm_score': signal_quality.llm_score.overall_score
        }

        self.signal_history.append(record)

        # 保持最近100条记录
        if len(self.signal_history) > 100:
            self.signal_history = self.signal_history[-100:]

    def _create_default_signal_quality(self) -> SignalQuality:
        """创建默认的低质量信号"""

        return SignalQuality(
            signal_grade=SignalGrade.F,
            confidence_score=0.1,
            technical_score=TechnicalScore(0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1),
            llm_score=LLMAnalysisScore(0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1),
            market_condition=MarketCondition.VOLATILE,
            signal_type=SignalType.TREND_FOLLOWING,
            risk_reward_ratio=0.0,
            entry_quality=0.1,
            timing_score=0.1,
            overall_quality=0.1,
            recommendation="系统错误，拒绝执行",
            warnings=["信号分析失败"]
        )

    def get_signal_statistics(self) -> Dict:
        """获取信号统计信息"""

        if not self.signal_history:
            return {
                'total_signals': 0,
                'grade_distribution': {},
                'average_confidence': 0.0,
                'average_risk_reward': 0.0
            }

        # 统计信号等级分布
        grade_counts = {}
        total_confidence = 0.0
        total_risk_reward = 0.0

        for record in self.signal_history:
            grade = record['signal_grade']
            grade_counts[grade] = grade_counts.get(grade, 0) + 1
            total_confidence += record['confidence_score']
            total_risk_reward += record['risk_reward_ratio']

        return {
            'total_signals': len(self.signal_history),
            'grade_distribution': grade_counts,
            'average_confidence': total_confidence / len(self.signal_history),
            'average_risk_reward': total_risk_reward / len(self.signal_history),
            'recent_signals': self.signal_history[-10:] if self.signal_history else []
        }

    def should_execute_signal(self, signal_quality: SignalQuality) -> Tuple[bool, str]:
        """判断是否应该执行信号"""

        # 基于信号等级决定
        if signal_quality.signal_grade == SignalGrade.F:
            return False, "信号等级F，拒绝执行"
        elif signal_quality.signal_grade == SignalGrade.D:
            return False, "信号等级D，质量过低"
        elif signal_quality.signal_grade in [SignalGrade.C, SignalGrade.C_PLUS]:
            if signal_quality.risk_reward_ratio < 1.5:
                return False, "信号等级C，且风险回报比不足"
            else:
                return True, "信号等级C，但风险回报比可接受"
        else:  # B级及以上
            return True, f"信号等级{signal_quality.signal_grade.value}，允许执行"

    def get_position_size_multiplier(self, signal_quality: SignalQuality) -> float:
        """获取仓位大小倍数"""

        # 基于信号等级调整仓位
        grade_multipliers = {
            SignalGrade.A_PLUS: 1.0,
            SignalGrade.A: 0.9,
            SignalGrade.B_PLUS: 0.8,
            SignalGrade.B: 0.7,
            SignalGrade.C_PLUS: 0.5,
            SignalGrade.C: 0.3,
            SignalGrade.D: 0.1,
            SignalGrade.F: 0.0
        }

        base_multiplier = grade_multipliers.get(signal_quality.signal_grade, 0.3)

        # 基于市场状态调整
        market_adjustments = {
            MarketCondition.STRONG_TREND_UP: 1.0,
            MarketCondition.TREND_UP: 0.9,
            MarketCondition.WEAK_TREND_UP: 0.7,
            MarketCondition.STRONG_TREND_DOWN: 1.0,
            MarketCondition.TREND_DOWN: 0.9,
            MarketCondition.WEAK_TREND_DOWN: 0.7,
            MarketCondition.RANGING: 0.6,
            MarketCondition.RANGING_HIGH: 0.6,
            MarketCondition.RANGING_LOW: 0.6,
            MarketCondition.VOLATILE: 0.4,
            MarketCondition.CALM: 0.5
        }

        market_multiplier = market_adjustments.get(signal_quality.market_condition, 0.5)

        # 基于风险回报比调整
        rr_multiplier = 1.0
        if signal_quality.risk_reward_ratio >= 3.0:
            rr_multiplier = 1.2
        elif signal_quality.risk_reward_ratio >= 2.0:
            rr_multiplier = 1.1
        elif signal_quality.risk_reward_ratio < 1.0:
            rr_multiplier = 0.5

        final_multiplier = base_multiplier * market_multiplier * rr_multiplier

        return max(min(final_multiplier, 1.0), 0.0)