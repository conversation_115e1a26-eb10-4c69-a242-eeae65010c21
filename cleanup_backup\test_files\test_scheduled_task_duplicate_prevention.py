"""
测试定时任务重复防止功能
"""
import os
import sys
import time
from datetime import datetime

from app.utils.forex_scheduled_tasks import start_hourly_forex_analysis, stop_all_tasks
from app.utils.mt4_client import mt4_client

def test_scheduled_task_duplicate_prevention():
    """测试定时任务重复防止功能"""
    try:
        print('=' * 50)
        print(f'开始测试定时任务重复防止功能，时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        print('=' * 50)
        
        # 步骤1: 确保MT4连接
        print('\n步骤1: 确保MT4连接')
        if not mt4_client.is_connected:
            print('MT4客户端未连接，尝试连接')
            connected = mt4_client.connect()
            if not connected:
                print('无法连接到MT4客户端，测试失败')
                return
        print('MT4连接正常')
        
        # 步骤2: 获取当前持仓和挂单
        print('\n步骤2: 获取当前持仓和挂单')
        positions_response = mt4_client.get_active_orders()
        positions = positions_response.get('orders', [])
        print(f'当前持仓数量: {len(positions)}')
        for position in positions:
            print(f'  - 持仓: 订单ID: {position.get("order_id")}, 类型: {position.get("type")}, 货币对: {position.get("symbol")}, 手数: {position.get("lots")}, 盈亏: {position.get("profit")}')
        
        pending_orders_response = mt4_client.get_pending_orders()
        pending_orders = pending_orders_response.get('orders', [])
        print(f'当前挂单数量: {len(pending_orders)}')
        for order in pending_orders:
            print(f'  - 挂单: 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 货币对: {order.get("symbol")}, 手数: {order.get("lots")}, 价格: {order.get("open_price")}, 止损: {order.get("sl")}, 止盈: {order.get("tp")}')
        
        # 步骤3: 启动定时任务，立即执行一次，并启用自动交易
        print('\n步骤3: 启动定时任务，立即执行一次，并启用自动交易')
        start_hourly_forex_analysis(run_immediately=True, auto_trade=True)
        
        # 步骤4: 等待任务执行完成
        print('\n步骤4: 等待任务执行完成')
        print('等待60秒，让任务执行完成')
        for i in range(60):
            time.sleep(1)
            if i % 10 == 0:
                print(f'已等待 {i} 秒')
        
        # 步骤5: 再次获取持仓和挂单
        print('\n步骤5: 再次获取持仓和挂单')
        new_positions_response = mt4_client.get_active_orders()
        new_positions = new_positions_response.get('orders', [])
        print(f'新的持仓数量: {len(new_positions)}')
        for position in new_positions:
            print(f'  - 持仓: 订单ID: {position.get("order_id")}, 类型: {position.get("type")}, 货币对: {position.get("symbol")}, 手数: {position.get("lots")}, 盈亏: {position.get("profit")}')
        
        new_pending_orders_response = mt4_client.get_pending_orders()
        new_pending_orders = new_pending_orders_response.get('orders', [])
        print(f'新的挂单数量: {len(new_pending_orders)}')
        for order in new_pending_orders:
            print(f'  - 挂单: 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 货币对: {order.get("symbol")}, 手数: {order.get("lots")}, 价格: {order.get("open_price")}, 止损: {order.get("sl")}, 止盈: {order.get("tp")}')
        
        # 步骤6: 再次启动定时任务，应该不会重复执行交易
        print('\n步骤6: 再次启动定时任务，应该不会重复执行交易')
        start_hourly_forex_analysis(run_immediately=True, auto_trade=True)
        
        # 步骤7: 等待任务执行完成
        print('\n步骤7: 等待任务执行完成')
        print('等待30秒，让任务执行完成')
        for i in range(30):
            time.sleep(1)
            if i % 10 == 0:
                print(f'已等待 {i} 秒')
        
        # 步骤8: 再次获取持仓和挂单
        print('\n步骤8: 再次获取持仓和挂单')
        final_positions_response = mt4_client.get_active_orders()
        final_positions = final_positions_response.get('orders', [])
        print(f'最终持仓数量: {len(final_positions)}')
        for position in final_positions:
            print(f'  - 持仓: 订单ID: {position.get("order_id")}, 类型: {position.get("type")}, 货币对: {position.get("symbol")}, 手数: {position.get("lots")}, 盈亏: {position.get("profit")}')
        
        final_pending_orders_response = mt4_client.get_pending_orders()
        final_pending_orders = final_pending_orders_response.get('orders', [])
        print(f'最终挂单数量: {len(final_pending_orders)}')
        for order in final_pending_orders:
            print(f'  - 挂单: 订单ID: {order.get("order_id")}, 类型: {order.get("type")}, 货币对: {order.get("symbol")}, 手数: {order.get("lots")}, 价格: {order.get("open_price")}, 止损: {order.get("sl")}, 止盈: {order.get("tp")}')
        
        # 步骤9: 检查是否有新增持仓或挂单
        print('\n步骤9: 检查是否有新增持仓或挂单')
        if len(final_positions) > len(new_positions) or len(final_pending_orders) > len(new_pending_orders):
            print('持仓或挂单数量增加，可能重复执行了交易')
        else:
            print('持仓和挂单数量未增加，成功防止了重复交易')
        
        # 步骤10: 停止所有任务
        print('\n步骤10: 停止所有任务')
        stop_all_tasks()
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_scheduled_task_duplicate_prevention()
