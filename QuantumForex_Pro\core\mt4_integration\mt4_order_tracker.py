#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MT4订单跟踪器
实时跟踪MT4中的订单状态变化，确保与本地数据同步
"""

import json
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

@dataclass
class MT4Order:
    """MT4订单信息"""
    ticket: int
    symbol: str
    order_type: str  # BUY/SELL
    volume: float
    open_price: float
    open_time: datetime
    stop_loss: float = 0.0
    take_profit: float = 0.0
    close_price: float = 0.0
    close_time: Optional[datetime] = None
    profit: float = 0.0
    commission: float = 0.0
    swap: float = 0.0
    comment: str = ""
    magic_number: int = 0
    status: str = "OPEN"  # OPEN/CLOSED

class MT4OrderTracker:
    """MT4订单跟踪器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 数据库路径
        self.db_path = Path("data/mt4_orders.db")
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # 初始化数据库
        self._init_database()

        # 运行状态
        self.running = False
        self.monitor_thread = None

        # 回调函数
        self.order_opened_callbacks = []
        self.order_closed_callbacks = []
        self.order_modified_callbacks = []

        # 缓存
        self.last_known_orders = {}

        # 监控间隔
        self.monitor_interval = 5  # 5秒检查一次

    def _init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS mt4_orders (
                        ticket INTEGER PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        order_type TEXT NOT NULL,
                        volume REAL NOT NULL,
                        open_price REAL NOT NULL,
                        open_time TEXT NOT NULL,
                        stop_loss REAL DEFAULT 0.0,
                        take_profit REAL DEFAULT 0.0,
                        close_price REAL DEFAULT 0.0,
                        close_time TEXT,
                        profit REAL DEFAULT 0.0,
                        commission REAL DEFAULT 0.0,
                        swap REAL DEFAULT 0.0,
                        comment TEXT DEFAULT '',
                        magic_number INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'OPEN',
                        last_updated TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 创建索引
                conn.execute("CREATE INDEX IF NOT EXISTS idx_ticket ON mt4_orders(ticket)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_symbol ON mt4_orders(symbol)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_status ON mt4_orders(status)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_open_time ON mt4_orders(open_time)")

                conn.commit()
                self.logger.info("MT4订单数据库初始化完成")

        except Exception as e:
            self.logger.error(f"MT4订单数据库初始化失败: {e}")
            raise

    def start_monitoring(self):
        """开始监控MT4订单"""
        try:
            if self.running:
                self.logger.warning("MT4订单监控已经在运行")
                return

            self.running = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()

            self.logger.info("MT4订单监控启动成功")

        except Exception as e:
            self.logger.error(f"启动MT4订单监控失败: {e}")
            self.running = False

    def stop_monitoring(self):
        """停止监控MT4订单"""
        try:
            self.running = False
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=10)

            self.logger.info("MT4订单监控已停止")

        except Exception as e:
            self.logger.error(f"停止MT4订单监控失败: {e}")

    def _monitor_loop(self):
        """监控循环"""
        self.logger.info("MT4订单监控循环启动")

        while self.running:
            try:
                # 获取当前MT4订单
                current_orders = self._get_mt4_orders()

                # 检测变化
                self._detect_order_changes(current_orders)

                # 更新缓存
                self.last_known_orders = current_orders

                # 等待下次检查
                time.sleep(self.monitor_interval)

            except Exception as e:
                self.logger.error(f"MT4订单监控循环异常: {e}")
                time.sleep(self.monitor_interval)

        self.logger.info("MT4订单监控循环结束")

    def _get_mt4_orders(self) -> Dict[int, MT4Order]:
        """获取MT4当前订单"""
        try:
            # 导入MT4客户端
            import sys
            import os

            # 添加项目根目录到路径
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            sys.path.append(project_root)

            try:
                from utils.mt4_client import MT4Client
            except ImportError:
                # 如果无法导入MT4客户端，创建一个模拟客户端
                class MockMT4Client:
                    def get_active_orders(self):
                        return {'status': 'success', 'orders': []}
                    def get_order_history(self, order_id=None, days=7):
                        return {'status': 'success', 'orders': []}
                MT4Client = MockMT4Client

            mt4_client = MT4Client()

            orders = {}

            # 获取活跃订单
            active_orders_response = mt4_client.get_active_orders()
            if active_orders_response and active_orders_response.get('status') == 'success':
                active_orders = active_orders_response.get('orders', [])

                for order_data in active_orders:
                    try:
                        order = self._parse_mt4_order(order_data, "OPEN")
                        if order:
                            orders[order.ticket] = order
                    except Exception as e:
                        self.logger.error(f"解析活跃订单失败: {e}")

            # 暂时跳过历史订单获取，因为MT4服务器不支持ORDER_HISTORY操作
            # 系统将专注于监控活跃订单的变化来检测平仓事件
            self.logger.debug("跳过历史订单获取 - MT4服务器不支持ORDER_HISTORY操作")

            return orders

        except Exception as e:
            self.logger.error(f"获取MT4订单失败: {e}")
            return {}

    def _parse_mt4_order(self, order_data: Dict, default_status: str) -> Optional[MT4Order]:
        """解析MT4订单数据"""
        try:
            # 解析订单号
            ticket = int(order_data.get('ticket', 0))
            if ticket == 0:
                return None

            # 解析基本信息
            symbol = order_data.get('symbol', '')
            order_type = order_data.get('type', '').upper()
            if order_type in ['0', 'OP_BUY']:
                order_type = 'BUY'
            elif order_type in ['1', 'OP_SELL']:
                order_type = 'SELL'

            volume = float(order_data.get('volume', 0.0))
            open_price = float(order_data.get('open_price', 0.0))

            # 解析时间
            open_time_str = order_data.get('open_time', '')
            try:
                open_time = datetime.fromisoformat(open_time_str.replace('Z', '+00:00'))
            except:
                open_time = datetime.now()

            # 解析关闭信息
            close_price = float(order_data.get('close_price', 0.0))
            close_time = None
            close_time_str = order_data.get('close_time', '')
            if close_time_str:
                try:
                    close_time = datetime.fromisoformat(close_time_str.replace('Z', '+00:00'))
                except:
                    pass

            # 解析其他信息
            stop_loss = float(order_data.get('stop_loss', 0.0))
            take_profit = float(order_data.get('take_profit', 0.0))
            profit = float(order_data.get('profit', 0.0))
            commission = float(order_data.get('commission', 0.0))
            swap = float(order_data.get('swap', 0.0))
            comment = order_data.get('comment', '')
            magic_number = int(order_data.get('magic', 0))

            # 确定状态
            status = default_status
            if close_time or close_price > 0:
                status = "CLOSED"

            return MT4Order(
                ticket=ticket,
                symbol=symbol,
                order_type=order_type,
                volume=volume,
                open_price=open_price,
                open_time=open_time,
                stop_loss=stop_loss,
                take_profit=take_profit,
                close_price=close_price,
                close_time=close_time,
                profit=profit,
                commission=commission,
                swap=swap,
                comment=comment,
                magic_number=magic_number,
                status=status
            )

        except Exception as e:
            self.logger.error(f"解析MT4订单数据失败: {e}")
            return None

    def _detect_order_changes(self, current_orders: Dict[int, MT4Order]):
        """检测订单变化"""
        try:
            # 检测新开订单
            for ticket, order in current_orders.items():
                if ticket not in self.last_known_orders:
                    if order.status == "OPEN":
                        self._handle_order_opened(order)
                    elif order.status == "CLOSED":
                        self._handle_order_closed(order)

                    # 保存到数据库
                    self._save_order_to_db(order)

            # 检测订单状态变化
            for ticket, order in current_orders.items():
                if ticket in self.last_known_orders:
                    old_order = self.last_known_orders[ticket]

                    # 检测订单关闭
                    if old_order.status == "OPEN" and order.status == "CLOSED":
                        self._handle_order_closed(order)
                        self._update_order_in_db(order)

                    # 检测订单修改
                    elif self._order_modified(old_order, order):
                        self._handle_order_modified(order)
                        self._update_order_in_db(order)

            # 检测订单消失（可能是数据延迟）
            for ticket in self.last_known_orders:
                if ticket not in current_orders:
                    old_order = self.last_known_orders[ticket]
                    if old_order.status == "OPEN":
                        self.logger.warning(f"订单 {ticket} 从MT4中消失，可能已关闭")

        except Exception as e:
            self.logger.error(f"检测订单变化失败: {e}")

    def _order_modified(self, old_order: MT4Order, new_order: MT4Order) -> bool:
        """检查订单是否被修改"""
        return (old_order.stop_loss != new_order.stop_loss or
                old_order.take_profit != new_order.take_profit or
                old_order.volume != new_order.volume)

    def _handle_order_opened(self, order: MT4Order):
        """处理订单开仓"""
        try:
            self.logger.info(f"🔵 检测到新开订单: {order.ticket} {order.symbol} {order.order_type} {order.volume}")

            # 调用回调函数
            for callback in self.order_opened_callbacks:
                try:
                    callback(order)
                except Exception as e:
                    self.logger.error(f"订单开仓回调失败: {e}")

        except Exception as e:
            self.logger.error(f"处理订单开仓失败: {e}")

    def _handle_order_closed(self, order: MT4Order):
        """处理订单关闭"""
        try:
            self.logger.info(f"🔴 检测到订单关闭: {order.ticket} {order.symbol} 盈亏: ${order.profit:.2f}")

            # 调用回调函数
            for callback in self.order_closed_callbacks:
                try:
                    callback(order)
                except Exception as e:
                    self.logger.error(f"订单关闭回调失败: {e}")

        except Exception as e:
            self.logger.error(f"处理订单关闭失败: {e}")

    def _handle_order_modified(self, order: MT4Order):
        """处理订单修改"""
        try:
            self.logger.info(f"🟡 检测到订单修改: {order.ticket} {order.symbol}")

            # 调用回调函数
            for callback in self.order_modified_callbacks:
                try:
                    callback(order)
                except Exception as e:
                    self.logger.error(f"订单修改回调失败: {e}")

        except Exception as e:
            self.logger.error(f"处理订单修改失败: {e}")

    def _save_order_to_db(self, order: MT4Order):
        """保存订单到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                data = asdict(order)
                data['open_time'] = order.open_time.isoformat()
                if order.close_time:
                    data['close_time'] = order.close_time.isoformat()

                columns = ', '.join(data.keys())
                placeholders = ', '.join(['?' for _ in data])

                conn.execute(
                    f"INSERT OR REPLACE INTO mt4_orders ({columns}) VALUES ({placeholders})",
                    list(data.values())
                )
                conn.commit()

        except Exception as e:
            self.logger.error(f"保存订单到数据库失败: {e}")

    def _update_order_in_db(self, order: MT4Order):
        """更新数据库中的订单"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE mt4_orders SET
                        close_price = ?,
                        close_time = ?,
                        profit = ?,
                        commission = ?,
                        swap = ?,
                        stop_loss = ?,
                        take_profit = ?,
                        status = ?,
                        last_updated = CURRENT_TIMESTAMP
                    WHERE ticket = ?
                """, (
                    order.close_price,
                    order.close_time.isoformat() if order.close_time else None,
                    order.profit,
                    order.commission,
                    order.swap,
                    order.stop_loss,
                    order.take_profit,
                    order.status,
                    order.ticket
                ))
                conn.commit()

        except Exception as e:
            self.logger.error(f"更新数据库订单失败: {e}")

    def register_order_opened_callback(self, callback: Callable[[MT4Order], None]):
        """注册订单开仓回调"""
        self.order_opened_callbacks.append(callback)

    def register_order_closed_callback(self, callback: Callable[[MT4Order], None]):
        """注册订单关闭回调"""
        self.order_closed_callbacks.append(callback)

    def register_order_modified_callback(self, callback: Callable[[MT4Order], None]):
        """注册订单修改回调"""
        self.order_modified_callbacks.append(callback)

    def get_order_by_ticket(self, ticket: int) -> Optional[MT4Order]:
        """根据订单号获取订单"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("SELECT * FROM mt4_orders WHERE ticket = ?", (ticket,))
                row = cursor.fetchone()

                if row:
                    data = dict(row)
                    data['open_time'] = datetime.fromisoformat(data['open_time'])
                    if data['close_time']:
                        data['close_time'] = datetime.fromisoformat(data['close_time'])

                    return MT4Order(**data)

        except Exception as e:
            self.logger.error(f"获取订单失败: {e}")

        return None

    def get_recent_orders(self, hours: int = 24) -> List[MT4Order]:
        """获取最近的订单"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM mt4_orders
                    WHERE open_time >= ?
                    ORDER BY open_time DESC
                """, (cutoff_time.isoformat(),))

                orders = []
                for row in cursor.fetchall():
                    data = dict(row)
                    data['open_time'] = datetime.fromisoformat(data['open_time'])
                    if data['close_time']:
                        data['close_time'] = datetime.fromisoformat(data['close_time'])
                    orders.append(MT4Order(**data))

                return orders

        except Exception as e:
            self.logger.error(f"获取最近订单失败: {e}")
            return []
