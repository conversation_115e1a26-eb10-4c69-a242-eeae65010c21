"""
外汇市场开放时间检测模块
用于检测外汇市场是否开放，以及计算下次开放时间
"""
import pytz
from datetime import datetime, timedelta


def is_market_open():
    """
    检查外汇市场是否开放

    外汇市场开放时间（UTC时间）：
    - 周日 21:00 - 周五 21:00
    - 周五 21:00 - 周日 21:00 休市

    对应北京时间：
    - 周一 05:00 - 周六 05:00

    Returns:
        bool: 市场是否开放
    """
    try:
        # 获取当前UTC时间
        utc_now = datetime.now(pytz.UTC)

        # 获取星期几（0=周一，6=周日）
        weekday = utc_now.weekday()
        hour = utc_now.hour

        # 周日22:00开始开市（北京时间周一05:00）
        if weekday == 6:  # 周日
            return hour >= 21

        # 周一到周四全天开市
        elif weekday <= 3:  # 周一到周四
            return True

        # 周五21:00结束（北京时间周六05:00）
        elif weekday == 4:  # 周五
            return hour < 21

        # 周六全天休市
        elif weekday == 5:  # 周六
            return False

    except Exception as e:
        print(f"检查市场开放状态时出错: {e}")
        # 出错时默认认为市场开放，避免系统完全停止
        return True


def get_market_status():
    """
    获取市场状态描述

    Returns:
        str: 市场状态描述
    """
    try:
        # 获取当前UTC时间
        utc_now = datetime.now(pytz.UTC)

        # 获取星期几（0=周一，6=周日）
        weekday = utc_now.weekday()
        hour = utc_now.hour
        weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

        # 检查是否开市
        if not is_market_open():
            if weekday == 5:  # 周六
                return f"{weekday_names[weekday]}休市"
            elif weekday == 6 and hour < 21:  # 周日21:00前
                return f"{weekday_names[weekday]}休市"
            elif weekday == 4 and hour >= 21:  # 周五21:00后
                return "周五收市"
            else:
                return "市场休市"
        else:
            # 市场开放，判断交易时段
            if weekday == 6 and hour >= 21:  # 周日晚开市
                return "周日晚开市"
            elif 0 <= hour < 9:
                return "亚洲交易时段"
            elif 9 <= hour < 16:
                return "欧洲交易时段"
            elif 16 <= hour < 21:
                return "美洲交易时段"
            else:
                return "市场开放"

    except Exception as e:
        print(f"获取市场状态时出错: {e}")
        return "状态未知"


def get_next_market_open_time():
    """
    获取下次市场开放时间

    Returns:
        datetime: 下次市场开放的UTC时间，如果无法确定则返回None
    """
    try:
        # 获取当前UTC时间
        utc_now = datetime.now(pytz.UTC)

        # 获取星期几（0=周一，6=周日）
        weekday = utc_now.weekday()
        hour = utc_now.hour

        # 如果市场已经开放，返回None
        if is_market_open():
            return None

        if weekday == 4 and hour >= 21:  # 周五21:00后
            # 下周日21:00开市
            days_to_sunday = 2  # 周五到周日
            next_sunday = utc_now.replace(hour=21, minute=0, second=0, microsecond=0) + timedelta(days=days_to_sunday)
            return next_sunday
        elif weekday == 5:  # 周六
            # 下周日21:00开市
            days_to_sunday = 1
            next_sunday = utc_now.replace(hour=21, minute=0, second=0, microsecond=0) + timedelta(days=days_to_sunday)
            return next_sunday
        elif weekday == 6 and hour < 21:  # 周日21:00前
            # 今天21:00开市
            today_21 = utc_now.replace(hour=21, minute=0, second=0, microsecond=0)
            return today_21
        else:
            # 其他情况，市场应该已经开放
            return None

    except Exception as e:
        print(f"计算下次市场开放时间时出错: {e}")
        return None


def is_mt4_likely_connected():
    """
    基于市场开放时间判断MT4是否可能连接
    这是一个辅助判断，不依赖实际的MT4连接状态

    Returns:
        bool: MT4是否可能连接
    """
    return is_market_open()


def get_market_close_reason():
    """
    获取市场关闭的原因

    Returns:
        str: 市场关闭的原因
    """
    try:
        # 获取当前UTC时间
        utc_now = datetime.now(pytz.UTC)

        # 获取星期几（0=周一，6=周日）
        weekday = utc_now.weekday()
        hour = utc_now.hour

        if weekday == 5:  # 周六
            return "周末休市（周六）"
        elif weekday == 6 and hour < 21:  # 周日21:00前
            return "周末休市（周日）"
        elif weekday == 4 and hour >= 21:  # 周五21:00后
            return "周五收市时间"
        else:
            return "市场开放中"

    except Exception as e:
        print(f"获取市场关闭原因时出错: {e}")
        return "未知原因"


def should_pause_system():
    """
    判断系统是否应该暂停运行

    Returns:
        tuple: (是否应该暂停, 暂停原因)
    """
    if not is_market_open():
        return True, get_market_close_reason()
    else:
        return False, "市场开放中"


def get_time_until_market_open():
    """
    获取距离市场开放的时间（秒）

    Returns:
        int: 距离市场开放的秒数，如果市场已开放则返回0
    """
    if is_market_open():
        return 0

    next_open = get_next_market_open_time()
    if next_open:
        utc_now = datetime.now(pytz.UTC)
        return int((next_open - utc_now).total_seconds())
    else:
        return 0


def format_time_until_open():
    """
    格式化显示距离市场开放的时间

    Returns:
        str: 格式化的时间字符串
    """
    seconds = get_time_until_market_open()
    if seconds <= 0:
        return "市场已开放"

    hours = seconds // 3600
    minutes = (seconds % 3600) // 60

    if hours > 24:
        days = hours // 24
        hours = hours % 24
        return f"{days}天{hours}小时{minutes}分钟"
    elif hours > 0:
        return f"{hours}小时{minutes}分钟"
    else:
        return f"{minutes}分钟"
