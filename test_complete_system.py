#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统测试 - 从数据获取到交易执行
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_step_1_data_collection():
    """步骤1: 测试数据收集"""
    print("📊 步骤1: 测试数据收集")
    
    try:
        # 1.1 智能货币对选择
        from app.utils.intelligent_pair_selector import select_optimal_currency_pairs
        print("  1.1 智能货币对选择...")
        pairs = select_optimal_currency_pairs()
        print(f"  ✅ 选择结果: {pairs}")
        
        # 1.2 多货币对数据获取
        from app.utils.multi_pair_data_manager import get_multi_pair_analysis_data
        print("  1.2 多货币对数据获取...")
        data = get_multi_pair_analysis_data(pairs)
        print(f"  ✅ 获取成功，包含 {len(data)} 个货币对")
        
        # 1.3 MT4实时数据
        from app.utils.mt4_client import MT4Client
        print("  1.3 MT4实时数据获取...")
        mt4_client = MT4Client()
        if mt4_client.connect():
            price_info = mt4_client.get_market_info("EURUSD")
            print(f"  ✅ EURUSD实时价格: {price_info}")
            
            active_orders = mt4_client.get_active_orders()
            print(f"  ✅ 活跃订单: {len(active_orders.get('orders', []))} 个")
            
            pending_orders = mt4_client.get_pending_orders()
            print(f"  ✅ 挂单: {len(pending_orders.get('orders', []))} 个")
        
        return True, data, mt4_client
        
    except Exception as e:
        print(f"  ❌ 数据收集失败: {e}")
        return False, None, None

def test_step_2_llm_analysis():
    """步骤2: 测试LLM分析"""
    print("\n🧠 步骤2: 测试LLM分析")
    
    try:
        # 2.1 策略优化
        from app.utils.llm_optimized_trading_strategy import LLMOptimizedTradingStrategy
        print("  2.1 LLM策略优化...")
        strategy_optimizer = LLMOptimizedTradingStrategy()
        
        # 模拟市场数据
        market_data = {
            'volatility': 0.015,
            'trend_strength': 0.7,
            'rsi': 65.0,
            'macd_signal': 'bullish'
        }
        
        strategy_result = strategy_optimizer.optimize_strategy(market_data)
        print(f"  ✅ 策略选择: {strategy_result.get('selected_strategy', 'unknown')}")
        print(f"  ✅ 效率评分: {strategy_result.get('efficiency_score', 0)}")
        
        # 2.2 多轮分析（模拟）
        print("  2.2 多轮LLM分析...")
        # 这里我们跳过实际的LLM调用，模拟分析结果
        analysis_result = {
            'action': 'BUY',
            'orderType': 'MARKET',
            'entryPrice': None,
            'stopLoss': 1.1380,
            'takeProfit': 1.1420,
            'lotSize': 0.05,
            'riskLevel': 'MEDIUM',
            'reasoning': '技术指标显示上涨趋势，RSI未超买',
            'orderManagement': ['监控现有订单', '设置追踪止损'],
            'isFinalDecision': True
        }
        print(f"  ✅ 分析决策: {analysis_result['action']}")
        print(f"  ✅ 风险等级: {analysis_result['riskLevel']}")
        
        return True, analysis_result
        
    except Exception as e:
        print(f"  ❌ LLM分析失败: {e}")
        return False, None

def test_step_3_trade_execution():
    """步骤3: 测试交易执行"""
    print("\n💼 步骤3: 测试交易执行")
    
    try:
        # 3.1 风险管理检查
        print("  3.1 风险管理检查...")
        from app.core.risk_management import AdvancedRiskManager
        risk_manager = AdvancedRiskManager()
        
        # 模拟交易请求
        trade_request = {
            'symbol': 'EURUSD',
            'action': 'BUY',
            'lot_size': 0.05,
            'stop_loss': 1.1380,
            'take_profit': 1.1420
        }
        
        risk_check = risk_manager.evaluate_trade_risk(trade_request)
        print(f"  ✅ 风险评估: {risk_check}")
        
        # 3.2 订单执行（模拟）
        print("  3.2 订单执行...")
        from app.utils.mt4_client import MT4Client
        mt4_client = MT4Client()
        
        if mt4_client.connect():
            # 模拟下单（实际环境中会执行真实交易）
            print("  ⚠️ 模拟交易模式 - 不执行真实订单")
            order_result = {
                'success': True,
                'orderId': 'SIMULATED_123456',
                'message': '模拟订单创建成功'
            }
            print(f"  ✅ 订单结果: {order_result}")
        
        # 3.3 订单管理
        print("  3.3 订单管理...")
        from app.utils.execution_optimizer import ExecutionOptimizer
        optimizer = ExecutionOptimizer(mt4_client)
        
        # 模拟订单管理操作
        management_operations = ['监控现有订单', '设置追踪止损']
        print(f"  ✅ 管理操作: {management_operations}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 交易执行失败: {e}")
        return False

def test_step_4_monitoring():
    """步骤4: 测试监控系统"""
    print("\n📈 步骤4: 测试监控系统")
    
    try:
        # 4.1 实时监控
        print("  4.1 实时监控...")
        from app.utils.real_time_monitor import RealTimeMonitor
        monitor = RealTimeMonitor()
        
        # 获取系统状态
        status = monitor.get_current_status()
        print(f"  ✅ 监控状态: {status.get('monitoring_status', 'unknown')}")
        
        # 4.2 Token统计
        print("  4.2 Token统计...")
        from app.utils.token_statistics import get_token_summary
        token_summary = get_token_summary()
        print(f"  ✅ Token统计: {token_summary}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 监控系统失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 开始完整系统测试")
    print("=" * 60)
    
    # 步骤1: 数据收集
    success_1, data, mt4_client = test_step_1_data_collection()
    if not success_1:
        print("❌ 数据收集失败，终止测试")
        return
    
    # 步骤2: LLM分析
    success_2, analysis_result = test_step_2_llm_analysis()
    if not success_2:
        print("❌ LLM分析失败，终止测试")
        return
    
    # 步骤3: 交易执行
    success_3 = test_step_3_trade_execution()
    if not success_3:
        print("❌ 交易执行失败，终止测试")
        return
    
    # 步骤4: 监控系统
    success_4 = test_step_4_monitoring()
    
    print("\n" + "=" * 60)
    if all([success_1, success_2, success_3, success_4]):
        print("🎉 完整系统测试成功！所有功能正常")
    else:
        print("⚠️ 部分功能存在问题，需要进一步检查")
    
    print("\n📋 测试总结:")
    print(f"  数据收集: {'✅' if success_1 else '❌'}")
    print(f"  LLM分析: {'✅' if success_2 else '❌'}")
    print(f"  交易执行: {'✅' if success_3 else '❌'}")
    print(f"  监控系统: {'✅' if success_4 else '❌'}")

if __name__ == "__main__":
    main()
