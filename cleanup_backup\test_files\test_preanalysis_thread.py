"""
测试MT4连接失败后的预分析线程行为
"""
import time
import sys
from datetime import datetime, timedelta
import threading

from app.utils.forex_scheduled_tasks import start_realtime_forex_analysis, tasks, stop_all_tasks

if __name__ == "__main__":
    print("开始测试MT4连接失败后的预分析线程行为...")
    print("当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 确保输出立即显示
    sys.stdout.flush()
    
    # 启动实时外汇分析任务，不立即执行，这样就会启动预分析线程
    print("启动实时外汇分析任务...")
    start_realtime_forex_analysis(run_immediately=False, auto_trade=False, check_interval=10)
    
    print("实时外汇分析任务已启动，主线程等待300秒...")
    print("任务状态:", tasks)
    
    # 确保输出立即显示
    sys.stdout.flush()
    
    # 主线程等待300秒，每30秒输出一次状态
    for i in range(10):
        time.sleep(30)
        print(f"已等待 {(i+1)*30} 秒，任务状态: detector_running={tasks['realtime_analysis']['detector_running']}, running={tasks['realtime_analysis']['running']}")
        sys.stdout.flush()
    
    # 停止所有任务
    print("停止所有任务...")
    stop_all_tasks()
    
    print("测试完成，退出程序")
