#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的数据库检查脚本
"""

import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simple_db_check():
    """简单的数据库检查"""

    try:
        from app.utils.db_client import get_connection

        conn = get_connection()
        if not conn:
            print("❌ 无法连接到数据库")
            return

        cursor = conn.cursor()

        print("🔍 简单数据库检查...")
        print("=" * 40)

        # 显示所有表名
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()

        print(f"📋 找到 {len(tables)} 个表:")

        eurusd_tables = []
        for i, table in enumerate(tables):
            # 处理字典格式的返回值
            if isinstance(table, dict):
                table_name = list(table.values())[0]
            elif isinstance(table, (list, tuple)):
                table_name = table[0]
            else:
                table_name = str(table)

            print(f"{i+1:2d}. {table_name}")

            # 检查是否是EURUSD相关表
            if 'eurusd' in table_name.lower() or 'eur' in table_name.lower():
                eurusd_tables.append(table_name)

        print(f"\n⭐ 可能的EURUSD表 ({len(eurusd_tables)}个):")
        for table in eurusd_tables:
            print(f"   📄 {table}")

            try:
                # 检查表结构
                cursor.execute(f"DESCRIBE `{table}`")
                columns = cursor.fetchall()

                column_names = [col[0] for col in columns]
                print(f"      列: {column_names}")

                # 检查数据量
                cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                count = cursor.fetchone()[0]
                print(f"      数据量: {count} 条")

                # 如果有数据，显示最新一条 (尝试不同的时间列名)
                if count > 0:
                    time_columns = ['timestamp', 'time', 'datetime', 'date_time', 'created_at', 'updated_at']
                    time_col = None

                    for col in time_columns:
                        if col in column_names:
                            time_col = col
                            break

                    if time_col:
                        cursor.execute(f"SELECT * FROM `{table}` ORDER BY `{time_col}` DESC LIMIT 1")
                        latest = cursor.fetchone()
                        print(f"      最新 (按{time_col}排序): {latest}")
                    else:
                        cursor.execute(f"SELECT * FROM `{table}` LIMIT 1")
                        sample = cursor.fetchone()
                        print(f"      样本数据: {sample}")

            except Exception as e:
                print(f"      ❌ 错误: {e}")

            print()

        # 如果没有找到EURUSD表，显示所有表的详细信息
        if not eurusd_tables:
            print("\n🔍 没有找到EURUSD表，显示前10个表的详细信息:")
            for i, table in enumerate(tables[:10]):
                # 处理字典格式的返回值
                if isinstance(table, dict):
                    table_name = list(table.values())[0]
                elif isinstance(table, (list, tuple)):
                    table_name = table[0]
                else:
                    table_name = str(table)

                print(f"\n{i+1}. 表: {table_name}")

                try:
                    cursor.execute(f"DESCRIBE `{table_name}`")
                    columns = cursor.fetchall()
                    print(f"   列: {[col[0] for col in columns]}")

                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    count = cursor.fetchone()[0]
                    print(f"   数据量: {count}")

                except Exception as e:
                    print(f"   ❌ 错误: {e}")

        conn.close()

    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_db_check()
