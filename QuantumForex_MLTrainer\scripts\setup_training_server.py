#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantumForex MLTrainer 训练服务器设置脚本
一键设置训练服务器环境和与交易端的连接
"""

import os
import sys
import json
import logging
import subprocess
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.network_config import network_config
from utils.compatibility_manager import CompatibilityManager

def setup_logging():
    """设置日志"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    log_file = log_dir / f"setup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)

def check_python_environment():
    """检查Python环境"""
    logger = logging.getLogger(__name__)
    
    logger.info("🐍 检查Python环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        logger.error(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        logger.error("需要Python 3.8或更高版本")
        return False
    
    logger.info(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查pip
    try:
        import pip
        logger.info("✅ pip可用")
    except ImportError:
        logger.error("❌ pip不可用")
        return False
    
    return True

def install_dependencies():
    """安装依赖包"""
    logger = logging.getLogger(__name__)
    
    logger.info("📦 安装依赖包...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        logger.error("❌ requirements.txt文件不存在")
        return False
    
    try:
        # 升级pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True, text=True)
        logger.info("✅ pip已升级")
        
        # 安装依赖
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)], 
                               check=True, capture_output=True, text=True)
        logger.info("✅ 依赖包安装完成")
        
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ 依赖包安装失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return False

def create_shared_folders():
    """创建共享文件夹"""
    logger = logging.getLogger(__name__)
    
    logger.info("📁 创建共享文件夹...")
    
    shared_folder = Path(network_config.FILE_SHARE_CONFIG['shared_folder_local'])
    
    try:
        # 创建主共享文件夹
        shared_folder.mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ 主共享文件夹已创建: {shared_folder}")
        
        # 创建子文件夹
        for folder_name in network_config.FILE_SHARE_CONFIG['folders'].values():
            subfolder = shared_folder / folder_name
            subfolder.mkdir(exist_ok=True)
            logger.info(f"✅ 子文件夹已创建: {subfolder}")
        
        # 创建README文件
        readme_content = """# QuantumForex 共享文件夹

这个文件夹用于训练端和交易端之间的数据交换。

## 文件夹结构
- models/: 训练好的机器学习模型
- data/: 数据交换文件
- logs/: 日志文件
- config/: 配置文件
- backup/: 备份文件
- temp/: 临时文件

## 注意事项
- 请勿手动删除或修改模型文件
- 日志文件会自动轮转
- 备份文件定期清理

创建时间: {}
""".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        readme_file = shared_folder / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        logger.info("✅ README文件已创建")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建共享文件夹失败: {e}")
        return False

def setup_file_sharing():
    """设置文件共享"""
    logger = logging.getLogger(__name__)
    
    logger.info("🔗 设置文件共享...")
    
    shared_folder = Path(network_config.FILE_SHARE_CONFIG['shared_folder_local'])
    share_name = network_config.FILE_SHARE_CONFIG['shared_folder_name']
    
    try:
        # Windows文件共享设置
        # 注意：这需要管理员权限
        
        # 检查是否已经共享
        check_cmd = f'net share {share_name}'
        result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"✅ 文件夹已经共享: {share_name}")
        else:
            # 创建共享
            share_cmd = f'net share {share_name}="{shared_folder}" /grant:everyone,full'
            result = subprocess.run(share_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"✅ 文件共享已创建: {share_name}")
            else:
                logger.warning(f"⚠️ 文件共享创建失败，可能需要管理员权限")
                logger.warning("请手动设置文件夹共享:")
                logger.warning(f"1. 右键点击文件夹: {shared_folder}")
                logger.warning("2. 选择'属性' -> '共享' -> '高级共享'")
                logger.warning("3. 勾选'共享此文件夹'")
                logger.warning(f"4. 共享名设置为: {share_name}")
                logger.warning("5. 设置权限为'完全控制'")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 设置文件共享失败: {e}")
        return False

def test_network_connectivity():
    """测试网络连接"""
    logger = logging.getLogger(__name__)
    
    logger.info("🌐 测试网络连接...")
    
    # 测试本地网络
    try:
        import socket
        
        # 测试本机IP
        local_ip = network_config.NETWORK_CONFIG['training_server_ip']
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((local_ip, 80))
        sock.close()
        
        logger.info(f"✅ 本机IP可达: {local_ip}")
        
        # 测试网关
        gateway = network_config.NETWORK_CONFIG['gateway']
        result = subprocess.run(['ping', '-n', '1', gateway], 
                               capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"✅ 网关可达: {gateway}")
        else:
            logger.warning(f"⚠️ 网关不可达: {gateway}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 网络连接测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    logger = logging.getLogger(__name__)
    
    logger.info("🗄️ 测试数据库连接...")
    
    try:
        import pymysql
        
        db_config = network_config.DATABASE_CONFIG['pizza_quotes']
        
        connection = pymysql.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database=db_config['database'],
            charset=db_config['charset'],
            connect_timeout=10
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        
        connection.close()
        
        logger.info("✅ 数据库连接成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        logger.error("请检查网络连接和数据库配置")
        return False

def initialize_compatibility_system():
    """初始化兼容性系统"""
    logger = logging.getLogger(__name__)
    
    logger.info("🔧 初始化兼容性系统...")
    
    try:
        compatibility_manager = CompatibilityManager()
        success = compatibility_manager.initialize_compatibility_system()
        
        if success:
            logger.info("✅ 兼容性系统初始化成功")
            
            # 显示模型状态
            status = compatibility_manager.get_model_status()
            logger.info(f"📊 模型状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
        else:
            logger.error("❌ 兼容性系统初始化失败")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 兼容性系统初始化异常: {e}")
        return False

def create_startup_scripts():
    """创建启动脚本"""
    logger = logging.getLogger(__name__)
    
    logger.info("📝 创建启动脚本...")
    
    try:
        # 创建Windows批处理文件
        bat_content = f"""@echo off
echo 启动 QuantumForex MLTrainer...
cd /d "{Path.cwd()}"
python scripts/train_all_models.py
pause
"""
        
        bat_file = Path("start_training.bat")
        with open(bat_file, 'w', encoding='gbk') as f:
            f.write(bat_content)
        
        logger.info(f"✅ 启动脚本已创建: {bat_file}")
        
        # 创建Python启动脚本
        py_content = f"""#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import os
sys.path.append(r"{Path.cwd()}")

from scripts.train_all_models import main

if __name__ == "__main__":
    main()
"""
        
        py_file = Path("start_training.py")
        with open(py_file, 'w', encoding='utf-8') as f:
            f.write(py_content)
        
        logger.info(f"✅ Python启动脚本已创建: {py_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建启动脚本失败: {e}")
        return False

def main():
    """主设置流程"""
    logger = setup_logging()
    
    logger.info("🚀 QuantumForex MLTrainer 训练服务器设置")
    logger.info("=" * 60)
    
    setup_steps = [
        ("检查Python环境", check_python_environment),
        ("安装依赖包", install_dependencies),
        ("创建共享文件夹", create_shared_folders),
        ("设置文件共享", setup_file_sharing),
        ("测试网络连接", test_network_connectivity),
        ("测试数据库连接", test_database_connection),
        ("初始化兼容性系统", initialize_compatibility_system),
        ("创建启动脚本", create_startup_scripts)
    ]
    
    success_count = 0
    total_steps = len(setup_steps)
    
    for step_name, step_func in setup_steps:
        logger.info(f"\n🔄 执行步骤: {step_name}")
        try:
            if step_func():
                success_count += 1
                logger.info(f"✅ {step_name} - 完成")
            else:
                logger.error(f"❌ {step_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {step_name} - 异常: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 设置完成总结")
    logger.info("=" * 60)
    logger.info(f"✅ 成功步骤: {success_count}/{total_steps}")
    logger.info(f"📈 成功率: {success_count/total_steps:.1%}")
    
    if success_count == total_steps:
        logger.info("🎉 训练服务器设置完全成功！")
        logger.info("📋 下一步操作:")
        logger.info("1. 运行 start_training.bat 开始训练")
        logger.info("2. 检查共享文件夹中的模型文件")
        logger.info("3. 验证与交易端的连接")
        return True
    else:
        logger.warning("⚠️ 部分步骤失败，请检查错误信息并手动完成")
        logger.info("📋 可能需要的手动操作:")
        logger.info("1. 以管理员身份运行以设置文件共享")
        logger.info("2. 检查防火墙设置")
        logger.info("3. 验证网络配置")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按任意键退出...")
    sys.exit(0 if success else 1)
