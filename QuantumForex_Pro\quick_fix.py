#!/usr/bin/env python3
"""
QuantumForex Pro 快速修复脚本
解决服务器部署时的常见问题
"""

import os
import sys
import re
from datetime import datetime

def print_status(message, status="INFO"):
    """打印状态信息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    status_symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARNING": "⚠️"
    }
    symbol = status_symbols.get(status, "ℹ️")
    print(f"[{timestamp}] {symbol} {message}")

def find_python_files(directory):
    """查找所有Python文件"""
    python_files = []
    for root, dirs, files in os.walk(directory):
        # 跳过__pycache__目录
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files

def check_app_utils_imports(file_path):
    """检查文件中是否有app.utils的导入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找app.utils的导入
        app_utils_patterns = [
            r'from\s+app\.utils',
            r'import\s+app\.utils',
            r'app\.utils\.'
        ]
        
        found_issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            for pattern in app_utils_patterns:
                if re.search(pattern, line):
                    found_issues.append((i, line.strip()))
        
        return found_issues
    
    except Exception as e:
        print_status(f"读取文件失败 {file_path}: {e}", "ERROR")
        return []

def fix_app_utils_imports(file_path, issues):
    """修复app.utils的导入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换规则
        replacements = [
            (r'from\s+app\.utils\.', 'from utils.'),
            (r'import\s+app\.utils\.(\w+)', r'import utils.\1'),
            (r'app\.utils\.', 'utils.')
        ]
        
        original_content = content
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        if content != original_content:
            # 备份原文件
            backup_path = file_path + '.backup'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            # 写入修复后的内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print_status(f"修复完成: {file_path}", "SUCCESS")
            return True
        else:
            print_status(f"无需修复: {file_path}", "INFO")
            return False
    
    except Exception as e:
        print_status(f"修复文件失败 {file_path}: {e}", "ERROR")
        return False

def test_imports():
    """测试关键导入"""
    print_status("测试关键模块导入...")
    
    test_cases = [
        ("utils.db_client", "from utils.db_client import test_connection"),
        ("utils.mt4_client", "from utils.mt4_client import MT4Client"),
        ("utils.intelligent_pair_selector", "from utils.intelligent_pair_selector import IntelligentPairSelector")
    ]
    
    all_success = True
    
    for module_name, import_statement in test_cases:
        try:
            exec(import_statement)
            print_status(f"{module_name} 导入成功", "SUCCESS")
        except ImportError as e:
            print_status(f"{module_name} 导入失败: {e}", "ERROR")
            all_success = False
        except Exception as e:
            print_status(f"{module_name} 测试异常: {e}", "ERROR")
            all_success = False
    
    return all_success

def check_utils_directory():
    """检查utils目录是否存在"""
    utils_path = os.path.join(os.getcwd(), 'utils')
    
    if not os.path.exists(utils_path):
        print_status("utils目录不存在", "ERROR")
        return False
    
    required_files = ['__init__.py', 'db_client.py', 'mt4_client.py', 'intelligent_pair_selector.py']
    missing_files = []
    
    for file_name in required_files:
        file_path = os.path.join(utils_path, file_name)
        if not os.path.exists(file_path):
            missing_files.append(file_name)
    
    if missing_files:
        print_status(f"utils目录缺少文件: {', '.join(missing_files)}", "ERROR")
        return False
    
    print_status("utils目录检查通过", "SUCCESS")
    return True

def main():
    """主函数"""
    print_status("=" * 60)
    print_status("QuantumForex Pro 快速修复脚本")
    print_status("=" * 60)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print_status(f"当前目录: {current_dir}")
    
    # 检查utils目录
    if not check_utils_directory():
        print_status("请确保在QuantumForex_Pro目录中运行此脚本", "ERROR")
        sys.exit(1)
    
    # 查找所有Python文件
    print_status("扫描Python文件...")
    python_files = find_python_files(current_dir)
    print_status(f"找到 {len(python_files)} 个Python文件")
    
    # 检查app.utils导入问题
    total_issues = 0
    files_with_issues = []
    
    for file_path in python_files:
        issues = check_app_utils_imports(file_path)
        if issues:
            total_issues += len(issues)
            files_with_issues.append((file_path, issues))
            print_status(f"发现问题: {file_path} ({len(issues)}个)", "WARNING")
            for line_num, line_content in issues:
                print_status(f"  第{line_num}行: {line_content}", "WARNING")
    
    if total_issues == 0:
        print_status("未发现app.utils导入问题", "SUCCESS")
    else:
        print_status(f"总共发现 {total_issues} 个app.utils导入问题", "WARNING")
        
        # 询问是否修复
        response = input("是否自动修复这些问题? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            fixed_count = 0
            for file_path, issues in files_with_issues:
                if fix_app_utils_imports(file_path, issues):
                    fixed_count += 1
            
            print_status(f"修复完成: {fixed_count}/{len(files_with_issues)} 个文件", "SUCCESS")
        else:
            print_status("跳过自动修复", "INFO")
    
    # 测试导入
    print_status("测试模块导入...")
    if test_imports():
        print_status("所有关键模块导入成功", "SUCCESS")
    else:
        print_status("模块导入测试失败", "ERROR")
        print_status("请检查依赖是否正确安装", "INFO")
        print_status("运行: python install_dependencies.py", "INFO")
    
    print_status("=" * 60)
    print_status("快速修复完成", "SUCCESS")
    print_status("=" * 60)

if __name__ == "__main__":
    main()
