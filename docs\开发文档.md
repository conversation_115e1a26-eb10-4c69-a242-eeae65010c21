# 外汇交易系统开发文档

## 系统概述

外汇交易系统是一个基于Python的自动化交易系统，使用大语言模型（LLM）进行市场分析和交易决策。系统通过MT4客户端执行交易，支持EURUSD货币对的实时分析和自动交易。

## 系统架构

系统由以下主要模块组成：

1. **数据获取模块**：从数据库获取历史数据，从MT4获取实时市场数据
2. **分析模块**：使用多轮分析方法，通过LLM进行市场分析和交易决策
3. **交易执行模块**：通过MT4客户端执行交易指令
4. **历史记录模块**：保存分析结果和交易记录
5. **统计评估模块**：评估交易结果和系统性能
6. **Token统计模块**：统计LLM调用的token消耗和成本

## 主要功能

1. **实时市场分析**：定期分析EURUSD市场，生成交易建议
2. **自动交易执行**：根据分析结果自动执行交易
3. **订单管理**：管理现有订单，包括修改和关闭
4. **性能评估**：评估交易结果和系统性能
5. **Token消耗统计**：统计和监控LLM调用的token消耗和成本

## 关键模块说明

### 1. 多轮分析模块

多轮分析模块使用三轮分析方法，通过LLM进行市场分析和交易决策：

1. **预分析**：判断是否需要执行完整分析，设置下次分析间隔
2. **初始分析**：进行基础技术分析和基本面分析
3. **深入分析**：基于初始分析结果进行深入分析
4. **最终决策**：综合前两轮分析结果，做出最终交易决策

### 2. MT4客户端模块

MT4客户端模块负责与MT4服务器通信，执行交易指令：

1. **连接管理**：管理与MT4服务器的连接
2. **交易执行**：执行交易指令，包括开仓、平仓、修改和删除订单
3. **市场数据获取**：获取实时市场数据和账户信息

### 3. 数据处理模块

数据处理模块负责处理和聚合市场数据：

1. **数据聚合**：将1分钟数据聚合为15分钟和1小时数据
2. **技术指标计算**：计算各种技术指标，如MA、RSI、MACD等
3. **数据格式化**：将数据格式化为LLM可以理解的格式

### 4. Token统计模块

Token统计模块负责统计和监控LLM调用的token消耗和成本：

1. **Token记录**：记录每次LLM调用的token消耗，包括提示词token和生成token
2. **成本计算**：根据不同模型的价格计算token消耗成本，R1模型16元/百万token，V3模型8元/百万token
3. **统计分析**：按小时、按日、按模型和按分析类型统计token消耗
4. **报告生成**：生成token统计报告和图表，保存在`app/data/`目录下
5. **实时监控**：在每次预分析和完整分析后显示当前小时和累计token消耗

## 最近更新

### 2025-05-24 LLM Token统计功能

1. **Token统计模块**：
   - 开发了新的token统计模块`app/utils/token_statistics.py`，用于跟踪LLM调用的token消耗
   - 实现了按小时、按日、按模型和按分析类型的token统计功能
   - 支持R1模型(16元/百万token)和V3模型(8元/百万token)的成本计算
   - 在每次预分析后自动生成token统计报告，显示当前小时和累计消耗
   - 在完整分析结束后也会生成token统计，包括完整分析的平均token消耗
   - 自动生成token统计图表，保存在`app/data/charts/`目录下
   - 生成详细的token统计报告，保存在`app/data/token_report.txt`

2. **Token消耗监控**：
   - 修改了LLM客户端，自动记录每次调用的token消耗
   - 区分预分析和完整分析的token消耗，便于分析系统效率
   - 实时显示每小时token消耗和成本，便于监控系统运行成本
   - 支持阶段性统计，可查看任意时间段的token消耗情况
   - 添加了token消耗趋势图表，便于分析长期使用成本

### 2025-05-23 13日均线计算验证

1. **13日均线计算验证**：
   - 开发了自动化测试脚本`test_ma13_calculation.py`，用于验证13日均线计算的准确性
   - 测试脚本能够自动从MT4服务器获取实时市场数据，并与我们的计算结果进行比较
   - 测试结果表明，我们的13日均线计算与MT4的计算结果完全一致，最大差异为0
   - 测试覆盖了M15和H1两个时间周期，确保在不同时间框架下计算结果都是准确的
   - 测试脚本支持自动保存测试数据到JSON文件，便于后续分析和调试
   - 添加了详细的测试日志输出，包括平均差异、最大差异和有效比较点数量等信息

### 2023-05-17 预分析触发优化

1. **预分析重复触发问题修复**：
   - 添加了对上次分析结果的比较机制，避免重复触发完整分析
   - 当市场状况与上次分析相似且上次建议为观望时，跳过完整分析
   - 在LLM提示词中添加了上次分析结果信息，使LLM能够考虑历史决策
   - 添加了"考虑上次分析结果"的决策标准，引导LLM更智能地判断是否需要新分析
   - 优化了日志输出，添加与上次分析比较的信息

2. **阈值和触发条件优化**：
   - 进一步提高了触发完整分析的阈值，减少不必要的分析
   - 短期价格变化阈值从0.3%提高到0.5%
   - 中期价格变化阈值从0.5%提高到0.8%
   - 价格波动性阈值从1.0%提高到1.5%
   - RSI极值区域从80/20缩小到85/15
   - 添加了持仓风险检测，当价格接近止损或止盈点时触发分析

3. **持仓定期评估优化**：
   - 将持仓定期评估频率从每小时一次降低到每2小时一次
   - 只在活跃交易时段的整点小时进行定期评估
   - 添加了对上次分析时间的检查，如果上次分析在1小时内，跳过定期评估
   - 将定期评估的间隔从60分钟延长到120分钟
   - 优化了日志输出，添加更详细的评估决策信息

### 2023-05-16 预分析灵活性优化

1. **预分析LLM提示词优化**：
   - 重新设计了预分析的LLM提示词，使其更加灵活地判断市场波动情况
   - 添加了更详细的市场数据分析指导，帮助LLM做出更准确的判断
   - 引导LLM综合考虑多种因素，而不仅仅依赖固定阈值
   - 添加了市场环境、持仓管理需求等更全面的考量因素
   - 增加了信心度评估机制，让LLM表明对决策的确信程度

2. **预分析决策逻辑优化**：
   - 添加了信心度评估机制，当LLM信心度低于50%时重新评估决策
   - 在信心度低的情况下，系统更倾向于进行完整分析，避免错过交易机会
   - 保留了基于固定阈值的备用判断机制，作为安全保障
   - 优化了日志输出，添加信心度相关信息，便于监控和调试
   - 改进了预分析结果的解析逻辑，更好地处理各种响应格式

### 2023-05-15 系统优化

1. **MT4响应解析问题修复**：
   - 改进了`get_active_orders`和`get_pending_orders`函数的JSON解析逻辑
   - 添加了标准JSON解析尝试，失败后才使用正则表达式提取
   - 优化了正则表达式模式，使其更加健壮
   - 添加了更详细的日志输出，便于排查问题
   - 统一了日志格式，添加了时间戳

2. **预分析与完整分析互斥关系优化**：
   - 修复了预分析触发完整分析时的逻辑问题
   - 确保强制分析时完全跳过预分析步骤
   - 修改了`forex_scheduled_tasks.py`中的预分析回调逻辑，使用强制模式避免重复预分析
   - 优化了`perform_multi_round_analysis`函数中的日志输出，添加更详细的状态信息
   - 修复了`forex_trading_service.py`中检查预分析结果的条件判断

3. **日志输出优化**：
   - 添加了更多时间戳日志，便于跟踪执行流程
   - 统一了日志格式，使用`[时间戳] >>> 步骤X: XXX`格式
   - 添加了更详细的错误处理和日志记录

### 2023-05-10 系统优化

1. **强制分析逻辑优化**：
   - 修改了`perform_multi_round_analysis`函数，使强制分析完全跳过预分析步骤
   - 确保在首次分析或强制分析时，直接执行完整分析，不受预分析结果影响

2. **MT4连接问题修复**：
   - 添加了连接锁超时机制，防止连接锁长时间不释放
   - 改进了连接状态检查，确保连接真正建立后才标记为已连接
   - 添加了连接ID，便于跟踪和调试连接问题
   - 在连接失败时立即释放连接锁，避免阻塞后续连接请求
   - 添加了更详细的错误日志，便于排查连接问题

3. **日志输出和执行步骤优化**：
   - 使用统一的日志格式，包括时间戳和日志级别
   - 为每个主要步骤添加开始和结束日志，使用`===== 步骤X: XXX =====`格式标记步骤
   - 使用`>>>`表示正常信息，`!!!`表示警告或错误信息，便于区分
   - 添加了执行时间统计，记录每个主要操作的执行时间
   - 添加了更多上下文信息，如操作类型、参数和结果

## 使用说明

### 启动系统

```bash
python run.py  # 启动完整系统
python run_realtime_analysis.py  # 仅启动实时分析模块
```

### 配置说明

系统配置文件位于`config`目录下，主要包括：

1. **database_config.py**：数据库配置
2. **mt4_config.py**：MT4服务器配置
3. **llm_config.py**：LLM配置
4. **system_config.py**：系统配置

## 注意事项

1. 确保MT4服务器已启动并正确配置
2. 确保数据库连接正常
3. 确保LLM API密钥有效
4. 系统启动后会自动执行分析和交易，请谨慎使用

## 未来计划

1. 添加更多货币对支持
2. 改进风险管理策略
3. 添加更多技术指标和分析方法
4. 优化LLM提示词，提高分析准确性
5. 添加Web界面，便于监控和管理
