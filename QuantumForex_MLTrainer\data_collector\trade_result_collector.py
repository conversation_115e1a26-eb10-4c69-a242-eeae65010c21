"""
QuantumForex MLTrainer 交易结果收集器
收集交易端的历史交易结果用于模型训练和评估
"""

import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from config.training_config import training_config

class TradeResultCollector:
    """交易结果收集器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 数据配置
        self.data_config = training_config.DATA_CONFIG
        
        # 可能的交易结果文件路径
        self.trading_paths = [
            Path(r'../QuantumForex_Pro/logs'),
            Path(r'../QuantumForex_Pro/data/trading_results'),
            Path(r'C:\QuantumForex_Pro\logs'),
            Path(r'C:\QuantumForex_Pro\data\trading_results'),
        ]
        
        # 本地存储路径
        self.local_data_path = Path('data/trading_results')
        self.local_data_path.mkdir(parents=True, exist_ok=True)
    
    def collect_trading_logs(self, days: int = None) -> pd.DataFrame:
        """收集交易日志数据"""
        try:
            if days is None:
                days = self.data_config['data_range']['lookback_days']
            
            self.logger.info(f"📊 收集交易日志: {days}天")
            
            all_trades = []
            
            # 搜索交易日志文件
            for trading_path in self.trading_paths:
                if trading_path.exists():
                    trades = self._parse_log_files(trading_path, days)
                    all_trades.extend(trades)
            
            if not all_trades:
                self.logger.warning("⚠️ 没有找到交易日志数据")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(all_trades)
            
            # 数据预处理
            df = self._preprocess_trading_data(df)
            
            # 数据去重和排序
            df = self._clean_trading_data(df)
            
            self.logger.info(f"✅ 交易日志收集完成: {len(df)}条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 收集交易日志失败: {e}")
            return pd.DataFrame()
    
    def _parse_log_files(self, log_path: Path, days: int) -> List[Dict]:
        """解析日志文件"""
        try:
            trades = []
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # 查找日志文件
            log_files = []
            
            # 查找不同格式的日志文件
            patterns = ['*.log', '*.json', '*trading*.txt', '*trade*.csv']
            for pattern in patterns:
                log_files.extend(log_path.glob(pattern))
            
            for log_file in log_files:
                try:
                    # 检查文件修改时间
                    file_mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if file_mtime < cutoff_date:
                        continue
                    
                    # 根据文件类型解析
                    if log_file.suffix == '.json':
                        file_trades = self._parse_json_log(log_file)
                    elif log_file.suffix == '.csv':
                        file_trades = self._parse_csv_log(log_file)
                    else:
                        file_trades = self._parse_text_log(log_file)
                    
                    trades.extend(file_trades)
                    
                except Exception as e:
                    self.logger.warning(f"解析日志文件失败 {log_file}: {e}")
            
            return trades
            
        except Exception as e:
            self.logger.error(f"解析日志文件失败: {e}")
            return []
    
    def _parse_json_log(self, log_file: Path) -> List[Dict]:
        """解析JSON格式日志"""
        try:
            trades = []
            
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        
                        # 提取交易信息
                        if self._is_trade_record(data):
                            trade = self._extract_trade_info(data)
                            if trade:
                                trades.append(trade)
                                
                    except json.JSONDecodeError:
                        continue
            
            return trades
            
        except Exception as e:
            self.logger.error(f"解析JSON日志失败 {log_file}: {e}")
            return []
    
    def _parse_csv_log(self, log_file: Path) -> List[Dict]:
        """解析CSV格式日志"""
        try:
            df = pd.read_csv(log_file)
            trades = []
            
            for _, row in df.iterrows():
                trade = self._extract_trade_from_row(row)
                if trade:
                    trades.append(trade)
            
            return trades
            
        except Exception as e:
            self.logger.error(f"解析CSV日志失败 {log_file}: {e}")
            return []
    
    def _parse_text_log(self, log_file: Path) -> List[Dict]:
        """解析文本格式日志"""
        try:
            trades = []
            
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    trade = self._extract_trade_from_text(line.strip())
                    if trade:
                        trades.append(trade)
            
            return trades
            
        except Exception as e:
            self.logger.error(f"解析文本日志失败 {log_file}: {e}")
            return []
    
    def _is_trade_record(self, data: Dict) -> bool:
        """判断是否为交易记录"""
        trade_keywords = ['trade', 'order', 'position', 'buy', 'sell', 'profit', 'loss']
        
        # 检查数据中是否包含交易相关字段
        data_str = str(data).lower()
        return any(keyword in data_str for keyword in trade_keywords)
    
    def _extract_trade_info(self, data: Dict) -> Optional[Dict]:
        """从数据中提取交易信息"""
        try:
            trade = {}
            
            # 提取基本信息
            trade['timestamp'] = self._extract_timestamp(data)
            trade['symbol'] = self._extract_symbol(data)
            trade['action'] = self._extract_action(data)
            trade['volume'] = self._extract_volume(data)
            trade['price'] = self._extract_price(data)
            trade['profit'] = self._extract_profit(data)
            trade['strategy'] = self._extract_strategy(data)
            
            # 验证必要字段
            if trade['timestamp'] and trade['symbol']:
                return trade
            else:
                return None
                
        except Exception as e:
            self.logger.debug(f"提取交易信息失败: {e}")
            return None
    
    def _extract_timestamp(self, data: Dict) -> Optional[datetime]:
        """提取时间戳"""
        timestamp_fields = ['timestamp', 'time', 'datetime', 'date']
        
        for field in timestamp_fields:
            if field in data:
                try:
                    return pd.to_datetime(data[field])
                except:
                    continue
        
        return None
    
    def _extract_symbol(self, data: Dict) -> Optional[str]:
        """提取货币对"""
        symbol_fields = ['symbol', 'pair', 'currency', 'instrument']
        
        for field in symbol_fields:
            if field in data:
                return str(data[field])
        
        return None
    
    def _extract_action(self, data: Dict) -> Optional[str]:
        """提取交易动作"""
        action_fields = ['action', 'type', 'side', 'direction']
        
        for field in action_fields:
            if field in data:
                action = str(data[field]).lower()
                if 'buy' in action or 'long' in action:
                    return 'BUY'
                elif 'sell' in action or 'short' in action:
                    return 'SELL'
        
        return None
    
    def _extract_volume(self, data: Dict) -> Optional[float]:
        """提取交易量"""
        volume_fields = ['volume', 'size', 'lot', 'amount']
        
        for field in volume_fields:
            if field in data:
                try:
                    return float(data[field])
                except:
                    continue
        
        return None
    
    def _extract_price(self, data: Dict) -> Optional[float]:
        """提取价格"""
        price_fields = ['price', 'entry_price', 'open_price', 'execution_price']
        
        for field in price_fields:
            if field in data:
                try:
                    return float(data[field])
                except:
                    continue
        
        return None
    
    def _extract_profit(self, data: Dict) -> Optional[float]:
        """提取盈亏"""
        profit_fields = ['profit', 'pnl', 'gain', 'loss', 'result']
        
        for field in profit_fields:
            if field in data:
                try:
                    return float(data[field])
                except:
                    continue
        
        return None
    
    def _extract_strategy(self, data: Dict) -> Optional[str]:
        """提取策略名称"""
        strategy_fields = ['strategy', 'model', 'algorithm', 'method']
        
        for field in strategy_fields:
            if field in data:
                return str(data[field])
        
        return 'unknown'
    
    def _extract_trade_from_text(self, line: str) -> Optional[Dict]:
        """从文本行提取交易信息"""
        try:
            # 简单的文本解析逻辑
            if not any(keyword in line.lower() for keyword in ['trade', 'buy', 'sell', 'order']):
                return None
            
            trade = {
                'timestamp': datetime.now(),  # 默认当前时间
                'symbol': 'UNKNOWN',
                'action': None,
                'volume': None,
                'price': None,
                'profit': None,
                'strategy': 'text_log',
                'raw_text': line
            }
            
            # 尝试提取货币对
            for pair in ['EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCHF', 'USDCAD', 'USDJPY']:
                if pair in line.upper():
                    trade['symbol'] = pair
                    break
            
            # 尝试提取动作
            if 'buy' in line.lower() or 'long' in line.lower():
                trade['action'] = 'BUY'
            elif 'sell' in line.lower() or 'short' in line.lower():
                trade['action'] = 'SELL'
            
            return trade if trade['symbol'] != 'UNKNOWN' else None
            
        except Exception as e:
            self.logger.debug(f"从文本提取交易信息失败: {e}")
            return None
    
    def _extract_trade_from_row(self, row: pd.Series) -> Optional[Dict]:
        """从CSV行提取交易信息"""
        try:
            trade = {}
            
            # 映射常见的CSV列名
            column_mapping = {
                'timestamp': ['timestamp', 'time', 'datetime', 'date'],
                'symbol': ['symbol', 'pair', 'currency', 'instrument'],
                'action': ['action', 'type', 'side', 'direction'],
                'volume': ['volume', 'size', 'lot', 'amount'],
                'price': ['price', 'entry_price', 'open_price'],
                'profit': ['profit', 'pnl', 'gain', 'loss']
            }
            
            for trade_field, possible_columns in column_mapping.items():
                for col in possible_columns:
                    if col in row.index and pd.notna(row[col]):
                        if trade_field == 'timestamp':
                            trade[trade_field] = pd.to_datetime(row[col])
                        elif trade_field in ['volume', 'price', 'profit']:
                            trade[trade_field] = float(row[col])
                        else:
                            trade[trade_field] = str(row[col])
                        break
            
            return trade if 'symbol' in trade else None
            
        except Exception as e:
            self.logger.debug(f"从CSV行提取交易信息失败: {e}")
            return None
    
    def _preprocess_trading_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """预处理交易数据"""
        try:
            # 确保时间戳格式正确
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
                df = df.dropna(subset=['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            # 标准化数值字段
            numeric_fields = ['volume', 'price', 'profit']
            for field in numeric_fields:
                if field in df.columns:
                    df[field] = pd.to_numeric(df[field], errors='coerce')
            
            # 标准化动作字段
            if 'action' in df.columns:
                df['action'] = df['action'].str.upper()
            
            return df
            
        except Exception as e:
            self.logger.error(f"预处理交易数据失败: {e}")
            return df
    
    def _clean_trading_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理交易数据"""
        try:
            # 删除重复记录
            df = df.drop_duplicates()
            
            # 按时间排序
            df.sort_index(inplace=True)
            
            # 删除明显错误的数据
            if 'volume' in df.columns:
                df = df[df['volume'] > 0]
            
            if 'price' in df.columns:
                df = df[df['price'] > 0]
            
            return df
            
        except Exception as e:
            self.logger.error(f"清理交易数据失败: {e}")
            return df
    
    def save_trading_data(self, df: pd.DataFrame, filename: str = None) -> bool:
        """保存交易数据"""
        try:
            if filename is None:
                filename = f"trading_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            file_path = self.local_data_path / filename
            df.to_csv(file_path)
            
            self.logger.info(f"✅ 交易数据已保存: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 保存交易数据失败: {e}")
            return False
    
    def load_trading_data(self, filename: str) -> pd.DataFrame:
        """加载交易数据"""
        try:
            file_path = self.local_data_path / filename
            
            if file_path.exists():
                df = pd.read_csv(file_path, index_col=0, parse_dates=True)
                self.logger.info(f"✅ 交易数据已加载: {file_path}")
                return df
            else:
                self.logger.warning(f"⚠️ 文件不存在: {file_path}")
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"❌ 加载交易数据失败: {e}")
            return pd.DataFrame()
    
    def get_trading_summary(self, df: pd.DataFrame = None) -> Dict:
        """获取交易摘要"""
        try:
            if df is None:
                df = self.collect_trading_logs()
            
            if df.empty:
                return {'error': '没有交易数据'}
            
            summary = {
                'total_trades': len(df),
                'date_range': {
                    'start': df.index.min().isoformat() if not df.empty else None,
                    'end': df.index.max().isoformat() if not df.empty else None
                },
                'symbols': df['symbol'].value_counts().to_dict() if 'symbol' in df.columns else {},
                'actions': df['action'].value_counts().to_dict() if 'action' in df.columns else {},
                'total_profit': df['profit'].sum() if 'profit' in df.columns else 0,
                'avg_profit': df['profit'].mean() if 'profit' in df.columns else 0,
                'win_rate': (df['profit'] > 0).mean() if 'profit' in df.columns else 0
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"❌ 获取交易摘要失败: {e}")
            return {'error': str(e)}

# 创建全局实例
trade_collector = TradeResultCollector()
