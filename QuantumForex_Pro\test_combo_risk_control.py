#!/usr/bin/env python3
"""
QuantumForex Pro - 组合级别风控系统测试
验证组合交易的风险控制和持仓管理功能
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_combo_risk_manager():
    """测试组合风险管理器"""
    print("🔍 测试组合风险管理器...")
    print("=" * 60)
    
    try:
        from core.portfolio_manager.combo_risk_manager import ComboRiskManager, ComboPosition, ComboRiskLevel
        from core.portfolio_manager.combo_trading_manager import ComboTrade, ComboType
        
        # 创建风险管理器
        risk_manager = ComboRiskManager()
        
        # 创建测试组合
        test_combo = ComboTrade(
            combo_id="TEST_HEDGE_001",
            combo_type=ComboType.HEDGE_PAIR,
            symbols=["GBPUSD", "USDCHF"],
            directions=["long", "long"],
            position_sizes=[0.03, 0.03],
            entry_times=[datetime.now(), datetime.now()],
            target_correlation=-0.5,
            risk_level="low",
            description="测试对冲组合"
        )
        
        # 注册组合
        success = risk_manager.register_combo(
            combo_trade=test_combo,
            order_ids=["12345", "12346"],
            entry_prices=[1.2500, 0.9200]
        )
        
        print(f"✅ 组合注册: {'成功' if success else '失败'}")
        
        # 模拟持仓更新
        mock_positions = [
            {
                'order_id': '12345',
                'symbol': 'GBPUSD',
                'current_price': 1.2480,
                'profit': -6.0
            },
            {
                'order_id': '12346',
                'symbol': 'USDCHF',
                'current_price': 0.9220,
                'profit': 6.5
            }
        ]
        
        risk_manager.update_combo_positions(mock_positions)
        print("✅ 持仓更新完成")
        
        # 评估风险
        risk_assessments = risk_manager.assess_combo_risks()
        
        print(f"\n📊 风险评估结果:")
        for assessment in risk_assessments:
            print(f"   组合ID: {assessment.combo_id}")
            print(f"   风险等级: {assessment.risk_level.value}")
            print(f"   风险评分: {assessment.risk_score:.2f}")
            print(f"   总盈亏: ${assessment.total_profit:.2f}")
            print(f"   建议操作: {assessment.recommended_action.value}")
            print(f"   优先级: {assessment.action_priority}")
            print(f"   原因: {assessment.reason}")
        
        # 获取摘要
        summary = risk_manager.get_combo_summary()
        print(f"\n📋 组合摘要:")
        print(f"   活跃组合: {summary.get('active_combos', 0)}")
        print(f"   总盈亏: ${summary.get('total_profit', 0):.2f}")
        print(f"   USD敞口: {summary.get('total_usd_exposure', 0):.3f}")
        print(f"   风险状态: {summary.get('risk_status', 'UNKNOWN')}")
        
        return len(risk_assessments) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_combo_position_manager():
    """测试组合持仓管理器"""
    print("\n🔍 测试组合持仓管理器...")
    print("=" * 60)
    
    try:
        from core.portfolio_manager.combo_risk_manager import ComboRiskManager
        from core.portfolio_manager.combo_position_manager import create_combo_position_manager
        
        # 创建模拟交易执行器
        class MockTradeExecutor:
            def _get_mt4_real_positions(self):
                return [
                    {
                        'order_id': '12345',
                        'symbol': 'GBPUSD',
                        'action': 'BUY',
                        'volume': 0.03,
                        'entry_price': 1.2500,
                        'current_price': 1.2480,
                        'profit': -6.0,
                        'stop_loss': 1.2450,
                        'take_profit': 1.2550
                    },
                    {
                        'order_id': '12346',
                        'symbol': 'USDCHF',
                        'action': 'BUY',
                        'volume': 0.03,
                        'entry_price': 0.9200,
                        'current_price': 0.9220,
                        'profit': 6.5,
                        'stop_loss': 0.9150,
                        'take_profit': 0.9250
                    }
                ]
            
            def close_position(self, order_id):
                print(f"🔴 模拟平仓: {order_id}")
                return {'success': True}
            
            def modify_position(self, order_id, stop_loss=None, take_profit=None):
                print(f"🔧 模拟修改: {order_id}, SL={stop_loss}, TP={take_profit}")
                return {'success': True}
        
        # 创建管理器
        risk_manager = ComboRiskManager()
        mock_executor = MockTradeExecutor()
        position_manager = create_combo_position_manager(mock_executor, risk_manager)
        
        # 先注册一个测试组合
        from core.portfolio_manager.combo_trading_manager import ComboTrade, ComboType
        test_combo = ComboTrade(
            combo_id="TEST_HEDGE_002",
            combo_type=ComboType.HEDGE_PAIR,
            symbols=["GBPUSD", "USDCHF"],
            directions=["long", "long"],
            position_sizes=[0.03, 0.03],
            entry_times=[datetime.now() - timedelta(hours=2), datetime.now() - timedelta(hours=2)],
            target_correlation=-0.5,
            risk_level="medium",
            description="测试持仓管理组合"
        )
        
        risk_manager.register_combo(
            combo_trade=test_combo,
            order_ids=["12345", "12346"],
            entry_prices=[1.2500, 0.9200]
        )
        
        # 分析组合持仓
        risk_assessments = position_manager.analyze_combo_positions()
        
        print(f"📊 持仓分析结果: {len(risk_assessments)}个组合")
        
        # 执行组合操作
        if risk_assessments:
            execution_summary = position_manager.execute_combo_actions(risk_assessments)
            
            print(f"\n🎯 执行摘要:")
            print(f"   总评估: {execution_summary.get('total_assessments', 0)}")
            print(f"   执行操作: {execution_summary.get('executed_actions', 0)}")
            print(f"   失败操作: {execution_summary.get('failed_actions', 0)}")
            
            # 显示执行结果
            for result in execution_summary.get('results', []):
                print(f"\n📋 操作结果:")
                print(f"   组合ID: {result.combo_id}")
                print(f"   操作: {result.action}")
                print(f"   成功: {'是' if result.success else '否'}")
                print(f"   执行订单: {result.executed_orders}")
                print(f"   失败订单: {result.failed_orders}")
                print(f"   原因: {result.reason}")
        
        # 获取状态摘要
        status_summary = position_manager.get_combo_status_summary()
        print(f"\n📈 状态摘要:")
        for key, value in status_summary.items():
            print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_combo_execution_integration():
    """测试组合执行集成"""
    print("\n🔍 测试组合执行集成...")
    print("=" * 60)
    
    try:
        from core.execution_engine.trade_executor import TradeExecutor
        from core.portfolio_manager.combo_trading_manager import ComboTrade, ComboType
        
        # 创建交易执行器（模拟模式）
        executor = TradeExecutor()
        
        # 创建测试组合
        test_combo = ComboTrade(
            combo_id="INTEGRATION_TEST_001",
            combo_type=ComboType.HEDGE_PAIR,
            symbols=["EURUSD", "USDCHF"],
            directions=["long", "long"],
            position_sizes=[0.02, 0.02],
            entry_times=[datetime.now(), datetime.now()],
            target_correlation=-0.5,
            risk_level="low",
            description="集成测试组合"
        )
        
        print("🎯 测试组合交易执行...")
        
        # 执行组合交易
        result = executor.execute_combo_trade(test_combo)
        
        print(f"📊 执行结果:")
        print(f"   成功: {'是' if result.get('success', False) else '否'}")
        print(f"   组合ID: {result.get('combo_id', 'N/A')}")
        print(f"   消息: {result.get('message', 'N/A')}")
        print(f"   执行订单: {result.get('executed_orders', [])}")
        print(f"   失败订单: {result.get('failed_orders', [])}")
        
        # 测试组合级别的风控
        if executor.combo_position_manager:
            print("\n🛡️ 测试组合级别风控...")
            
            # 分析组合持仓
            risk_assessments = executor.combo_position_manager.analyze_combo_positions()
            print(f"   风险评估: {len(risk_assessments)}个组合")
            
            # 执行风控操作
            if risk_assessments:
                execution_summary = executor.combo_position_manager.execute_combo_actions(risk_assessments)
                print(f"   风控操作: {execution_summary.get('executed_actions', 0)}个执行")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_combo_vs_individual_risk_control():
    """测试组合vs单独风控的区别"""
    print("\n🔍 测试组合vs单独风控的区别...")
    print("=" * 60)
    
    print("📚 组合级别风控的优势:")
    print("1. 🔗 整体性管理：将相关交易作为一个整体进行风险评估")
    print("2. 🎯 协调操作：同时调整组合中的所有订单，保持策略完整性")
    print("3. 💰 净敞口控制：考虑组合内部的对冲效果，计算真实风险敞口")
    print("4. ⏰ 时间一致性：组合中的所有交易具有相同的生命周期管理")
    print("5. 📊 统一评估：使用组合级别的风险指标，而非单个订单指标")
    
    print("\n❌ 单独风控的问题:")
    print("1. 🔀 割裂管理：对每个订单独立进行风控，忽略组合关系")
    print("2. ⚡ 不协调操作：可能只平仓组合中的部分订单，破坏策略完整性")
    print("3. 📈 重复计算风险：没有考虑对冲效果，可能高估实际风险")
    print("4. 🕐 时间不一致：不同订单可能在不同时间被风控系统处理")
    print("5. 📉 策略失效：部分平仓可能导致原本的对冲策略失效")
    
    print("\n🎯 实现的改进:")
    print("✅ 组合注册：交易执行时自动注册到组合风险管理器")
    print("✅ 组合模式：execute_trade支持combo_mode，跳过单独风控")
    print("✅ 整体评估：ComboRiskManager评估整个组合的风险状态")
    print("✅ 协调操作：ComboPositionManager同时操作组合中的所有订单")
    print("✅ 净敞口计算：准确计算USD等基础货币的净敞口")
    
    return True

if __name__ == "__main__":
    print("🚀 QuantumForex Pro - 组合级别风控系统测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行所有测试
    tests = [
        ("组合风险管理器", test_combo_risk_manager),
        ("组合持仓管理器", test_combo_position_manager),
        ("组合执行集成", test_combo_execution_integration),
        ("组合vs单独风控", test_combo_vs_individual_risk_control)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"🔄 开始执行: {test_name}")
            print(f"{'='*60}")
            
            results[test_name] = test_func()
            
            if results[test_name]:
                print(f"✅ {test_name} - 测试通过")
            else:
                print(f"❌ {test_name} - 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("📊 组合级别风控系统测试结果汇总:")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 组合级别风控系统测试全部通过！")
        print("✅ 系统现在支持组合级别的风险控制和持仓管理")
        print("✅ 组合交易将作为整体进行风控，不再单独处理")
        print("🚀 可以安全地使用组合交易功能")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    sys.exit(0 if all_passed else 1)
