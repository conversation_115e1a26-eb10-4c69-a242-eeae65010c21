"""
测试市场变化分析线程
"""
import time
from datetime import datetime, timedelta
import threading

from app.utils.forex_scheduled_tasks import run_market_analyzer

if __name__ == "__main__":
    print("开始测试市场变化分析线程...")
    
    # 创建并启动市场变化分析线程
    analyzer_thread = threading.Thread(target=run_market_analyzer, daemon=True)
    analyzer_thread.start()
    
    print("市场变化分析线程已启动，主线程等待60秒...")
    
    # 主线程等待60秒
    time.sleep(60)
    
    print("测试完成，退出程序")
