#!/usr/bin/env python3
"""
QuantumForex Pro - Trainer模型集成测试
验证Pro系统是否成功使用Trainer训练的高级模型
"""

import sys
import os
from datetime import datetime
import pandas as pd

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_trainer_model_loading():
    """测试Trainer模型加载"""
    print("🔍 测试Trainer模型加载...")
    print("=" * 60)

    try:
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine, ModelType

        # 创建ML引擎实例
        ml_engine = LightweightMLEngine()

        print("📊 ML引擎模型状态:")

        trainer_models_found = 0
        standard_models_found = 0

        for model_type in ModelType:
            model = ml_engine.models.get(model_type)

            if model is not None:
                model_name = type(model).__name__

                # 判断是否是Trainer模型
                is_trainer_model = False

                # 检查模型特征来判断是否是Trainer训练的高级模型
                if 'LGBM' in model_name or 'LGB' in model_name or 'XGB' in model_name or 'XGBoost' in model_name:
                    is_trainer_model = True
                elif hasattr(model, 'n_estimators'):
                    n_estimators = model.n_estimators
                    if n_estimators > 100:  # Trainer模型通常有更多的估计器
                        is_trainer_model = True
                elif hasattr(model, 'max_iter'):
                    max_iter = model.max_iter
                    if max_iter > 1000:  # 高级模型通常有更多迭代
                        is_trainer_model = True

                # 检查模型性能指标
                performance = ml_engine.model_performance.get(model_type, 0.5)

                if is_trainer_model:
                    status = "🤖 Trainer高级模型"
                    trainer_models_found += 1
                else:
                    status = "📊 标准轻量模型"
                    standard_models_found += 1

                print(f"   {model_type.value}: {model_name} - {status}")
                print(f"      性能指标: {performance:.3f}")

                if hasattr(model, 'n_estimators'):
                    print(f"      估计器数量: {model.n_estimators}")
                elif hasattr(model, 'max_iter'):
                    print(f"      最大迭代: {model.max_iter}")
            else:
                print(f"   {model_type.value}: ❌ 未加载")

        print(f"\n📈 模型统计:")
        print(f"   Trainer高级模型: {trainer_models_found}")
        print(f"   标准轻量模型: {standard_models_found}")
        print(f"   总模型数量: {trainer_models_found + standard_models_found}")

        return trainer_models_found > 0

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trainer_model_prediction():
    """测试Trainer模型预测功能"""
    print("\n🔍 测试Trainer模型预测功能...")
    print("=" * 60)

    try:
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine

        # 创建ML引擎
        ml_engine = LightweightMLEngine()

        # 创建测试数据
        test_data = pd.DataFrame({
            'close': [1.1000, 1.1010, 1.1020, 1.1015, 1.1025, 1.1030, 1.1035],
            'high': [1.1005, 1.1015, 1.1025, 1.1020, 1.1030, 1.1035, 1.1040],
            'low': [0.9995, 1.1005, 1.1015, 1.1010, 1.1020, 1.1025, 1.1030],
            'volume': [1000, 1100, 1200, 1050, 1150, 1250, 1300]
        })

        test_indicators = {
            'trend_analysis': {
                'trend_score': 0.6,
                'adx_analysis': {
                    'adx': 25.0,
                    'plus_di': 20.0,
                    'minus_di': 15.0
                }
            },
            'momentum_analysis': {
                'momentum_score': 0.7,
                'rsi_analysis': {
                    'rsi_data': {
                        'rsi_14': 55.0
                    }
                }
            },
            'volatility_analysis': {
                'volatility_score': 0.4
            }
        }

        print("🧪 生成ML预测...")
        predictions = ml_engine.generate_predictions(test_data, test_indicators)

        if predictions:
            print(f"✅ 成功生成{len(predictions)}个预测")

            for model_type, prediction in predictions.items():
                model = ml_engine.models.get(model_type)
                model_name = type(model).__name__ if model else "Unknown"

                print(f"\n📊 {model_type.value} ({model_name}):")
                print(f"   预测值: {prediction.prediction:.6f}")
                print(f"   置信度: {prediction.confidence:.3f}")
                print(f"   模型准确率: {prediction.model_accuracy:.3f}")

                # 显示概率分布
                if len(prediction.probability_distribution) > 1:
                    print(f"   概率分布: {prediction.probability_distribution}")

                # 显示特征重要性（前3个）
                if prediction.feature_importance:
                    top_features = sorted(prediction.feature_importance.items(),
                                        key=lambda x: x[1], reverse=True)[:3]
                    print(f"   重要特征: {dict(top_features)}")

            return True
        else:
            print("❌ 未能生成预测")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_performance_comparison():
    """测试模型性能对比"""
    print("\n🔍 测试模型性能对比...")
    print("=" * 60)

    try:
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine, ModelType

        ml_engine = LightweightMLEngine()

        print("📈 模型性能对比:")

        for model_type in ModelType:
            model = ml_engine.models.get(model_type)
            performance = ml_engine.model_performance.get(model_type, 0.5)

            if model is not None:
                model_name = type(model).__name__

                # 判断模型类型
                if 'LGBM' in model_name or 'LGB' in model_name or 'XGB' in model_name or 'XGBoost' in model_name:
                    model_category = "🤖 Trainer高级"
                elif hasattr(model, 'n_estimators') and model.n_estimators > 100:
                    model_category = "🤖 Trainer高级"
                else:
                    model_category = "📊 标准轻量"

                print(f"   {model_type.value}:")
                print(f"      类型: {model_category}")
                print(f"      模型: {model_name}")
                print(f"      性能: {performance:.3f}")

                # 性能评级
                if performance >= 0.8:
                    rating = "🌟🌟🌟 优秀"
                elif performance >= 0.7:
                    rating = "🌟🌟 良好"
                elif performance >= 0.6:
                    rating = "🌟 一般"
                else:
                    rating = "❌ 较差"

                print(f"      评级: {rating}")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_status():
    """测试集成状态"""
    print("\n🔍 测试集成状态...")
    print("=" * 60)

    try:
        from core.ml_engine.lightweight_ml_engine import LightweightMLEngine
        from pathlib import Path

        # 检查模型文件
        models_dir = Path("data/models")

        if not models_dir.exists():
            print("❌ 模型目录不存在")
            return False

        # 统计模型文件
        trainer_files = []
        standard_files = []

        for model_file in models_dir.glob("*.pkl"):
            if any(keyword in model_file.name for keyword in ['lightgbm', 'xgboost', 'random_forest']):
                if 'model' in model_file.name:
                    trainer_files.append(model_file)
            elif model_file.name.endswith('_model.pkl'):
                standard_files.append(model_file)

        print(f"📁 模型文件统计:")
        print(f"   Trainer模型文件: {len(trainer_files)}")
        print(f"   标准模型文件: {len(standard_files)}")

        # 检查ML引擎使用情况
        ml_engine = LightweightMLEngine()

        trainer_models_in_use = 0
        standard_models_in_use = 0

        for model_type in ml_engine.models:
            model = ml_engine.models[model_type]
            if model is not None:
                model_name = type(model).__name__

                if 'LGBM' in model_name or 'LGB' in model_name or 'XGB' in model_name or 'XGBoost' in model_name:
                    trainer_models_in_use += 1
                elif hasattr(model, 'n_estimators') and model.n_estimators > 100:
                    trainer_models_in_use += 1
                else:
                    standard_models_in_use += 1

        print(f"\n🔄 ML引擎使用情况:")
        print(f"   使用Trainer模型: {trainer_models_in_use}")
        print(f"   使用标准模型: {standard_models_in_use}")

        # 判断集成状态
        if trainer_models_in_use > 0:
            if trainer_models_in_use >= 3:
                status = "🟢 完全集成"
                description = "Pro系统主要使用Trainer训练的高级模型"
            else:
                status = "🟡 部分集成"
                description = "Pro系统部分使用Trainer模型，部分使用标准模型"
        else:
            status = "🔴 未集成"
            description = "Pro系统仍在使用标准轻量级模型"

        print(f"\n🎯 集成状态: {status}")
        print(f"📝 描述: {description}")

        return trainer_models_in_use > 0

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 QuantumForex Pro - Trainer模型集成测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 执行所有测试
    tests = [
        ("Trainer模型加载", test_trainer_model_loading),
        ("Trainer模型预测", test_trainer_model_prediction),
        ("模型性能对比", test_model_performance_comparison),
        ("集成状态检查", test_integration_status)
    ]

    results = {}
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"🔄 开始测试: {test_name}")
            print(f"{'='*60}")

            results[test_name] = test_func()

            if results[test_name]:
                print(f"✅ {test_name} - 测试通过")
            else:
                print(f"❌ {test_name} - 测试失败")

        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False

    print("\n" + "=" * 60)
    print("📊 Trainer模型集成测试结果汇总:")
    print("=" * 60)

    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False

    print("=" * 60)
    if all_passed:
        print("🎉 Pro-Trainer集成测试全部通过！")
        print("✅ Pro系统现在使用Trainer训练的高级模型")
        print("✅ 两个系统已经成功打通")
        print("🚀 享受更高精度的ML预测！")
    else:
        print("❌ 部分测试失败，集成可能不完整")
        print("💡 建议检查模型文件和加载逻辑")

    sys.exit(0 if all_passed else 1)
