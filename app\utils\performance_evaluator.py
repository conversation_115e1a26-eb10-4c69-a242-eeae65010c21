"""
交易绩效评估模块
用于评估LLM交易决策的绩效，提供奖励机制
"""
import os
import json
import math
import numpy as np
from datetime import datetime, timedelta
from app.utils.error_logger import log_error, ErrorType


# 虚拟账户配置
INITIAL_BALANCE = 10000.0  # 初始资金：10,000美元
RISK_FREE_RATE = 0.03  # 无风险利率：3%

# 绩效数据文件路径
PERFORMANCE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
PERFORMANCE_FILE = os.path.join(PERFORMANCE_DIR, 'performance_data.json')
VIRTUAL_ACCOUNT_FILE = os.path.join(PERFORMANCE_DIR, 'virtual_account.json')

# 确保数据目录存在
if not os.path.exists(PERFORMANCE_DIR):
    os.makedirs(PERFORMANCE_DIR)


class TradeStatus:
    """交易状态枚举"""
    OPEN = "OPEN"  # 开仓
    CLOSED = "CLOSED"  # 平仓
    PENDING = "PENDING"  # 挂单
    CANCELLED = "CANCELLED"  # 取消


class TradeResult:
    """交易结果枚举"""
    WIN = "WIN"  # 盈利
    LOSS = "LOSS"  # 亏损
    BREAKEVEN = "BREAKEVEN"  # 持平
    UNKNOWN = "UNKNOWN"  # 未知


def initialize_virtual_account():
    """
    初始化虚拟账户

    Returns:
        dict: 虚拟账户数据
    """
    if os.path.exists(VIRTUAL_ACCOUNT_FILE):
        try:
            with open(VIRTUAL_ACCOUNT_FILE, 'r', encoding='utf-8') as f:
                account = json.load(f)
                return account
        except Exception as e:
            print(f'读取虚拟账户数据失败: {e}')

    # 创建新的虚拟账户
    account = {
        "initial_balance": INITIAL_BALANCE,
        "current_balance": INITIAL_BALANCE,
        "equity": INITIAL_BALANCE,
        "open_trades": [],
        "closed_trades": [],
        "pending_orders": [],
        "performance_metrics": {
            "total_return": 0.0,
            "win_rate": 0.0,
            "profit_factor": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
            "average_win": 0.0,
            "average_loss": 0.0,
            "largest_win": 0.0,
            "largest_loss": 0.0,
            "consecutive_wins": 0,
            "consecutive_losses": 0,
            "max_consecutive_wins": 0,
            "max_consecutive_losses": 0
        },
        "daily_balance": [
            {
                "date": datetime.now().strftime('%Y-%m-%d'),
                "balance": INITIAL_BALANCE,
                "equity": INITIAL_BALANCE
            }
        ],
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }

    # 保存虚拟账户数据
    save_virtual_account(account)

    return account


def save_virtual_account(account):
    """
    保存虚拟账户数据

    Args:
        account (dict): 虚拟账户数据
    """
    try:
        # 更新时间戳
        account["updated_at"] = datetime.now().isoformat()

        # 保存虚拟账户数据
        with open(VIRTUAL_ACCOUNT_FILE, 'w', encoding='utf-8') as f:
            json.dump(account, f, ensure_ascii=False, indent=2)

        return True
    except Exception as e:
        print(f'保存虚拟账户数据失败: {e}')

        # 记录错误
        log_error(
            error_type=ErrorType.UNKNOWN_ERROR,
            message=f'保存虚拟账户数据失败: {e}',
            details={'exception': str(e)},
            operation='SAVE_VIRTUAL_ACCOUNT'
        )

        return False


def get_virtual_account():
    """
    获取虚拟账户数据

    Returns:
        dict: 虚拟账户数据
    """
    if not os.path.exists(VIRTUAL_ACCOUNT_FILE):
        return initialize_virtual_account()

    try:
        with open(VIRTUAL_ACCOUNT_FILE, 'r', encoding='utf-8') as f:
            account = json.load(f)

        return account
    except Exception as e:
        print(f'读取虚拟账户数据失败: {e}')

        # 记录错误
        log_error(
            error_type=ErrorType.UNKNOWN_ERROR,
            message=f'读取虚拟账户数据失败: {e}',
            details={'exception': str(e)},
            operation='GET_VIRTUAL_ACCOUNT'
        )

        # 初始化新的虚拟账户
        return initialize_virtual_account()


def record_trade(trade_data):
    """
    记录交易

    Args:
        trade_data (dict): 交易数据

    Returns:
        bool: 是否成功
    """
    try:
        # 获取虚拟账户
        account = get_virtual_account()

        # 交易类型
        trade_type = trade_data.get('action', 'NONE')

        # 如果是观望，不记录交易
        if trade_type == 'NONE':
            return True

        # 交易ID
        trade_id = trade_data.get('orderId', str(datetime.now().timestamp()))

        # 交易时间
        trade_time = datetime.now().isoformat()

        # 交易品种
        symbol = trade_data.get('symbol', 'EURUSD')

        # 交易方向
        direction = trade_type  # BUY或SELL

        # 交易手数
        lot_size = float(trade_data.get('lotSize', 0.01))

        # 入场价格
        entry_price = trade_data.get('entryPrice')
        if entry_price is None and trade_data.get('orderType') == 'MARKET':
            # 市价单，使用当前价格
            entry_price = trade_data.get('currentPrice')

        # 止损价格
        stop_loss = trade_data.get('stopLoss')

        # 止盈价格
        take_profit = trade_data.get('takeProfit')

        # 交易理由
        reasoning = trade_data.get('reasoning', '')

        # 风险等级
        risk_level = trade_data.get('riskLevel', 'MEDIUM')

        # 创建交易记录
        trade = {
            "id": trade_id,
            "time": trade_time,
            "symbol": symbol,
            "direction": direction,
            "lot_size": lot_size,
            "entry_price": entry_price,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "reasoning": reasoning,
            "risk_level": risk_level,
            "status": TradeStatus.OPEN,
            "exit_price": None,
            "exit_time": None,
            "profit_loss": 0.0,
            "profit_loss_pips": 0.0,
            "result": TradeResult.UNKNOWN,
            "duration": 0,
            "trade_data": trade_data
        }

        # 添加到开仓交易列表
        account["open_trades"].append(trade)

        # 更新账户余额和权益
        # 注意：这里简化处理，实际上应该考虑保证金等因素
        margin_used = lot_size * 1000  # 假设每0.01手需要10美元保证金
        account["equity"] = account["current_balance"]

        # 保存虚拟账户
        save_virtual_account(account)

        return True
    except Exception as e:
        print(f'记录交易失败: {e}')

        # 记录错误
        log_error(
            error_type=ErrorType.UNKNOWN_ERROR,
            message=f'记录交易失败: {e}',
            details={'exception': str(e), 'trade_data': trade_data},
            operation='RECORD_TRADE'
        )

        return False


def update_trade_status(trade_id, status, exit_price=None, exit_time=None):
    """
    更新交易状态

    Args:
        trade_id (str): 交易ID
        status (str): 交易状态
        exit_price (float, optional): 出场价格
        exit_time (str, optional): 出场时间

    Returns:
        bool: 是否成功
    """
    try:
        # 获取虚拟账户
        account = get_virtual_account()

        # 查找交易
        trade = None
        for t in account["open_trades"]:
            if t["id"] == trade_id:
                trade = t
                break

        if trade is None:
            print(f'未找到交易: {trade_id}')
            return False

        # 更新交易状态
        trade["status"] = status

        # 如果是平仓，计算盈亏
        if status == TradeStatus.CLOSED and exit_price is not None:
            # 更新出场价格和时间
            trade["exit_price"] = exit_price
            trade["exit_time"] = exit_time or datetime.now().isoformat()

            # 计算交易持续时间
            entry_time = datetime.fromisoformat(trade["time"].replace('Z', '+00:00'))
            exit_time_obj = datetime.fromisoformat(trade["exit_time"].replace('Z', '+00:00'))
            trade["duration"] = (exit_time_obj - entry_time).total_seconds() / 3600  # 小时

            # 计算盈亏（点数）
            if trade["direction"] == "BUY":
                profit_loss_pips = (exit_price - trade["entry_price"]) * 10000
            else:  # SELL
                profit_loss_pips = (trade["entry_price"] - exit_price) * 10000

            trade["profit_loss_pips"] = profit_loss_pips

            # 计算盈亏（美元）
            profit_loss = profit_loss_pips * trade["lot_size"] * 10  # 假设每点10美元/标准手
            trade["profit_loss"] = profit_loss

            # 确定交易结果
            if profit_loss > 0:
                trade["result"] = TradeResult.WIN
            elif profit_loss < 0:
                trade["result"] = TradeResult.LOSS
            else:
                trade["result"] = TradeResult.BREAKEVEN

            # 更新账户余额
            account["current_balance"] += profit_loss
            account["equity"] = account["current_balance"]

            # 将交易从开仓列表移动到平仓列表
            account["open_trades"].remove(trade)
            account["closed_trades"].append(trade)

            # 更新每日余额
            today = datetime.now().strftime('%Y-%m-%d')
            daily_balance_updated = False

            for daily in account["daily_balance"]:
                if daily["date"] == today:
                    daily["balance"] = account["current_balance"]
                    daily["equity"] = account["equity"]
                    daily_balance_updated = True
                    break

            if not daily_balance_updated:
                account["daily_balance"].append({
                    "date": today,
                    "balance": account["current_balance"],
                    "equity": account["equity"]
                })

            # 更新绩效指标
            update_performance_metrics(account)

        # 保存虚拟账户
        save_virtual_account(account)

        return True
    except Exception as e:
        print(f'更新交易状态失败: {e}')

        # 记录错误
        log_error(
            error_type=ErrorType.UNKNOWN_ERROR,
            message=f'更新交易状态失败: {e}',
            details={'exception': str(e), 'trade_id': trade_id, 'status': status},
            operation='UPDATE_TRADE_STATUS'
        )

        return False


def update_performance_metrics(account):
    """
    更新绩效指标

    Args:
        account (dict): 虚拟账户数据
    """
    try:
        # 获取平仓交易
        closed_trades = account["closed_trades"]

        if not closed_trades:
            return

        # 计算总收益率
        total_return = (account["current_balance"] - account["initial_balance"]) / account["initial_balance"] * 100

        # 计算胜率
        wins = [t for t in closed_trades if t["result"] == TradeResult.WIN]
        losses = [t for t in closed_trades if t["result"] == TradeResult.LOSS]

        win_rate = len(wins) / len(closed_trades) * 100 if closed_trades else 0

        # 计算盈亏比
        total_profit = sum(t["profit_loss"] for t in wins)
        total_loss = abs(sum(t["profit_loss"] for t in losses))

        profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')

        # 计算最大回撤
        balances = [account["initial_balance"]]
        for trade in sorted(closed_trades, key=lambda x: x["exit_time"]):
            balances.append(balances[-1] + trade["profit_loss"])

        max_balance = balances[0]
        max_drawdown = 0

        for balance in balances:
            max_balance = max(max_balance, balance)
            drawdown = (max_balance - balance) / max_balance * 100
            max_drawdown = max(max_drawdown, drawdown)

        # 计算夏普比率
        if len(balances) > 1:
            returns = [(balances[i] - balances[i-1]) / balances[i-1] for i in range(1, len(balances))]
            avg_return = np.mean(returns)
            std_return = np.std(returns)

            sharpe_ratio = (avg_return - RISK_FREE_RATE / 365) / std_return * np.sqrt(365) if std_return > 0 else 0
        else:
            sharpe_ratio = 0

        # 计算平均盈利和平均亏损
        average_win = total_profit / len(wins) if wins else 0
        average_loss = total_loss / len(losses) if losses else 0

        # 计算最大盈利和最大亏损
        largest_win = max([t["profit_loss"] for t in wins]) if wins else 0
        largest_loss = min([t["profit_loss"] for t in losses]) if losses else 0

        # 计算连续盈利和连续亏损
        consecutive_wins = 0
        consecutive_losses = 0
        max_consecutive_wins = 0
        max_consecutive_losses = 0

        current_streak = 0
        current_streak_type = None

        for trade in closed_trades:
            if trade["result"] == TradeResult.WIN:
                if current_streak_type == TradeResult.WIN:
                    current_streak += 1
                else:
                    current_streak = 1
                    current_streak_type = TradeResult.WIN

                max_consecutive_wins = max(max_consecutive_wins, current_streak)

                if current_streak > consecutive_wins:
                    consecutive_wins = current_streak
            elif trade["result"] == TradeResult.LOSS:
                if current_streak_type == TradeResult.LOSS:
                    current_streak += 1
                else:
                    current_streak = 1
                    current_streak_type = TradeResult.LOSS

                max_consecutive_losses = max(max_consecutive_losses, current_streak)

                if current_streak > consecutive_losses:
                    consecutive_losses = current_streak

        # 更新绩效指标
        account["performance_metrics"] = {
            "total_return": total_return,
            "win_rate": win_rate,
            "profit_factor": profit_factor,
            "max_drawdown": max_drawdown,
            "sharpe_ratio": sharpe_ratio,
            "average_win": average_win,
            "average_loss": average_loss,
            "largest_win": largest_win,
            "largest_loss": largest_loss,
            "consecutive_wins": consecutive_wins if current_streak_type == TradeResult.WIN else 0,
            "consecutive_losses": consecutive_losses if current_streak_type == TradeResult.LOSS else 0,
            "max_consecutive_wins": max_consecutive_wins,
            "max_consecutive_losses": max_consecutive_losses
        }
    except Exception as e:
        print(f'更新绩效指标失败: {e}')

        # 记录错误
        log_error(
            error_type=ErrorType.UNKNOWN_ERROR,
            message=f'更新绩效指标失败: {e}',
            details={'exception': str(e)},
            operation='UPDATE_PERFORMANCE_METRICS'
        )


def remove_pending_order(order_id):
    """
    从虚拟账户中移除挂单

    Args:
        order_id (str): 订单ID

    Returns:
        bool: 是否成功
    """
    try:
        # 获取虚拟账户
        account = get_virtual_account()

        # 查找挂单
        pending_order = None
        for order in account.get("pending_orders", []):
            if order["id"] == order_id:
                pending_order = order
                break

        # 如果在pending_orders中找不到，尝试在open_trades中查找
        if pending_order is None:
            for trade in account.get("open_trades", []):
                if trade["id"] == order_id and trade.get("trade_data", {}).get("orderType") in ["LIMIT", "STOP"]:
                    pending_order = trade
                    break

        if pending_order is None:
            print(f'未找到挂单: {order_id}')
            return False

        # 从列表中移除挂单
        if pending_order in account.get("pending_orders", []):
            account["pending_orders"].remove(pending_order)
        elif pending_order in account.get("open_trades", []):
            account["open_trades"].remove(pending_order)

        # 保存虚拟账户
        save_virtual_account(account)

        return True
    except Exception as e:
        print(f'移除挂单失败: {e}')

        # 记录错误
        log_error(
            error_type=ErrorType.UNKNOWN_ERROR,
            message=f'移除挂单失败: {e}',
            details={'exception': str(e), 'order_id': order_id},
            operation='REMOVE_PENDING_ORDER'
        )

        return False


def get_performance_summary():
    """
    获取绩效摘要

    Returns:
        dict: 绩效摘要
    """
    try:
        # 获取虚拟账户
        account = get_virtual_account()

        # 计算今日盈亏
        today = datetime.now().strftime('%Y-%m-%d')
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

        today_balance = None
        yesterday_balance = None

        for daily in account["daily_balance"]:
            if daily["date"] == today:
                today_balance = daily["balance"]
            elif daily["date"] == yesterday:
                yesterday_balance = daily["balance"]

        if today_balance is not None and yesterday_balance is not None:
            today_profit_loss = today_balance - yesterday_balance
            today_return = today_profit_loss / yesterday_balance * 100
        else:
            today_profit_loss = 0
            today_return = 0

        # 计算本周盈亏
        today_date = datetime.now()
        start_of_week = (today_date - timedelta(days=today_date.weekday())).strftime('%Y-%m-%d')

        week_start_balance = None

        for daily in sorted(account["daily_balance"], key=lambda x: x["date"]):
            if daily["date"] >= start_of_week:
                if week_start_balance is None:
                    week_start_balance = daily["balance"]

        if week_start_balance is not None and today_balance is not None:
            week_profit_loss = today_balance - week_start_balance
            week_return = week_profit_loss / week_start_balance * 100
        else:
            week_profit_loss = 0
            week_return = 0

        # 计算本月盈亏
        start_of_month = today_date.replace(day=1).strftime('%Y-%m-%d')

        month_start_balance = None

        for daily in sorted(account["daily_balance"], key=lambda x: x["date"]):
            if daily["date"] >= start_of_month:
                if month_start_balance is None:
                    month_start_balance = daily["balance"]

        if month_start_balance is not None and today_balance is not None:
            month_profit_loss = today_balance - month_start_balance
            month_return = month_profit_loss / month_start_balance * 100
        else:
            month_profit_loss = 0
            month_return = 0

        # 获取最近的交易
        recent_trades = sorted(account["closed_trades"], key=lambda x: x["exit_time"], reverse=True)[:5]

        # 构建绩效摘要
        summary = {
            "account_summary": {
                "initial_balance": account["initial_balance"],
                "current_balance": account["current_balance"],
                "equity": account["equity"],
                "total_return": account["performance_metrics"]["total_return"],
                "win_rate": account["performance_metrics"]["win_rate"],
                "profit_factor": account["performance_metrics"]["profit_factor"],
                "max_drawdown": account["performance_metrics"]["max_drawdown"],
                "sharpe_ratio": account["performance_metrics"]["sharpe_ratio"]
            },
            "period_performance": {
                "today": {
                    "profit_loss": today_profit_loss,
                    "return": today_return
                },
                "week": {
                    "profit_loss": week_profit_loss,
                    "return": week_return
                },
                "month": {
                    "profit_loss": month_profit_loss,
                    "return": month_return
                }
            },
            "recent_trades": [
                {
                    "time": t["exit_time"],
                    "symbol": t["symbol"],
                    "direction": t["direction"],
                    "profit_loss": t["profit_loss"],
                    "profit_loss_pips": t["profit_loss_pips"],
                    "result": t["result"]
                } for t in recent_trades
            ],
            "performance_metrics": account["performance_metrics"],
            "open_trades_count": len(account["open_trades"]),
            "closed_trades_count": len(account["closed_trades"])
        }

        return summary
    except Exception as e:
        print(f'获取绩效摘要失败: {e}')

        # 记录错误
        log_error(
            error_type=ErrorType.UNKNOWN_ERROR,
            message=f'获取绩效摘要失败: {e}',
            details={'exception': str(e)},
            operation='GET_PERFORMANCE_SUMMARY'
        )

        return {
            "account_summary": {
                "initial_balance": INITIAL_BALANCE,
                "current_balance": INITIAL_BALANCE,
                "equity": INITIAL_BALANCE,
                "total_return": 0.0,
                "win_rate": 0.0,
                "profit_factor": 0.0,
                "max_drawdown": 0.0,
                "sharpe_ratio": 0.0
            },
            "period_performance": {
                "today": {"profit_loss": 0.0, "return": 0.0},
                "week": {"profit_loss": 0.0, "return": 0.0},
                "month": {"profit_loss": 0.0, "return": 0.0}
            },
            "recent_trades": [],
            "performance_metrics": {
                "total_return": 0.0,
                "win_rate": 0.0,
                "profit_factor": 0.0,
                "max_drawdown": 0.0,
                "sharpe_ratio": 0.0,
                "average_win": 0.0,
                "average_loss": 0.0,
                "largest_win": 0.0,
                "largest_loss": 0.0,
                "consecutive_wins": 0,
                "consecutive_losses": 0,
                "max_consecutive_wins": 0,
                "max_consecutive_losses": 0
            },
            "open_trades_count": 0,
            "closed_trades_count": 0
        }
