"""
技术指标计算模块
提供各种技术指标的计算功能
"""

import math
from datetime import datetime
from typing import List, Dict, Optional
from app.utils.logger_manager import log_analysis, LogLevel

def calculate_indicators(data: List[Dict], timeframe: str) -> Dict:
    """
    计算技术指标
    
    Args:
        data: K线数据列表
        timeframe: 时间框架
        
    Returns:
        Dict: 技术指标字典
    """
    try:
        if not data or len(data) < 5:
            log_analysis(f"数据不足，无法计算{timeframe}技术指标", LogLevel.WARNING)
            return {
                'timeframe': timeframe,
                'dataAvailable': False,
                'message': '数据不足'
            }
        
        # 提取价格数据
        closes = [float(d['close']) for d in data]
        highs = [float(d['high']) for d in data]
        lows = [float(d['low']) for d in data]
        opens = [float(d['open']) for d in data]
        
        # 计算各种技术指标
        indicators = {
            'timeframe': timeframe,
            'dataAvailable': True,
            'currentPrice': closes[-1],
            'klineCount': len(data)
        }
        
        # 移动平均线
        if len(closes) >= 5:
            indicators['ma5'] = calculate_ma(closes, 5)
        if len(closes) >= 10:
            indicators['ma10'] = calculate_ma(closes, 10)
        if len(closes) >= 20:
            indicators['ma20'] = calculate_ma(closes, 20)
        if len(closes) >= 50:
            indicators['ma50'] = calculate_ma(closes, 50)
        
        # RSI
        if len(closes) >= 14:
            indicators['rsi'] = calculate_rsi(closes, 14)
        
        # MACD
        if len(closes) >= 26:
            macd_data = calculate_macd(closes)
            indicators.update(macd_data)
        
        # 布林带
        if len(closes) >= 20:
            bollinger_data = calculate_bollinger_bands(closes, 20, 2)
            indicators.update(bollinger_data)
        
        # ATR (平均真实波幅)
        if len(data) >= 14:
            indicators['atr'] = calculate_atr(highs, lows, closes, 14)
        
        # 随机指标 (KDJ)
        if len(data) >= 14:
            kdj_data = calculate_kdj(highs, lows, closes, 9, 3, 3)
            indicators.update(kdj_data)
        
        # 威廉指标 (Williams %R)
        if len(data) >= 14:
            indicators['williams_r'] = calculate_williams_r(highs, lows, closes, 14)
        
        # 商品通道指数 (CCI)
        if len(data) >= 20:
            indicators['cci'] = calculate_cci(highs, lows, closes, 20)
        
        log_analysis(f"成功计算{timeframe}技术指标", LogLevel.INFO)
        return indicators
        
    except Exception as e:
        log_analysis(f"计算{timeframe}技术指标失败: {e}", LogLevel.ERROR)
        return {
            'timeframe': timeframe,
            'dataAvailable': False,
            'error': str(e)
        }

def calculate_ma(prices: List[float], period: int) -> float:
    """计算移动平均线"""
    if len(prices) < period:
        return 0.0
    return sum(prices[-period:]) / period

def calculate_rsi(prices: List[float], period: int = 14) -> float:
    """计算RSI指标"""
    try:
        if len(prices) < period + 1:
            return 50.0
        
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(change))
        
        if len(gains) < period:
            return 50.0
        
        # 计算平均收益和平均损失
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return max(0, min(100, rsi))
        
    except Exception:
        return 50.0

def calculate_macd(prices: List[float], fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Dict:
    """计算MACD指标"""
    try:
        if len(prices) < slow_period:
            return {
                'macd': 0.0,
                'macdSignal': 0.0,
                'macdHistogram': 0.0
            }
        
        # 计算EMA
        ema_fast = calculate_ema(prices, fast_period)
        ema_slow = calculate_ema(prices, slow_period)
        
        # MACD线
        macd = ema_fast - ema_slow
        
        # 计算信号线（MACD的EMA）
        macd_values = [macd]  # 简化处理，实际应该计算历史MACD值
        macd_signal = macd  # 简化处理
        
        # MACD柱状图
        macd_histogram = macd - macd_signal
        
        return {
            'macd': macd,
            'macdSignal': macd_signal,
            'macdHistogram': macd_histogram
        }
        
    except Exception:
        return {
            'macd': 0.0,
            'macdSignal': 0.0,
            'macdHistogram': 0.0
        }

def calculate_ema(prices: List[float], period: int) -> float:
    """计算指数移动平均线"""
    try:
        if len(prices) < period:
            return sum(prices) / len(prices)
        
        multiplier = 2 / (period + 1)
        ema = prices[0]
        
        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))
        
        return ema
        
    except Exception:
        return 0.0

def calculate_bollinger_bands(prices: List[float], period: int = 20, std_dev: float = 2) -> Dict:
    """计算布林带"""
    try:
        if len(prices) < period:
            return {
                'bollinger_upper': 0.0,
                'bollinger_middle': 0.0,
                'bollinger_lower': 0.0
            }
        
        # 中轨（移动平均线）
        middle = calculate_ma(prices, period)
        
        # 计算标准差
        recent_prices = prices[-period:]
        variance = sum((price - middle) ** 2 for price in recent_prices) / period
        std = math.sqrt(variance)
        
        # 上轨和下轨
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        
        return {
            'bollinger_upper': upper,
            'bollinger_middle': middle,
            'bollinger_lower': lower
        }
        
    except Exception:
        return {
            'bollinger_upper': 0.0,
            'bollinger_middle': 0.0,
            'bollinger_lower': 0.0
        }

def calculate_atr(highs: List[float], lows: List[float], closes: List[float], period: int = 14) -> float:
    """计算平均真实波幅 (ATR)"""
    try:
        if len(highs) < period or len(lows) < period or len(closes) < period:
            return 0.0
        
        true_ranges = []
        
        for i in range(1, len(highs)):
            tr1 = highs[i] - lows[i]
            tr2 = abs(highs[i] - closes[i-1])
            tr3 = abs(lows[i] - closes[i-1])
            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)
        
        if len(true_ranges) < period:
            return 0.0
        
        return sum(true_ranges[-period:]) / period
        
    except Exception:
        return 0.0

def calculate_kdj(highs: List[float], lows: List[float], closes: List[float], 
                  k_period: int = 9, d_period: int = 3, j_period: int = 3) -> Dict:
    """计算KDJ指标"""
    try:
        if len(highs) < k_period or len(lows) < k_period or len(closes) < k_period:
            return {
                'k': 50.0,
                'd': 50.0,
                'j': 50.0
            }
        
        # 计算RSV (Raw Stochastic Value)
        recent_highs = highs[-k_period:]
        recent_lows = lows[-k_period:]
        current_close = closes[-1]
        
        highest_high = max(recent_highs)
        lowest_low = min(recent_lows)
        
        if highest_high == lowest_low:
            rsv = 50.0
        else:
            rsv = (current_close - lowest_low) / (highest_high - lowest_low) * 100
        
        # 简化的K、D、J计算
        k = rsv
        d = k
        j = 3 * k - 2 * d
        
        return {
            'k': max(0, min(100, k)),
            'd': max(0, min(100, d)),
            'j': max(0, min(100, j))
        }
        
    except Exception:
        return {
            'k': 50.0,
            'd': 50.0,
            'j': 50.0
        }

def calculate_williams_r(highs: List[float], lows: List[float], closes: List[float], period: int = 14) -> float:
    """计算威廉指标 (Williams %R)"""
    try:
        if len(highs) < period or len(lows) < period or len(closes) < period:
            return -50.0
        
        recent_highs = highs[-period:]
        recent_lows = lows[-period:]
        current_close = closes[-1]
        
        highest_high = max(recent_highs)
        lowest_low = min(recent_lows)
        
        if highest_high == lowest_low:
            return -50.0
        
        williams_r = (highest_high - current_close) / (highest_high - lowest_low) * -100
        
        return max(-100, min(0, williams_r))
        
    except Exception:
        return -50.0

def calculate_cci(highs: List[float], lows: List[float], closes: List[float], period: int = 20) -> float:
    """计算商品通道指数 (CCI)"""
    try:
        if len(highs) < period or len(lows) < period or len(closes) < period:
            return 0.0
        
        # 计算典型价格 (Typical Price)
        typical_prices = []
        for i in range(len(highs)):
            tp = (highs[i] + lows[i] + closes[i]) / 3
            typical_prices.append(tp)
        
        if len(typical_prices) < period:
            return 0.0
        
        # 计算移动平均
        recent_tp = typical_prices[-period:]
        sma_tp = sum(recent_tp) / period
        
        # 计算平均偏差
        mean_deviation = sum(abs(tp - sma_tp) for tp in recent_tp) / period
        
        if mean_deviation == 0:
            return 0.0
        
        # 计算CCI
        current_tp = typical_prices[-1]
        cci = (current_tp - sma_tp) / (0.015 * mean_deviation)
        
        return max(-300, min(300, cci))
        
    except Exception:
        return 0.0
