#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI与run.py API的连接
"""

import requests
import time

def test_api_connection():
    """测试API连接"""
    
    print("🔍 测试GUI与run.py API连接...")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # 测试基本连接
    print("1. 测试基本连接...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 服务器响应: {data.get('message', 'Unknown')}")
            print(f"   ✅ 版本: {data.get('version', 'Unknown')}")
            print(f"   ✅ 状态: {data.get('status', 'Unknown')}")
        else:
            print(f"   ❌ 连接失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        return False
    
    # 测试数据库连接
    print("\n2. 测试数据库连接...")
    try:
        response = requests.get(f"{base_url}/api/forex-trading/test-db", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ 数据库连接: {data.get('message', 'Success')}")
            else:
                print(f"   ❌ 数据库连接失败: {data.get('message', 'Unknown error')}")
        else:
            print(f"   ❌ 数据库测试失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 数据库测试失败: {e}")
    
    # 测试K线数据获取
    print("\n3. 测试K线数据获取...")
    try:
        response = requests.get(f"{base_url}/api/forex-trading/eurusd/klines?period=15&count=1", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                klines = data.get('data', [])
                if klines:
                    latest = klines[0]
                    print(f"   ✅ 获取到K线数据:")
                    print(f"      时间: {latest.get('time', 'Unknown')}")
                    print(f"      价格: {latest.get('close', 'Unknown')}")
                    print(f"      最高: {latest.get('high', 'Unknown')}")
                    print(f"      最低: {latest.get('low', 'Unknown')}")
                else:
                    print("   ⚠️ K线数据为空")
            else:
                print(f"   ❌ K线数据获取失败: {data.get('message', 'Unknown error')}")
        else:
            print(f"   ❌ K线数据请求失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"   ❌ K线数据获取失败: {e}")
    
    # 测试分析API
    print("\n4. 测试分析API...")
    try:
        response = requests.get(f"{base_url}/api/forex-trading/analyze", timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                result = data.get('result', {})
                print(f"   ✅ 分析API响应成功:")
                print(f"      来源: {'缓存' if data.get('fromCache') else '实时分析'}")
                
                # 显示交易指令
                trade_instructions = result.get('tradeInstructions', {})
                if trade_instructions:
                    print(f"      交易建议: {trade_instructions.get('action', 'NONE')}")
                    print(f"      信心度: {result.get('confidence', 0):.1%}")
                
                # 显示市场数据
                market_data = result.get('marketData', {})
                if market_data:
                    print(f"      当前价格: {market_data.get('currentPrice', 'Unknown')}")
                
            else:
                print(f"   ❌ 分析API失败: {data.get('message', 'Unknown error')}")
        else:
            print(f"   ❌ 分析API请求失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 分析API测试失败: {e}")
    
    print(f"\n🎉 API连接测试完成!")
    return True

def test_gui_simulation():
    """模拟GUI的API调用"""
    
    print("\n🎮 模拟GUI API调用...")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # 模拟GUI的数据获取循环
    for i in range(3):
        print(f"\n第 {i+1} 次调用:")
        
        try:
            # 获取分析结果
            response = requests.get(f"{base_url}/api/forex-trading/analyze", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result = data.get('result', {})
                    
                    # 提取GUI需要的数据
                    market_data = result.get('marketData', {})
                    trade_instructions = result.get('tradeInstructions', {})
                    
                    print(f"   📊 市场数据:")
                    print(f"      价格: {market_data.get('currentPrice', 'N/A')}")
                    print(f"      货币对: {market_data.get('symbol', 'N/A')}")
                    
                    print(f"   🧠 分析结果:")
                    print(f"      建议: {trade_instructions.get('action', 'NONE')}")
                    print(f"      信心: {result.get('confidence', 0):.1%}")
                    print(f"      来源: {'缓存' if data.get('fromCache') else '实时'}")
                    
                    # 提取技术指标
                    indicators = result.get('indicators', {})
                    if indicators:
                        print(f"   📈 技术指标:")
                        print(f"      RSI: {indicators.get('rsi', 'N/A')}")
                        print(f"      MACD: {indicators.get('macd', 'N/A')}")
                        print(f"      MA20: {indicators.get('ma_20', 'N/A')}")
                
                else:
                    print(f"   ❌ API返回失败: {data.get('message', 'Unknown')}")
            else:
                print(f"   ❌ 请求失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 调用失败: {e}")
        
        if i < 2:  # 不是最后一次
            print("   等待5秒...")
            time.sleep(5)
    
    print(f"\n✅ GUI模拟调用完成!")

def main():
    """主函数"""
    
    print("🚀 开始测试GUI与run.py的连接...")
    
    # 基本连接测试
    if not test_api_connection():
        print("❌ 基本连接测试失败，请确保run.py正在运行")
        return
    
    # GUI模拟测试
    test_gui_simulation()
    
    print("\n🎯 测试总结:")
    print("   ✅ GUI可以连接到run.py的API")
    print("   ✅ 可以获取实时的分析结果")
    print("   ✅ 可以获取市场数据和技术指标")
    print("   ✅ GUI仪表板应该能正常显示run.py的运行结果")

if __name__ == "__main__":
    main()
