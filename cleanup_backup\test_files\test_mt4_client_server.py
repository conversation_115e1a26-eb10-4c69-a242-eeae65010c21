"""
测试MT4客户端与Python服务器之间的通信
"""
import zmq
import time
import json
import threading
import sys
from datetime import datetime

# 确保输出不会被缓存
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 脚本开始执行')
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ZeroMQ版本: {zmq.__version__}')

# 服务器地址
SERVER_ADDRESS = "tcp://*:5555"
CLIENT_ADDRESS = "tcp://localhost:5555"

# 服务器线程
def server_thread():
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器线程开始')
    
    # 创建上下文和套接字
    context = zmq.Context()
    socket = context.socket(zmq.REP)
    
    # 绑定地址
    socket.bind(SERVER_ADDRESS)
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器绑定到: {SERVER_ADDRESS}')
    
    # 处理请求
    while True:
        try:
            # 接收请求
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 等待请求...')
            message = socket.recv_string()
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 收到请求: {message}')
            
            # 解析请求
            request = json.loads(message)
            
            # 处理请求
            action = request.get('action', '')
            
            if action == 'ping':
                response = {
                    'status': 'success',
                    'message': 'pong',
                    'server_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            elif action == 'REGISTER':
                response = {
                    'status': 'success',
                    'message': '注册成功',
                    'user_id': '123456',
                    'username': request.get('username', ''),
                    'auth_code': 'TEST-AUTH-CODE-123456'
                }
            elif action == 'LOGIN_WITH_CREDENTIALS':
                response = {
                    'status': 'success',
                    'message': '登录成功',
                    'user_id': '123456',
                    'username': request.get('username', ''),
                    'auth_code': 'TEST-AUTH-CODE-123456'
                }
            elif action == 'LOGIN':
                response = {
                    'status': 'success',
                    'message': '授权验证成功',
                    'user_id': '123456',
                    'username': 'testuser',
                    'account_type': '标准账户',
                    'expiry_date': '2025-05-13 15:00:00'
                }
            elif action == 'GET_SIGNALS':
                response = {
                    'status': 'success',
                    'message': '获取交易信号成功',
                    'signals': [
                        {
                            'id': '789012',
                            'symbol': 'EURUSD',
                            'direction': 'BUY',
                            'price': 1.0850,
                            'sl': 1.0800,
                            'tp': 1.0900,
                            'lot': 0.1,
                            'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
                    ]
                }
            else:
                response = {
                    'status': 'error',
                    'message': f'未知的操作: {action}'
                }
            
            # 发送响应
            response_str = json.dumps(response)
            socket.send_string(response_str)
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 已发送响应: {response_str}')
            
            # 如果是测试请求，处理完后退出
            if request.get('test_exit', False):
                break
                
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 处理请求时出错: {e}')
            
            # 发送错误响应
            try:
                socket.send_string(json.dumps({
                    'status': 'error',
                    'message': f'服务器内部错误: {str(e)}'
                }))
            except:
                pass
    
    # 关闭套接字和上下文
    socket.close()
    context.term()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器线程结束')

# 客户端函数
def send_request(request):
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 客户端开始')
    
    try:
        # 创建上下文和套接字
        context = zmq.Context()
        socket = context.socket(zmq.REQ)
        
        # 设置超时时间
        socket.setsockopt(zmq.RCVTIMEO, 5000)  # 5秒超时
        
        # 连接到服务器
        socket.connect(CLIENT_ADDRESS)
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 客户端连接到: {CLIENT_ADDRESS}')
        
        # 发送请求
        request_str = json.dumps(request)
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 发送请求: {request_str}')
        socket.send_string(request_str)
        
        # 接收响应
        response_str = socket.recv_string()
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 收到响应: {response_str}')
        
        # 解析响应
        response = json.loads(response_str)
        
        # 关闭套接字和上下文
        socket.close()
        context.term()
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 客户端结束')
        
        return response
    except zmq.error.Again:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器响应超时')
        return {'status': 'error', 'message': '服务器响应超时'}
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 客户端出错: {e}')
        return {'status': 'error', 'message': f'客户端出错: {e}'}

# 测试函数
def run_tests():
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 开始测试')
    
    # 测试ping
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试ping')
    ping_response = send_request({
        'action': 'ping',
        'requestId': 'test-ping'
    })
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ping响应: {ping_response}')
    
    # 测试注册
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试注册')
    register_response = send_request({
        'action': 'REGISTER',
        'username': 'testuser',
        'password': 'qwert12345@'
    })
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 注册响应: {register_response}')
    
    # 测试凭证登录
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试凭证登录')
    login_credentials_response = send_request({
        'action': 'LOGIN_WITH_CREDENTIALS',
        'username': 'testuser',
        'password': 'qwert12345@'
    })
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 凭证登录响应: {login_credentials_response}')
    
    # 测试授权码登录
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试授权码登录')
    login_auth_response = send_request({
        'action': 'LOGIN',
        'auth_code': 'TEST-AUTH-CODE-123456'
    })
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 授权码登录响应: {login_auth_response}')
    
    # 测试获取信号
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试获取信号')
    signals_response = send_request({
        'action': 'GET_SIGNALS',
        'auth_code': 'TEST-AUTH-CODE-123456'
    })
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 获取信号响应: {signals_response}')
    
    # 发送退出请求
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 发送退出请求')
    exit_response = send_request({
        'action': 'ping',
        'requestId': 'test-exit',
        'test_exit': True
    })
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 退出响应: {exit_response}')
    
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 测试完成')

# 主函数
def main():
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 主函数开始')
    
    # 启动服务器线程
    server = threading.Thread(target=server_thread)
    server.daemon = True
    server.start()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器线程已启动')
    
    # 等待服务器启动
    time.sleep(1)
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 等待服务器启动完成')
    
    # 运行测试
    run_tests()
    
    # 等待服务器线程结束
    server.join(timeout=5)
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 主函数结束')

if __name__ == "__main__":
    main()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 脚本结束执行')
