#!/usr/bin/env python3
"""
简单测试重新平衡决策的最小交易单位修复
"""

def test_volume_calculation():
    """测试仓位计算逻辑"""
    print("🧪 测试仓位计算逻辑")
    print("="*50)
    
    # 模拟场景：需要减仓0.008手
    position_size = 0.03  # 当前持仓
    current_weight = 0.68  # 当前权重68%
    target_weight = 0.50   # 目标权重50%
    weight_diff = abs(current_weight - target_weight)  # 18%
    
    print(f"📊 原始计算:")
    print(f"   当前持仓: {position_size}手")
    print(f"   当前权重: {current_weight:.1%}")
    print(f"   目标权重: {target_weight:.1%}")
    print(f"   权重差异: {weight_diff:.1%}")
    
    # 原始计算（有问题的）
    reduce_size_original = position_size * (weight_diff / current_weight)
    print(f"   原始减仓量: {reduce_size_original:.6f}手")
    
    # 修复后的计算
    reduce_size = reduce_size_original
    
    print(f"\n🔧 修复逻辑:")
    if reduce_size < 0.01:
        print(f"   检测到减仓量{reduce_size:.6f}手 < 0.01手最小单位")
        if weight_diff > 0.15:  # 权重差异超过15%
            reduce_size = 0.01
            print(f"   权重差异{weight_diff:.1%}较大，使用最小减仓量0.01手")
        else:
            print(f"   权重差异{weight_diff:.1%}较小，跳过重新平衡")
            reduce_size = 0
    else:
        # 确保是0.01的倍数
        reduce_size = round(reduce_size / 0.01) * 0.01
        reduce_size = max(0.01, reduce_size)
        print(f"   调整为0.01倍数: {reduce_size:.2f}手")
    
    # 确保不超过现有持仓
    reduce_size = min(reduce_size, position_size)
    
    print(f"\n✅ 最终结果:")
    print(f"   最终减仓量: {reduce_size:.2f}手")
    
    if reduce_size >= 0.01:
        print(f"   ✅ 符合MT4最小交易单位要求")
        if reduce_size % 0.01 == 0:
            print(f"   ✅ 是0.01手的倍数")
        else:
            print(f"   ⚠️ 不是0.01手的倍数")
    elif reduce_size == 0:
        print(f"   📝 跳过重新平衡（权重差异太小）")
    else:
        print(f"   ❌ 仍然小于最小交易单位")
    
    print("="*50)

if __name__ == "__main__":
    test_volume_calculation()
