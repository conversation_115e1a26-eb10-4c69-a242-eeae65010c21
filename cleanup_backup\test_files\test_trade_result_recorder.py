"""
测试交易结果记录器
"""
import os
import sys
import json
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    # 导入交易结果记录器
    from app.utils.trade_result_recorder import (
        record_trade_open,
        record_trade_modify,
        record_trade_close,
        format_trade_results_for_prompt,
        load_trade_results
    )
    print("成功导入交易结果记录器")
except ImportError as e:
    print(f"导入交易结果记录器失败: {e}")
    sys.exit(1)

def test_trade_result_recorder():
    """测试交易结果记录器"""
    print("开始测试交易结果记录器")
    
    # 测试记录开仓操作
    print("\n测试记录开仓操作")
    order_id = "test_order_1"
    symbol = "EURUSD"
    action = "BUY"
    order_type = "MARKET"
    entry_price = 1.12345
    stop_loss = 1.12245
    take_profit = 1.12445
    lot_size = 0.01
    
    trade_id = record_trade_open(
        order_id=order_id,
        symbol=symbol,
        action=action,
        order_type=order_type,
        entry_price=entry_price,
        stop_loss=stop_loss,
        take_profit=take_profit,
        lot_size=lot_size
    )
    
    print(f"记录开仓操作成功，交易ID: {trade_id}")
    
    # 测试记录修改订单操作
    print("\n测试记录修改订单操作")
    new_stop_loss = 1.12235
    new_take_profit = 1.12455
    
    result = record_trade_modify(
        order_id=order_id,
        new_stop_loss=new_stop_loss,
        new_take_profit=new_take_profit
    )
    
    print(f"记录修改订单操作结果: {result}")
    
    # 测试记录平仓操作
    print("\n测试记录平仓操作")
    close_price = 1.12400
    profit_loss = 55.0  # 假设盈利55美元
    
    result = record_trade_close(
        order_id=order_id,
        close_price=close_price,
        profit_loss=profit_loss,
        close_reason="TAKE_PROFIT"
    )
    
    print(f"记录平仓操作结果: {result}")
    
    # 测试格式化交易结果
    print("\n测试格式化交易结果")
    formatted_results = format_trade_results_for_prompt(5)
    print(f"格式化交易结果:\n{formatted_results}")
    
    # 测试加载交易结果
    print("\n测试加载交易结果")
    results = load_trade_results()
    print(f"加载到 {len(results)} 条交易记录")
    
    # 打印交易记录
    print("\n交易记录详情:")
    for i, trade in enumerate(results):
        print(f"\n交易 {i+1}:")
        print(f"  交易ID: {trade.get('trade_id')}")
        print(f"  订单ID: {trade.get('order_id')}")
        print(f"  交易品种: {trade.get('symbol')}")
        print(f"  交易方向: {trade.get('action')}")
        print(f"  订单类型: {trade.get('order_type')}")
        print(f"  入场价格: {trade.get('entry_price')}")
        print(f"  止损价格: {trade.get('stop_loss')}")
        print(f"  止盈价格: {trade.get('take_profit')}")
        print(f"  仓位大小: {trade.get('lot_size')}")
        print(f"  开仓时间: {trade.get('open_time')}")
        print(f"  平仓时间: {trade.get('close_time')}")
        print(f"  平仓价格: {trade.get('close_price')}")
        print(f"  盈亏金额: {trade.get('profit_loss')}")
        print(f"  状态: {trade.get('status')}")
        print(f"  平仓原因: {trade.get('close_reason')}")
        
        print("  历史记录:")
        for j, history in enumerate(trade.get('history', [])):
            print(f"    记录 {j+1}:")
            print(f"      时间: {history.get('time')}")
            print(f"      操作: {history.get('action')}")
            print(f"      详情: {history.get('details')}")

if __name__ == "__main__":
    test_trade_result_recorder()
