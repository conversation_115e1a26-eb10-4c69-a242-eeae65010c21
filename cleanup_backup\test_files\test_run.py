"""
测试运行脚本
用于测试系统是否能正常运行
"""
import os
import sys
import time
import traceback
from datetime import datetime

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'

print("=" * 50)
print("测试运行脚本")
print("=" * 50)
print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print(f"当前工作目录: {os.getcwd()}")
print(f"Python版本: {sys.version}")
print(f"系统平台: {sys.platform}")
print("=" * 50)

# 测试导入模块
try:
    print("\n测试导入模块...")
    import app
    print("导入app模块成功")
    
    from app.utils import mt4_client
    print("导入mt4_client模块成功")
    
    from app.utils import llm_client
    print("导入llm_client模块成功")
    
    from app.utils import market_change_detector
    print("导入market_change_detector模块成功")
    
    from app.utils.forex_scheduled_tasks import start_realtime_forex_analysis, stop_all_tasks
    print("导入forex_scheduled_tasks模块成功")
    
    print("\n所有模块导入成功")
except Exception as e:
    print(f"\n导入模块失败: {e}")
    traceback.print_exc()
    sys.exit(1)

# 测试MT4连接
try:
    print("\n测试MT4连接...")
    mt4_client.mt4_client.connect()
    print("MT4连接测试完成")
except Exception as e:
    print(f"MT4连接测试失败: {e}")
    traceback.print_exc()

# 测试市场数据获取
try:
    print("\n测试市场数据获取...")
    market_info = mt4_client.mt4_client.get_market_info('EURUSD')
    print(f"市场数据: {market_info}")
except Exception as e:
    print(f"市场数据获取测试失败: {e}")
    traceback.print_exc()

# 测试K线数据获取
try:
    print("\n测试K线数据获取...")
    from app.services.forex_trading_service import get_aggregated_klines
    klines = get_aggregated_klines(15, 10)
    print(f"获取到 {len(klines)} 根K线数据")
except Exception as e:
    print(f"K线数据获取测试失败: {e}")
    traceback.print_exc()

print("\n测试完成")
