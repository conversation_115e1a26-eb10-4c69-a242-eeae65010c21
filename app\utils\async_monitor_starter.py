#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步监控系统启动器
确保监控系统和主程序能够独立运行，互不阻塞
"""

import threading
import time
from datetime import datetime
from typing import Optional

class AsyncMonitorStarter:
    """异步监控系统启动器"""

    def __init__(self):
        self.monitor_thread: Optional[threading.Thread] = None
        self.analysis_thread: Optional[threading.Thread] = None
        self.monitor_started = False
        self.analysis_started = False

    def start_monitoring_system(self, interval: int = 60):
        """启动监控系统"""
        if self.monitor_started:
            print("监控系统已经启动，跳过重复启动")
            return

        def monitor_worker():
            try:
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 正在启动监控系统...")

                # 导入监控系统
                from app.utils.real_time_monitor import real_time_monitor

                # 启动监控
                real_time_monitor.start_monitoring(interval=interval)
                self.monitor_started = True

                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ✅ 监控系统启动成功")

                # 记录启动事件
                real_time_monitor.add_alert('info', 'system', '监控系统异步启动成功')

            except Exception as e:
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ 监控系统启动失败: {e}")
                import traceback
                traceback.print_exc()

        # 在独立线程中启动监控系统
        self.monitor_thread = threading.Thread(
            target=monitor_worker,
            daemon=True,
            name='MonitoringSystem'
        )
        self.monitor_thread.start()
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 监控系统正在异步启动...")

    def start_analysis_system(self, app, delay_seconds: int = 5):
        """延迟启动分析系统"""
        if self.analysis_started:
            print("分析系统已经启动，跳过重复启动")
            return

        def analysis_worker():
            try:
                # 等待指定时间，确保监控系统和Web服务器完全启动
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 分析系统将在{delay_seconds}秒后启动...")
                time.sleep(delay_seconds)

                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 正在启动分析系统...")

                # 导入配置（从app.__init__模块）
                import os
                ANALYSIS_MODE = os.environ.get('ANALYSIS_MODE', 'realtime').lower()
                REALTIME_CHECK_INTERVAL = int(os.environ.get('REALTIME_CHECK_INTERVAL', '60'))

                with app.app_context():
                    from app.utils import forex_scheduled_tasks

                    # 先停止所有现有任务
                    print('停止所有现有任务...')
                    forex_scheduled_tasks.stop_all_tasks()
                    print('所有现有任务已停止')

                    # 根据配置选择分析模式
                    if ANALYSIS_MODE == 'realtime':
                        print(f'使用实时分析模式，检查间隔: {REALTIME_CHECK_INTERVAL}秒')
                        print('系统将通过智能货币对选择和定期分析机制自动开始分析')
                        forex_scheduled_tasks.start_realtime_forex_analysis(
                            run_immediately=False,  # 移除启动时强制分析，让定期分析机制自然接管
                            auto_trade=True,
                            check_interval=REALTIME_CHECK_INTERVAL
                        )
                    elif ANALYSIS_MODE == 'hybrid':
                        print(f'使用混合分析模式（定时+实时），检查间隔: {REALTIME_CHECK_INTERVAL}秒')
                        print('系统将通过智能货币对选择和定期分析机制自动开始分析')
                        forex_scheduled_tasks.start_realtime_forex_analysis(
                            run_immediately=False,  # 移除启动时强制分析
                            auto_trade=True,
                            check_interval=REALTIME_CHECK_INTERVAL,
                            hourly_force_analysis=True
                        )
                    else:
                        print('使用每小时分析模式')
                        print('系统将通过智能货币对选择和定期分析机制自动开始分析')
                        forex_scheduled_tasks.start_hourly_forex_analysis(
                            run_immediately=False,  # 移除启动时强制分析
                            auto_trade=True
                        )

                    self.analysis_started = True
                    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ✅ 分析系统启动成功")

                    # 记录启动事件到监控系统
                    try:
                        from app.utils.real_time_monitor import real_time_monitor
                        real_time_monitor.add_alert('info', 'system', '分析系统异步启动成功')
                    except:
                        pass

            except Exception as e:
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ 分析系统启动失败: {e}")
                import traceback
                traceback.print_exc()

                # 记录错误到监控系统
                try:
                    from app.utils.real_time_monitor import real_time_monitor
                    real_time_monitor.add_alert('error', 'system', f'分析系统启动失败: {str(e)}')
                except:
                    pass

        # 在独立线程中启动分析系统
        self.analysis_thread = threading.Thread(
            target=analysis_worker,
            daemon=True,
            name='AnalysisSystem'
        )
        self.analysis_thread.start()

    def get_status(self):
        """获取启动状态"""
        return {
            'monitor_started': self.monitor_started,
            'analysis_started': self.analysis_started,
            'monitor_thread_alive': self.monitor_thread.is_alive() if self.monitor_thread else False,
            'analysis_thread_alive': self.analysis_thread.is_alive() if self.analysis_thread else False,
            'timestamp': datetime.now().isoformat()
        }

    def wait_for_systems(self, timeout: int = 30):
        """等待系统启动完成"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if self.monitor_started and self.analysis_started:
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ✅ 所有系统启动完成")
                return True
            time.sleep(1)

        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ⚠️ 系统启动超时")
        return False

    def stop_all_systems(self):
        """停止所有系统"""
        try:
            # 停止监控系统
            if self.monitor_started:
                from app.utils.real_time_monitor import real_time_monitor
                real_time_monitor.stop_monitoring()
                self.monitor_started = False
                print("监控系统已停止")

            # 停止分析系统
            if self.analysis_started:
                from app.utils import forex_scheduled_tasks
                forex_scheduled_tasks.stop_all_tasks()
                self.analysis_started = False
                print("分析系统已停止")

        except Exception as e:
            print(f"停止系统时出错: {e}")

# 创建全局实例
async_monitor_starter = AsyncMonitorStarter()

def start_all_systems_async(app, monitor_interval: int = 60, analysis_delay: int = 5):
    """异步启动所有系统"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🚀 开始异步启动所有系统...")

    # 先启动监控系统
    async_monitor_starter.start_monitoring_system(interval=monitor_interval)

    # 延迟启动分析系统
    async_monitor_starter.start_analysis_system(app, delay_seconds=analysis_delay)

    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 📊 监控面板地址: http://localhost:5000/monitoring/")
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🔄 系统将在后台异步启动，互不阻塞")

    return async_monitor_starter

def get_systems_status():
    """获取系统状态"""
    return async_monitor_starter.get_status()

def stop_all_systems():
    """停止所有系统"""
    async_monitor_starter.stop_all_systems()
