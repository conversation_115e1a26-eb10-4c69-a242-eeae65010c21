"""
专业投资组合策略
基于现代投资组合理论和量化交易最佳实践
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

class MarketRegime(Enum):
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"

class PortfolioAction(Enum):
    ENTER_LONG = "enter_long"
    ENTER_SHORT = "enter_short"
    EXIT_POSITION = "exit_position"
    HEDGE_POSITION = "hedge_position"
    REBALANCE = "rebalance"
    HOLD = "hold"

@dataclass
class CurrencyPairAnalysis:
    symbol: str
    trend_strength: float
    volatility: float
    momentum: float
    support_resistance: Dict
    correlation_score: float
    market_regime: MarketRegime

@dataclass
class PortfolioDecision:
    action: PortfolioAction
    symbol: str
    size: float
    entry_price: float
    stop_loss: float
    take_profit: float
    confidence: float
    reasoning: str
    risk_reward_ratio: float

class ProfessionalPortfolioStrategy:
    """
    专业投资组合策略

    核心理念：
    1. 基于货币相关性的组合构建
    2. 动态风险平价
    3. 多时间框架确认
    4. 智能仓位管理
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 货币相关性矩阵（基于历史数据，移除黄金，完善7个外汇货币对）
        self.correlation_matrix = {
            'EURUSD': {'GBPUSD': 0.85, 'AUDUSD': 0.75, 'NZDUSD': 0.70, 'USDCHF': -0.80, 'USDCAD': -0.65, 'USDJPY': -0.45},
            'GBPUSD': {'EURUSD': 0.85, 'AUDUSD': 0.70, 'NZDUSD': 0.65, 'USDCHF': -0.75, 'USDCAD': -0.60, 'USDJPY': -0.40},
            'AUDUSD': {'EURUSD': 0.75, 'GBPUSD': 0.70, 'NZDUSD': 0.85, 'USDCHF': -0.65, 'USDCAD': 0.60, 'USDJPY': -0.35},
            'NZDUSD': {'EURUSD': 0.70, 'GBPUSD': 0.65, 'AUDUSD': 0.85, 'USDCHF': -0.60, 'USDCAD': 0.55, 'USDJPY': -0.30},
            'USDCHF': {'EURUSD': -0.80, 'GBPUSD': -0.75, 'AUDUSD': -0.65, 'NZDUSD': -0.60, 'USDCAD': -0.50, 'USDJPY': 0.60},
            'USDCAD': {'EURUSD': -0.65, 'GBPUSD': -0.60, 'AUDUSD': 0.60, 'NZDUSD': 0.55, 'USDCHF': -0.50, 'USDJPY': -0.25},
            'USDJPY': {'EURUSD': -0.45, 'GBPUSD': -0.40, 'AUDUSD': -0.35, 'NZDUSD': -0.30, 'USDCHF': 0.60, 'USDCAD': -0.25}
        }

        # 策略参数
        self.max_portfolio_risk = 0.02  # 最大组合风险2%
        self.max_correlation_exposure = 0.70  # 最大相关性暴露
        self.rebalance_threshold = 0.05  # 重新平衡阈值
        self.min_confidence_threshold = 0.65  # 最小置信度阈值

        # 当前持仓状态
        self.current_positions = {}
        self.portfolio_metrics = {}

        # 基本面分析集成
        self.enable_fundamental_analysis = True
        self.fundamental_weight = 0.3  # 基本面分析权重30%

        # 交易时机控制
        self.last_trade_times = {}  # 记录每个货币对的最后交易时间
        self.min_trade_interval = 30  # 最小交易间隔（分钟）
        self.max_concurrent_trades = 3  # 最大同时持仓数量
        self.max_same_direction_trades = 2  # 同方向最大持仓数量

    def analyze_market_regime(self, market_data: Dict) -> Dict[str, MarketRegime]:
        """分析市场状态"""
        regimes = {}

        for symbol, data in market_data.items():
            if 'ohlcv' not in data or data['ohlcv'].empty:
                continue

            df = data['ohlcv']

            # 计算趋势强度
            sma_20 = df['close'].rolling(20).mean()
            sma_50 = df['close'].rolling(50).mean()
            trend_strength = (sma_20.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]

            # 计算波动率
            returns = df['close'].pct_change()
            volatility = returns.rolling(20).std().iloc[-1] * np.sqrt(252)

            # 判断市场状态
            if abs(trend_strength) > 0.02:
                if trend_strength > 0:
                    regime = MarketRegime.TRENDING_UP
                else:
                    regime = MarketRegime.TRENDING_DOWN
            elif volatility > 0.20:
                regime = MarketRegime.HIGH_VOLATILITY
            elif volatility < 0.10:
                regime = MarketRegime.LOW_VOLATILITY
            else:
                regime = MarketRegime.RANGING

            regimes[symbol] = regime

        return regimes

    def calculate_portfolio_correlation(self, positions: Dict) -> float:
        """计算投资组合相关性"""
        if len(positions) < 2:
            return 0.0

        total_correlation = 0.0
        pair_count = 0

        symbols = list(positions.keys())
        for i in range(len(symbols)):
            for j in range(i + 1, len(symbols)):
                symbol1, symbol2 = symbols[i], symbols[j]

                # 获取相关性
                correlation = self.correlation_matrix.get(symbol1, {}).get(symbol2, 0.0)

                # 权重调整
                weight1 = positions[symbol1].get('weight', 0.0)
                weight2 = positions[symbol2].get('weight', 0.0)

                total_correlation += correlation * weight1 * weight2
                pair_count += 1

        return total_correlation / max(pair_count, 1)

    def generate_portfolio_decisions(self, market_data: Dict, current_positions: Dict, risk_adjustment: Dict = None) -> List[PortfolioDecision]:
        """生成投资组合决策"""
        try:
            decisions = []

            # 应用风险调整参数
            if risk_adjustment:
                position_multiplier = risk_adjustment.get('position_multiplier', 1.0)
                risk_level = risk_adjustment.get('risk_level', 'MEDIUM')

                # 根据风险等级调整策略参数
                if risk_level == 'HIGH':
                    self.min_confidence_threshold = 0.8  # 提高置信度要求
                elif risk_level == 'LOW':
                    self.min_confidence_threshold = 0.5  # 降低置信度要求
            else:
                position_multiplier = 1.0

            # 1. 分析市场状态
            market_regimes = self.analyze_market_regime(market_data)

            # 2. 分析每个货币对
            pair_analyses = {}
            for symbol in market_data.keys():
                analysis = self._analyze_currency_pair(symbol, market_data[symbol], market_regimes.get(symbol))
                if analysis:
                    pair_analyses[symbol] = analysis

            # 3. 检查当前组合风险
            portfolio_risk = self._calculate_portfolio_risk(current_positions, pair_analyses)

            # 4. 生成交易决策
            if portfolio_risk > self.max_portfolio_risk:
                # 风险过高，减仓或对冲
                decisions.extend(self._generate_risk_reduction_decisions(current_positions, pair_analyses))
            else:
                # 寻找新的交易机会
                opportunity_decisions = self._generate_opportunity_decisions(pair_analyses, current_positions)
                self.logger.info(f"📊 生成了{len(opportunity_decisions)}个交易机会决策")
                decisions.extend(opportunity_decisions)

            # 5. 检查组合平衡
            rebalance_decisions = self._check_rebalancing_needs(current_positions, pair_analyses)
            decisions.extend(rebalance_decisions)

            # 6. 应用风险调整到仓位大小
            if position_multiplier != 1.0:
                for decision in decisions:
                    decision.size *= position_multiplier

            # 7. 过滤低置信度决策
            decisions = [d for d in decisions if d.confidence >= self.min_confidence_threshold]

            return decisions

        except Exception as e:
            self.logger.error(f"生成投资组合决策失败: {e}")
            return []

    def _analyze_currency_pair(self, symbol: str, data: Dict, regime: MarketRegime) -> Optional[CurrencyPairAnalysis]:
        """分析单个货币对"""
        try:
            if 'ohlcv' not in data or data['ohlcv'].empty:
                return None

            df = data['ohlcv']

            # 趋势强度分析
            trend_strength = self._calculate_trend_strength(df)

            # 波动率分析
            volatility = self._calculate_volatility(df)

            # 动量分析
            momentum = self._calculate_momentum(df)

            # 支撑阻力分析
            support_resistance = self._find_support_resistance(df)

            # 相关性评分
            correlation_score = self._calculate_correlation_score(symbol)

            return CurrencyPairAnalysis(
                symbol=symbol,
                trend_strength=trend_strength,
                volatility=volatility,
                momentum=momentum,
                support_resistance=support_resistance,
                correlation_score=correlation_score,
                market_regime=regime
            )

        except Exception as e:
            self.logger.error(f"分析货币对{symbol}失败: {e}")
            return None

    def _calculate_trend_strength(self, df: pd.DataFrame) -> float:
        """计算趋势强度"""
        # 使用多个时间框架的移动平均线
        sma_10 = df['close'].rolling(10).mean()
        sma_20 = df['close'].rolling(20).mean()
        sma_50 = df['close'].rolling(50).mean()

        current_price = df['close'].iloc[-1]

        # 计算价格相对于均线的位置
        trend_score = 0.0

        if current_price > sma_10.iloc[-1]:
            trend_score += 0.3
        if current_price > sma_20.iloc[-1]:
            trend_score += 0.4
        if current_price > sma_50.iloc[-1]:
            trend_score += 0.3

        # 计算均线排列
        if sma_10.iloc[-1] > sma_20.iloc[-1] > sma_50.iloc[-1]:
            trend_score += 0.5  # 多头排列
        elif sma_10.iloc[-1] < sma_20.iloc[-1] < sma_50.iloc[-1]:
            trend_score -= 0.5  # 空头排列

        return max(-1.0, min(1.0, trend_score))

    def _calculate_volatility(self, df: pd.DataFrame) -> float:
        """计算波动率"""
        returns = df['close'].pct_change().dropna()
        return returns.rolling(20).std().iloc[-1] * np.sqrt(252)

    def _calculate_momentum(self, df: pd.DataFrame) -> float:
        """计算动量"""
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 标准化到-1到1
        return (rsi.iloc[-1] - 50) / 50

    def _find_support_resistance(self, df: pd.DataFrame) -> Dict:
        """寻找支撑阻力位"""
        try:
            current_price = df['close'].iloc[-1]

            # 计算ATR用于动态支撑阻力
            atr = self._calculate_atr(df)

            # 寻找近期关键价位（5-10天内）
            recent_highs = df['high'].rolling(5, center=True).max().dropna()
            recent_lows = df['low'].rolling(5, center=True).min().dropna()

            # 过滤出当前价格附近的关键位
            price_range = atr * 3  # ATR的3倍作为有效范围

            # 寻找支撑位（当前价格下方）
            valid_supports = recent_lows[
                (recent_lows < current_price) &
                (recent_lows > current_price - price_range)
            ]

            # 寻找阻力位（当前价格上方）
            valid_resistances = recent_highs[
                (recent_highs > current_price) &
                (recent_highs < current_price + price_range)
            ]

            # 选择最近的支撑阻力位
            if len(valid_supports) > 0:
                support = valid_supports.iloc[-1]  # 最近的支撑
            else:
                support = current_price - atr * 2  # 默认支撑

            if len(valid_resistances) > 0:
                resistance = valid_resistances.iloc[-1]  # 最近的阻力
            else:
                resistance = current_price + atr * 2  # 默认阻力

            return {
                'resistance': resistance,
                'support': support,
                'current_price': current_price,
                'atr': atr
            }

        except Exception as e:
            self.logger.error(f"计算支撑阻力位失败: {e}")
            current_price = df['close'].iloc[-1]
            atr = 0.002  # 默认ATR
            return {
                'resistance': current_price + atr,
                'support': current_price - atr,
                'current_price': current_price,
                'atr': atr
            }

    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """计算平均真实波幅"""
        try:
            if len(df) < period + 1:
                return 0.002  # 默认ATR

            high = df['high']
            low = df['low']
            close = df['close']

            # 计算真实波幅
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))

            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = tr.rolling(period).mean().iloc[-1]

            return atr if not pd.isna(atr) else 0.002

        except Exception:
            return 0.002

    def _calculate_correlation_score(self, symbol: str) -> float:
        """计算相关性评分"""
        # 基于当前持仓计算相关性风险
        if not self.current_positions:
            return 0.0

        total_correlation = 0.0
        for pos_symbol in self.current_positions.keys():
            correlation = self.correlation_matrix.get(symbol, {}).get(pos_symbol, 0.0)
            total_correlation += abs(correlation)

        return total_correlation / len(self.current_positions)

    def _calculate_portfolio_risk(self, positions: Dict, analyses: Dict) -> float:
        """计算投资组合风险"""
        if not positions:
            return 0.0

        total_risk = 0.0
        for symbol, position in positions.items():
            if symbol in analyses:
                analysis = analyses[symbol]
                # 确保position是字典类型
                if isinstance(position, dict):
                    position_size = position.get('size', 0.0)
                else:
                    # 如果position不是字典，使用默认值
                    position_size = 0.01  # 默认仓位大小

                position_risk = position_size * analysis.volatility
                total_risk += position_risk ** 2

        return np.sqrt(total_risk)

    def _generate_opportunity_decisions(self, analyses: Dict, current_positions: Dict) -> List[PortfolioDecision]:
        """生成交易机会决策（集成组合交易管理）"""
        try:
            # 导入组合交易管理器
            from core.portfolio_manager.combo_trading_manager import combo_trading_manager

            # 1. 为每个货币对生成基础交易信号
            base_signals = []
            for symbol, analysis in analyses.items():
                # 跳过已有持仓的货币对（避免重复开仓）
                if symbol in current_positions:
                    continue

                # 检查相关性风险
                if analysis.correlation_score > self.max_correlation_exposure:
                    continue

                # 生成基础信号
                signal = self._generate_trade_signal(analysis)
                if signal:
                    # 转换为组合管理器需要的格式
                    signal_dict = {
                        'symbol': signal.symbol,
                        'action': signal.action.value if hasattr(signal.action, 'value') else str(signal.action),
                        'position_size': signal.size,
                        'confidence': signal.confidence,
                        'signal_strength': (analysis.trend_strength + analysis.momentum) / 2,
                        'entry_price': signal.entry_price,
                        'stop_loss': signal.stop_loss,
                        'take_profit': signal.take_profit
                    }
                    base_signals.append(signal_dict)

            # 2. 如果没有基础信号，返回空列表
            if not base_signals:
                return []

            # 3. 使用组合交易管理器分析交易机会
            combo_decision = combo_trading_manager.analyze_combo_opportunities(
                base_signals, current_positions
            )

            # 4. 转换组合决策为策略决策
            decisions = []

            if combo_decision.action == 'create_combo':
                self.logger.info(f"🎯 组合交易决策: {combo_decision.reason}")
                self.logger.info(f"   置信度: {combo_decision.confidence:.1%}")
                self.logger.info(f"   预期收益: {combo_decision.expected_return:.1%}")
                self.logger.info(f"   最大风险: {combo_decision.max_risk:.1%}")

                for combo_trade in combo_decision.combo_trades:
                    self.logger.info(f"📊 组合类型: {combo_trade.combo_type.value}")
                    self.logger.info(f"   描述: {combo_trade.description}")
                    self.logger.info(f"   货币对: {combo_trade.symbols}")
                    self.logger.info(f"   方向: {combo_trade.directions}")
                    self.logger.info(f"   仓位: {combo_trade.position_sizes}")

                    # 为组合中的每个交易创建决策
                    for i, symbol in enumerate(combo_trade.symbols):
                        if i < len(combo_trade.directions) and i < len(combo_trade.position_sizes):
                            # 获取对应的分析数据
                            analysis = analyses.get(symbol)
                            if analysis:
                                # 确定交易动作
                                direction = combo_trade.directions[i]
                                if direction == 'long':
                                    action = PortfolioAction.ENTER_LONG
                                elif direction == 'short':
                                    action = PortfolioAction.ENTER_SHORT
                                else:
                                    continue

                                # 计算入场价格和止损止盈
                                current_price = analysis.support_resistance['current_price']
                                stop_loss, take_profit = self._calculate_practical_stop_take_profit(
                                    current_price, action, analysis.volatility, symbol
                                )

                                # 创建决策
                                decision = PortfolioDecision(
                                    action=action,
                                    symbol=symbol,
                                    size=combo_trade.position_sizes[i],
                                    entry_price=current_price,
                                    stop_loss=stop_loss,
                                    take_profit=take_profit,
                                    confidence=combo_decision.confidence,
                                    reasoning=f"组合交易: {combo_trade.description}",
                                    risk_reward_ratio=abs(take_profit - current_price) / abs(current_price - stop_loss) if abs(current_price - stop_loss) > 0 else 1.8
                                )
                                decisions.append(decision)
            else:
                # 如果组合管理器建议持有，返回原始的单一信号
                self.logger.info(f"📋 组合管理器建议: {combo_decision.reason}")
                for signal_dict in base_signals:
                    # 找到对应的原始信号
                    for symbol, analysis in analyses.items():
                        if symbol == signal_dict['symbol']:
                            original_signal = self._generate_trade_signal(analysis)
                            if original_signal:
                                decisions.append(original_signal)
                            break

            return decisions

        except Exception as e:
            self.logger.error(f"生成组合交易机会失败: {e}")
            # 降级到原始逻辑
            return self._generate_fallback_opportunity_decisions(analyses, current_positions)

    def _generate_fallback_opportunity_decisions(self, analyses: Dict, current_positions: Dict) -> List[PortfolioDecision]:
        """降级的交易机会决策（原始逻辑）"""
        decisions = []

        for symbol, analysis in analyses.items():
            # 跳过已有持仓的货币对（避免重复开仓）
            if symbol in current_positions:
                continue

            # 检查相关性风险
            if analysis.correlation_score > self.max_correlation_exposure:
                continue

            # 生成交易信号
            decision = self._generate_trade_signal(analysis)
            if decision:
                decisions.append(decision)

        return decisions

    def _generate_trade_signal(self, analysis: CurrencyPairAnalysis) -> Optional[PortfolioDecision]:
        """生成交易信号（集成基本面分析）"""
        try:
            # 1. 技术面分析
            technical_strength = (analysis.trend_strength + analysis.momentum) / 2
            technical_confidence = abs(technical_strength) * (1 - analysis.correlation_score)

            # 2. 基本面分析（如果启用）
            fundamental_strength = 0.0
            fundamental_confidence = 0.0
            event_risk_adjustment = 1.0
            reasoning_parts = [f"技术信号强度{technical_strength:.3f}"]

            if self.enable_fundamental_analysis:
                try:
                    # 导入基本面分析模块
                    from core.news_engine.fundamental_analysis_engine import fundamental_engine
                    from core.news_engine.event_driven_trading import event_trading_manager

                    # 获取基本面分析
                    fundamental_analysis = fundamental_engine.analyze_fundamentals(analysis.symbol, 'short')

                    # 转换基本面信号为数值
                    signal_values = {
                        'strong_bullish': 0.8,
                        'bullish': 0.4,
                        'neutral': 0.0,
                        'bearish': -0.4,
                        'strong_bearish': -0.8
                    }
                    fundamental_strength = signal_values.get(fundamental_analysis.signal.value, 0.0)
                    fundamental_confidence = fundamental_analysis.confidence

                    # 获取事件交易信号
                    event_signal = event_trading_manager.analyze_event_trading_opportunity(
                        analysis.symbol, self.current_positions
                    )

                    # 事件风险调整
                    if event_signal.action.value == 'avoid_trading':
                        return None  # 避免交易
                    elif event_signal.action.value == 'reduce_position':
                        event_risk_adjustment = 0.5  # 减少仓位
                    else:
                        event_risk_adjustment = event_signal.position_size_multiplier

                    # 更新推理说明
                    if fundamental_confidence > 0:
                        reasoning_parts.append(f"基本面{fundamental_strength:.2f}")
                    if event_risk_adjustment != 1.0:
                        reasoning_parts.append(f"事件调整×{event_risk_adjustment:.2f}")

                    self.logger.info(f"基本面分析 {analysis.symbol}: "
                                   f"信号={fundamental_analysis.signal.value}, "
                                   f"强度={fundamental_strength:.2f}, "
                                   f"置信度={fundamental_confidence:.2f}")

                except Exception as e:
                    self.logger.warning(f"基本面分析失败 {analysis.symbol}: {e}")
                    # 继续使用技术分析

            # 3. 综合信号强度
            if self.enable_fundamental_analysis and fundamental_confidence > 0:
                # 技术面70% + 基本面30%
                combined_strength = (technical_strength * 0.7 + fundamental_strength * 0.3)
                combined_confidence = (technical_confidence * 0.7 + fundamental_confidence * 0.3)
            else:
                combined_strength = technical_strength
                combined_confidence = technical_confidence

            # 4. 时间段调整
            time_multiplier = self._get_session_multiplier()
            final_confidence = combined_confidence * time_multiplier

            if final_confidence < self.min_confidence_threshold:
                return None

            # 5. 确定交易方向
            if combined_strength > 0.3:
                action = PortfolioAction.ENTER_LONG
            elif combined_strength < -0.3:
                action = PortfolioAction.ENTER_SHORT
            else:
                return None

            # 6. 计算入场价格和止损止盈
            current_price = analysis.support_resistance['current_price']
            entry_price = current_price
            atr = analysis.support_resistance.get('atr', 0.002)

            # 基于ATR的实用止损止盈计算
            stop_loss, take_profit = self._calculate_practical_stop_take_profit(
                entry_price, action, atr, analysis.symbol
            )

            # 7. 计算仓位大小（应用事件风险调整）
            base_position_size = self._calculate_optimal_position_size(
                analysis, entry_price, stop_loss, final_confidence
            )
            adjusted_position_size = base_position_size * event_risk_adjustment

            # 8. 生成最终推理说明
            reasoning = ", ".join(reasoning_parts) + f", 置信度{final_confidence:.3f}"

            return PortfolioDecision(
                action=action,
                symbol=analysis.symbol,
                size=adjusted_position_size,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                confidence=final_confidence,
                reasoning=reasoning,
                risk_reward_ratio=abs(take_profit - entry_price) / abs(entry_price - stop_loss)
            )

        except Exception as e:
            self.logger.error(f"生成交易信号失败 {analysis.symbol}: {e}")
            return None

    def _generate_risk_reduction_decisions(self, positions: Dict, analyses: Dict) -> List[PortfolioDecision]:
        """生成风险降低决策"""
        decisions = []

        # 找出风险最高的持仓
        risk_scores = {}
        for symbol, position in positions.items():
            if symbol in analyses:
                analysis = analyses[symbol]
                risk_score = position.get('size', 0.0) * analysis.volatility * analysis.correlation_score
                risk_scores[symbol] = risk_score

        # 按风险排序
        sorted_risks = sorted(risk_scores.items(), key=lambda x: x[1], reverse=True)

        # 减少最高风险的持仓
        for symbol, risk_score in sorted_risks[:2]:  # 最多处理2个最高风险持仓
            position = positions[symbol]
            if isinstance(position, dict):
                position_size = position.get('size', 0.0) * 0.5  # 减仓50%
            else:
                position_size = 0.005  # 默认减仓大小

            decision = PortfolioDecision(
                action=PortfolioAction.EXIT_POSITION,
                symbol=symbol,
                size=position_size,
                entry_price=0.0,
                stop_loss=0.0,
                take_profit=0.0,
                confidence=0.8,
                reasoning=f"风险管理：降低高风险持仓，风险评分: {risk_score:.3f}",
                risk_reward_ratio=0.0
            )
            decisions.append(decision)

        return decisions

    def _check_rebalancing_needs(self, positions: Dict, analyses: Dict) -> List[PortfolioDecision]:
        """检查重新平衡需求"""
        decisions = []

        if not positions:
            return decisions

        # 提取真实的货币对持仓数据
        real_positions = {}
        if 'positions_by_symbol' in positions:
            # 使用positions_by_symbol中的真实货币对数据
            for symbol, symbol_positions in positions['positions_by_symbol'].items():
                if symbol_positions and len(symbol_positions) > 0:
                    # 计算该货币对的总仓位
                    total_size = sum(pos.get('volume', 0) for pos in symbol_positions)
                    total_value = sum(pos.get('volume', 0) * pos.get('entry_price', 0) for pos in symbol_positions)

                    real_positions[symbol] = {
                        'size': total_size,
                        'value': total_value,
                        'positions': symbol_positions
                    }
        else:
            # 兼容旧格式：直接使用positions中的货币对数据
            for symbol, position in positions.items():
                # 只处理真实的货币对（跳过统计数据）
                if symbol in ['total_positions', 'total_profit', 'raw_positions', 'positions_by_symbol']:
                    continue

                if isinstance(position, dict) and 'orders' in position:
                    real_positions[symbol] = position

        if not real_positions:
            return decisions

        # 计算当前权重
        total_value = 0.0
        for pos in real_positions.values():
            total_value += abs(pos.get('value', 0.0))

        if total_value == 0:
            return decisions

        # 检查是否需要重新平衡
        for symbol, position in real_positions.items():
            current_weight = abs(position.get('value', 0.0)) / total_value
            position_size = position.get('size', 0.0)

            target_weight = 1.0 / len(real_positions)  # 等权重

            weight_diff = abs(current_weight - target_weight)
            if weight_diff > self.rebalance_threshold:
                # 需要重新平衡
                if current_weight > target_weight:
                    # 减仓 - 确保符合MT4最小交易单位
                    reduce_size = position_size * (weight_diff / current_weight) if current_weight > 0 else 0.01

                    # 关键修复：确保减仓量符合MT4最小交易单位0.01手
                    if reduce_size < 0.01:
                        # 如果计算出的减仓量小于0.01手，有两个选择：
                        # 1. 跳过这次重新平衡（权重差异太小）
                        # 2. 使用最小单位0.01手
                        if weight_diff > 0.15:  # 权重差异超过15%才强制重新平衡
                            reduce_size = 0.01
                            self.logger.info(f"权重差异{weight_diff:.1%}较大，使用最小减仓量0.01手")
                        else:
                            self.logger.info(f"权重差异{weight_diff:.1%}较小，跳过重新平衡（避免小额交易）")
                            continue
                    else:
                        # 确保是0.01的倍数
                        reduce_size = round(reduce_size / 0.01) * 0.01
                        reduce_size = max(0.01, reduce_size)  # 确保不小于最小单位

                    # 确保不超过现有持仓
                    reduce_size = min(reduce_size, position_size)

                    decision = PortfolioDecision(
                        action=PortfolioAction.REBALANCE,
                        symbol=symbol,
                        size=-reduce_size,  # 负数表示减仓
                        entry_price=0.0,
                        stop_loss=0.0,
                        take_profit=0.0,
                        confidence=0.7,
                        reasoning=f"重新平衡：当前权重{current_weight:.2%}，目标权重{target_weight:.2%}，减仓{reduce_size:.2f}手",
                        risk_reward_ratio=0.0
                    )
                    decisions.append(decision)
                    self.logger.info(f"生成重新平衡决策: {symbol} 减仓{reduce_size:.2f}手 (权重{current_weight:.1%}→{target_weight:.1%})")

        return decisions

    def _calculate_optimal_position_size(self, analysis: CurrencyPairAnalysis,
                                       entry_price: float, stop_loss: float,
                                       confidence: float) -> float:
        """计算最优仓位大小

        基于专业风险管理原则：
        1. 固定风险百分比（每笔交易风险1-2%）
        2. 信号强度调整
        3. 波动率调整
        4. 相关性风险控制
        5. 资金利用率管理
        """
        try:
            # 1. 基础参数设置（针对1万美金账户）
            account_balance = 10000.0  # 1万美金标准账户
            max_risk_per_trade = 0.01  # 每笔交易最大风险1%（保守）
            max_position_value_ratio = 0.15  # 单笔交易最大占用资金15%

            # 2. 计算基于风险的仓位大小
            risk_amount = account_balance * max_risk_per_trade  # 100美金风险
            stop_distance = abs(entry_price - stop_loss)

            # 防止除零错误
            if stop_distance <= 0:
                return 0.01  # 默认最小仓位

            # 3. 计算点值和止损点数
            if analysis.symbol == 'USDJPY':
                # 日元对：1点 = 0.01，0.01手的点值 = 0.1美元
                pip_size = 0.01
                pip_value_per_mini_lot = 0.1  # 0.01手每点价值
                stop_pips = stop_distance / pip_size
            else:
                # 其他货币对：1点 = 0.0001，0.01手的点值 = 0.1美元
                pip_size = 0.0001
                pip_value_per_mini_lot = 0.1  # 0.01手每点价值
                stop_pips = stop_distance / pip_size

            # 4. 基于风险金额计算仓位（以0.01手为单位）
            # 风险金额 = 仓位手数 × 止损点数 × 每点价值
            # 仓位手数 = 风险金额 / (止损点数 × 每点价值)
            risk_based_lots = risk_amount / (stop_pips * pip_value_per_mini_lot)

            # 5. 资金占用限制检查
            position_value = risk_based_lots * 1000  # 0.01手 ≈ 1000美元名义价值
            max_position_value = account_balance * max_position_value_ratio  # 1500美元

            if position_value > max_position_value:
                # 如果超过资金占用限制，按资金限制计算
                risk_based_lots = max_position_value / 1000

            # 6. 信号强度调整（置信度越高，仓位越大）
            confidence_multiplier = 0.7 + (confidence * 0.6)  # 0.7-1.3倍

            # 7. 波动率调整（高波动率降低仓位）
            volatility_multiplier = max(0.5, min(1.2, 0.15 / analysis.volatility))

            # 8. 趋势强度调整
            trend_multiplier = 0.8 + (abs(analysis.trend_strength) * 0.4)  # 0.8-1.2倍

            # 9. 相关性风险调整
            correlation_multiplier = max(0.7, 1.0 - analysis.correlation_score * 0.3)

            # 10. 综合计算最终仓位
            final_size = (risk_based_lots *
                         confidence_multiplier *
                         volatility_multiplier *
                         trend_multiplier *
                         correlation_multiplier)

            # 11. 仓位限制（适合1万美金账户）
            min_size = 0.01  # 最小仓位
            max_size = 0.05  # 最大仓位5%（1万美金账户的合理上限）

            # 确保在合理范围内
            final_size = max(min_size, min(max_size, final_size))

            # 12. 精度调整（0.01的倍数）
            final_size = round(final_size / 0.01) * 0.01

            # 13. 最终资金占用检查
            final_position_value = final_size * 1000
            max_total_exposure = account_balance * 0.3  # 总敞口不超过30%

            self.logger.info(f"仓位计算 {analysis.symbol}: "
                           f"风险基础={risk_based_lots:.3f}, "
                           f"置信度×{confidence_multiplier:.2f}, "
                           f"波动率×{volatility_multiplier:.2f}, "
                           f"趋势×{trend_multiplier:.2f}, "
                           f"相关性×{correlation_multiplier:.2f}, "
                           f"最终={final_size:.2f}手 "
                           f"(${final_position_value:.0f})")

            return final_size

        except Exception as e:
            self.logger.error(f"计算最优仓位失败: {e}")
            # 返回保守的默认仓位
            return 0.01

    def _calculate_practical_stop_take_profit(self, entry_price: float, action: PortfolioAction,
                                            atr: float, symbol: str) -> tuple:
        """计算实用的止损止盈

        基于以下原则：
        1. 止损距离基于ATR，通常1.5-2.5倍ATR
        2. 止盈距离为止损的1.5-2倍
        3. 考虑不同货币对的特性
        4. 确保合理的点数范围
        """
        try:
            # 货币对特定配置（移除黄金，保留7个外汇货币对）
            currency_configs = {
                'EURUSD': {'min_stop_pips': 15, 'max_stop_pips': 30, 'pip_value': 0.0001},
                'GBPUSD': {'min_stop_pips': 18, 'max_stop_pips': 35, 'pip_value': 0.0001},
                'AUDUSD': {'min_stop_pips': 16, 'max_stop_pips': 32, 'pip_value': 0.0001},
                'NZDUSD': {'min_stop_pips': 18, 'max_stop_pips': 35, 'pip_value': 0.0001},
                'USDCHF': {'min_stop_pips': 15, 'max_stop_pips': 30, 'pip_value': 0.0001},
                'USDCAD': {'min_stop_pips': 16, 'max_stop_pips': 32, 'pip_value': 0.0001},
                'USDJPY': {'min_stop_pips': 15, 'max_stop_pips': 30, 'pip_value': 0.01}
            }

            config = currency_configs.get(symbol, {
                'min_stop_pips': 15, 'max_stop_pips': 30, 'pip_value': 0.0001
            })

            pip_value = config['pip_value']
            min_stop_pips = config['min_stop_pips']
            max_stop_pips = config['max_stop_pips']

            # 基于ATR计算止损距离
            atr_multiplier = 2.0  # 2倍ATR作为止损
            stop_distance = atr * atr_multiplier

            # 转换为点数并限制范围
            stop_pips = stop_distance / pip_value
            stop_pips = max(min_stop_pips, min(max_stop_pips, stop_pips))

            # 转换回价格距离
            final_stop_distance = stop_pips * pip_value

            # 动态风险回报比计算
            risk_reward_ratio = self._calculate_dynamic_risk_reward_ratio(symbol, atr)
            take_profit_distance = final_stop_distance * risk_reward_ratio

            if action == PortfolioAction.ENTER_LONG:
                stop_loss = entry_price - final_stop_distance
                take_profit = entry_price + take_profit_distance
            else:  # ENTER_SHORT
                stop_loss = entry_price + final_stop_distance
                take_profit = entry_price - take_profit_distance

            # 确保价格精度
            if symbol == 'USDJPY':
                stop_loss = round(stop_loss, 3)
                take_profit = round(take_profit, 3)
            else:
                stop_loss = round(stop_loss, 5)
                take_profit = round(take_profit, 5)

            return stop_loss, take_profit

        except Exception as e:
            self.logger.error(f"计算止损止盈失败: {e}")
            # 返回保守的默认值（20点止损，36点止盈）
            default_stop_distance = 20 * 0.0001  # 20点
            default_tp_distance = 36 * 0.0001    # 36点

            if action == PortfolioAction.ENTER_LONG:
                return entry_price - default_stop_distance, entry_price + default_tp_distance
            else:
                return entry_price + default_stop_distance, entry_price - default_tp_distance

    def _calculate_dynamic_risk_reward_ratio(self, symbol: str, atr: float) -> float:
        """计算动态风险回报比

        基于市场条件动态调整：
        1. 趋势市场：更高的风险回报比
        2. 震荡市场：较低的风险回报比
        3. 波动率：高波动率降低风险回报比
        4. 货币对特性：不同货币对的历史表现
        """
        try:
            # 基础风险回报比
            base_ratio = 1.8

            # 1. 波动率调整
            # 高波动率市场降低风险回报比（更保守）
            if symbol == 'USDJPY':
                atr_threshold = 0.5  # 日元对阈值
            else:
                atr_threshold = 0.0008  # 其他货币对阈值

            if atr > atr_threshold * 1.5:
                volatility_adjustment = 0.8  # 高波动率：降低到1.44
            elif atr < atr_threshold * 0.5:
                volatility_adjustment = 1.2  # 低波动率：提高到2.16
            else:
                volatility_adjustment = 1.0  # 正常波动率：保持1.8

            # 2. 货币对特性调整
            currency_adjustments = {
                'EURUSD': 1.0,   # 标准
                'GBPUSD': 0.9,   # 英镑波动大，保守一些
                'AUDUSD': 1.1,   # 澳元趋势性好
                'NZDUSD': 1.1,   # 纽元趋势性好
                'USDCHF': 1.0,   # 瑞郎标准
                'USDCAD': 1.0,   # 加元标准
                'USDJPY': 0.95   # 日元稍保守
            }

            currency_adjustment = currency_adjustments.get(symbol, 1.0)

            # 3. 时间段调整（可以根据实际时间实现）
            # 这里简化处理
            time_adjustment = 1.0

            # 4. 综合计算
            final_ratio = (base_ratio *
                          volatility_adjustment *
                          currency_adjustment *
                          time_adjustment)

            # 5. 限制范围
            final_ratio = max(1.2, min(2.5, final_ratio))

            return final_ratio

        except Exception as e:
            self.logger.error(f"计算动态风险回报比失败: {e}")
            return 1.8  # 返回默认值

    def _get_session_multiplier(self) -> float:
        """获取时间段权重

        在活跃时段给予更高权重，增加交易机会
        但不降低信号质量要求
        """
        try:
            from datetime import datetime
            import pytz

            # 获取当前UTC时间
            utc_now = datetime.now(pytz.UTC)
            hour_utc = utc_now.hour

            # 定义交易时段（UTC时间）
            # 亚洲时段：22:00-08:00 UTC (相对平静)
            # 伦敦时段：07:00-16:00 UTC (活跃)
            # 纽约时段：12:00-21:00 UTC (活跃)
            # 重叠时段：12:00-16:00 UTC (最活跃)

            if 12 <= hour_utc <= 16:
                # 伦敦-纽约重叠时段：最活跃
                return 1.15  # 提高15%置信度
            elif 7 <= hour_utc <= 11 or 17 <= hour_utc <= 21:
                # 伦敦或纽约单独时段：活跃
                return 1.10  # 提高10%置信度
            elif 22 <= hour_utc <= 23 or 0 <= hour_utc <= 6:
                # 亚洲时段：相对平静但仍有机会
                return 1.05  # 提高5%置信度
            else:
                # 安静时段：8-9点，21-22点
                return 1.0   # 保持原始置信度

        except Exception as e:
            self.logger.error(f"获取时间段权重失败: {e}")
            return 1.0  # 默认不调整