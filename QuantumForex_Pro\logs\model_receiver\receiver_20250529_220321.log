2025-05-29 22:03:21,870 - __main__ - INFO - 🚀 启动 QuantumForex Pro 云端模型接收服务
2025-05-29 22:03:21,870 - __main__ - INFO - ============================================================
2025-05-29 22:03:21,871 - __main__ - INFO - 🌐 启动API服务器...
2025-05-29 22:03:21,872 - __main__ - INFO - 📍 监听地址: 0.0.0.0:8081
2025-05-29 22:03:21,872 - __main__ - INFO - 🔗 API端点:
2025-05-29 22:03:21,872 - __main__ - INFO -    - 健康检查: http://**************:8081/api/health
2025-05-29 22:03:21,872 - __main__ - INFO -    - 模型状态: http://**************:8081/api/models/status
2025-05-29 22:03:21,873 - __main__ - INFO -    - 模型上传: http://**************:8081/api/models/upload
2025-05-29 22:03:21,874 - __main__ - INFO -    - 模型列表: http://**************:8081/api/models/list
2025-05-29 22:03:21,874 - utils.cloud_model_receiver - INFO - 🚀 启动云端模型接收服务器: 0.0.0.0:8081
2025-05-29 22:03:21,895 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://***************:8081
2025-05-29 22:03:21,895 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 22:03:52,011 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:03:52] "GET /api/health HTTP/1.1" 200 -
2025-05-29 22:03:52,015 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:03:52,028 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: test_price_prediction_model.pkl.backup_20250529_220352
2025-05-29 22:03:52,029 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\test_price_prediction_model.pkl
2025-05-29 22:03:53,064 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: test_price_prediction_model.pkl
2025-05-29 22:03:53,065 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:03:53] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:03:53,100 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:03:53,104 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: test_price_prediction_scaler.pkl.backup_20250529_220353
2025-05-29 22:03:53,105 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\test_price_prediction_scaler.pkl
2025-05-29 22:03:53,116 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: test_price_prediction_scaler.pkl
2025-05-29 22:03:53,117 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:03:53] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:06:23,896 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:06:23] "GET /api/health HTTP/1.1" 200 -
2025-05-29 22:10:32,512 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:10:32] "GET /api/health HTTP/1.1" 200 -
2025-05-29 22:13:06,822 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:13:06,848 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 22:13:06,853 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_221032.pkl
2025-05-29 22:13:07,186 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_221032.pkl
2025-05-29 22:13:07,187 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:13:07] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:13:07,218 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:13:07,239 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 22:13:07,241 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_221032.pkl
2025-05-29 22:13:07,294 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_221032.pkl
2025-05-29 22:13:07,295 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:13:07] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:30:18,572 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:30:18] "[33mGET /api/trading/records?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:30:18,597 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:30:18] "[33mGET /api/optimization/history?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:30:18,604 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:30:18] "[33mGET /api/llm/analysis_history?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:32:52,223 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:32:52] "[33mGET /api/trading/records?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:32:52,229 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:32:52] "[33mGET /api/optimization/history?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:32:52,237 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:32:52] "[33mGET /api/llm/analysis_history?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:34:07,337 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:34:07] "[33mGET /api/trading/records?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:34:07,343 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:34:07] "[33mGET /api/optimization/history?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:34:07,348 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:34:07] "[33mGET /api/llm/analysis_history?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:36:57,799 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:36:57] "[33mGET /api/trading/records?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:36:57,808 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:36:57] "[33mGET /api/optimization/history?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:36:57,816 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:36:57] "[33mGET /api/llm/analysis_history?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:37:44,004 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:37:44] "[33mGET /api/trading/records?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:37:44,010 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:37:44] "[33mGET /api/optimization/history?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:37:44,018 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:37:44] "[33mGET /api/llm/analysis_history?days=1 HTTP/1.1[0m" 404 -
2025-05-29 22:38:15,885 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:38:15] "[33mGET /api/trading/records?days=7 HTTP/1.1[0m" 404 -
2025-05-29 22:38:15,903 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:38:15] "[33mGET /api/optimization/history?days=7 HTTP/1.1[0m" 404 -
2025-05-29 22:38:15,918 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:38:15] "[33mGET /api/llm/analysis_history?days=7 HTTP/1.1[0m" 404 -
2025-05-29 22:38:18,409 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:38:18] "GET /api/health HTTP/1.1" 200 -
2025-05-29 22:38:18,417 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:38:18,447 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_lightgbm_20250529_221032.pkl.backup_20250529_223818
2025-05-29 22:38:18,450 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_221032.pkl
2025-05-29 22:38:18,476 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_221032.pkl
2025-05-29 22:38:18,476 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:38:18] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:38:18,506 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:38:18,525 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 22:38:18,527 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_223818.pkl
2025-05-29 22:38:18,558 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_223818.pkl
2025-05-29 22:38:18,559 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:38:18] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:38:18,580 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:38:18,606 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_random_forest_20250529_221032.pkl.backup_20250529_223818
2025-05-29 22:38:18,609 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_221032.pkl
2025-05-29 22:38:18,663 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_221032.pkl
2025-05-29 22:38:18,667 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:38:18] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:38:18,699 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:38:18,724 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 22:38:18,726 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_223818.pkl
2025-05-29 22:38:18,781 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_223818.pkl
2025-05-29 22:38:18,782 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:38:18] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:42:04,657 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:42:04] "[33mGET /api/trading/records?days=7 HTTP/1.1[0m" 404 -
2025-05-29 22:42:04,668 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:42:04] "[33mGET /api/optimization/history?days=7 HTTP/1.1[0m" 404 -
2025-05-29 22:42:04,674 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:42:04] "[33mGET /api/llm/analysis_history?days=7 HTTP/1.1[0m" 404 -
2025-05-29 22:42:08,740 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:42:08] "GET /api/health HTTP/1.1" 200 -
2025-05-29 22:42:08,748 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:42:08,772 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_lightgbm_20250529_221032.pkl.backup_20250529_224208
2025-05-29 22:42:08,775 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_221032.pkl
2025-05-29 22:42:08,799 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_221032.pkl
2025-05-29 22:42:08,800 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:42:08] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:42:08,816 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:42:08,834 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_lightgbm_20250529_223818.pkl.backup_20250529_224208
2025-05-29 22:42:08,836 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_223818.pkl
2025-05-29 22:42:08,865 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_223818.pkl
2025-05-29 22:42:08,866 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:42:08] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:42:08,892 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:42:08,909 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 22:42:08,911 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_lightgbm_20250529_224208.pkl
2025-05-29 22:42:08,939 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_lightgbm_20250529_224208.pkl
2025-05-29 22:42:08,940 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:42:08] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:42:08,959 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:42:08,979 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_random_forest_20250529_221032.pkl.backup_20250529_224208
2025-05-29 22:42:08,981 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_221032.pkl
2025-05-29 22:42:09,028 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_221032.pkl
2025-05-29 22:42:09,029 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:42:09] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:42:09,051 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:42:09,080 - utils.cloud_model_receiver - INFO - 📦 现有模型已备份: price_prediction_direction_5min_random_forest_20250529_223818.pkl.backup_20250529_224209
2025-05-29 22:42:09,082 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_223818.pkl
2025-05-29 22:42:09,130 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_223818.pkl
2025-05-29 22:42:09,131 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:42:09] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:42:09,181 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:42:09,210 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 22:42:09,212 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_random_forest_20250529_224208.pkl
2025-05-29 22:42:09,276 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_random_forest_20250529_224208.pkl
2025-05-29 22:42:09,277 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:42:09] "POST /api/models/upload HTTP/1.1" 200 -
2025-05-29 22:42:09,951 - utils.cloud_model_receiver - INFO - 📥 接收模型上传请求...
2025-05-29 22:42:09,972 - utils.cloud_model_receiver - INFO - 📦 没有现有模型需要备份
2025-05-29 22:42:09,974 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: data\models\price_prediction_direction_5min_xgboost_20250529_224208.pkl
2025-05-29 22:42:10,022 - utils.cloud_model_receiver - INFO - ✅ 模型部署成功: price_prediction_direction_5min_xgboost_20250529_224208.pkl
2025-05-29 22:42:10,023 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 22:42:10] "POST /api/models/upload HTTP/1.1" 200 -
