@echo off
chcp 65001 >nul
echo ================================================================
echo QuantumForex Pro 依赖安装脚本 (Windows)
echo ================================================================
echo.

echo [%date% %time%] 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    echo 请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo [%date% %time%] 升级pip...
python -m pip install --upgrade pip

echo.
echo [%date% %time%] 安装核心依赖...
python -m pip install pandas>=1.3.0
python -m pip install numpy>=1.21.0
python -m pip install scikit-learn>=1.6.0
python -m pip install PyMySQL>=1.0.2
python -m pip install python-dotenv>=0.19.0
python -m pip install requests>=2.25.0
python -m pip install psutil>=7.0.0
python -m pip install scipy>=1.7.0
python -m pip install pyzmq>=24.0.0
python -m pip install python-dateutil>=2.8.0

echo.
echo [%date% %time%] 安装可选依赖...
python -m pip install redis>=4.0.0
python -m pip install ujson>=4.0.0
python -m pip install colorlog>=6.0.0
python -m pip install PyYAML>=6.0
python -m pip install websocket-client>=1.2.0

echo.
echo [%date% %time%] 运行依赖检查脚本...
python install_dependencies.py

echo.
echo [%date% %time%] 测试系统启动...
echo 正在测试QuantumForex Pro模块导入...
python -c "from utils.db_client import test_connection; print('✅ 数据库模块导入成功')"
python -c "from utils.mt4_client import MT4Client; print('✅ MT4客户端导入成功')"
python -c "from utils.intelligent_pair_selector import IntelligentPairSelector; print('✅ 智能选择器导入成功')"

echo.
echo ================================================================
echo ✅ 安装完成！
echo 现在可以运行以下命令启动系统:
echo    python start.py
echo ================================================================
pause
