"""
简单的LLM API测试脚本
"""
import os
import sys
import json
import requests
import traceback
from datetime import datetime

# DeepSeek API密钥
API_KEY = 'sk-dplvjslhezcjinavtmaporlyumqqwnowcbjwyvmetxychflk'

# API端点
API_ENDPOINT = 'https://api.siliconflow.cn/v1/chat/completions'

def test_llm_api():
    """测试LLM API"""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 开始测试LLM API...")
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {API_KEY}'
    }
    
    # 使用简单的提示词
    data = {
        'model': 'Pro/deepseek-ai/DeepSeek-R1',
        'messages': [
            {'role': 'system', 'content': '你是一位专业的外汇分析师，擅长分析欧元/美元货币对的技术指标和基本面因素，给出客观、专业的交易建议。请用中文回答。'},
            {'role': 'user', 'content': '请分析当前欧元/美元的走势，并给出交易建议。请在回复中包含JSON格式的交易指令。'}
        ],
        'temperature': 0.1,
        'max_tokens': 1000
    }
    
    try:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 发送请求...")
        start_time = datetime.now()
        
        response = requests.post(
            API_ENDPOINT,
            json=data,
            headers=headers,
            timeout=(15, 60)  # 连接超时15秒，读取超时1分钟
        )
        
        end_time = datetime.now()
        elapsed_time = (end_time - start_time).total_seconds()
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 收到响应，状态码: {response.status_code}，耗时: {elapsed_time:.2f}秒")
        
        # 打印原始响应内容
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 原始响应内容:")
        print(response.text)
        
        if response.status_code == 200:
            try:
                response_json = response.json()
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 成功解析响应JSON")
                
                # 检查响应JSON是否包含必要的字段
                if 'choices' in response_json and len(response_json['choices']) > 0:
                    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 响应JSON包含choices字段")
                    
                    if 'message' in response_json['choices'][0]:
                        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 响应JSON包含message字段")
                        
                        if 'content' in response_json['choices'][0]['message']:
                            content = response_json['choices'][0]['message']['content']
                            content_length = len(content)
                            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 响应JSON包含content字段，长度: {content_length} 字符")
                            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> content预览:\n{content[:500]}...")
                        else:
                            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 警告: 响应JSON缺少content字段")
                    else:
                        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 警告: 响应JSON缺少message字段")
                else:
                    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 警告: 响应JSON缺少choices字段或choices为空")
            except json.JSONDecodeError as json_error:
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 警告: 响应内容不是有效的JSON: {json_error}")
            
            # 打印token使用情况
            if 'usage' in response_json:
                prompt_tokens = response_json['usage'].get('prompt_tokens', 0)
                completion_tokens = response_json['usage'].get('completion_tokens', 0)
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> Token使用: 提示词={prompt_tokens}, 生成={completion_tokens}, 总计={prompt_tokens + completion_tokens}")
            
            print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试成功!")
            return True
        else:
            print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试失败! 状态码: {response.status_code}")
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] >>> 测试异常! 错误: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_llm_api()
