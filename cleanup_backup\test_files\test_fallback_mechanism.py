"""
测试API调用失败回退机制

这个脚本模拟API调用失败的情况，测试系统的回退机制是否正确工作。
"""
import sys
import time
import traceback
from datetime import datetime
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
sys.path.append('.')

# 导入需要测试的模块
from app.utils.multi_round_analysis import AnalysisFallbackException, perform_multi_round_analysis
from app.services.forex_trading_service import analyze_forex
from app.utils import llm_client

# 测试配置
TEST_RETRY_AFTER = 10  # 测试用的重试时间（秒）

def print_header(message):
    """打印带有分隔线的标题"""
    print("\n" + "=" * 80)
    print(f" {message} ".center(80, "="))
    print("=" * 80)

def test_analysis_fallback_exception():
    """测试AnalysisFallbackException异常类"""
    print_header("测试AnalysisFallbackException异常类")
    
    # 创建回退信息
    fallback_info = {
        'timestamp': datetime.now().isoformat(),
        'error': 'API调用失败测试',
        'message': '多轮分析API调用失败，需要回退并重试',
        'retry_after': TEST_RETRY_AFTER
    }
    
    # 创建异常
    try:
        raise AnalysisFallbackException(fallback_info)
    except AnalysisFallbackException as e:
        print(f"异常消息: {str(e)}")
        print(f"回退信息: {e.fallback_info}")
        assert e.fallback_info == fallback_info, "回退信息不匹配"
        print("测试通过: AnalysisFallbackException异常类正常工作")

def mock_send_to_deepseek_failure(*args, **kwargs):
    """模拟send_to_deepseek调用失败"""
    raise Exception("模拟API调用失败")

def test_perform_multi_round_analysis_fallback():
    """测试perform_multi_round_analysis函数在API调用失败时的回退机制"""
    print_header("测试perform_multi_round_analysis函数回退机制")
    
    # 准备测试数据
    test_data = {
        'symbol': 'EURUSD',
        'currentPrice': 1.1234,
        'timestamp': datetime.now().isoformat(),
        'technicalIndicators': {
            'ma': {'5': 1.1230, '10': 1.1220, '20': 1.1210, '50': 1.1200},
            'rsi': 55,
            'macd': {'macdLine': 0.0002, 'signalLine': 0.0001, 'histogram': 0.0001},
            'bollinger': {'upper': 1.1250, 'middle': 1.1230, 'lower': 1.1210}
        }
    }
    
    # 使用patch模拟llm_client.send_to_deepseek调用失败
    with patch('app.utils.llm_client.send_to_deepseek', side_effect=mock_send_to_deepseek_failure):
        try:
            # 调用perform_multi_round_analysis函数
            result = perform_multi_round_analysis(test_data)
            print("错误: 应该抛出AnalysisFallbackException异常，但没有")
            assert False, "没有抛出预期的异常"
        except AnalysisFallbackException as e:
            print(f"正确: 抛出了AnalysisFallbackException异常: {str(e)}")
            print(f"回退信息: {e.fallback_info}")
            assert 'retry_after' in e.fallback_info, "回退信息中应该包含retry_after字段"
            print("测试通过: perform_multi_round_analysis函数在API调用失败时正确触发回退机制")
        except Exception as e:
            print(f"错误: 抛出了意外的异常: {str(e)}")
            traceback.print_exc()
            assert False, "抛出了意外的异常"

def test_analyze_forex_fallback():
    """测试analyze_forex函数处理AnalysisFallbackException异常的能力"""
    print_header("测试analyze_forex函数处理回退异常")
    
    # 使用patch模拟perform_multi_round_analysis抛出AnalysisFallbackException异常
    fallback_info = {
        'timestamp': datetime.now().isoformat(),
        'error': 'API调用失败测试',
        'message': '多轮分析API调用失败，需要回退并重试',
        'retry_after': TEST_RETRY_AFTER
    }
    
    mock_exception = AnalysisFallbackException(fallback_info)
    
    with patch('app.utils.multi_round_analysis.perform_multi_round_analysis', 
               side_effect=mock_exception):
        # 调用analyze_forex函数
        result = analyze_forex(force=True)
        
        # 验证结果
        print(f"analyze_forex返回结果: {result}")
        assert result is not None, "analyze_forex应该返回一个结果"
        assert isinstance(result, dict), "analyze_forex应该返回一个字典"
        assert result.get('status') == 'FALLBACK_REQUIRED', "结果应该包含status=FALLBACK_REQUIRED"
        assert 'retry_after' in result, "结果应该包含retry_after字段"
        assert result.get('retry_after') == TEST_RETRY_AFTER, f"retry_after应该是{TEST_RETRY_AFTER}"
        print("测试通过: analyze_forex函数正确处理了回退异常")

def test_market_change_callback_fallback():
    """测试market_change_callback函数处理回退结果的能力"""
    print_header("测试market_change_callback函数处理回退结果")
    
    # 由于market_change_callback函数涉及到多个线程和锁，
    # 完整测试比较复杂，这里我们只进行简单的模拟
    
    print("注意: 这个测试需要手动验证")
    print("请检查以下内容:")
    print("1. 当analyze_forex返回status=FALLBACK_REQUIRED时，market_change_callback是否释放锁")
    print("2. 是否安排了延迟重试任务")
    print("3. 延迟任务是否在指定时间后执行")
    
    print("\n可以通过查看系统日志验证这些行为")
    print("测试跳过: 需要手动验证market_change_callback函数的回退处理")

def run_all_tests():
    """运行所有测试"""
    print_header("开始测试API调用失败回退机制")
    
    try:
        # 测试AnalysisFallbackException异常类
        test_analysis_fallback_exception()
        
        # 测试perform_multi_round_analysis函数回退机制
        test_perform_multi_round_analysis_fallback()
        
        # 测试analyze_forex函数处理回退异常
        test_analyze_forex_fallback()
        
        # 测试market_change_callback函数处理回退结果
        test_market_change_callback_fallback()
        
        print_header("所有测试完成")
        print("回退机制测试结果: 通过")
    except AssertionError as e:
        print(f"测试失败: {str(e)}")
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    run_all_tests()
