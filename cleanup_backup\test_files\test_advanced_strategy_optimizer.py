#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级策略优化系统
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_advanced_strategy_optimizer():
    """测试高级策略优化系统"""
    print("🧬 高级策略优化系统测试")
    print("=" * 60)
    
    try:
        # 1. 测试模块导入
        print("📦 测试模块导入...")
        from app.core.advanced_strategy_optimizer import (
            AdvancedStrategyOptimizer, StrategyType, OptimizationMethod, OptimizationTarget
        )
        print("   ✅ 高级策略优化系统模块导入成功")
        
        # 2. 测试系统初始化
        print("\n🔧 测试系统初始化...")
        optimizer = AdvancedStrategyOptimizer()
        print("   ✅ 高级策略优化系统初始化成功")
        print(f"   支持的交易品种: {optimizer.supported_symbols}")
        print(f"   支持的策略类型: {len(optimizer.strategy_templates)}种")
        
        # 3. 测试策略初始化
        print("\n📊 测试策略初始化...")
        
        test_strategies = [
            (StrategyType.TREND_FOLLOWING, 'EURUSD'),
            (StrategyType.MEAN_REVERSION, 'GBPUSD'),
            (StrategyType.BREAKOUT, 'GOLD'),
            (StrategyType.SCALPING, 'USDJPY')
        ]
        
        for strategy_type, symbol in test_strategies:
            strategy_config = optimizer.initialize_strategy(strategy_type, symbol)
            print(f"   ✅ {symbol} {strategy_type.value}策略初始化成功")
            print(f"      参数数量: {len(strategy_config.parameters)}")
            
            # 显示部分参数
            param_names = list(strategy_config.parameters.keys())[:3]
            for param_name in param_names:
                param = strategy_config.parameters[param_name]
                print(f"      {param_name}: {param.current_value} ({param.min_value}-{param.max_value})")
        
        # 4. 测试单个策略优化
        print("\n🎯 测试单个策略优化...")
        
        # 测试趋势跟随策略优化
        print("   测试EURUSD趋势跟随策略优化...")
        optimization_result = optimizer.optimize_strategy(
            StrategyType.TREND_FOLLOWING, 
            'EURUSD',
            OptimizationMethod.GENETIC_ALGORITHM,
            OptimizationTarget.MAXIMIZE_SHARPE
        )
        
        print(f"   ✅ 优化完成")
        print(f"   优化方法: {optimization_result.optimization_method.value}")
        print(f"   优化目标: {optimization_result.target.value}")
        print(f"   改进幅度: {optimization_result.improvement_percentage:.2f}%")
        print(f"   置信度: {optimization_result.confidence_score:.2f}")
        
        print("   原始性能:")
        for metric, value in optimization_result.original_performance.items():
            print(f"     {metric}: {value:.4f}")
        
        print("   优化后性能:")
        for metric, value in optimization_result.optimized_performance.items():
            print(f"     {metric}: {value:.4f}")
        
        # 5. 测试不同优化方法
        print("\n🔬 测试不同优化方法...")
        
        optimization_methods = [
            OptimizationMethod.GRID_SEARCH,
            OptimizationMethod.RANDOM_SEARCH
        ]
        
        for method in optimization_methods:
            print(f"\n   测试{method.value}...")
            result = optimizer.optimize_strategy(
                StrategyType.MEAN_REVERSION,
                'GBPUSD',
                method,
                OptimizationTarget.MAXIMIZE_WIN_RATE
            )
            
            print(f"   改进幅度: {result.improvement_percentage:.2f}%")
            print(f"   置信度: {result.confidence_score:.2f}")
        
        # 6. 测试不同优化目标
        print("\n🎯 测试不同优化目标...")
        
        optimization_targets = [
            OptimizationTarget.MAXIMIZE_RETURN,
            OptimizationTarget.MINIMIZE_DRAWDOWN,
            OptimizationTarget.MAXIMIZE_PROFIT_FACTOR
        ]
        
        for target in optimization_targets:
            print(f"\n   测试{target.value}...")
            result = optimizer.optimize_strategy(
                StrategyType.BREAKOUT,
                'GOLD',
                OptimizationMethod.GENETIC_ALGORITHM,
                target
            )
            
            print(f"   改进幅度: {result.improvement_percentage:.2f}%")
            print(f"   目标指标: {result.optimized_performance.get(optimizer._get_target_metric_name(target), 0):.4f}")
        
        # 7. 测试批量优化
        print("\n🚀 测试批量优化...")
        
        print("   执行所有品种的趋势跟随策略批量优化...")
        batch_results = optimizer.batch_optimize_all_symbols(
            StrategyType.TREND_FOLLOWING,
            OptimizationMethod.GENETIC_ALGORITHM,
            OptimizationTarget.MAXIMIZE_SHARPE
        )
        
        print(f"   ✅ 批量优化完成，优化了{len(batch_results)}个品种")
        
        for result in batch_results:
            print(f"   {result.symbol}: 改进{result.improvement_percentage:.2f}% (置信度{result.confidence_score:.2f})")
        
        # 8. 测试最优策略获取
        print("\n🏆 测试最优策略获取...")
        
        for symbol in ['EURUSD', 'GBPUSD', 'GOLD']:
            optimal_strategy = optimizer.get_optimal_strategy_for_symbol(symbol)
            if optimal_strategy:
                print(f"   {symbol}最优策略: {optimal_strategy.strategy_type.value}")
                if optimal_strategy.performance_metrics:
                    sharpe = optimal_strategy.performance_metrics.get('sharpe_ratio', 0)
                    win_rate = optimal_strategy.performance_metrics.get('win_rate', 0)
                    print(f"     夏普比率: {sharpe:.3f}, 胜率: {win_rate:.1%}")
            else:
                print(f"   {symbol}: 暂无优化策略")
        
        # 9. 测试优化总结
        print("\n📊 测试优化总结...")
        
        optimization_summary = optimizer.get_optimization_summary()
        print(f"   总优化次数: {optimization_summary['total_optimizations']}")
        print(f"   平均改进幅度: {optimization_summary['avg_improvement']:.2f}%")
        
        if optimization_summary['best_optimization']:
            best = optimization_summary['best_optimization']
            print(f"   最佳优化: {best['symbol']} {best['strategy']} (改进{best['improvement']:.2f}%)")
        
        print("   策略表现统计:")
        for strategy_name, perf in optimization_summary['strategy_performance'].items():
            print(f"     {strategy_name}: {perf['count']}次优化, 平均改进{perf['avg_improvement']:.2f}%")
        
        # 10. 测试策略建议
        print("\n💡 测试策略建议...")
        
        for symbol in ['EURUSD', 'AUDUSD', 'NZDUSD']:
            recommendations = optimizer.get_strategy_recommendations(symbol)
            print(f"\n   {symbol}策略建议:")
            
            if recommendations:
                for rec in recommendations[:3]:  # 显示前3个建议
                    print(f"     {rec['action']}: {rec['strategy_type']} - {rec['reason']} (优先级: {rec['priority']})")
            else:
                print("     暂无建议")
        
        # 11. 测试数据导出
        print("\n💾 测试数据导出...")
        
        export_result = optimizer.export_optimization_results()
        if export_result['status'] == 'success':
            print(f"   ✅ 优化结果导出成功: {export_result['filepath']}")
        else:
            print(f"   ❌ 导出失败: {export_result['error']}")
        
        # 12. 性能测试
        print("\n⚡ 性能测试...")
        
        start_time = datetime.now()
        
        # 测试单次优化性能
        optimizer.optimize_strategy(
            StrategyType.SCALPING,
            'USDJPY',
            OptimizationMethod.RANDOM_SEARCH,
            OptimizationTarget.MAXIMIZE_SHARPE
        )
        
        single_optimization_time = (datetime.now() - start_time).total_seconds()
        print(f"   单次优化耗时: {single_optimization_time:.2f}秒")
        
        # 测试批量优化性能
        start_time = datetime.now()
        
        optimizer.batch_optimize_all_symbols(
            StrategyType.SCALPING,
            OptimizationMethod.RANDOM_SEARCH,
            OptimizationTarget.MAXIMIZE_WIN_RATE
        )
        
        batch_optimization_time = (datetime.now() - start_time).total_seconds()
        print(f"   批量优化耗时: {batch_optimization_time:.2f}秒")
        
        print("\n🎉 高级策略优化系统测试完成！")
        print("   ✅ 所有核心功能正常工作")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_strategy_optimizer_summary():
    """显示策略优化系统总结"""
    print("\n📋 高级策略优化系统总结")
    print("=" * 50)
    
    print("🎯 第六阶段完成：高级策略优化系统")
    print("   ✅ 创建了高级策略优化系统")
    print("   ✅ 支持4种主要策略类型优化")
    print("   ✅ 实现了3种优化算法")
    print("   ✅ 集成了6种优化目标")
    print("   ✅ 添加了批量优化功能")
    print("   ✅ 实现了策略表现跟踪")
    
    print("\n🔄 系统改进效果：")
    print("   - 策略优化：从固定参数 → 智能参数优化")
    print("   - 优化算法：从单一方法 → 多种算法选择")
    print("   - 优化目标：从单一指标 → 多目标优化")
    print("   - 批量处理：从单个优化 → 批量自动优化")
    
    print("\n📈 预期收益提升：")
    print("   - 参数优化：通过智能优化提升策略表现")
    print("   - 多策略支持：不同市场条件下的最优策略")
    print("   - 持续改进：定期优化保持策略有效性")
    print("   - 科学方法：基于遗传算法等科学优化方法")
    
    print("\n🔧 技术实现亮点：")
    print("   - 支持4种策略：趋势跟随、均值回归、突破、剥头皮")
    print("   - 3种优化算法：遗传算法、网格搜索、随机搜索")
    print("   - 6种优化目标：收益、夏普比率、回撤、胜率、盈利因子、波动率")
    print("   - 智能参数管理：自动参数范围和类型管理")
    
    print("\n🚀 下一步优化方向：")
    print("   1. 机器学习模型集成")
    print("   2. 实时自适应学习")
    print("   3. 高频交易策略优化")
    print("   4. 深度学习策略开发")

if __name__ == "__main__":
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始高级策略优化系统测试")
    
    # 执行策略优化系统测试
    success = test_advanced_strategy_optimizer()
    
    if success:
        # 显示系统总结
        show_strategy_optimizer_summary()
        
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🎉 第六阶段优化完成！")
        print("高级策略优化系统已成功创建，系统具备了智能策略参数优化能力。")
    else:
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ 测试失败，请检查系统配置。")
