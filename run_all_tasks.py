"""
启动所有外汇交易系统定时任务
"""
import os
import sys
import time
import signal
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.forex_scheduled_tasks import start_hourly_forex_analysis, start_daily_statistics, stop_all_tasks


def signal_handler(sig, frame):
    """信号处理函数，用于捕获Ctrl+C"""
    print("\n捕获到中断信号，正在停止所有任务...")
    stop_all_tasks()
    print("程序已退出")
    sys.exit(0)


def main():
    """主函数"""
    print("=" * 50)
    print("外汇交易系统定时任务管理器")
    print("=" * 50)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 注册信号处理函数
    signal.signal(signal.SIGINT, signal_handler)
    
    # 启动每小时外汇分析任务
    start_hourly_forex_analysis(run_immediately=True, auto_trade=True)
    
    # 启动每日统计分析任务
    start_daily_statistics(run_immediately=True, report_time='17:00')
    
    print("\n所有定时任务已启动")
    print("按Ctrl+C停止所有任务")
    
    # 保持主线程运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n捕获到中断信号，正在停止所有任务...")
        stop_all_tasks()
        print("程序已退出")


if __name__ == "__main__":
    main()
