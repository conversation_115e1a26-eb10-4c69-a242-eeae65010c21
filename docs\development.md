# 外汇交易系统开发文档

## 系统概述

外汇交易系统是一个基于LLM（大型语言模型）的自动化交易系统，主要用于分析EURUSD（欧元/美元）货币对，并根据分析结果执行交易操作。系统采用多轮分析模式，通过三轮分析得出最终交易决策，并通过MT4客户端执行交易。

## 系统架构

系统主要由以下几个部分组成：

1. **数据获取模块**：从数据库获取EURUSD分钟数据，并进行聚合处理
2. **技术分析模块**：计算各种技术指标，如移动平均线、RSI、MACD等
3. **LLM分析模块**：使用DeepSeek-V3模型进行市场分析和交易决策
4. **MT4交易模块**：与MT4客户端通信，执行交易操作
5. **统计评估模块**：对分析、命令和结果进行统计和评估
6. **定时任务模块**：定时执行分析、交易和统计

## 多轮分析流程

系统采用多轮分析模式，主要解决提示词长度限制问题，同时提高分析质量。多轮分析流程如下：

1. **第一轮（初始分析）**：提供基本市场数据，进行初步分析，并确定需要哪些额外信息
2. **第二轮（详细分析）**：根据第一轮分析结果，提供更详细的市场数据，进行深入分析
3. **第三轮（最终决策）**：根据前两轮分析结果，给出最终交易决策

## 交易指令格式

交易指令采用JSON格式，包含以下字段：

```json
{
  "action": "BUY或SELL或NONE",
  "orderType": "MARKET或LIMIT或STOP",
  "entryPrice": 数值或null,
  "stopLoss": 数值,
  "takeProfit": 数值,
  "lotSize": 数值,
  "riskLevel": "LOW或MEDIUM或HIGH",
  "reasoning": "详细的交易理由",
  "orderManagement": [
    {
      "action": "MODIFY或DELETE或CLOSE",
      "orderId": "具体订单ID",
      "newStopLoss": 数值或null,
      "newTakeProfit": 数值或null,
      "newEntryPrice": 数值或null,
      "reason": "详细的操作原因"
    }
  ],
  "isFinalDecision": true或false
}
```

## 最终决策标记

为了确保只有最终决策（第三轮分析结果）被执行，系统引入了`isFinalDecision`标记。只有当该标记为`true`时，交易指令才会被执行。

### 最终决策标记的实现

1. 在`multi_round_analysis.py`中，解析交易指令后，添加`isFinalDecision: true`标记
2. 同时确保订单管理指令也被标记为最终决策
3. 在`forex_scheduled_tasks.py`中，检查交易指令是否包含最终决策标记，只有包含该标记的指令才会被执行
4. 在`forex_trading_service.py`中，执行交易前也会检查是否是最终决策
5. 在`forex_analysis_history.py`中，保存分析记录时，确保交易指令包含最终决策标记

## 最近修复的问题

### 多轮分析最终决策未被正确应用

**问题描述**：系统可能会执行非最终决策的交易指令，导致交易执行与最终分析结果不一致。

**解决方案**：

1. 在多轮分析模块中，明确标记最终决策交易指令，包括订单管理指令
2. 在定时任务模块中，检查交易指令是否包含最终决策标记，只有包含该标记的指令才会被执行
3. 在交易执行服务中，执行交易前也会检查是否是最终决策
4. 在分析历史记录模块中，保存分析记录时，确保交易指令包含最终决策标记

### 无止损交易问题

**问题描述**：系统可能会执行无止损的交易，这在外汇交易中是非常危险的，可能导致巨大的损失。

**解决方案**：

1. 实现严格的止损保护机制，确保所有交易都设置了合理的止损
2. 在新订单创建时，检查止损是否为0或未设置，如果是，设置合理的默认止损
3. 在订单修改时，检查新止损是否为0或未设置，如果是，设置合理的默认止损
4. 如果无法设置合理的止损，取消交易，确保交易安全

### MT4连接失败导致预分析线程退出问题

**问题描述**：当MT4连接失败时，预分析线程会直接中断整个项目，而不是等待一段时间后重试，导致系统无法继续运行。

**解决方案**：

1. 修改`run_market_analyzer`函数中的错误处理逻辑，当`analysis_data`为`None`时（例如MT4连接失败），不再使用`continue`语句直接跳过当前循环
2. 改为设置一个`skip_analysis`标志，并在后续代码中根据该标志决定是否执行预分析步骤
3. 确保锁的释放在`finally`块中处理，避免锁未释放导致的死锁问题
4. 在`forex_trading_service.py`中保持MT4连接失败时返回`None`的逻辑，但确保预分析线程能够正确处理这种情况
5. 当MT4连接失败时，系统会中断当前分析，并在5分钟后重试，而不是中断整个项目
6. 这样系统可以在MT4连接恢复后自动恢复正常运行，无需人工干预

### LLM预分析回答解析问题

**问题描述**：系统无法正确解析LLM预分析回答中的分析决定和下次分析间隔，导致使用默认值，可能影响系统的分析频率和效率。

**解决方案**：

1. 增强`multi_round_analysis.py`中的解析逻辑，使用多种正则表达式模式匹配不同格式的回答
2. 添加更广泛的解析策略，包括更宽松的匹配和多级回退机制
3. 改进提示词，更明确地指导LLM按照系统期望的格式输出结果
4. 在提示词中强调格式的重要性，并提供具体的格式要求和示例
5. 这样系统可以更准确地解析LLM的回答，减少使用默认值的情况

### 倒计时显示混乱问题

**问题描述**：系统在显示距离下次预分析的倒计时时，会显示混乱的倒计时信息（如先显示55分钟，后显示25分钟），造成混淆。同时，倒计时显示不够规律，可能导致用户误以为系统卡住。

**解决方案**：

1. 修改`app/__init__.py`，确保只启动一个分析任务：
   - 在混合模式下，不再同时启动`start_hourly_forex_analysis`和`start_realtime_forex_analysis`
   - 只启动`start_realtime_forex_analysis`，但设置`hourly_force_analysis=True`，实现每小时强制执行一次完整分析
   - 这样避免了多个线程同时运行导致的倒计时混乱

2. 在`forex_scheduled_tasks.py`中添加线程安全机制来保护倒计时变量：
   - 添加专用的`countdown_lock`锁，用于保护倒计时相关的全局变量
   - 在所有访问和修改倒计时变量的地方使用锁保护，确保线程安全
   - 使用`with countdown_lock:`语句块包裹所有对倒计时变量的操作

3. 彻底重写倒计时显示逻辑：
   - 每分钟只显示一次倒计时，避免频繁输出和混乱的时间显示
   - 当距离上次显示已经过去了至少60秒时，也会显示倒计时，确保有足够的日志输出
   - 优化倒计时显示格式，当剩余时间接近整数分钟时，显示为整数分钟，避免显示如"5.0分钟"这样的小数

4. 在所有可能修改下次分析时间的地方都添加重置全局倒计时变量的代码，并使用锁保护：
   - 初始化时
   - 锁获取失败时
   - 分析数据获取失败时
   - 预分析结果处理时
   - 错误处理时

5. 添加每小时强制执行一次完整分析的功能：
   - 使用`schedule`库设置每小时整点执行一次强制分析
   - 这样可以确保系统定期执行完整分析，不会因为预分析总是决定不需要分析而长时间不执行完整分析

这些修改确保了倒计时信息的一致性、准确性和规律性，避免了混乱的倒计时显示，提高了系统运行状态的可观察性。通过添加线程安全机制，解决了多线程环境下全局变量访问冲突的问题，确保了倒计时显示的一致性。

### 风险管理不合理问题

**问题描述**：系统可能执行风险回报比不合理的交易，如止损距离远大于止盈距离，导致交易效率低下。

**解决方案**：

1. 修改`forex_trading_service.py`中的风险管理逻辑，强制执行更合理的风险回报比
2. 当风险回报比小于0.8（风险大于回报）时，自动调整止盈以改善风险回报比
3. 当风险回报比大于5（止损可能设置过远）时，自动调整止损以获得更合理的风险回报比
4. 在LLM提示词中强调短线交易策略和合理的风险回报比要求
5. 明确指出止损点数应在10-50点之间，止盈点数应在10-150点之间，符合短线交易特性
6. 这样可以确保系统执行的交易具有更合理的风险回报比，提高交易效率

### 修改的文件

1. `app/utils/multi_round_analysis.py`：添加对订单管理指令的最终决策标记，增强LLM回答解析逻辑，改进预分析提示词，强调短线交易策略和合理的风险回报比
2. `app/utils/forex_scheduled_tasks.py`：增强对最终决策标记的检查，添加更详细的日志，修复MT4连接失败导致预分析线程退出的问题，优化倒计时显示逻辑，添加线程安全机制保护倒计时变量
3. `app/services/forex_trading_service.py`：在执行交易前检查最终决策标记，添加止损保护机制，确保MT4连接失败时正确返回`None`，强制执行更合理的风险回报比
4. `app/utils/forex_analysis_history.py`：保存分析记录时确保包含最终决策标记
5. `app/utils/llm_client.py`：修改订单管理指令解析，确保不使用0作为止损值
6. `docs/development.md`：更新开发文档，记录最近修复的问题

## 止损保护机制

为了确保交易安全，系统实现了严格的止损保护机制，确保所有交易都设置了合理的止损，防止无止损交易。

### 止损止盈保护的实现

1. **新订单创建**：
   - 检查止损和止盈是否为0或未设置
   - 如果止损未设置，根据交易方向设置合理的默认止损（买单为当前价格下方100点，卖单为当前价格上方100点）
   - 如果止盈未设置，根据止损和风险回报比设置合理的默认止盈（至少为风险的1.5倍）
   - 如果无法获取当前价格设置合理的止损止盈，取消交易

2. **订单修改**：
   - 检查新止损和止盈是否为0或未设置
   - 如果止损未设置，获取订单类型（买单或卖单）和开仓价格
   - 根据订单类型设置合理的默认止损
   - 如果止盈未设置，根据止损和风险回报比设置合理的默认止盈
   - 确保风险回报比至少为1.5
   - 如果无法获取订单信息或价格，保持原有设置（如果原有设置也为0，会发出警告）

3. **挂单修改**：
   - 检查新止损和止盈是否为0或未设置
   - 如果止损未设置，根据挂单类型和价格设置合理的默认止损
   - 如果止盈未设置，根据止损和风险回报比设置合理的默认止盈
   - 确保所有挂单都有合理的止损和止盈设置

4. **主动检查和修复**：
   - 系统会在每次分析前自动检查所有订单
   - 识别没有设置止损或止盈的订单
   - 根据订单类型和价格自动设置合理的止损和止盈
   - 计算并记录风险回报比，但不强制调整
   - 为缺失的止盈提供建议值（默认为1.5倍风险），但明确标注这只是系统建议
   - 记录修复结果，并在日志中详细说明

5. **风险回报比计算与建议**：
   - 使用正确的入场价计算风险回报比（而不是当前市场价格）
   - 对于市价单，使用预估入场价（当前价格）计算
   - 对于限价单和止损单，使用指定的入场价计算
   - 对于已有订单，使用实际的开仓价计算
   - 买单风险 = 入场价 - 止损价，回报 = 止盈价 - 入场价
   - 卖单风险 = 止损价 - 入场价，回报 = 入场价 - 止盈价
   - 风险回报比 = 回报 / 风险
   - 系统不再强制调整风险回报比，而是提供建议和警告：
     - 如果风险回报比小于0.5（风险是回报的2倍以上），发出严重警告
     - 如果风险回报比在0.5-1.0之间（风险略大于回报），发出提示
     - 如果风险回报比在1.0-1.5之间，发出良好提示
     - 如果风险回报比在1.5-5.0之间，发出优秀提示
     - 如果风险回报比大于5，发出警告，可能表示止损设置过远
   - 系统会记录风险回报比警告，但最终决策权交给LLM
   - 默认止盈设置使用1.5倍风险作为建议值，但明确标注这只是系统建议

### 止损止盈保护的优势

1. **降低风险**：防止无止损或无止盈交易，大大降低了交易风险
2. **自动化保护**：系统自动检查和修复无止损或无止盈订单，无需人工干预
3. **合理的默认值**：根据交易方向和价格设置合理的默认止损和止盈
4. **合理的风险回报比**：确保所有交易都有至少1.5的风险回报比，提高交易的成功率
5. **交易安全第一**：如果无法设置合理的止损止盈，系统会取消交易，确保交易安全

## 统计评估模块

统计评估模块用于对分析、命令和结果进行统计和评估，帮助用户了解系统的运行情况和性能表现。

### 统计指标

统计评估模块包含以下主要指标：

1. **基本统计**：分析次数、操作次数、操作成功率等
2. **交易行为分布**：BUY、SELL、NONE等交易行为的分布情况
3. **订单管理统计**：修改订单、平仓操作、删除挂单的次数和比例
4. **时间分布**：分析和交易在不同时间段的分布情况
5. **决策一致性**：最终决策与初始分析的一致性比例
6. **执行延迟**：从分析完成到交易执行的平均延迟时间

### 统计报告

统计评估模块会生成以下输出：

1. **统计数据文件**：保存在`app/data/forex_statistics.json`
2. **统计报告文本**：保存在`app/data/forex_statistics_report.txt`
3. **统计图表**：保存在`app/data/charts/`目录下，包括交易行为分布饼图、订单管理操作柱状图、时间分布柱状图等

### 定时统计任务

系统设置了每日定时统计任务，默认在每天17:00执行，生成当天的统计报告和图表。用户也可以通过运行`run_statistics.py`脚本手动触发统计分析。

## 运行脚本

系统提供了以下运行脚本：

1. **run_analysis.py**：手动执行一次外汇分析
2. **run_statistics.py**：手动执行一次统计分析
3. **run_stop_loss_check.py**：手动执行一次止损检查，修复无止损订单
4. **run_all_tasks.py**：启动所有定时任务，包括每小时分析和每日统计

## 未来改进计划

### MT4服务端市场变化检测方案

**背景**：目前系统使用Python端的预分析功能来检测市场变化，这种方式需要频繁地从MT4获取数据，效率较低，且可能会错过一些重要的市场变化。

**方案概述**：将市场变化检测逻辑迁移到MT4服务端，由MT4直接监控市场变化，当检测到重要变化时，主动通知Python端执行完整分析。

**实现步骤**：

1. **MT4服务端开发**：
   - 在MT4 Server (MQL4) 中实现市场变化检测算法
   - 监控价格突破、技术指标交叉、波动率变化、成交量异常等情况
   - 实现通过ZMQ向Python端发送通知的功能
   - 设计通知消息格式，包含触发原因、当前市场数据等信息

2. **Python端改造**：
   - 增加接收MT4通知的接口和处理逻辑
   - 修改预分析流程，支持MT4触发的直接分析
   - 保留现有的预分析和定时分析功能作为备份机制

3. **测试与优化**：
   - 测试MT4检测算法的准确性和灵敏度
   - 优化触发条件，避免过度触发或遗漏重要变化
   - 测试系统在MT4触发下的响应速度和稳定性

**预期效果**：
- 提高系统对市场变化的响应速度
- 减少Python端的资源消耗
- 提高市场变化检测的准确性
- 减少网络通信开销

**开发计划**：
- 待MT4和Python通信问题解决后实施
- 优先级：中高
- 预计开发周期：2周

### 其他改进计划

1. **增强错误处理**：添加更详细的错误日志，提高系统稳定性
2. **优化LLM提示词**：进一步优化提示词，提高分析质量
3. **添加回测功能**：实现历史数据回测，评估策略效果
4. **增加更多技术指标**：添加更多技术指标，提高分析准确性
5. **实现LLM奖励机制**：根据交易结果对LLM进行奖励，提高决策质量
6. **增强统计分析**：添加更多统计指标和图表，提供更全面的性能评估
