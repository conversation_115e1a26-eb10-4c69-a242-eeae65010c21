#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
新闻数据收集器
实时获取外汇相关新闻数据
"""

import logging
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum
import time

class NewsImportance(Enum):
    """新闻重要性等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class NewsSource(Enum):
    """新闻来源"""
    JIN10 = "jin10"
    INVESTING = "investing"
    FOREXFACTORY = "forexfactory"
    REUTERS = "reuters"
    BLOOMBERG = "bloomberg"

@dataclass
class NewsItem:
    """新闻项目"""
    id: str
    title: str
    content: str
    source: NewsSource
    importance: NewsImportance
    publish_time: datetime
    currency_pairs: List[str]
    sentiment_score: float  # -1.0 to 1.0
    impact_score: float     # 0.0 to 1.0
    keywords: List[str]

class NewsDataCollector:
    """新闻数据收集器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 外汇相关关键词
        self.forex_keywords = {
            'currencies': ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'NZD', 'CAD', 'CHF',
                          '美元', '欧元', '英镑', '日元', '澳元', '纽元', '加元', '瑞郎'],
            'central_banks': ['Fed', 'ECB', 'BOE', 'BOJ', 'RBA', 'RBNZ', 'BOC', 'SNB',
                             '美联储', '欧洲央行', '英国央行', '日本央行', '澳洲联储'],
            'economic_indicators': ['GDP', 'CPI', 'PPI', 'PMI', 'NFP', 'unemployment',
                                   '通胀', '就业', '利率', '经济增长', '贸易'],
            'market_terms': ['汇率', '外汇', '货币政策', '量化宽松', 'QE', '加息', '降息']
        }
        
        # 货币对映射
        self.currency_pair_mapping = {
            'USD': ['EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCHF', 'USDCAD', 'USDJPY'],
            'EUR': ['EURUSD', 'EURGBP', 'EURJPY', 'EURAUD'],
            'GBP': ['GBPUSD', 'EURGBP', 'GBPJPY', 'GBPAUD'],
            'JPY': ['USDJPY', 'EURJPY', 'GBPJPY', 'AUDJPY'],
            'AUD': ['AUDUSD', 'EURAUD', 'GBPAUD', 'AUDJPY'],
            'NZD': ['NZDUSD', 'AUDNZD'],
            'CAD': ['USDCAD', 'CADJPY'],
            'CHF': ['USDCHF', 'EURCHF']
        }
        
        # 新闻缓存
        self.news_cache = []
        self.last_update = None
        
    def collect_news(self, hours_back: int = 24) -> List[NewsItem]:
        """收集新闻数据
        
        Args:
            hours_back: 回溯小时数
            
        Returns:
            List[NewsItem]: 新闻列表
        """
        try:
            all_news = []
            
            # 1. 从数据库获取新闻（如果有的话）
            db_news = self._get_news_from_database(hours_back)
            all_news.extend(db_news)
            
            # 2. 从外部API获取新闻（模拟实现）
            api_news = self._get_news_from_apis(hours_back)
            all_news.extend(api_news)
            
            # 3. 过滤和处理新闻
            filtered_news = self._filter_forex_news(all_news)
            
            # 4. 分析新闻
            analyzed_news = self._analyze_news(filtered_news)
            
            # 5. 更新缓存
            self.news_cache = analyzed_news
            self.last_update = datetime.now()
            
            self.logger.info(f"收集到 {len(analyzed_news)} 条外汇相关新闻")
            
            return analyzed_news
            
        except Exception as e:
            self.logger.error(f"收集新闻数据失败: {e}")
            return []
    
    def _get_news_from_database(self, hours_back: int) -> List[NewsItem]:
        """从数据库获取新闻"""
        try:
            from utils.db_client import get_latest_news
            
            # 获取数据库中的新闻
            db_news_data = get_latest_news(limit=100)
            
            news_items = []
            for item in db_news_data:
                # 检查时间范围
                news_time = datetime.strptime(item['time'], '%Y-%m-%d %H:%M:%S')
                if (datetime.now() - news_time).total_seconds() > hours_back * 3600:
                    continue
                
                news_item = NewsItem(
                    id=f"db_{item['id']}",
                    title=item['content'][:50] + "..." if len(item['content']) > 50 else item['content'],
                    content=item['content'],
                    source=NewsSource.JIN10,
                    importance=self._parse_importance(item.get('importance', 'MEDIUM')),
                    publish_time=news_time,
                    currency_pairs=[],  # 待分析
                    sentiment_score=0.0,  # 待分析
                    impact_score=0.0,     # 待分析
                    keywords=[]           # 待分析
                )
                news_items.append(news_item)
            
            return news_items
            
        except Exception as e:
            self.logger.error(f"从数据库获取新闻失败: {e}")
            return []
    
    def _get_news_from_apis(self, hours_back: int) -> List[NewsItem]:
        """从外部API获取新闻（模拟实现）"""
        try:
            # 这里可以集成真实的新闻API
            # 例如：Reuters API, Bloomberg API, Alpha Vantage News API等
            
            # 模拟新闻数据
            mock_news = [
                {
                    'id': 'api_001',
                    'title': '美联储官员暗示可能暂停加息',
                    'content': '美联储官员在最新讲话中表示，考虑到当前经济数据，央行可能在下次会议中暂停加息步伐。',
                    'source': 'reuters',
                    'importance': 'high',
                    'publish_time': datetime.now() - timedelta(hours=2)
                },
                {
                    'id': 'api_002', 
                    'title': '欧洲央行维持利率不变',
                    'content': '欧洲央行在今日会议中决定维持主要再融资利率在4.5%不变，符合市场预期。',
                    'source': 'bloomberg',
                    'importance': 'medium',
                    'publish_time': datetime.now() - timedelta(hours=4)
                },
                {
                    'id': 'api_003',
                    'title': '美国非农就业数据超预期',
                    'content': '美国11月非农就业人数增加25万，远超预期的18万，失业率维持在3.7%。',
                    'source': 'investing',
                    'importance': 'high',
                    'publish_time': datetime.now() - timedelta(hours=6)
                }
            ]
            
            news_items = []
            for item in mock_news:
                news_item = NewsItem(
                    id=item['id'],
                    title=item['title'],
                    content=item['content'],
                    source=NewsSource(item['source']),
                    importance=self._parse_importance(item['importance']),
                    publish_time=item['publish_time'],
                    currency_pairs=[],  # 待分析
                    sentiment_score=0.0,  # 待分析
                    impact_score=0.0,     # 待分析
                    keywords=[]           # 待分析
                )
                news_items.append(news_item)
            
            return news_items
            
        except Exception as e:
            self.logger.error(f"从API获取新闻失败: {e}")
            return []
    
    def _filter_forex_news(self, news_list: List[NewsItem]) -> List[NewsItem]:
        """过滤外汇相关新闻"""
        filtered_news = []
        
        for news in news_list:
            # 检查标题和内容是否包含外汇关键词
            text_to_check = (news.title + " " + news.content).lower()
            
            is_forex_related = False
            for category, keywords in self.forex_keywords.items():
                for keyword in keywords:
                    if keyword.lower() in text_to_check:
                        is_forex_related = True
                        break
                if is_forex_related:
                    break
            
            if is_forex_related:
                filtered_news.append(news)
        
        return filtered_news
    
    def _analyze_news(self, news_list: List[NewsItem]) -> List[NewsItem]:
        """分析新闻内容"""
        analyzed_news = []
        
        for news in news_list:
            # 1. 识别相关货币对
            news.currency_pairs = self._identify_currency_pairs(news)
            
            # 2. 计算情感分数
            news.sentiment_score = self._calculate_sentiment(news)
            
            # 3. 计算影响分数
            news.impact_score = self._calculate_impact(news)
            
            # 4. 提取关键词
            news.keywords = self._extract_keywords(news)
            
            analyzed_news.append(news)
        
        return analyzed_news
    
    def _identify_currency_pairs(self, news: NewsItem) -> List[str]:
        """识别新闻影响的货币对"""
        affected_pairs = set()
        text = (news.title + " " + news.content).upper()
        
        # 检查货币代码
        for currency, pairs in self.currency_pair_mapping.items():
            if currency in text:
                affected_pairs.update(pairs)
        
        # 检查货币对直接提及
        common_pairs = ['EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCHF', 'USDCAD', 'USDJPY']
        for pair in common_pairs:
            if pair in text:
                affected_pairs.add(pair)
        
        return list(affected_pairs)
    
    def _calculate_sentiment(self, news: NewsItem) -> float:
        """计算新闻情感分数"""
        # 简化的情感分析
        positive_words = ['上涨', '增长', '改善', '乐观', '强劲', '超预期', 'rise', 'growth', 'improve', 'positive', 'strong', 'beat']
        negative_words = ['下跌', '下降', '恶化', '悲观', '疲软', '不及预期', 'fall', 'decline', 'worsen', 'negative', 'weak', 'miss']
        
        text = (news.title + " " + news.content).lower()
        
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        total_words = len(text.split())
        if total_words == 0:
            return 0.0
        
        # 计算情感分数 (-1.0 到 1.0)
        sentiment = (positive_count - negative_count) / max(total_words / 10, 1)
        return max(-1.0, min(1.0, sentiment))
    
    def _calculate_impact(self, news: NewsItem) -> float:
        """计算新闻影响分数"""
        impact = 0.0
        
        # 基于重要性等级
        importance_weights = {
            NewsImportance.LOW: 0.2,
            NewsImportance.MEDIUM: 0.5,
            NewsImportance.HIGH: 0.8,
            NewsImportance.CRITICAL: 1.0
        }
        impact += importance_weights.get(news.importance, 0.5)
        
        # 基于新闻源权重
        source_weights = {
            NewsSource.REUTERS: 0.9,
            NewsSource.BLOOMBERG: 0.9,
            NewsSource.JIN10: 0.7,
            NewsSource.INVESTING: 0.6,
            NewsSource.FOREXFACTORY: 0.8
        }
        impact *= source_weights.get(news.source, 0.5)
        
        # 基于时效性
        hours_old = (datetime.now() - news.publish_time).total_seconds() / 3600
        time_decay = max(0.1, 1.0 - hours_old / 24)  # 24小时内线性衰减
        impact *= time_decay
        
        return min(1.0, impact)
    
    def _extract_keywords(self, news: NewsItem) -> List[str]:
        """提取关键词"""
        keywords = []
        text = news.title + " " + news.content
        
        # 提取外汇相关关键词
        for category, words in self.forex_keywords.items():
            for word in words:
                if word.lower() in text.lower():
                    keywords.append(word)
        
        return list(set(keywords))  # 去重
    
    def _parse_importance(self, importance_str: str) -> NewsImportance:
        """解析重要性等级"""
        importance_map = {
            'LOW': NewsImportance.LOW,
            'MEDIUM': NewsImportance.MEDIUM,
            'HIGH': NewsImportance.HIGH,
            'CRITICAL': NewsImportance.CRITICAL
        }
        return importance_map.get(importance_str.upper(), NewsImportance.MEDIUM)
    
    def get_recent_news(self, hours: int = 6) -> List[NewsItem]:
        """获取最近的新闻"""
        if not self.news_cache or not self.last_update:
            return self.collect_news(hours)
        
        # 检查缓存是否过期（30分钟）
        if (datetime.now() - self.last_update).total_seconds() > 1800:
            return self.collect_news(hours)
        
        # 过滤指定时间范围内的新闻
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_news = [news for news in self.news_cache if news.publish_time >= cutoff_time]
        
        return recent_news
    
    def get_high_impact_news(self, hours: int = 12) -> List[NewsItem]:
        """获取高影响力新闻"""
        recent_news = self.get_recent_news(hours)
        high_impact_news = [news for news in recent_news if news.impact_score >= 0.7]
        
        # 按影响分数排序
        high_impact_news.sort(key=lambda x: x.impact_score, reverse=True)
        
        return high_impact_news

# 创建全局实例
news_collector = NewsDataCollector()
