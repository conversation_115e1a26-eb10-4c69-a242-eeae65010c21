@echo off
echo ========================================
echo Forex Trading System - Deployment Package
echo ========================================

:: 设置变量
set PACKAGE_NAME=ForexTradingSystem_v1.0.0
set PACKAGE_DIR=%PACKAGE_NAME%
set CURRENT_DIR=%~dp0
set EXCLUDE_FILE=package_exclude.txt

:: 创建排除文件列表
echo 📝 创建排除文件列表...
(
echo cleanup_backup\
echo backups\
echo venv\
echo __pycache__\
echo *.pyc
echo *.pyo
echo *.log
echo .git\
echo .gitignore
echo node_modules\
echo *.tmp
echo *.temp
echo production_test_report_*.json
echo package_for_deployment.bat
echo %EXCLUDE_FILE%
) > %EXCLUDE_FILE%

:: 清理旧的打包文件
if exist "%PACKAGE_DIR%" (
    echo 🗑️ 清理旧的打包文件...
    rmdir /s /q "%PACKAGE_DIR%"
)

if exist "%PACKAGE_NAME%.zip" (
    del "%PACKAGE_NAME%.zip"
)

:: 创建打包目录
echo 📦 创建打包目录...
mkdir "%PACKAGE_DIR%"

:: 复制文件
echo 📁 复制项目文件...
xcopy /E /I /Y "%CURRENT_DIR%" "%PACKAGE_DIR%\" /EXCLUDE:%EXCLUDE_FILE%

:: 创建部署说明文件
echo 📄 创建部署说明...
(
echo # 外汇交易系统部署包
echo.
echo ## 版本信息
echo - 版本: v1.0.0
echo - 打包时间: %date% %time%
echo - 适用系统: Windows Server 2012+
echo.
echo ## 部署步骤
echo 1. 解压到目标目录
echo 2. 安装Python 3.9+
echo 3. 配置 .env.local 文件
echo 4. 运行 start_server.bat 测试
echo 5. 运行 install_service.bat 安装服务
echo.
echo ## 重要文件
echo - start_server.bat: 启动脚本
echo - install_service.bat: 服务安装脚本
echo - update_system.bat: 更新脚本
echo - Windows_部署指南.md: 详细部署指南
echo - .env.example: 配置文件示例
echo.
echo ## 技术支持
echo 详细说明请参考 Windows_部署指南.md
) > "%PACKAGE_DIR%\部署说明.txt"

:: 创建快速启动脚本
echo 📄 创建快速启动脚本...
(
echo @echo off
echo chcp 65001
echo echo ========================================
echo echo 外汇交易系统 - 快速部署
echo echo ========================================
echo echo.
echo echo 📋 部署步骤:
echo echo 1. 确保已安装 Python 3.9+
echo echo 2. 配置 .env.local 文件
echo echo 3. 运行此脚本进行部署
echo echo.
echo pause
echo.
echo echo 🚀 开始部署...
echo call start_server.bat
) > "%PACKAGE_DIR%\快速部署.bat"

:: 复制重要的配置文件
echo 📄 准备配置文件...
if not exist "%PACKAGE_DIR%\.env.local" (
    copy "%PACKAGE_DIR%\.env.example" "%PACKAGE_DIR%\.env.local"
)

:: 创建版本信息文件
echo 📄 创建版本信息...
(
echo {
echo   "version": "1.0.0",
echo   "build_date": "%date%",
echo   "build_time": "%time%",
echo   "build_number": "001",
echo   "description": "外汇交易系统生产版本",
echo   "features": [
echo     "智能外汇分析",
echo     "实时交易监控",
echo     "风险管理系统",
echo     "多货币对支持",
echo     "远程更新功能"
echo   ],
echo   "requirements": {
echo     "python": "3.9+",
echo     "os": "Windows Server 2012+",
echo     "memory": "4GB+",
echo     "disk": "10GB+"
echo   }
echo }
) > "%PACKAGE_DIR%\version.json"

:: 压缩打包
echo 📦 压缩打包...
powershell -command "Compress-Archive -Path '%PACKAGE_DIR%' -DestinationPath '%PACKAGE_NAME%.zip' -Force"

if exist "%PACKAGE_NAME%.zip" (
    echo ✅ 打包完成！
    echo 📦 包文件: %PACKAGE_NAME%.zip

    :: 显示包大小
    for %%I in ("%PACKAGE_NAME%.zip") do (
        echo 📊 包大小: %%~zI 字节
    )

    :: 清理临时文件
    rmdir /s /q "%PACKAGE_DIR%"
    del "%EXCLUDE_FILE%"

    echo.
    echo 🎉 部署包创建成功！
    echo 📁 文件位置: %CURRENT_DIR%%PACKAGE_NAME%.zip
    echo.
    echo 📋 下一步:
    echo 1. 将 %PACKAGE_NAME%.zip 上传到服务器
    echo 2. 解压到目标目录
    echo 3. 按照部署说明进行安装
    echo.
) else (
    echo ❌ 打包失败！
    echo 请检查PowerShell是否可用
)

pause
