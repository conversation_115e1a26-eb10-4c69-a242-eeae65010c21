# QuantumForex Pro - 世界顶级量化交易系统

## 🏆 系统概述

**QuantumForex Pro** 是基于LLM策略大脑的下一代量化交易系统，专为Windows Server 2012环境优化，实现稳定收益、高胜率和低回撤的交易目标。

## 🎯 核心目标

- **年化收益率**：25-40%
- **最大回撤**：<12%
- **夏普比率**：>2.0
- **胜率**：>60%
- **盈亏比**：>1.8

## 🧠 四层智能架构

### 第四层：LLM策略大脑 (Chief Strategy Officer)
- 市场宏观分析和策略决策
- 动态参数优化和风险调整
- 异常情况处理和策略切换

### 第三层：多因子信号融合引擎
- 50+高级技术指标综合分析
- 基本面、技术面、情绪面多维融合
- 智能信号质量评估和过滤

### 第二层：机器学习预测引擎
- 轻量级ML模型（适配老服务器）
- 价格预测、波动率建模、趋势识别
- 自适应模型参数和特征选择

### 第一层：高效执行引擎
- 智能订单路由和滑点优化
- 实时风险监控和仓位管理
- 高频信号处理和交易执行

## 🔧 技术栈

- **核心语言**：Python 3.8+
- **数据处理**：Pandas, NumPy, TA-Lib
- **机器学习**：Scikit-learn, XGBoost (轻量版)
- **LLM集成**：DeepSeek API (继承现有配置)
- **数据库**：MySQL (pizza_quotes) + Redis缓存
- **交易接口**：MT4 ZeroMQ (继承现有配置)

## 📊 继承的配置

### 数据库配置
```
Host: pizza-wnet-db1.mysql.rds.aliyuncs.com:6688
Database: pizza_quotes
User: ea_quote_srv
Password: pizza666!
```

### LLM API配置
```
API: https://api.siliconflow.cn/v1/chat/completions
Key: sk-dplvjslhezcjinavtmaporlyumqqwnowcbjwyvmetxychflk
Model: Pro/deepseek-ai/DeepSeek-R1 (主) / DeepSeek-V3 (备)
```

### MT4连接配置
```
Address: tcp://127.0.0.1:5555
Timeout: 30s
Auth: test-auth-code-1
```

## 🚀 创新亮点

1. **LLM驱动的策略进化**：系统能够自主学习和进化
2. **多维度信号融合**：50+技术指标智能组合
3. **自适应风险管理**：根据市场波动率动态调整
4. **服务器优化设计**：轻量级模型，适配老硬件

## 📁 项目结构

```
QuantumForex_Pro/
├── docs/                          # 设计和开发文档
├── core/                          # 核心引擎
│   ├── signal_engine/             # 信号生成引擎
│   ├── ml_engine/                 # 机器学习引擎
│   ├── risk_engine/               # 风险管理引擎
│   ├── execution_engine/          # 执行引擎
│   └── llm_brain/                 # LLM策略大脑
├── strategies/                    # 交易策略
│   ├── trend_following/           # 趋势跟踪策略
│   ├── mean_reversion/            # 均值回归策略
│   ├── momentum/                  # 动量策略
│   └── hybrid/                    # 混合策略
├── data/                          # 数据管理
│   ├── market_data/               # 市场数据
│   ├── models/                    # 训练好的模型
│   └── cache/                     # 缓存数据
├── utils/                         # 工具模块
│   ├── technical_analysis/        # 技术分析工具
│   ├── feature_engineering/       # 特征工程
│   ├── backtesting/              # 回测框架
│   └── monitoring/               # 监控工具
├── config/                        # 配置文件
├── tests/                         # 测试文件
└── main.py                        # 主程序入口
```

## 🎯 开发阶段

### 阶段1：核心引擎开发 (Week 1)
- [ ] 信号生成引擎：50+技术指标实现
- [ ] 机器学习引擎：轻量级模型集成
- [ ] 风险管理引擎：多层次风险控制
- [ ] 执行引擎：智能订单管理

### 阶段2：策略系统开发 (Week 2)
- [ ] 趋势跟踪策略：多时间框架趋势识别
- [ ] 均值回归策略：统计套利和反转交易
- [ ] 动量策略：价格动量和成交量确认
- [ ] 混合策略：多策略智能组合

### 阶段3：LLM大脑集成 (Week 3)
- [ ] LLM策略分析器：宏观市场分析
- [ ] 参数优化器：动态参数调整
- [ ] 风险评估器：智能风险管理
- [ ] 决策协调器：策略协调和执行

### 阶段4：系统集成测试 (Week 4)
- [ ] 回测框架：历史数据验证
- [ ] 实时测试：模拟交易验证
- [ ] 性能优化：服务器适配优化
- [ ] 部署准备：生产环境配置

## 📈 预期性能

基于现有项目的成功经验和新系统的技术优势，预期性能指标：

- **收益稳定性**：月收益率波动<5%
- **风险控制**：日最大亏损<3%
- **执行效率**：信号响应时间<30秒
- **系统稳定性**：7x24小时稳定运行

## 🔄 持续优化

系统将持续学习和优化：
- 每日性能评估和参数调整
- 每周策略组合优化
- 每月模型重训练和升级
- 季度系统架构评估和改进

---

**注意**：本系统基于现有成功项目的配置和经验，确保开箱即用的同时实现技术突破。
