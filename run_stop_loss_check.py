"""
运行止损止盈检查工具
用于检查和修复无止损或无止盈订单
"""
import os
import sys
import time
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.stop_loss_checker import check_and_fix_orders


def main():
    """主函数"""
    print("=" * 50)
    print("外汇交易系统止损止盈检查工具")
    print("=" * 50)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 运行止损止盈检查
    start_time = time.time()
    result = check_and_fix_orders()
    end_time = time.time()

    if result['success']:
        print("\n止损止盈检查完成！")
        print(f"活跃订单数量: {result.get('active_orders_count', 0)}")
        print(f"挂单数量: {result.get('pending_orders_count', 0)}")
        print(f"无止损活跃订单数量: {result.get('no_sl_active_orders_count', 0)}")
        print(f"无止盈活跃订单数量: {result.get('no_tp_active_orders_count', 0)}")
        print(f"无止损挂单数量: {result.get('no_sl_pending_orders_count', 0)}")
        print(f"无止盈挂单数量: {result.get('no_tp_pending_orders_count', 0)}")

        # 显示修复结果
        fixed_orders = result.get('fixed_orders', [])
        if fixed_orders:
            print("\n修复的订单:")
            print("-" * 50)
            for i, order in enumerate(fixed_orders):
                print(f"{i+1}. 订单ID: {order.get('order_id')}")
                print(f"   类型: {order.get('type')}")
                print(f"   旧止损: {order.get('old_sl')}")
                print(f"   新止损: {order.get('new_sl')}")
                print(f"   旧止盈: {order.get('old_tp')}")
                print(f"   新止盈: {order.get('new_tp')}")
                print(f"   风险回报比: {order.get('risk_reward_ratio', 0):.2f}")
                print(f"   修复状态: {'成功' if order.get('success') else '失败'}")
                if not order.get('success'):
                    print(f"   错误信息: {order.get('error', '')}")
                print()
        else:
            print("\n没有需要修复的订单")
    else:
        print("\n止损止盈检查失败！")
        print(f"错误信息: {result.get('message', '')}")

    print(f"\n耗时: {end_time - start_time:.2f}秒")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)


if __name__ == "__main__":
    main()
