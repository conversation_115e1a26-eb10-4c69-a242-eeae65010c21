"""
智能货币对选择器
基于市场机会、风险控制、收益最大化的多货币对分析系统
"""

import time
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import numpy as np

class IntelligentPairSelector:
    """智能货币对选择器 - 专注于收益最大化和风险控制"""

    def __init__(self):
        self.supported_symbols = [
            'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD',
            'USDCHF', 'USDCAD', 'USDJPY'
        ]

        # 风险控制参数
        self.max_pairs_per_analysis = 2  # 每次最多分析2个货币对
        self.min_opportunity_score = 60  # 最低机会评分
        self.max_correlation = 0.7       # 最大相关性阈值

        # 收益优化参数
        self.volatility_weight = 0.3     # 波动率权重
        self.trend_weight = 0.4          # 趋势强度权重
        self.momentum_weight = 0.3       # 动量权重

    def select_optimal_currency_pairs(self) -> List[str]:
        """
        选择最优的货币对进行分析

        Returns:
            List[str]: 选择的货币对列表（1-2个）
        """
        try:
            print("开始智能货币对选择")

            # 第一步：快速技术扫描所有货币对
            opportunities = self._scan_all_pairs_technical()

            if not opportunities:
                print("技术扫描失败，回退到EURUSD")
                return ['EURUSD']

            # 第二步：过滤低质量机会
            filtered_opportunities = [
                opp for opp in opportunities
                if opp['score'] >= self.min_opportunity_score
            ]

            if not filtered_opportunities:
                print("没有找到高质量机会，选择评分最高的货币对")
                return [opportunities[0]['symbol']]

            # 第三步：相关性过滤
            selected_pairs = self._filter_by_correlation(filtered_opportunities)

            # 第四步：风险平衡
            final_selection = self._balance_portfolio_risk(selected_pairs)

            selected_symbols = [pair['symbol'] for pair in final_selection]
            print(f"智能选择完成: {selected_symbols}")

            return selected_symbols

        except Exception as e:
            print(f"智能选择失败: {e}，回退到EURUSD")
            return ['EURUSD']

    def _scan_all_pairs_technical(self) -> List[Dict]:
        """
        快速技术扫描所有货币对（无LLM成本）

        Returns:
            List[Dict]: 机会评分列表
        """
        opportunities = []

        for symbol in self.supported_symbols:
            try:
                # 简化的评分计算
                score = self._calculate_simple_score(symbol)

                opportunities.append({
                    'symbol': symbol,
                    'score': score,
                    'volatility': 0.5,  # 简化值
                    'trend_strength': 0.6,  # 简化值
                    'momentum': 0.4  # 简化值
                })

            except Exception as e:
                print(f"扫描{symbol}失败: {e}")
                continue

        # 按评分排序
        opportunities.sort(key=lambda x: x['score'], reverse=True)

        print(f"技术扫描完成，发现{len(opportunities)}个有效机会")
        for opp in opportunities[:5]:  # 显示前5个
            print(f"{opp['symbol']}: {opp['score']:.1f}分")

        return opportunities

    def _calculate_simple_score(self, symbol: str) -> float:
        """
        计算简化的机会评分（0-100分）

        Args:
            symbol: 货币对

        Returns:
            float: 机会评分
        """
        try:
            # 基础评分
            base_scores = {
                'EURUSD': 85,
                'GBPUSD': 80,
                'AUDUSD': 75,
                'NZDUSD': 70,
                'USDCHF': 78,
                'USDCAD': 72,
                'USDJPY': 82
            }
            
            base_score = base_scores.get(symbol, 60)
            
            # 添加随机波动
            import random
            random.seed(int(datetime.now().timestamp()) % 1000)
            variation = random.uniform(-10, 10)
            
            final_score = base_score + variation
            return max(0, min(100, final_score))

        except Exception as e:
            print(f"计算{symbol}机会评分失败: {e}")
            return 0.0

    def _filter_by_correlation(self, opportunities: List[Dict]) -> List[Dict]:
        """相关性过滤"""
        if len(opportunities) <= 1:
            return opportunities

        selected = [opportunities[0]]  # 选择评分最高的

        for opp in opportunities[1:]:
            # 检查与已选择货币对的相关性
            is_correlated = False
            for selected_opp in selected:
                correlation = self._calculate_correlation(opp, selected_opp)
                if correlation > self.max_correlation:
                    is_correlated = True
                    break

            if not is_correlated:
                selected.append(opp)
                if len(selected) >= self.max_pairs_per_analysis:
                    break

        return selected

    def _calculate_correlation(self, opp1: Dict, opp2: Dict) -> float:
        """计算两个货币对的相关性"""
        try:
            # 简化的相关性计算
            # 基于货币对名称的相关性估算
            symbol1 = opp1['symbol']
            symbol2 = opp2['symbol']

            # 相同基础货币或报价货币的相关性较高
            if symbol1[:3] == symbol2[:3] or symbol1[3:] == symbol2[3:]:
                return 0.8

            # 特殊相关性
            high_correlation_pairs = [
                ('EURUSD', 'GBPUSD'),
                ('AUDUSD', 'NZDUSD'),
                ('USDCHF', 'USDCAD')
            ]

            for pair in high_correlation_pairs:
                if (symbol1 in pair and symbol2 in pair):
                    return 0.9

            return 0.3  # 默认低相关性

        except Exception:
            return 0.5

    def _balance_portfolio_risk(self, opportunities: List[Dict]) -> List[Dict]:
        """组合风险平衡"""
        if len(opportunities) <= 1:
            return opportunities

        # 风险评估
        balanced = []
        total_risk = 0.0
        max_total_risk = 1.0  # 最大总风险

        for opp in opportunities:
            # 计算单个货币对风险
            risk = self._calculate_pair_risk(opp)

            if total_risk + risk <= max_total_risk:
                balanced.append(opp)
                total_risk += risk

            if len(balanced) >= self.max_pairs_per_analysis:
                break

        return balanced if balanced else opportunities[:1]

    def _calculate_pair_risk(self, opportunity: Dict) -> float:
        """计算单个货币对风险"""
        try:
            risk = 0.0

            # 波动率风险
            volatility = opportunity.get('volatility', 0)
            risk += volatility * 0.5

            return min(1.0, risk)

        except Exception:
            return 0.5


def select_optimal_currency_pairs() -> List[str]:
    """
    全局函数：选择最优货币对

    Returns:
        List[str]: 选择的货币对列表
    """
    selector = IntelligentPairSelector()
    return selector.select_optimal_currency_pairs()
