#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantumForex Pro - 真实交易测试脚本
进行真实的MT4交易测试，验证生产环境可用性
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_current_market_price(mt4_client, symbol):
    """获取当前市场价格"""
    try:
        market_info = mt4_client.get_market_info(symbol)
        if market_info and market_info.get('status') == 'success':
            data = market_info.get('data', {})
            return {
                'bid': float(data.get('bid', 0)),
                'ask': float(data.get('ask', 0)),
                'spread': float(data.get('spread', 0))
            }
        return None
    except Exception as e:
        print(f"获取市场价格失败: {e}")
        return None

def calculate_reasonable_sl_tp(symbol, current_price, trade_type, pip_value=0.0001):
    """计算合理的止损止盈价格"""
    try:
        if trade_type.upper() == 'BUY':
            # 买入：止损在下方，止盈在上方
            sl = current_price - (30 * pip_value)  # 30点止损
            tp = current_price + (50 * pip_value)  # 50点止盈
        else:  # SELL
            # 卖出：止损在上方，止盈在下方
            sl = current_price + (30 * pip_value)  # 30点止损
            tp = current_price - (50 * pip_value)  # 50点止盈
        
        # 对于JPY货币对，调整pip值
        if 'JPY' in symbol:
            pip_value = 0.01
            if trade_type.upper() == 'BUY':
                sl = current_price - (30 * pip_value)
                tp = current_price + (50 * pip_value)
            else:
                sl = current_price + (30 * pip_value)
                tp = current_price - (50 * pip_value)
        
        return round(sl, 5), round(tp, 5)
    except Exception as e:
        print(f"计算止损止盈失败: {e}")
        return 0, 0

def test_real_trading():
    """进行真实交易测试"""
    print("=" * 60)
    print("🚀 QuantumForex Pro - 真实交易测试")
    print("=" * 60)
    print("⚠️  警告：这是真实交易测试，将使用真实资金！")
    print("⚠️  请确保您了解风险并同意进行测试！")
    print("=" * 60)
    
    # 确认用户同意
    confirm = input("是否继续进行真实交易测试？(输入 'YES' 确认): ").strip()
    if confirm != 'YES':
        print("❌ 测试已取消")
        return
    
    try:
        # 导入MT4客户端
        from utils.mt4_client import mt4_client
        
        print("\n📋 真实交易测试计划:")
        print("1. 连接MT4服务器")
        print("2. 获取账户信息")
        print("3. 获取当前市场价格")
        print("4. 计算合理的止损止盈")
        print("5. 执行小额测试交易")
        print("6. 监控交易结果")
        print("7. 清理测试订单")
        
        # 1. 连接测试
        print("\n" + "─" * 40)
        print("🔗 1. 连接MT4服务器...")
        connected = mt4_client.connect()
        if not connected:
            print("❌ MT4连接失败，无法进行真实交易测试")
            return
        print("✅ MT4连接成功")
        
        # 2. 获取账户信息
        print("\n💰 2. 获取账户信息...")
        account_info = mt4_client.get_account_info()
        if account_info and account_info.get('status') == 'success':
            data = account_info.get('data', {})
            balance = float(data.get('balance', 0))
            equity = float(data.get('equity', 0))
            free_margin = float(data.get('free_margin', 0))
            
            print(f"   账户余额: ${balance:.2f}")
            print(f"   账户净值: ${equity:.2f}")
            print(f"   可用保证金: ${free_margin:.2f}")
            
            # 检查账户是否有足够资金
            if balance < 100:
                print("❌ 账户余额不足，无法进行交易测试")
                return
        else:
            print("❌ 无法获取账户信息")
            return
        
        # 3. 选择测试货币对和获取价格
        test_symbol = 'EURUSD'  # 使用EURUSD进行测试
        print(f"\n📊 3. 获取{test_symbol}当前价格...")
        
        market_data = get_current_market_price(mt4_client, test_symbol)
        if not market_data:
            print(f"❌ 无法获取{test_symbol}市场价格")
            return
        
        current_bid = market_data['bid']
        current_ask = market_data['ask']
        spread = market_data['spread']
        
        print(f"   当前买价: {current_bid}")
        print(f"   当前卖价: {current_ask}")
        print(f"   点差: {spread}点")
        
        # 4. 计算合理的止损止盈
        print("\n🎯 4. 计算交易参数...")
        
        # 使用最小手数进行测试
        test_lot = 0.01
        trade_type = 'BUY'  # 测试买入
        
        # 使用当前ask价格作为买入价格
        entry_price = current_ask
        sl_price, tp_price = calculate_reasonable_sl_tp(test_symbol, entry_price, trade_type)
        
        print(f"   交易类型: {trade_type}")
        print(f"   交易手数: {test_lot}")
        print(f"   入场价格: {entry_price}")
        print(f"   止损价格: {sl_price}")
        print(f"   止盈价格: {tp_price}")
        
        # 最后确认
        print("\n⚠️  最后确认:")
        print(f"   即将执行: {trade_type} {test_lot}手 {test_symbol}")
        print(f"   预计风险: 约${test_lot * 10000 * 0.003:.2f} (30点止损)")
        print(f"   预计收益: 约${test_lot * 10000 * 0.005:.2f} (50点止盈)")
        
        final_confirm = input("\n确认执行真实交易？(输入 'EXECUTE' 确认): ").strip()
        if final_confirm != 'EXECUTE':
            print("❌ 交易已取消")
            return
        
        # 5. 执行真实交易
        print("\n💼 5. 执行真实交易...")
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始执行买入操作...")
        
        trade_result = mt4_client.buy(
            symbol=test_symbol,
            lot=test_lot,
            sl=sl_price,
            tp=tp_price,
            comment='QuantumForex_Pro_RealTest'
        )
        
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 交易执行结果: {trade_result}")
        
        if trade_result and trade_result.get('status') == 'success':
            order_id = trade_result.get('order_id', 'N/A')
            print(f"✅ 交易执行成功！")
            print(f"   订单ID: {order_id}")
            print(f"   交易详情: {trade_result.get('message', 'N/A')}")
            
            # 6. 监控交易结果
            print("\n📈 6. 监控交易结果...")
            print("等待5秒后检查订单状态...")
            time.sleep(5)
            
            # 获取活跃订单
            active_orders = mt4_client.get_active_orders()
            if active_orders and active_orders.get('status') == 'success':
                orders = active_orders.get('orders', [])
                test_order = None
                
                for order in orders:
                    if order.get('comment') == 'QuantumForex_Pro_RealTest':
                        test_order = order
                        break
                
                if test_order:
                    print(f"✅ 找到测试订单:")
                    print(f"   订单ID: {test_order.get('order_id', 'N/A')}")
                    print(f"   货币对: {test_order.get('symbol', 'N/A')}")
                    print(f"   类型: {test_order.get('type', 'N/A')}")
                    print(f"   手数: {test_order.get('lots', 'N/A')}")
                    print(f"   开仓价: {test_order.get('open_price', 'N/A')}")
                    print(f"   当前价: {test_order.get('current_price', 'N/A')}")
                    print(f"   盈亏: {test_order.get('profit', 'N/A')}")
                    
                    # 7. 清理测试订单（可选）
                    print("\n🧹 7. 清理测试订单...")
                    cleanup = input("是否立即关闭测试订单？(y/n): ").strip().lower()
                    if cleanup in ['y', 'yes', '是']:
                        close_result = mt4_client.close_order(test_order.get('order_id'))
                        if close_result and close_result.get('status') == 'success':
                            print("✅ 测试订单已成功关闭")
                        else:
                            print(f"❌ 关闭订单失败: {close_result.get('message', '未知错误')}")
                    else:
                        print("⚠️  测试订单保留，请手动管理")
                else:
                    print("⚠️  未找到测试订单，可能已被自动处理")
            else:
                print("❌ 无法获取活跃订单状态")
        
        else:
            error_msg = trade_result.get('message', '未知错误') if trade_result else '无响应'
            print(f"❌ 交易执行失败: {error_msg}")
            
            # 分析失败原因
            if '交易执行失败' in error_msg:
                print("\n🔍 可能的失败原因:")
                print("   1. 止损止盈价格设置不合理")
                print("   2. 市场条件不允许交易")
                print("   3. 账户权限限制")
                print("   4. 服务器繁忙或网络问题")
        
        print("\n" + "=" * 60)
        print("✅ 真实交易测试完成！")
        print("=" * 60)
        
    except ImportError as e:
        print(f"❌ 导入MT4客户端失败: {e}")
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_real_trading()
