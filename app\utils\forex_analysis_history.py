"""
外汇分析历史记录工具
用于存储和获取历史分析记录，以便在后续分析中参考
"""
import os
import json
from datetime import datetime, timedelta


# 历史记录文件路径
HISTORY_FILE_PATH = os.path.join(os.path.dirname(__file__), '../data/forex_analysis_history.json')
LATEST_FILE_PATH = os.path.join(os.path.dirname(__file__), '../data/forex_latest_analysis.json')


def ensure_directory_exists():
    """确保目录存在"""
    directory = os.path.dirname(HISTORY_FILE_PATH)
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)


def save_analysis_record(analysis_result):
    """
    保存分析记录

    Args:
        analysis_result (dict): 分析结果
    """
    try:
        ensure_directory_exists()

        # 读取现有历史记录
        history = []
        if os.path.exists(HISTORY_FILE_PATH):
            with open(HISTORY_FILE_PATH, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 处理不同的数据格式
            if isinstance(data, list):
                # 旧格式：直接是数组
                history = data
            elif isinstance(data, dict) and 'analysis_history' in data:
                # 新格式：包含analysis_history字段
                history = data['analysis_history']
            else:
                # 未知格式，创建新的空数组
                history = []

        # 检查是否是多轮分析结果
        is_multi_round = isinstance(analysis_result.get('analysis'), dict) and 'initial' in analysis_result.get('analysis', {})

        # 获取分析文本
        analysis_text = ''
        if is_multi_round:
            # 多轮分析，使用最终分析结果
            analysis_text = analysis_result.get('analysis', {}).get('final', '')
        else:
            # 单轮分析
            analysis_text = analysis_result.get('analysis', '')

        # 获取提示词
        prompt = ''
        if is_multi_round:
            # 多轮分析，使用最终提示词
            prompt = analysis_result.get('prompts', {}).get('final', '')
        else:
            # 单轮分析
            prompt = analysis_result.get('prompt', '')

        # 获取交易指令
        trade_instructions = analysis_result.get('tradeInstructions', {})

        # 确保交易指令中包含最终决策标记
        if is_multi_round and not trade_instructions.get('isFinalDecision'):
            trade_instructions['isFinalDecision'] = True
            print('添加最终决策标记到交易指令')

            # 确保订单管理指令也被标记为最终决策
            if 'orderManagement' in trade_instructions and trade_instructions['orderManagement']:
                for order_action in trade_instructions['orderManagement']:
                    if isinstance(order_action, dict):
                        order_action['isFinalDecision'] = True
                print('添加最终决策标记到订单管理指令')

        # 获取预分析关注点
        pre_analysis_focus_points = []
        if 'preAnalysisFocusPoints' in trade_instructions:
            pre_analysis_focus_points = trade_instructions.get('preAnalysisFocusPoints', [])
            print(f'从交易指令中提取到{len(pre_analysis_focus_points)}个预分析关注点')

        # 创建完整的记录对象
        record = {
            'timestamp': analysis_result.get('timestamp', datetime.now().isoformat()),
            'symbol': analysis_result.get('symbol', 'EURUSD'),
            'currentPrice': analysis_result.get('currentPrice'),
            'analysis': analysis_text,
            'tradeInstructions': trade_instructions,  # 使用可能已更新的交易指令
            'tradeExecuted': analysis_result.get('tradeExecuted', False),  # 添加交易执行状态
            'indicators': {
                'ma5': analysis_result.get('data', {}).get('indicators', {}).get('ma', {}).get(5),
                'ma20': analysis_result.get('data', {}).get('indicators', {}).get('ma', {}).get(20),
                'rsi': analysis_result.get('data', {}).get('indicators', {}).get('rsi')
            },
            # 保存预分析关注点
            'preAnalysisFocusPoints': pre_analysis_focus_points,
            # 保存完整的分析结果，但不包括原始数据（太大）
            'fullAnalysisResult': {
                **analysis_result,
                'data': None,  # 不保存原始数据
                'tradeInstructions': trade_instructions  # 使用可能已更新的交易指令
            },
            # 保存提示词，以便查看分析依据
            'prompt': prompt,
            # 添加分析模式标记
            'analysisMode': 'MULTI_ROUND' if is_multi_round else 'SINGLE_ROUND'
        }

        # 添加到历史记录
        history.insert(0, record)

        # 只保留最近10条记录
        if len(history) > 10:
            history = history[:10]

        # 保存到文件
        with open(HISTORY_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
        print('分析记录已保存到历史文件')

        # 同时保存最新的分析结果到单独的文件，方便快速访问
        with open(LATEST_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(record, f, ensure_ascii=False, indent=2)
        print('最新分析记录已保存到单独文件')

    except Exception as e:
        print(f'保存分析记录失败: {e}')


def get_recent_analysis_records(limit=3):
    """
    获取最近的分析记录

    Args:
        limit (int): 获取的记录数量

    Returns:
        list: 历史分析记录
    """
    try:
        if not os.path.exists(HISTORY_FILE_PATH):
            return []

        with open(HISTORY_FILE_PATH, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 处理不同的数据格式
        if isinstance(data, list):
            # 旧格式：直接是数组
            history = data
        elif isinstance(data, dict) and 'analysis_history' in data:
            # 新格式：包含analysis_history字段
            history = data['analysis_history']
        else:
            # 未知格式，返回空数组
            print(f'历史分析记录格式未知: {type(data)}')
            return []

        return history[:limit]
    except Exception as e:
        print(f'获取历史分析记录失败: {e}')
        return []


def format_history_for_prompt(records):
    """
    格式化历史记录为提示词

    Args:
        records (list): 历史分析记录

    Returns:
        str: 格式化后的历史记录
    """
    if not records or len(records) == 0:
        return '无历史分析记录'

    result = '## 历史分析记录\n\n'

    for i, record in enumerate(records):
        date = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
        result += f"### {i + 1}. {date} 分析\n"
        result += f"- 价格: {record['currentPrice']}\n"

        # 添加交易方向
        if record.get('tradeInstructions'):
            action = record['tradeInstructions'].get('action')
            if action == 'BUY':
                direction = '买入'
            elif action == 'SELL':
                direction = '卖出'
            else:
                direction = '观望'
            result += f"- 交易方向: {direction}\n"
        else:
            result += "- 交易方向: 未知\n"

        # 添加技术指标
        if record.get('indicators'):
            ma5 = record['indicators'].get('ma5', 0)
            ma20 = record['indicators'].get('ma20', 0)
            rsi = record['indicators'].get('rsi', 0)

            if ma5 and ma20 and rsi:
                result += f"- 技术指标: MA5={ma5:.5f}, MA20={ma20:.5f}, RSI={rsi:.2f}\n"

        # 添加主要结论
        if record.get('tradeInstructions') and record['tradeInstructions'].get('reasoning'):
            result += f"- 主要结论: {record['tradeInstructions']['reasoning']}\n\n"
        elif record.get('analysis'):
            # 如果没有reasoning字段，尝试从分析文本中提取结论
            conclusion = extract_main_conclusion(record['analysis'])
            result += f"- 主要结论: {conclusion}\n\n"
        else:
            result += "- 主要结论: 无\n\n"

    return result


def extract_main_conclusion(analysis):
    """
    从分析文本中提取主要结论

    Args:
        analysis (str): 分析文本

    Returns:
        str: 主要结论
    """
    import re

    # 尝试提取结论部分
    conclusion_match = re.search(r'结论[：:](.*?)(?=\n\n|\n##|$)', analysis, re.IGNORECASE | re.DOTALL)
    if conclusion_match:
        return conclusion_match.group(1).strip()

    # 尝试提取交易建议部分
    recommendation_match = re.search(r'交易建议[：:](.*?)(?=\n\n|\n##|$)', analysis, re.IGNORECASE | re.DOTALL)
    if recommendation_match:
        return recommendation_match.group(1).strip()

    # 如果都没有找到，返回分析的前100个字符
    return analysis[:100].replace('\n', ' ') + '...'


def get_latest_analysis_record():
    """
    获取最新的分析记录

    Returns:
        dict|None: 最新的分析记录，如果不存在则返回None
    """
    try:
        if not os.path.exists(LATEST_FILE_PATH):
            print('最新分析记录文件不存在')
            return None

        try:
            with open(LATEST_FILE_PATH, 'r', encoding='utf-8') as f:
                latest_record = json.load(f)

            # 检查记录是否有效
            if not latest_record or 'timestamp' not in latest_record:
                print('最新分析记录无效')
                return None

            # 检查记录是否过期（超过24小时）
            record_time = datetime.fromisoformat(latest_record['timestamp'].replace('Z', '+00:00'))
            now = datetime.now()
            time_diff = now - record_time
            hours_diff = time_diff.total_seconds() / 3600

            if hours_diff > 24:
                print('最新分析记录已过期（超过24小时）')
                return None

            print(f'找到有效的最新分析记录，时间: {record_time.isoformat()}')

            # 恢复完整的分析结果
            if 'fullAnalysisResult' in latest_record:
                result = {
                    **latest_record['fullAnalysisResult'],
                    'fromCache': True
                }
                return result

            return {
                'timestamp': latest_record['timestamp'],
                'symbol': latest_record['symbol'],
                'currentPrice': latest_record['currentPrice'],
                'analysis': latest_record['analysis'],
                'tradeInstructions': latest_record['tradeInstructions'],
                'fromCache': True
            }
        except Exception as e:
            print(f'解析最新分析记录文件失败: {e}')
            return None
    except Exception as e:
        print(f'获取最新分析记录失败: {e}')
        return None


def get_analysis_records(count=3):
    """
    获取指定数量的分析记录

    Args:
        count (int): 要获取的记录数量

    Returns:
        list: 分析记录数组
    """
    return get_recent_analysis_records(count)


def get_latest_analysis():
    """
    获取最新的分析记录（完整格式）

    Returns:
        dict: 最新的分析记录
    """
    try:
        # 尝试从最新分析文件中读取
        if os.path.exists(LATEST_FILE_PATH):
            with open(LATEST_FILE_PATH, 'r', encoding='utf-8') as f:
                return json.load(f)

        # 如果最新分析文件不存在，从历史记录中获取最新的
        records = get_recent_analysis_records(limit=1)
        if records:
            return records[0]

        return None
    except Exception as e:
        print(f"获取最新分析记录失败: {e}")
        return None


def get_analysis_history():
    """
    获取所有分析历史记录

    Returns:
        list: 分析历史记录列表
    """
    try:
        if not os.path.exists(HISTORY_FILE_PATH):
            return []

        with open(HISTORY_FILE_PATH, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"获取分析历史记录失败: {e}")
        return []
