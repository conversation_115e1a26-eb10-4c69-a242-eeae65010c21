"""
外汇交易系统应用
支持基于市场变化的实时分析
"""
import os
import sys
import time
from datetime import datetime

# 设置UTF-8编码（必须在导入其他模块之前）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    # 设置控制台编码
    import codecs
    try:
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
    except:
        pass  # 如果已经设置过或不支持，忽略错误

from flask import Flask, jsonify

try:
    from dotenv import load_dotenv
    # 加载环境变量
    load_dotenv(encoding='utf-8')

    # 尝试加载本地环境变量（包含授权信息）
    local_env = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env.local')
    if os.path.exists(local_env):
        load_dotenv(local_env, encoding='utf-8')
except ImportError:
    print("警告: python-dotenv 未安装，跳过环境变量加载")

# 全局变量，用于防止重复执行分析任务
analysis_task_initialized = False

# 全局变量，用于跟踪最后一次分析时间，防止短时间内重复分析
last_analysis_time = None

# 全局变量，用于防止重复启动MT4客户端服务器
client_server_initialized = False

# 分析模式配置
# 可选值: 'hourly'（每小时分析）, 'realtime'（实时分析）, 'hybrid'（混合模式）
ANALYSIS_MODE = os.environ.get('ANALYSIS_MODE', 'hourly').lower()

# 实时分析配置
REALTIME_CHECK_INTERVAL = int(os.environ.get('REALTIME_CHECK_INTERVAL', '60'))  # 市场检查间隔（秒）

# MT4客户端服务器配置
ENABLE_CLIENT_SERVER = os.environ.get('ENABLE_CLIENT_SERVER', 'true').lower() == 'true'  # 是否启用MT4客户端服务器

# MT4服务器跳过模式（智能判断）
def should_skip_mt4_connection():
    """
    智能判断是否应该跳过MT4连接
    优先级：手动设置 > 市场时间自动判断
    """
    # 首先检查手动设置的环境变量
    manual_skip = os.environ.get('SKIP_MT4_CONNECTION', '').lower()
    if manual_skip == 'true':
        return True
    elif manual_skip == 'false':
        return False

    # 如果没有手动设置，则根据市场时间自动判断
    try:
        from app.utils.market_time_checker import is_market_open
        market_open = is_market_open()
        return not market_open  # 市场关闭时跳过MT4连接
    except Exception:
        return False  # 无法判断时默认不跳过


def create_app():
    """创建Flask应用"""
    # 设置模板目录
    template_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'templates')
    app = Flask(__name__, template_folder=template_dir)

    # 配置
    app.config['JSON_AS_ASCII'] = False
    app.config['JSON_SORT_KEYS'] = False

    # 注册蓝图
    from app.routes.forex_trading_routes import forex_trading_bp
    from app.routes.monitoring_routes import monitoring_bp
    app.register_blueprint(forex_trading_bp)
    app.register_blueprint(monitoring_bp)

    # 根路由
    @app.route('/')
    def index():
        return jsonify({
            'message': '外汇交易系统API服务器',
            'version': '1.0.0',
            'status': 'running'
        })

    # 系统状态路由
    @app.route('/system/status')
    def system_status():
        try:
            from app.utils.async_monitor_starter import get_systems_status
            status = get_systems_status()
            return jsonify({
                'success': True,
                'data': status,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }), 500

    # 启动MT4客户端服务器
    global client_server_initialized

    # 检查是否跳过MT4连接
    skip_mt4 = should_skip_mt4_connection()
    if skip_mt4:
        # 判断跳过原因
        manual_skip = os.environ.get('SKIP_MT4_CONNECTION', '').lower()
        if manual_skip == 'true':
            print('⚠️  MT4服务器跳过模式已启用（手动设置），将跳过所有MT4相关连接')
            print('⚠️  这是手动测试模式，如需恢复请设置 SKIP_MT4_CONNECTION=false')
        else:
            print('🕒 MT4服务器跳过模式已启用（市场关闭），将跳过所有MT4相关连接')
            print('🕒 系统检测到市场关闭时间，自动启用跳过模式')

    # 防止重复初始化客户端服务器
    if ENABLE_CLIENT_SERVER and not client_server_initialized and not skip_mt4:
        try:
            print('正在启动MT4客户端服务器...')
            from app.utils import client_server

            # 在单独的线程中启动服务器
            client_server.start_server_thread()

            client_server_initialized = True
            print('MT4客户端服务器初始化完成')
        except Exception as e:
            print(f'初始化MT4客户端服务器时出错: {e}')
    elif not ENABLE_CLIENT_SERVER:
        print('MT4客户端服务器已禁用，跳过初始化')
    elif skip_mt4:
        print('MT4服务器跳过模式已启用，跳过客户端服务器初始化')
    else:
        print('MT4客户端服务器已经初始化，跳过重复初始化')

    # 使用新的异步启动器启动所有系统
    try:
        from app.utils.async_monitor_starter import start_all_systems_async

        # 异步启动监控系统和分析系统
        starter = start_all_systems_async(
            app=app,
            monitor_interval=60,  # 监控系统每分钟收集一次数据
            analysis_delay=5      # 分析系统延迟5秒启动
        )

        # 设置全局标志
        global analysis_task_initialized
        analysis_task_initialized = True

        print('✅ 异步启动器已配置完成')

    except Exception as e:
        print(f'❌ 配置异步启动器时出错: {e}')
        import traceback
        traceback.print_exc()

    return app
