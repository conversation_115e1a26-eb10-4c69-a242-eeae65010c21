"""
QuantumForex Pro - 交易执行引擎
智能订单执行和仓位管理
"""

import sys
import os
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
import time

# 添加项目路径以导入现有的MT4客户端
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

try:
    from config.config import config
except ImportError:
    # 如果无法导入配置，使用默认配置
    config = {
        'mt4': {
            'server_address': 'tcp://127.0.0.1:5555',
            'timeout': 30000
        }
    }

class OrderType(Enum):
    """订单类型枚举"""
    BUY = "BUY"
    SELL = "SELL"
    BUY_LIMIT = "BUY_LIMIT"
    SELL_LIMIT = "SELL_LIMIT"
    BUY_STOP = "BUY_STOP"
    SELL_STOP = "SELL_STOP"

class OrderStatus(Enum):
    """订单状态枚举"""
    PENDING = "PENDING"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    PARTIAL = "PARTIAL"

@dataclass
class TradeOrder:
    """交易订单数据结构"""
    symbol: str
    order_type: OrderType
    volume: float
    entry_price: float
    stop_loss: float
    take_profit: float
    comment: str
    timestamp: str
    order_id: Optional[str] = None
    status: OrderStatus = OrderStatus.PENDING
    fill_price: Optional[float] = None
    fill_time: Optional[str] = None

class TradeExecutor:
    """
    交易执行引擎
    负责执行交易决策和管理订单
    """

    def __init__(self):
        self.config = config
        self.active_orders = {}
        self.order_history = []
        self.mt4_client = None

        # 初始化MT4连接
        self._initialize_mt4_connection()

        # 初始化全局持仓协调器
        self.global_coordinator = None
        self._initialize_global_coordinator()

        # 初始化组合交易管理
        self.combo_risk_manager = None
        self.combo_position_manager = None
        self._initialize_combo_managers()

    def _initialize_mt4_connection(self):
        """初始化MT4连接"""
        try:
            from utils.mt4_client import MT4Client
            self.mt4_client = MT4Client()
            print("✅ MT4客户端初始化完成")
        except Exception as e:
            print(f"⚠️ MT4客户端初始化失败: {e}")
            self.mt4_client = None

    def _initialize_global_coordinator(self):
        """初始化全局持仓协调器"""
        try:
            from core.global_position_coordinator import GlobalPositionCoordinator
            self.global_coordinator = GlobalPositionCoordinator(trade_executor=self)
            print("✅ 全局持仓协调器初始化完成")
        except Exception as e:
            print(f"⚠️ 全局持仓协调器初始化失败: {e}")
            self.global_coordinator = None

    def _initialize_combo_managers(self):
        """初始化组合交易管理器"""
        try:
            from core.portfolio_manager.combo_risk_manager import ComboRiskManager
            from core.portfolio_manager.combo_position_manager import create_combo_position_manager

            # 创建组合风险管理器
            self.combo_risk_manager = ComboRiskManager()

            # 创建组合持仓管理器
            self.combo_position_manager = create_combo_position_manager(
                trade_executor=self,
                combo_risk_manager=self.combo_risk_manager
            )

            print("✅ 组合交易管理器初始化完成")
        except Exception as e:
            print(f"⚠️ 组合交易管理器初始化失败: {e}")
            self.combo_risk_manager = None
            self.combo_position_manager = None

    def _get_mt4_real_positions(self, symbol: str = None) -> List[Dict]:
        """从MT4服务器获取真实持仓"""
        try:
            if not self.mt4_client or not self.mt4_client.connect():
                print("❌ MT4未连接，无法获取真实持仓")
                return []

            print("🔍 从MT4服务器获取真实持仓...")

            # 使用MT4客户端获取活跃订单
            response = self.mt4_client.get_active_orders()

            if response and response.get('status') == 'success':
                orders = response.get('orders', [])
                print(f"📊 MT4服务器返回{len(orders)}个活跃订单")

                # 转换为标准格式
                positions = []
                for order in orders:
                    pos = {
                        'order_id': str(order.get('ticket', order.get('order_id', 'unknown'))),
                        'symbol': order.get('symbol', ''),
                        'action': 'BUY' if order.get('type', 0) == 0 else 'SELL',
                        'volume': float(order.get('lots', order.get('volume', 0))),
                        'entry_price': float(order.get('open_price', order.get('price', 0))),
                        'stop_loss': float(order.get('sl', 0)),
                        'take_profit': float(order.get('tp', 0)),
                        'open_time': order.get('open_time', ''),
                        'comment': order.get('comment', ''),
                        'profit': float(order.get('profit', 0))
                    }

                    # 如果指定了货币对，只返回该货币对的持仓
                    if symbol is None or pos['symbol'] == symbol:
                        positions.append(pos)

                return positions
            else:
                print("⚠️ 获取MT4持仓失败或无持仓")
                return []

        except Exception as e:
            print(f"❌ 获取MT4持仓异常: {e}")
            import traceback
            traceback.print_exc()
            return []

    def execute_combo_trade(self, combo_trade) -> Dict:
        """执行组合交易"""
        try:
            from core.portfolio_manager.combo_trading_manager import ComboTrade

            print(f"🔄 开始执行组合交易: {combo_trade.combo_id}")
            print(f"   组合类型: {combo_trade.combo_type.value}")
            print(f"   货币对: {combo_trade.symbols}")
            print(f"   方向: {combo_trade.directions}")
            print(f"   仓位: {combo_trade.position_sizes}")

            executed_orders = []
            failed_orders = []
            entry_prices = []

            # 逐个执行组合中的每个交易
            for i, symbol in enumerate(combo_trade.symbols):
                direction = combo_trade.directions[i]
                volume = combo_trade.position_sizes[i]

                print(f"🎯 执行第{i+1}个交易: {symbol} {direction.upper()} {volume}手")

                # 转换方向为标准格式
                action = 'BUY' if direction.lower() == 'long' else 'SELL'

                # 执行单个交易
                result = self.execute_trade(
                    symbol=symbol,
                    action=action,  # 使用标准的BUY/SELL
                    volume=volume,
                    comment=f"COMBO_{combo_trade.combo_id}_{i+1}",
                    combo_mode=True  # 标记为组合模式，跳过单独的风控检查
                )

                if result and result.get('success', False):
                    order_id = result.get('order_id')
                    entry_price = result.get('entry_price', 0.0)

                    executed_orders.append(order_id)
                    entry_prices.append(entry_price)

                    print(f"✅ 第{i+1}个交易执行成功: 订单号 {order_id}")
                else:
                    failed_orders.append(f"{symbol}_{direction}")
                    entry_prices.append(0.0)

                    print(f"❌ 第{i+1}个交易执行失败: {symbol} {direction}")

            # 判断组合执行结果
            success_count = len(executed_orders)
            total_count = len(combo_trade.symbols)

            if success_count == total_count:
                # 全部成功，注册组合
                if self.combo_risk_manager:
                    self.combo_risk_manager.register_combo(
                        combo_trade=combo_trade,
                        order_ids=executed_orders,
                        entry_prices=entry_prices
                    )

                print(f"🎉 组合交易全部执行成功: {success_count}/{total_count}")
                return {
                    'success': True,
                    'combo_id': combo_trade.combo_id,
                    'executed_orders': executed_orders,
                    'failed_orders': failed_orders,
                    'entry_prices': entry_prices,
                    'message': f'组合交易全部执行成功 ({success_count}/{total_count})'
                }

            elif success_count > 0:
                # 部分成功，需要处理
                print(f"⚠️ 组合交易部分执行成功: {success_count}/{total_count}")
                print("🔄 考虑回滚已执行的订单以保持组合完整性...")

                # 可选：回滚已执行的订单
                # self._rollback_partial_combo(executed_orders)

                return {
                    'success': False,
                    'combo_id': combo_trade.combo_id,
                    'executed_orders': executed_orders,
                    'failed_orders': failed_orders,
                    'entry_prices': entry_prices,
                    'message': f'组合交易部分失败 ({success_count}/{total_count})'
                }

            else:
                # 全部失败
                print(f"❌ 组合交易全部执行失败: {success_count}/{total_count}")
                return {
                    'success': False,
                    'combo_id': combo_trade.combo_id,
                    'executed_orders': executed_orders,
                    'failed_orders': failed_orders,
                    'entry_prices': entry_prices,
                    'message': f'组合交易全部失败'
                }

        except Exception as e:
            print(f"❌ 组合交易执行异常: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'combo_id': getattr(combo_trade, 'combo_id', 'unknown'),
                'executed_orders': [],
                'failed_orders': [],
                'entry_prices': [],
                'message': f'组合交易执行异常: {e}'
            }

    def _check_existing_position(self, symbol: str, action: str) -> Dict:
        """检查现有持仓（优先从MT4获取真实数据）"""
        try:
            print(f"🔍 检查{symbol}的现有持仓...")

            # 优先从MT4获取真实持仓
            mt4_positions = self._get_mt4_real_positions(symbol)

            if mt4_positions:
                print(f"📊 MT4服务器上{symbol}有{len(mt4_positions)}个持仓")
                existing_positions = mt4_positions
            else:
                print(f"📊 MT4服务器上{symbol}无持仓，检查本地记录...")
                # 如果MT4没有持仓，检查本地记录（可能是网络问题）
                existing_positions = []
                for order_id, order in self.active_orders.items():
                    if order.symbol == symbol:
                        existing_positions.append({
                            'order_id': order_id,
                            'symbol': order.symbol,
                            'action': order.order_type.value,
                            'volume': order.volume,
                            'entry_price': order.fill_price,
                            'status': order.status.value
                        })
                print(f"📊 本地记录{symbol}有{len(existing_positions)}个持仓")

            # 检查是否有相同方向的持仓
            same_direction_positions = [
                pos for pos in existing_positions
                if pos['action'] == action
            ]

            # 检查是否有相反方向的持仓
            opposite_direction_positions = [
                pos for pos in existing_positions
                if (pos['action'] == 'BUY' and action == 'SELL') or
                   (pos['action'] == 'SELL' and action == 'BUY')
            ]

            # 详细输出检查结果
            if same_direction_positions:
                print(f"⚠️ 发现{len(same_direction_positions)}个相同方向({action})持仓")
                for pos in same_direction_positions:
                    print(f"   订单号: {pos['order_id']}, 手数: {pos['volume']}")

            if opposite_direction_positions:
                print(f"⚠️ 发现{len(opposite_direction_positions)}个相反方向持仓")
                for pos in opposite_direction_positions:
                    print(f"   订单号: {pos['order_id']}, 方向: {pos['action']}, 手数: {pos['volume']}")

            return {
                'has_existing': len(existing_positions) > 0,
                'same_direction': same_direction_positions,
                'opposite_direction': opposite_direction_positions,
                'total_positions': len(existing_positions),
                'total_volume': sum(pos['volume'] for pos in existing_positions),
                'all_positions': existing_positions
            }

        except Exception as e:
            print(f"❌ 检查持仓失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'has_existing': False,
                'same_direction': [],
                'opposite_direction': [],
                'total_positions': 0,
                'total_volume': 0,
                'all_positions': []
            }

    def _should_execute_trade(self, symbol: str, action: str, volume: float) -> Dict:
        """判断是否应该执行交易"""
        try:
            # 检查现有持仓
            position_check = self._check_existing_position(symbol, action)

            # 持仓管理规则（严格限制防止重复订单）
            max_positions_per_symbol = 2  # 每个货币对最多2个持仓
            max_same_direction = 1  # 同一方向最多1个持仓
            max_total_volume_per_symbol = 0.10  # 每个货币对最大总仓位0.10手
            max_global_positions = 10  # 全局最多10个持仓

            # 规则1：检查全局持仓数量
            total_global_positions = len(self.active_orders)
            if total_global_positions >= max_global_positions:
                return {
                    'should_execute': False,
                    'reason': f"已达到全局最大持仓数量限制({max_global_positions})",
                    'action': 'skip',
                    'details': position_check
                }

            # 规则2：检查单个货币对持仓数量
            if position_check['total_positions'] >= max_positions_per_symbol:
                return {
                    'should_execute': False,
                    'reason': f"货币对{symbol}已达到最大持仓数量限制({max_positions_per_symbol})",
                    'action': 'skip',
                    'details': position_check
                }

            # 规则3：检查相同方向持仓数量
            same_direction_count = len(position_check['same_direction'])
            if same_direction_count >= max_same_direction:
                return {
                    'should_execute': False,
                    'reason': f"已有{same_direction_count}个相同方向({action})持仓，达到限制({max_same_direction})",
                    'action': 'skip',
                    'details': position_check['same_direction']
                }

            # 规则3.5：检查价格相似性（防止相似价格的重复订单）
            price_similarity_check = self._check_price_similarity(symbol, action, volume)
            if not price_similarity_check['allow']:
                return {
                    'should_execute': False,
                    'reason': price_similarity_check['reason'],
                    'action': 'skip',
                    'details': price_similarity_check['details']
                }

            # 规则4：检查总仓位大小
            if position_check['total_volume'] + volume > max_total_volume_per_symbol:
                return {
                    'should_execute': False,
                    'reason': f"货币对{symbol}总仓位将超过限制({max_total_volume_per_symbol}手)",
                    'action': 'skip',
                    'details': position_check
                }

            # 规则5：检查总体风险控制
            all_positions = list(self.active_orders.values())
            if all_positions:
                total_profit = sum(self._calculate_order_profit(order) for order in all_positions)
                if total_profit <= -40:  # 总亏损超过$40时限制新开仓
                    return {
                        'should_execute': False,
                        'reason': f"总亏损${total_profit:.2f}过大，暂停新开仓",
                        'action': 'skip',
                        'details': position_check
                    }

            # 规则6：相反方向持仓处理（允许适度对冲）
            opposite_direction_count = len(position_check['opposite_direction'])
            if opposite_direction_count >= 2 and same_direction_count == 0:
                # 如果相反方向已有2个持仓，且当前方向没有持仓，允许开仓进行对冲
                return {
                    'should_execute': True,
                    'reason': f"对冲交易：平衡{opposite_direction_count}个相反方向持仓",
                    'action': 'execute',
                    'details': position_check
                }

            # 规则5：允许执行
            return {
                'should_execute': True,
                'reason': "无持仓冲突，允许执行",
                'action': 'execute',
                'details': position_check
            }

        except Exception as e:
            print(f"❌ 持仓检查失败: {e}")
            return {
                'should_execute': False,
                'reason': f"持仓检查异常: {e}",
                'action': 'error',
                'details': {}
            }

    def _check_price_similarity(self, symbol: str, action: str, volume: float) -> Dict:
        """检查价格相似性，防止相似价格的重复订单"""
        try:
            # 获取当前市场价格
            current_price = self._get_current_market_price(symbol)
            if not current_price:
                print(f"⚠️ 无法获取{symbol}当前价格，为安全起见拒绝交易")
                return {'allow': False, 'reason': '无法获取当前价格，安全限制', 'details': {}}

            # 检查现有持仓的价格
            similar_positions = []
            price_threshold = 0.0020  # 20点的价格差异阈值（比原来的5点更严格）

            for order_id, order in self.active_orders.items():
                if order.symbol == symbol:
                    # 检查价格相似性
                    price_diff = abs(order.entry_price - current_price)

                    if price_diff <= price_threshold:
                        similar_positions.append({
                            'order_id': order_id,
                            'entry_price': order.entry_price,
                            'current_price': current_price,
                            'price_diff': price_diff,
                            'action': order.order_type.value,
                            'volume': order.volume
                        })

            # 如果有相似价格的订单，检查是否应该阻止
            if similar_positions:
                # 检查是否有相同方向的相似价格订单
                same_direction_similar = [
                    pos for pos in similar_positions
                    if (action == 'BUY' and pos['action'] == 'BUY') or
                       (action == 'SELL' and pos['action'] == 'SELL')
                ]

                if same_direction_similar:
                    return {
                        'allow': False,
                        'reason': f"检测到{len(same_direction_similar)}个相似价格的相同方向订单",
                        'details': {
                            'similar_positions': same_direction_similar,
                            'price_threshold': price_threshold,
                            'current_price': current_price
                        }
                    }

                # 检查总的相似价格订单数量
                if len(similar_positions) >= 2:
                    return {
                        'allow': False,
                        'reason': f"该价格附近已有{len(similar_positions)}个订单，避免过度集中",
                        'details': {
                            'similar_positions': similar_positions,
                            'price_threshold': price_threshold,
                            'current_price': current_price
                        }
                    }

            return {'allow': True, 'reason': '价格检查通过', 'details': {}}

        except Exception as e:
            print(f"❌ 价格相似性检查失败: {e}")
            return {'allow': False, 'reason': f'价格检查异常: {e}', 'details': {}}

    def _get_current_market_price(self, symbol: str) -> Optional[float]:
        """获取当前市场价格"""
        try:
            if self.mt4_client and self.mt4_client.connect():
                # 使用MT4的get_market_info方法获取价格
                market_info = self.mt4_client.get_market_info(symbol)
                if market_info and market_info.get('status') == 'success':
                    # 正确获取data字段中的价格数据
                    data = market_info.get('data', {})
                    bid = data.get('bid', 0)
                    ask = data.get('ask', 0)
                    if bid > 0 and ask > 0:
                        mid_price = (bid + ask) / 2
                        print(f"📊 获取{symbol}当前价格: Bid={bid}, Ask={ask}, Mid={mid_price}")
                        return mid_price
                    else:
                        print(f"⚠️ {symbol}价格数据无效: Bid={bid}, Ask={ask}")
                else:
                    print(f"⚠️ {symbol}市场信息获取失败: {market_info}")

            print(f"⚠️ 无法获取{symbol}的当前价格")
            return None

        except Exception as e:
            print(f"❌ 获取当前价格失败: {e}")
            return None

    def _print_current_positions(self):
        """打印当前持仓状态（优先显示MT4真实持仓）"""
        try:
            print("🔍 获取当前持仓状态...")

            # 优先从MT4获取真实持仓
            mt4_positions = self._get_mt4_real_positions()

            if mt4_positions:
                print(f"📊 MT4服务器真实持仓 ({len(mt4_positions)}个):")
                print("="*60)

                total_profit = 0
                for pos in mt4_positions:
                    profit = pos.get('profit', 0)
                    total_profit += profit

                    print(f"🔹 {pos['symbol']} {pos['action']} {pos['volume']}手")
                    print(f"   📋 订单号: {pos['order_id']}")
                    print(f"   💰 入场价: {pos['entry_price']}")
                    print(f"   🛡️ 止损: {pos['stop_loss']}")
                    print(f"   🎯 止盈: {pos['take_profit']}")
                    print(f"   📈 当前盈亏: ${profit:.2f}")
                    print(f"   🕐 开仓时间: {pos['open_time']}")
                    print(f"   💬 备注: {pos['comment']}")
                    print("   " + "-"*50)

                print(f"💰 总盈亏: ${total_profit:.2f}")
                print("="*60)

                # 同步到本地记录
                self._sync_mt4_positions_to_local(mt4_positions)

            else:
                print("📊 MT4服务器无持仓")

                # 检查本地记录
                if self.active_orders:
                    print(f"⚠️ 但本地记录有{len(self.active_orders)}个持仓，可能存在数据不同步")
                    print("📊 本地记录持仓:")
                    for order_id, order in self.active_orders.items():
                        print(f"   {order.symbol} {order.order_type.value} {order.volume}手 (订单号: {order_id})")

                    # 自动同步数据
                    print("🔄 启动自动同步机制...")
                    self._auto_sync_positions()
                else:
                    print("✅ 确认无任何持仓")

        except Exception as e:
            print(f"❌ 显示持仓失败: {e}")
            import traceback
            traceback.print_exc()

    def _sync_mt4_positions_to_local(self, mt4_positions: List[Dict]):
        """将MT4持仓同步到本地记录"""
        try:
            print("🔄 同步MT4持仓到本地记录...")

            # 清空本地记录
            self.active_orders.clear()

            # 将MT4持仓转换为本地格式
            for pos in mt4_positions:
                order = TradeOrder(
                    symbol=pos['symbol'],
                    order_type=OrderType.BUY if pos['action'] == 'BUY' else OrderType.SELL,
                    volume=pos['volume'],
                    entry_price=pos['entry_price'],
                    stop_loss=pos['stop_loss'],
                    take_profit=pos['take_profit'],
                    comment=pos['comment'],
                    timestamp=pos['open_time'],
                    order_id=pos['order_id'],
                    status=OrderStatus.FILLED,
                    fill_price=pos['entry_price'],
                    fill_time=pos['open_time']
                )

                self.active_orders[pos['order_id']] = order

            print(f"✅ 已同步{len(mt4_positions)}个MT4持仓到本地记录")

        except Exception as e:
            print(f"❌ 同步持仓失败: {e}")
            import traceback
            traceback.print_exc()

    def _auto_sync_positions(self):
        """自动同步持仓数据"""
        try:
            print("🔍 开始自动同步持仓数据...")

            # 1. 重新尝试获取MT4活跃订单
            print("📡 重新连接MT4服务器获取活跃订单...")
            mt4_positions = self._get_mt4_real_positions()

            if mt4_positions:
                print(f"✅ 重新获取到{len(mt4_positions)}个MT4持仓")
                self._sync_mt4_positions_to_local(mt4_positions)
                return

            # 2. 如果仍然无法获取活跃订单，查询历史记录验证
            print("📚 查询MT4历史记录验证本地持仓...")
            verified_positions = self._verify_positions_with_history()

            if verified_positions is not None:
                if len(verified_positions) == 0:
                    print("✅ 历史记录确认：所有本地持仓已平仓，清理本地记录")
                    self.active_orders.clear()
                else:
                    print(f"✅ 历史记录确认：{len(verified_positions)}个持仓仍然有效")
                    self._update_local_positions_from_history(verified_positions)
            else:
                print("⚠️ 无法验证持仓状态，保持本地记录不变")
                print("💡 建议手动检查MT4平台确认持仓状态")

        except Exception as e:
            print(f"❌ 自动同步失败: {e}")
            import traceback
            traceback.print_exc()

    def _verify_positions_with_history(self) -> Optional[List[Dict]]:
        """通过历史记录验证持仓状态"""
        try:
            if not self.mt4_client or not self.mt4_client.connect():
                print("❌ MT4未连接，无法查询历史记录")
                return None

            verified_positions = []

            # 检查每个本地持仓
            for order_id, order in self.active_orders.items():
                print(f"🔍 验证订单 {order_id} ({order.symbol} {order.order_type.value})...")

                # 查询该订单的历史记录
                is_still_active = self._check_order_in_history(order_id)

                if is_still_active is True:
                    # 订单仍然活跃
                    verified_positions.append({
                        'order_id': order_id,
                        'symbol': order.symbol,
                        'action': order.order_type.value,
                        'volume': order.volume,
                        'entry_price': order.entry_price,
                        'stop_loss': order.stop_loss,
                        'take_profit': order.take_profit,
                        'open_time': order.timestamp,
                        'comment': order.comment,
                        'profit': 0  # 无法从历史记录获取当前盈亏
                    })
                    print(f"✅ 订单 {order_id} 仍然活跃")
                elif is_still_active is False:
                    print(f"❌ 订单 {order_id} 已平仓")
                else:
                    print(f"⚠️ 订单 {order_id} 状态未知")
                    # 状态未知时，保守处理，假设仍然活跃
                    verified_positions.append({
                        'order_id': order_id,
                        'symbol': order.symbol,
                        'action': order.order_type.value,
                        'volume': order.volume,
                        'entry_price': order.entry_price,
                        'stop_loss': order.stop_loss,
                        'take_profit': order.take_profit,
                        'open_time': order.timestamp,
                        'comment': order.comment,
                        'profit': 0
                    })

            return verified_positions

        except Exception as e:
            print(f"❌ 验证持仓历史记录失败: {e}")
            return None

    def _check_order_in_history(self, order_id: str) -> Optional[bool]:
        """检查订单在历史记录中的状态"""
        try:
            # 这里需要调用MT4的历史记录查询功能
            # 由于MT4客户端可能没有直接的历史记录查询API，
            # 我们可以尝试通过其他方式验证

            # 方法1：尝试查询特定订单
            if hasattr(self.mt4_client, 'get_order_history'):
                history = self.mt4_client.get_order_history(order_id)
                if history:
                    # 如果找到历史记录，检查订单状态
                    if history.get('status') == 'closed':
                        return False  # 订单已关闭
                    else:
                        return True   # 订单可能仍然活跃

            # 方法2：尝试修改订单（如果订单不存在会失败）
            # 这是一个安全的测试方法，不会实际修改订单
            if hasattr(self.mt4_client, 'check_order_exists'):
                exists = self.mt4_client.check_order_exists(order_id)
                return exists

            # 方法3：如果没有专门的API，返回None表示无法确定
            print(f"⚠️ 无法确定订单 {order_id} 的状态，MT4客户端缺少历史查询功能")
            return None

        except Exception as e:
            print(f"❌ 检查订单 {order_id} 历史记录失败: {e}")
            return None

    def _update_local_positions_from_history(self, verified_positions: List[Dict]):
        """根据历史记录验证结果更新本地持仓"""
        try:
            print("🔄 根据历史记录更新本地持仓...")

            # 清空本地记录
            self.active_orders.clear()

            # 重新添加验证过的持仓
            for pos in verified_positions:
                order = TradeOrder(
                    symbol=pos['symbol'],
                    order_type=OrderType.BUY if pos['action'] == 'BUY' else OrderType.SELL,
                    volume=pos['volume'],
                    entry_price=pos['entry_price'],
                    stop_loss=pos['stop_loss'],
                    take_profit=pos['take_profit'],
                    comment=pos['comment'],
                    timestamp=pos['open_time'],
                    order_id=pos['order_id'],
                    status=OrderStatus.FILLED,
                    fill_price=pos['entry_price'],
                    fill_time=pos['open_time']
                )

                self.active_orders[pos['order_id']] = order

            print(f"✅ 已更新本地记录，保留{len(verified_positions)}个验证过的持仓")

        except Exception as e:
            print(f"❌ 更新本地持仓失败: {e}")
            import traceback
            traceback.print_exc()

    def close_position(self, order_id: str) -> bool:
        """平仓指定订单"""
        try:
            print(f"🔄 尝试平仓订单: {order_id}")

            if not self.mt4_client or not self.mt4_client.connect():
                print("❌ MT4未连接，无法平仓")
                return False

            # 调用MT4平仓
            response = self.mt4_client.close_position(order_id)

            if response and response.get('status') == 'success':
                print(f"✅ 订单{order_id}平仓成功")

                # 从本地记录中移除
                if order_id in self.active_orders:
                    del self.active_orders[order_id]

                return True
            else:
                error_msg = response.get('message', '未知错误') if response else '无响应'
                print(f"❌ 订单{order_id}平仓失败: {error_msg}")
                return False

        except Exception as e:
            print(f"❌ 平仓订单{order_id}异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def execute_trading_decisions(self, trading_decisions: List[Dict]) -> List[TradeOrder]:
        """
        执行交易决策（带持仓管理）

        Args:
            trading_decisions: 交易决策列表

        Returns:
            List[TradeOrder]: 执行的订单列表
        """
        executed_orders = []

        try:
            if not trading_decisions:
                print("📊 无交易决策需要执行")
                return executed_orders

            print(f"🎯 开始执行{len(trading_decisions)}个交易决策...")
            print("🔍 首先检查现有持仓...")

            # 显示当前持仓状态
            self._print_current_positions()

            for i, decision in enumerate(trading_decisions, 1):
                try:
                    symbol = decision.get('symbol', 'UNKNOWN')
                    action = decision.get('action', 'UNKNOWN')
                    volume = decision.get('volume', 0.02)

                    print(f"\n📋 执行决策 {i}/{len(trading_decisions)}: {symbol} {action} {volume}手")

                    # 检查是否应该执行交易
                    trade_check = self._should_execute_trade(symbol, action, volume)

                    print(f"🔍 持仓检查结果: {trade_check['reason']}")

                    if not trade_check['should_execute']:
                        print(f"⏸️ 跳过交易: {trade_check['reason']}")
                        continue

                    # 处理相反方向持仓
                    if trade_check['action'] == 'close_opposite_first':
                        print("🔄 检测到相反方向持仓，先执行平仓...")
                        for opposite_pos in trade_check['details']:
                            close_result = self.close_position(opposite_pos['order_id'])
                            if close_result:
                                print(f"✅ 平仓成功: {opposite_pos['order_id']}")
                            else:
                                print(f"❌ 平仓失败: {opposite_pos['order_id']}")

                    # 处理不同类型的交易决策
                    if action in ['CLOSE', 'CLOSE_PARTIAL']:
                        # 直接处理平仓操作
                        close_result = self._handle_close_decision(decision)
                        if close_result:
                            print(f"✅ {symbol} 平仓操作执行成功")
                            # 创建一个虚拟订单记录平仓操作
                            virtual_order = TradeOrder(
                                symbol=symbol,
                                order_type=OrderType.CLOSE,
                                volume=volume,
                                entry_price=0.0,
                                order_id=f"CLOSE_{symbol}_{datetime.now().strftime('%H%M%S')}",
                                status=OrderStatus.FILLED,
                                comment=f"平仓操作: {action}",
                                timestamp=datetime.now().isoformat()
                            )
                            executed_orders.append(virtual_order)
                        else:
                            print(f"❌ {symbol} 平仓操作失败")
                    else:
                        # 创建交易订单
                        order = self._create_trade_order(decision)

                        if order:
                            # 执行订单
                            executed_order = self._execute_order(order)

                            if executed_order:
                                executed_orders.append(executed_order)
                                print(f"✅ {symbol} 订单执行成功")
                            else:
                                print(f"❌ {symbol} 订单执行失败")
                        else:
                            print(f"❌ {symbol} 订单创建失败")

                except Exception as e:
                    print(f"❌ 决策执行失败: {e}")
                    continue

            print(f"\n📊 交易执行完成: {len(executed_orders)}/{len(trading_decisions)} 成功")

            # 显示执行后的持仓状态
            print("\n🔍 执行后持仓状态:")
            self._print_current_positions()

            return executed_orders

        except Exception as e:
            print(f"❌ 交易执行引擎错误: {e}")
            return executed_orders

    def _create_trade_order(self, decision: Dict) -> Optional[TradeOrder]:
        """创建交易订单"""
        try:
            symbol = decision.get('symbol', '')
            action = decision.get('action', 'NEUTRAL')

            # 跳过中性信号
            if action == 'NEUTRAL':
                return None

            # 处理平仓类型的决策
            if action in ['CLOSE', 'CLOSE_PARTIAL']:
                return self._handle_close_decision(decision)

            # 确定订单类型
            if action in ['BUY', 'STRONG_BUY', 'enter_long', 'ENTER_LONG']:
                order_type = OrderType.BUY
            elif action in ['SELL', 'STRONG_SELL', 'enter_short', 'ENTER_SHORT']:
                order_type = OrderType.SELL
            else:
                print(f"⚠️ 不支持的交易动作: {action}")
                return None

            # 计算交易量
            volume = self._calculate_position_size(decision)

            # 创建订单
            order = TradeOrder(
                symbol=symbol,
                order_type=order_type,
                volume=volume,
                entry_price=decision.get('entry_price', 0.0),
                stop_loss=decision.get('stop_loss', 0.0),
                take_profit=decision.get('take_profit', 0.0),
                comment=f"QuantumForex_{action}_{decision.get('confidence', 0):.0%}",
                timestamp=datetime.now().isoformat()
            )

            return order

        except Exception as e:
            print(f"❌ 创建订单失败: {e}")
            return None

    def _handle_close_decision(self, decision: Dict) -> bool:
        """处理平仓决策，返回是否成功"""
        try:
            symbol = decision.get('symbol', '')
            action = decision.get('action', '')
            volume = decision.get('volume', 0)

            print(f"🔄 处理平仓决策: {symbol} {action} {volume}手")

            # 获取该货币对的现有持仓
            existing_positions = self._get_mt4_real_positions()
            symbol_positions = [pos for pos in existing_positions if pos.get('symbol') == symbol]

            if not symbol_positions:
                print(f"⚠️ 没有找到{symbol}的持仓，无法执行平仓")
                return False

            if action == 'CLOSE':
                # 全部平仓
                print(f"🔴 执行{symbol}全部平仓...")
                success_count = 0
                for pos in symbol_positions:
                    order_id = pos.get('order_id', pos.get('ticket', ''))
                    if order_id:
                        success = self.close_position(str(order_id))
                        if success:
                            print(f"✅ 平仓成功: {order_id}")
                            success_count += 1
                        else:
                            print(f"❌ 平仓失败: {order_id}")
                return success_count > 0  # 至少有一个平仓成功就返回True

            elif action == 'CLOSE_PARTIAL':
                # 部分平仓 - 找到最大亏损的持仓进行平仓
                print(f"🟡 执行{symbol}部分平仓 {volume}手...")

                # 按盈亏排序，优先平仓亏损最大的
                sorted_positions = sorted(symbol_positions, key=lambda x: x.get('profit', 0))

                remaining_volume = volume
                success_count = 0

                for pos in sorted_positions:
                    if remaining_volume <= 0:
                        break

                    pos_volume = pos.get('volume', pos.get('size', pos.get('lots', 0)))
                    order_id = pos.get('order_id', pos.get('ticket', ''))

                    if pos_volume <= remaining_volume:
                        # 完全平仓这个订单
                        if order_id:
                            success = self.close_position(str(order_id))
                            if success:
                                print(f"✅ 完全平仓: {order_id} ({pos_volume}手)")
                                remaining_volume -= pos_volume
                                success_count += 1
                            else:
                                print(f"❌ 平仓失败: {order_id}")
                    else:
                        # 这里应该实现部分平仓，但MT4通常不支持部分平仓单个订单
                        # 所以我们跳过这个订单，寻找下一个
                        print(f"⚠️ 订单{order_id}体积{pos_volume}手大于需要平仓的{remaining_volume}手，跳过")
                        continue

                if remaining_volume > 0:
                    print(f"⚠️ 还有{remaining_volume}手未能平仓")

                return success_count > 0  # 至少有一个平仓成功就返回True

            return False

        except Exception as e:
            print(f"❌ 处理平仓决策失败: {e}")
            return False

    def _calculate_position_size(self, decision: Dict) -> float:
        """计算仓位大小（集成风险管理建议）"""
        try:
            # 优先使用策略建议的仓位大小
            strategy_volume = decision.get('volume', 0)

            if strategy_volume > 0:
                # 策略已经计算了仓位大小，但需要根据风险管理建议调整
                print(f"📊 策略建议仓位: {strategy_volume}手")

                # 检查是否有风险管理建议
                risk_adjustment = self._get_risk_adjustment_factor(decision)

                if risk_adjustment < 1.0:
                    adjusted_volume = strategy_volume * risk_adjustment
                    print(f"⚠️ 风险管理调整: {strategy_volume}手 → {adjusted_volume:.3f}手 (调整系数: {risk_adjustment:.2f})")
                    volume = adjusted_volume
                else:
                    print(f"✅ 无风险调整需要，使用原始仓位: {strategy_volume}手")
                    volume = strategy_volume

                # 确保仓位在合理范围内，符合MT4实际限制
                if volume <= 0:
                    return 0.0  # 风险管理要求停止交易
                elif volume < 0.01:
                    # 如果调整后的仓位小于最小值，使用最小仓位0.01手
                    print(f"⚠️ 调整后仓位{volume:.3f}手小于最小值，使用最小仓位0.01手")
                    return 0.01
                else:
                    volume = max(0.01, min(volume, 0.1))  # 0.01-0.1手
                    return round(volume, 3)

            # 如果策略没有提供仓位大小，则使用备用计算逻辑
            print("⚠️ 策略未提供仓位大小，使用备用计算逻辑")

            # 基础仓位大小
            base_volume = 0.01  # 0.01手

            # 根据信号强度调整
            strength = decision.get('strength', 0.5)
            confidence = decision.get('confidence', 0.5)

            # 综合评分
            signal_quality = (strength + confidence) / 2

            # 仓位调整
            if signal_quality > 0.8:
                volume = base_volume * 2.0  # 强信号加倍
            elif signal_quality > 0.6:
                volume = base_volume * 1.5  # 中等信号增加50%
            else:
                volume = base_volume  # 基础仓位

            # 确保仓位在合理范围内
            volume = max(0.01, min(volume, 0.1))  # 0.01-0.1手

            return round(volume, 3)

        except Exception as e:
            print(f"❌ 计算仓位大小失败: {e}")
            return 0.01  # 默认最小仓位

    def _get_risk_adjustment_factor(self, decision: Dict) -> float:
        """获取风险调整因子"""
        try:
            # 默认无调整
            adjustment_factor = 1.0

            # 检查全局风险状态（从系统状态获取）
            if hasattr(self, '_current_risk_assessment'):
                risk_assessment = self._current_risk_assessment

                if risk_assessment:
                    risk_metrics = risk_assessment.get('risk_metrics')
                    if risk_metrics:
                        recommended_action = getattr(risk_metrics, 'recommended_action', None)

                        # 根据风险管理建议调整仓位
                        if recommended_action:
                            action_str = str(recommended_action).lower()

                            if 'reduce_position' in action_str:
                                # 对于0.02手基准，减少50%到0.01手是合理的
                                # 对于更大的仓位，也按50%减少
                                adjustment_factor = 0.5  # 减少50%仓位
                                print(f"🔧 风险管理: 检测到reduce_position建议，仓位减少50%")
                            elif 'stop_new_trades' in action_str:
                                adjustment_factor = 0.0  # 停止交易
                                print(f"🛑 风险管理: 检测到stop_new_trades建议，停止交易")
                            elif 'emergency' in action_str:
                                adjustment_factor = 0.0  # 紧急停止
                                print(f"🚨 风险管理: 检测到emergency建议，紧急停止交易")

            # 检查决策本身的置信度
            confidence = decision.get('confidence', 0.5)
            if confidence < 0.6:
                confidence_adjustment = confidence / 0.6  # 置信度低于60%时按比例减少
                adjustment_factor *= confidence_adjustment
                print(f"📊 置信度调整: {confidence:.1%} → 仓位调整系数 {confidence_adjustment:.2f}")

            # 检查市场波动率（如果有的话）
            volatility = decision.get('volatility', 0)
            if volatility > 0.02:  # 高波动率
                volatility_adjustment = 0.7  # 减少30%
                adjustment_factor *= volatility_adjustment
                print(f"📈 波动率调整: {volatility:.3f} → 仓位调整系数 {volatility_adjustment:.2f}")

            return max(0.0, min(adjustment_factor, 1.0))  # 确保在0-1之间

        except Exception as e:
            print(f"❌ 计算风险调整因子失败: {e}")
            return 1.0  # 出错时不调整

    def set_risk_assessment(self, risk_assessment: Dict):
        """设置当前风险评估（供外部调用）"""
        self._current_risk_assessment = risk_assessment

    def _get_market_info(self, symbol: str) -> Optional[Dict]:
        """获取市场信息，包括点差"""
        try:
            if not self.mt4_client or not self.mt4_client.connect():
                return None

            response = self.mt4_client.get_market_info(symbol)
            if response and response.get('status') == 'success':
                return response.get('data', {})
            return None
        except Exception as e:
            print(f"❌ 获取{symbol}市场信息失败: {e}")
            return None

    def _calculate_spread_adjusted_prices(self, order: TradeOrder) -> TradeOrder:
        """计算考虑点差的价格"""
        try:
            # 获取市场信息
            market_info = self._get_market_info(order.symbol)
            if not market_info:
                print(f"⚠️ 无法获取{order.symbol}市场信息，使用原始价格")
                return order

            bid = float(market_info.get('bid', 0))
            ask = float(market_info.get('ask', 0))
            spread = float(market_info.get('spread', 0))

            print(f"📊 {order.symbol}市场信息:")
            print(f"   Bid: {bid}")
            print(f"   Ask: {ask}")
            print(f"   Spread: {spread} 点")

            # 根据订单类型调整价格
            if order.order_type == OrderType.BUY:
                # 买入使用Ask价格
                actual_entry = ask
                print(f"   买入订单使用Ask价格: {actual_entry}")

                # 调整止损止盈（考虑点差）
                if order.stop_loss > 0:
                    # 买入止损应该低于入场价
                    spread_adjustment = spread * 0.00001  # 转换为价格单位
                    order.stop_loss = max(order.stop_loss - spread_adjustment, bid - 0.001)

                if order.take_profit > 0:
                    # 买入止盈应该高于入场价
                    order.take_profit = order.take_profit  # 保持原有止盈

            elif order.order_type == OrderType.SELL:
                # 卖出使用Bid价格
                actual_entry = bid
                print(f"   卖出订单使用Bid价格: {actual_entry}")

                # 调整止损止盈（考虑点差）
                if order.stop_loss > 0:
                    # 卖出止损应该高于入场价
                    spread_adjustment = spread * 0.00001  # 转换为价格单位
                    order.stop_loss = min(order.stop_loss + spread_adjustment, ask + 0.001)

                if order.take_profit > 0:
                    # 卖出止盈应该低于入场价
                    order.take_profit = order.take_profit  # 保持原有止盈

            # 更新入场价格为实际市场价格
            order.entry_price = actual_entry

            print(f"📈 点差调整后价格:")
            print(f"   入场价格: {order.entry_price}")
            print(f"   止损价格: {order.stop_loss}")
            print(f"   止盈价格: {order.take_profit}")

            return order

        except Exception as e:
            print(f"❌ 计算点差调整价格失败: {e}")
            return order

    def _execute_order(self, order: TradeOrder) -> Optional[TradeOrder]:
        """执行真实MT4订单（考虑点差）"""
        try:
            if not self.mt4_client:
                print("❌ MT4客户端未初始化")
                return None

            # 确保MT4连接
            if not self.mt4_client.connect():
                print("❌ MT4连接失败，无法执行真实交易")
                return None

            # 计算考虑点差的价格
            order = self._calculate_spread_adjusted_prices(order)

            print(f"📤 执行真实MT4交易: {order.symbol} {order.order_type.value} {order.volume}手")
            print(f"   入场价格: {order.entry_price}")
            print(f"   止损: {order.stop_loss}")
            print(f"   止盈: {order.take_profit}")

            # 使用MT4客户端的真实交易方法
            response = None

            if order.order_type == OrderType.BUY:
                # 执行市价买入
                response = self.mt4_client.buy(
                    symbol=order.symbol,
                    lot=order.volume,
                    sl=order.stop_loss,
                    tp=order.take_profit,
                    comment=order.comment
                )
            elif order.order_type == OrderType.SELL:
                # 执行市价卖出
                response = self.mt4_client.sell(
                    symbol=order.symbol,
                    lot=order.volume,
                    sl=order.stop_loss,
                    tp=order.take_profit,
                    comment=order.comment
                )
            else:
                print(f"❌ 不支持的订单类型: {order.order_type}")
                return None

            print(f"📋 MT4交易响应: {response}")

            if response and response.get('status') == 'success':
                # 订单执行成功
                order.status = OrderStatus.FILLED
                order.order_id = str(response.get('order_id', response.get('ticket', 'unknown')))
                order.fill_price = float(response.get('price', order.entry_price))
                order.fill_time = datetime.now().isoformat()

                # 记录订单
                self.active_orders[order.order_id] = order
                self.order_history.append(order)

                print(f"✅ 真实MT4订单执行成功!")
                print(f"   订单号: {order.order_id}")
                print(f"   成交价格: {order.fill_price}")
                print(f"   成交时间: {order.fill_time}")

                return order
            else:
                # 订单执行失败
                order.status = OrderStatus.REJECTED
                error_msg = response.get('message', response.get('error', '未知错误')) if response else '无响应'
                print(f"❌ MT4订单执行失败: {error_msg}")

                # 记录失败的订单
                self.order_history.append(order)
                return None

        except Exception as e:
            print(f"❌ MT4订单执行异常: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _get_real_mt4_positions(self) -> List[TradeOrder]:
        """从MT4获取真实持仓"""
        try:
            if not self.mt4_client or not self.mt4_client.connect():
                print("⚠️ MT4未连接，无法获取真实持仓")
                return []

            # 使用MT4客户端获取活跃订单
            response = self.mt4_client.get_active_orders()

            if response and response.get('status') == 'success':
                orders = response.get('orders', [])
                print(f"📊 从MT4获取到{len(orders)}个活跃订单")
                return self._convert_mt4_positions(orders)
            else:
                print("⚠️ 获取MT4持仓失败")
                return []

        except Exception as e:
            print(f"❌ 获取MT4持仓异常: {e}")
            return []

    def get_active_positions(self) -> List[TradeOrder]:
        """获取活跃持仓 - 优先从MT4获取真实持仓"""
        try:
            # 首先尝试从MT4获取真实持仓
            real_positions = self._get_real_mt4_positions()

            if real_positions:
                print(f"📊 获取到{len(real_positions)}个MT4真实持仓")
                return real_positions
            else:
                # 如果没有MT4持仓，返回本地记录的订单
                local_positions = list(self.active_orders.values())
                print(f"📊 返回{len(local_positions)}个本地记录的持仓")
                return local_positions

        except Exception as e:
            print(f"❌ 获取持仓失败: {e}")
            return list(self.active_orders.values())

    def _convert_mt4_positions(self, mt4_positions: List[Dict]) -> List[TradeOrder]:
        """转换MT4持仓格式"""
        converted_positions = []

        try:
            for pos in mt4_positions:
                order = TradeOrder(
                    symbol=pos.get('symbol', ''),
                    order_type=OrderType.BUY if pos.get('type') == 0 else OrderType.SELL,
                    volume=float(pos.get('lots', 0)),
                    entry_price=float(pos.get('open_price', 0)),
                    stop_loss=float(pos.get('sl', 0)),
                    take_profit=float(pos.get('tp', 0)),
                    comment=pos.get('comment', ''),
                    timestamp=pos.get('open_time', ''),
                    order_id=str(pos.get('ticket', '')),
                    status=OrderStatus.FILLED,
                    fill_price=float(pos.get('open_price', 0)),
                    fill_time=pos.get('open_time', '')
                )
                converted_positions.append(order)

        except Exception as e:
            print(f"❌ 转换持仓格式失败: {e}")

        return converted_positions

    def close_position(self, order_id: str) -> bool:
        """执行真实MT4平仓"""
        try:
            if not self.mt4_client:
                print("❌ MT4客户端未初始化")
                return False

            if not self.mt4_client.connect():
                print("❌ MT4连接失败，无法执行平仓")
                return False

            print(f"📤 执行真实MT4平仓: 订单号 {order_id}")

            # 使用MT4客户端的真实平仓方法
            response = self.mt4_client.close_order(order_id)

            print(f"📋 MT4平仓响应: {response}")

            if response and response.get('status') == 'success':
                # 从本地活跃订单中移除（如果存在）
                if order_id in self.active_orders:
                    del self.active_orders[order_id]

                print(f"✅ 真实MT4平仓成功: 订单号 {order_id}")
                return True
            else:
                error_msg = response.get('message', response.get('error', '未知错误')) if response else '无响应'
                print(f"❌ MT4平仓失败: {error_msg}")
                return False

        except Exception as e:
            print(f"❌ MT4平仓异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _calculate_order_profit(self, order: TradeOrder) -> float:
        """计算订单盈亏（考虑点差）"""
        try:
            if order.status != OrderStatus.FILLED:
                return 0.0

            # 获取当前市场价格
            market_info = self._get_market_info(order.symbol)
            if not market_info:
                return 0.0

            current_bid = float(market_info.get('bid', 0))
            current_ask = float(market_info.get('ask', 0))
            spread = float(market_info.get('spread', 0))

            # 计算盈亏（考虑点差成本）
            if order.order_type == OrderType.BUY:
                # 买入订单：当前Bid价格 - 入场价格 - 点差成本
                profit_pips = (current_bid - order.fill_price) * 10000  # 转换为点数
                spread_cost = spread  # 点差成本
                net_profit_pips = profit_pips - spread_cost
                profit_usd = net_profit_pips * order.volume * 1.0  # 简化计算
            elif order.order_type == OrderType.SELL:
                # 卖出订单：入场价格 - 当前Ask价格 - 点差成本
                profit_pips = (order.fill_price - current_ask) * 10000  # 转换为点数
                spread_cost = spread  # 点差成本
                net_profit_pips = profit_pips - spread_cost
                profit_usd = net_profit_pips * order.volume * 1.0  # 简化计算
            else:
                return 0.0

            return profit_usd

        except Exception as e:
            print(f"❌ 计算订单{order.order_id}盈亏失败: {e}")
            return 0.0

    def get_trading_statistics(self) -> Dict:
        """获取交易统计（包含盈亏分析）"""
        try:
            total_orders = len(self.order_history)
            active_positions = len(self.active_orders)
            successful_orders = len([o for o in self.order_history if o.status == OrderStatus.FILLED])
            failed_orders = len([o for o in self.order_history if o.status == OrderStatus.REJECTED])

            if total_orders == 0:
                return {
                    'total_orders': 0,
                    'successful_orders': 0,
                    'failed_orders': 0,
                    'active_positions': 0,
                    'success_rate': 0.0,
                    'win_rate': 0.0,
                    'total_volume': 0.0,
                    'total_profit_usd': 0.0,
                    'active_profit_usd': 0.0,
                    'profitable_trades': 0,
                    'losing_trades': 0,
                    'average_profit_per_trade': 0.0
                }

            # 计算总交易量
            total_volume = sum(order.volume for order in self.order_history)

            # 计算盈亏
            total_profit = 0.0
            profitable_trades = 0
            losing_trades = 0

            for order in self.order_history:
                if order.status == OrderStatus.FILLED:
                    profit = self._calculate_order_profit(order)
                    total_profit += profit

                    if profit > 0:
                        profitable_trades += 1
                    elif profit < 0:
                        losing_trades += 1

            # 计算活跃订单盈亏
            active_profit = 0.0
            for order in self.active_orders.values():
                active_profit += self._calculate_order_profit(order)

            # 计算胜率
            win_rate = profitable_trades / successful_orders if successful_orders > 0 else 0.0
            success_rate = successful_orders / total_orders if total_orders > 0 else 0.0

            return {
                'total_orders': total_orders,
                'successful_orders': successful_orders,
                'failed_orders': failed_orders,
                'active_positions': active_positions,
                'success_rate': success_rate,
                'win_rate': win_rate,
                'total_volume': total_volume,
                'total_profit_usd': round(total_profit, 2),
                'active_profit_usd': round(active_profit, 2),
                'profitable_trades': profitable_trades,
                'losing_trades': losing_trades,
                'average_profit_per_trade': round(total_profit / successful_orders, 2) if successful_orders > 0 else 0.0
            }

        except Exception as e:
            print(f"❌ 统计计算失败: {e}")
            return {
                'total_orders': 0,
                'successful_orders': 0,
                'failed_orders': 0,
                'active_positions': 0,
                'success_rate': 0.0,
                'win_rate': 0.0,
                'total_volume': 0.0,
                'total_profit_usd': 0.0,
                'active_profit_usd': 0.0,
                'profitable_trades': 0,
                'losing_trades': 0,
                'average_profit_per_trade': 0.0
            }

    def print_trading_summary(self):
        """打印详细的交易摘要（包含盈亏分析）"""
        stats = self.get_trading_statistics()

        print("\n" + "="*60)
        print("📊 交易统计摘要（考虑点差成本）")
        print("="*60)
        print(f"📈 总订单数: {stats['total_orders']}")
        print(f"✅ 成功订单: {stats['successful_orders']}")
        print(f"❌ 失败订单: {stats['failed_orders']}")
        print(f"📊 执行成功率: {stats['success_rate']:.1%}")
        print(f"🔄 活跃持仓: {stats['active_positions']}")
        print(f"📊 总交易量: {stats['total_volume']} 手")
        print("─" * 60)
        print(f"💰 总盈亏: ${stats['total_profit_usd']}")
        print(f"📈 活跃盈亏: ${stats['active_profit_usd']}")
        print(f"🎯 盈利交易: {stats['profitable_trades']}")
        print(f"📉 亏损交易: {stats['losing_trades']}")
        print(f"🏆 交易胜率: {stats['win_rate']:.1%}")
        print(f"💵 平均每笔盈亏: ${stats['average_profit_per_trade']}")

        # 盈亏分析
        if stats['total_profit_usd'] > 0:
            print("🎉 总体盈利！策略有效")
        elif stats['total_profit_usd'] < 0:
            print("⚠️ 总体亏损，需要优化策略")
            print("💡 建议：检查点差成本、止损止盈设置")
        else:
            print("➖ 盈亏平衡")

        # 风险提示
        if stats['win_rate'] < 0.5:
            print("⚠️ 胜率偏低，建议优化信号质量")

        print("="*60 + "\n")

    def execute_trade(self, symbol: str, action: str, volume: float, strategy_source: str = 'default',
                     comment: str = None, combo_mode: bool = False) -> Dict:
        """
        执行单个交易（通过全局协调器）

        Args:
            symbol: 货币对
            action: 交易动作 (BUY/SELL)
            volume: 交易量
            strategy_source: 策略来源

        Returns:
            Dict: 执行结果
        """
        try:
            print(f"🎯 收到交易请求: {symbol} {action} {volume}手 (来源: {strategy_source})")

            # 组合模式：跳过全局协调器，直接执行
            if combo_mode:
                print("🔄 组合模式：直接执行交易...")
                return self._execute_trade_directly(symbol, action, volume, strategy_source, comment, combo_mode)

            # 如果有全局协调器，通过协调器执行
            elif self.global_coordinator:
                print("🔒 通过全局协调器执行交易...")
                return self.global_coordinator.request_trade_execution(symbol, action, volume, strategy_source)
            else:
                print("⚠️ 全局协调器未初始化，直接执行交易...")
                # 直接执行（保持向后兼容）
                return self._execute_trade_directly(symbol, action, volume, strategy_source, comment, combo_mode)

        except Exception as e:
            print(f"❌ 交易执行失败: {e}")
            return {
                'success': False,
                'reason': f'执行异常: {e}',
                'action': 'error'
            }

    def _execute_trade_directly(self, symbol: str, action: str, volume: float, strategy_source: str,
                               comment: str = None, combo_mode: bool = False) -> Dict:
        """
        直接执行交易（不通过协调器）
        """
        try:
            # 组合模式：跳过单独的风控检查
            if combo_mode:
                print("🔄 组合模式：跳过单独风控检查")
            else:
                # 检查是否应该执行交易
                trade_check = self._should_execute_trade(symbol, action, volume)

                if not trade_check['should_execute']:
                    return {
                        'success': False,
                        'reason': trade_check['reason'],
                        'action': 'rejected',
                        'details': trade_check
                    }

            # 创建交易决策
            decision = {
                'symbol': symbol,
                'action': action,
                'volume': volume,
                'strategy_source': strategy_source,
                'entry_price': 0.0,  # 市价单
                'stop_loss': 0.0,    # 由策略设置
                'take_profit': 0.0,  # 由策略设置
                'confidence': 0.8    # 默认置信度
            }

            # 创建订单
            order = self._create_trade_order(decision)
            if not order:
                return {
                    'success': False,
                    'reason': '订单创建失败',
                    'action': 'error'
                }

            # 执行订单
            executed_order = self._execute_order(order)
            if executed_order:
                return {
                    'success': True,
                    'reason': '交易执行成功',
                    'action': 'executed',
                    'order_id': executed_order.order_id,
                    'details': {
                        'symbol': executed_order.symbol,
                        'action': executed_order.order_type.value,
                        'volume': executed_order.volume,
                        'fill_price': executed_order.fill_price
                    }
                }
            else:
                return {
                    'success': False,
                    'reason': '订单执行失败',
                    'action': 'failed'
                }

        except Exception as e:
            print(f"❌ 直接执行交易失败: {e}")
            return {
                'success': False,
                'reason': f'直接执行异常: {e}',
                'action': 'error'
            }
