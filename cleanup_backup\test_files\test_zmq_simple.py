"""
简单的ZeroMQ测试
"""
import zmq
import time
import json
import threading
import sys
from datetime import datetime

# 确保输出不会被缓存
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 脚本开始执行')
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ZeroMQ版本: {zmq.__version__}')

# 服务器地址
SERVER_ADDRESS = "tcp://*:5555"
CLIENT_ADDRESS = "tcp://localhost:5555"

# 服务器线程
def server_thread():
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器线程开始')
    
    # 创建上下文和套接字
    context = zmq.Context()
    socket = context.socket(zmq.REP)
    
    # 绑定地址
    socket.bind(SERVER_ADDRESS)
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器绑定到: {SERVER_ADDRESS}')
    
    # 接收一个请求
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 等待请求...')
    message = socket.recv_string()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 收到请求: {message}')
    
    # 发送响应
    response = "Hello from server!"
    socket.send_string(response)
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 已发送响应: {response}')
    
    # 关闭套接字和上下文
    socket.close()
    context.term()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器线程结束')

# 客户端函数
def client():
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 客户端开始')
    
    # 创建上下文和套接字
    context = zmq.Context()
    socket = context.socket(zmq.REQ)
    
    # 连接到服务器
    socket.connect(CLIENT_ADDRESS)
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 客户端连接到: {CLIENT_ADDRESS}')
    
    # 发送请求
    request = "Hello from client!"
    socket.send_string(request)
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 已发送请求: {request}')
    
    # 接收响应
    response = socket.recv_string()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 收到响应: {response}')
    
    # 关闭套接字和上下文
    socket.close()
    context.term()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 客户端结束')

# 主函数
def main():
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 主函数开始')
    
    # 启动服务器线程
    server = threading.Thread(target=server_thread)
    server.daemon = True
    server.start()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 服务器线程已启动')
    
    # 等待服务器启动
    time.sleep(1)
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 等待服务器启动完成')
    
    # 运行客户端
    client()
    
    # 等待服务器线程结束
    server.join(timeout=5)
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 主函数结束')

if __name__ == "__main__":
    main()
    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 脚本结束执行')
