#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
学习协调器
协调整个学习系统的运行，包括数据记录、模式分析、参数优化
"""

import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
import logging

from .trade_result_recorder import TradeResultRecorder
from .pattern_analyzer import PatternAnalyzer
from .parameter_optimizer import ParameterOptimizer, OptimizationResult

@dataclass
class LearningTask:
    """学习任务"""
    task_id: str
    task_type: str  # record_trade/analyze_patterns/optimize_parameters
    priority: int   # 1-5, 1最高
    data: Dict
    created_at: datetime
    status: str = "pending"  # pending/running/completed/failed

class LearningCoordinator:
    """学习协调器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.trade_recorder = TradeResultRecorder()
        self.pattern_analyzer = PatternAnalyzer(self.trade_recorder)
        self.parameter_optimizer = ParameterOptimizer(self.trade_recorder, self.pattern_analyzer)
        
        # 任务队列
        self.task_queue = []
        self.task_lock = threading.Lock()
        
        # 运行状态
        self.running = False
        self.worker_thread = None
        
        # 回调函数
        self.parameter_update_callbacks = []
        
        # 学习统计
        self.learning_stats = {
            'total_trades_recorded': 0,
            'total_patterns_found': 0,
            'total_optimizations': 0,
            'last_learning_cycle': None,
            'learning_enabled': True
        }
        
    def start(self):
        """启动学习系统"""
        try:
            if self.running:
                self.logger.warning("学习系统已经在运行")
                return
            
            self.running = True
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
            
            self.logger.info("学习系统启动成功")
            
        except Exception as e:
            self.logger.error(f"启动学习系统失败: {e}")
            self.running = False
    
    def stop(self):
        """停止学习系统"""
        try:
            self.running = False
            if self.worker_thread and self.worker_thread.is_alive():
                self.worker_thread.join(timeout=5)
            
            self.logger.info("学习系统已停止")
            
        except Exception as e:
            self.logger.error(f"停止学习系统失败: {e}")
    
    def record_trade_entry(self, trade_data: Dict) -> str:
        """记录交易开仓"""
        try:
            # 立即记录（高优先级）
            trade_id = self.trade_recorder.record_trade_entry(trade_data)
            
            if trade_id:
                self.learning_stats['total_trades_recorded'] += 1
                self.logger.info(f"记录交易开仓: {trade_id}")
            
            return trade_id
            
        except Exception as e:
            self.logger.error(f"记录交易开仓失败: {e}")
            return ""
    
    def record_trade_exit(self, trade_id: str, exit_data: Dict):
        """记录交易平仓"""
        try:
            # 立即记录（高优先级）
            self.trade_recorder.record_trade_exit(trade_id, exit_data)
            
            # 添加学习任务
            self._add_task(LearningTask(
                task_id=f"learn_from_trade_{trade_id}",
                task_type="analyze_trade_result",
                priority=2,
                data={'trade_id': trade_id, 'exit_data': exit_data},
                created_at=datetime.now()
            ))
            
            self.logger.info(f"记录交易平仓: {trade_id}")
            
        except Exception as e:
            self.logger.error(f"记录交易平仓失败: {e}")
    
    def request_pattern_analysis(self, priority: int = 3) -> str:
        """请求模式分析"""
        try:
            task_id = f"pattern_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self._add_task(LearningTask(
                task_id=task_id,
                task_type="analyze_patterns",
                priority=priority,
                data={},
                created_at=datetime.now()
            ))
            
            return task_id
            
        except Exception as e:
            self.logger.error(f"请求模式分析失败: {e}")
            return ""
    
    def request_parameter_optimization(self, force: bool = False, priority: int = 4) -> str:
        """请求参数优化"""
        try:
            task_id = f"param_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self._add_task(LearningTask(
                task_id=task_id,
                task_type="optimize_parameters",
                priority=priority,
                data={'force': force},
                created_at=datetime.now()
            ))
            
            return task_id
            
        except Exception as e:
            self.logger.error(f"请求参数优化失败: {e}")
            return ""
    
    def get_pattern_recommendations(self, current_conditions: Dict) -> List[str]:
        """获取模式建议"""
        try:
            return self.pattern_analyzer.get_pattern_recommendations(current_conditions)
        except Exception as e:
            self.logger.error(f"获取模式建议失败: {e}")
            return []
    
    def get_current_parameters(self) -> Dict:
        """获取当前优化参数"""
        try:
            return self.parameter_optimizer.get_current_parameters()
        except Exception as e:
            self.logger.error(f"获取当前参数失败: {e}")
            return {}
    
    def register_parameter_update_callback(self, callback: Callable[[List[OptimizationResult]], None]):
        """注册参数更新回调"""
        self.parameter_update_callbacks.append(callback)
    
    def _add_task(self, task: LearningTask):
        """添加学习任务"""
        with self.task_lock:
            self.task_queue.append(task)
            # 按优先级排序
            self.task_queue.sort(key=lambda x: x.priority)
    
    def _get_next_task(self) -> Optional[LearningTask]:
        """获取下一个任务"""
        with self.task_lock:
            if self.task_queue:
                return self.task_queue.pop(0)
        return None
    
    def _worker_loop(self):
        """工作线程循环"""
        self.logger.info("学习系统工作线程启动")
        
        while self.running:
            try:
                # 获取任务
                task = self._get_next_task()
                
                if task:
                    self._execute_task(task)
                else:
                    # 没有任务时，执行定期学习循环
                    self._periodic_learning_cycle()
                    time.sleep(10)  # 等待10秒
                
            except Exception as e:
                self.logger.error(f"学习系统工作循环异常: {e}")
                time.sleep(5)
        
        self.logger.info("学习系统工作线程结束")
    
    def _execute_task(self, task: LearningTask):
        """执行学习任务"""
        try:
            task.status = "running"
            self.logger.info(f"执行学习任务: {task.task_id} ({task.task_type})")
            
            if task.task_type == "analyze_trade_result":
                self._analyze_trade_result(task.data)
            elif task.task_type == "analyze_patterns":
                self._analyze_patterns()
            elif task.task_type == "optimize_parameters":
                self._optimize_parameters(task.data.get('force', False))
            
            task.status = "completed"
            self.logger.info(f"学习任务完成: {task.task_id}")
            
        except Exception as e:
            task.status = "failed"
            self.logger.error(f"学习任务失败: {task.task_id} - {e}")
    
    def _analyze_trade_result(self, data: Dict):
        """分析单笔交易结果"""
        try:
            trade_id = data['trade_id']
            
            # 这里可以添加单笔交易的即时学习逻辑
            # 例如：如果是亏损交易，立即分析原因
            
            # 获取交易记录
            trade_record = self.trade_recorder._load_from_database(trade_id)
            if trade_record and trade_record.profit_loss < 0:
                # 亏损交易，分析原因
                self.logger.info(f"分析亏损交易: {trade_id}, 亏损: ${trade_record.profit_loss:.2f}")
                
                # 可以在这里添加即时的参数调整逻辑
                
        except Exception as e:
            self.logger.error(f"分析交易结果失败: {e}")
    
    def _analyze_patterns(self):
        """分析交易模式"""
        try:
            patterns = self.pattern_analyzer.analyze_patterns()
            
            winning_count = len(patterns['winning_patterns'])
            losing_count = len(patterns['losing_patterns'])
            
            self.learning_stats['total_patterns_found'] = winning_count + losing_count
            
            self.logger.info(f"模式分析完成: {winning_count} 个盈利模式, {losing_count} 个亏损模式")
            
        except Exception as e:
            self.logger.error(f"模式分析失败: {e}")
    
    def _optimize_parameters(self, force: bool = False):
        """优化参数"""
        try:
            optimization_results = self.parameter_optimizer.optimize_parameters(force)
            
            if optimization_results:
                self.learning_stats['total_optimizations'] += 1
                
                # 通知回调函数
                for callback in self.parameter_update_callbacks:
                    try:
                        callback(optimization_results)
                    except Exception as e:
                        self.logger.error(f"参数更新回调失败: {e}")
                
                self.logger.info(f"参数优化完成: 优化了 {len(optimization_results)} 个参数")
                
                # 记录优化结果
                for result in optimization_results:
                    self.logger.info(f"参数优化: {result.parameter_name} {result.old_value} → {result.new_value} (改进: {result.improvement_score:.3f})")
            
        except Exception as e:
            self.logger.error(f"参数优化失败: {e}")
    
    def _periodic_learning_cycle(self):
        """定期学习循环"""
        try:
            now = datetime.now()
            last_cycle = self.learning_stats.get('last_learning_cycle')
            
            # 每小时执行一次完整的学习循环
            if not last_cycle or (now - datetime.fromisoformat(last_cycle)).total_seconds() > 3600:
                
                self.logger.info("执行定期学习循环")
                
                # 1. 模式分析
                self._analyze_patterns()
                
                # 2. 参数优化（如果需要）
                self._optimize_parameters(force=False)
                
                self.learning_stats['last_learning_cycle'] = now.isoformat()
                
        except Exception as e:
            self.logger.error(f"定期学习循环失败: {e}")
    
    def get_learning_statistics(self) -> Dict:
        """获取学习统计"""
        try:
            # 获取交易统计
            trade_stats = self.trade_recorder.get_statistics()
            
            # 获取优化摘要
            optimization_summary = self.parameter_optimizer.get_optimization_summary()
            
            return {
                'learning_stats': self.learning_stats,
                'trade_stats': trade_stats,
                'optimization_summary': optimization_summary,
                'task_queue_size': len(self.task_queue),
                'system_running': self.running
            }
            
        except Exception as e:
            self.logger.error(f"获取学习统计失败: {e}")
            return {'error': str(e)}
    
    def enable_learning(self, enabled: bool = True):
        """启用/禁用学习"""
        self.learning_stats['learning_enabled'] = enabled
        self.logger.info(f"学习系统 {'启用' if enabled else '禁用'}")
    
    def is_learning_enabled(self) -> bool:
        """检查学习是否启用"""
        return self.learning_stats.get('learning_enabled', True)
