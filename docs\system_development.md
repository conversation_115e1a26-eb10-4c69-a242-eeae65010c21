# 外汇交易系统开发文档

## 最新更新

### 2025-05-24 市场时间检测与周末暂停机制优化
- **新增功能**：创建了专门的市场时间检测模块 `market_time_checker.py`
- **智能暂停**：系统现在能够智能检测外汇市场开放时间，在周末和非交易时间自动暂停分析
- **优雅等待**：替代了简单的程序终止，系统会在市场关闭时进入等待状态，并显示下次开放时间
- **回退机制修复**：修复了回退机制中的锁释放错误，避免"release unlocked lock"异常
- **服务器友好**：适合部署在服务器上长期运行，不会因为周末而意外终止

### 2025-05-24 Token统计功能优化
- **问题修复**：修复了token统计功能导致的系统死锁问题
- **功能调整**：暂时禁用token统计文件记录功能，保留控制台输出
- **性能优化**：避免了线程锁导致的程序无法正常结束的问题
- **用户体验**：系统现在可以正常运行，不会出现死锁情况

## 系统架构

外汇交易系统由以下组件组成：

1. **MT4Client.mq4（客户端EA）**：
   - 由多个客户在自己的电脑上运行
   - 连接到远程Python服务器："tcp://124.222.55.158:5555"
   - 发送注册、登录和获取信号的请求
   - 接收信号并执行交易

2. **ForexTradingSystem_Python（Python中间层）**：
   - 部署在服务器上（124.222.55.158）
   - 监听5555端口，接收来自客户端的请求
   - 处理用户注册和登录请求
   - 生成交易信号
   - 与本地MT4Server_v2通信执行交易

3. **MT4Server_v2.mq4（交易执行服务器）**：
   - 也部署在同一服务器上（124.222.55.158）
   - 运行在模拟盘上
   - 监听本地端口（127.0.0.1:5555）
   - 接收来自Python系统的交易请求
   - 执行实际的交易操作

## 通信流程

1. **客户端与Python服务器通信**：
   - MT4Client.mq4通过ZeroMQ连接到Python服务器
   - 发送注册、登录和获取信号的请求
   - 接收响应并执行交易

2. **Python服务器与MT4Server_v2通信**：
   - Python系统通过ZeroMQ连接到MT4Server_v2
   - 发送交易请求
   - 接收交易执行结果

## 组件详解

### MT4Client.mq4

MT4Client.mq4是一个MT4 EA客户端，用于连接到Python服务器，获取交易信号并执行交易。

主要功能：
- 用户注册和登录
- 获取交易信号
- 执行交易

### ForexTradingSystem_Python

ForexTradingSystem_Python是一个Python实现的交易系统，包含以下模块：

1. **Flask Web服务器**：
   - 提供HTTP API，用于Web界面交互
   - 监听5000端口

2. **ZeroMQ服务器**：
   - 处理来自MT4Client.mq4的请求
   - 监听5555端口
   - 实现用户注册、登录和信号分发功能

3. **MT4客户端工具**：
   - 连接到MT4Server_v2
   - 发送交易请求
   - 接收交易执行结果

4. **分析模块**：
   - 获取市场数据
   - 执行技术分析
   - 生成交易信号
   - 统计token使用情况

### MT4Server_v2.mq4

MT4Server_v2.mq4是一个MT4 EA服务器，用于执行交易操作。

主要功能：
- 接收交易请求
- 执行交易操作
- 返回交易执行结果

## MT4客户端服务器

MT4客户端服务器是一个ZeroMQ服务器，用于处理来自MT4Client.mq4的请求。

### 功能

1. **用户管理**：
   - 用户注册
   - 用户登录（凭证登录和授权码登录）
   - 授权验证

2. **信号分发**：
   - 获取最新的分析结果
   - 提取交易信号
   - 发送给客户端

### 请求类型

1. **REGISTER**：注册新用户
   ```json
   {
     "action": "REGISTER",
     "username": "用户名",
     "password": "密码"
   }
   ```

2. **LOGIN_WITH_CREDENTIALS**：使用凭证登录
   ```json
   {
     "action": "LOGIN_WITH_CREDENTIALS",
     "username": "用户名",
     "password": "密码"
   }
   ```

3. **LOGIN**：使用授权码登录
   ```json
   {
     "action": "LOGIN",
     "auth_code": "授权码"
   }
   ```

4. **GET_SIGNALS**：获取交易信号
   ```json
   {
     "action": "GET_SIGNALS",
     "auth_code": "授权码"
   }
   ```

### 响应格式

所有响应都包含以下字段：
- `status`：操作状态，`success`或`error`
- `message`：操作消息

成功响应示例：
```json
{
  "status": "success",
  "message": "操作成功",
  ...其他字段
}
```

错误响应示例：
```json
{
  "status": "error",
  "message": "操作失败原因"
}
```

## 配置

系统配置通过环境变量设置，主要配置项包括：

1. **服务器配置**：
   - `PORT`：HTTP服务器端口，默认5000

2. **数据库配置**：
   - `PIZZA_QUOTES_DB_HOST`：数据库主机
   - `PIZZA_QUOTES_DB_PORT`：数据库端口
   - `PIZZA_QUOTES_DB_USER`：数据库用户名
   - `PIZZA_QUOTES_DB_PASSWORD`：数据库密码
   - `PIZZA_QUOTES_DB_NAME`：数据库名称

3. **MT4配置**：
   - `MT4_SERVER_ADDRESS`：MT4服务器地址，默认`tcp://127.0.0.1:5555`

4. **MT4客户端服务器配置**：
   - `ENABLE_CLIENT_SERVER`：是否启用MT4客户端服务器，默认`true`
   - `CLIENT_SERVER_ADDRESS`：MT4客户端服务器地址，默认`tcp://*:5555`

5. **分析模式配置**：
   - `ANALYSIS_MODE`：分析模式，可选值：`hourly`、`realtime`、`hybrid`
   - `REALTIME_CHECK_INTERVAL`：实时分析检查间隔（秒）

## 启动系统

1. **启动Python系统**：
   ```bash
   python run.py
   ```

2. **启动MT4Server_v2**：
   在MT4中加载MT4Server_v2.mq4 EA

3. **启动MT4Client**：
   在客户端MT4中加载MT4Client.mq4 EA

## 市场时间检测功能

系统集成了智能的市场时间检测功能，用于判断外汇市场是否开放，并在市场关闭时暂停系统运行。

### 功能特点

1. **准确的市场时间判断**：
   - 基于UTC时间准确判断外汇市场开放状态
   - 支持周一00:00 - 周五22:00的标准外汇交易时间
   - 自动处理周末休市时间

2. **智能暂停机制**：
   - 在市场关闭时自动暂停分析任务
   - 显示市场状态和下次开放时间
   - 避免在非交易时间进行无意义的分析

3. **服务器友好设计**：
   - 适合长期部署在服务器上运行
   - 不会因为周末而意外终止程序
   - 优雅地等待市场重新开放

### 使用方式

市场时间检测功能在系统运行过程中自动工作，无需手动配置。系统会：

1. 在每次分析前检查市场是否开放
2. 如果市场关闭，显示状态信息并进入等待
3. 自动计算到下次开放的时间
4. 在市场重新开放时恢复正常运行

### API接口

`app/utils/market_time_checker.py`提供以下主要函数：

- `is_market_open()`：检查市场是否开放
- `get_market_status()`：获取市场状态描述
- `get_next_market_open_time()`：获取下次开放时间
- `should_pause_system()`：判断系统是否应该暂停
- `format_time_until_open()`：格式化显示距离开放的时间

## Token统计功能

系统集成了Token统计功能，用于记录和分析LLM API调用的token消耗情况。

### 功能特点

1. **记录每次API调用的token使用情况**：
   - 记录模型名称、提示词token数、生成内容token数、总token数、成本等信息
   - 区分预分析和完整分析的token使用情况

2. **统计维度**：
   - 按小时统计
   - 按日期统计
   - 按模型统计
   - 按分析类型统计（预分析/完整分析）

3. **报告生成**：
   - 生成文本格式的统计报告
   - 包含总体统计、模型统计、分析类型统计和最近24小时统计
   - 在预分析和完整分析完成后自动打印报告

### 使用方式

Token统计功能在系统运行过程中自动工作，无需手动触发。每次执行预分析或完整分析后，系统会自动生成并打印token统计报告。

### 配置项

Token统计功能的配置项包括：

- 模型价格配置：在`app/utils/token_statistics.py`中的`MODEL_PRICES`字典中配置
  ```python
  MODEL_PRICES = {
      'Pro/deepseek-ai/DeepSeek-R1': 16,  # R1模型价格：16元/百万token
      'Pro/deepseek-ai/DeepSeek-V3': 8     # V3模型价格：8元/百万token
  }
  ```

## 测试

可以使用以下脚本测试MT4客户端服务器：

```bash
python test_client_server.py
```

该脚本会测试以下功能：
- ping请求
- 用户注册
- 凭证登录
- 授权码登录
- 获取交易信号

可以使用以下脚本测试Token统计功能：

```bash
python test_token_stats.py
```

该脚本会测试以下功能：
- 记录token使用情况
- 加载token统计数据
- 生成token统计报告
