"""
QuantumForex MLTrainer 基础训练器
所有模型训练器的基类
"""

import pandas as pd
import numpy as np
import joblib
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from sklearn.model_selection import train_test_split, TimeSeriesSplit, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
import xgboost as xgb
import lightgbm as lgb

from config.training_config import training_config

class BaseModelTrainer:
    """基础模型训练器"""

    def __init__(self, model_type: str):
        self.logger = logging.getLogger(__name__)
        self.model_type = model_type

        # 配置
        self.model_config = training_config.MODEL_CONFIG
        self.training_config = training_config.TRAINING_FLOW

        # 模型存储
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.label_encoders = {}  # 存储标签编码器

        # 训练历史
        self.training_history = []

        # 特征重要性
        self.feature_importance = {}

        # 模型路径
        self.models_path = Path('data/models')
        self.models_path.mkdir(parents=True, exist_ok=True)

    def prepare_data(self, df: pd.DataFrame, target_column: str,
                    feature_columns: List[str] = None) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """准备训练数据"""
        try:
            self.logger.info(f"📊 准备训练数据: {len(df)}条记录")

            # 删除包含NaN的行
            df_clean = df.dropna()

            if len(df_clean) == 0:
                raise ValueError("清理后数据为空")

            # 选择特征列
            if feature_columns is None:
                feature_columns = [col for col in df_clean.columns if col != target_column]

            # 确保特征列存在
            available_features = [col for col in feature_columns if col in df_clean.columns]
            if not available_features:
                raise ValueError("没有可用的特征列")

            # 提取特征和目标
            X = df_clean[available_features].values
            y = df_clean[target_column].values

            self.logger.info(f"✅ 数据准备完成: {X.shape[0]}样本, {X.shape[1]}特征")
            return X, y, available_features

        except Exception as e:
            self.logger.error(f"❌ 数据准备失败: {e}")
            raise

    def split_data(self, X: np.ndarray, y: np.ndarray,
                  time_series: bool = True) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """分割训练和测试数据"""
        try:
            if time_series and self.model_config['data_split']['time_series_split']:
                # 时间序列分割
                split_point = int(len(X) * self.model_config['data_split']['train_ratio'])
                X_train, X_test = X[:split_point], X[split_point:]
                y_train, y_test = y[:split_point], y[split_point:]
            else:
                # 随机分割
                test_size = 1 - self.model_config['data_split']['train_ratio']
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=test_size, random_state=42
                )

            self.logger.info(f"📊 数据分割: 训练集{len(X_train)}, 测试集{len(X_test)}")
            return X_train, X_test, y_train, y_test

        except Exception as e:
            self.logger.error(f"❌ 数据分割失败: {e}")
            raise

    def scale_features(self, X_train: np.ndarray, X_test: np.ndarray,
                      scaler_name: str = 'default') -> Tuple[np.ndarray, np.ndarray]:
        """特征标准化"""
        try:
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)

            # 保存scaler
            self.scalers[scaler_name] = scaler

            self.logger.info(f"✅ 特征标准化完成: {scaler_name}")
            return X_train_scaled, X_test_scaled

        except Exception as e:
            self.logger.error(f"❌ 特征标准化失败: {e}")
            raise

    def create_models(self) -> Dict[str, Any]:
        """创建模型实例"""
        try:
            models = {}
            model_types = self.model_config['model_types']

            # Random Forest
            if model_types['random_forest']['enabled']:
                if self.model_type == 'classification':
                    models['random_forest'] = RandomForestClassifier(
                        n_estimators=100,
                        max_depth=10,
                        random_state=42,
                        n_jobs=-1
                    )
                else:
                    models['random_forest'] = RandomForestRegressor(
                        n_estimators=100,
                        max_depth=10,
                        random_state=42,
                        n_jobs=-1
                    )

            # XGBoost
            if model_types['xgboost']['enabled']:
                if self.model_type == 'classification':
                    models['xgboost'] = xgb.XGBClassifier(
                        n_estimators=100,
                        max_depth=6,
                        learning_rate=0.1,
                        random_state=42,
                        n_jobs=-1
                    )
                else:
                    models['xgboost'] = xgb.XGBRegressor(
                        n_estimators=100,
                        max_depth=6,
                        learning_rate=0.1,
                        random_state=42,
                        n_jobs=-1
                    )

            # LightGBM
            if model_types['lightgbm']['enabled']:
                if self.model_type == 'classification':
                    models['lightgbm'] = lgb.LGBMClassifier(
                        n_estimators=100,
                        max_depth=6,
                        learning_rate=0.1,
                        random_state=42,
                        n_jobs=-1,
                        verbose=-1
                    )
                else:
                    models['lightgbm'] = lgb.LGBMRegressor(
                        n_estimators=100,
                        max_depth=6,
                        learning_rate=0.1,
                        random_state=42,
                        n_jobs=-1,
                        verbose=-1
                    )

            self.logger.info(f"✅ 创建模型: {list(models.keys())}")
            return models

        except Exception as e:
            self.logger.error(f"❌ 创建模型失败: {e}")
            return {}

    def train_models(self, X_train: np.ndarray, y_train: np.ndarray,
                    X_test: np.ndarray, y_test: np.ndarray,
                    feature_names: List[str]) -> Dict[str, Any]:
        """训练所有模型"""
        try:
            self.logger.info("🧠 开始训练模型...")

            models = self.create_models()
            results = {}

            # 为XGBoost处理标签编码
            y_train_encoded, y_test_encoded, label_encoder = self._encode_labels_for_xgboost(y_train, y_test)

            for model_name, model in models.items():
                try:
                    self.logger.info(f"🔄 训练模型: {model_name}")

                    # 选择合适的标签
                    if model_name == 'xgboost' and self.model_type == 'classification':
                        # XGBoost使用编码后的标签
                        train_y = y_train_encoded
                        test_y = y_test_encoded
                    else:
                        # 其他模型使用原始标签
                        train_y = y_train
                        test_y = y_test

                    # 训练模型
                    model.fit(X_train, train_y)

                    # 预测
                    y_pred = model.predict(X_test)

                    # 如果是XGBoost，需要将预测结果解码回原始标签
                    if model_name == 'xgboost' and self.model_type == 'classification' and label_encoder is not None:
                        y_pred = label_encoder.inverse_transform(y_pred)
                        test_y = y_test  # 评估时使用原始标签

                    # 评估
                    metrics = self._evaluate_model(test_y, y_pred, model_name)

                    # 特征重要性
                    importance = self._get_feature_importance(model, feature_names)

                    # 保存结果
                    results[model_name] = {
                        'model': model,
                        'metrics': metrics,
                        'feature_importance': importance,
                        'predictions': y_pred
                    }

                    # 保存模型和标签编码器
                    self.models[model_name] = model
                    self.feature_importance[model_name] = importance
                    if model_name == 'xgboost' and label_encoder is not None:
                        self.label_encoders[model_name] = label_encoder

                    self.logger.info(f"✅ {model_name} 训练完成")

                except Exception as e:
                    self.logger.error(f"❌ {model_name} 训练失败: {e}")
                    continue

            self.logger.info(f"🎉 模型训练完成: {len(results)}个模型")
            return results

        except Exception as e:
            self.logger.error(f"❌ 模型训练失败: {e}")
            return {}

    def _evaluate_model(self, y_true: np.ndarray, y_pred: np.ndarray, model_name: str) -> Dict[str, float]:
        """评估模型性能"""
        try:
            metrics = {}

            if self.model_type == 'classification':
                metrics['accuracy'] = accuracy_score(y_true, y_pred)
                metrics['precision'] = precision_score(y_true, y_pred, average='weighted', zero_division=0)
                metrics['recall'] = recall_score(y_true, y_pred, average='weighted', zero_division=0)
                metrics['f1_score'] = f1_score(y_true, y_pred, average='weighted', zero_division=0)
            else:
                from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
                metrics['mse'] = mean_squared_error(y_true, y_pred)
                metrics['rmse'] = np.sqrt(metrics['mse'])
                metrics['mae'] = mean_absolute_error(y_true, y_pred)
                metrics['r2'] = r2_score(y_true, y_pred)

            return metrics

        except Exception as e:
            self.logger.error(f"❌ 模型评估失败: {e}")
            return {}

    def _get_feature_importance(self, model: Any, feature_names: List[str]) -> Dict[str, float]:
        """获取特征重要性"""
        try:
            importance_dict = {}

            if hasattr(model, 'feature_importances_'):
                importances = model.feature_importances_
                for name, importance in zip(feature_names, importances):
                    importance_dict[name] = float(importance)

            return importance_dict

        except Exception as e:
            self.logger.error(f"❌ 获取特征重要性失败: {e}")
            return {}

    def _encode_labels_for_xgboost(self, y_train: np.ndarray, y_test: np.ndarray) -> Tuple[np.ndarray, np.ndarray, Any]:
        """为XGBoost编码标签"""
        try:
            # 检查是否需要编码
            unique_labels = np.unique(np.concatenate([y_train, y_test]))

            # 如果标签不是从0开始的连续整数，则需要编码
            if not (np.array_equal(unique_labels, np.arange(len(unique_labels))) and unique_labels[0] == 0):
                from sklearn.preprocessing import LabelEncoder
                label_encoder = LabelEncoder()

                # 拟合所有标签
                all_labels = np.concatenate([y_train, y_test])
                label_encoder.fit(all_labels)

                # 编码
                y_train_encoded = label_encoder.transform(y_train)
                y_test_encoded = label_encoder.transform(y_test)

                return y_train_encoded, y_test_encoded, label_encoder
            else:
                # 不需要编码
                return y_train, y_test, None

        except Exception as e:
            self.logger.error(f"❌ 标签编码失败: {e}")
            return y_train, y_test, None

    def save_models(self, model_name_prefix: str = None) -> bool:
        """保存训练好的模型"""
        try:
            if model_name_prefix is None:
                model_name_prefix = self.model_type

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            for model_name, model in self.models.items():
                # 保存模型
                model_file = self.models_path / f"{model_name_prefix}_{model_name}_{timestamp}.pkl"
                joblib.dump(model, model_file)

                # 保存scaler
                if model_name in self.scalers:
                    scaler_file = self.models_path / f"{model_name_prefix}_{model_name}_scaler_{timestamp}.pkl"
                    joblib.dump(self.scalers[model_name], scaler_file)

                self.logger.info(f"✅ 模型已保存: {model_file}")

            # 保存特征重要性
            importance_file = self.models_path / f"{model_name_prefix}_feature_importance_{timestamp}.json"
            import json
            with open(importance_file, 'w') as f:
                json.dump(self.feature_importance, f, indent=2)

            return True

        except Exception as e:
            self.logger.error(f"❌ 保存模型失败: {e}")
            return False

    def load_model(self, model_file: str) -> Any:
        """加载模型"""
        try:
            model_path = self.models_path / model_file
            if model_path.exists():
                model = joblib.load(model_path)
                self.logger.info(f"✅ 模型已加载: {model_file}")
                return model
            else:
                self.logger.error(f"❌ 模型文件不存在: {model_file}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 加载模型失败: {e}")
            return None

    def get_best_model(self, results: Dict[str, Any], metric: str = 'accuracy') -> Tuple[str, Any]:
        """获取最佳模型"""
        try:
            best_score = -np.inf if metric in ['accuracy', 'precision', 'recall', 'f1_score', 'r2'] else np.inf
            best_model_name = None
            best_model = None

            for model_name, result in results.items():
                if metric in result['metrics']:
                    score = result['metrics'][metric]

                    if metric in ['accuracy', 'precision', 'recall', 'f1_score', 'r2']:
                        if score > best_score:
                            best_score = score
                            best_model_name = model_name
                            best_model = result['model']
                    else:  # 对于MSE, RMSE, MAE等，越小越好
                        if score < best_score:
                            best_score = score
                            best_model_name = model_name
                            best_model = result['model']

            if best_model_name:
                self.logger.info(f"🏆 最佳模型: {best_model_name} ({metric}: {best_score:.4f})")

            return best_model_name, best_model

        except Exception as e:
            self.logger.error(f"❌ 获取最佳模型失败: {e}")
            return None, None

    def create_target_variable(self, df: pd.DataFrame, method: str = 'price_direction') -> pd.Series:
        """创建目标变量（子类需要重写）"""
        raise NotImplementedError("子类必须实现create_target_variable方法")

    def get_training_summary(self) -> Dict[str, Any]:
        """获取训练摘要"""
        try:
            summary = {
                'model_type': self.model_type,
                'trained_models': list(self.models.keys()),
                'training_time': datetime.now().isoformat(),
                'feature_importance': self.feature_importance,
                'training_history': self.training_history
            }

            return summary

        except Exception as e:
            self.logger.error(f"❌ 获取训练摘要失败: {e}")
            return {}

# 创建基础训练器实例
base_trainer = BaseModelTrainer('base')
