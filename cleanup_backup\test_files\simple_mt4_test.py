"""
简单的MT4连接测试脚本
"""
import os
import sys
import time
import json
import zmq
from datetime import datetime

# 打印带时间戳的消息
def log(message):
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {message}")
    sys.stdout.flush()

# MT4服务器地址
SERVER_ADDRESS = "tcp://127.0.0.1:5555"

log("开始测试MT4连接")
log(f"服务器地址: {SERVER_ADDRESS}")

try:
    # 创建ZeroMQ上下文和套接字
    log("创建ZeroMQ上下文")
    context = zmq.Context()
    
    log("创建REQ套接字")
    socket = context.socket(zmq.REQ)
    
    # 设置超时时间
    log("设置超时时间: 10秒")
    socket.setsockopt(zmq.RCVTIMEO, 10000)  # 10秒超时
    
    # 连接到服务器
    log(f"连接到服务器: {SERVER_ADDRESS}")
    socket.connect(SERVER_ADDRESS)
    
    # 发送ping请求
    request = {
        'action': 'ping',
        'requestId': 'test-ping-' + datetime.now().strftime('%Y%m%d%H%M%S')
    }
    
    request_str = json.dumps(request)
    log(f"发送请求: {request_str}")
    socket.send_string(request_str)
    
    # 接收响应
    log("等待响应...")
    try:
        response_str = socket.recv_string()
        log(f"收到响应: {response_str}")
        
        # 解析响应
        response = json.loads(response_str)
        log(f"响应状态: {response.get('status', 'unknown')}")
        log(f"响应消息: {response.get('message', 'no message')}")
        
        if response.get('status') == 'success':
            log("测试成功: MT4服务器连接正常")
        else:
            log(f"测试失败: MT4服务器返回错误 - {response.get('message', 'unknown error')}")
    except zmq.error.Again:
        log("测试失败: MT4服务器响应超时")
    except Exception as e:
        log(f"接收响应时出错: {e}")
    
    # 关闭套接字和上下文
    log("关闭套接字")
    socket.close()
    
    log("终止上下文")
    context.term()
except Exception as e:
    log(f"测试过程中出错: {e}")

log("测试结束")
