#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实模型上传
"""

import sys
import os
import joblib
import numpy as np
from pathlib import Path
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_model():
    """创建测试模型"""
    print("🔧 创建测试模型...")
    
    # 创建简单的测试数据
    np.random.seed(42)
    X = np.random.randn(100, 5)
    y = np.random.choice([0, 1, 2], size=100)
    
    # 训练模型
    model = RandomForestClassifier(n_estimators=10, random_state=42)
    model.fit(X, y)
    
    # 创建scaler
    scaler = StandardScaler()
    scaler.fit(X)
    
    # 确保data/models文件夹存在
    models_dir = Path('data/models')
    models_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存模型
    model_file = models_dir / 'test_price_prediction_model.pkl'
    scaler_file = models_dir / 'test_price_prediction_scaler.pkl'
    
    joblib.dump(model, model_file)
    joblib.dump(scaler, scaler_file)
    
    print(f"✅ 测试模型已保存:")
    print(f"   - 模型: {model_file}")
    print(f"   - 标准化器: {scaler_file}")
    
    return model_file, scaler_file

def test_real_upload():
    """测试真实模型上传"""
    print("🚀 测试真实模型上传")
    print("=" * 50)
    
    try:
        # 创建测试模型
        model_file, scaler_file = create_test_model()
        
        # 导入云传输管理器
        from utils.cloud_transfer import CloudTransferManager
        
        # 创建传输管理器
        ctm = CloudTransferManager()
        
        # 测试连接
        print("\n🔍 测试连接...")
        if not ctm.test_connection():
            print("❌ 连接失败，无法进行上传测试")
            return False
        
        print("✅ 连接正常")
        
        # 上传模型
        print(f"\n📤 上传模型: {model_file.name}")
        result1 = ctm.upload_model(model_file, 'price_prediction')
        
        if result1['success']:
            print("✅ 模型上传成功！")
            print(f"📊 响应: {result1.get('response', {})}")
        else:
            print(f"❌ 模型上传失败: {result1.get('error', 'Unknown error')}")
        
        # 上传标准化器
        print(f"\n📤 上传标准化器: {scaler_file.name}")
        result2 = ctm.upload_model(scaler_file, 'price_prediction')
        
        if result2['success']:
            print("✅ 标准化器上传成功！")
            print(f"📊 响应: {result2.get('response', {})}")
        else:
            print(f"❌ 标准化器上传失败: {result2.get('error', 'Unknown error')}")
        
        # 测试批量上传
        print(f"\n📦 测试批量上传...")
        batch_result = ctm.upload_all_models()
        
        print(f"📊 批量上传结果:")
        print(f"   - 成功: {batch_result.get('success', False)}")
        print(f"   - 上传数量: {batch_result.get('uploaded_count', 0)}")
        print(f"   - 总数量: {batch_result.get('total_count', 0)}")
        print(f"   - 成功率: {batch_result.get('success_rate', 0):.1%}")
        
        # 清理测试文件
        print(f"\n🧹 清理测试文件...")
        model_file.unlink()
        scaler_file.unlink()
        print("✅ 测试文件已清理")
        
        # 总结
        success_count = sum([
            result1['success'],
            result2['success'],
            batch_result.get('success', False)
        ])
        
        print("\n" + "=" * 50)
        print("📊 测试总结")
        print("=" * 50)
        print(f"✅ 成功测试: {success_count}/3")
        print(f"📈 成功率: {success_count/3:.1%}")
        
        if success_count >= 2:
            print("🎉 本地通信测试通过！")
            return True
        else:
            print("⚠️ 部分功能需要调整")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_real_upload()
    input("\n按任意键退出...")
    sys.exit(0 if success else 1)
