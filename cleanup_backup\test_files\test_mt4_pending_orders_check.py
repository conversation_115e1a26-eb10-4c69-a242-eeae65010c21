"""
MT4挂单查询测试脚本
用于测试获取挂单（委托单）
"""
import os
import sys
import time
from app.utils.mt4_client import mt4_client

def test_mt4_pending_orders_check():
    """测试获取MT4挂单"""
    try:
        print('开始测试获取挂单...')
        
        # 连接到MT4服务器
        print('连接到MT4服务器...')
        connected = mt4_client.connect()
        print(f'连接结果: {connected}')
        
        if not connected:
            print('连接MT4服务器失败，无法继续测试')
            return
        
        # 获取活跃订单
        print('\n获取活跃订单...')
        active_orders = mt4_client.get_active_orders()
        print(f'活跃订单: {active_orders}')
        
        # 尝试直接发送PENDING_ORDERS请求
        print('\n尝试获取挂单...')
        try:
            pending_orders_response = mt4_client.send_request({
                'action': 'PENDING_ORDERS'
            })
            print(f'挂单响应: {pending_orders_response}')
        except Exception as error:
            print(f'获取挂单出错: {error}')
        
        # 尝试其他可能的命令
        print('\n尝试其他可能的命令...')
        commands = [
            'GET_PENDING_ORDERS',
            'PENDING_ORDERS',
            'GET_ORDERS',
            'ALL_ORDERS'
        ]
        
        for cmd in commands:
            print(f'\n尝试命令: {cmd}')
            try:
                response = mt4_client.send_request({
                    'action': cmd
                })
                print(f'响应: {response}')
            except Exception as error:
                print(f'命令 {cmd} 出错: {error}')
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_mt4_pending_orders_check()
