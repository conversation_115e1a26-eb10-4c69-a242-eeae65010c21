"""
自动交易测试脚本
用于测试自动交易功能
"""
import os
import sys
import time
from app.services.forex_trading_service import execute_trade

def test_auto_trade():
    """测试自动交易"""
    try:
        print('开始测试自动交易...')
        
        # 创建一个测试交易指令
        trade_instructions = {
            'action': 'BUY',
            'orderType': 'MARKET',
            'entryPrice': None,
            'stopLoss': 1.12000,
            'takeProfit': 1.13500,
            'riskLevel': 'MEDIUM',
            'reasoning': '测试自动交易功能'
        }
        
        print(f'交易指令: {trade_instructions}')
        
        # 执行交易
        result = execute_trade(trade_instructions)
        
        print(f'交易结果: {result}')
        
        print('\n测试完成!')
    except Exception as error:
        print(f'测试过程中出错: {error}')

if __name__ == '__main__':
    test_auto_trade()
