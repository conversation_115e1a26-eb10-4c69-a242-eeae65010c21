"""
运行外汇交易统计分析
"""
import os
import sys
import time
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.forex_statistics import run_statistics_analysis


def main():
    """主函数"""
    print("=" * 50)
    print("外汇交易系统统计分析工具")
    print("=" * 50)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行统计分析
    start_time = time.time()
    report = run_statistics_analysis()
    end_time = time.time()
    
    if report:
        print("\n统计报告生成成功！")
        print(f"报告保存路径: {os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'data', 'forex_statistics_report.txt')}")
        print(f"图表保存路径: {os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'data', 'charts')}")
        
        # 显示报告摘要
        print("\n报告摘要:")
        print("-" * 50)
        lines = report.split('\n')
        for line in lines[:20]:  # 只显示前20行
            print(line)
        if len(lines) > 20:
            print("...")
            print("(查看完整报告请打开报告文件)")
    else:
        print("\n统计报告生成失败！")
    
    print(f"\n耗时: {end_time - start_time:.2f}秒")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)


if __name__ == "__main__":
    main()
