@echo off
chcp 65001
echo ========================================
echo 外汇交易系统打包脚本 v2.0 - 服务器就绪版
echo ========================================

set PACKAGE_NAME=ForexTradingSystem_v2.0.0_ServerReady
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

echo 📦 开始打包外汇交易系统...
echo 📅 时间戳: %TIMESTAMP%
echo 📁 包名: %PACKAGE_NAME%

REM 创建打包目录
if exist "%PACKAGE_NAME%" rmdir /s /q "%PACKAGE_NAME%"
mkdir "%PACKAGE_NAME%"

echo.
echo 📋 复制核心文件...

REM 复制核心应用文件
echo   📁 复制应用代码...
xcopy "app" "%PACKAGE_NAME%\app\" /E /I /Y /Q
if errorlevel 1 (
    echo ❌ 复制app目录失败
    pause
    exit /b 1
)

echo   📄 复制主要文件...
copy "run.py" "%PACKAGE_NAME%\" >nul
copy "requirements.txt" "%PACKAGE_NAME%\" >nul
copy "README.md" "%PACKAGE_NAME%\" >nul

REM 复制配置文件
echo   ⚙️ 复制配置文件...
if exist ".env.local" copy ".env.local" "%PACKAGE_NAME%\" >nul
if exist "config.py" copy "config.py" "%PACKAGE_NAME%\" >nul

REM 复制部署和启动脚本
echo   🚀 复制部署和启动脚本...
copy "start_server.bat" "%PACKAGE_NAME%\" >nul
copy "deploy_server.bat" "%PACKAGE_NAME%\" >nul

REM 复制文档
echo   📚 复制文档...
if exist "docs" xcopy "docs" "%PACKAGE_NAME%\docs\" /E /I /Y /Q >nul
copy "Windows_部署指南.md" "%PACKAGE_NAME%\" >nul
copy "SERVER_DEPLOYMENT_GUIDE.md" "%PACKAGE_NAME%\" >nul

REM 复制模板文件
echo   📝 复制模板文件...
if exist "templates" xcopy "templates" "%PACKAGE_NAME%\templates\" /E /I /Y /Q >nul

REM 创建必要的目录结构
echo   📁 创建目录结构...
mkdir "%PACKAGE_NAME%\logs" >nul 2>&1
mkdir "%PACKAGE_NAME%\logs\error_logs" >nul 2>&1
mkdir "%PACKAGE_NAME%\app\data" >nul 2>&1
mkdir "%PACKAGE_NAME%\app\data\errors" >nul 2>&1
mkdir "%PACKAGE_NAME%\app\data\charts" >nul 2>&1
mkdir "%PACKAGE_NAME%\backups" >nul 2>&1

REM 复制远程更新功能
echo   🔄 复制远程更新功能...
copy "remote_update_client.py" "%PACKAGE_NAME%\" >nul
copy "update_system.bat" "%PACKAGE_NAME%\" >nul
if exist "install_service.bat" copy "install_service.bat" "%PACKAGE_NAME%\" >nul

REM 创建版本信息
echo   📋 创建版本信息...
echo { > "%PACKAGE_NAME%\version.json"
echo   "version": "2.0.0", >> "%PACKAGE_NAME%\version.json"
echo   "build_date": "%TIMESTAMP%", >> "%PACKAGE_NAME%\version.json"
echo   "description": "外汇交易系统 - 服务器就绪版本", >> "%PACKAGE_NAME%\version.json"
echo   "features": [ >> "%PACKAGE_NAME%\version.json"
echo     "编码问题修复", >> "%PACKAGE_NAME%\version.json"
echo     "依赖管理优化", >> "%PACKAGE_NAME%\version.json"
echo     "自动部署脚本", >> "%PACKAGE_NAME%\version.json"
echo     "服务器兼容性增强", >> "%PACKAGE_NAME%\version.json"
echo     "错误处理改进" >> "%PACKAGE_NAME%\version.json"
echo   ] >> "%PACKAGE_NAME%\version.json"
echo } >> "%PACKAGE_NAME%\version.json"

REM Create deployment readme file
echo   Creating deployment readme...
echo # Forex Trading System v2.0.0 - Server Ready > "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo ## Quick Deployment >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo 1. Run deploy_server.bat >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo 2. Run start_server.bat >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo ## Version Features >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - Fixed Windows Server 2012 encoding issues >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - Optimized dependency management >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - Enhanced error handling >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - Added auto deployment scripts >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - Improved server compatibility >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo ## Documentation >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - SERVER_DEPLOYMENT_GUIDE.md: Detailed deployment guide >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - Windows_deployment_guide.md: Windows deployment instructions >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo - docs/: Technical documentation >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo ## Troubleshooting >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo Check these files if you encounter issues: >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo 1. forex_system.log - System logs >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo 2. app/data/errors/ - Error logs >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo 3. SERVER_DEPLOYMENT_GUIDE.md - Troubleshooting section >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo. >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo Build time: %TIMESTAMP% >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"
echo Version: v2.0.0 >> "%PACKAGE_NAME%\DEPLOYMENT_README.txt"

REM Clean unnecessary files
echo   Cleaning unnecessary files...
if exist "%PACKAGE_NAME%\app\__pycache__" rmdir /s /q "%PACKAGE_NAME%\app\__pycache__"
if exist "%PACKAGE_NAME%\app\*\__pycache__" (
    for /d %%i in ("%PACKAGE_NAME%\app\*\__pycache__") do rmdir /s /q "%%i"
)
if exist "%PACKAGE_NAME%\*.pyc" del "%PACKAGE_NAME%\*.pyc" /s /q
if exist "%PACKAGE_NAME%\*.log" del "%PACKAGE_NAME%\*.log" /q

echo.
echo Creating ZIP package...
powershell -command "Compress-Archive -Path '%PACKAGE_NAME%' -DestinationPath '%PACKAGE_NAME%.zip' -Force"

if exist "%PACKAGE_NAME%.zip" (
    echo Package created successfully!
    echo Package file: %PACKAGE_NAME%.zip
    echo Package directory: %PACKAGE_NAME%\
    echo.
    echo Deployment steps:
    echo 1. Upload %PACKAGE_NAME%.zip to server
    echo 2. Extract to target directory
    echo 3. Run deploy_server.bat
    echo 4. Run start_server.bat
    echo.
    echo See SERVER_DEPLOYMENT_GUIDE.md for detailed instructions
) else (
    echo Package creation failed
)

echo.
echo ========================================
echo Packaging completed!
echo ========================================
pause
