"""
QuantumForex MLTrainer 性能指标计算器
计算各种模型性能和交易相关指标
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Any
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

class PerformanceMetrics:
    """性能指标计算器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_classification_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                                       labels: List[str] = None) -> Dict[str, Any]:
        """计算分类模型指标"""
        try:
            metrics = {}
            
            # 基础分类指标
            metrics['accuracy'] = accuracy_score(y_true, y_pred)
            metrics['precision'] = precision_score(y_true, y_pred, average='weighted', zero_division=0)
            metrics['recall'] = recall_score(y_true, y_pred, average='weighted', zero_division=0)
            metrics['f1_score'] = f1_score(y_true, y_pred, average='weighted', zero_division=0)
            
            # 混淆矩阵
            cm = confusion_matrix(y_true, y_pred)
            metrics['confusion_matrix'] = cm.tolist()
            
            # 各类别的详细指标
            precision_per_class = precision_score(y_true, y_pred, average=None, zero_division=0)
            recall_per_class = recall_score(y_true, y_pred, average=None, zero_division=0)
            f1_per_class = f1_score(y_true, y_pred, average=None, zero_division=0)
            
            if labels is None:
                labels = [f'Class_{i}' for i in range(len(precision_per_class))]
            
            metrics['per_class'] = {}
            for i, label in enumerate(labels):
                if i < len(precision_per_class):
                    metrics['per_class'][label] = {
                        'precision': float(precision_per_class[i]),
                        'recall': float(recall_per_class[i]),
                        'f1_score': float(f1_per_class[i])
                    }
            
            self.logger.info(f"✅ 分类指标计算完成: 准确率={metrics['accuracy']:.4f}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"❌ 计算分类指标失败: {e}")
            return {}
    
    def calculate_regression_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """计算回归模型指标"""
        try:
            metrics = {}
            
            # 基础回归指标
            metrics['mse'] = mean_squared_error(y_true, y_pred)
            metrics['rmse'] = np.sqrt(metrics['mse'])
            metrics['mae'] = mean_absolute_error(y_true, y_pred)
            metrics['r2'] = r2_score(y_true, y_pred)
            
            # 平均绝对百分比误差
            mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
            metrics['mape'] = mape
            
            # 方向准确率（预测方向是否正确）
            direction_accuracy = np.mean(np.sign(y_true) == np.sign(y_pred))
            metrics['direction_accuracy'] = direction_accuracy
            
            self.logger.info(f"✅ 回归指标计算完成: R²={metrics['r2']:.4f}, RMSE={metrics['rmse']:.4f}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"❌ 计算回归指标失败: {e}")
            return {}
    
    def calculate_trading_metrics(self, predictions: np.ndarray, actual_returns: np.ndarray,
                                prices: np.ndarray = None, transaction_cost: float = 0.0001) -> Dict[str, float]:
        """计算交易相关指标"""
        try:
            metrics = {}
            
            # 将预测转换为交易信号
            signals = self._predictions_to_signals(predictions)
            
            # 计算策略收益
            strategy_returns = signals * actual_returns
            
            # 扣除交易成本
            position_changes = np.abs(np.diff(signals, prepend=0))
            transaction_costs = position_changes * transaction_cost
            strategy_returns = strategy_returns - transaction_costs
            
            # 累积收益
            cumulative_returns = np.cumprod(1 + strategy_returns) - 1
            metrics['total_return'] = cumulative_returns[-1] if len(cumulative_returns) > 0 else 0
            
            # 年化收益率（假设252个交易日）
            if len(strategy_returns) > 0:
                periods_per_year = 252 * 24 * 60  # 分钟数据
                annualized_return = (1 + metrics['total_return']) ** (periods_per_year / len(strategy_returns)) - 1
                metrics['annualized_return'] = annualized_return
            else:
                metrics['annualized_return'] = 0
            
            # 夏普比率
            if len(strategy_returns) > 1:
                sharpe_ratio = np.mean(strategy_returns) / np.std(strategy_returns) * np.sqrt(periods_per_year)
                metrics['sharpe_ratio'] = sharpe_ratio if not np.isnan(sharpe_ratio) else 0
            else:
                metrics['sharpe_ratio'] = 0
            
            # 最大回撤
            peak = np.maximum.accumulate(1 + cumulative_returns)
            drawdown = (1 + cumulative_returns) / peak - 1
            metrics['max_drawdown'] = np.min(drawdown)
            
            # 胜率
            winning_trades = strategy_returns > 0
            metrics['win_rate'] = np.mean(winning_trades) if len(strategy_returns) > 0 else 0
            
            # 盈亏比
            winning_returns = strategy_returns[winning_trades]
            losing_returns = strategy_returns[~winning_trades]
            
            if len(winning_returns) > 0 and len(losing_returns) > 0:
                avg_win = np.mean(winning_returns)
                avg_loss = np.mean(np.abs(losing_returns))
                metrics['profit_loss_ratio'] = avg_win / avg_loss if avg_loss != 0 else 0
            else:
                metrics['profit_loss_ratio'] = 0
            
            # 盈利因子
            total_wins = np.sum(winning_returns) if len(winning_returns) > 0 else 0
            total_losses = np.sum(np.abs(losing_returns)) if len(losing_returns) > 0 else 0
            metrics['profit_factor'] = total_wins / total_losses if total_losses != 0 else float('inf')
            
            # 交易次数
            metrics['total_trades'] = np.sum(position_changes > 0)
            
            # Calmar比率
            if metrics['max_drawdown'] != 0:
                metrics['calmar_ratio'] = metrics['annualized_return'] / abs(metrics['max_drawdown'])
            else:
                metrics['calmar_ratio'] = float('inf') if metrics['annualized_return'] > 0 else 0
            
            self.logger.info(f"✅ 交易指标计算完成: 总收益={metrics['total_return']:.4f}, 夏普比率={metrics['sharpe_ratio']:.4f}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"❌ 计算交易指标失败: {e}")
            return {}
    
    def _predictions_to_signals(self, predictions: np.ndarray) -> np.ndarray:
        """将预测转换为交易信号"""
        try:
            signals = np.zeros_like(predictions, dtype=float)
            
            # 如果预测是分类结果（-1, 0, 1）
            if np.all(np.isin(predictions, [-1, 0, 1])):
                signals = predictions.astype(float)
            
            # 如果预测是概率或连续值
            else:
                # 使用阈值转换为信号
                upper_threshold = np.percentile(predictions, 75)
                lower_threshold = np.percentile(predictions, 25)
                
                signals[predictions > upper_threshold] = 1   # 买入信号
                signals[predictions < lower_threshold] = -1  # 卖出信号
                signals[(predictions >= lower_threshold) & (predictions <= upper_threshold)] = 0  # 持有
            
            return signals
            
        except Exception as e:
            self.logger.error(f"❌ 转换交易信号失败: {e}")
            return np.zeros_like(predictions)
    
    def calculate_feature_importance_metrics(self, feature_importance: Dict[str, float],
                                           top_n: int = 20) -> Dict[str, Any]:
        """计算特征重要性指标"""
        try:
            if not feature_importance:
                return {}
            
            # 排序特征重要性
            sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
            
            # 前N个重要特征
            top_features = sorted_features[:top_n]
            
            # 重要性分布统计
            importances = list(feature_importance.values())
            
            metrics = {
                'top_features': [{'feature': name, 'importance': importance} for name, importance in top_features],
                'total_features': len(feature_importance),
                'importance_stats': {
                    'mean': np.mean(importances),
                    'std': np.std(importances),
                    'max': np.max(importances),
                    'min': np.min(importances)
                },
                'concentration_ratio': sum([imp for _, imp in top_features[:5]]) / sum(importances) if importances else 0
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"❌ 计算特征重要性指标失败: {e}")
            return {}
    
    def calculate_model_stability(self, predictions_list: List[np.ndarray]) -> Dict[str, float]:
        """计算模型稳定性指标"""
        try:
            if len(predictions_list) < 2:
                return {'stability_score': 1.0, 'prediction_variance': 0.0}
            
            # 将预测结果堆叠
            predictions_array = np.array(predictions_list)
            
            # 计算预测方差
            prediction_variance = np.mean(np.var(predictions_array, axis=0))
            
            # 计算预测一致性
            mean_predictions = np.mean(predictions_array, axis=0)
            consistency_scores = []
            
            for predictions in predictions_list:
                correlation = np.corrcoef(predictions, mean_predictions)[0, 1]
                consistency_scores.append(correlation if not np.isnan(correlation) else 0)
            
            stability_score = np.mean(consistency_scores)
            
            metrics = {
                'stability_score': stability_score,
                'prediction_variance': prediction_variance,
                'consistency_scores': consistency_scores
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"❌ 计算模型稳定性失败: {e}")
            return {}
    
    def generate_performance_report(self, y_true: np.ndarray, y_pred: np.ndarray,
                                  actual_returns: np.ndarray = None,
                                  feature_importance: Dict[str, float] = None,
                                  model_type: str = 'classification') -> Dict[str, Any]:
        """生成完整的性能报告"""
        try:
            report = {
                'model_type': model_type,
                'sample_count': len(y_true),
                'timestamp': pd.Timestamp.now().isoformat()
            }
            
            # 模型性能指标
            if model_type == 'classification':
                report['model_metrics'] = self.calculate_classification_metrics(y_true, y_pred)
            else:
                report['model_metrics'] = self.calculate_regression_metrics(y_true, y_pred)
            
            # 交易性能指标
            if actual_returns is not None:
                report['trading_metrics'] = self.calculate_trading_metrics(y_pred, actual_returns)
            
            # 特征重要性
            if feature_importance:
                report['feature_importance'] = self.calculate_feature_importance_metrics(feature_importance)
            
            self.logger.info("✅ 性能报告生成完成")
            return report
            
        except Exception as e:
            self.logger.error(f"❌ 生成性能报告失败: {e}")
            return {}

# 创建性能指标计算器实例
performance_metrics = PerformanceMetrics()
