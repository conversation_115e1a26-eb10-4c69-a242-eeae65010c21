#!/usr/bin/env python3
"""
硅基流动API密钥设置脚本
帮助用户快速配置AgenticSeek使用硅基流动API
"""

import os
import re
from pathlib import Path

def setup_siliconflow_api():
    """设置硅基流动API密钥"""
    print("🚀 AgenticSeek - 硅基流动API设置")
    print("=" * 50)
    
    # 检查.env文件
    env_path = Path(".env")
    if not env_path.exists():
        print("❌ .env 文件不存在")
        return False
    
    print("📋 硅基流动API密钥设置指南:")
    print("1. 访问 https://cloud.siliconflow.cn/")
    print("2. 注册/登录账号")
    print("3. 进入控制台 -> API密钥")
    print("4. 创建新的API密钥")
    print("5. 复制密钥（格式：sk-xxxxxxxxxx）")
    print("-" * 50)
    
    # 获取用户输入的API密钥
    while True:
        api_key = input("🔑 请输入你的硅基流动API密钥: ").strip()
        
        if not api_key:
            print("❌ API密钥不能为空")
            continue
        
        if not api_key.startswith('sk-'):
            print("❌ API密钥格式错误，应该以 'sk-' 开头")
            continue
        
        if len(api_key) < 20:
            print("❌ API密钥长度太短，请检查是否完整")
            continue
        
        break
    
    # 更新.env文件
    try:
        with open(env_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换或添加SILICONFLOW_API_KEY
        if 'SILICONFLOW_API_KEY' in content:
            # 替换现有的密钥
            content = re.sub(
                r'SILICONFLOW_API_KEY=.*',
                f"SILICONFLOW_API_KEY='{api_key}'",
                content
            )
        else:
            # 添加新的密钥
            content += f"\nSILICONFLOW_API_KEY='{api_key}'\n"
        
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ API密钥设置成功！")
        
    except Exception as e:
        print(f"❌ 设置失败: {str(e)}")
        return False
    
    return True

def test_api_connection():
    """测试API连接"""
    print("\n🔍 测试API连接...")
    
    try:
        from dotenv import load_dotenv
        import openai
        
        load_dotenv()
        api_key = os.getenv('SILICONFLOW_API_KEY')
        
        if not api_key:
            print("❌ 未找到API密钥")
            return False
        
        client = openai.OpenAI(
            api_key=api_key,
            base_url="https://api.siliconflow.cn/v1"
        )
        
        # 发送测试请求
        response = client.chat.completions.create(
            model="deepseek-ai/DeepSeek-V3",
            messages=[
                {"role": "user", "content": "你好，请简单介绍一下你自己"}
            ],
            max_tokens=50
        )
        
        print("✅ API连接测试成功！")
        print(f"🤖 AI回复: {response.choices[0].message.content}")
        return True
        
    except ImportError:
        print("❌ 缺少依赖包，请运行: pip install openai python-dotenv")
        return False
    except Exception as e:
        print(f"❌ API连接失败: {str(e)}")
        return False

def show_available_models():
    """显示可用模型"""
    print("\n📋 硅基流动平台可用模型:")
    models = [
        "deepseek-ai/DeepSeek-V3 (推荐)",
        "deepseek-ai/DeepSeek-V2.5",
        "Qwen/Qwen2.5-72B-Instruct",
        "meta-llama/Meta-Llama-3.1-70B-Instruct",
        "01-ai/Yi-1.5-34B-Chat",
        "microsoft/DialoGPT-medium"
    ]
    
    for i, model in enumerate(models, 1):
        print(f"  {i}. {model}")
    
    print("\n💡 当前配置使用: deepseek-ai/DeepSeek-V3")
    print("   如需更换模型，请修改 config.ini 中的 provider_model")

def main():
    """主函数"""
    print("🌟 欢迎使用AgenticSeek硅基流动配置工具！")
    
    # 设置API密钥
    if setup_siliconflow_api():
        # 测试连接
        if test_api_connection():
            print("\n🎉 配置完成！现在可以运行AgenticSeek了:")
            print("   python simple_cli.py")
        else:
            print("\n⚠️ API密钥已设置，但连接测试失败")
            print("   请检查密钥是否正确，或稍后重试")
    
    # 显示可用模型
    show_available_models()
    
    print("\n📚 更多信息:")
    print("   - 硅基流动官网: https://cloud.siliconflow.cn/")
    print("   - API文档: https://docs.siliconflow.cn/")
    print("   - 模型价格: https://cloud.siliconflow.cn/pricing")

if __name__ == "__main__":
    main()
