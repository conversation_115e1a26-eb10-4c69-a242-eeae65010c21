#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuantumForex Pro - 自动化真实交易测试脚本
自动进行真实的MT4交易测试，验证生产环境可用性
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_current_market_price(mt4_client, symbol):
    """获取当前市场价格"""
    try:
        market_info = mt4_client.get_market_info(symbol)
        if market_info and market_info.get('status') == 'success':
            data = market_info.get('data', {})
            return {
                'bid': float(data.get('bid', 0)),
                'ask': float(data.get('ask', 0)),
                'spread': float(data.get('spread', 0))
            }
        return None
    except Exception as e:
        print(f"获取市场价格失败: {e}")
        return None

def calculate_reasonable_sl_tp(symbol, current_price, trade_type, pip_value=0.0001):
    """计算合理的止损止盈价格"""
    try:
        if trade_type.upper() == 'BUY':
            # 买入：止损在下方，止盈在上方
            sl = current_price - (20 * pip_value)  # 20点止损
            tp = current_price + (30 * pip_value)  # 30点止盈
        else:  # SELL
            # 卖出：止损在上方，止盈在下方
            sl = current_price + (20 * pip_value)  # 20点止损
            tp = current_price - (30 * pip_value)  # 30点止盈
        
        # 对于JPY货币对，调整pip值
        if 'JPY' in symbol:
            pip_value = 0.01
            if trade_type.upper() == 'BUY':
                sl = current_price - (20 * pip_value)
                tp = current_price + (30 * pip_value)
            else:
                sl = current_price + (20 * pip_value)
                tp = current_price - (30 * pip_value)
        
        return round(sl, 5), round(tp, 5)
    except Exception as e:
        print(f"计算止损止盈失败: {e}")
        return 0, 0

def auto_test_real_trading():
    """自动化真实交易测试"""
    print("=" * 60)
    print("🚀 QuantumForex Pro - 自动化真实交易测试")
    print("=" * 60)
    print("⚠️  这是真实交易测试，将使用真实资金进行小额测试！")
    print("⚠️  测试将自动执行，无需用户交互！")
    print("=" * 60)
    
    try:
        # 导入MT4客户端
        from utils.mt4_client import mt4_client
        
        print("\n📋 自动化真实交易测试流程:")
        print("1. 连接MT4服务器")
        print("2. 获取账户信息")
        print("3. 获取当前市场价格")
        print("4. 计算合理的止损止盈")
        print("5. 执行小额测试交易")
        print("6. 监控交易结果")
        print("7. 自动清理测试订单")
        
        # 1. 连接测试
        print("\n" + "─" * 40)
        print("🔗 1. 连接MT4服务器...")
        connected = mt4_client.connect()
        if not connected:
            print("❌ MT4连接失败，无法进行真实交易测试")
            return False
        print("✅ MT4连接成功")
        
        # 2. 获取账户信息
        print("\n💰 2. 获取账户信息...")
        account_info = mt4_client.get_account_info()
        if account_info and account_info.get('status') == 'success':
            data = account_info.get('data', {})
            balance = float(data.get('balance', 0))
            equity = float(data.get('equity', 0))
            free_margin = float(data.get('free_margin', 0))
            
            print(f"   账户余额: ${balance:.2f}")
            print(f"   账户净值: ${equity:.2f}")
            print(f"   可用保证金: ${free_margin:.2f}")
            
            # 检查账户是否有足够资金
            if balance < 100:
                print("❌ 账户余额不足，无法进行交易测试")
                return False
        else:
            print("❌ 无法获取账户信息")
            return False
        
        # 3. 选择测试货币对和获取价格
        test_symbol = 'EURUSD'  # 使用EURUSD进行测试
        print(f"\n📊 3. 获取{test_symbol}当前价格...")
        
        market_data = get_current_market_price(mt4_client, test_symbol)
        if not market_data:
            print(f"❌ 无法获取{test_symbol}市场价格")
            return False
        
        current_bid = market_data['bid']
        current_ask = market_data['ask']
        spread = market_data['spread']
        
        print(f"   当前买价: {current_bid}")
        print(f"   当前卖价: {current_ask}")
        print(f"   点差: {spread}点")
        
        # 4. 计算合理的止损止盈
        print("\n🎯 4. 计算交易参数...")
        
        # 使用最小手数进行测试
        test_lot = 0.01
        trade_type = 'BUY'  # 测试买入
        
        # 使用当前ask价格作为买入价格
        entry_price = current_ask
        sl_price, tp_price = calculate_reasonable_sl_tp(test_symbol, entry_price, trade_type)
        
        print(f"   交易类型: {trade_type}")
        print(f"   交易手数: {test_lot}")
        print(f"   入场价格: {entry_price}")
        print(f"   止损价格: {sl_price}")
        print(f"   止盈价格: {tp_price}")
        
        # 计算风险和收益
        risk_amount = test_lot * 10000 * abs(entry_price - sl_price)
        profit_amount = test_lot * 10000 * abs(tp_price - entry_price)
        
        print(f"   预计风险: ${risk_amount:.2f}")
        print(f"   预计收益: ${profit_amount:.2f}")
        print(f"   风险收益比: 1:{profit_amount/risk_amount:.2f}")
        
        # 5. 执行真实交易
        print("\n💼 5. 执行真实交易...")
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 自动执行买入操作...")
        
        trade_result = mt4_client.buy(
            symbol=test_symbol,
            lot=test_lot,
            sl=sl_price,
            tp=tp_price,
            comment='QuantumForex_Pro_AutoTest'
        )
        
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 交易执行结果: {trade_result}")
        
        if trade_result and trade_result.get('status') == 'success':
            order_id = trade_result.get('order_id', 'N/A')
            print(f"✅ 交易执行成功！")
            print(f"   订单ID: {order_id}")
            print(f"   交易详情: {trade_result.get('message', 'N/A')}")
            
            # 6. 监控交易结果
            print("\n📈 6. 监控交易结果...")
            print("等待10秒后检查订单状态...")
            time.sleep(10)
            
            # 获取活跃订单
            active_orders = mt4_client.get_active_orders()
            if active_orders and active_orders.get('status') == 'success':
                orders = active_orders.get('orders', [])
                test_order = None
                
                for order in orders:
                    if order.get('comment') == 'QuantumForex_Pro_AutoTest':
                        test_order = order
                        break
                
                if test_order:
                    print(f"✅ 找到测试订单:")
                    print(f"   订单ID: {test_order.get('order_id', 'N/A')}")
                    print(f"   货币对: {test_order.get('symbol', 'N/A')}")
                    print(f"   类型: {test_order.get('type', 'N/A')}")
                    print(f"   手数: {test_order.get('lots', 'N/A')}")
                    print(f"   开仓价: {test_order.get('open_price', 'N/A')}")
                    print(f"   当前价: {test_order.get('current_price', 'N/A')}")
                    print(f"   盈亏: {test_order.get('profit', 'N/A')}")
                    
                    # 7. 自动清理测试订单
                    print("\n🧹 7. 自动清理测试订单...")
                    print("等待5秒后自动关闭测试订单...")
                    time.sleep(5)
                    
                    close_result = mt4_client.close_order(test_order.get('order_id'))
                    if close_result and close_result.get('status') == 'success':
                        print("✅ 测试订单已自动关闭")
                        print(f"   关闭详情: {close_result.get('message', 'N/A')}")
                        return True
                    else:
                        print(f"❌ 自动关闭订单失败: {close_result.get('message', '未知错误')}")
                        print("⚠️  请手动关闭测试订单")
                        return False
                else:
                    print("⚠️  未找到测试订单，可能已被自动处理")
                    return True
            else:
                print("❌ 无法获取活跃订单状态")
                return False
        
        else:
            error_msg = trade_result.get('message', '未知错误') if trade_result else '无响应'
            print(f"❌ 交易执行失败: {error_msg}")
            
            # 分析失败原因
            if '交易执行失败' in error_msg:
                print("\n🔍 可能的失败原因:")
                print("   1. 止损止盈价格设置不合理")
                print("   2. 市场条件不允许交易")
                print("   3. 账户权限限制")
                print("   4. 服务器繁忙或网络问题")
                print("   5. 点差过大或流动性不足")
            
            return False
        
    except ImportError as e:
        print(f"❌ 导入MT4客户端失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始自动化真实交易测试...")
    success = auto_test_real_trading()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 自动化真实交易测试完成！系统在生产环境中可正常运行！")
    else:
        print("❌ 自动化真实交易测试失败！需要进一步调试！")
    print("=" * 60)
