"""
测试优化后的预分析功能
"""
import os
import sys
import time
import traceback
from datetime import datetime, timedelta
import json
import random

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
try:
    from app.utils.multi_round_analysis import should_perform_analysis
    print("模块导入成功")
except Exception as e:
    print(f"模块导入失败: {e}")
    sys.exit(1)


def create_test_data(price_change_percent=0.1, rsi_value=50, has_positions=False):
    """创建测试数据"""
    # 基础价格
    base_price = 1.1000

    # 根据价格变化百分比计算当前价格和前一个价格
    current_price = base_price * (1 + price_change_percent / 100)

    # 创建K线数据
    timeframe15m = []
    for i in range(20):
        # 生成递增的时间
        time_str = (datetime.now().replace(minute=0, second=0, microsecond=0) -
                   timedelta(minutes=(20 - i) * 15)).strftime("%Y-%m-%d %H:%M:%S")

        # 为最后两根K线设置特定价格，其他K线使用随机价格
        if i == 18:  # 倒数第二根
            close_price = base_price
        elif i == 19:  # 最后一根
            close_price = current_price
        else:
            # 其他K线使用随机价格
            close_price = base_price * (1 + random.uniform(-0.5, 0.5) / 100)

        # 生成开盘价、最高价和最低价 - 减小波动范围
        open_price = close_price * (1 + random.uniform(-0.02, 0.02) / 100)
        high_price = max(open_price, close_price) * (1 + random.uniform(0.005, 0.03) / 100)
        low_price = min(open_price, close_price) * (1 - random.uniform(0.005, 0.03) / 100)

        # 添加K线
        timeframe15m.append({
            "time": time_str,
            "open": str(round(open_price, 5)),
            "high": str(round(high_price, 5)),
            "low": str(round(low_price, 5)),
            "close": str(round(close_price, 5)),
            "volume": str(random.randint(100, 1000))
        })

    # 创建技术指标
    indicators = {
        "ma": {
            "5": str(round(base_price * (1 + random.uniform(-0.2, 0.2) / 100), 5)),
            "10": str(round(base_price * (1 + random.uniform(-0.2, 0.2) / 100), 5)),
            "20": str(round(base_price * (1 + random.uniform(-0.2, 0.2) / 100), 5)),
            "50": str(round(base_price * (1 + random.uniform(-0.2, 0.2) / 100), 5))
        },
        "rsi": rsi_value,
        "macd": {
            "macdLine": [str(round(random.uniform(-0.001, 0.001), 6))],
            "signalLine": [str(round(random.uniform(-0.001, 0.001), 6))],
            "histogram": [str(round(random.uniform(-0.001, 0.001), 6))]
        },
        "bollinger": {
            "upper": [str(round(base_price * 1.002, 5))],
            "middle": [str(round(base_price, 5))],
            "lower": [str(round(base_price * 0.998, 5))]
        },
        "momentum": str(round(random.uniform(-0.5, 0.5), 5))
    }

    # 创建持仓数据
    positions = []
    if has_positions:
        positions.append({
            "ticket": "12345678",
            "type": "BUY",
            "lots": "0.1",
            "openTime": (datetime.now() - timedelta(hours=5)).strftime("%Y-%m-%d %H:%M:%S"),
            "openPrice": str(round(base_price * 0.995, 5)),
            "stopLoss": str(round(base_price * 0.99, 5)),
            "takeProfit": str(round(base_price * 1.01, 5)),
            "profit": str(round((current_price - base_price * 0.995) * 10000, 2))
        })

    # 创建完整的测试数据
    data = {
        "symbol": "EURUSD",
        "timeframe15m": timeframe15m,
        "indicators": indicators,
        "positions": positions,
        "pendingOrders": [],
        "currentPrice": str(round(current_price, 5))
    }

    return data


def test_preanalysis():
    """测试预分析功能"""
    try:
        print("=" * 50)
        print("测试优化后的预分析功能")
        print("=" * 50)
        print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 测试场景
        test_scenarios = [
            {"name": "小幅价格变化，无持仓", "price_change": 0.1, "rsi": 50, "has_positions": False},
            {"name": "中等价格变化，无持仓", "price_change": 0.4, "rsi": 50, "has_positions": False},
            {"name": "大幅价格变化，无持仓", "price_change": 0.7, "rsi": 50, "has_positions": False},
            {"name": "小幅价格变化，有持仓", "price_change": 0.1, "rsi": 50, "has_positions": True},
            {"name": "中等价格变化，有持仓", "price_change": 0.4, "rsi": 50, "has_positions": True},
            {"name": "极端RSI值，无持仓", "price_change": 0.1, "rsi": 85, "has_positions": False},
            {"name": "正常RSI值，无持仓", "price_change": 0.1, "rsi": 65, "has_positions": False}
        ]

        # 执行测试
        for scenario in test_scenarios:
            print("\n" + "=" * 30)
            print(f"测试场景: {scenario['name']}")
            print("-" * 30)

            # 创建测试数据
            data = create_test_data(
                price_change_percent=scenario["price_change"],
                rsi_value=scenario["rsi"],
                has_positions=scenario["has_positions"]
            )

            # 确保输出立即显示
            sys.stdout.flush()

            # 执行预分析
            print("\n开始执行预分析...")
            try:
                should_analyze, reason, next_interval = should_perform_analysis(data)

                # 输出结果
                print("\n预分析结果:")
                print(f"是否需要分析: {should_analyze}")
                print(f"原因: {reason}")
                print(f"下次分析间隔: {next_interval}分钟")
            except Exception as e:
                print(f"\n预分析执行失败: {e}")
                traceback.print_exc()

        print("\n测试完成")
    except Exception as e:
        print(f"测试过程出错: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    test_preanalysis()
