"""
MT4客户端工具
用于与MT4服务器通信，执行交易操作
支持多用户和授权验证（授权验证由MT4 Server-V2处理）
"""
import os
import json
import uuid
import time
import zmq
from dotenv import load_dotenv
from datetime import datetime

# 加载环境变量
load_dotenv()

# 检查是否跳过MT4连接
def should_skip_mt4_connection():
    """
    智能判断是否应该跳过MT4连接（基于外汇市场实际交易时间）
    系统时间为北京时间，判断全球外汇市场是否开放

    Returns:
        bool: True表示应该跳过MT4连接，False表示正常连接
    """
    # 首先检查手动设置的环境变量
    manual_skip = os.environ.get('SKIP_MT4_CONNECTION', '').lower()
    if manual_skip == 'true':
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🔧 手动设置测试模式：SKIP_MT4_CONNECTION=true')
        return True
    elif manual_skip == 'false':
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 🔥 手动设置生产模式：SKIP_MT4_CONNECTION=false')
        return False

    # 基于外汇市场实际交易时间判断（北京时间）
    try:
        from datetime import datetime
        now = datetime.now()  # 北京时间
        weekday = now.weekday()  # 0=周一, 6=周日
        hour = now.hour

        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 📅 外汇市场时间检查: 周{["一","二","三","四","五","六","日"][weekday]} {hour:02d}:{now.minute:02d} (北京时间)')

        # 外汇市场真正的停盘时间（北京时间）：
        # 周六 05:00 - 周一 06:00
        # 其他时间至少有一个主要市场开放

        if weekday == 5:  # 周六
            if hour >= 5:  # 周六05:00后，纽约市场关闭
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 🕒 外汇市场停盘：周六{hour:02d}:00，纽约市场已关闭')
                return True
            else:  # 周六05:00前，纽约市场仍开放
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 🌍 外汇市场开放：纽约市场交易中')
                return False

        elif weekday == 6:  # 周日
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 🕒 外汇市场停盘：周日全天休市')
            return True

        elif weekday == 0:  # 周一
            if hour < 6:  # 周一06:00前，悉尼市场未开
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 🕒 外汇市场停盘：周一{hour:02d}:00，悉尼市场未开')
                return True
            else:  # 周一06:00后，悉尼市场开放
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 🌍 外汇市场开放：悉尼市场已开盘')
                return False

        # 周二到周五，至少有一个主要市场开放
        # 悉尼(06:00-15:00) → 东京(08:00-17:00) → 伦敦(15:30-00:30) → 纽约(20:30-05:30)
        market_status = []

        # 悉尼市场 06:00-15:00
        if 6 <= hour < 15:
            market_status.append("悉尼")

        # 东京市场 08:00-17:00
        if 8 <= hour < 17:
            market_status.append("东京")

        # 伦敦市场 15:30-00:30 (跨日)
        if hour >= 15 or hour < 1:  # 15:00-23:59 或 00:00-00:59
            if (hour == 15 and now.minute >= 30) or hour > 15 or hour < 1:
                market_status.append("伦敦")

        # 纽约市场 20:30-05:30 (跨日)
        if hour >= 20 or hour < 6:  # 20:00-23:59 或 00:00-05:59
            if (hour == 20 and now.minute >= 30) or hour > 20 or hour < 6:
                market_status.append("纽约")

        if market_status:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 🌍 外汇市场开放：{", ".join(market_status)}市场交易中')
            return False
        else:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 🕒 外汇市场间歇：主要市场间隙时间')
            # 即使在间隙时间，也允许连接（可能有其他小市场开放）
            return False

    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ⚠️ 无法检查外汇市场时间，默认允许连接: {e}')
        return False

# 动态获取跳过状态
def get_skip_mt4_status():
    """获取当前MT4跳过状态"""
    return should_skip_mt4_connection()

# MT4服务器地址
MT4_SERVER_ADDRESS = os.getenv('MT4_SERVER_ADDRESS', 'tcp://127.0.0.1:5555')
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器地址: {MT4_SERVER_ADDRESS}')
# 授权码（可选）
AUTH_CODE = os.getenv('AUTH_CODE', None)


class MT4Client:
    """MT4客户端类，支持多用户和授权验证"""

    def __init__(self, server_address=None, auth_code=None):
        """
        初始化MT4客户端

        Args:
            server_address (str, optional): MT4服务器地址
            auth_code (str, optional): 授权码
        """
        self.server_address = server_address or MT4_SERVER_ADDRESS
        self.auth_code = auth_code or AUTH_CODE  # 使用传入的授权码或环境变量中的授权码
        self.user_info = None
        self.socket = None
        self.context = None
        self.is_connected = False
        self.is_authorized = False  # 授权状态由MT4 Server-V2验证
        self.request_timeout = 60000  # 请求超时时间（毫秒），增加到60秒，提高稳定性
        self.max_retries = 5  # 最大重试次数，增加到5次
        self.retry_delay = 3  # 重试延迟（秒），增加到3秒
        self.last_connect_attempt = 0  # 上次连接尝试时间
        self.connect_cooldown = 3  # 连接冷却时间（秒），减少到3秒，提高响应速度
        self.connecting = False  # 连接锁，防止多线程同时连接
        self.connect_lock_time = 0  # 连接锁设置时间
        self.connect_lock_timeout = 60  # 连接锁超时时间（秒），增加到60秒
        self.connection_id = 0  # 连接ID，用于跟踪连接操作
        self.heartbeat_interval = 60  # 心跳检测间隔（秒）
        self.last_heartbeat = time.time()  # 上次心跳时间
        self.heartbeat_enabled = True  # 是否启用心跳检测

    def set_auth_code(self, auth_code):
        """
        设置授权码

        Args:
            auth_code (str): 授权码
        """
        self.auth_code = auth_code
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 设置授权码: {auth_code}')
        # 授权验证将在连接MT4服务器时进行

    def connect(self):
        """
        连接到MT4服务器，包含重试机制和连接状态管理
        如果设置了授权码，会在连接时发送授权信息

        Returns:
            bool: 连接是否成功
        """
        # 检查是否跳过MT4连接
        if should_skip_mt4_connection():
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ⚠️  MT4服务器跳过模式已启用，模拟连接成功')
            self.is_connected = True
            self.is_authorized = True
            self.user_info = {
                'id': 'test-user',
                'username': '测试用户',
                'expiry_date': '2025-12-31',
                'account_type': '测试账户'
            }
            return True

        # 如果设置了授权码，记录日志
        if self.auth_code:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 使用授权码连接MT4服务器: {self.auth_code}')
        # 检查是否已经连接
        if self.is_connected and self.socket:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已经连接到MT4服务器，无需重新连接')
            return True

        # 检查连接锁是否超时
        current_time = time.time()
        if self.connecting:
            # 如果连接锁已经超时，强制释放
            if current_time - self.connect_lock_time > self.connect_lock_timeout:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接锁已超时 ({self.connect_lock_timeout}秒)，强制释放')
                self.connecting = False
            else:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已有连接操作正在进行中，跳过本次连接请求')
                return False

        # 检查连接冷却时间
        if current_time - self.last_connect_attempt < self.connect_cooldown:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接请求过于频繁，请等待 {self.connect_cooldown - (current_time - self.last_connect_attempt):.1f} 秒后再试')
            return False

        # 设置连接锁和连接ID
        self.connecting = True
        self.connect_lock_time = current_time
        self.last_connect_attempt = current_time
        self.connection_id += 1
        connection_id = self.connection_id

        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接操作 #{connection_id} 开始')

        try:
            # 无论如何，先断开现有连接并清理资源
            self.disconnect()

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 正在连接到MT4服务器: {self.server_address}')

            # 创建新的ZMQ上下文和套接字
            if self.context is None:
                self.context = zmq.Context()

            # 确保创建新的套接字
            self.socket = self.context.socket(zmq.REQ)

            # 设置超时时间
            self.socket.setsockopt(zmq.RCVTIMEO, self.request_timeout)

            # 连接到服务器
            self.socket.connect(self.server_address)

            # 使用重试机制发送ping请求测试连接
            for retry in range(self.max_retries):
                try:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 发送ping请求测试连接... (尝试 {retry+1}/{self.max_retries})')

                    # 直接使用socket发送ping请求，避免递归调用send_request
                    request_id = str(uuid.uuid4())
                    request = {'action': 'ping', 'requestId': request_id}

                    # 如果设置了授权码，添加到请求中
                    if self.auth_code:
                        request['auth_code'] = self.auth_code
                        request['auth_type'] = 'client'  # 标识为客户端授权

                    request_str = json.dumps(request)

                    self.socket.send_string(request_str)
                    response_str = self.socket.recv_string()

                    # 解析响应
                    response = json.loads(response_str)

                    if response and response.get('status') == 'success':
                        self.is_connected = True
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 成功连接到MT4服务器')

                        # 检查授权状态
                        if 'auth_status' in response:
                            self.is_authorized = response.get('auth_status') == 'valid'
                            if self.is_authorized:
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 授权验证成功，用户: {response.get("username", "未知")}')
                                # 保存用户信息
                                self.user_info = {
                                    'id': response.get('user_id', ''),
                                    'username': response.get('username', '未知用户'),
                                    'expiry_date': response.get('expiry_date', ''),
                                    'account_type': response.get('account_type', '标准账户')
                                }
                            else:
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 授权验证失败: {response.get("auth_message", "未知错误")}')

                        self.connecting = False
                        return True
                    else:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器连接测试失败: {response}')

                        # 如果不是最后一次重试，等待后再试
                        if retry < self.max_retries - 1:
                            wait_time = self.retry_delay * (retry + 1)  # 指数退避
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                            time.sleep(wait_time)
                        else:
                            self.is_connected = False

                except zmq.error.Again:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器响应超时 (尝试 {retry+1}/{self.max_retries})')

                    # 如果不是最后一次重试，等待后再试
                    if retry < self.max_retries - 1:
                        wait_time = self.retry_delay * (retry + 1)  # 指数退避
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                        time.sleep(wait_time)
                    else:
                        self.is_connected = False

                except Exception as ping_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 发送ping请求失败: {ping_error} (尝试 {retry+1}/{self.max_retries})')

                    # 如果不是最后一次重试，等待后再试
                    if retry < self.max_retries - 1:
                        wait_time = self.retry_delay * (retry + 1)  # 指数退避
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                        time.sleep(wait_time)
                    else:
                        self.is_connected = False

            # 所有重试都失败
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接操作 #{connection_id} 失败: 所有重试都失败')
            self.is_connected = False
            self.connecting = False
            return False

        except Exception as error:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接操作 #{connection_id} 失败: {error}')
            self.is_connected = False

            # 释放连接锁
            self.connecting = False
            return False
        finally:
            # 确保在任何情况下都释放连接锁
            if not self.is_connected:
                self.connecting = False
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接操作 #{connection_id} 结束，连接锁已释放')

    def disconnect(self):
        """断开与MT4服务器的连接"""
        if self.socket:
            try:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 正在断开与MT4服务器的连接')

                try:
                    # 尝试断开连接
                    self.socket.disconnect(self.server_address)
                except Exception as disconnect_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 断开连接时出错: {disconnect_error}')

                try:
                    # 尝试关闭套接字
                    self.socket.close()
                except Exception as close_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 关闭套接字时出错: {close_error}')

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已断开与MT4服务器的连接')
            except Exception as error:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 断开MT4服务器连接时出错: {error}')
            finally:
                # 无论如何，都确保清理资源
                self.socket = None
                self.is_connected = False
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已清理连接资源')

    def _fix_mt4_error_messages(self, response_str):
        """
        修复MT4服务器返回的错误消息格式

        Args:
            response_str (str): 原始响应字符串

        Returns:
            str: 修复后的响应字符串
        """
        import re
        now = datetime.now()

        # 处理 "message":"交易执行失败X" 格式，其中X是任何非法字符
        message_error_pattern = r'"message":"([^"]*)"'
        message_match = re.search(message_error_pattern, response_str)
        if message_match and message_match.group(1):
            error_message = message_match.group(1)

            # 检查是否包含"交易执行失败"
            if "交易执行失败" in error_message:
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到交易执行失败消息，尝试修复JSON')

                # 提取错误描述（如果有）
                error_desc = ""
                if ", 错误:" in error_message:
                    error_desc = error_message.split(", 错误:")[1].strip()

                # 替换为安全格式
                safe_message = "交易执行失败"
                if error_desc:
                    safe_message += f"，错误代码：{error_desc}"

                # 替换原始消息
                response_str = re.sub(
                    r'"message":"' + re.escape(error_message) + r'"',
                    f'"message":"{safe_message}"',
                    response_str
                )

                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修复后的消息: {safe_message}')

        # 处理删除挂单响应中的特殊格式
        if "DELETEPENDING" in response_str and "交易执行" in response_str:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到删除挂单响应，尝试修复JSON')

            # 如果响应中包含"成功"，则认为是成功响应
            if "成功" in response_str:
                response_str = '{"status":"success","message":"挂单删除成功"}'
            else:
                # 尝试提取错误信息
                error_match = re.search(r'错误:\s*([^"]+)', response_str)
                error_message = error_match.group(1) if error_match else '未知错误'
                response_str = f'{{"status":"error","message":"挂单删除失败: {error_message}"}}'

            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 修复后的响应: {response_str}')

        # 处理特殊格式 "交易执行失败},"
        if '交易执行失败},' in response_str:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到特殊错误格式 "交易执行失败}},"，尝试修复JSON')
            response_str = response_str.replace('交易执行失败},', '交易执行失败"}')

        # 处理特殊格式 "交易执行失败, },"
        if '交易执行失败, },' in response_str:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到特殊错误格式 "交易执行失败, }},"，尝试修复JSON')
            response_str = response_str.replace('交易执行失败, },', '交易执行失败"}')

        # 处理特殊格式 "交易执行失败},"
        if '交易执行失败},"' in response_str:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到特殊错误格式 "交易执行失败}},"，尝试修复JSON')
            response_str = response_str.replace('交易执行失败},"', '交易执行失败"}')

        # 处理日志中看到的特殊格式
        if '"message":"交易执行失败},"' in response_str:
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到特殊错误格式 "message":"交易执行失败}},"，尝试修复JSON')
            response_str = response_str.replace('"message":"交易执行失败},"', '"message":"交易执行失败"}')

        # 处理截断的JSON响应
        if response_str.endswith(','):
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到截断的JSON响应，尝试修复')
            response_str = response_str.rstrip(',') + '}'

        # 处理未知action的截断响应
        if '未知的action:' in response_str and not response_str.endswith('}'):
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 检测到未知action的截断响应，尝试修复')
            # 查找最后一个完整的引号位置
            last_quote = response_str.rfind('"')
            if last_quote > 0:
                response_str = response_str[:last_quote+1] + '}'

        return response_str

    def send_request(self, request):
        """
        发送请求到MT4服务器

        Args:
            request (dict): 请求数据

        Returns:
            dict: 响应数据
        """
        # 检查是否跳过MT4连接
        if should_skip_mt4_connection():
            # 模拟响应
            action = request.get('action', 'unknown')
            if action == 'ping':
                return {'status': 'success', 'message': '模拟ping成功'}
            elif action in ['get_market_info', 'MARKET_INFO']:
                symbol = request.get('symbol', 'EURUSD')
                return {
                    'status': 'success',
                    'data': {
                        'symbol': symbol,
                        'bid': 1.0850,
                        'ask': 1.0852,
                        'spread': 2
                    }
                }
            elif action in ['get_positions', 'get_active_orders', 'ACTIVE_ORDERS']:
                return {'status': 'success', 'orders': []}
            elif action in ['get_pending_orders', 'PENDING_ORDERS']:
                return {'status': 'success', 'orders': []}
            elif action in ['BUY', 'SELL']:
                return {'status': 'success', 'message': f'模拟{action}成功', 'order_id': '123456'}
            else:
                return {'status': 'success', 'message': f'模拟{action}成功'}

        if not self.is_connected:
            if not self.connect():
                return {'status': 'error', 'message': 'MT4服务器连接失败'}

        try:
            request_str = json.dumps(request)
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 发送请求到MT4服务器: {request_str}')

            self.socket.send_string(request_str)
            response_str = self.socket.recv_string()

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 收到MT4服务器响应，长度: {len(response_str)}字节')

            # 尝试修复JSON格式问题
            response_str = self._fix_mt4_error_messages(response_str)

            # 移除控制字符
            response_str = ''.join(char for char in response_str if ord(char) >= 32 or char in '\n\r\t')

            return json.loads(response_str)
        except json.JSONDecodeError as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] JSON解析失败: {e}')
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 原始响应: {response_str}')

            # 尝试修复截断的JSON
            try:
                fixed_response = self._fix_truncated_json(response_str)
                if fixed_response != response_str:
                    print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 尝试修复后的响应: {fixed_response}')
                    return json.loads(fixed_response)
            except:
                pass

            return {'status': 'error', 'message': f'JSON解析失败: {str(e)}'}
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 发送请求失败: {e}')
            return {'status': 'error', 'message': str(e)}

    def _fix_truncated_json(self, response_str):
        """
        修复截断的JSON响应

        Args:
            response_str (str): 原始响应字符串

        Returns:
            str: 修复后的响应字符串
        """
        import re

        # 如果响应以逗号结尾，说明被截断了
        if response_str.endswith(','):
            response_str = response_str.rstrip(',') + '}'

        # 如果响应包含"交易执行失败"但没有正确结束
        if '交易执行失败' in response_str and not response_str.endswith('}'):
            # 查找message字段的开始位置
            message_start = response_str.find('"message":"')
            if message_start != -1:
                # 构造完整的错误响应
                response_str = '{"status":"error","message":"交易执行失败"}'

        # 如果响应包含"交易执行成功"但没有正确结束
        if '交易执行成功' in response_str and not response_str.endswith('}'):
            # 检查是否有order_id字段但值缺失
            if '"order_id":' in response_str:
                # 尝试提取已有的订单ID
                import re

                # 查找order_id的值
                order_id_match = re.search(r'"order_id"\s*:\s*"?([^",}]*)"?', response_str)
                if order_id_match:
                    order_id = order_id_match.group(1).strip()
                    if order_id and order_id != '':
                        # 有部分订单ID，使用它
                        response_str = f'{{"status":"success","message":"交易执行成功","order_id":"{order_id}"}}'
                    else:
                        # 没有订单ID，使用UNKNOWN
                        response_str = '{"status":"success","message":"交易执行成功","order_id":"UNKNOWN"}'
                else:
                    # 找不到订单ID，使用UNKNOWN
                    response_str = '{"status":"success","message":"交易执行成功","order_id":"UNKNOWN"}'

        # 如果响应包含"订单关闭成功"但没有正确结束
        if '订单关闭成功' in response_str and not response_str.endswith('}'):
            # 构造完整的成功响应
            response_str = '{"status":"success","message":"订单关闭成功"}'

        # 如果响应包含"未知的action"但没有正确结束
        if '未知的action:' in response_str and not response_str.endswith('}'):
            # 查找最后一个引号位置
            last_quote = response_str.rfind('"')
            if last_quote > 0:
                response_str = response_str[:last_quote+1] + '}'

        # 检查JSON是否有未闭合的引号
        quote_count = response_str.count('"')
        if quote_count % 2 != 0:
            # 引号数量不匹配，尝试补全
            response_str += '"}'

        # 检查括号是否匹配
        open_braces = response_str.count('{')
        close_braces = response_str.count('}')
        if open_braces > close_braces:
            response_str += '}' * (open_braces - close_braces)

        return response_str

    def get_market_info(self, symbol):
        """
        获取市场信息

        Args:
            symbol (str): 货币对符号

        Returns:
            dict: 市场信息
        """
        try:
            # 从MT4服务器获取数据
            response = self.send_request({
                'action': 'MARKET_INFO',
                'symbol': symbol,
                'requestId': str(uuid.uuid4())
            })

            if not response:
                raise Exception('未收到MT4服务器响应')

            if response.get('status') != 'success':
                raise Exception(f'获取市场信息失败: {response.get("message")}')

            return response
        except Exception as error:
            print(f'获取市场信息出错: {error}')
            # 返回模拟市场数据
            market_data = {
                'EURUSD': {'bid': 1.0850, 'ask': 1.0852, 'spread': 2},
                'GBPUSD': {'bid': 1.2650, 'ask': 1.2653, 'spread': 3},
                'AUDUSD': {'bid': 0.6750, 'ask': 0.6753, 'spread': 3},
                'NZDUSD': {'bid': 0.6150, 'ask': 0.6153, 'spread': 3},
                'USDCHF': {'bid': 0.8950, 'ask': 0.8953, 'spread': 3},
                'USDCAD': {'bid': 1.3450, 'ask': 1.3453, 'spread': 3},
                'USDJPY': {'bid': 149.50, 'ask': 149.53, 'spread': 3}
            }

            if symbol in market_data:
                return {
                    'status': 'success',
                    'data': market_data[symbol]
                }
            else:
                return {'status': 'error', 'message': f'不支持的货币对: {symbol}'}

    def get_positions(self):
        """
        获取当前持仓

        Returns:
            dict: 持仓信息
        """
        request = {
            'action': 'get_positions',
            'requestId': str(uuid.uuid4())
        }
        return self.send_request(request)

    def place_order(self, symbol, order_type, volume, price=None, stop_loss=None, take_profit=None):
        """
        下单

        Args:
            symbol (str): 货币对符号
            order_type (str): 订单类型 (BUY, SELL)
            volume (float): 交易量
            price (float, optional): 价格
            stop_loss (float, optional): 止损价格
            take_profit (float, optional): 止盈价格

        Returns:
            dict: 下单结果
        """
        request = {
            'action': 'place_order',
            'symbol': symbol,
            'type': order_type,
            'volume': volume,
            'requestId': str(uuid.uuid4())
        }

        if price is not None:
            request['price'] = price
        if stop_loss is not None:
            request['stop_loss'] = stop_loss
        if take_profit is not None:
            request['take_profit'] = take_profit

        return self.send_request(request)

    def close_position(self, order_id):
        """
        平仓

        Args:
            order_id (str): 订单ID

        Returns:
            dict: 平仓结果
        """
        request = {
            'action': 'close_position',
            'order_id': order_id,
            'requestId': str(uuid.uuid4())
        }
        return self.send_request(request)

    def get_active_orders(self):
        """
        获取活跃订单

        Returns:
            dict: 活跃订单信息
        """
        # 检查是否跳过MT4连接
        if should_skip_mt4_connection():
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ⚠️  MT4服务器跳过模式：模拟获取活跃订单')
            return {'status': 'success', 'orders': [], 'message': '模拟获取活跃订单成功'}

        try:
            # 设置更长的超时时间，专门用于获取活跃订单
            original_timeout = self.request_timeout
            if self.socket:
                self.socket.setsockopt(zmq.RCVTIMEO, 60000)  # 60秒超时
            else:
                # 如果socket为None，说明在跳过模式下，直接返回错误
                return {
                    'status': 'error',
                    'message': 'MT4跳过模式下无法获取活跃订单',
                    'orders': []
                }

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 开始获取活跃订单，超时时间设置为60秒...')

            # 从MT4服务器获取数据
            request = {
                'action': 'ACTIVE_ORDERS',
                'requestId': str(uuid.uuid4())
            }

            response = self.send_request(request)

            # 恢复原来的超时时间
            if self.socket:
                self.socket.setsockopt(zmq.RCVTIMEO, original_timeout)

            if not response:
                raise Exception('未收到MT4服务器响应')

            if response.get('status') != 'success':
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取活跃订单失败: {response.get("message", "未知错误")}')
                return {
                    'status': 'error',
                    'message': response.get('message', '获取活跃订单失败'),
                    'orders': []
                }

            # 获取订单列表
            orders = response.get('orders', [])

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 成功获取 {len(orders)} 个活跃订单')

            return {
                'status': 'success',
                'orders': orders
            }

        except Exception as error:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取活跃订单出错: {error}')
            return {
                'status': 'error',
                'message': f'获取活跃订单出错: {str(error)}',
                'orders': []
            }

    def get_pending_orders(self):
        """
        获取挂单

        Returns:
            dict: 挂单列表
        """
        # 检查是否跳过MT4连接
        if should_skip_mt4_connection():
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ⚠️  MT4服务器跳过模式：模拟获取挂单')
            return {'status': 'success', 'orders': [], 'message': '模拟获取挂单成功'}

        try:
            # 设置更长的超时时间，专门用于获取挂单
            original_timeout = self.request_timeout
            if self.socket:
                self.socket.setsockopt(zmq.RCVTIMEO, 60000)  # 60秒超时

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 开始获取挂单，超时时间设置为60秒...')

            # 从MT4服务器获取数据
            request = {
                'action': 'PENDING_ORDERS',
                'requestId': str(uuid.uuid4())
            }

            response = self.send_request(request)

            # 恢复原来的超时时间
            if self.socket:
                self.socket.setsockopt(zmq.RCVTIMEO, original_timeout)

            if not response:
                raise Exception('未收到MT4服务器响应')

            if response.get('status') != 'success':
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取挂单失败: {response.get("message", "未知错误")}')
                return {
                    'status': 'error',
                    'message': response.get('message', '获取挂单失败'),
                    'orders': []
                }

            # 获取订单列表
            orders = response.get('orders', [])

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 成功获取 {len(orders)} 个挂单')

            return {
                'status': 'success',
                'orders': orders
            }

        except Exception as error:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取挂单出错: {error}')
            return {
                'status': 'error',
                'message': f'获取挂单出错: {str(error)}',
                'orders': []
            }

    def get_all_orders(self):
        """
        获取所有订单，包括活跃订单和挂单

        Returns:
            dict: 所有订单列表
        """
        try:
            # 获取活跃订单
            active_orders_response = self.get_active_orders()
            active_orders = active_orders_response.get('orders', [])

            # 获取挂单
            pending_orders_response = self.get_pending_orders()
            pending_orders = pending_orders_response.get('orders', [])

            # 合并订单
            all_orders = active_orders + pending_orders

            return {
                'status': 'success',
                'active_orders': active_orders,
                'pending_orders': pending_orders,
                'all_orders': all_orders
            }
        except Exception as error:
            print(f'获取所有订单出错: {error}')
            return {
                'status': 'error',
                'message': f'获取所有订单出错: {str(error)}',
                'active_orders': [],
                'pending_orders': [],
                'all_orders': []
            }

    def buy(self, symbol, lot, sl=0, tp=0, comment='', max_retries=3):
        """
        执行市价买入，带重试机制和订单ID提取

        Args:
            symbol (str): 货币对符号
            lot (float): 交易手数
            sl (float, optional): 止损价格
            tp (float, optional): 止盈价格
            comment (str, optional): 注释
            max_retries (int, optional): 最大重试次数

        Returns:
            dict: 交易结果，包含订单ID
        """
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行买入操作: {symbol}, 手数: {lot}, 止损: {sl}, 止盈: {tp}')

        # 添加重试机制
        for attempt in range(max_retries):
            try:
                now = datetime.now()
                if attempt > 0:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 买入操作重试 #{attempt+1}/{max_retries}')

                # 从MT4服务器执行交易
                response = self.send_request({
                    'action': 'BUY',
                    'symbol': symbol,
                    'lot': str(lot),
                    'sl': str(sl),
                    'tp': str(tp),
                    'comment': comment
                })

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 买入操作响应: {response}')

                if not response:
                    raise Exception('未收到MT4服务器响应')

                if response.get('status') != 'success':
                    error_msg = response.get('message', '未知错误')
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行买入操作失败: {error_msg}')

                    # 检查是否需要重试
                    if attempt < max_retries - 1:
                        wait_time = self.retry_delay * (attempt + 1)  # 指数退避
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                        time.sleep(wait_time)
                        continue
                    else:
                        raise Exception(f'执行买入操作失败: {error_msg}')

                # 提取订单ID
                order_id = response.get('order_id', '')

                # 如果订单ID是UNKNOWN，尝试重新获取活跃订单来找到真实ID
                if order_id == 'UNKNOWN' or not order_id:
                    import time
                    time.sleep(1)  # 等待订单创建完成

                    # 获取最新的活跃订单
                    try:
                        active_orders = self.get_active_orders()
                        if active_orders.get('status') == 'success':
                            orders = active_orders.get('orders', [])
                            # 查找最新的订单（通常是最后一个）
                            if orders:
                                latest_order = orders[-1]  # 最后一个订单
                                if isinstance(latest_order, dict) and 'order_id' in latest_order:
                                    order_id = str(latest_order['order_id'])
                                    now = datetime.now()
                                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 从活跃订单中获取到订单ID: {order_id}')
                                elif isinstance(latest_order, str):
                                    order_id = latest_order
                                    now = datetime.now()
                                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 从活跃订单中获取到订单ID: {order_id}')
                    except Exception as e:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 获取活跃订单失败: {e}')

                # 如果响应中没有订单ID，尝试从消息中提取
                if not order_id and 'message' in response:
                    import re
                    # 尝试从消息中提取订单ID
                    id_match = re.search(r'订单号[：:]\s*(\d+)', response['message'])
                    if id_match:
                        order_id = id_match.group(1)

                # 如果成功提取到订单ID，添加到响应中
                if order_id and order_id != 'UNKNOWN':
                    response['order_id'] = order_id
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 买入操作成功，订单ID: {order_id}')
                else:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 买入操作成功，但未能获取订单ID')

                return response

            except Exception as error:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行买入操作出错: {error}')

                # 检查是否需要重试
                if attempt < max_retries - 1:
                    wait_time = self.retry_delay * (attempt + 1)  # 指数退避
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                    time.sleep(wait_time)
                else:
                    raise

    def sell(self, symbol, lot, sl=0, tp=0, comment='', max_retries=3):
        """
        执行市价卖出，带重试机制和订单ID提取

        Args:
            symbol (str): 货币对符号
            lot (float): 交易手数
            sl (float, optional): 止损价格
            tp (float, optional): 止盈价格
            comment (str, optional): 注释
            max_retries (int, optional): 最大重试次数

        Returns:
            dict: 交易结果，包含订单ID
        """
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行卖出操作: {symbol}, 手数: {lot}, 止损: {sl}, 止盈: {tp}')

        # 添加重试机制
        for attempt in range(max_retries):
            try:
                now = datetime.now()
                if attempt > 0:
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 卖出操作重试 #{attempt+1}/{max_retries}')

                # 从MT4服务器执行交易
                response = self.send_request({
                    'action': 'SELL',
                    'symbol': symbol,
                    'lot': str(lot),
                    'sl': str(sl),
                    'tp': str(tp),
                    'comment': comment
                })

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 卖出操作响应: {response}')

                if not response:
                    raise Exception('未收到MT4服务器响应')

                if response.get('status') != 'success':
                    error_msg = response.get('message', '未知错误')
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行卖出操作失败: {error_msg}')

                    # 检查是否需要重试
                    if attempt < max_retries - 1:
                        wait_time = self.retry_delay * (attempt + 1)  # 指数退避
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                        time.sleep(wait_time)
                        continue
                    else:
                        raise Exception(f'执行卖出操作失败: {error_msg}')

                # 提取订单ID
                order_id = response.get('order_id', '')

                # 如果响应中没有订单ID，尝试从消息中提取
                if not order_id and 'message' in response:
                    import re
                    # 尝试从消息中提取订单ID
                    id_match = re.search(r'订单号[：:]\s*(\d+)', response['message'])
                    if id_match:
                        order_id = id_match.group(1)

                # 如果成功提取到订单ID，添加到响应中
                if order_id:
                    response['order_id'] = order_id
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 卖出操作成功，订单ID: {order_id}')
                else:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 卖出操作成功，但未能获取订单ID')

                return response

            except Exception as error:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 执行卖出操作出错: {error}')

                # 检查是否需要重试
                if attempt < max_retries - 1:
                    wait_time = self.retry_delay * (attempt + 1)  # 指数退避
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                    time.sleep(wait_time)
                else:
                    raise

    def modify_order(self, order_id, sl, tp):
        """
        修改订单

        Args:
            order_id (int): 订单ID
            sl (float): 新的止损价格
            tp (float): 新的止盈价格

        Returns:
            dict: 修改结果
        """
        return self.send_request({
            'action': 'MODIFY',
            'order_id': str(order_id),
            'sl': str(sl),
            'tp': str(tp)
        })

    def close_order(self, order_id, lots=0):
        """
        关闭订单

        Args:
            order_id (int): 订单ID
            lots (float, optional): 平仓手数，如果为0则全部平仓

        Returns:
            dict: 平仓结果
        """
        request = {
            'action': 'CLOSE',
            'order_id': str(order_id)
        }

        if lots > 0:
            request['lots'] = str(lots)

        return self.send_request(request)

    def delete_order(self, order_id, max_retries=3):
        """
        删除挂单

        Args:
            order_id (int): 订单ID
            max_retries (int, optional): 最大重试次数

        Returns:
            dict: 删除结果
        """
        # 使用DELETEPENDING操作删除挂单
        # 这个操作使用MT4的OrderDelete函数删除挂单

        # 添加重试机制
        for attempt in range(max_retries):
            try:
                print(f'尝试删除挂单 {order_id}，第 {attempt + 1} 次尝试')

                response = self.send_request({
                    'action': 'DELETEPENDING',
                    'order_id': str(order_id)
                })

                # 检查响应
                if response.get('status') == 'success':
                    print(f'成功删除挂单 {order_id}')
                    return response

                # 如果失败，记录错误并重试
                print(f'删除挂单 {order_id} 失败: {response.get("message")}，将重试')
                time.sleep(1)  # 等待1秒后重试
            except Exception as error:
                print(f'删除挂单 {order_id} 时出错: {error}，将重试')
                time.sleep(1)  # 等待1秒后重试

        # 所有重试都失败
        print(f'删除挂单 {order_id} 失败，已达到最大重试次数 {max_retries}')
        return {
            'status': 'error',
            'message': f'删除挂单失败，已达到最大重试次数 {max_retries}'
        }

    def get_account_info(self):
        """
        获取账户信息

        Returns:
            dict: 账户信息
        """
        try:
            # 从MT4服务器获取数据
            response = self.send_request({
                'action': 'ACCOUNT_INFO',
                'requestId': str(uuid.uuid4())
            })

            if not response:
                raise Exception('未收到MT4服务器响应')

            if response.get('status') != 'success':
                raise Exception(f'获取账户信息失败: {response.get("message")}')

            return response
        except Exception as error:
            print(f'获取账户信息出错: {error}')
            # 返回模拟账户信息
            return {
                'status': 'success',
                'data': {
                    'account': '********',
                    'balance': 10000.0,
                    'equity': 10000.0,
                    'margin': 0.0,
                    'free_margin': 10000.0,
                    'margin_level': 0.0
                }
            }

    def get_order_history(self, order_id: str = None, days: int = 7):
        """
        获取订单历史

        Args:
            order_id (str, optional): 特定订单ID
            days (int): 查询天数

        Returns:
            dict: 订单历史
        """
        try:
            request = {
                'action': 'ORDER_HISTORY',
                'days': days,
                'requestId': str(uuid.uuid4())
            }

            if order_id:
                request['order_id'] = str(order_id)

            response = self.send_request(request)

            if not response:
                raise Exception('未收到MT4服务器响应')

            if response.get('status') != 'success':
                raise Exception(f'获取订单历史失败: {response.get("message")}')

            return response
        except Exception as error:
            print(f'获取订单历史出错: {error}')
            return {
                'status': 'success',
                'orders': []
            }


# 创建全局MT4客户端实例
mt4_client = MT4Client()
