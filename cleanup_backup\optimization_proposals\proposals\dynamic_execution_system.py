#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态交易执行系统
目标：优化交易执行，提高胜率和收益率
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class RiskLevel(Enum):
    """风险等级"""
    VERY_LOW = "极低风险"
    LOW = "低风险"
    MEDIUM = "中等风险"
    HIGH = "高风险"
    VERY_HIGH = "极高风险"

@dataclass
class TradingEnvironment:
    """交易环境"""
    market_regime: str
    volatility_level: str
    liquidity_score: float
    spread: float
    time_of_day: str
    economic_events: List[Dict]

@dataclass
class PositionSizing:
    """仓位配置"""
    base_size: float
    risk_adjusted_size: float
    max_size: float
    confidence_multiplier: float
    final_size: float

class DynamicPositionManager:
    """动态仓位管理器"""
    
    def __init__(self):
        self.base_position_sizes = {
            RiskLevel.VERY_LOW: 0.15,
            RiskLevel.LOW: 0.12,
            RiskLevel.MEDIUM: 0.08,
            RiskLevel.HIGH: 0.05,
            RiskLevel.VERY_HIGH: 0.02
        }
        
        self.market_regime_multipliers = {
            'TRENDING': 1.2,
            'RANGING': 0.8,
            'BREAKOUT': 0.7,
            'REVERSAL': 0.6,
            'UNCERTAIN': 0.4
        }
        
        self.volatility_multipliers = {
            'LOW': 1.3,
            'NORMAL': 1.0,
            'HIGH': 0.7
        }
    
    def calculate_optimal_position_size(self, 
                                      signal_confidence: float,
                                      risk_level: RiskLevel,
                                      trading_env: TradingEnvironment,
                                      account_risk: float = 0.02) -> PositionSizing:
        """计算最优仓位大小"""
        
        # 1. 基础仓位
        base_size = self.base_position_sizes[risk_level]
        
        # 2. 市场状态调整
        regime_multiplier = self.market_regime_multipliers.get(trading_env.market_regime, 0.8)
        volatility_multiplier = self.volatility_multipliers.get(trading_env.volatility_level, 1.0)
        
        # 3. 流动性调整
        liquidity_multiplier = min(trading_env.liquidity_score, 1.0)
        
        # 4. 点差调整
        spread_multiplier = max(0.5, 1 - (trading_env.spread - 2) * 0.1)
        
        # 5. 时间段调整
        time_multiplier = self._get_time_multiplier(trading_env.time_of_day)
        
        # 6. 经济事件调整
        event_multiplier = self._get_event_multiplier(trading_env.economic_events)
        
        # 7. 信号置信度调整
        confidence_multiplier = 0.5 + (signal_confidence * 0.5)
        
        # 8. 计算风险调整后仓位
        risk_adjusted_size = (base_size * 
                            regime_multiplier * 
                            volatility_multiplier * 
                            liquidity_multiplier * 
                            spread_multiplier * 
                            time_multiplier * 
                            event_multiplier)
        
        # 9. 应用置信度
        final_size = risk_adjusted_size * confidence_multiplier
        
        # 10. 限制最大仓位
        max_size = min(0.2, base_size * 1.5)
        final_size = min(final_size, max_size)
        
        # 11. 确保最小仓位
        final_size = max(final_size, 0.01)
        
        return PositionSizing(
            base_size=base_size,
            risk_adjusted_size=risk_adjusted_size,
            max_size=max_size,
            confidence_multiplier=confidence_multiplier,
            final_size=final_size
        )
    
    def _get_time_multiplier(self, time_of_day: str) -> float:
        """获取时间段乘数"""
        multipliers = {
            'ASIAN': 0.8,
            'EARLY_EUROPEAN': 0.9,
            'EUROPEAN': 1.1,
            'US': 1.2,
            'LATE_US': 0.9
        }
        return multipliers.get(time_of_day, 1.0)
    
    def _get_event_multiplier(self, economic_events: List[Dict]) -> float:
        """获取经济事件乘数"""
        if not economic_events:
            return 1.0
        
        # 检查未来2小时内的高影响事件
        now = datetime.now()
        high_impact_events = []
        
        for event in economic_events:
            event_time = datetime.fromisoformat(event.get('time', ''))
            time_diff = (event_time - now).total_seconds() / 3600
            
            if 0 <= time_diff <= 2 and event.get('impact', '') == 'HIGH':
                high_impact_events.append(event)
        
        if high_impact_events:
            return 0.5  # 重要事件前减少仓位
        
        return 1.0

class AdaptiveStopLossManager:
    """自适应止损管理器"""
    
    def __init__(self):
        self.atr_multipliers = {
            'TRENDING': 1.5,
            'RANGING': 1.0,
            'BREAKOUT': 2.0,
            'REVERSAL': 1.2,
            'UNCERTAIN': 0.8
        }
    
    def calculate_dynamic_stop_loss(self,
                                  entry_price: float,
                                  direction: str,
                                  market_data: Dict,
                                  trading_env: TradingEnvironment) -> Dict:
        """计算动态止损"""
        
        # 1. 基于ATR的止损
        atr_stop = self._calculate_atr_stop(entry_price, direction, market_data, trading_env)
        
        # 2. 基于技术位的止损
        technical_stop = self._calculate_technical_stop(entry_price, direction, market_data)
        
        # 3. 基于波动率的止损
        volatility_stop = self._calculate_volatility_stop(entry_price, direction, market_data)
        
        # 4. 选择最优止损
        optimal_stop = self._select_optimal_stop(
            entry_price, direction, atr_stop, technical_stop, volatility_stop
        )
        
        # 5. 计算风险金额
        risk_pips = abs(entry_price - optimal_stop) * 10000
        
        return {
            'stop_loss': optimal_stop,
            'risk_pips': risk_pips,
            'stop_type': 'DYNAMIC',
            'atr_stop': atr_stop,
            'technical_stop': technical_stop,
            'volatility_stop': volatility_stop,
            'reasoning': self._generate_stop_reasoning(optimal_stop, atr_stop, technical_stop, volatility_stop)
        }
    
    def _calculate_atr_stop(self, entry_price: float, direction: str, 
                           market_data: Dict, trading_env: TradingEnvironment) -> float:
        """基于ATR计算止损"""
        # 简化ATR计算
        volatility_regime = trading_env.volatility_level
        atr_multiplier = self.atr_multipliers.get(trading_env.market_regime, 1.0)
        
        # 估算ATR（实际应从历史数据计算）
        estimated_atr = 0.0015  # 15点的ATR估算
        
        if volatility_regime == 'HIGH':
            estimated_atr *= 1.5
        elif volatility_regime == 'LOW':
            estimated_atr *= 0.7
        
        stop_distance = estimated_atr * atr_multiplier
        
        if direction == 'BUY':
            return entry_price - stop_distance
        else:
            return entry_price + stop_distance
    
    def _calculate_technical_stop(self, entry_price: float, direction: str, market_data: Dict) -> float:
        """基于技术位计算止损"""
        ma13_15m = market_data.get('ma13_15min', {}).get('value', entry_price)
        ma13_1h = market_data.get('ma13_1h', {}).get('value', entry_price)
        
        # 使用更远的均线作为技术止损
        if direction == 'BUY':
            technical_stop = min(ma13_15m, ma13_1h) - 0.0005  # 均线下方5点
        else:
            technical_stop = max(ma13_15m, ma13_1h) + 0.0005  # 均线上方5点
        
        return technical_stop
    
    def _calculate_volatility_stop(self, entry_price: float, direction: str, market_data: Dict) -> float:
        """基于波动率计算止损"""
        price_change_percent = abs(market_data.get('price_change_percent', 0))
        
        # 基于近期波动率调整止损距离
        if price_change_percent > 0.3:
            stop_distance = 0.002  # 20点
        elif price_change_percent > 0.15:
            stop_distance = 0.0015  # 15点
        else:
            stop_distance = 0.001  # 10点
        
        if direction == 'BUY':
            return entry_price - stop_distance
        else:
            return entry_price + stop_distance
    
    def _select_optimal_stop(self, entry_price: float, direction: str,
                           atr_stop: float, technical_stop: float, volatility_stop: float) -> float:
        """选择最优止损"""
        stops = [atr_stop, technical_stop, volatility_stop]
        
        if direction == 'BUY':
            # 买入时选择最高的止损（最保守）
            return max(stops)
        else:
            # 卖出时选择最低的止损（最保守）
            return min(stops)
    
    def _generate_stop_reasoning(self, optimal_stop: float, atr_stop: float,
                               technical_stop: float, volatility_stop: float) -> str:
        """生成止损推理"""
        if optimal_stop == atr_stop:
            return "基于ATR的动态止损"
        elif optimal_stop == technical_stop:
            return "基于技术位的止损"
        elif optimal_stop == volatility_stop:
            return "基于波动率的止损"
        else:
            return "综合考虑的最优止损"

class IntelligentTakeProfitManager:
    """智能止盈管理器"""
    
    def calculate_dynamic_take_profit(self,
                                    entry_price: float,
                                    stop_loss: float,
                                    direction: str,
                                    market_data: Dict,
                                    trading_env: TradingEnvironment) -> Dict:
        """计算动态止盈"""
        
        # 1. 计算风险距离
        risk_distance = abs(entry_price - stop_loss)
        
        # 2. 基于市场状态确定风险回报比
        risk_reward_ratio = self._get_optimal_risk_reward_ratio(trading_env)
        
        # 3. 计算基础止盈
        base_profit_distance = risk_distance * risk_reward_ratio
        
        # 4. 技术位调整
        technical_targets = self._identify_technical_targets(entry_price, direction, market_data)
        
        # 5. 选择最优止盈
        optimal_tp = self._select_optimal_take_profit(
            entry_price, direction, base_profit_distance, technical_targets
        )
        
        # 6. 分批止盈策略
        partial_targets = self._calculate_partial_targets(entry_price, optimal_tp, direction)
        
        return {
            'take_profit': optimal_tp,
            'risk_reward_ratio': risk_reward_ratio,
            'profit_pips': abs(optimal_tp - entry_price) * 10000,
            'partial_targets': partial_targets,
            'technical_targets': technical_targets,
            'strategy': 'DYNAMIC_SCALING'
        }
    
    def _get_optimal_risk_reward_ratio(self, trading_env: TradingEnvironment) -> float:
        """获取最优风险回报比"""
        base_ratios = {
            'TRENDING': 2.0,
            'RANGING': 1.5,
            'BREAKOUT': 2.5,
            'REVERSAL': 1.8,
            'UNCERTAIN': 1.2
        }
        
        base_ratio = base_ratios.get(trading_env.market_regime, 1.5)
        
        # 根据波动率调整
        if trading_env.volatility_level == 'HIGH':
            return base_ratio * 1.2
        elif trading_env.volatility_level == 'LOW':
            return base_ratio * 0.8
        
        return base_ratio
    
    def _identify_technical_targets(self, entry_price: float, direction: str, market_data: Dict) -> List[float]:
        """识别技术目标位"""
        targets = []
        
        # 基于支撑阻力位
        support_resistance = market_data.get('support_resistance', {})
        
        if direction == 'BUY':
            resistance_levels = support_resistance.get('resistance_levels', [])
            targets.extend([level for level in resistance_levels if level > entry_price])
        else:
            support_levels = support_resistance.get('support_levels', [])
            targets.extend([level for level in support_levels if level < entry_price])
        
        # 基于斐波那契回撤
        fib_targets = self._calculate_fibonacci_targets(entry_price, direction, market_data)
        targets.extend(fib_targets)
        
        return sorted(targets)
    
    def _calculate_fibonacci_targets(self, entry_price: float, direction: str, market_data: Dict) -> List[float]:
        """计算斐波那契目标"""
        # 简化的斐波那契计算
        recent_high = market_data.get('recent_high', entry_price * 1.01)
        recent_low = market_data.get('recent_low', entry_price * 0.99)
        
        fib_levels = [0.382, 0.618, 1.0, 1.618]
        targets = []
        
        if direction == 'BUY':
            range_size = recent_high - recent_low
            for level in fib_levels:
                target = entry_price + (range_size * level)
                targets.append(target)
        else:
            range_size = recent_high - recent_low
            for level in fib_levels:
                target = entry_price - (range_size * level)
                targets.append(target)
        
        return targets
    
    def _select_optimal_take_profit(self, entry_price: float, direction: str,
                                  base_profit_distance: float, technical_targets: List[float]) -> float:
        """选择最优止盈"""
        if direction == 'BUY':
            base_tp = entry_price + base_profit_distance
        else:
            base_tp = entry_price - base_profit_distance
        
        if not technical_targets:
            return base_tp
        
        # 寻找最接近基础止盈的技术目标
        if direction == 'BUY':
            suitable_targets = [t for t in technical_targets if t >= base_tp * 0.8]
        else:
            suitable_targets = [t for t in technical_targets if t <= base_tp * 1.2]
        
        if suitable_targets:
            return suitable_targets[0]
        
        return base_tp
    
    def _calculate_partial_targets(self, entry_price: float, final_tp: float, direction: str) -> List[Dict]:
        """计算分批止盈目标"""
        total_distance = abs(final_tp - entry_price)
        
        # 分3批止盈：50%, 30%, 20%
        partial_targets = []
        
        if direction == 'BUY':
            target_50 = entry_price + (total_distance * 0.5)
            target_80 = entry_price + (total_distance * 0.8)
        else:
            target_50 = entry_price - (total_distance * 0.5)
            target_80 = entry_price - (total_distance * 0.8)
        
        partial_targets.append({
            'target': target_50,
            'percentage': 50,
            'description': '第一目标：50%仓位获利'
        })
        
        partial_targets.append({
            'target': target_80,
            'percentage': 30,
            'description': '第二目标：30%仓位获利'
        })
        
        partial_targets.append({
            'target': final_tp,
            'percentage': 20,
            'description': '最终目标：剩余仓位获利'
        })
        
        return partial_targets

class TradeExecutionOptimizer:
    """交易执行优化器"""
    
    def __init__(self):
        self.position_manager = DynamicPositionManager()
        self.stop_manager = AdaptiveStopLossManager()
        self.tp_manager = IntelligentTakeProfitManager()
    
    def optimize_trade_execution(self,
                                signal: Dict,
                                market_data: Dict,
                                trading_env: TradingEnvironment,
                                account_info: Dict) -> Dict:
        """优化交易执行"""
        
        # 1. 评估信号质量
        signal_quality = self._evaluate_signal_quality(signal, market_data)
        
        # 2. 确定风险等级
        risk_level = self._determine_risk_level(signal_quality, trading_env)
        
        # 3. 计算最优仓位
        position_sizing = self.position_manager.calculate_optimal_position_size(
            signal_quality['confidence'], risk_level, trading_env
        )
        
        # 4. 优化入场价格
        optimized_entry = self._optimize_entry_price(signal, market_data, trading_env)
        
        # 5. 计算动态止损
        stop_loss_info = self.stop_manager.calculate_dynamic_stop_loss(
            optimized_entry['price'], signal['action'], market_data, trading_env
        )
        
        # 6. 计算智能止盈
        take_profit_info = self.tp_manager.calculate_dynamic_take_profit(
            optimized_entry['price'], stop_loss_info['stop_loss'],
            signal['action'], market_data, trading_env
        )
        
        # 7. 生成最终交易指令
        optimized_trade = {
            'action': signal['action'],
            'orderType': optimized_entry['order_type'],
            'entryPrice': optimized_entry['price'],
            'stopLoss': stop_loss_info['stop_loss'],
            'takeProfit': take_profit_info['take_profit'],
            'lotSize': position_sizing.final_size,
            'riskLevel': risk_level.value,
            'reasoning': self._generate_execution_reasoning(
                signal_quality, position_sizing, stop_loss_info, take_profit_info
            ),
            'optimization_details': {
                'signal_quality': signal_quality,
                'position_sizing': position_sizing.__dict__,
                'stop_loss_info': stop_loss_info,
                'take_profit_info': take_profit_info,
                'risk_level': risk_level.value
            }
        }
        
        return optimized_trade
    
    def _evaluate_signal_quality(self, signal: Dict, market_data: Dict) -> Dict:
        """评估信号质量"""
        # 简化的信号质量评估
        base_confidence = signal.get('signalConfidence', 'MEDIUM')
        
        confidence_scores = {
            'HIGH': 0.8,
            'MEDIUM': 0.6,
            'LOW': 0.4
        }
        
        confidence = confidence_scores.get(base_confidence, 0.6)
        
        # 基于市场状态调整
        market_regime = market_data.get('market_regime', {})
        if market_regime.get('confidence', 0) > 0.7:
            confidence *= 1.1
        
        return {
            'confidence': min(confidence, 1.0),
            'strength': signal.get('strength', 0.6),
            'consistency': signal.get('consistency', 0.7)
        }
    
    def _determine_risk_level(self, signal_quality: Dict, trading_env: TradingEnvironment) -> RiskLevel:
        """确定风险等级"""
        base_risk_score = 0.5
        
        # 信号质量影响
        base_risk_score -= (signal_quality['confidence'] - 0.5) * 0.4
        
        # 市场状态影响
        if trading_env.market_regime == 'TRENDING':
            base_risk_score -= 0.1
        elif trading_env.market_regime in ['BREAKOUT', 'REVERSAL']:
            base_risk_score += 0.2
        elif trading_env.market_regime == 'UNCERTAIN':
            base_risk_score += 0.3
        
        # 波动率影响
        if trading_env.volatility_level == 'HIGH':
            base_risk_score += 0.15
        elif trading_env.volatility_level == 'LOW':
            base_risk_score -= 0.1
        
        # 流动性影响
        if trading_env.liquidity_score < 0.7:
            base_risk_score += 0.1
        
        # 映射到风险等级
        if base_risk_score <= 0.2:
            return RiskLevel.VERY_LOW
        elif base_risk_score <= 0.4:
            return RiskLevel.LOW
        elif base_risk_score <= 0.6:
            return RiskLevel.MEDIUM
        elif base_risk_score <= 0.8:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH
    
    def _optimize_entry_price(self, signal: Dict, market_data: Dict, trading_env: TradingEnvironment) -> Dict:
        """优化入场价格"""
        current_price = market_data.get('current_price', 0)
        spread = trading_env.spread
        
        # 基于订单类型优化
        if signal.get('orderType') == 'MARKET':
            # 市价单考虑滑点
            slippage = spread * 0.5
            if signal['action'] == 'BUY':
                optimized_price = current_price + slippage / 10000
            else:
                optimized_price = current_price - slippage / 10000
            
            return {
                'price': optimized_price,
                'order_type': 'MARKET',
                'reasoning': '市价单，已考虑滑点'
            }
        
        elif signal.get('orderType') == 'LIMIT':
            # 限价单寻找更好价格
            if signal['action'] == 'BUY':
                optimized_price = current_price - (spread * 0.3) / 10000
            else:
                optimized_price = current_price + (spread * 0.3) / 10000
            
            return {
                'price': optimized_price,
                'order_type': 'LIMIT',
                'reasoning': '限价单，等待更好价格'
            }
        
        else:
            return {
                'price': current_price,
                'order_type': 'MARKET',
                'reasoning': '默认市价执行'
            }
    
    def _generate_execution_reasoning(self, signal_quality: Dict, position_sizing: PositionSizing,
                                    stop_loss_info: Dict, take_profit_info: Dict) -> str:
        """生成执行推理"""
        reasoning_parts = [
            f"信号置信度: {signal_quality['confidence']:.2f}",
            f"仓位大小: {position_sizing.final_size:.2f} (基础: {position_sizing.base_size:.2f})",
            f"风险: {stop_loss_info['risk_pips']:.1f}点",
            f"目标: {take_profit_info['profit_pips']:.1f}点",
            f"风险回报比: 1:{take_profit_info['risk_reward_ratio']:.1f}",
            stop_loss_info['reasoning']
        ]
        
        return '; '.join(reasoning_parts)
