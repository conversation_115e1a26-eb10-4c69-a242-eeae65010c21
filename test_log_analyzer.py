#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试智能日志分析器
验证是否能正确读取和分析Pro系统日志
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append('.')

def test_log_file_detection():
    """测试日志文件检测"""
    print("[TEST] 测试日志文件检测...")
    
    # 查找Pro系统的日志文件
    log_files = []
    
    # 检查当前目录
    for file in os.listdir('.'):
        if 'QuantumForex_Pro' in file and file.endswith('.log'):
            log_files.append(file)
            print(f"   发现日志文件: {file}")
    
    # 检查Pro目录下的日志
    pro_log_dir = 'QuantumForex_Pro/data/logs'
    if os.path.exists(pro_log_dir):
        for file in os.listdir(pro_log_dir):
            if file.endswith('.log'):
                log_files.append(os.path.join(pro_log_dir, file))
                print(f"   发现Pro日志文件: {file}")
    
    if not log_files:
        print("   [WARN] 未发现任何日志文件")
        
        # 创建模拟日志文件进行测试
        test_log_content = """2025-05-30 10:32:27 - INFO - 🚀 QuantumForex Pro 正在启动...
2025-05-30 10:32:28 - INFO - ✅ 技术分析引擎初始化完成
2025-05-30 10:32:29 - INFO - 🔄 初始化信号融合引擎...
2025-05-30 10:32:30 - ERROR - 连接MT4失败: 无法连接到服务器
2025-05-30 10:32:31 - WARNING - 风险过高，暂停交易
2025-05-30 10:32:32 - INFO - 📊 获取市场数据...
2025-05-30 10:32:33 - ERROR - 数据获取失败: JSON解析错误
2025-05-30 10:32:34 - INFO - ✅ 成功获取EURUSD真实数据
"""
        
        test_log_file = 'QuantumForex_Pro_test.log'
        with open(test_log_file, 'w', encoding='utf-8') as f:
            f.write(test_log_content)
        
        log_files.append(test_log_file)
        print(f"   [INFO] 创建测试日志文件: {test_log_file}")
    
    return log_files

def test_log_parsing():
    """测试日志解析"""
    print("\n[TEST] 测试日志解析...")
    
    from intelligent_log_analyzer import IntelligentLogAnalyzer
    
    # 创建分析器
    analyzer = IntelligentLogAnalyzer()
    
    # 测试日志行解析
    test_lines = [
        "2025-05-30 10:32:27 - INFO - 🚀 QuantumForex Pro 正在启动...",
        "2025-05-30 10:32:30 - ERROR - 连接MT4失败: 无法连接到服务器",
        "2025-05-30 10:32:31 - WARNING - 风险过高，暂停交易",
        "2025-05-30 10:32:33 - ERROR - 数据获取失败: JSON解析错误",
        "✅ 成功获取EURUSD真实数据",  # 无时间戳的行
        "📊 MT4服务器返回0个活跃订单"
    ]
    
    parsed_events = []
    for line in test_lines:
        event = analyzer._parse_log_line(line)
        if event:
            parsed_events.append(event)
            print(f"   解析成功: {event.level} | {event.component} | {event.message[:50]}...")
        else:
            print(f"   解析失败: {line[:50]}...")
    
    print(f"   [INFO] 成功解析 {len(parsed_events)} 个事件")
    return parsed_events

def test_issue_detection():
    """测试问题检测"""
    print("\n[TEST] 测试问题检测...")
    
    from intelligent_log_analyzer import IntelligentLogAnalyzer, LogEvent
    
    # 创建分析器
    analyzer = IntelligentLogAnalyzer()
    
    # 创建测试事件
    test_events = [
        LogEvent(
            timestamp=datetime.now(),
            level='ERROR',
            component='MT4',
            message='连接MT4失败: 无法连接到服务器',
            raw_line='2025-05-30 10:32:30 - ERROR - 连接MT4失败: 无法连接到服务器'
        ),
        LogEvent(
            timestamp=datetime.now(),
            level='ERROR',
            component='DATA',
            message='数据获取失败: JSON解析错误',
            raw_line='2025-05-30 10:32:33 - ERROR - 数据获取失败: JSON解析错误'
        ),
        LogEvent(
            timestamp=datetime.now(),
            level='WARNING',
            component='RISK',
            message='风险过高，暂停交易',
            raw_line='2025-05-30 10:32:31 - WARNING - 风险过高，暂停交易'
        )
    ]
    
    # 添加到缓冲区
    analyzer.log_buffer = test_events
    
    # 执行分析
    analyzer._analyze_logs()
    
    # 检查检测到的问题
    print(f"   [INFO] 检测到 {len(analyzer.detected_issues)} 个问题类型")
    
    for issue_type, issue in analyzer.detected_issues.items():
        print(f"   [ALERT] {issue.description}: {issue.count}次 (严重性: {issue.severity})")
        print(f"      证据: {issue.evidence[-1] if issue.evidence else 'N/A'}")
    
    return analyzer.detected_issues

def test_real_log_reading():
    """测试真实日志读取"""
    print("\n[TEST] 测试真实日志读取...")
    
    from intelligent_log_analyzer import IntelligentLogAnalyzer
    
    # 创建分析器
    analyzer = IntelligentLogAnalyzer()
    
    # 尝试读取真实日志
    new_logs = analyzer._read_new_logs()
    
    if new_logs:
        print(f"   [INFO] 成功读取 {len(new_logs)} 行日志")
        
        # 显示最近几行
        for i, line in enumerate(new_logs[-5:]):
            if line.strip():
                print(f"   [{i+1}] {line[:80]}...")
    else:
        print("   [WARN] 未读取到任何日志")
    
    return new_logs

def test_comprehensive_analysis():
    """测试综合分析"""
    print("\n[TEST] 测试综合分析...")
    
    from intelligent_log_analyzer import IntelligentLogAnalyzer
    
    # 创建分析器
    analyzer = IntelligentLogAnalyzer()
    
    # 启动短期监听测试
    print("   [INFO] 启动10秒监听测试...")
    analyzer.start_monitoring()
    
    # 等待10秒
    time.sleep(10)
    
    # 停止监听
    analyzer.stop_monitoring()
    
    # 获取状态
    status = analyzer.get_current_status()
    
    print(f"   [DATA] 监听结果:")
    print(f"      总事件: {status['total_events']}")
    print(f"      检测问题: {status['detected_issues']}")
    print(f"      最近问题: {len(status.get('recent_issues', []))}")
    
    return status

def main():
    """主函数"""
    print("[START] 智能日志分析器测试")
    print("=" * 80)
    
    test_results = []
    
    # 1. 测试日志文件检测
    log_files = test_log_file_detection()
    test_results.append(("日志文件检测", len(log_files) > 0))
    
    # 2. 测试日志解析
    parsed_events = test_log_parsing()
    test_results.append(("日志解析", len(parsed_events) > 0))
    
    # 3. 测试问题检测
    detected_issues = test_issue_detection()
    test_results.append(("问题检测", len(detected_issues) > 0))
    
    # 4. 测试真实日志读取
    real_logs = test_real_log_reading()
    test_results.append(("真实日志读取", len(real_logs) > 0))
    
    # 5. 测试综合分析
    analysis_status = test_comprehensive_analysis()
    test_results.append(("综合分析", analysis_status['is_monitoring'] == False))  # 应该已停止
    
    # 总结测试结果
    print("\n" + "=" * 80)
    print("[DATA] 测试结果总结:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "[OK] 通过" if result else "[FAIL] 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print("")
    print(f"[TARGET] 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n[OK] 所有测试通过！智能日志分析器工作正常！")
        print("\n[DATA] 功能验证:")
        print("   ✅ 能够检测日志文件")
        print("   ✅ 能够解析日志事件")
        print("   ✅ 能够识别问题模式")
        print("   ✅ 能够读取真实日志")
        print("   ✅ 能够执行综合分析")
        print("\n[TIP] 智能日志分析器已准备就绪，可以监听Pro系统！")
    else:
        print(f"\n[WARN] {total - passed} 个测试失败，需要进一步调试")
    
    # 清理测试文件
    if os.path.exists('QuantumForex_Pro_test.log'):
        os.remove('QuantumForex_Pro_test.log')
        print("\n[INFO] 清理测试文件完成")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n[TARGET] 测试结果: {'成功' if success else '失败'}")
    input("按任意键退出...")
