#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模式分析器
分析交易历史，识别成功和失败的模式
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

from .trade_result_recorder import TradeResultRecorder, TradeRecord

@dataclass
class TradingPattern:
    """交易模式"""
    pattern_id: str
    pattern_type: str  # winning/losing
    description: str
    conditions: Dict
    win_rate: float
    avg_profit: float
    sample_size: int
    confidence: float

class PatternAnalyzer:
    """模式分析器"""

    def __init__(self, trade_recorder: TradeResultRecorder):
        self.trade_recorder = trade_recorder
        self.logger = logging.getLogger(__name__)

        # 分析参数
        self.min_sample_size = 8   # 最小样本数量（降低要求）
        self.min_confidence = 0.3  # 最小置信度（降低要求，适应小样本）

    def analyze_patterns(self, days: int = 90) -> Dict[str, List[TradingPattern]]:
        """分析交易模式"""
        try:
            # 获取交易数据
            trades = self.trade_recorder.get_recent_trades(days)

            if len(trades) < self.min_sample_size:
                self.logger.warning(f"交易样本不足: {len(trades)} < {self.min_sample_size}")
                return {'winning_patterns': [], 'losing_patterns': []}

            # 转换为DataFrame
            df = self._trades_to_dataframe(trades)

            # 分析不同类型的模式
            patterns = {
                'winning_patterns': [],
                'losing_patterns': []
            }

            # 1. 基于市场条件的模式
            market_patterns = self._analyze_market_condition_patterns(df)
            patterns['winning_patterns'].extend(market_patterns['winning'])
            patterns['losing_patterns'].extend(market_patterns['losing'])

            # 2. 基于技术指标的模式
            technical_patterns = self._analyze_technical_patterns(df)
            patterns['winning_patterns'].extend(technical_patterns['winning'])
            patterns['losing_patterns'].extend(technical_patterns['losing'])

            # 3. 基于时间的模式
            time_patterns = self._analyze_time_patterns(df)
            patterns['winning_patterns'].extend(time_patterns['winning'])
            patterns['losing_patterns'].extend(time_patterns['losing'])

            # 4. 基于策略参数的模式
            strategy_patterns = self._analyze_strategy_patterns(df)
            patterns['winning_patterns'].extend(strategy_patterns['winning'])
            patterns['losing_patterns'].extend(strategy_patterns['losing'])

            self.logger.info(f"发现 {len(patterns['winning_patterns'])} 个盈利模式, {len(patterns['losing_patterns'])} 个亏损模式")

            return patterns

        except Exception as e:
            self.logger.error(f"模式分析失败: {e}")
            return {'winning_patterns': [], 'losing_patterns': []}

    def _trades_to_dataframe(self, trades: List[TradeRecord]) -> pd.DataFrame:
        """将交易记录转换为DataFrame"""
        data = []
        for trade in trades:
            if trade.exit_time is None:  # 跳过未平仓的交易
                continue

            data.append({
                'trade_id': trade.trade_id,
                'symbol': trade.symbol,
                'action': trade.action,
                'entry_time': trade.entry_time,
                'entry_hour': trade.entry_time.hour,
                'entry_weekday': trade.entry_time.weekday(),
                'entry_price': trade.entry_price,  # 添加缺失的字段
                'strategy_used': trade.strategy_used,
                'confidence': trade.confidence,
                'market_condition': trade.market_condition,
                'rsi': trade.rsi,
                'ma_20': trade.ma_20,
                'ma_50': trade.ma_50,
                'atr': trade.atr,
                'volatility': trade.volatility,
                'stop_loss': trade.stop_loss,
                'take_profit': trade.take_profit,
                'exit_reason': trade.exit_reason,
                'profit_loss': trade.profit_loss,
                'profit_loss_pct': trade.profit_loss_pct,
                'holding_duration_minutes': trade.holding_duration_minutes,
                'is_winning': trade.profit_loss > 0
            })

        return pd.DataFrame(data)

    def _calculate_pattern_confidence(self, win_rate: float, sample_size: int) -> float:
        """计算模式置信度（适应小样本）"""
        try:
            # 基础置信度：基于胜率与随机胜率(0.5)的差异
            base_confidence = abs(win_rate - 0.5) * 2  # 0-1之间

            # 样本数量调整：样本越多，置信度越高
            sample_factor = min(1.0, sample_size / 20)  # 20个样本达到最大样本因子

            # 综合置信度
            confidence = base_confidence * (0.5 + 0.5 * sample_factor)

            return min(0.95, confidence)  # 最大置信度限制为0.95

        except Exception:
            return 0.0

    def _analyze_market_condition_patterns(self, df: pd.DataFrame) -> Dict:
        """分析市场条件模式"""
        patterns = {'winning': [], 'losing': []}

        try:
            # 按市场条件分组分析
            for condition in df['market_condition'].unique():
                condition_trades = df[df['market_condition'] == condition]

                if len(condition_trades) < self.min_sample_size:
                    continue

                win_rate = condition_trades['is_winning'].mean()
                avg_profit = condition_trades['profit_loss'].mean()

                # 改进置信度计算：基于样本数量和胜率差异
                confidence = self._calculate_pattern_confidence(win_rate, len(condition_trades))

                pattern = TradingPattern(
                    pattern_id=f"market_condition_{condition}",
                    pattern_type='winning' if win_rate > 0.6 else 'losing',
                    description=f"市场条件: {condition}",
                    conditions={'market_condition': condition},
                    win_rate=win_rate,
                    avg_profit=avg_profit,
                    sample_size=len(condition_trades),
                    confidence=confidence
                )

                if pattern.confidence >= self.min_confidence:
                    if win_rate > 0.6:
                        patterns['winning'].append(pattern)
                    elif win_rate < 0.4:
                        patterns['losing'].append(pattern)

        except Exception as e:
            self.logger.error(f"市场条件模式分析失败: {e}")

        return patterns

    def _analyze_technical_patterns(self, df: pd.DataFrame) -> Dict:
        """分析技术指标模式"""
        patterns = {'winning': [], 'losing': []}

        try:
            # RSI模式分析
            rsi_patterns = self._analyze_rsi_patterns(df)
            patterns['winning'].extend(rsi_patterns['winning'])
            patterns['losing'].extend(rsi_patterns['losing'])

            # 移动平均线模式分析
            ma_patterns = self._analyze_ma_patterns(df)
            patterns['winning'].extend(ma_patterns['winning'])
            patterns['losing'].extend(ma_patterns['losing'])

            # 波动率模式分析
            volatility_patterns = self._analyze_volatility_patterns(df)
            patterns['winning'].extend(volatility_patterns['winning'])
            patterns['losing'].extend(volatility_patterns['losing'])

        except Exception as e:
            self.logger.error(f"技术指标模式分析失败: {e}")

        return patterns

    def _analyze_rsi_patterns(self, df: pd.DataFrame) -> Dict:
        """分析RSI模式"""
        patterns = {'winning': [], 'losing': []}

        try:
            # RSI区间分析
            rsi_ranges = [
                ('oversold', 0, 30),
                ('neutral_low', 30, 45),
                ('neutral_mid', 45, 55),
                ('neutral_high', 55, 70),
                ('overbought', 70, 100)
            ]

            for range_name, min_rsi, max_rsi in rsi_ranges:
                range_trades = df[(df['rsi'] >= min_rsi) & (df['rsi'] < max_rsi)]

                if len(range_trades) < self.min_sample_size:
                    continue

                win_rate = range_trades['is_winning'].mean()
                avg_profit = range_trades['profit_loss'].mean()

                confidence = self._calculate_pattern_confidence(win_rate, len(range_trades))

                pattern = TradingPattern(
                    pattern_id=f"rsi_{range_name}",
                    pattern_type='winning' if win_rate > 0.6 else 'losing',
                    description=f"RSI {range_name} ({min_rsi}-{max_rsi})",
                    conditions={'rsi_min': min_rsi, 'rsi_max': max_rsi},
                    win_rate=win_rate,
                    avg_profit=avg_profit,
                    sample_size=len(range_trades),
                    confidence=confidence
                )

                if pattern.confidence >= self.min_confidence:
                    if win_rate > 0.6:
                        patterns['winning'].append(pattern)
                    elif win_rate < 0.4:
                        patterns['losing'].append(pattern)

        except Exception as e:
            self.logger.error(f"RSI模式分析失败: {e}")

        return patterns

    def _analyze_ma_patterns(self, df: pd.DataFrame) -> Dict:
        """分析移动平均线模式"""
        patterns = {'winning': [], 'losing': []}

        try:
            # 计算价格相对于移动平均线的位置
            df['price_vs_ma20'] = (df['entry_price'] - df['ma_20']) / df['ma_20']
            df['price_vs_ma50'] = (df['entry_price'] - df['ma_50']) / df['ma_50']
            df['ma20_vs_ma50'] = (df['ma_20'] - df['ma_50']) / df['ma_50']

            # 趋势方向分析
            trend_conditions = [
                ('strong_uptrend', lambda x: (x['price_vs_ma20'] > 0.001) & (x['ma20_vs_ma50'] > 0.002)),
                ('weak_uptrend', lambda x: (x['price_vs_ma20'] > 0) & (x['ma20_vs_ma50'] > 0) & (x['ma20_vs_ma50'] <= 0.002)),
                ('sideways', lambda x: abs(x['ma20_vs_ma50']) <= 0.001),
                ('weak_downtrend', lambda x: (x['price_vs_ma20'] < 0) & (x['ma20_vs_ma50'] < 0) & (x['ma20_vs_ma50'] >= -0.002)),
                ('strong_downtrend', lambda x: (x['price_vs_ma20'] < -0.001) & (x['ma20_vs_ma50'] < -0.002))
            ]

            for trend_name, condition_func in trend_conditions:
                trend_trades = df[condition_func(df)]

                if len(trend_trades) < self.min_sample_size:
                    continue

                win_rate = trend_trades['is_winning'].mean()
                avg_profit = trend_trades['profit_loss'].mean()

                pattern = TradingPattern(
                    pattern_id=f"ma_trend_{trend_name}",
                    pattern_type='winning' if win_rate > 0.6 else 'losing',
                    description=f"移动平均线趋势: {trend_name}",
                    conditions={'trend_type': trend_name},
                    win_rate=win_rate,
                    avg_profit=avg_profit,
                    sample_size=len(trend_trades),
                    confidence=min(win_rate, 1 - win_rate) * (len(trend_trades) / 100)
                )

                if pattern.confidence >= self.min_confidence:
                    if win_rate > 0.6:
                        patterns['winning'].append(pattern)
                    elif win_rate < 0.4:
                        patterns['losing'].append(pattern)

        except Exception as e:
            self.logger.error(f"移动平均线模式分析失败: {e}")

        return patterns

    def _analyze_volatility_patterns(self, df: pd.DataFrame) -> Dict:
        """分析波动率模式"""
        patterns = {'winning': [], 'losing': []}

        try:
            # 波动率分级
            volatility_quantiles = df['volatility'].quantile([0.25, 0.5, 0.75])

            volatility_ranges = [
                ('low', 0, volatility_quantiles[0.25]),
                ('medium_low', volatility_quantiles[0.25], volatility_quantiles[0.5]),
                ('medium_high', volatility_quantiles[0.5], volatility_quantiles[0.75]),
                ('high', volatility_quantiles[0.75], 1.0)
            ]

            for vol_name, min_vol, max_vol in volatility_ranges:
                vol_trades = df[(df['volatility'] >= min_vol) & (df['volatility'] < max_vol)]

                if len(vol_trades) < self.min_sample_size:
                    continue

                win_rate = vol_trades['is_winning'].mean()
                avg_profit = vol_trades['profit_loss'].mean()

                pattern = TradingPattern(
                    pattern_id=f"volatility_{vol_name}",
                    pattern_type='winning' if win_rate > 0.6 else 'losing',
                    description=f"波动率 {vol_name} ({min_vol:.4f}-{max_vol:.4f})",
                    conditions={'volatility_min': min_vol, 'volatility_max': max_vol},
                    win_rate=win_rate,
                    avg_profit=avg_profit,
                    sample_size=len(vol_trades),
                    confidence=min(win_rate, 1 - win_rate) * (len(vol_trades) / 100)
                )

                if pattern.confidence >= self.min_confidence:
                    if win_rate > 0.6:
                        patterns['winning'].append(pattern)
                    elif win_rate < 0.4:
                        patterns['losing'].append(pattern)

        except Exception as e:
            self.logger.error(f"波动率模式分析失败: {e}")

        return patterns

    def _analyze_time_patterns(self, df: pd.DataFrame) -> Dict:
        """分析时间模式"""
        patterns = {'winning': [], 'losing': []}

        try:
            # 小时模式分析
            for hour in range(24):
                hour_trades = df[df['entry_hour'] == hour]

                if len(hour_trades) < self.min_sample_size:
                    continue

                win_rate = hour_trades['is_winning'].mean()
                avg_profit = hour_trades['profit_loss'].mean()

                pattern = TradingPattern(
                    pattern_id=f"hour_{hour}",
                    pattern_type='winning' if win_rate > 0.6 else 'losing',
                    description=f"交易时间: {hour}:00-{hour+1}:00",
                    conditions={'entry_hour': hour},
                    win_rate=win_rate,
                    avg_profit=avg_profit,
                    sample_size=len(hour_trades),
                    confidence=min(win_rate, 1 - win_rate) * (len(hour_trades) / 100)
                )

                if pattern.confidence >= self.min_confidence:
                    if win_rate > 0.6:
                        patterns['winning'].append(pattern)
                    elif win_rate < 0.4:
                        patterns['losing'].append(pattern)

        except Exception as e:
            self.logger.error(f"时间模式分析失败: {e}")

        return patterns

    def _analyze_strategy_patterns(self, df: pd.DataFrame) -> Dict:
        """分析策略参数模式"""
        patterns = {'winning': [], 'losing': []}

        try:
            # 置信度区间分析
            confidence_ranges = [
                ('low', 0.0, 0.6),
                ('medium', 0.6, 0.8),
                ('high', 0.8, 1.0)
            ]

            for conf_name, min_conf, max_conf in confidence_ranges:
                conf_trades = df[(df['confidence'] >= min_conf) & (df['confidence'] < max_conf)]

                if len(conf_trades) < self.min_sample_size:
                    continue

                win_rate = conf_trades['is_winning'].mean()
                avg_profit = conf_trades['profit_loss'].mean()

                pattern = TradingPattern(
                    pattern_id=f"confidence_{conf_name}",
                    pattern_type='winning' if win_rate > 0.6 else 'losing',
                    description=f"策略置信度 {conf_name} ({min_conf:.1f}-{max_conf:.1f})",
                    conditions={'confidence_min': min_conf, 'confidence_max': max_conf},
                    win_rate=win_rate,
                    avg_profit=avg_profit,
                    sample_size=len(conf_trades),
                    confidence=min(win_rate, 1 - win_rate) * (len(conf_trades) / 100)
                )

                if pattern.confidence >= self.min_confidence:
                    if win_rate > 0.6:
                        patterns['winning'].append(pattern)
                    elif win_rate < 0.4:
                        patterns['losing'].append(pattern)

        except Exception as e:
            self.logger.error(f"策略参数模式分析失败: {e}")

        return patterns

    def get_pattern_recommendations(self, current_conditions: Dict) -> List[str]:
        """基于当前条件获取模式建议"""
        try:
            patterns = self.analyze_patterns()
            recommendations = []

            # 检查当前条件是否匹配已知的盈利模式
            for pattern in patterns['winning_patterns']:
                if self._matches_conditions(current_conditions, pattern.conditions):
                    recommendations.append(f"✅ 匹配盈利模式: {pattern.description} (胜率: {pattern.win_rate:.1%})")

            # 检查当前条件是否匹配已知的亏损模式
            for pattern in patterns['losing_patterns']:
                if self._matches_conditions(current_conditions, pattern.conditions):
                    recommendations.append(f"⚠️ 匹配亏损模式: {pattern.description} (胜率: {pattern.win_rate:.1%})")

            return recommendations

        except Exception as e:
            self.logger.error(f"获取模式建议失败: {e}")
            return []

    def _matches_conditions(self, current: Dict, pattern_conditions: Dict) -> bool:
        """检查当前条件是否匹配模式条件"""
        try:
            for key, value in pattern_conditions.items():
                if key not in current:
                    continue

                if key.endswith('_min') and current[key.replace('_min', '')] < value:
                    return False
                elif key.endswith('_max') and current[key.replace('_max', '')] >= value:
                    return False
                elif not key.endswith(('_min', '_max')) and current[key] != value:
                    return False

            return True

        except Exception:
            return False
