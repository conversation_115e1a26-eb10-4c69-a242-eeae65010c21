"""
测试风险回报比检查功能
"""
import os
import sys
import time
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.forex_trading_service import execute_trade
from app.utils import mt4_client


def test_low_risk_reward_ratio():
    """测试低风险回报比的情况"""
    print("=" * 50)
    print("测试低风险回报比的情况")
    print("=" * 50)

    # 获取当前价格
    market_info_response = mt4_client.mt4_client.get_market_info('EURUSD')
    if not market_info_response or market_info_response.get('status') != 'success':
        print("无法获取当前价格，测试终止")
        return None

    current_price = float(market_info_response['data']['ask'])
    print(f"当前价格: {current_price}")

    # 创建一个低风险回报比的交易指令（风险回报比约为1:1）
    stop_loss = current_price - 0.01  # 止损100点
    take_profit = current_price + 0.01  # 止盈100点，风险回报比为1:1

    # 计算风险回报比
    risk = current_price - stop_loss
    reward = take_profit - current_price
    risk_reward_ratio = reward / risk

    print(f"止损: {stop_loss}")
    print(f"止盈: {take_profit}")
    print(f"风险: {risk:.5f}")
    print(f"回报: {reward:.5f}")
    print(f"风险回报比: {risk_reward_ratio:.2f}")

    trade_instructions = {
        'action': 'BUY',
        'orderType': 'MARKET',
        'entryPrice': None,
        'stopLoss': stop_loss,
        'takeProfit': take_profit,
        'lotSize': 0.01,
        'reasoning': '测试低风险回报比的情况',
        'isFinalDecision': True
    }

    print(f"交易指令: {trade_instructions}")

    # 执行交易
    result = execute_trade(trade_instructions, check_duplicate=True)

    print(f"交易结果: {result}")
    print("=" * 50)

    return result


def test_high_risk_reward_ratio():
    """测试高风险回报比的情况"""
    print("=" * 50)
    print("测试高风险回报比的情况")
    print("=" * 50)

    # 获取当前价格
    market_info_response = mt4_client.mt4_client.get_market_info('EURUSD')
    if not market_info_response or market_info_response.get('status') != 'success':
        print("无法获取当前价格，测试终止")
        return None

    current_price = float(market_info_response['data']['ask'])
    print(f"当前价格: {current_price}")

    # 创建一个高风险回报比的交易指令（风险回报比约为1:6）
    stop_loss = current_price - 0.01  # 止损100点
    take_profit = current_price + 0.06  # 止盈600点，风险回报比为1:6

    # 计算风险回报比
    risk = current_price - stop_loss
    reward = take_profit - current_price
    risk_reward_ratio = reward / risk

    print(f"止损: {stop_loss}")
    print(f"止盈: {take_profit}")
    print(f"风险: {risk:.5f}")
    print(f"回报: {reward:.5f}")
    print(f"风险回报比: {risk_reward_ratio:.2f}")

    trade_instructions = {
        'action': 'BUY',
        'orderType': 'MARKET',
        'entryPrice': None,
        'stopLoss': stop_loss,
        'takeProfit': take_profit,
        'lotSize': 0.01,
        'reasoning': '测试高风险回报比的情况',
        'isFinalDecision': True
    }

    print(f"交易指令: {trade_instructions}")

    # 执行交易
    result = execute_trade(trade_instructions, check_duplicate=True)

    print(f"交易结果: {result}")
    print("=" * 50)

    return result


def main():
    """主函数"""
    print("开始测试风险回报比检查功能")

    # 确保MT4客户端已连接
    if not mt4_client.mt4_client.is_connected:
        print("MT4客户端未连接，尝试连接")
        connected = mt4_client.mt4_client.connect()
        if not connected:
            print("无法连接到MT4客户端，测试终止")
            return

    # 测试低风险回报比的情况
    test_low_risk_reward_ratio()

    # 等待2秒
    time.sleep(2)

    # 测试高风险回报比的情况
    test_high_risk_reward_ratio()

    print("测试完成")


if __name__ == "__main__":
    main()
