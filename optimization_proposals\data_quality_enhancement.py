#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量与特征工程优化方案
目标：提高LLM分析的数据质量，增强交易信号的准确性
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

class AdvancedMarketDataProcessor:
    """高级市场数据处理器"""
    
    def __init__(self):
        self.lookback_periods = {
            '15m': 96,  # 24小时
            '1h': 168,  # 7天
            '4h': 168,  # 4周
            '1d': 90    # 3个月
        }
    
    def calculate_market_microstructure(self, price_data: List[Dict]) -> Dict:
        """计算市场微观结构指标"""
        df = pd.DataFrame(price_data)
        
        # 价格效率指标
        price_efficiency = self._calculate_price_efficiency(df)
        
        # 波动率聚类
        volatility_regime = self._detect_volatility_regime(df)
        
        # 趋势强度
        trend_strength = self._calculate_trend_strength(df)
        
        # 支撑阻力强度
        support_resistance = self._calculate_support_resistance_strength(df)
        
        return {
            'price_efficiency': price_efficiency,
            'volatility_regime': volatility_regime,
            'trend_strength': trend_strength,
            'support_resistance': support_resistance,
            'market_phase': self._determine_market_phase(trend_strength, volatility_regime)
        }
    
    def _calculate_price_efficiency(self, df: pd.DataFrame) -> float:
        """计算价格效率（趋势vs噪音比）"""
        if len(df) < 20:
            return 0.5
            
        # 计算真实价格移动vs总价格移动
        price_change = abs(df['close'].iloc[-1] - df['close'].iloc[0])
        total_movement = df['high'].subtract(df['low']).sum()
        
        efficiency = price_change / total_movement if total_movement > 0 else 0
        return min(max(efficiency, 0), 1)
    
    def _detect_volatility_regime(self, df: pd.DataFrame) -> str:
        """检测波动率状态"""
        if len(df) < 20:
            return 'NORMAL'
            
        # 计算ATR
        high_low = df['high'] - df['low']
        high_close = abs(df['high'] - df['close'].shift(1))
        low_close = abs(df['low'] - df['close'].shift(1))
        
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = true_range.rolling(14).mean().iloc[-1]
        atr_ma = true_range.rolling(50).mean().iloc[-1]
        
        volatility_ratio = atr / atr_ma if atr_ma > 0 else 1
        
        if volatility_ratio > 1.5:
            return 'HIGH'
        elif volatility_ratio < 0.7:
            return 'LOW'
        else:
            return 'NORMAL'
    
    def _calculate_trend_strength(self, df: pd.DataFrame) -> Dict:
        """计算多时间框架趋势强度"""
        if len(df) < 50:
            return {'short': 0, 'medium': 0, 'long': 0, 'overall': 'NEUTRAL'}
        
        close = df['close']
        
        # 短期趋势（10-20周期）
        short_trend = self._calculate_directional_strength(close, 10, 20)
        
        # 中期趋势（20-50周期）
        medium_trend = self._calculate_directional_strength(close, 20, 50)
        
        # 长期趋势（50+周期）
        long_trend = self._calculate_directional_strength(close, 50, min(len(close), 100))
        
        # 综合趋势判断
        overall_score = (short_trend * 0.5 + medium_trend * 0.3 + long_trend * 0.2)
        
        if overall_score > 0.6:
            overall = 'STRONG_UP'
        elif overall_score > 0.3:
            overall = 'UP'
        elif overall_score < -0.6:
            overall = 'STRONG_DOWN'
        elif overall_score < -0.3:
            overall = 'DOWN'
        else:
            overall = 'NEUTRAL'
        
        return {
            'short': short_trend,
            'medium': medium_trend,
            'long': long_trend,
            'overall': overall,
            'score': overall_score
        }
    
    def _calculate_directional_strength(self, prices: pd.Series, short_period: int, long_period: int) -> float:
        """计算方向性强度"""
        if len(prices) < long_period:
            return 0
        
        short_ma = prices.rolling(short_period).mean()
        long_ma = prices.rolling(long_period).mean()
        
        # 计算均线分离度
        separation = (short_ma.iloc[-1] - long_ma.iloc[-1]) / long_ma.iloc[-1]
        
        # 计算均线斜率
        short_slope = (short_ma.iloc[-1] - short_ma.iloc[-5]) / short_ma.iloc[-5] if len(short_ma) >= 5 else 0
        long_slope = (long_ma.iloc[-1] - long_ma.iloc[-10]) / long_ma.iloc[-10] if len(long_ma) >= 10 else 0
        
        # 综合强度
        strength = (separation * 100 + short_slope * 50 + long_slope * 25) / 3
        return max(min(strength, 1), -1)
    
    def _calculate_support_resistance_strength(self, df: pd.DataFrame) -> Dict:
        """计算支撑阻力强度"""
        if len(df) < 20:
            return {'support_strength': 0, 'resistance_strength': 0}
        
        current_price = df['close'].iloc[-1]
        
        # 寻找近期高低点
        highs = df['high'].rolling(5, center=True).max()
        lows = df['low'].rolling(5, center=True).min()
        
        # 计算支撑位强度
        support_levels = lows[lows == df['low']].dropna()
        support_strength = self._calculate_level_strength(current_price, support_levels, 'support')
        
        # 计算阻力位强度
        resistance_levels = highs[highs == df['high']].dropna()
        resistance_strength = self._calculate_level_strength(current_price, resistance_levels, 'resistance')
        
        return {
            'support_strength': support_strength,
            'resistance_strength': resistance_strength
        }
    
    def _calculate_level_strength(self, current_price: float, levels: pd.Series, level_type: str) -> float:
        """计算价格水平强度"""
        if len(levels) == 0:
            return 0
        
        # 找到最近的水平
        if level_type == 'support':
            relevant_levels = levels[levels <= current_price]
        else:
            relevant_levels = levels[levels >= current_price]
        
        if len(relevant_levels) == 0:
            return 0
        
        # 计算距离权重
        distances = abs(relevant_levels - current_price) / current_price
        weights = 1 / (1 + distances * 100)  # 距离越近权重越大
        
        return weights.sum() / len(levels)
    
    def _determine_market_phase(self, trend_strength: Dict, volatility_regime: str) -> str:
        """确定市场阶段"""
        trend_score = trend_strength['score']
        
        if volatility_regime == 'HIGH':
            if abs(trend_score) > 0.5:
                return 'TRENDING_VOLATILE'
            else:
                return 'RANGING_VOLATILE'
        elif volatility_regime == 'LOW':
            if abs(trend_score) > 0.3:
                return 'TRENDING_QUIET'
            else:
                return 'RANGING_QUIET'
        else:  # NORMAL
            if abs(trend_score) > 0.4:
                return 'TRENDING_NORMAL'
            else:
                return 'RANGING_NORMAL'

class EnhancedTechnicalIndicators:
    """增强技术指标计算器"""
    
    @staticmethod
    def calculate_adaptive_ma(prices: pd.Series, period: int = 14) -> pd.Series:
        """自适应移动平均线"""
        # 基于波动率调整的移动平均
        volatility = prices.rolling(period).std()
        volatility_ratio = volatility / volatility.rolling(period*2).mean()
        
        # 动态调整周期
        adaptive_period = period * (2 - volatility_ratio.fillna(1))
        adaptive_period = adaptive_period.clip(period//2, period*2)
        
        # 计算自适应MA
        adaptive_ma = pd.Series(index=prices.index, dtype=float)
        for i in range(len(prices)):
            if i >= period:
                current_period = int(adaptive_period.iloc[i])
                start_idx = max(0, i - current_period + 1)
                adaptive_ma.iloc[i] = prices.iloc[start_idx:i+1].mean()
        
        return adaptive_ma
    
    @staticmethod
    def calculate_momentum_divergence(prices: pd.Series, rsi: pd.Series) -> Dict:
        """计算动量背离"""
        # 寻找价格和RSI的背离
        price_peaks = EnhancedTechnicalIndicators._find_peaks(prices)
        rsi_peaks = EnhancedTechnicalIndicators._find_peaks(rsi)
        
        price_troughs = EnhancedTechnicalIndicators._find_peaks(-prices)
        rsi_troughs = EnhancedTechnicalIndicators._find_peaks(-rsi)
        
        # 检测背离
        bullish_divergence = EnhancedTechnicalIndicators._detect_divergence(
            price_troughs, rsi_troughs, 'bullish'
        )
        bearish_divergence = EnhancedTechnicalIndicators._detect_divergence(
            price_peaks, rsi_peaks, 'bearish'
        )
        
        return {
            'bullish_divergence': bullish_divergence,
            'bearish_divergence': bearish_divergence,
            'divergence_strength': max(bullish_divergence, bearish_divergence)
        }
    
    @staticmethod
    def _find_peaks(series: pd.Series, distance: int = 5) -> List[int]:
        """寻找峰值"""
        peaks = []
        for i in range(distance, len(series) - distance):
            if all(series.iloc[i] >= series.iloc[i-j] for j in range(1, distance+1)) and \
               all(series.iloc[i] >= series.iloc[i+j] for j in range(1, distance+1)):
                peaks.append(i)
        return peaks
    
    @staticmethod
    def _detect_divergence(price_extremes: List[int], indicator_extremes: List[int], 
                          divergence_type: str) -> float:
        """检测背离强度"""
        if len(price_extremes) < 2 or len(indicator_extremes) < 2:
            return 0
        
        # 简化的背离检测逻辑
        # 实际实现需要更复杂的匹配算法
        return 0.5  # 占位符

class MarketRegimeDetector:
    """市场状态检测器"""
    
    def __init__(self):
        self.regimes = ['TRENDING', 'RANGING', 'BREAKOUT', 'REVERSAL']
    
    def detect_current_regime(self, market_data: Dict) -> Dict:
        """检测当前市场状态"""
        trend_strength = market_data.get('trend_strength', {})
        volatility_regime = market_data.get('volatility_regime', 'NORMAL')
        price_efficiency = market_data.get('price_efficiency', 0.5)
        
        # 趋势市场
        if abs(trend_strength.get('score', 0)) > 0.5 and price_efficiency > 0.6:
            regime = 'TRENDING'
            confidence = 0.8
        
        # 震荡市场
        elif abs(trend_strength.get('score', 0)) < 0.3 and price_efficiency < 0.4:
            regime = 'RANGING'
            confidence = 0.7
        
        # 突破市场
        elif volatility_regime == 'HIGH' and price_efficiency > 0.5:
            regime = 'BREAKOUT'
            confidence = 0.6
        
        # 反转市场
        elif market_data.get('divergence_strength', 0) > 0.6:
            regime = 'REVERSAL'
            confidence = 0.7
        
        else:
            regime = 'UNCERTAIN'
            confidence = 0.3
        
        return {
            'regime': regime,
            'confidence': confidence,
            'characteristics': self._get_regime_characteristics(regime)
        }
    
    def _get_regime_characteristics(self, regime: str) -> Dict:
        """获取市场状态特征"""
        characteristics = {
            'TRENDING': {
                'best_strategy': '13日均线右侧交易',
                'entry_style': '回踩入场',
                'risk_level': 'MEDIUM',
                'position_size_multiplier': 1.2
            },
            'RANGING': {
                'best_strategy': '支撑阻力交易',
                'entry_style': '边界反弹',
                'risk_level': 'HIGH',
                'position_size_multiplier': 0.8
            },
            'BREAKOUT': {
                'best_strategy': '突破跟随',
                'entry_style': '突破确认',
                'risk_level': 'HIGH',
                'position_size_multiplier': 0.6
            },
            'REVERSAL': {
                'best_strategy': '反转交易',
                'entry_style': '背离确认',
                'risk_level': 'HIGH',
                'position_size_multiplier': 0.7
            },
            'UNCERTAIN': {
                'best_strategy': '观望',
                'entry_style': '等待确认',
                'risk_level': 'VERY_HIGH',
                'position_size_multiplier': 0.5
            }
        }
        
        return characteristics.get(regime, characteristics['UNCERTAIN'])

# 使用示例
def enhance_market_data_for_llm(raw_data: Dict) -> Dict:
    """为LLM增强市场数据"""
    processor = AdvancedMarketDataProcessor()
    indicators = EnhancedTechnicalIndicators()
    regime_detector = MarketRegimeDetector()
    
    # 处理价格数据
    price_data = raw_data.get('timeframe15m', [])
    
    # 计算高级指标
    microstructure = processor.calculate_market_microstructure(price_data)
    
    # 检测市场状态
    market_regime = regime_detector.detect_current_regime(microstructure)
    
    # 增强数据
    enhanced_data = {
        **raw_data,
        'market_microstructure': microstructure,
        'market_regime': market_regime,
        'trading_recommendations': {
            'optimal_strategy': market_regime['characteristics']['best_strategy'],
            'entry_style': market_regime['characteristics']['entry_style'],
            'risk_adjustment': market_regime['characteristics']['position_size_multiplier'],
            'confidence_level': market_regime['confidence']
        }
    }
    
    return enhanced_data
